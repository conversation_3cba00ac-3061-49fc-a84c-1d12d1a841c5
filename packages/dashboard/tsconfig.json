{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "composite": false, "declaration": false, "declarationMap": false, "sourceMap": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "noEmit": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "dev-dist", "code-health", "public/assets", "scripts", "**/*.test.*", "**/*.spec.*", "**/*.config.js", "**/*.config.ts", "**/*.d.ts"], "references": [{"path": "../shared"}]}