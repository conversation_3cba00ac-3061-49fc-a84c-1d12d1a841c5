/**
 * Theme System Types
 *
 * TypeScript definitions for the ADHD Trading Dashboard theme system
 * supporting Mercedes Green, F1 Official, and Dark themes.
 */

export type ThemeName = 'mercedes-green' | 'f1-official' | 'dark';

export type ConfidenceLevel = 'HIGH' | 'MEDIUM' | 'LOW';

export type RecommendationType =
  | 'PRIORITIZE'
  | 'INCREASE_SIZE'
  | 'STANDARD'
  | 'REDUCE_SIZE'
  | 'AVOID';

export type PatternQualityRating =
  | 'EXCEPTIONAL'
  | 'EXCELLENT'
  | 'GOOD'
  | 'FAIR'
  | 'AVERAGE'
  | 'POOR';

export type PriorityLevel = 'HIGH' | 'MEDIUM' | 'LOW';

export interface ThemeColors {
  // Core Colors
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;

  // Text Colors
  textPrimary: string;
  textSecondary: string;
  textMuted: string;
  textInverse: string;

  // Status Colors
  success: string;
  warning: string;
  error: string;
  info: string;

  // Session States
  sessionActive: string;
  sessionOptimal: string;
  sessionCaution: string;
  sessionTransition: string;
  sessionInactive: string;

  // Interactive States
  hover: string;
  active: string;
  focus: string;

  // Border Colors
  borderPrimary: string;
  borderSecondary: string;
  borderAccent: string;
}

export interface SessionBackgroundStates {
  activeBg: string;
  optimalBg: string;
  activeBorder: string;
  optimalBorder: string;
}

export interface RecommendationColors {
  prioritize: {
    bg: string;
    text: string;
  };
  increase: {
    bg: string;
    text: string;
  };
  standard: {
    bg: string;
    text: string;
  };
  reduce: {
    bg: string;
    text: string;
  };
  avoid: {
    bg: string;
    text: string;
  };
}

export interface PatternQualityColors {
  excellent: {
    bg: string;
    text: string;
    border: string;
  };
  good: {
    bg: string;
    text: string;
    border: string;
  };
  average: {
    bg: string;
    text: string;
    border: string;
  };
  poor: {
    bg: string;
    text: string;
    border: string;
  };
}

export interface ThemeConfig {
  name: ThemeName;
  displayName: string;
  colors: ThemeColors;
  sessionStates: SessionBackgroundStates;
  recommendations: RecommendationColors;
  patternQuality: PatternQualityColors;
}

export interface StyledComponentProps {
  theme?: ThemeConfig;
}

export interface ProbabilityBadgeProps extends StyledComponentProps {
  confidence: ConfidenceLevel;
}

export interface SessionWindowCardProps extends StyledComponentProps {
  isActive: boolean;
  isOptimal: boolean;
}

export interface RecommendationBadgeProps extends StyledComponentProps {
  recommendation: RecommendationType;
}

export interface PatternQualityBadgeProps extends StyledComponentProps {
  rating: PatternQualityRating;
}

/**
 * CSS Variable Names used in the theme system
 */
export const CSS_VARIABLES = {
  // Core Colors
  PRIMARY_COLOR: '--primary-color',
  SECONDARY_COLOR: '--secondary-color',
  ACCENT_COLOR: '--accent-color',
  BG_PRIMARY: '--bg-primary',
  BG_CARD: '--bg-card',

  // Text Colors
  TEXT_PRIMARY: '--text-primary',
  TEXT_SECONDARY: '--text-secondary',
  TEXT_MUTED: '--text-muted',
  TEXT_INVERSE: '--text-inverse',

  // Status Colors
  SUCCESS_COLOR: '--success-color',
  WARNING_COLOR: '--warning-color',
  ERROR_COLOR: '--error-color',
  INFO_COLOR: '--info-color',

  // Session Background States
  SESSION_ACTIVE_BG: '--session-active-bg',
  SESSION_OPTIMAL_BG: '--session-optimal-bg',
  SESSION_ACTIVE_BORDER: '--session-active-border',
  SESSION_OPTIMAL_BORDER: '--session-optimal-border',

  // Recommendation Colors
  RECOMMENDATION_PRIORITIZE_BG: '--recommendation-prioritize-bg',
  RECOMMENDATION_PRIORITIZE_TEXT: '--recommendation-prioritize-text',
  RECOMMENDATION_INCREASE_BG: '--recommendation-increase-bg',
  RECOMMENDATION_INCREASE_TEXT: '--recommendation-increase-text',
  RECOMMENDATION_STANDARD_BG: '--recommendation-standard-bg',
  RECOMMENDATION_STANDARD_TEXT: '--recommendation-standard-text',
  RECOMMENDATION_REDUCE_BG: '--recommendation-reduce-bg',
  RECOMMENDATION_REDUCE_TEXT: '--recommendation-reduce-text',
  RECOMMENDATION_AVOID_BG: '--recommendation-avoid-bg',
  RECOMMENDATION_AVOID_TEXT: '--recommendation-avoid-text',

  // Border Colors
  BORDER_PRIMARY: '--border-primary',
  BORDER_SECONDARY: '--border-secondary',
  BORDER_ACCENT: '--border-accent',
} as const;

/**
 * Theme validation utility
 */
export function isValidTheme(theme: string): theme is ThemeName {
  return ['mercedes-green', 'f1-official', 'dark'].includes(theme);
}

/**
 * Get CSS variable value
 */
export function getCSSVariable(variableName: string): string {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
  }
  return '';
}
