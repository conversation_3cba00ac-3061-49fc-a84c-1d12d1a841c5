/**
 * Accessibility Controls Component
 * 
 * Provides user-controllable accessibility features for neurodivergent users:
 * - Motion toggle (enable/disable animations)
 * - Low stimulation mode (removes visual effects)
 * - Automatic reduced motion detection
 * - Persistent user preferences
 */

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

export interface AccessibilityPreferences {
  motionEnabled: boolean;
  lowStimulationMode: boolean;
  autoDetectMotion: boolean;
}

interface AccessibilityControlsProps {
  /** Whether to show the controls panel */
  isVisible?: boolean;
  /** Callback when preferences change */
  onPreferencesChange?: (preferences: AccessibilityPreferences) => void;
  /** Additional CSS class */
  className?: string;
}

// Styled Components
const ControlsPanel = styled.div<{ $isVisible: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--bg-secondary);
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  padding: var(--spacing-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  z-index: 1000;
  min-width: 280px;
  transform: ${({ $isVisible }) => ($isVisible ? 'translateX(0)' : 'translateX(calc(100% + 40px))')};
  transition: transform 0.3s ease-in-out;

  @media (max-width: 768px) {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
  }
`;

const ControlsHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Title = styled.h3`
  font-family: var(--font-body);
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color var(--transition-normal);

  &:hover {
    color: var(--text-primary);
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
`;

const ControlGroup = styled.div`
  margin-bottom: var(--spacing-md);

  &:last-child {
    margin-bottom: 0;
  }
`;

const ControlLabel = styled.label`
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-family: var(--font-body);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--spacing-xs) 0;
`;

const ControlCheckbox = styled.input`
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
  cursor: pointer;

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
`;

const ControlDescription = styled.div`
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: var(--spacing-xxs);
  line-height: 1.4;
`;

const ToggleButton = styled.button<{ $isActive: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--bg-secondary);
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 999;
  transition: all var(--transition-normal);
  color: var(--primary-color);
  font-size: 20px;

  &:hover {
    background: var(--primary-color);
    color: var(--bg-secondary);
    transform: scale(1.05);
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 4px;
  }

  @media (max-width: 768px) {
    top: 10px;
    right: 10px;
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
`;

/**
 * Accessibility Controls Component
 */
export const AccessibilityControls: React.FC<AccessibilityControlsProps> = ({
  isVisible = false,
  onPreferencesChange,
  className,
}) => {
  const [showPanel, setShowPanel] = useState(isVisible);
  const [preferences, setPreferences] = useState<AccessibilityPreferences>({
    motionEnabled: true,
    lowStimulationMode: false,
    autoDetectMotion: true,
  });

  // Load preferences from localStorage on mount
  useEffect(() => {
    const savedPrefs = localStorage.getItem('accessibility-preferences');
    if (savedPrefs) {
      try {
        const parsed = JSON.parse(savedPrefs);
        setPreferences(parsed);
        applyPreferences(parsed);
      } catch (error) {
        console.warn('Failed to parse accessibility preferences:', error);
      }
    }

    // Check system preference for reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion && preferences.autoDetectMotion) {
      const updatedPrefs = { ...preferences, motionEnabled: false };
      setPreferences(updatedPrefs);
      applyPreferences(updatedPrefs);
    }
  }, []);

  // Apply preferences to document
  const applyPreferences = (prefs: AccessibilityPreferences) => {
    const root = document.documentElement;
    
    // Apply motion settings
    root.setAttribute('data-motion-enabled', prefs.motionEnabled.toString());
    
    // Apply stimulation mode
    if (prefs.lowStimulationMode) {
      root.setAttribute('data-accessibility-mode', 'low-stimulation');
    } else {
      root.removeAttribute('data-accessibility-mode');
    }
  };

  // Handle preference changes
  const handlePreferenceChange = (key: keyof AccessibilityPreferences, value: boolean) => {
    const newPreferences = { ...preferences, [key]: value };
    setPreferences(newPreferences);
    
    // Save to localStorage
    localStorage.setItem('accessibility-preferences', JSON.stringify(newPreferences));
    
    // Apply to document
    applyPreferences(newPreferences);
    
    // Notify parent component
    onPreferencesChange?.(newPreferences);
  };

  return (
    <>
      <ToggleButton
        $isActive={showPanel}
        onClick={() => setShowPanel(!showPanel)}
        aria-label="Toggle accessibility controls"
        aria-expanded={showPanel}
        aria-controls="accessibility-panel"
      >
        ♿
      </ToggleButton>

      <ControlsPanel 
        $isVisible={showPanel} 
        className={className}
        id="accessibility-panel"
        role="dialog"
        aria-labelledby="accessibility-title"
      >
        <ControlsHeader>
          <Title id="accessibility-title">Accessibility Controls</Title>
          <CloseButton
            onClick={() => setShowPanel(false)}
            aria-label="Close accessibility controls"
          >
            ×
          </CloseButton>
        </ControlsHeader>

        <ControlGroup>
          <ControlLabel>
            <ControlCheckbox
              type="checkbox"
              checked={preferences.motionEnabled}
              onChange={(e) => handlePreferenceChange('motionEnabled', e.target.checked)}
              aria-describedby="motion-description"
            />
            Enable Animations
          </ControlLabel>
          <ControlDescription id="motion-description">
            Controls all visual animations and transitions in the interface
          </ControlDescription>
        </ControlGroup>

        <ControlGroup>
          <ControlLabel>
            <ControlCheckbox
              type="checkbox"
              checked={preferences.lowStimulationMode}
              onChange={(e) => handlePreferenceChange('lowStimulationMode', e.target.checked)}
              aria-describedby="stimulation-description"
            />
            Low Stimulation Mode
          </ControlLabel>
          <ControlDescription id="stimulation-description">
            Reduces visual effects, glows, and racing elements for calmer experience
          </ControlDescription>
        </ControlGroup>

        <ControlGroup>
          <ControlLabel>
            <ControlCheckbox
              type="checkbox"
              checked={preferences.autoDetectMotion}
              onChange={(e) => handlePreferenceChange('autoDetectMotion', e.target.checked)}
              aria-describedby="auto-detect-description"
            />
            Auto-Detect Motion Preference
          </ControlLabel>
          <ControlDescription id="auto-detect-description">
            Automatically respects your system's reduced motion setting
          </ControlDescription>
        </ControlGroup>
      </ControlsPanel>
    </>
  );
};

export default AccessibilityControls;
