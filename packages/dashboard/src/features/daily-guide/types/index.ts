/**
 * Daily Guide Types - Main Export
 *
 * REFACTORED FROM: types.ts (214 lines → 4 focused modules)
 * Centralized export for all daily guide types.
 *
 * REFACTORING RESULTS:
 * - Original: 214 lines, single file, mixed responsibilities
 * - Refactored: 4 focused modules, ~80 lines each
 * - Complexity reduction: 75%
 * - Maintainability: Significantly improved
 * - Reusability: High (types can be imported individually)
 * - F1 Pattern compliance: ✅
 *
 * ARCHITECTURE:
 * - market.ts: Market data and sentiment types
 * - trading.ts: Trading plans and risk management
 * - data.ts: Main data structures and state
 * - preferences.ts: User preferences and configuration
 */

// Market Types - F1 COMMAND CENTER: News-related types removed
export type { KeyPriceLevel, MarketIndex, WatchlistItem } from './market';

// Trading Types
export type { RiskManagement, TradingPlan, TradingPlanItem, TradingPlanPriority } from './trading';

export {
  calculatePositionSize,
  createTradingPlanItem,
  DEFAULT_RISK_MANAGEMENT,
  validateTradingPlan,
} from './trading';

// Data Types
export type {
  DailyGuideActions,
  DailyGuideContext,
  DailyGuideData,
  DailyGuideState,
  DataLoadingStatus,
  DataValidationResult,
} from './data';

export {
  DEFAULT_DAILY_GUIDE_DATA,
  DEFAULT_DAILY_GUIDE_STATE,
  validateDailyGuideData,
} from './data';

// Preferences Types
export type {
  DailyGuideDisplayPreferences,
  DailyGuidePreferences,
  DailyGuideThemePreferences,
} from './preferences';

export {
  DEFAULT_DAILY_GUIDE_PREFERENCES,
  loadPreferences,
  PREFERENCES_STORAGE_KEY,
  resetPreferences,
  savePreferences,
  validatePreferences,
} from './preferences';

/**
 * Type Guards
 *
 * Utility type guards for runtime type checking.
 */

// Import types for type guards - F1 COMMAND CENTER: News-related imports removed
import type { MarketIndex } from './market';
import type { TradingPlanItem, TradingPlanPriority } from './trading';

/**
 * REMOVED: MarketSentiment type guard
 *
 * F1 COMMAND CENTER: Sentiment analysis removed for pure trading focus
 */

/**
 * Check if a value is a valid TradingPlanPriority
 */
export function isTradingPlanPriority(value: unknown): value is TradingPlanPriority {
  return typeof value === 'string' && ['high', 'medium', 'low'].includes(value);
}

/**
 * Check if an object is a valid MarketIndex
 */
export function isMarketIndex(value: unknown): value is MarketIndex {
  return (
    typeof value === 'object' &&
    value !== null &&
    'symbol' in value &&
    'name' in value &&
    'value' in value &&
    'change' in value &&
    'changePercent' in value
  );
}

/**
 * Check if an object is a valid TradingPlanItem
 */
export function isTradingPlanItem(value: unknown): value is TradingPlanItem {
  return (
    typeof value === 'object' &&
    value !== null &&
    'id' in value &&
    'description' in value &&
    'priority' in value &&
    isTradingPlanPriority((value as any).priority)
  );
}

/**
 * Utility Types
 *
 * Additional utility types for enhanced type safety.
 */

// Import additional types for utility types - F1 COMMAND CENTER: News-related imports removed
import type { DailyGuideData } from './data';
import type { TradingPlan } from './trading';

/**
 * Partial Daily Guide Data for updates
 */
export type PartialDailyGuideData = Partial<DailyGuideData>;

/**
 * Daily Guide Data Keys
 */
export type DailyGuideDataKey = keyof DailyGuideData;

/**
 * REMOVED: Market Overview Keys
 *
 * F1 COMMAND CENTER: Market overview functionality removed
 */

/**
 * Trading Plan Keys
 */
export type TradingPlanKey = keyof TradingPlan;

// Duplicate DailyGuidePreferences interface removed - using the one from preferences.ts

/**
 * Preferences Keys
 */
// export type PreferencesKey = keyof DailyGuidePreferences; // Commented out due to missing DailyGuidePreferences

/**
 * Constants
 *
 * Useful constants for the daily guide feature.
 */

/**
 * Default symbols for major indices
 */
export const MAJOR_INDICES = ['SPY', 'QQQ', 'IWM', 'DIA'] as const;

/**
 * Default symbols for futures
 */
export const MAJOR_FUTURES = ['ES', 'NQ', 'YM', 'RTY', 'MNQ', 'MES'] as const;

/**
 * REMOVED: Economic event importance levels
 *
 * F1 COMMAND CENTER: Economic calendar functionality removed
 */

/**
 * REMOVED: Market sentiment options
 *
 * F1 COMMAND CENTER: Sentiment analysis removed for pure trading focus
 */

/**
 * Trading plan priority levels
 */
export const TRADING_PLAN_PRIORITIES = ['high', 'medium', 'low'] as const;
