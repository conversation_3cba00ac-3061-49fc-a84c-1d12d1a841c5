/**
 * Market Types
 *
 * REFACTORED FROM: types.ts (214 lines → focused modules)
 * Market-related type definitions for the daily guide feature.
 *
 * BENEFITS:
 * - Focused responsibility (market data only)
 * - Better reusability across features
 * - Easier maintenance and testing
 * - Follows F1 architectural patterns
 */

/**
 * REMOVED: Market Sentiment
 *
 * F1 COMMAND CENTER: News-related sentiment analysis removed for pure trading focus
 */

/**
 * Market Index
 *
 * Represents a major market index with current and historical data.
 */
export interface MarketIndex {
  /** The index symbol (e.g., SPY, QQQ, IWM) */
  symbol: string;
  /** The index name (e.g., S&P 500, NASDAQ 100) */
  name: string;
  /** The current value */
  value: number;
  /** The change from previous close */
  change: number;
  /** The percentage change from previous close */
  changePercent: number;
  /** The previous close value */
  previousClose?: number;
}

/**
 * REMOVED: Economic Event
 *
 * F1 COMMAND CENTER: Economic calendar functionality removed for distraction-free trading
 */

/**
 * REMOVED: Market News Item
 *
 * F1 COMMAND CENTER: News feed functionality eliminated for pure technical analysis focus
 */

/**
 * REMOVED: Market Overview
 *
 * F1 COMMAND CENTER: Comprehensive news/sentiment overview removed for pure ICT trading focus
 */

/**
 * Key Price Level
 *
 * Important support and resistance levels for a symbol.
 */
export interface KeyPriceLevel {
  /** The symbol */
  symbol: string;
  /** Support levels (price strings) */
  support: string[];
  /** Resistance levels (price strings) */
  resistance: string[];
  /** Pivot point */
  pivotPoint?: string;
  /** The level description */
  description?: string;
}

/**
 * Watchlist Item
 *
 * A symbol being watched for potential trading opportunities.
 */
export interface WatchlistItem {
  /** The symbol */
  symbol: string;
  /** The company name */
  name: string;
  /** The current price */
  price: number;
  /** The reason for watching */
  reason: string;
  /** The potential trade setup */
  setup?: string;
  /** The entry price */
  entryPrice?: number;
  /** The stop loss price */
  stopLoss?: number;
  /** The take profit price */
  takeProfit?: number;
}
