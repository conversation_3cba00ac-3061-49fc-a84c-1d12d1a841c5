/**
 * Daily Guide API
 *
 * API functions for the daily guide feature
 */

import { DailyGuideData, TradingPlanPriority } from '../types';

/**
 * Fetch daily guide data from the API
 *
 * In a real application, this would make an actual API call.
 * For now, we're generating mock data.
 */
export const fetchDailyGuideData = async (): Promise<DailyGuideData> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));

  // Randomly decide if we should throw an error (for testing error handling)
  const shouldError = Math.random() < 0.05; // 5% chance of error
  if (shouldError) {
    throw new Error('Failed to fetch daily guide data');
  }

  return generateMockData();
};

/**
 * Generate mock data for development and testing
 */
const generateMockData = (): DailyGuideData => {
  // Market data removed as it's not part of the DailyGuideData interface

  // Trading plan
  // Using different priorities for each item
  // const priorities: TradingPlanPriority[] = ['high', 'medium', 'low'];
  const tradingPlan = [
    {
      id: '1',
      description: 'Wait for market open before placing any trades',
      priority: 'high' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '2',
      description: 'Focus on tech sector for long opportunities',
      priority: 'medium' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '3',
      description: 'Use tight stop losses due to expected volatility',
      priority: 'high' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '4',
      description: 'Review earnings reports for potential opportunities',
      priority: 'medium' as TradingPlanPriority,
      completed: false,
    },
    {
      id: '5',
      description: 'Avoid over-trading in the first hour',
      priority: 'low' as TradingPlanPriority,
      completed: false,
    },
  ];

  // Key levels removed - not part of DailyGuideData interface

  // REMOVED: Market news
  // F1 COMMAND CENTER: News functionality eliminated for pure trading focus

  return {
    tradingPlan: {
      items: tradingPlan,
      strategy: 'Focus on FVG and RD setups during high-volume sessions',
      riskManagement: {
        maxRiskPerTrade: 1.5,
        maxDailyLoss: 3.0,
        maxTrades: 5,
        positionSizing: '1% of account per trade',
      },
      notes: 'Market showing signs of consolidation. Be patient and wait for clear setups.',
    },
    // F1 COMMAND CENTER: Pure trading focus - news/overview functionality removed
    keyPriceLevels: [],
    watchlist: [],
  };
};
