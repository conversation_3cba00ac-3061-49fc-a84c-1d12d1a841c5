/**
 * Market-Aware Pattern Quality Hook
 *
 * Enhances pattern quality scoring with market state awareness.
 * Addresses the weekend/off-hours 0.2/5.0 display issue by providing
 * context-appropriate scoring and messaging.
 */

import { useMemo } from 'react';
import { usePatternQualityScoring, PatternQualityScore } from './usePatternQualityScoring';
import { getCurrentMarketState, MarketState } from '../components/MarketStateIndicator';

export interface MarketAwarePatternQuality {
  /** Current pattern quality score */
  currentScore: PatternQualityScore;
  /** Market state information */
  marketState: MarketState;
  /** Whether the score is based on live or historical data */
  isLiveData: boolean;
  /** Context message explaining the score */
  contextMessage: string;
  /** Whether to show a market closed warning */
  showMarketClosedWarning: boolean;
  /** Historical accuracy data */
  historicalAccuracy: number;
  /** Score distribution data */
  scoreDistribution: { score: number; count: number }[];
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
}

/**
 * Generate context message based on market state and score
 */
const generateContextMessage = (
  marketState: MarketState,
  score: PatternQualityScore
): string => {
  if (marketState.isOpen) {
    if (score.totalScore >= 4.0) {
      return 'Live market analysis shows exceptional setup quality';
    } else if (score.totalScore >= 3.0) {
      return 'Live market analysis indicates good setup potential';
    } else if (score.totalScore >= 2.0) {
      return 'Live market analysis suggests average setup quality';
    } else {
      return 'Live market analysis indicates poor setup quality';
    }
  } else {
    // Markets closed
    if (marketState.status === 'CLOSED' && (marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday')) {
      return 'Weekend analysis based on most recent trading session data';
    } else if (marketState.status === 'PRE_MARKET') {
      return 'Pre-market analysis based on previous session data';
    } else if (marketState.status === 'AFTER_HOURS') {
      return 'After-hours analysis based on current session data';
    } else {
      return 'Analysis based on historical trading data';
    }
  }
};

/**
 * Adjust pattern quality score for market state
 */
const adjustScoreForMarketState = (
  originalScore: PatternQualityScore,
  marketState: MarketState
): PatternQualityScore => {
  // If markets are open, return original score
  if (marketState.isOpen) {
    return originalScore;
  }

  // For closed markets, provide more appropriate messaging
  const adjustedScore = { ...originalScore };

  if (marketState.status === 'CLOSED' && (marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday')) {
    // Weekend - indicate no current setup
    adjustedScore.recommendation = 'Markets closed for weekend - No current setups to analyze';
    adjustedScore.expectedWinProbability = 0;
  } else if (marketState.status === 'PRE_MARKET') {
    // Pre-market - adjust recommendation
    adjustedScore.recommendation = `Pre-market setup analysis: ${originalScore.recommendation}`;
  } else if (marketState.status === 'AFTER_HOURS') {
    // After hours - adjust recommendation
    adjustedScore.recommendation = `After-hours analysis: ${originalScore.recommendation}`;
  }

  return adjustedScore;
};

/**
 * Market-Aware Pattern Quality Hook
 */
export const useMarketAwarePatternQuality = (): MarketAwarePatternQuality => {
  const {
    analysis,
    isLoading,
    error,
  } = usePatternQualityScoring();

  const marketState = getCurrentMarketState();

  const marketAwareAnalysis = useMemo(() => {
    const adjustedScore = adjustScoreForMarketState(analysis.currentScore, marketState);
    const contextMessage = generateContextMessage(marketState, adjustedScore);
    const isLiveData = marketState.isOpen;
    const showMarketClosedWarning = !marketState.isOpen && 
      (marketState.status === 'CLOSED' && (marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday'));

    return {
      currentScore: adjustedScore,
      marketState,
      isLiveData,
      contextMessage,
      showMarketClosedWarning,
      historicalAccuracy: analysis.historicalAccuracy,
      scoreDistribution: analysis.scoreDistribution,
      isLoading,
      error,
    };
  }, [analysis, marketState, isLoading, error]);

  return marketAwareAnalysis;
};

export default useMarketAwarePatternQuality;
