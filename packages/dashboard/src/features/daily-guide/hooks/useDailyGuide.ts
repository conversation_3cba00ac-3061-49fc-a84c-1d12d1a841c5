/**
 * Use Daily Guide Hook
 *
 * A custom hook for the daily guide feature.
 */
import { useEffect, useCallback } from 'react';
import {
  useDailyGuideSelector,
  useDailyGuideActions,
  dailyGuideActions,
  dailyGuideSelectors,
} from '../state';
import { DailyGuideData, TradingPlanItem, TradingPlanPriority } from '../types';

/**
 * Generate mock data for development
 */
const generateMockData = (): DailyGuideData => {
  // Market overview
  const mockMarketOverview = {
    sentiment: ['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)] as
      | 'bullish'
      | 'bearish'
      | 'neutral',
    summary:
      'Markets are showing mixed signals with tech stocks outperforming the broader indices. Watch for resistance at key levels.',
    indices: [
      {
        symbol: 'SPY',
        name: 'S&P 500',
        value: 4500 + Math.random() * 100,
        change: Math.random() * 2 - 1,
        changePercent: (Math.random() * 2 - 1) / 100,
      },
      {
        symbol: 'QQQ',
        name: '<PERSON>sda<PERSON>',
        value: 14000 + Math.random() * 500,
        change: Math.random() * 2 - 1,
        changePercent: (Math.random() * 2 - 1) / 100,
      },
      {
        symbol: 'DIA',
        name: '<PERSON>',
        value: 35000 + Math.random() * 500,
        change: Math.random() * 2 - 1,
        changePercent: (Math.random() * 2 - 1) / 100,
      },
    ],
    economicEvents: [
      {
        title: 'Fed Interest Rate Decision',
        time: '14:00',
        importance: 'high' as 'high' | 'medium' | 'low',
        expected: '5.25%',
        previous: '5.25%',
      },
      {
        title: 'Unemployment Claims',
        time: '08:30',
        importance: 'medium' as 'high' | 'medium' | 'low',
        expected: '235K',
        previous: '240K',
        actual: '232K',
      },
      {
        title: 'GDP Growth Rate',
        time: '08:30',
        importance: 'high' as 'high' | 'medium' | 'low',
        expected: '2.1%',
        previous: '2.0%',
      },
    ],
    news: [
      {
        id: '1',
        title: 'Fed signals potential rate cuts in upcoming meeting',
        source: 'Bloomberg',
        timestamp: new Date().toISOString(),
        url: 'https://example.com/news/1',
        impact: 'high' as 'high' | 'medium' | 'low',
      },
      {
        id: '2',
        title: 'Tech stocks rally on positive earnings surprises',
        source: 'CNBC',
        timestamp: new Date().toISOString(),
        url: 'https://example.com/news/2',
        impact: 'medium' as 'high' | 'medium' | 'low',
      },
      {
        id: '3',
        title: 'Oil prices drop on increased supply concerns',
        source: 'Reuters',
        timestamp: new Date().toISOString(),
        url: 'https://example.com/news/3',
        impact: 'medium' as 'high' | 'medium' | 'low',
      },
    ],
    lastUpdated: new Date().toISOString(),
  };

  // Trading plan
  const mockTradingPlan = {
    items: [
      {
        id: '1',
        description: 'Wait for market open before placing any trades',
        priority: 'high' as TradingPlanPriority,
        completed: false,
      },
      {
        id: '2',
        description: 'Focus on tech sector for long opportunities',
        priority: 'medium' as TradingPlanPriority,
        completed: false,
      },
      {
        id: '3',
        description: 'Use tight stop losses due to expected volatility',
        priority: 'high' as TradingPlanPriority,
        completed: false,
      },
      {
        id: '4',
        description: 'Review earnings reports for potential opportunities',
        priority: 'medium' as TradingPlanPriority,
        completed: false,
      },
      {
        id: '5',
        description: 'Avoid over-trading in the first hour',
        priority: 'low' as TradingPlanPriority,
        completed: false,
      },
    ],
    strategy: 'Focus on momentum plays in tech sector with tight risk management',
    riskManagement: {
      maxRiskPerTrade: 1.0,
      maxDailyLoss: 3.0,
      maxTrades: 5,
      positionSizing: '2% of account per trade',
    },
    notes: 'Market is showing signs of volatility. Be cautious and wait for clear setups.',
  };

  // Key price levels
  const mockKeyPriceLevels = [
    {
      symbol: 'SPY',
      support: ['450.00', '445.75', '442.30'],
      resistance: ['455.50', '460.00', '462.75'],
      pivotPoint: '452.25',
    },
    {
      symbol: 'QQQ',
      support: ['365.20', '360.00', '355.50'],
      resistance: ['370.00', '375.35', '380.00'],
      pivotPoint: '367.50',
    },
    {
      symbol: 'AAPL',
      support: ['175.00', '170.50', '165.75'],
      resistance: ['180.00', '185.50', '190.25'],
      pivotPoint: '177.25',
    },
  ];

  // Watchlist
  const mockWatchlist = [
    {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      price: 175.5,
      reason: 'Earnings report coming up',
      setup: 'Breakout above 180',
      entryPrice: 180.25,
      stopLoss: 175.0,
      takeProfit: 190.0,
    },
    {
      symbol: 'MSFT',
      name: 'Microsoft Corp.',
      price: 340.75,
      reason: 'Strong technical pattern',
      setup: 'Bull flag on daily chart',
      entryPrice: 342.5,
      stopLoss: 335.0,
      takeProfit: 355.0,
    },
    {
      symbol: 'NVDA',
      name: 'NVIDIA Corp.',
      price: 450.25,
      reason: 'AI momentum continues',
      setup: 'Pullback to support',
      entryPrice: 445.0,
      stopLoss: 435.0,
      takeProfit: 475.0,
    },
  ];

  return {
    marketOverview: mockMarketOverview,
    tradingPlan: mockTradingPlan,
    keyPriceLevels: mockKeyPriceLevels,
    watchlist: mockWatchlist,
    marketNews: mockMarketOverview.news,
  };
};

/**
 * Use Daily Guide Hook
 *
 * A custom hook for the daily guide feature.
 *
 * @returns The daily guide state and actions
 */
export function useDailyGuide() {
  // Access store and actions
  // Removed unused state destructuring - will be added back when needed
  const actions = useDailyGuideActions(dailyGuideActions);

  // Use selectors for derived state
  const selectedDate = useDailyGuideSelector(dailyGuideSelectors.selectSelectedDate);
  const marketOverview = useDailyGuideSelector(dailyGuideSelectors.selectMarketOverview);
  const tradingPlan = useDailyGuideSelector(dailyGuideSelectors.selectTradingPlan);
  const keyPriceLevels = useDailyGuideSelector(dailyGuideSelectors.selectKeyPriceLevels);
  const watchlist = useDailyGuideSelector(dailyGuideSelectors.selectWatchlist);
  const marketNews = useDailyGuideSelector(dailyGuideSelectors.selectMarketNews);
  const isLoading = useDailyGuideSelector(dailyGuideSelectors.selectIsLoading);
  const error = useDailyGuideSelector(dailyGuideSelectors.selectError);
  const tradingPlanItems = useDailyGuideSelector(dailyGuideSelectors.selectTradingPlanItems);
  const tradingPlanCompletion = useDailyGuideSelector(
    dailyGuideSelectors.selectTradingPlanCompletion
  );
  const marketSentiment = useDailyGuideSelector(dailyGuideSelectors.selectMarketSentiment);
  const marketSummary = useDailyGuideSelector(dailyGuideSelectors.selectMarketSummary);
  const lastUpdated = useDailyGuideSelector(dailyGuideSelectors.selectLastUpdated);

  // Format current date in a readable format
  const today = new Date(selectedDate);
  const currentDate = today.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // API integration - using mock data for now
  const fetchData = useCallback(async () => {
    try {
      actions.fetchDataStart();

      // In a real app, you would fetch data from an API
      // For now, we'll use mock data with a timeout to simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));

      const mockData = generateMockData();
      actions.fetchDataSuccess(mockData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching daily guide data:', err);
      actions.fetchDataError(errorMessage);
    }
  }, [actions]);

  // Fetch data on mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Fetch data when selected date changes
  useEffect(() => {
    fetchData();
  }, [selectedDate, fetchData]);

  // Event handlers
  const handleDateChange = useCallback(
    (date: string) => {
      actions.updateSelectedDate(date);
    },
    [actions]
  );

  const handleTradingPlanItemToggle = useCallback(
    (id: string, completed: boolean) => {
      actions.updateTradingPlanItem(id, completed);
    },
    [actions]
  );

  const handleAddTradingPlanItem = useCallback(
    (item: TradingPlanItem) => {
      actions.addTradingPlanItem(item);
    },
    [actions]
  );

  const handleRemoveTradingPlanItem = useCallback(
    (id: string) => {
      actions.removeTradingPlanItem(id);
    },
    [actions]
  );

  const handleRefresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  return {
    // State
    selectedDate,
    marketOverview,
    tradingPlan,
    keyPriceLevels,
    watchlist,
    marketNews,
    isLoading,
    error,
    tradingPlanItems,
    tradingPlanCompletion,
    marketSentiment,
    marketSummary,
    lastUpdated,
    currentDate,

    // Actions
    onDateChange: handleDateChange,
    onTradingPlanItemToggle: handleTradingPlanItemToggle,
    onAddTradingPlanItem: handleAddTradingPlanItem,
    onRemoveTradingPlanItem: handleRemoveTradingPlanItem,
    onRefresh: handleRefresh,
  };
}
