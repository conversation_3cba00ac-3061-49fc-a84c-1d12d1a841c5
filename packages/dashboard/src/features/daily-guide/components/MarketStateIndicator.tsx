/**
 * Market State Indicator Component
 *
 * Displays current market status and provides context for data freshness.
 * Addresses the weekend/off-hours 0.2/5.0 pattern quality issue by showing
 * when markets are closed and data may be stale.
 */

import React from 'react';
import styled from 'styled-components';

export interface MarketState {
  isOpen: boolean;
  status: 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'AFTER_HOURS';
  nextOpen?: string;
  timeZone: string;
  dayOfWeek: string;
}

export interface MarketStateIndicatorProps {
  /** Current market state */
  marketState: MarketState;
  /** Whether to show detailed information */
  showDetails?: boolean;
  /** Custom className */
  className?: string;
}

// Styled components
const StateContainer = styled.div<{ $isOpen: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  
  background: ${({ $isOpen }) => 
    $isOpen 
      ? 'var(--success-bg, rgba(34, 197, 94, 0.1))' 
      : 'var(--warning-bg, rgba(251, 191, 36, 0.1))'
  };
  
  border: 1px solid ${({ $isOpen }) => 
    $isOpen 
      ? 'var(--success-border, rgba(34, 197, 94, 0.3))' 
      : 'var(--warning-border, rgba(251, 191, 36, 0.3))'
  };
  
  color: ${({ $isOpen }) => 
    $isOpen 
      ? 'var(--success-text, #22c55e)' 
      : 'var(--warning-text, #fbbf24)'
  };
`;

const StatusDot = styled.div<{ $isOpen: boolean }>`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${({ $isOpen }) => 
    $isOpen 
      ? 'var(--success-text, #22c55e)' 
      : 'var(--warning-text, #fbbf24)'
  };
  
  ${({ $isOpen }) => $isOpen && `
    animation: pulse 2s infinite;
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `}
`;

const StatusText = styled.span`
  font-size: 12px;
  font-weight: 600;
`;

const DetailText = styled.div`
  font-size: 11px;
  font-weight: 400;
  color: var(--text-secondary);
  margin-top: 4px;
  text-transform: none;
  letter-spacing: normal;
`;

/**
 * Get current market state based on NY time
 */
export const getCurrentMarketState = (): MarketState => {
  const now = new Date();
  
  // Convert to NY time (EST/EDT)
  const nyTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}));
  const hour = nyTime.getHours();
  const minutes = nyTime.getMinutes();
  const dayOfWeek = nyTime.getDay(); // 0 = Sunday, 6 = Saturday
  
  const timeInMinutes = hour * 60 + minutes;
  const marketOpen = 9 * 60 + 30; // 9:30 AM
  const marketClose = 16 * 60; // 4:00 PM
  
  // Weekend check
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    const nextMonday = new Date(nyTime);
    nextMonday.setDate(nyTime.getDate() + (dayOfWeek === 0 ? 1 : 2)); // Next Monday
    nextMonday.setHours(9, 30, 0, 0);
    
    return {
      isOpen: false,
      status: 'CLOSED',
      nextOpen: nextMonday.toLocaleString('en-US', {
        weekday: 'long',
        hour: 'numeric',
        minute: '2-digit',
        timeZone: 'America/New_York'
      }),
      timeZone: 'ET',
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]
    };
  }
  
  // Weekday market hours
  if (timeInMinutes >= marketOpen && timeInMinutes < marketClose) {
    return {
      isOpen: true,
      status: 'OPEN',
      timeZone: 'ET',
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]
    };
  } else if (timeInMinutes < marketOpen) {
    const openTime = new Date(nyTime);
    openTime.setHours(9, 30, 0, 0);
    
    return {
      isOpen: false,
      status: 'PRE_MARKET',
      nextOpen: openTime.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        timeZone: 'America/New_York'
      }),
      timeZone: 'ET',
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]
    };
  } else {
    // After hours - next open is tomorrow 9:30 AM (or Monday if Friday)
    const nextOpen = new Date(nyTime);
    if (dayOfWeek === 5) { // Friday
      nextOpen.setDate(nyTime.getDate() + 3); // Monday
    } else {
      nextOpen.setDate(nyTime.getDate() + 1); // Tomorrow
    }
    nextOpen.setHours(9, 30, 0, 0);
    
    return {
      isOpen: false,
      status: 'AFTER_HOURS',
      nextOpen: nextOpen.toLocaleString('en-US', {
        weekday: dayOfWeek === 5 ? 'long' : undefined,
        hour: 'numeric',
        minute: '2-digit',
        timeZone: 'America/New_York'
      }),
      timeZone: 'ET',
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]
    };
  }
};

/**
 * Market State Indicator Component
 */
export const MarketStateIndicator: React.FC<MarketStateIndicatorProps> = ({
  marketState,
  showDetails = false,
  className
}) => {
  const getStatusText = () => {
    switch (marketState.status) {
      case 'OPEN':
        return 'Markets Open';
      case 'PRE_MARKET':
        return 'Pre-Market';
      case 'AFTER_HOURS':
        return 'After Hours';
      case 'CLOSED':
        return `Markets Closed - ${marketState.dayOfWeek}`;
      default:
        return 'Unknown';
    }
  };

  const getDetailText = () => {
    if (!showDetails) return null;
    
    if (marketState.isOpen) {
      return 'Live market data • Real-time analysis';
    } else if (marketState.nextOpen) {
      return `Next open: ${marketState.nextOpen} ${marketState.timeZone} • Using historical data`;
    } else {
      return 'Using historical data for analysis';
    }
  };

  return (
    <StateContainer $isOpen={marketState.isOpen} className={className}>
      <StatusDot $isOpen={marketState.isOpen} />
      <div>
        <StatusText>{getStatusText()}</StatusText>
        {showDetails && getDetailText() && (
          <DetailText>{getDetailText()}</DetailText>
        )}
      </div>
    </StateContainer>
  );
};

export default MarketStateIndicator;
