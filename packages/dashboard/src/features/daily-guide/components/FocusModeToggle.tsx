/**
 * Focus Mode Toggle Component
 *
 * Provides a toggle between quick decision mode and detailed analysis mode.
 * Designed for ADHD-optimized workflows where users can choose their
 * preferred level of information detail.
 */

import React from 'react';
import styled from 'styled-components';

export type FocusMode = 'quick' | 'detailed';

export interface FocusModeToggleProps {
  /** Current focus mode */
  mode: FocusMode;
  /** Callback when mode changes */
  onModeChange: (mode: FocusMode) => void;
  /** Whether the toggle is disabled */
  disabled?: boolean;
  /** Custom className */
  className?: string;
}

// Styled components
const ToggleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--surface-bg);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 4px;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const ToggleButton = styled.button<{ $isActive: boolean; $disabled: boolean }>`
  background: ${({ $isActive }) => ($isActive ? 'var(--primary-color)' : 'transparent')};
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: ${({ $isActive }) => ($isActive ? 'var(--primary-contrast)' : 'var(--text-secondary)')};
  cursor: ${({ $disabled }) => ($disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 90px;
  justify-content: center;
  opacity: ${({ $disabled }) => ($disabled ? 0.5 : 1)};

  &:hover:not(:disabled) {
    background: ${({ $isActive }) => ($isActive ? 'var(--primary-hover)' : 'var(--hover-bg)')};
    color: ${({ $isActive }) => ($isActive ? 'var(--primary-contrast)' : 'var(--text-primary)')};
    transform: translateY(-1px);
  }

  &:active:not(:disabled) {
    transform: scale(0.98);
  }
`;

const ModeIcon = styled.span`
  font-size: 14px;
`;

const ModeLabel = styled.span`
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ModeDescription = styled.div`
  font-size: 10px;
  color: var(--text-muted);
  margin-top: 4px;
  text-align: center;
  max-width: 200px;
`;

/**
 * Focus Mode Toggle Component
 */
export const FocusModeToggle: React.FC<FocusModeToggleProps> = ({
  mode,
  onModeChange,
  disabled = false,
  className,
}) => {
  const handleModeChange = (newMode: FocusMode) => {
    if (!disabled && newMode !== mode) {
      onModeChange(newMode);
    }
  };

  const getModeConfig = (focusMode: FocusMode) => {
    switch (focusMode) {
      case 'quick':
        return {
          icon: '⚡',
          label: 'Quick',
          description: 'Essential metrics only',
        };
      case 'detailed':
        return {
          icon: '📊',
          label: 'Detailed',
          description: 'Full analysis view',
        };
      default:
        return {
          icon: '❓',
          label: 'Unknown',
          description: '',
        };
    }
  };

  return (
    <div className={className}>
      <ToggleContainer>
        <ToggleButton
          $isActive={mode === 'quick'}
          $disabled={disabled}
          onClick={() => handleModeChange('quick')}
          disabled={disabled}
          title='Quick Decision Mode - Show only essential trading metrics'
        >
          <ModeIcon>{getModeConfig('quick').icon}</ModeIcon>
          <ModeLabel>{getModeConfig('quick').label}</ModeLabel>
        </ToggleButton>

        <ToggleButton
          $isActive={mode === 'detailed'}
          $disabled={disabled}
          onClick={() => handleModeChange('detailed')}
          disabled={disabled}
          title='Detailed Analysis Mode - Show comprehensive trading intelligence'
        >
          <ModeIcon>{getModeConfig('detailed').icon}</ModeIcon>
          <ModeLabel>{getModeConfig('detailed').label}</ModeLabel>
        </ToggleButton>
      </ToggleContainer>

      {mode && <ModeDescription>{getModeConfig(mode).description}</ModeDescription>}
    </div>
  );
};

export default FocusModeToggle;
