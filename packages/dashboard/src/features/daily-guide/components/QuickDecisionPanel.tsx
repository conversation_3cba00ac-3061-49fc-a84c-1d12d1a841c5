/**
 * Quick Decision Panel Component
 *
 * ADHD-optimized summary view showing only the essential information
 * needed for immediate trading decisions. Eliminates information overload
 * by focusing on actionable insights.
 */

import React from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';
import { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';

export interface QuickDecisionPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
  /** Callback to expand to detailed view */
  onExpandDetails?: () => void;
}

// Neurodiversity-Safe F1 Command Center Components
const PanelContainer = styled.div`
  background: var(--bg-secondary);
  border: 2px solid var(--elite-card-border);
  border-radius: 8px; /* Gentle corners, F1 angles when racing effects enabled */
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;

  /* Claude's Subtle Top Accent - No Dynamic Effects */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6; /* Fixed subtle opacity */
  }

  /* Optional F1 Flag - Very Subtle */
  &::after {
    content: '🏁';
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 18px;
    opacity: calc(0.15 * var(--racing-effects, 0)); /* Much more subtle */
    animation: gentle-glow var(--animation-speed) ease-in-out infinite;
  }

  /* Claude's Gentle Animations - Respectful of Sensory Needs */
  @keyframes subtle-racing-stripe {
    0% {
      transform: translateX(-100%);
      opacity: 0.3;
    }
    100% {
      transform: translateX(100%);
      opacity: 0.1;
    }
  }

  @keyframes gentle-glow {
    0%,
    100% {
      opacity: calc(0.15 * var(--racing-effects, 0));
    }
    50% {
      opacity: calc(0.25 * var(--racing-effects, 0));
    }
  }
`;

const PanelHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
`;

const Title = styled.h3`
  margin: 0;
  font-family: var(--font-primary); /* Orbitron for headers only */
  font-size: var(--font-size-xl); /* Reduced from 2xl */
  font-weight: 700; /* Reduced from 900 */
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 1px;

  /* Claude's Subtle Styling - No Aggressive Effects */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const QuickGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  align-items: stretch;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
`;

const MarketContextBanner = styled.div<{ $isWeekend: boolean }>`
  background: ${({ $isWeekend }) =>
    $isWeekend
      ? 'linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%)'
      : 'linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)'};
  border: 2px solid
    ${({ $isWeekend }) => ($isWeekend ? 'var(--warning-color)' : 'var(--info-color)')};
  border-radius: var(--spacing-xs);
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
`;

const PrimaryRecommendation = styled.div`
  text-align: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border: 2px solid var(--primary-color);
  border-radius: 8px;
  position: relative;
  /* Claude's Subtle Angular Cut - Only on Racing Plate */
  clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 0 100%);

  /* Claude's Subtle Racing Number */
  &::after {
    content: '#1';
    position: absolute;
    top: 8px;
    right: 12px;
    font-family: var(--font-primary);
    font-size: 24px; /* Much smaller */
    font-weight: 700; /* Reduced from 900 */
    color: var(--accent-color);
    opacity: 0.3; /* More visible than before */
    line-height: 1;
  }
`;

const ModelName = styled.div`
  font-family: var(--font-body); /* Inter for readability - Claude's recommendation */
  font-size: clamp(1.5rem, 4vw, 2.2rem); /* Claude's size from example */
  font-weight: 700; /* Reduced from 900 */
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.2;
  text-transform: uppercase;
  letter-spacing: 2px; /* Claude's letter spacing */
  position: relative;
  z-index: 2;

  /* Claude's Subtle Styling - No Aggressive Effects */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
`;

const ConfidenceBadge = styled.span<{ $confidence: string }>`
  background: var(--accent-color);
  color: var(--text-primary);
  padding: var(--spacing-xxs) var(--spacing-sm);
  border-radius: 20px;
  font-weight: 700;
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
`;

const SetupRecommendation = styled.div`
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--elite-card-border);
  border-left: 4px solid var(--primary-color);
  position: relative;

  /* Claude's Subtle Top Accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6;
  }
`;

const SetupTitle = styled.div`
  font-size: var(--font-size-xs);
  color: var(--accent-color);
  margin-bottom: var(--spacing-xs);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const SetupDescription = styled.div`
  font-size: var(--font-size-md);
  line-height: 1.4;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
`;

const SetupStats = styled.div`
  font-size: var(--font-size-xs);
  color: var(--accent-color);
  font-weight: 600;
  letter-spacing: 0.5px;
`;

const ProbabilitySection = styled.div`
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--elite-card-border);
  border-radius: 8px;
  position: relative;

  /* Claude's Subtle Top Accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6;
  }
`;

const ProbabilityValue = styled.div<{ $probability: number }>`
  font-family: var(--font-data); /* JetBrains Mono for data */
  font-size: clamp(1.5rem, 4vw, 2.5rem); /* Claude's more reasonable size */
  font-weight: 700;
  line-height: 1;
  margin-bottom: var(--spacing-xxs);

  /* Claude's Color Coding - Clear but not aggressive */
  color: ${({ $probability }) => {
    if ($probability >= 70) return 'var(--success-color)';
    if ($probability >= 50) return 'var(--warning-color)';
    return 'var(--error-color)';
  }};

  /* Claude's Subtle Text Shadow */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
`;

const ProbabilityLabel = styled.div`
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
`;

const QualityAlert = styled.div<{ $show: boolean }>`
  display: ${({ $show }) => ($show ? 'block' : 'none')};
  /* Claude's Clear but Not Alarming Approach */
  background: rgba(255, 165, 0, 0.08);
  border: 1px solid var(--warning-color);
  border-radius: 6px;
  padding: var(--spacing-md) var(--spacing-lg);
  margin: var(--spacing-md) 0;
  text-align: center;
  font-family: var(--font-body); /* Inter for readability */
  font-weight: 600;
  font-size: var(--font-size-sm);
  color: var(--warning-color);

  /* Claude's Subtle Warning Icon */
  &::before {
    content: '⚠️ ';
    font-size: 1.2rem;
    margin-right: var(--spacing-xs);
  }
`;

// Claude's Progressive Disclosure Pattern
const ExpandableSection = styled.div`
  margin-top: var(--spacing-md);
`;

const ExpandTrigger = styled.button`
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  font-weight: 600;
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(0, 255, 228, 0.05);
  border: 1px solid var(--primary-color);
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  user-select: none;
  outline: none;
  position: relative;
  width: 100%;
  font-family: var(--font-body);

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  &:hover {
    background: rgba(0, 255, 228, 0.1);
  }

  &[aria-expanded='true'] {
    background: rgba(0, 255, 228, 0.15);
  }
`;

const ExpandArrow = styled.span<{ $expanded: boolean }>`
  transition: transform 0.2s ease;
  font-size: 0.8rem;
  transform: ${({ $expanded }) => ($expanded ? 'rotate(180deg)' : 'rotate(0deg)')};
`;

const ExpandableContent = styled.div<{ $expanded: boolean }>`
  max-height: ${({ $expanded }) => ($expanded ? '300px' : '0')};
  overflow: hidden;
  transition: all 0.4s ease;
  opacity: ${({ $expanded }) => ($expanded ? '1' : '0')};
  margin-top: ${({ $expanded }) => ($expanded ? 'var(--spacing-md)' : '0')};
  padding: ${({ $expanded }) => ($expanded ? 'var(--spacing-md)' : '0')};
  background: ${({ $expanded }) => ($expanded ? 'rgba(0, 255, 228, 0.03)' : 'transparent')};
  border: ${({ $expanded }) => ($expanded ? '1px solid rgba(0, 255, 228, 0.2)' : 'none')};
  border-radius: 6px;
`;

const ReasoningContent = styled.div`
  font-family: var(--font-body); /* Inter for body text */
  font-size: var(--font-size-sm);
  line-height: 1.5;
  color: var(--text-secondary);
  font-weight: 500;
`;

/**
 * Quick Decision Panel Component
 */
export const QuickDecisionPanel: React.FC<QuickDecisionPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  setupIntelligence,
  isLoading = false,
  error = null,
}) => {
  const [isReasoningExpanded, setIsReasoningExpanded] = React.useState(false);
  const marketState = getCurrentMarketState();
  const isWeekend = marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday';
  const showContextBanner = !marketState.isOpen;

  const toggleReasoning = () => {
    setIsReasoningExpanded(!isReasoningExpanded);
  };

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-secondary)' }}>
          Analyzing market conditions...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--error-text)' }}>
          Error loading intelligence data
        </div>
      </PanelContainer>
    );
  }

  return (
    <PanelContainer
      role='region'
      aria-labelledby='elite-intelligence-title'
      aria-describedby='market-context'
    >
      <PanelHeader>
        <Title id='elite-intelligence-title'>🏁 Elite Intelligence Command Center</Title>
        <MarketStateIndicator marketState={marketState} />
      </PanelHeader>

      {showContextBanner && (
        <MarketContextBanner
          $isWeekend={isWeekend}
          id='market-context'
          role='status'
          aria-live='polite'
        >
          {isWeekend
            ? '📅 Weekend Analysis - Using most recent trading session data for pattern evaluation'
            : `⏰ ${marketState.status.replace('_', ' ')} - Analysis based on ${
                marketState.status === 'PRE_MARKET' ? 'previous session' : 'current session'
              } data`}
        </MarketContextBanner>
      )}

      <QuickGrid role='grid' aria-label='Trading intelligence summary'>
        <PrimaryRecommendation
          role='gridcell'
          aria-labelledby='model-recommendation-title'
          aria-describedby='model-confidence'
        >
          <ModelName
            id='model-recommendation-title'
            aria-label={`Recommended trading model: ${modelRecommendation.recommendedModel}`}
          >
            {modelRecommendation.recommendedModel}
          </ModelName>
          <ConfidenceBadge
            $confidence={modelRecommendation.confidence}
            id='model-confidence'
            aria-label={`Confidence level: ${modelRecommendation.probability.toFixed(0)} percent ${
              modelRecommendation.confidence
            }`}
          >
            {modelRecommendation.probability.toFixed(0)}% {modelRecommendation.confidence}
          </ConfidenceBadge>
        </PrimaryRecommendation>

        <SetupRecommendation
          role='gridcell'
          aria-labelledby='setup-title'
          aria-describedby='setup-stats'
        >
          <SetupTitle id='setup-title'>Recommended Setup</SetupTitle>
          <SetupDescription
            aria-label={`Trading setup: ${setupIntelligence.currentRecommendations.primarySetup} plus ${setupIntelligence.currentRecommendations.secondarySetup}`}
          >
            {setupIntelligence.currentRecommendations.primarySetup} +{' '}
            {setupIntelligence.currentRecommendations.secondarySetup}
          </SetupDescription>
          <SetupStats
            id='setup-stats'
            aria-label={`Expected performance: ${setupIntelligence.currentRecommendations.expectedWinRate.toFixed(
              0
            )} percent win rate, ${setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(
              1
            )} R average return`}
          >
            {setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0)}% Win Rate |{' '}
            {setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)}R Avg
          </SetupStats>
        </SetupRecommendation>

        <ProbabilitySection
          role='gridcell'
          aria-labelledby='probability-label'
          aria-describedby='probability-value'
        >
          <ProbabilityValue
            $probability={successProbability.finalProbability}
            id='probability-value'
            aria-label={`Success probability: ${successProbability.finalProbability.toFixed(
              0
            )} percent`}
          >
            {successProbability.finalProbability.toFixed(0)}%
          </ProbabilityValue>
          <ProbabilityLabel id='probability-label'>Success Probability</ProbabilityLabel>
        </ProbabilitySection>
      </QuickGrid>

      <QualityAlert
        $show={patternQuality.totalScore < 2.0}
        role='alert'
        aria-live='assertive'
        aria-label={`Pattern quality warning: ${
          patternQuality.rating
        } quality with score ${patternQuality.totalScore.toFixed(1)} out of 5. Recommendation: ${
          patternQuality.recommendation
        }`}
      >
        ⚠️ Current Pattern Quality: {patternQuality.rating} ({patternQuality.totalScore.toFixed(1)}
        /5.0) - {patternQuality.recommendation}
      </QualityAlert>

      {/* Claude's Progressive Disclosure Pattern */}
      <ExpandableSection>
        <ExpandTrigger
          onClick={toggleReasoning}
          aria-expanded={isReasoningExpanded}
          aria-controls='reasoning-content'
          aria-label={`Expand ${modelRecommendation.recommendedModel} analysis details`}
        >
          <ExpandArrow $expanded={isReasoningExpanded}>▼</ExpandArrow>
          <span>WHY {modelRecommendation.recommendedModel}?</span>
        </ExpandTrigger>
        <ExpandableContent
          $expanded={isReasoningExpanded}
          id='reasoning-content'
          aria-hidden={!isReasoningExpanded}
        >
          <ReasoningContent aria-label={`Model reasoning: ${modelRecommendation.reasoning}`}>
            {modelRecommendation.reasoning}
          </ReasoningContent>
        </ExpandableContent>
      </ExpandableSection>
    </PanelContainer>
  );
};

export default QuickDecisionPanel;
