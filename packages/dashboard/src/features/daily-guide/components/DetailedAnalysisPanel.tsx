/**
 * Detailed Analysis Panel Component
 *
 * Expandable sections showing comprehensive trading intelligence.
 * Organized into logical sections with progressive disclosure to prevent
 * information overload while providing access to detailed analysis.
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';
import { ModelRecommendation } from '../hooks/useModelSelectionEngine';
import { PatternQualityScore } from '../hooks/usePatternQualityScoring';
import { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';

export interface DetailedAnalysisPanelProps {
  /** Model recommendation data */
  modelRecommendation: ModelRecommendation;
  /** Pattern quality analysis */
  patternQuality: PatternQualityScore;
  /** Success probability calculation */
  successProbability: SuccessProbability;
  /** Setup intelligence data */
  setupIntelligence: SetupIntelligenceData;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
}

// Neurodiversity-Safe F1 Command Center Styled Components
const PanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
`;

const ExpandableSection = styled.div`
  background: var(--elite-card-bg);
  border: 2px solid var(--elite-card-border);
  border-radius: 8px; /* Fixed corners - Claude's gentle approach */
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  position: relative;

  /* Claude's Subtle Top Accent - No Dynamic Effects */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6; /* Fixed subtle opacity */
  }
`;

const SectionHeader = styled.button<{ $isExpanded: boolean }>`
  width: 100%;
  background: var(--elite-section-bg);
  border: none;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease; /* Claude's gentle timing */
  position: relative;

  &:hover:not(:disabled) {
    background: var(--hover-bg);
    transform: translateY(-2px); /* Claude's subtle lift */
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  border-bottom: ${({ $isExpanded }) =>
    $isExpanded ? '1px solid var(--elite-card-border)' : 'none'};
`;

const SectionTitle = styled.div`
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-family: var(--font-primary); /* Orbitron for headers only */
  font-size: var(--font-size-lg);
  font-weight: 700; /* Reduced from aggressive weights */
  color: var(--text-primary);
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const SectionSummary = styled.div`
  font-family: var(--font-body); /* Inter for readability */
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  margin-top: 2px;
  font-weight: 500;
`;

const ExpandIcon = styled.span<{ $isExpanded: boolean }>`
  font-size: var(--font-size-sm);
  transition: transform 0.3s ease; /* Claude's gentle timing */
  transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};
  color: var(--primary-color);
`;

const SectionContent = styled.div<{ $isExpanded: boolean }>`
  max-height: ${({ $isExpanded }) => ($isExpanded ? '1000px' : '0')};
  overflow: hidden;
  transition: all 0.4s ease; /* Claude's gentle timing */
  opacity: ${({ $isExpanded }) => ($isExpanded ? 1 : 0)};
  padding: ${({ $isExpanded }) => ($isExpanded ? 'var(--spacing-lg)' : '0 var(--spacing-lg)')};
`;

const DetailGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
`;

const DetailItem = styled.div`
  background: var(--elite-section-bg);
  border: 1px solid var(--elite-card-border);
  border-radius: 8px; /* Fixed corners - Claude's approach */
  padding: var(--spacing-lg);
  transition: all 0.3s ease; /* Claude's gentle timing */
  position: relative;

  /* Claude's Subtle Top Accent */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6;
  }

  &:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px); /* Claude's subtle lift */
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    background: var(--hover-bg);
  }
`;

const DetailLabel = styled.div`
  font-family: var(--font-body); /* Inter for labels */
  font-size: var(--font-size-xs);
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-xs);
`;

const DetailValue = styled.div`
  font-family: var(--font-data); /* JetBrains Mono for data values */
  font-size: var(--font-size-lg);
  font-weight: 700; /* Reduced from aggressive weights */
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
`;

const DetailDescription = styled.div`
  font-family: var(--font-body); /* Inter for descriptions */
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: 1.4;
  font-weight: 500;
`;

/**
 * Detailed Analysis Panel Component
 */
export const DetailedAnalysisPanel: React.FC<DetailedAnalysisPanelProps> = ({
  modelRecommendation,
  patternQuality,
  successProbability,
  isLoading = false,
  error = null,
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['model']));

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  if (isLoading) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }}>
          Loading detailed analysis...
        </div>
      </PanelContainer>
    );
  }

  if (error) {
    return (
      <PanelContainer>
        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--error-text)' }}>
          Error loading detailed analysis: {error}
        </div>
      </PanelContainer>
    );
  }

  const isExpanded = (sectionId: string) => expandedSections.has(sectionId);

  return (
    <PanelContainer>
      {/* Model Selection Analysis - Claude's Neurodiversity-Safe F1 Command Center */}
      <ExpandableSection>
        <SectionHeader
          $isExpanded={isExpanded('model')}
          onClick={() => toggleSection('model')}
          aria-expanded={isExpanded('model')}
          aria-controls='model-analysis-content'
          aria-label='Model Selection Analysis Command Panel'
          role='button'
          tabIndex={0}
        >
          <div>
            <SectionTitle>🎯 Model Selection Analysis</SectionTitle>
            <SectionSummary>
              {modelRecommendation.recommendedModel} • {modelRecommendation.probability.toFixed(0)}%
              confidence
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('model')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent
          $isExpanded={isExpanded('model')}
          id='model-analysis-content'
          aria-hidden={!isExpanded('model')}
          role='region'
          aria-labelledby='model-analysis-title'
        >
          <DetailGrid role='grid' aria-label='Model selection metrics'>
            <DetailItem role='gridcell'>
              <DetailLabel>Recommended Model</DetailLabel>
              <DetailValue
                aria-label={`Recommended trading model: ${modelRecommendation.recommendedModel}`}
              >
                {modelRecommendation.recommendedModel}
              </DetailValue>
              <DetailDescription>
                {modelRecommendation.confidence} confidence •{' '}
                {modelRecommendation.probability.toFixed(0)}% probability
              </DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Alternative Model</DetailLabel>
              <DetailValue
                aria-label={`Alternative model: ${modelRecommendation.alternativeModel}`}
              >
                {modelRecommendation.alternativeModel}
              </DetailValue>
              <DetailDescription>{modelRecommendation.alternativeCondition}</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Market Volatility</DetailLabel>
              <DetailValue
                aria-label={`Market volatility: ${modelRecommendation.marketConditions.volatility}`}
              >
                {modelRecommendation.marketConditions.volatility.toUpperCase()}
              </DetailValue>
              <DetailDescription>Current market volatility assessment</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>HTF Trend</DetailLabel>
              <DetailValue
                aria-label={`Higher timeframe trend: ${modelRecommendation.marketConditions.htfTrend}`}
              >
                {modelRecommendation.marketConditions.htfTrend.toUpperCase()}
              </DetailValue>
              <DetailDescription>Higher timeframe trend direction</DetailDescription>
            </DetailItem>
          </DetailGrid>

          <DetailDescription
            style={{
              padding: 'var(--spacing-lg)',
              background: 'var(--elite-section-bg)',
              borderRadius: '8px',
              border: '1px solid var(--elite-card-border)',
              fontFamily: 'var(--font-body)',
              lineHeight: '1.5',
            }}
            aria-label={`Model reasoning: ${modelRecommendation.reasoning}`}
          >
            <strong>Reasoning:</strong> {modelRecommendation.reasoning}
          </DetailDescription>
        </SectionContent>
      </ExpandableSection>

      {/* Pattern Quality Analysis - Claude's Neurodiversity-Safe F1 Command Center */}
      <ExpandableSection>
        <SectionHeader
          $isExpanded={isExpanded('pattern')}
          onClick={() => toggleSection('pattern')}
          aria-expanded={isExpanded('pattern')}
          aria-controls='pattern-analysis-content'
          aria-label='Pattern Quality Analysis Command Panel'
          role='button'
          tabIndex={0}
        >
          <div>
            <SectionTitle>📊 Pattern Quality Analysis</SectionTitle>
            <SectionSummary>
              {patternQuality.totalScore.toFixed(1)}/5.0 • {patternQuality.rating}
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('pattern')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent
          $isExpanded={isExpanded('pattern')}
          id='pattern-analysis-content'
          aria-hidden={!isExpanded('pattern')}
          role='region'
          aria-labelledby='pattern-analysis-title'
        >
          <DetailGrid role='grid' aria-label='Pattern quality metrics'>
            <DetailItem role='gridcell'>
              <DetailLabel>PD Array Confluence</DetailLabel>
              <DetailValue
                aria-label={`PD Array Confluence score: ${patternQuality.breakdown.pdArrayConfluence.toFixed(
                  1
                )} out of 5`}
              >
                {patternQuality.breakdown.pdArrayConfluence.toFixed(1)}
              </DetailValue>
              <DetailDescription>Multiple PD array alignment strength</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>FVG Characteristics</DetailLabel>
              <DetailValue
                aria-label={`FVG Characteristics score: ${patternQuality.breakdown.fvgCharacteristics.toFixed(
                  1
                )} out of 5`}
              >
                {patternQuality.breakdown.fvgCharacteristics.toFixed(1)}
              </DetailValue>
              <DetailDescription>Fair value gap quality assessment</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>RD Strength</DetailLabel>
              <DetailValue
                aria-label={`RD Strength score: ${patternQuality.breakdown.rdStrength.toFixed(
                  1
                )} out of 5`}
              >
                {patternQuality.breakdown.rdStrength.toFixed(1)}
              </DetailValue>
              <DetailDescription>Reaction/displacement power analysis</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Confirmation Signals</DetailLabel>
              <DetailValue
                aria-label={`Confirmation Signals score: ${patternQuality.breakdown.confirmationSignals.toFixed(
                  1
                )} out of 5`}
              >
                {patternQuality.breakdown.confirmationSignals.toFixed(1)}
              </DetailValue>
              <DetailDescription>Supporting technical signal strength</DetailDescription>
            </DetailItem>
          </DetailGrid>

          <DetailDescription
            style={{
              padding: 'var(--spacing-lg)',
              background: 'var(--elite-section-bg)',
              borderRadius: '8px',
              border: '1px solid var(--elite-card-border)',
              fontFamily: 'var(--font-body)',
              lineHeight: '1.5',
            }}
            aria-label={`Pattern quality recommendation: ${patternQuality.recommendation}`}
          >
            <strong>Recommendation:</strong> {patternQuality.recommendation}
          </DetailDescription>
        </SectionContent>
      </ExpandableSection>

      {/* Success Probability Analysis - Claude's Neurodiversity-Safe F1 Command Center */}
      <ExpandableSection>
        <SectionHeader
          $isExpanded={isExpanded('probability')}
          onClick={() => toggleSection('probability')}
          aria-expanded={isExpanded('probability')}
          aria-controls='probability-analysis-content'
          aria-label='Success Probability Analysis Command Panel'
          role='button'
          tabIndex={0}
        >
          <div>
            <SectionTitle>🎯 Success Probability Analysis</SectionTitle>
            <SectionSummary>
              {successProbability.finalProbability.toFixed(0)}% •{' '}
              {successProbability.recommendation.replace('_', ' ')}
            </SectionSummary>
          </div>
          <ExpandIcon $isExpanded={isExpanded('probability')}>▼</ExpandIcon>
        </SectionHeader>

        <SectionContent
          $isExpanded={isExpanded('probability')}
          id='probability-analysis-content'
          aria-hidden={!isExpanded('probability')}
          role='region'
          aria-labelledby='probability-analysis-title'
        >
          <DetailGrid role='grid' aria-label='Success probability metrics'>
            <DetailItem role='gridcell'>
              <DetailLabel>Base Model Win Rate</DetailLabel>
              <DetailValue
                aria-label={`Base model win rate: ${successProbability.breakdown.baseModelWinRate.toFixed(
                  0
                )} percent`}
              >
                {successProbability.breakdown.baseModelWinRate.toFixed(0)}%
              </DetailValue>
              <DetailDescription>Historical model performance data</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Session Bonus</DetailLabel>
              <DetailValue
                aria-label={`Session bonus: ${
                  successProbability.breakdown.sessionBonus > 0 ? 'plus' : ''
                } ${successProbability.breakdown.sessionBonus.toFixed(0)} percent`}
              >
                {successProbability.breakdown.sessionBonus > 0 ? '+' : ''}
                {successProbability.breakdown.sessionBonus.toFixed(0)}%
              </DetailValue>
              <DetailDescription>Current session performance enhancement</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Quality Bonus</DetailLabel>
              <DetailValue
                aria-label={`Quality bonus: ${
                  successProbability.breakdown.qualityBonus > 0 ? 'plus' : ''
                } ${successProbability.breakdown.qualityBonus.toFixed(0)} percent`}
              >
                {successProbability.breakdown.qualityBonus > 0 ? '+' : ''}
                {successProbability.breakdown.qualityBonus.toFixed(0)}%
              </DetailValue>
              <DetailDescription>Pattern quality enhancement factor</DetailDescription>
            </DetailItem>

            <DetailItem role='gridcell'>
              <DetailLabel>Expected R-Multiple</DetailLabel>
              <DetailValue
                aria-label={`Expected R-Multiple range: ${successProbability.expectedRMultiple.min.toFixed(
                  1
                )} to ${successProbability.expectedRMultiple.max.toFixed(
                  1
                )}, average ${successProbability.expectedRMultiple.average.toFixed(1)}`}
              >
                {successProbability.expectedRMultiple.min.toFixed(1)} -{' '}
                {successProbability.expectedRMultiple.max.toFixed(1)}
              </DetailValue>
              <DetailDescription>
                Risk-reward range (avg: {successProbability.expectedRMultiple.average.toFixed(1)})
              </DetailDescription>
            </DetailItem>
          </DetailGrid>
        </SectionContent>
      </ExpandableSection>
    </PanelContainer>
  );
};

export default DetailedAnalysisPanel;
