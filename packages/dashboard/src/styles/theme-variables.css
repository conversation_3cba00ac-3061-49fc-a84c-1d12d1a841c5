/**
 * Comprehensive Theme Variable System
 *
 * This file defines CSS variables for all themes to ensure consistent
 * color usage across the entire application and eliminate hardcoded colors.
 */

/* =============================================================================
   SEMANTIC COLOR SYSTEM (Theme-Agnostic Component Variables)
   ============================================================================= */

:root {
  /* Component-specific semantic variables */
  --session-card-bg: var(--bg-card);
  --session-card-border: var(--border-primary);
  --session-card-accent: var(--primary-color);
  --session-card-active-accent: var(--accent-color);

  /* PD Array semantic variables */
  --pd-array-fvg-bg: var(--info-color);
  --pd-array-nwog-bg: var(--success-color);
  --pd-array-rd-bg: var(--error-color);
  --pd-array-liquidity-bg: var(--secondary-color);

  /* Interactive states */
  --hover-overlay: rgba(255, 255, 255, 0.1);
  --active-overlay: rgba(255, 255, 255, 0.2);

  /* Professional shadow system */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.2);
  --shadow-focus: 0 0 0 2px var(--primary-color);
  --shadow-active: 0 0 0 1px var(--primary-color);
}

/* =============================================================================
   MERCEDES GREEN THEME (F1 Team Theme)
   ============================================================================= */

[data-theme='mercedes-green'] {
  /* Core Brand Colors */
  --primary-color: #00d2be;
  --primary-dark: #00a896;
  --primary-light: #00ffe5;
  --secondary-color: #0600ef;
  --accent-color: #00ffe5;

  /* High Contrast Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-accent: #00ffe5;
  --text-muted: #9ca3af;
  --text-inverse: #1a1a1a;

  /* Professional Backgrounds */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2a2a2a;
  --bg-card: #1a1a1a;
  --bg-elevated: #2a2a2a;
  --bg-surface: #2a2a2a;

  /* Border Colors */
  --border-primary: #333333;
  --border-secondary: #404040;
  --border-accent: #00d2be;

  /* Component Specific Colors */
  --model-name-color: #00d2be; /* RD-CONT/FVG-RD model names */
  --model-card-bg: #00d2be1a; /* Model card backgrounds (10% opacity) */
  --model-card-border: #00d2be4d; /* Model card borders (30% opacity) */
  --badge-priority-bg: #00d2be; /* Priority badges */
  --badge-priority-text: #ffffff;
  --card-accent-border: #00d2be; /* Card accent borders */
  --live-indicator-color: #00ffe5; /* Live indicators */
  --session-active-border: #00d2be66; /* Session window borders (40% opacity) */

  /* Status Colors - Mercedes F1 Semantic */
  --success-color: #00d2be; /* Mercedes green for success */
  --warning-color: #ff8700; /* McLaren orange for warnings */
  --error-color: #e10600; /* Ferrari red for critical alerts only */
  --info-color: #0600ef; /* Racing blue for information */

  /* Session States - F1 Racing Theme */
  --session-active: #00d2be;
  --session-optimal: #00ffe5;
  --session-caution: #ffd320;
  --session-transition: #0600ef;
  --session-inactive: #9ca3af;

  /* Session Background States */
  --session-active-bg: rgba(0, 210, 190, 0.1);
  --session-optimal-bg: rgba(0, 255, 229, 0.08);
  --session-active-border: rgba(0, 210, 190, 0.4);
  --session-optimal-border: rgba(0, 255, 229, 0.3);

  /* Interactive States */
  --hover-bg: #00d2be1a;
  --active-bg: #00d2be33;
  --focus-ring: #00d2be;

  /* Elite Intelligence Colors */
  --setup-success-bg: rgba(26, 26, 26, 0.8);
  --setup-success-border: #333333;
  --setup-success-text: #00ffe5;

  /* Priority Levels */
  --priority-high-bg: rgba(0, 255, 229, 0.15);
  --priority-high-text: #00ffe5;
  --priority-high-border: #00ffe5;

  --priority-medium-bg: rgba(0, 210, 190, 0.1);
  --priority-medium-text: #00d2be;
  --priority-medium-border: #00d2be;

  --priority-low-bg: rgba(0, 168, 150, 0.08);
  --priority-low-text: #00a896;
  --priority-low-border: #00a896;

  /* Setup Intelligence */
  --setup-intelligence-bg: rgba(26, 26, 26, 0.6);
  --setup-intelligence-accent: #00ffe5;
  --setup-intelligence-border: rgba(51, 51, 51, 0.8);

  /* Pattern Quality */
  --pattern-excellent-bg: rgba(0, 255, 229, 0.2);
  --pattern-excellent-text: #00ffe5;
  --pattern-excellent-border: rgba(0, 255, 229, 0.4);

  --pattern-good-bg: rgba(0, 210, 190, 0.15);
  --pattern-good-text: #00d2be;
  --pattern-good-border: rgba(0, 210, 190, 0.3);

  --pattern-average-bg: rgba(0, 168, 150, 0.1);
  --pattern-average-text: #00a896;
  --pattern-average-border: rgba(0, 168, 150, 0.25);

  --pattern-poor-bg: rgba(192, 192, 192, 0.1);
  --pattern-poor-text: #c0c0c0;
  --pattern-poor-border: rgba(192, 192, 192, 0.3);

  /* Recommendation Badges */
  --recommendation-prioritize-bg: #00ffe5;
  --recommendation-prioritize-text: #1b1b1b;

  --recommendation-increase-bg: #00d2be;
  --recommendation-increase-text: #1b1b1b;

  --recommendation-standard-bg: #1a1a1a;
  --recommendation-standard-text: #00ffe5;

  --recommendation-reduce-bg: #333333;
  --recommendation-reduce-text: #e5e7eb;

  --recommendation-avoid-bg: #e10600;
  --recommendation-avoid-text: #ffffff;

  /* CLEAN F1 PIT WALL STYLE - Session Cards */
  --session-card-bg: #1a1a1a;
  --session-card-border: #333333;
  --session-card-accent: #00d2be;
  --session-card-active-accent: #00ffe5;
  --session-card-shadow: rgba(0, 0, 0, 0.2);
  --session-card-active-shadow: rgba(0, 210, 190, 0.1);

  /* HIGH CONTRAST TEXT */
  --session-text-primary: #ffffff;
  --session-text-secondary: #e5e7eb;
  --session-text-accent: #00ffe5;

  /* Theme-specific shadow enhancements */
  --shadow-accent: 0 0 8px rgba(0, 210, 190, 0.2);
}

/* =============================================================================
   F1 OFFICIAL THEME (Authentic F1 App Style)
   ============================================================================= */

[data-theme='f1-official'] {
  /* F1 Brand Colors - Official Red Primary */
  --primary-color: #e10600;
  --primary-dark: #b30500;
  --primary-light: #ff1e1e;
  --secondary-color: #15151e;
  --accent-color: #ffd700;

  /* F1 Timing Screen Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #b8b8c8;
  --text-accent: #ffd700;
  --text-muted: #8b8b9b;
  --text-inverse: #15151e;

  /* F1 App Background Colors */
  --bg-primary: #15151e;
  --bg-secondary: #1e1e2e;
  --bg-card: #2a2a3a;
  --bg-elevated: #353545;
  --bg-surface: #1e1e2e;

  /* Border Colors */
  --border-primary: #3a3a4a;
  --border-secondary: #4a4a5a;
  --border-accent: #e10600;

  /* Component Specific Colors */
  --model-name-color: #e10600; /* RD-CONT/FVG-RD model names */
  --model-card-bg: #e106001a; /* Model card backgrounds (10% opacity) */
  --model-card-border: #e106004d; /* Model card borders (30% opacity) */
  --badge-priority-bg: #e10600; /* Priority badges */
  --badge-priority-text: #ffffff;
  --card-accent-border: #e10600; /* Card accent borders */
  --live-indicator-color: #ffd700; /* Live indicators */
  --session-active-border: #e1060066; /* Session window borders (40% opacity) */

  /* F1 Timing Screen Status Colors */
  --success-color: #00ff41; /* F1 timing green (sector improvements) */
  --warning-color: #ffd700; /* F1 yellow flags */
  --error-color: #ff1e1e; /* F1 timing red (sector losses) */
  --info-color: #00b4d8; /* F1 information blue */

  /* F1 Session States */
  --session-active: #e10600;
  --session-optimal: #ffd700;
  --session-caution: #ff8700;
  --session-transition: #00b4d8;
  --session-inactive: #8b8b9b;

  /* Session Background States */
  --session-active-bg: rgba(225, 6, 0, 0.1);
  --session-optimal-bg: rgba(255, 215, 0, 0.08);
  --session-active-border: rgba(225, 6, 0, 0.4);
  --session-optimal-border: rgba(255, 215, 0, 0.3);

  /* Interactive States */
  --hover-bg: #e106001a;
  --active-bg: #e1060033;
  --focus-ring: #e10600;

  /* Elite Intelligence Colors */
  --setup-success-bg: rgba(42, 42, 58, 0.8);
  --setup-success-border: #3a3a4a;
  --setup-success-text: #ffd700;

  /* Priority Levels */
  --priority-high-bg: rgba(255, 215, 0, 0.15);
  --priority-high-text: #ffd700;
  --priority-high-border: #ffd700;

  --priority-medium-bg: rgba(225, 6, 0, 0.1);
  --priority-medium-text: #e10600;
  --priority-medium-border: #e10600;

  --priority-low-bg: rgba(184, 184, 200, 0.08);
  --priority-low-text: #b8b8c8;
  --priority-low-border: #b8b8c8;

  /* Setup Intelligence */
  --setup-intelligence-bg: rgba(42, 42, 58, 0.6);
  --setup-intelligence-accent: #ffd700;
  --setup-intelligence-border: rgba(58, 58, 74, 0.8);

  /* Pattern Quality */
  --pattern-excellent-bg: rgba(0, 255, 65, 0.2);
  --pattern-excellent-text: #00ff41;
  --pattern-excellent-border: rgba(0, 255, 65, 0.4);

  --pattern-good-bg: rgba(255, 215, 0, 0.15);
  --pattern-good-text: #ffd700;
  --pattern-good-border: rgba(255, 215, 0, 0.3);

  --pattern-average-bg: rgba(225, 6, 0, 0.1);
  --pattern-average-text: #e10600;
  --pattern-average-border: rgba(225, 6, 0, 0.25);

  --pattern-poor-bg: rgba(255, 30, 30, 0.1);
  --pattern-poor-text: #ff1e1e;
  --pattern-poor-border: rgba(255, 30, 30, 0.3);

  /* Recommendation Badges */
  --recommendation-prioritize-bg: #ffd700;
  --recommendation-prioritize-text: #15151e;

  --recommendation-increase-bg: #00ff41;
  --recommendation-increase-text: #15151e;

  --recommendation-standard-bg: #2a2a3a;
  --recommendation-standard-text: #ffd700;

  --recommendation-reduce-bg: #3a3a4a;
  --recommendation-reduce-text: #b8b8c8;

  --recommendation-avoid-bg: #e10600;
  --recommendation-avoid-text: #ffffff;

  /* F1 OFFICIAL SESSION CARDS */
  --session-card-bg: #2a2a3a;
  --session-card-border: #3a3a4a;
  --session-card-accent: #e10600;
  --session-card-active-accent: #ffd700;
  --session-card-shadow: rgba(0, 0, 0, 0.3);
  --session-card-active-shadow: rgba(225, 6, 0, 0.2);

  /* F1 HIGH CONTRAST TEXT */
  --session-text-primary: #ffffff;
  --session-text-secondary: #b8b8c8;
  --session-text-accent: #ffd700;

  /* Theme-specific shadow enhancements */
  --shadow-accent: 0 0 8px rgba(225, 6, 0, 0.3);
}

/* =============================================================================
   DARK THEME (Generic Dark Theme)
   ============================================================================= */

[data-theme='dark'] {
  /* Generic Dark Colors */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary-color: #4f46e5;
  --accent-color: #10b981;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #e5e7eb;
  --text-accent: #6366f1;
  --text-muted: #9ca3af;
  --text-inverse: #111827;

  /* Background Colors */
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-card: #374151;
  --bg-elevated: #4b5563;
  --bg-surface: #1f2937;

  /* Component Specific Colors */
  --model-name-color: #6366f1; /* RD-CONT/FVG-RD model names */
  --model-card-bg: #6366f11a; /* Model card backgrounds (10% opacity) */
  --model-card-border: #6366f14d; /* Model card borders (30% opacity) */
  --badge-priority-bg: #10b981; /* Priority badges */
  --badge-priority-text: #ffffff;
  --card-accent-border: #6366f1; /* Card accent borders */
  --live-indicator-color: #10b981; /* Live indicators */
  --session-active-border: #6366f166; /* Session window borders (40% opacity) */

  /* Status Colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Session Background States */
  --session-active-bg: rgba(99, 102, 241, 0.1);
  --session-optimal-bg: rgba(16, 185, 129, 0.08);
  --session-optimal-border: rgba(16, 185, 129, 0.3);

  /* Interactive States */
  --hover-bg: #6366f11a;
  --active-bg: #6366f133;
  --focus-ring: #6366f1;

  /* Border Colors */
  --border-primary: #4b5563;
  --border-secondary: #6b7280;
  --border-accent: #6366f1;

  /* Elite Intelligence Colors */
  --setup-success-bg: rgba(99, 102, 241, 0.1);
  --setup-success-border: #6366f1;
  --setup-success-text: #a5b4fc;

  /* Priority Levels */
  --priority-high-bg: rgba(16, 185, 129, 0.15);
  --priority-high-text: #10b981;
  --priority-high-border: #10b981;

  --priority-medium-bg: rgba(99, 102, 241, 0.1);
  --priority-medium-text: #6366f1;
  --priority-medium-border: #6366f1;

  --priority-low-bg: rgba(156, 163, 175, 0.08);
  --priority-low-text: #9ca3af;
  --priority-low-border: #9ca3af;

  /* Setup Intelligence */
  --setup-intelligence-bg: rgba(99, 102, 241, 0.08);
  --setup-intelligence-accent: #6366f1;
  --setup-intelligence-border: rgba(99, 102, 241, 0.3);

  /* Pattern Quality */
  --pattern-excellent-bg: rgba(16, 185, 129, 0.2);
  --pattern-excellent-text: #10b981;
  --pattern-excellent-border: rgba(16, 185, 129, 0.4);

  --pattern-good-bg: rgba(99, 102, 241, 0.15);
  --pattern-good-text: #6366f1;
  --pattern-good-border: rgba(99, 102, 241, 0.3);

  --pattern-average-bg: rgba(245, 158, 11, 0.1);
  --pattern-average-text: #f59e0b;
  --pattern-average-border: rgba(245, 158, 11, 0.25);

  --pattern-poor-bg: rgba(239, 68, 68, 0.1);
  --pattern-poor-text: #ef4444;
  --pattern-poor-border: rgba(239, 68, 68, 0.3);

  /* Recommendation Badges */
  --recommendation-prioritize-bg: #10b981;
  --recommendation-prioritize-text: #ffffff;

  --recommendation-increase-bg: #6366f1;
  --recommendation-increase-text: #ffffff;

  --recommendation-standard-bg: #a855f7;
  --recommendation-standard-text: #ffffff;

  --recommendation-reduce-bg: #f59e0b;
  --recommendation-reduce-text: #ffffff;

  --recommendation-avoid-bg: #6b7280;
  --recommendation-avoid-text: #ffffff;

  /* DARK THEME SESSION CARDS */
  --session-card-bg: #374151;
  --session-card-border: #4b5563;
  --session-card-accent: #6366f1;
  --session-card-active-accent: #10b981;
  --session-card-shadow: rgba(0, 0, 0, 0.25);
  --session-card-active-shadow: rgba(99, 102, 241, 0.15);

  /* DARK THEME TEXT */
  --session-text-primary: #ffffff;
  --session-text-secondary: #e5e7eb;
  --session-text-accent: #a5b4fc;

  /* Theme-specific shadow enhancements */
  --shadow-accent: 0 0 8px rgba(99, 102, 241, 0.2);
}

/* =============================================================================
   GLOBAL UTILITY CLASSES
   ============================================================================= */

.theme-primary-color {
  color: var(--primary-color) !important;
}

.theme-model-name {
  color: var(--model-name-color) !important;
}

.theme-card-bg {
  background: var(--model-card-bg) !important;
}

.theme-card-border {
  border-color: var(--card-accent-border) !important;
}

.theme-live-indicator {
  background: var(--live-indicator-color) !important;
}

.theme-session-border {
  border-color: var(--session-active-border) !important;
}
