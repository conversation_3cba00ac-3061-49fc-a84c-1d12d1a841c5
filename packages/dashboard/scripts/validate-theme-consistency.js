#!/usr/bin/env node
/**
 * Theme Consistency Validator
 *
 * Scans the codebase for hardcoded colors and ensures all styling
 * uses the unified theme system.
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = new URL('.', import.meta.url).pathname;

// Color patterns to detect
const COLOR_PATTERNS = {
  hex: /#[0-9a-fA-F]{3,8}/g,
  rgb: /rgb\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)/g,
  rgba: /rgba\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)/g,
  hsl: /hsl\s*\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*\)/g,
  hsla: /hsla\s*\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*,\s*[\d.]+\s*\)/g,
};

// Allowed hardcoded colors (exceptions)
const ALLOWED_COLORS = new Set([
  '#fff',
  '#ffffff',
  '#000',
  '#000000', // Basic black/white
  'transparent',
  'inherit',
  'currentColor', // CSS keywords
  'rgba(0, 0, 0, 0)',
  'rgba(255, 255, 255, 0)', // Transparent variants
  'rgba(0, 0, 0, 0.1)',
  'rgba(0, 0, 0, 0.2)',
  'rgba(0, 0, 0, 0.3)', // Common shadows
  'rgba(255, 255, 255, 0.1)',
  'rgba(255, 255, 255, 0.2)',
  'rgba(255, 255, 255, 0.3)', // Common overlays
]);

// Files to exclude from validation
const EXCLUDED_FILES = new Set([
  'unified-theme.css',
  'theme-variables.css',
  'variables.css',
  'f1-theme.css',
  'DailyGuide.test.js',
  'DailyGuide.test.tsx',
  'EliteICTIntelligence.theme.test.js',
  'EliteICTIntelligence.theme.test.tsx',
  'validate-theme-architecture.js',
  'dolAnalysis.ts',
  'patternQuality.ts',
]);

/**
 * Recursively scan directory for files
 */
function scanDirectory(dir, extensions = ['.tsx', '.ts', '.css', '.js', '.jsx']) {
  const files = [];

  function scan(currentDir) {
    const items = readdirSync(currentDir);

    for (const item of items) {
      const fullPath = join(currentDir, item);
      const stat = statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules and build directories
        if (!['node_modules', 'dist', 'build', 'dev-dist'].includes(item)) {
          scan(fullPath);
        }
      } else if (extensions.includes(extname(item))) {
        files.push(fullPath);
      }
    }
  }

  scan(dir);
  return files;
}

/**
 * Find hardcoded colors in file content
 */
function findHardcodedColors(content, filePath) {
  const violations = [];

  // Skip if this is an excluded file
  const fileName = filePath.split('/').pop();
  if (EXCLUDED_FILES.has(fileName)) {
    return violations;
  }

  const lines = content.split('\n');

  for (const [patternName, pattern] of Object.entries(COLOR_PATTERNS)) {
    let match;

    while ((match = pattern.exec(content)) !== null) {
      const color = match[0];

      // Skip allowed colors
      if (ALLOWED_COLORS.has(color.toLowerCase())) {
        continue;
      }

      // Find line number
      const beforeMatch = content.substring(0, match.index);
      const lineNumber = beforeMatch.split('\n').length;
      const line = lines[lineNumber - 1];

      violations.push({
        file: filePath,
        line: lineNumber,
        color,
        pattern: patternName,
        context: line.trim(),
      });
    }

    // Reset regex
    pattern.lastIndex = 0;
  }

  return violations;
}

/**
 * Check if file uses CSS variables properly
 */
function validateCSSVariableUsage(content, filePath) {
  const issues = [];

  // Check for var() usage in CSS/styled-components
  const hasVarUsage = /var\(--[\w-]+\)/.test(content);
  const hasHardcodedColors = Object.values(COLOR_PATTERNS).some((pattern) => {
    pattern.lastIndex = 0;
    const match = pattern.test(content);
    pattern.lastIndex = 0;
    return match;
  });

  if (hasHardcodedColors && !hasVarUsage) {
    issues.push({
      file: filePath,
      type: 'missing-css-variables',
      message: 'File contains hardcoded colors but no CSS variable usage',
    });
  }

  return issues;
}

/**
 * Main validation function
 */
function validateThemeConsistency() {
  console.log('🔍 Validating theme consistency...');

  const srcDir = join(__dirname, '../src');
  const files = scanDirectory(srcDir);

  let totalViolations = 0;
  let totalIssues = 0;
  const violationsByFile = new Map();

  for (const file of files) {
    try {
      const content = readFileSync(file, 'utf8');
      const violations = findHardcodedColors(content, file);
      const issues = validateCSSVariableUsage(content, file);

      if (violations.length > 0) {
        violationsByFile.set(file, violations);
        totalViolations += violations.length;
      }

      totalIssues += issues.length;
    } catch (error) {
      console.warn(`⚠️  Could not read file: ${file}`);
    }
  }

  // Report results
  console.log('\n📊 Theme Consistency Report');
  console.log('='.repeat(50));
  console.log(`Files scanned: ${files.length}`);
  console.log(`Hardcoded color violations: ${totalViolations}`);
  console.log(`CSS variable issues: ${totalIssues}`);

  if (totalViolations > 0) {
    console.log('\n❌ Hardcoded Color Violations:');

    for (const [file, violations] of violationsByFile) {
      console.log(`\n📄 ${file.replace(srcDir, 'src')}`);

      for (const violation of violations) {
        console.log(`  Line ${violation.line}: ${violation.color} (${violation.pattern})`);
        console.log(`    Context: ${violation.context}`);
      }
    }

    console.log('\n💡 Fix these by replacing hardcoded colors with CSS variables:');
    console.log('   Example: color: #e10600 → color: var(--primary-color)');
  }

  if (totalViolations === 0 && totalIssues === 0) {
    console.log('\n✅ All theme consistency checks passed!');
    console.log('🎨 Your codebase uses the unified theme system correctly.');
    return true;
  }

  return false;
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = validateThemeConsistency();
  process.exit(success ? 0 : 1);
}

export { validateThemeConsistency };
