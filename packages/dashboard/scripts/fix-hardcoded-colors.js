#!/usr/bin/env node
/**
 * Automated Hardcoded Color Fixer
 * 
 * Systematically replaces hardcoded colors with CSS variables
 * across the entire codebase.
 */

import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = new URL('.', import.meta.url).pathname;

// Color mapping: hardcoded color -> CSS variable
const COLOR_MAPPINGS = {
  // Primary colors
  '#e10600': 'var(--primary-color)',
  '#dc2626': 'var(--primary-color)',
  '#b91c1c': 'var(--primary-dark)',
  '#ff1e1e': 'var(--primary-light)',

  // Secondary colors
  '#0600ef': 'var(--secondary-color)',
  '#4f46e5': 'var(--secondary-color)',
  '#6366f1': 'var(--secondary-color)',

  // Success colors
  '#22c55e': 'var(--success-color)',
  '#10b981': 'var(--success-color)',
  '#16a34a': 'var(--success-color)',
  '#4caf50': 'var(--success-color)',

  // Warning colors
  '#f59e0b': 'var(--warning-color)',
  '#ffd700': 'var(--warning-color)',
  '#ffeb3b': 'var(--warning-color)',

  // Error colors
  '#ef4444': 'var(--error-color)',
  '#f44336': 'var(--error-color)',
  '#ff1e1e': 'var(--error-color)',

  // Info colors
  '#3b82f6': 'var(--info-color)',
  '#1e5bc6': 'var(--info-color)',
  '#0099ff': 'var(--info-color)',

  // Background colors
  '#0f0f0f': 'var(--bg-primary)',
  '#111827': 'var(--bg-primary)',
  '#1a1a1a': 'var(--bg-primary)',
  '#1a1f2c': 'var(--bg-primary)',
  '#1f2937': 'var(--bg-secondary)',
  '#242936': 'var(--bg-card)',
  '#2a2a3a': 'var(--bg-card)',
  '#374151': 'var(--bg-card)',

  // Text colors
  '#9ca3af': 'var(--text-secondary)',
  '#6b7280': 'var(--text-secondary)',
  '#b0b7c3': 'var(--text-secondary)',
  '#d1d5db': 'var(--text-secondary)',

  // Border colors
  '#4b5563': 'var(--border-primary)',
  '#374151': 'var(--border-primary)',
  '#3a3f4c': 'var(--border-primary)',
};

// Files to exclude from automatic fixing
const EXCLUDED_FILES = new Set([
  'unified-theme.css',
  'theme-variables.css',
  'variables.css',
  'f1-theme.css',
  'fix-hardcoded-colors.js',
  'validate-theme-consistency.js',
]);

// Directories to exclude
const EXCLUDED_DIRS = new Set([
  'node_modules',
  'dist',
  'build',
  'dev-dist',
  '__tests__',
  '.git',
]);

/**
 * Recursively scan directory for files
 */
function scanDirectory(dir, extensions = ['.tsx', '.ts', '.css', '.js', '.jsx']) {
  const files = [];
  
  function scan(currentDir) {
    const items = readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = join(currentDir, item);
      const stat = statSync(fullPath);
      
      if (stat.isDirectory()) {
        if (!EXCLUDED_DIRS.has(item)) {
          scan(fullPath);
        }
      } else if (extensions.includes(extname(item))) {
        const fileName = item;
        if (!EXCLUDED_FILES.has(fileName)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  scan(dir);
  return files;
}

/**
 * Fix hardcoded colors in file content
 */
function fixHardcodedColors(content, filePath) {
  let fixedContent = content;
  let changeCount = 0;
  
  // Apply color mappings
  for (const [hardcodedColor, cssVariable] of Object.entries(COLOR_MAPPINGS)) {
    const regex = new RegExp(hardcodedColor.replace('#', '\\#'), 'gi');
    const matches = fixedContent.match(regex);
    
    if (matches) {
      fixedContent = fixedContent.replace(regex, cssVariable);
      changeCount += matches.length;
    }
  }
  
  return { content: fixedContent, changes: changeCount };
}

/**
 * Main fix function
 */
function fixHardcodedColorsInCodebase() {
  console.log('🔧 Fixing hardcoded colors in codebase...');
  
  const srcDir = join(__dirname, '../src');
  const files = scanDirectory(srcDir);
  
  let totalFiles = 0;
  let totalChanges = 0;
  const changedFiles = [];
  
  for (const file of files) {
    try {
      const originalContent = readFileSync(file, 'utf8');
      const { content: fixedContent, changes } = fixHardcodedColors(originalContent, file);
      
      if (changes > 0) {
        writeFileSync(file, fixedContent, 'utf8');
        changedFiles.push({ file, changes });
        totalChanges += changes;
        totalFiles++;
      }
      
    } catch (error) {
      console.warn(`⚠️  Could not process file: ${file} - ${error.message}`);
    }
  }
  
  // Report results
  console.log('\n🎨 Hardcoded Color Fix Report');
  console.log('=' .repeat(50));
  console.log(`Files processed: ${files.length}`);
  console.log(`Files changed: ${totalFiles}`);
  console.log(`Total color replacements: ${totalChanges}`);
  
  if (changedFiles.length > 0) {
    console.log('\n📝 Changed Files:');
    
    for (const { file, changes } of changedFiles.slice(0, 20)) { // Show first 20
      const relativePath = file.replace(srcDir, 'src');
      console.log(`  ${relativePath}: ${changes} replacements`);
    }
    
    if (changedFiles.length > 20) {
      console.log(`  ... and ${changedFiles.length - 20} more files`);
    }
  }
  
  if (totalChanges > 0) {
    console.log('\n✅ Hardcoded colors have been replaced with CSS variables!');
    console.log('💡 Run the validation script to check remaining issues.');
  } else {
    console.log('\n✅ No hardcoded colors found to fix.');
  }
  
  return totalChanges > 0;
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = fixHardcodedColorsInCodebase();
  process.exit(0);
}

export { fixHardcodedColorsInCodebase };
