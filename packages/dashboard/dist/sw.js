if(!self.define){let s,e={};const l=(l,r)=>(l=new URL(l+".js",r).href,e[l]||new Promise((e=>{if("document"in self){const s=document.createElement("script");s.src=l,s.onload=e,document.head.appendChild(s)}else s=l,importScripts(l),e()})).then((()=>{let s=e[l];if(!s)throw new Error(`Module ${l} didn’t register its module`);return s})));self.define=(r,i)=>{const n=s||("document"in self?document.currentScript.src:"")||location.href;if(e[n])return;let o={};const a=s=>l(s,n),c={module:{uri:n},exports:o,require:a};e[n]=Promise.all(r.map((s=>c[s]||a(s)))).then((s=>(i(...s),o)))}}define(["./workbox-cb8d25c0"],(function(s){"use strict";self.skipWaiting(),s.clientsClaim(),s.precacheAndRoute([{url:"asset-test.html",revision:"5ac8a6cccf7228246a5e2466f13d070c"},{url:"assets/Card-1e58b487.js",revision:null},{url:"assets/client-d6fc67cc.js",revision:null},{url:"assets/create-images.js",revision:null},{url:"assets/DailyGuide-ac65bbb9.js",revision:null},{url:"assets/dolAnalysis-cc48a373.js",revision:null},{url:"assets/generate-placeholder-assets.js",revision:null},{url:"assets/index.css",revision:null},{url:"assets/main-681bb6a1.js",revision:null},{url:"assets/NotFound-89e7ec7d.js",revision:null},{url:"assets/patternQuality-31c523c9.js",revision:null},{url:"assets/react-25c2faed.js",revision:null},{url:"assets/recharts-0fd68a7c.js",revision:null},{url:"assets/router-2c168ac3.js",revision:null},{url:"assets/Settings-ff0bb2e7.js",revision:null},{url:"assets/setupTransformer-489cc905.js",revision:null},{url:"assets/simple-ffedbc53.js",revision:null},{url:"assets/styled-components-00fe3932.js",revision:null},{url:"assets/TradeAnalysis-ccd21b25.js",revision:null},{url:"assets/TradeForm-3723b041.js",revision:null},{url:"assets/TradeFormBasicFields-bce7db22.js",revision:null},{url:"assets/TradeJournal-fde9d003.js",revision:null},{url:"assets/tradeStorage-a5c0ed9a.js",revision:null},{url:"assets/TradingDashboard-cac63645.js",revision:null},{url:"assets/web-vitals-60d3425a.js",revision:null},{url:"favicon.ico",revision:"51f07ed252377bd14b092c5c34cfd3c9"},{url:"favicon.svg",revision:"a6752bb0b3b15b2de9cbb1059b4411e6"},{url:"index.html",revision:"834dc6d98ec51cb18064029314aa0d5b"},{url:"logo192.png",revision:"85101af205c43ac3c1c69ffd4e0a1b75"},{url:"logo192.svg",revision:"042afe13204cb9bac32028a076568df3"},{url:"logo512.png",revision:"7c0514800de302ba3d2e0046e6aff763"},{url:"logo512.svg",revision:"15680eb9223d4853899c3529d56b4b4a"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"simple.html",revision:"3a9520819983e274401efd257ef8f8b8"},{url:"favicon.ico",revision:"51f07ed252377bd14b092c5c34cfd3c9"},{url:"logo192.png",revision:"85101af205c43ac3c1c69ffd4e0a1b75"},{url:"logo512.png",revision:"7c0514800de302ba3d2e0046e6aff763"},{url:"manifest.webmanifest",revision:"96cb2fb514b8bdcb81e81f34652af3f5"}],{}),s.cleanupOutdatedCaches(),s.registerRoute(new s.NavigationRoute(s.createHandlerBoundToURL("index.html")))}));
//# sourceMappingURL=sw.js.map
