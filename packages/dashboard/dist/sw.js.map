{"version": 3, "file": "sw.js", "sources": ["../../../../../private/var/folders/bb/rx1_5frn68gch7mbvhsvv0m00000gn/T/ab88f19794caf7537d08a835f0884956/sw.js"], "sourcesContent": ["import {clientsClaim as workbox_core_clientsClaim} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-core/clientsClaim.mjs';\nimport {precacheAndRoute as workbox_precaching_precacheAndRoute} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-precaching/precacheAndRoute.mjs';\nimport {cleanupOutdatedCaches as workbox_precaching_cleanupOutdatedCaches} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-precaching/cleanupOutdatedCaches.mjs';\nimport {registerRoute as workbox_routing_registerRoute} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-routing/registerRoute.mjs';\nimport {NavigationRoute as workbox_routing_NavigationRoute} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-routing/NavigationRoute.mjs';\nimport {createHandlerBoundToURL as workbox_precaching_createHandlerBoundToURL} from '/Users/<USER>/adhd-trading-dashboard-lib/node_modules/workbox-build/node_modules/workbox-precaching/createHandlerBoundToURL.mjs';/**\n * Welcome to your Workbox-powered service worker!\n *\n * You'll need to register this file in your web app.\n * See https://goo.gl/nhQhGp\n *\n * The rest of the code is auto-generated. Please don't update this file\n * directly; instead, make changes to your Workbox build configuration\n * and re-run your build process.\n * See https://goo.gl/2aRDsh\n */\n\n\n\n\n\n\n\n\nself.skipWaiting();\n\nworkbox_core_clientsClaim();\n\n\n/**\n * The precacheAndRoute() method efficiently caches and responds to\n * requests for URLs in the manifest.\n * See https://goo.gl/S9QRab\n */\nworkbox_precaching_precacheAndRoute([\n  {\n    \"url\": \"asset-test.html\",\n    \"revision\": \"5ac8a6cccf7228246a5e2466f13d070c\"\n  },\n  {\n    \"url\": \"assets/Card-1e58b487.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/client-d6fc67cc.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/create-images.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/DailyGuide-ac65bbb9.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/dolAnalysis-cc48a373.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/generate-placeholder-assets.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/index.css\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/main-681bb6a1.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/NotFound-89e7ec7d.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/patternQuality-31c523c9.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/react-25c2faed.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/recharts-0fd68a7c.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/router-2c168ac3.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/Settings-ff0bb2e7.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/setupTransformer-489cc905.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/simple-ffedbc53.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/styled-components-00fe3932.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/TradeAnalysis-ccd21b25.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/TradeForm-3723b041.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/TradeFormBasicFields-bce7db22.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/TradeJournal-fde9d003.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/tradeStorage-a5c0ed9a.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/TradingDashboard-cac63645.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"assets/web-vitals-60d3425a.js\",\n    \"revision\": null\n  },\n  {\n    \"url\": \"favicon.ico\",\n    \"revision\": \"51f07ed252377bd14b092c5c34cfd3c9\"\n  },\n  {\n    \"url\": \"favicon.svg\",\n    \"revision\": \"a6752bb0b3b15b2de9cbb1059b4411e6\"\n  },\n  {\n    \"url\": \"index.html\",\n    \"revision\": \"834dc6d98ec51cb18064029314aa0d5b\"\n  },\n  {\n    \"url\": \"logo192.png\",\n    \"revision\": \"85101af205c43ac3c1c69ffd4e0a1b75\"\n  },\n  {\n    \"url\": \"logo192.svg\",\n    \"revision\": \"042afe13204cb9bac32028a076568df3\"\n  },\n  {\n    \"url\": \"logo512.png\",\n    \"revision\": \"7c0514800de302ba3d2e0046e6aff763\"\n  },\n  {\n    \"url\": \"logo512.svg\",\n    \"revision\": \"15680eb9223d4853899c3529d56b4b4a\"\n  },\n  {\n    \"url\": \"registerSW.js\",\n    \"revision\": \"1872c500de691dce40960bb85481de07\"\n  },\n  {\n    \"url\": \"simple.html\",\n    \"revision\": \"3a9520819983e274401efd257ef8f8b8\"\n  },\n  {\n    \"url\": \"favicon.ico\",\n    \"revision\": \"51f07ed252377bd14b092c5c34cfd3c9\"\n  },\n  {\n    \"url\": \"logo192.png\",\n    \"revision\": \"85101af205c43ac3c1c69ffd4e0a1b75\"\n  },\n  {\n    \"url\": \"logo512.png\",\n    \"revision\": \"7c0514800de302ba3d2e0046e6aff763\"\n  },\n  {\n    \"url\": \"manifest.webmanifest\",\n    \"revision\": \"96cb2fb514b8bdcb81e81f34652af3f5\"\n  }\n], {});\nworkbox_precaching_cleanupOutdatedCaches();\nworkbox_routing_registerRoute(new workbox_routing_NavigationRoute(workbox_precaching_createHandlerBoundToURL(\"index.html\")));\n\n\n\n\n\n\n"], "names": ["self", "skipWaiting", "workbox_core_clientsClaim", "workbox_precaching_precacheAndRoute", "url", "revision", "workbox_precaching_cleanupOutdatedCaches", "workbox", "registerRoute", "workbox_routing_NavigationRoute", "NavigationRoute", "workbox_precaching_createHandlerBoundToURL"], "mappings": "0nBAwBAA,KAAKC,cAELC,EAAAA,eAQAC,EAAAA,iBAAoC,CAClC,CACEC,IAAO,kBACPC,SAAY,oCAEd,CACED,IAAO,0BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,0BACPC,SAAY,MAEd,CACED,IAAO,gCACPC,SAAY,MAEd,CACED,IAAO,iCACPC,SAAY,MAEd,CACED,IAAO,wCACPC,SAAY,MAEd,CACED,IAAO,mBACPC,SAAY,MAEd,CACED,IAAO,0BACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,oCACPC,SAAY,MAEd,CACED,IAAO,2BACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,8BACPC,SAAY,MAEd,CACED,IAAO,sCACP<PERSON>,SAAY,MAEd,CACED,IAAO,4BACPC,SAAY,MAEd,CACED,IAAO,uCACPC,SAAY,MAEd,CACED,IAAO,mCACPC,SAAY,MAEd,CACED,IAAO,+BACPC,SAAY,MAEd,CACED,IAAO,0CACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,MAEd,CACED,IAAO,kCACPC,SAAY,MAEd,CACED,IAAO,sCACPC,SAAY,MAEd,CACED,IAAO,gCACPC,SAAY,MAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,aACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,gBACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,cACPC,SAAY,oCAEd,CACED,IAAO,uBACPC,SAAY,qCAEb,CAAE,GACLC,EAAAA,wBAC6BC,EAAAC,cAAC,IAAIC,EAA+BC,gBAACC,0BAA2C"}