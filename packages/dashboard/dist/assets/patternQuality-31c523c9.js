const r={excellent:5,good:4,average:3,poor:2,unacceptable:1},n=[{value:"excellent",label:"Excellent (5)"},{value:"good",label:"Good (4)"},{value:"average",label:"Average (3)"},{value:"poor",label:"Poor (2)"},{value:"unacceptable",label:"Unacceptable (1)"}],a={clarity:{title:"Pattern Clarity",description:"How clear and well-defined is the pattern?",excellent:"<PERSON>tern is extremely clear with perfect formation",good:"<PERSON><PERSON> is clear with minor imperfections",average:"<PERSON><PERSON> is recognizable but has some ambiguity",poor:"<PERSON><PERSON> is difficult to recognize with significant ambiguity",unacceptable:"<PERSON><PERSON> is barely recognizable or completely ambiguous"},confluence:{title:"Confluence Factors",description:"How many supporting factors align with this pattern?",excellent:"Multiple strong confluence factors (5+ factors)",good:"Several good confluence factors (3-4 factors)",average:"Some confluence factors (2-3 factors)",poor:"Minimal confluence (1-2 weak factors)",unacceptable:"No confluence factors"},context:{title:"Market Context",description:"How well does the pattern fit within the broader market context?",excellent:"Perfect alignment with market structure and conditions",good:"Good alignment with market structure and conditions",average:"Reasonable alignment with some contradicting factors",poor:"Poor alignment with several contradicting factors",unacceptable:"Pattern contradicts the broader market context"},risk:{title:"Risk Profile",description:"How well-defined and manageable is the risk?",excellent:"Extremely clear stop location with minimal risk",good:"Clear stop location with reasonable risk",average:"Identifiable stop location but with moderate risk",poor:"Unclear stop location or high risk",unacceptable:"No clear stop location or extremely high risk"},reward:{title:"Reward Potential",description:"What is the potential reward relative to risk?",excellent:"Exceptional reward potential (5R+)",good:"Strong reward potential (3-5R)",average:"Reasonable reward potential (2-3R)",poor:"Limited reward potential (1-2R)",unacceptable:"Poor reward potential (<1R)"},timeframe:{title:"Timeframe Alignment",description:"How well does the pattern align across multiple timeframes?",excellent:"Strong alignment across all relevant timeframes",good:"Good alignment across most timeframes",average:"Alignment on primary timeframe with some higher/lower support",poor:"Limited alignment across timeframes",unacceptable:"Pattern only appears on a single timeframe with contradictions on others"},volume:{title:"Volume Profile",description:"How does volume support the pattern?",excellent:"Volume perfectly confirms the pattern",good:"Volume generally supports the pattern",average:"Volume is neutral or mixed",poor:"Volume somewhat contradicts the pattern",unacceptable:"Volume strongly contradicts the pattern"}},l=e=>Object.values(e).reduce((t,o)=>t+(r[o]||0),0),i=e=>{const t=Object.keys(a).length*5,o=Math.round(e/t*10);return Math.max(1,Math.min(10,o))},c=e=>e>=9?"Exceptional":e>=8?"Excellent":e>=7?"Very Good":e>=6?"Good":e>=5?"Average":e>=4?"Below Average":e>=3?"Poor":e>=2?"Very Poor":"Unacceptable",s=e=>e>=8?"var(--success-color)":e>=6?"#8BC34A":e>=5?"#FFC107":e>=3?"#FF9800":"var(--error-color)";export{a as PATTERN_QUALITY_CRITERIA,n as SCORE_RANGE_OPTIONS,r as SCORE_VALUES,l as calculateTotalScore,i as convertScoreToRating,s as getRatingColor,c as getRatingDescription};
//# sourceMappingURL=patternQuality-31c523c9.js.map
