import{j as r}from"./client-d6fc67cc.js";import{r as h,R as ee}from"./react-25c2faed.js";import{s as i}from"./styled-components-00fe3932.js";import{S as te}from"./setupTransformer-489cc905.js";import{t as re}from"./tradeStorage-a5c0ed9a.js";import{R as oe,L as ne,C as ae,X as ie,Y as se,T as ce,a as le,b as E}from"./recharts-0fd68a7c.js";import{T as de}from"./TradeFormBasicFields-bce7db22.js";const pe=()=>{const[e,t]=h.useState({trades:[],performanceMetrics:[],chartData:[],setupPerformance:[],sessionPerformance:[],isLoading:!0,error:null}),[o,n]=h.useState([]),d=a=>{const l=a.length,s=a.filter(y=>y.win).length,c=l>0?s/l*100:0,m=a.reduce((y,w)=>y+(w.pnl||0),0),u=l>0?a.reduce((y,w)=>y+(w.rMultiple||0),0)/l:0;return[{title:"Win Rate",value:`${c.toFixed(1)}%`},{title:"Total P&L",value:`$${m.toFixed(2)}`},{title:"Avg R-Multiple",value:u.toFixed(2)},{title:"Total Trades",value:l}]},x=a=>{const l=new Map;return a.forEach(s=>{var m;const c=s.setup||"Unknown";l.has(c)||l.set(c,[]),(m=l.get(c))==null||m.push(s)}),Array.from(l.entries()).map(([s,c])=>{const m=c.length,u=c.filter(v=>v.win).length,y=m>0?u/m*100:0,w=c.reduce((v,T)=>v+(T.pnl||0),0),D=m>0?c.reduce((v,T)=>v+(T.rMultiple||0),0)/m:0;return{name:s,winRate:y,avgRMultiple:D,totalTrades:m,pnl:w}}).sort((s,c)=>c.pnl-s.pnl)},g=a=>{const l=new Map;return a.forEach(s=>{var m;const c=s.session||"Unknown";l.has(c)||l.set(c,[]),(m=l.get(c))==null||m.push(s)}),Array.from(l.entries()).map(([s,c])=>{const m=c.length,u=c.filter(v=>v.win).length,y=m>0?u/m*100:0,w=c.reduce((v,T)=>v+(T.pnl||0),0),D=m>0?c.reduce((v,T)=>v+(T.rMultiple||0),0)/m:0;return{name:s,winRate:y,avgRMultiple:D,totalTrades:m,pnl:w}}).sort((s,c)=>c.pnl-s.pnl)},b=a=>{const l=new Map;a.forEach(c=>{const m=new Date(c.date).toLocaleDateString("en-US",{month:"numeric",day:"numeric"}),u=l.get(m)||0;l.set(m,u+(c.pnl||0))});let s=0;return Array.from(l.entries()).sort((c,m)=>new Date(c[0]).getTime()-new Date(m[0]).getTime()).map(([c,m])=>(s+=m,{date:c,pnl:m,cumulative:s}))},p=a=>a.map(l=>{var m,u,y;const s=l.trade;let c="No setup";return s.setupComponents?c=te.getShortDisplayString(s.setupComponents):s.setup&&(c=s.setup),{id:((m=s.id)==null?void 0:m.toString())||"0",date:s.date,model:s.model_type||"Unknown",session:s.session||"Unknown",setup:c,entry:s.entry_time||"00:00:00",exit:s.exit_time||"00:00:00",direction:s.direction,market:s.market||"MNQ",rMultiple:s.r_multiple||0,patternQuality:s.pattern_quality_rating||0,win:s.win_loss==="Win",entryPrice:s.entry_price||0,exitPrice:s.exit_price||0,risk:s.risk_points||0,pnl:s.achieved_pl||0,dolTarget:s.dol_target||"",rdType:s.rd_type||"",entryVersion:((u=l.fvg_details)==null?void 0:u.entry_version)||"",drawOnLiquidity:((y=l.fvg_details)==null?void 0:y.draw_on_liquidity)||""}}),f=h.useCallback(async()=>{t(a=>({...a,isLoading:!0,error:null}));try{const a=await re.getAllTrades();n(a);const l=p(a),s=d(l),c=x(l),m=g(l),u=b(l);t({trades:l,performanceMetrics:s,chartData:u,setupPerformance:c,sessionPerformance:m,isLoading:!1,error:null})}catch(a){console.error("Error fetching dashboard data:",a),t(l=>({...l,isLoading:!1,error:"Failed to load dashboard data"}))}},[]);return h.useEffect(()=>{f()},[f]),{...e,fetchDashboardData:f,completeTradeData:o}},me=i.div.withConfig({displayName:"HeaderContainer",componentId:"sc-c4qrnv-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"}),ge=i.div.withConfig({displayName:"F1Header",componentId:"sc-c4qrnv-1"})(["display:flex;align-items:center;justify-content:space-between;padding:",";background:linear-gradient( 135deg,"," 0%,rgba(75,85,99,0.1) 100% );border:1px solid ",";border-radius:",";position:relative;overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,"," 0%,transparent 100% );}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),fe=i.h1.withConfig({displayName:"F1Title",componentId:"sc-c4qrnv-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:2px;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;span{color:",";font-weight:800;}"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.h2)||"1.5rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),xe=i.div.withConfig({displayName:"LiveIndicator",componentId:"sc-c4qrnv-3"})(["display:flex;align-items:center;gap:",";color:",";font-weight:700;text-transform:uppercase;letter-spacing:1px;font-size:",";padding:"," ",";border-radius:",";border:1px solid ",";background:",";&::before{content:'●';animation:",";font-size:12px;}@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$isLive:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.primary)||"var(--primary-color)":((n=t.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.full)||"9999px"},({$isLive:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.primary)||"var(--primary-color)":((n=t.colors)==null?void 0:n.border)||"var(--border-primary)"},({$isLive:e,theme:t})=>{var o;return e?`${((o=t.colors)==null?void 0:o.primary)||"var(--primary-color)"}20`:"transparent"},({$isLive:e})=>e?"pulse 2s infinite":"none"),he=i.div.withConfig({displayName:"SubHeader",componentId:"sc-c4qrnv-4"})(["display:flex;justify-content:space-between;align-items:center;padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),ue=i.div.withConfig({displayName:"TitleSection",componentId:"sc-c4qrnv-5"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),ye=i.h2.withConfig({displayName:"SubTitle",componentId:"sc-c4qrnv-6"})(["font-size:",";margin:0;color:",";font-weight:600;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xxl)||"1.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),be=i.span.withConfig({displayName:"StatusBadge",componentId:"sc-c4qrnv-7"})(["background:",";color:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;border:1px solid ",";text-transform:uppercase;letter-spacing:0.5px;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xxs)||"2px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.full)||"9999px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),we=i.button.withConfig({displayName:"RefreshButton",componentId:"sc-c4qrnv-8"})(["background:",";color:",";border:none;border-radius:",";padding:"," ",";font-size:",";font-weight:600;cursor:",";display:flex;align-items:center;gap:",";transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;min-width:120px;justify-content:center;&:hover:not(:disabled){background:",";transform:translateY(-1px);box-shadow:0 4px 8px ","40;}&:active:not(:disabled){transform:translateY(0);}&:disabled{opacity:0.6;}"],({$isLoading:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.border)||"var(--border-primary)":((n=t.colors)==null?void 0:n.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textInverse)||"#ffffff"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({$isLoading:e})=>e?"not-allowed":"pointer",({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primaryDark)||"var(--primary-dark)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),ve=i.span.withConfig({displayName:"RefreshIcon",componentId:"sc-c4qrnv-9"})(["display:inline-block;animation:",";@keyframes spin{from{transform:rotate(0deg);}to{transform:rotate(360deg);}}"],({$isLoading:e})=>e?"spin 1s linear infinite":"none"),je=({className:e,isLoading:t=!1,isRefreshing:o=!1,sessionNumber:n=1,isLiveSession:d=!0,onRefresh:x,title:g="Trading Dashboard"})=>r.jsxs(me,{className:e,children:[r.jsxs(ge,{children:[r.jsxs(fe,{children:["🏎️ TRADING ",r.jsx("span",{children:"2025"})," DASHBOARD"]}),r.jsx(xe,{$isLive:d,children:d?"LIVE SESSION":"OFFLINE"})]}),r.jsxs(he,{children:[r.jsxs(ue,{children:[r.jsx(ye,{children:g}),r.jsxs(be,{$sessionNumber:n,children:["SESSION ",n]})]}),x&&r.jsxs(we,{onClick:x,disabled:t,$isLoading:t,title:t?"Refreshing data...":"Refresh dashboard data",children:[r.jsx(ve,{$isLoading:t||o,children:t||o?"⏳":"🔄"}),t?"Refreshing...":"Refresh Data"]})]})]}),Ce=i.div.withConfig({displayName:"TabsContainer",componentId:"sc-nh080e-0"})(["display:flex;gap:0;margin:"," 0 "," 0;border-bottom:1px solid ",";position:relative;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),Te=i.button.withConfig({displayName:"Tab",componentId:"sc-nh080e-1"})(["padding:"," ",";border:none;background:transparent;color:",";cursor:",";transition:all 0.2s ease;font-weight:",";font-size:",";position:relative;border-bottom:2px solid transparent;text-transform:uppercase;letter-spacing:0.025em;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;&::after{content:'';position:absolute;bottom:-1px;left:0;right:0;height:2px;background:",";transform:scaleX(",");transition:transform 0.2s ease;transform-origin:center;}&:hover:not(:disabled){color:",";transform:translateY(-1px);&::after{transform:scaleX(1);background:",";}}&:active:not(:disabled){transform:translateY(0);}"," @media (max-width:768px){padding:"," ",";font-size:",";}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({$isActive:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.textPrimary)||"#ffffff":((n=t.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$disabled:e})=>e?"not-allowed":"pointer",({$isActive:e})=>e?"600":"400",({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.md)||"1rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({$isActive:e})=>e?1:0,({$isActive:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.textPrimary)||"#ffffff":((n=t.colors)==null?void 0:n.textPrimary)||"#ffffff"},({$isActive:e,theme:t})=>{var o,n;return e?((o=t.colors)==null?void 0:o.primary)||"var(--primary-color)":((n=t.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$disabled:e})=>e&&`
    opacity: 0.5;
    cursor: not-allowed;
  `,({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"}),ke=i.span.withConfig({displayName:"TabIcon",componentId:"sc-nh080e-2"})(["margin-right:",";font-size:16px;@media (max-width:768px){margin-right:0;font-size:14px;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),Se=i.span.withConfig({displayName:"TabLabel",componentId:"sc-nh080e-3"})(["@media (max-width:768px){display:none;}"]),M={summary:{icon:"📊",label:"Summary",description:"Performance overview and key metrics"},trades:{icon:"📋",label:"Trades",description:"Recent trades and transaction history"},setups:{icon:"🎯",label:"Setups",description:"Setup analysis and performance breakdown"},analytics:{icon:"📈",label:"Analytics",description:"Advanced analytics and quick trade entry"}},Ie=({activeTab:e,onTabChange:t,disabled:o=!1,className:n})=>{const d=g=>{o||t(g)},x=(g,b)=>{(g.key==="Enter"||g.key===" ")&&!o&&(g.preventDefault(),t(b))};return r.jsx(Ce,{className:n,role:"tablist",children:Object.keys(M).map(g=>{const b=M[g],p=e===g;return r.jsxs(Te,{$isActive:p,$disabled:o,onClick:()=>d(g),onKeyDown:f=>x(f,g),disabled:o,role:"tab","aria-selected":p,"aria-controls":`dashboard-panel-${g}`,tabIndex:o?-1:0,title:b.description,children:[r.jsx(ke,{children:b.icon}),r.jsx(Se,{children:b.label})]},g)})})},j=["summary","trades","setups","analytics"],Ne="adhd-trading-dashboard:dashboard:active-tab",De=(e,t)=>{try{const o=localStorage.getItem(e);if(o&&j.includes(o))return o}catch(o){console.warn("Failed to load dashboard tab from localStorage:",o)}return t},ze=(e,t)=>{try{localStorage.setItem(e,t)}catch(o){console.warn("Failed to save dashboard tab to localStorage:",o)}},Re=({defaultTab:e="summary",storageKey:t=Ne}={})=>{const[o,n]=h.useState(()=>De(t,e)),d=h.useCallback(f=>{j.includes(f)&&(n(f),ze(t,f))},[t]),x=h.useCallback(()=>{const a=(j.indexOf(o)+1)%j.length;d(j[a])},[o,d]),g=h.useCallback(()=>{const f=j.indexOf(o),a=f===0?j.length-1:f-1;d(j[a])},[o,d]),b=h.useCallback(f=>o===f,[o]),p=h.useCallback(f=>j.indexOf(f),[]);return h.useEffect(()=>{const f=a=>{var l,s,c;if(!(((l=document.activeElement)==null?void 0:l.tagName)==="INPUT"||((s=document.activeElement)==null?void 0:s.tagName)==="TEXTAREA"||((c=document.activeElement)==null?void 0:c.tagName)==="SELECT")){if((a.ctrlKey||a.metaKey)&&!a.shiftKey)switch(a.key){case"ArrowLeft":a.preventDefault(),g();break;case"ArrowRight":a.preventDefault(),x();break}if(a.key>="1"&&a.key<="4"&&!a.ctrlKey&&!a.metaKey){const m=parseInt(a.key)-1;m<j.length&&(a.preventDefault(),d(j[m]))}if(a.altKey&&!a.ctrlKey&&!a.metaKey)switch(a.key.toLowerCase()){case"s":a.preventDefault(),d("summary");break;case"t":a.preventDefault(),d("trades");break;case"u":a.preventDefault(),d("setups");break;case"a":a.preventDefault(),d("analytics");break}}};return window.addEventListener("keydown",f),()=>window.removeEventListener("keydown",f)},[x,g,d]),{activeTab:o,setActiveTab:d,nextTab:x,previousTab:g,isTabActive:b,getTabIndex:p,availableTabs:j}},$=i.div.withConfig({displayName:"Container",componentId:"sc-1dnben6-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:",";margin-bottom:",";"],({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.xl),_=i.div.withConfig({displayName:"MetricCard",componentId:"sc-1dnben6-1"})(["background:linear-gradient( 135deg,"," 0%,rgba(75,85,99,0.05) 100% );border:1px solid var(--border-primary);border-radius:",";padding:",";box-shadow:0 8px 32px rgba(0,0,0,0.3);display:flex;flex-direction:column;justify-content:space-between;min-height:140px;position:relative;overflow:hidden;transition:all ",";&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,var(--border-primary) 0%,transparent 100%);}&::after{content:'';position:absolute;top:10px;right:10px;width:40px;height:40px;background:radial-gradient(circle,rgba(75,85,99,0.1) 0%,transparent 70%);border-radius:50%;}&:hover{transform:translateY(-2px);box-shadow:0 12px 40px rgba(75,85,99,0.2);border-color:var(--text-secondary);}"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.lg,({theme:e})=>e.spacing.lg,({theme:e})=>e.transitions.normal),q=i.h3.withConfig({displayName:"MetricTitle",componentId:"sc-1dnben6-2"})(["font-size:",";color:",";margin:0 0 "," 0;text-transform:uppercase;letter-spacing:1.5px;font-weight:bold;position:relative;z-index:2;"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.sm),Ae=i.div.withConfig({displayName:"MetricValue",componentId:"sc-1dnben6-3"})(["font-size:",";font-weight:bold;color:",";font-family:'Orbitron','Inter',monospace;position:relative;z-index:2;text-shadow:0 2px 4px rgba(0,0,0,0.3);"],({theme:e})=>e.fontSizes.h3,({theme:e})=>e.colors.textPrimary),Pe=i.div.withConfig({displayName:"MetricChange",componentId:"sc-1dnben6-4"})(["font-size:",";color:",";display:flex;align-items:center;margin-top:",";font-weight:bold;text-transform:uppercase;letter-spacing:1px;position:relative;z-index:2;&::before{content:",";margin-right:",";font-size:0.8em;}"],({theme:e})=>e.fontSizes.sm,({theme:e,isPositive:t})=>t?e.colors.success:e.colors.error,({theme:e})=>e.spacing.sm,({isPositive:e})=>e?"'▲'":"'▼'",({theme:e})=>e.spacing.xs),Le=i.div.withConfig({displayName:"LoadingIndicator",componentId:"sc-1dnben6-5"})(["color:var(--text-secondary);font-weight:bold;font-size:",";text-transform:uppercase;letter-spacing:1px;position:relative;z-index:2;&::after{content:'...';animation:loading 1.5s infinite;}@keyframes loading{0%,33%{content:'.';}34%,66%{content:'..';}67%,100%{content:'...';}}"],({theme:e})=>e.fontSizes.sm),U=({metrics:e,isLoading:t=!1})=>t?r.jsx($,{children:[1,2,3,4].map(o=>r.jsxs(_,{children:[r.jsx(q,{children:"LOADING DATA"}),r.jsx(Le,{children:"FETCHING TELEMETRY"})]},o))}):r.jsx($,{children:e.map((o,n)=>r.jsxs(_,{children:[r.jsx(q,{children:o.title}),r.jsx(Ae,{children:o.value}),o.change!==void 0&&r.jsxs(Pe,{isPositive:o.isPositive,children:[Math.abs(o.change),"% ",o.isPositive?"GAIN":"LOSS"]})]},n))}),A=i.div.withConfig({displayName:"ChartContainer",componentId:"sc-l5zznk-0"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";height:300px;margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md,({theme:e})=>e.shadows.sm,({theme:e})=>e.spacing.lg),P=i.h3.withConfig({displayName:"ChartTitle",componentId:"sc-l5zznk-1"})(["font-size:",";color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md),Fe=i.div.withConfig({displayName:"LoadingContainer",componentId:"sc-l5zznk-2"})(["display:flex;align-items:center;justify-content:center;height:100%;color:",";"],({theme:e})=>e.colors.textSecondary),Ee=i.div.withConfig({displayName:"NoDataContainer",componentId:"sc-l5zznk-3"})(["display:flex;align-items:center;justify-content:center;height:100%;color:",";"],({theme:e})=>e.colors.textSecondary),Me=({active:e,payload:t,label:o})=>e&&t&&t.length?r.jsxs("div",{style:{backgroundColor:"#252a37",padding:"10px",border:"1px solid #333",borderRadius:"4px"},children:[r.jsx("p",{style:{margin:0},children:`Date: ${o}`}),r.jsx("p",{style:{margin:0,color:"var(--primary-color)"},children:`Daily P&L: $${t[0].value.toFixed(2)}`}),r.jsx("p",{style:{margin:0,color:"var(--info-color)"},children:`Cumulative P&L: $${t[1].value.toFixed(2)}`})]}):null,Q=({data:e,isLoading:t=!1})=>t?r.jsxs(A,{children:[r.jsx(P,{children:"Performance"}),r.jsx(Fe,{children:"Loading chart data..."})]}):!e||e.length===0?r.jsxs(A,{children:[r.jsx(P,{children:"Performance"}),r.jsx(Ee,{children:"No performance data available"})]}):r.jsxs(A,{children:[r.jsx(P,{children:"Performance"}),r.jsx(oe,{width:"100%",height:"90%",children:r.jsxs(ne,{data:e,margin:{top:5,right:30,left:20,bottom:5},children:[r.jsx(ae,{strokeDasharray:"3 3",stroke:"rgba(255, 255, 255, 0.1)"}),r.jsx(ie,{dataKey:"date",stroke:"#aaaaaa",tick:{fill:"#aaaaaa"}}),r.jsx(se,{stroke:"#aaaaaa",tick:{fill:"#aaaaaa"},tickFormatter:o=>`$${o}`}),r.jsx(ce,{content:r.jsx(Me,{})}),r.jsx(le,{}),r.jsx(E,{type:"monotone",dataKey:"pnl",name:"Daily P&L",stroke:"var(--primary-color)",activeDot:{r:8},strokeWidth:2}),r.jsx(E,{type:"monotone",dataKey:"cumulative",name:"Cumulative P&L",stroke:"var(--info-color)",strokeWidth:2})]})})]}),L=i.div.withConfig({displayName:"TableContainer",componentId:"sc-1slwy9l-0"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";overflow-x:auto;margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md,({theme:e})=>e.shadows.sm,({theme:e})=>e.spacing.lg),F=i.h3.withConfig({displayName:"TableTitle",componentId:"sc-1slwy9l-1"})(["font-size:",";color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md),$e=i.table.withConfig({displayName:"Table",componentId:"sc-1slwy9l-2"})(["width:100%;border-collapse:collapse;min-width:800px;"]),_e=i.thead.withConfig({displayName:"TableHead",componentId:"sc-1slwy9l-3"})(["border-bottom:1px solid ",";"],({theme:e})=>e.colors.border),k=i.th.withConfig({displayName:"TableHeader",componentId:"sc-1slwy9l-4"})(["text-align:left;padding:",";color:",";font-size:",";font-weight:500;text-transform:uppercase;cursor:",";user-select:none;position:relative;"," ",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm,({sortable:e})=>e?"pointer":"default",({sortable:e,theme:t})=>e&&`
    &:hover {
      color: ${t.colors.textPrimary};
      background-color: rgba(255, 255, 255, 0.05);
    }
  `,({active:e,theme:t})=>e&&`
    color: ${t.colors.primary};
    font-weight: 600;
  `),S=i.span.withConfig({displayName:"SortIcon",componentId:"sc-1slwy9l-5"})(["margin-left:4px;font-size:12px;opacity:0.7;"," &::after{","}"],({direction:e})=>e==="asc"?'content: "↑";':e==="desc"?'content: "↓";':'content: "↕"; opacity: 0.3;',({direction:e})=>e==="asc"?'content: "↑";':e==="desc"?'content: "↓";':'content: "↕";'),qe=i.tr.withConfig({displayName:"TableRow",componentId:"sc-1slwy9l-6"})(["border-bottom:1px solid var(--border-primary);&:hover{background-color:rgba(255,255,255,0.05);}"]),I=i.td.withConfig({displayName:"TableCell",componentId:"sc-1slwy9l-7"})(["padding:",";color:",";font-size:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.sm),Oe=i(I).withConfig({displayName:"DirectionCell",componentId:"sc-1slwy9l-8"})(["color:",";"],({theme:e,direction:t})=>t==="Long"?e.colors.success:e.colors.error),He=i(I).withConfig({displayName:"ResultCell",componentId:"sc-1slwy9l-9"})(["color:",";"],({theme:e,win:t})=>t?e.colors.success:e.colors.error),Be=i(I).withConfig({displayName:"PnlCell",componentId:"sc-1slwy9l-10"})(["color:",";"],({theme:e,value:t})=>t>=0?e.colors.success:e.colors.error),Ke=i.div.withConfig({displayName:"LoadingContainer",componentId:"sc-1slwy9l-11"})(["display:flex;align-items:center;justify-content:center;height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),Ve=i.div.withConfig({displayName:"NoDataContainer",componentId:"sc-1slwy9l-12"})(["display:flex;align-items:center;justify-content:center;height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),Ye=(e,t,o)=>{console.group("🔍 Recent Trades Sorting Debug"),console.log("📊 Raw trades count:",e.length),console.log("📊 Sort criteria:",{sortBy:t,sortOrder:o}),console.log("📋 Sample raw trades (first 3):"),e.slice(0,3).forEach((n,d)=>{if(!n||!n.trade){console.log(`Trade ${d+1}: INVALID - trade or trade.trade is null/undefined`);return}console.log(`Trade ${d+1}:`,{id:n.trade.id,market:n.trade.market,date:n.trade.date,direction:n.trade.direction,entry:n.trade.entry_price,exit:n.trade.exit_price,[t]:n.trade[t]})}),console.groupEnd()},X=({trades:e,isLoading:t=!1})=>{console.log("🔥 ENHANCED RecentTradesTable component is being executed!");const[o,n]=h.useState("date"),[d,x]=h.useState("desc");ee.useEffect(()=>{if(console.log("🚀 RecentTradesTable component mounted/updated"),console.log("📊 Props received:",{tradesCount:(e==null?void 0:e.length)||0,isLoading:t,tradesType:typeof e,tradesIsArray:Array.isArray(e),tradesValue:e}),e&&e.length>0){const p=e[0],f=e[e.length-1];p&&p.trade?console.log("🎯 RecentTradesTable received trades:",{count:e.length,firstTrade:{id:p.trade.id,date:p.trade.date,market:p.trade.market,setup:p.trade.setup,session:p.trade.session},lastTrade:f&&f.trade?{id:f.trade.id,date:f.trade.date,market:f.trade.market}:"Invalid last trade"}):(console.log("⚠️ RecentTradesTable: Trades array exists but contains invalid data"),console.log("🔍 First trade:",p))}else console.log("⚠️ RecentTradesTable: No trades data or empty array"),console.log("🔍 Trades value:",e),console.log("🔍 Is loading:",t)},[e,t]);const g=h.useCallback(p=>{if(console.log(`🔄 Sorting by ${p}`),o===p){const f=d==="asc"?"desc":"asc";x(f),console.log(`🔄 Toggled direction to ${f}`)}else n(p),x("desc"),console.log(`🔄 New field ${p} with desc direction`)},[o,d]),b=h.useMemo(()=>{if(!e||e.length===0)return[];const p=e.filter(a=>a&&a.trade);if(p.length===0)return console.warn("⚠️ No valid trades found after filtering"),[];Ye(p,o,d);const f=[...p].sort((a,l)=>{var u,y;if(!a||!a.trade||!l||!l.trade)return console.warn("⚠️ Invalid trade data in sort function"),0;let s,c;switch(o){case"date":s=new Date(a.trade.date||""),c=new Date(l.trade.date||"");break;case"setup":s=a.trade.setup||"",c=l.trade.setup||"";break;case"session":s=a.trade.session||"",c=l.trade.session||"";break;case"direction":s=((u=a.trade.direction)==null?void 0:u.toLowerCase())||"",c=((y=l.trade.direction)==null?void 0:y.toLowerCase())||"";break;case"market":s=a.trade.market||"",c=l.trade.market||"";break;case"entry":s=parseFloat(String(a.trade.entry_price||0)),c=parseFloat(String(l.trade.entry_price||0));break;case"exit":s=parseFloat(String(a.trade.exit_price||0)),c=parseFloat(String(l.trade.exit_price||0));break;case"rMultiple":s=parseFloat(String(a.trade.r_multiple||0)),c=parseFloat(String(l.trade.r_multiple||0));break;case"pnl":s=parseFloat(String(a.trade.achieved_pl||0)),c=parseFloat(String(l.trade.achieved_pl||0));break;default:s=a.trade[o],c=l.trade[o]}if(console.log(`🔄 Comparing: ${s} vs ${c} (${o})`),s instanceof Date&&c instanceof Date){const w=s.getTime()-c.getTime();return d==="asc"?w:-w}if(typeof s=="number"&&typeof c=="number"){const w=s-c;return d==="asc"?w:-w}const m=String(s).localeCompare(String(c));return d==="asc"?m:-m});return console.log("✅ Sorted trades (first 3):"),f.slice(0,3).forEach((a,l)=>{a&&a.trade?console.log(`Sorted ${l+1}:`,{id:a.trade.id,date:a.trade.date,[o]:a.trade[o]}):console.log(`Sorted ${l+1}: INVALID TRADE`)}),f},[e,o,d]);return t?r.jsxs(L,{children:[r.jsx(F,{children:"Recent Trades"}),r.jsx(Ke,{children:"Loading trades data..."})]}):!e||e.length===0?r.jsxs(L,{children:[r.jsx(F,{children:"Recent Trades"}),r.jsx(Ve,{children:"No trades data available"})]}):r.jsxs(L,{children:[r.jsxs(F,{children:["Recent Trades (",b.length,")"]}),r.jsxs($e,{children:[r.jsx(_e,{children:r.jsxs("tr",{children:[r.jsxs(k,{sortable:!0,active:o==="date",onClick:()=>g("date"),children:["Date",r.jsx(S,{direction:o==="date"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="setup",onClick:()=>g("setup"),children:["Setup",r.jsx(S,{direction:o==="setup"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="session",onClick:()=>g("session"),children:["Session",r.jsx(S,{direction:o==="session"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="direction",onClick:()=>g("direction"),children:["Direction",r.jsx(S,{direction:o==="direction"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="market",onClick:()=>g("market"),children:["Market",r.jsx(S,{direction:o==="market"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="entry",onClick:()=>g("entry"),children:["Entry",r.jsx(S,{direction:o==="entry"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="exit",onClick:()=>g("exit"),children:["Exit",r.jsx(S,{direction:o==="exit"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="rMultiple",onClick:()=>g("rMultiple"),children:["R-Multiple",r.jsx(S,{direction:o==="rMultiple"?d:void 0})]}),r.jsxs(k,{sortable:!0,active:o==="pnl",onClick:()=>g("pnl"),children:["P&L",r.jsx(S,{direction:o==="pnl"?d:void 0})]})]})}),r.jsx("tbody",{children:b.map((p,f)=>!p||!p.trade?(console.warn(`⚠️ Skipping invalid trade at index ${f}`),null):r.jsxs(qe,{children:[r.jsx(I,{children:p.trade.date||"N/A"}),r.jsx(I,{children:p.trade.setup||"N/A"}),r.jsx(I,{children:p.trade.session||"N/A"}),r.jsx(Oe,{direction:p.trade.direction,children:p.trade.direction||"N/A"}),r.jsx(I,{children:p.trade.market||"N/A"}),r.jsx(I,{children:p.trade.entry_price||"N/A"}),r.jsx(I,{children:p.trade.exit_price||"N/A"}),r.jsx(He,{win:p.trade.win_loss==="Win",children:(p.trade.r_multiple||0).toFixed(2)}),r.jsxs(Be,{value:p.trade.achieved_pl||0,children:["$",(p.trade.achieved_pl||0).toFixed(2)]})]},p.trade.id||`trade-${f}`))})]})]})},O=i.div.withConfig({displayName:"Container",componentId:"sc-1mmg3za-0"})(["display:grid;grid-template-columns:1fr 1fr;gap:",";margin-bottom:",";@media (max-width:","){grid-template-columns:1fr;}"],({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg,({theme:e})=>e.breakpoints.md),z=i.div.withConfig({displayName:"AnalysisCard",componentId:"sc-1mmg3za-1"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md,({theme:e})=>e.shadows.sm),R=i.h3.withConfig({displayName:"CardTitle",componentId:"sc-1mmg3za-2"})(["font-size:",";color:",";margin:0 0 "," 0;"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.md),H=i.table.withConfig({displayName:"Table",componentId:"sc-1mmg3za-3"})(["width:100%;border-collapse:collapse;"]),B=i.thead.withConfig({displayName:"TableHead",componentId:"sc-1mmg3za-4"})(["border-bottom:1px solid ",";"],({theme:e})=>e.colors.border),C=i.th.withConfig({displayName:"TableHeader",componentId:"sc-1mmg3za-5"})(["text-align:left;padding:",";color:",";font-size:",";font-weight:500;text-transform:uppercase;"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),K=i.tr.withConfig({displayName:"TableRow",componentId:"sc-1mmg3za-6"})(["border-bottom:1px solid ",";&:hover{background-color:rgba(255,255,255,0.05);}"],({theme:e})=>e.colors.border),N=i.td.withConfig({displayName:"TableCell",componentId:"sc-1mmg3za-7"})(["padding:",";color:",";font-size:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.sm),V=i(N).withConfig({displayName:"WinRateCell",componentId:"sc-1mmg3za-8"})(["color:",";"],({theme:e,value:t})=>t>=70?e.colors.success:t>=50?e.colors.warning:e.colors.error),Y=i(N).withConfig({displayName:"PnlCell",componentId:"sc-1mmg3za-9"})(["color:",";"],({theme:e,value:t})=>t>=0?e.colors.success:e.colors.error),W=i.div.withConfig({displayName:"LoadingContainer",componentId:"sc-1mmg3za-10"})(["display:flex;align-items:center;justify-content:center;height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),G=i.div.withConfig({displayName:"NoDataContainer",componentId:"sc-1mmg3za-11"})(["display:flex;align-items:center;justify-content:center;height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),J=({setupPerformance:e,sessionPerformance:t,isLoading:o=!1})=>o?r.jsxs(O,{children:[r.jsxs(z,{children:[r.jsx(R,{children:"Setup Performance"}),r.jsx(W,{children:"Loading setup data..."})]}),r.jsxs(z,{children:[r.jsx(R,{children:"Session Performance"}),r.jsx(W,{children:"Loading session data..."})]})]}):r.jsxs(O,{children:[r.jsxs(z,{children:[r.jsx(R,{children:"Setup Performance"}),e.length===0?r.jsx(G,{children:"No setup data available"}):r.jsxs(H,{children:[r.jsx(B,{children:r.jsxs("tr",{children:[r.jsx(C,{children:"Setup"}),r.jsx(C,{children:"Win Rate"}),r.jsx(C,{children:"Avg R"}),r.jsx(C,{children:"Trades"}),r.jsx(C,{children:"P&L"})]})}),r.jsx("tbody",{children:e.map((n,d)=>r.jsxs(K,{children:[r.jsx(N,{children:n.name}),r.jsxs(V,{value:n.winRate,children:[n.winRate.toFixed(1),"%"]}),r.jsx(N,{children:n.avgRMultiple.toFixed(2)}),r.jsx(N,{children:n.totalTrades}),r.jsxs(Y,{value:n.pnl,children:["$",n.pnl.toFixed(2)]})]},d))})]})]}),r.jsxs(z,{children:[r.jsx(R,{children:"Session Performance"}),t.length===0?r.jsx(G,{children:"No session data available"}):r.jsxs(H,{children:[r.jsx(B,{children:r.jsxs("tr",{children:[r.jsx(C,{children:"Session"}),r.jsx(C,{children:"Win Rate"}),r.jsx(C,{children:"Avg R"}),r.jsx(C,{children:"Trades"}),r.jsx(C,{children:"P&L"})]})}),r.jsx("tbody",{children:t.map((n,d)=>r.jsxs(K,{children:[r.jsx(N,{children:n.name}),r.jsxs(V,{value:n.winRate,children:[n.winRate.toFixed(1),"%"]}),r.jsx(N,{children:n.avgRMultiple.toFixed(2)}),r.jsx(N,{children:n.totalTrades}),r.jsxs(Y,{value:n.pnl,children:["$",n.pnl.toFixed(2)]})]},d))})]})]})]}),We=i.div.withConfig({displayName:"AnalyticsContainer",componentId:"sc-k2i7co-0"})(["display:grid;grid-template-columns:1fr 400px;gap:",";@media (max-width:","){grid-template-columns:1fr;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"},({theme:e})=>{var t;return((t=e.breakpoints)==null?void 0:t.lg)||"1024px"}),Ge=i.div.withConfig({displayName:"ChartsSection",componentId:"sc-k2i7co-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),Ue=i.div.withConfig({displayName:"TradeFormSection",componentId:"sc-k2i7co-2"})(["background:",";border-radius:",";padding:",";border:1px solid ",";height:fit-content;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),Qe=i.h3.withConfig({displayName:"FormTitle",componentId:"sc-k2i7co-3"})(["margin:0 0 "," 0;color:",";font-size:",";font-weight:700;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"}),Xe=({data:e,isLoading:t})=>{var n,d;const o=((d=(n=e.completeTradeData)==null?void 0:n.sort((x,g)=>new Date(g.trade.date).getTime()-new Date(x.trade.date).getTime()))==null?void 0:d.slice(0,5))||[];return r.jsxs(r.Fragment,{children:[r.jsx(U,{metrics:e.performanceMetrics,isLoading:t}),r.jsx(Q,{data:e.chartData,isLoading:t}),r.jsx(X,{trades:o,isLoading:t})]})},Je=({data:e,isLoading:t})=>{var n;const o=((n=e.completeTradeData)==null?void 0:n.sort((d,x)=>new Date(x.trade.date).getTime()-new Date(d.trade.date).getTime()))||[];return r.jsx(X,{trades:o,isLoading:t})},Ze=({data:e,isLoading:t})=>r.jsx(J,{setupPerformance:e.setupPerformance,sessionPerformance:e.sessionPerformance,isLoading:t}),et=({data:e,isLoading:t,tradeFormValues:o,handleTradeFormChange:n})=>r.jsxs(We,{children:[r.jsxs(Ge,{children:[r.jsx(U,{metrics:e.performanceMetrics,isLoading:t}),r.jsx(Q,{data:e.chartData,isLoading:t}),r.jsx(J,{setupPerformance:e.setupPerformance,sessionPerformance:e.sessionPerformance,isLoading:t})]}),r.jsxs(Ue,{children:[r.jsx(Qe,{children:"🏎️ Quick Trade Entry"}),o&&n&&r.jsx(de,{formValues:o,handleChange:d=>n(d),validationErrors:{}})]})]}),tt={summary:{id:"summary",title:"Performance Summary",description:"Overview of trading performance and key metrics",icon:"📊",component:Xe,showInMobile:!0,requiresData:!0},trades:{id:"trades",title:"Recent Trades",description:"Complete list of recent trading activity",icon:"📋",component:Je,showInMobile:!0,requiresData:!0},setups:{id:"setups",title:"Setup Analysis",description:"Performance breakdown by trading setups and sessions",icon:"🎯",component:Ze,showInMobile:!0,requiresData:!0},analytics:{id:"analytics",title:"Advanced Analytics",description:"Comprehensive analytics with quick trade entry",icon:"📈",component:et,showInMobile:!1,requiresData:!0}},rt=e=>tt[e],ot=e=>{const{activeTab:t}=e,o=rt(t);if(!o)return r.jsxs("div",{style:{padding:"48px",textAlign:"center",color:"var(--error-color)"},children:["❌ Unknown tab: ",t]});const n=o.component;return r.jsx("div",{id:`dashboard-panel-${t}`,role:"tabpanel","aria-labelledby":`dashboard-tab-${t}`,children:r.jsx(n,{...e})})},nt=i.div.withConfig({displayName:"Container",componentId:"sc-8aj5pc-0"})(["display:flex;flex-direction:column;gap:",";background:",";color:",";min-height:100vh;padding:",";max-width:1400px;margin:0 auto;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"var(--bg-primary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),at=i.div.withConfig({displayName:"ContentArea",componentId:"sc-8aj5pc-1"})(["display:flex;flex-direction:column;gap:",";flex:1;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),it=i.div.withConfig({displayName:"TabContentContainer",componentId:"sc-8aj5pc-2"})(["animation:fadeIn 0.3s ease-in-out;@keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}"]),st=i.div.withConfig({displayName:"LoadingState",componentId:"sc-8aj5pc-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"48px"}),ct=i.div.withConfig({displayName:"LoadingIcon",componentId:"sc-8aj5pc-4"})(["font-size:48px;margin-bottom:",";opacity:0.7;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:0.7;}50%{opacity:0.3;}}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),lt=i.p.withConfig({displayName:"LoadingText",componentId:"sc-8aj5pc-5"})(["font-size:",";color:",";margin:0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),dt=i.div.withConfig({displayName:"ErrorState",componentId:"sc-8aj5pc-6"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;background:","10;border:1px solid ","40;border-radius:",";margin:"," 0;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"48px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),pt=i.div.withConfig({displayName:"ErrorIcon",componentId:"sc-8aj5pc-7"})(["font-size:48px;margin-bottom:",";color:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"}),mt=i.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-8aj5pc-8"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),gt=i.p.withConfig({displayName:"ErrorMessage",componentId:"sc-8aj5pc-9"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),ft=i.button.withConfig({displayName:"RetryButton",componentId:"sc-8aj5pc-10"})(["margin-top:",";padding:"," ",";background:",";color:white;border:none;border-radius:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;&:hover{background:",";transform:translateY(-1px);}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primaryDark)||"var(--primary-dark)"}),xt=i.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-8aj5pc-11"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";"],({theme:e})=>{var t;return((t=e.zIndex)==null?void 0:t.modal)||1e3}),ht=i.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-8aj5pc-12"})(["border:4px solid rgba(255,255,255,0.3);border-top:4px solid ",";border-radius:50%;width:40px;height:40px;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),Z=()=>r.jsxs(st,{children:[r.jsx(ct,{children:"🏎️"}),r.jsx(lt,{children:"Loading Trading Dashboard..."})]}),ut=({error:e,onRetry:t})=>r.jsxs(dt,{children:[r.jsx(pt,{children:"⚠️"}),r.jsx(mt,{children:"Dashboard Error"}),r.jsx(gt,{children:e}),r.jsx(ft,{onClick:t,children:"Try Again"})]}),yt=({initialTab:e})=>{const{trades:t,performanceMetrics:o,chartData:n,setupPerformance:d,sessionPerformance:x,isLoading:g,error:b,fetchDashboardData:p,completeTradeData:f}=pe(),{activeTab:a,setActiveTab:l}=Re({defaultTab:e||"summary"}),[s,c]=h.useState({date:new Date().toISOString().split("T")[0],symbol:"MNQ",direction:"long",quantity:"1",entryPrice:"0",exitPrice:"0",profit:"0",model:"",session:"",setup:"",patternQuality:"",dolTarget:"",rdType:"",drawOnLiquidity:"",entryVersion:"",notes:"",tags:[],result:"win"}),u={activeTab:a,data:{trades:t,performanceMetrics:o,chartData:n,setupPerformance:d,sessionPerformance:x,completeTradeData:f},isLoading:g,error:b,tradeFormValues:s,handleTradeFormChange:y=>{const{name:w,value:D,type:v}=y.target;c(T=>({...T,[w]:D}))}};return b?r.jsx(ut,{error:b,onRetry:p}):r.jsxs(nt,{children:[r.jsx(je,{isLoading:g,sessionNumber:1,isLiveSession:!0,onRefresh:p}),r.jsx(Ie,{activeTab:a,onTabChange:l,disabled:g}),r.jsx(at,{children:r.jsx(it,{children:r.jsx(h.Suspense,{fallback:r.jsx(Z,{}),children:r.jsx(ot,{...u})})})}),g&&r.jsx(xt,{children:r.jsx(ht,{})})]})},bt=e=>r.jsx(h.Suspense,{fallback:r.jsx(Z,{}),children:r.jsx(yt,{...e})}),It=({className:e,initialTab:t})=>r.jsx(bt,{className:e,initialTab:t});export{It as TradingDashboard,It as default};
//# sourceMappingURL=TradingDashboard-cac63645.js.map
