{"version": 3, "file": "TradingDashboard-cac63645.js", "sources": ["../../src/features/trading-dashboard/hooks/useTradingDashboard.ts", "../../src/features/trading-dashboard/components/F1DashboardHeader.tsx", "../../src/features/trading-dashboard/components/F1DashboardTabs.tsx", "../../src/features/trading-dashboard/components/useDashboardNavigation.ts", "../../src/features/trading-dashboard/components/MetricsPanel.tsx", "../../src/features/trading-dashboard/components/PerformanceChart.tsx", "../../src/features/trading-dashboard/components/RecentTradesTable.tsx", "../../src/features/trading-dashboard/components/SetupAnalysis.tsx", "../../src/features/trading-dashboard/components/dashboardTabConfig.tsx", "../../src/features/trading-dashboard/components/F1DashboardContainer.tsx", "../../src/features/trading-dashboard/TradingDashboard.tsx"], "sourcesContent": ["/**\n * useTradingDashboard Hook\n *\n * Custom hook for fetching and managing trading dashboard data\n */\n\nimport { CompleteTradeData, tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport { useCallback, useEffect, useState } from 'react';\nimport { SetupTransformer } from '../../../services/transformers/setupTransformer';\nimport {\n  ChartDataPoint,\n  DashboardState,\n  DashboardTrade,\n  PerformanceMetric,\n  SessionPerformance,\n  SetupPerformance,\n} from '../types';\n\n// Mock data removed - using real data from IndexedDB\n\n/**\n * useTradingDashboard Hook\n *\n * Fetches and manages trading dashboard data\n */\nexport const useTradingDashboard = (): DashboardState & {\n  fetchDashboardData: () => Promise<void>;\n  completeTradeData: CompleteTradeData[]; // Add original data for RecentTradesTable\n} => {\n  const [state, setState] = useState<DashboardState>({\n    trades: [],\n    performanceMetrics: [],\n    chartData: [],\n    setupPerformance: [],\n    sessionPerformance: [],\n    isLoading: true,\n    error: null,\n  });\n\n  // Store original CompleteTradeData for components that need nested format\n  const [completeTradeData, setCompleteTradeData] = useState<CompleteTradeData[]>([]);\n\n  // Calculate performance metrics based on trades\n  const calculateMetrics = (trades: DashboardTrade[]): PerformanceMetric[] => {\n    const totalTrades = trades.length;\n    const winningTrades = trades.filter(trade => trade.win).length;\n    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n    const totalPnl = trades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);\n    const avgRMultiple =\n      totalTrades > 0\n        ? trades.reduce((sum, trade) => sum + (trade.rMultiple || 0), 0) / totalTrades\n        : 0;\n\n    return [\n      { title: 'Win Rate', value: `${winRate.toFixed(1)}%` },\n      { title: 'Total P&L', value: `$${totalPnl.toFixed(2)}` },\n      { title: 'Avg R-Multiple', value: avgRMultiple.toFixed(2) },\n      { title: 'Total Trades', value: totalTrades },\n    ];\n  };\n\n  // Calculate setup performance\n  const calculateSetupPerformance = (trades: DashboardTrade[]): SetupPerformance[] => {\n    const setupMap = new Map<string, DashboardTrade[]>();\n\n    // Group trades by setup\n    trades.forEach(trade => {\n      const setup = trade.setup || 'Unknown';\n      if (!setupMap.has(setup)) {\n        setupMap.set(setup, []);\n      }\n      setupMap.get(setup)?.push(trade);\n    });\n\n    // Calculate metrics for each setup\n    return Array.from(setupMap.entries())\n      .map(([name, setupTrades]) => {\n        const totalTrades = setupTrades.length;\n        const winningTrades = setupTrades.filter(trade => trade.win).length;\n        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n        const pnl = setupTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);\n        const avgRMultiple =\n          totalTrades > 0\n            ? setupTrades.reduce((sum, trade) => sum + (trade.rMultiple || 0), 0) / totalTrades\n            : 0;\n\n        return {\n          name,\n          winRate,\n          avgRMultiple,\n          totalTrades,\n          pnl,\n        };\n      })\n      .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending\n  };\n\n  // Calculate session performance\n  const calculateSessionPerformance = (trades: DashboardTrade[]): SessionPerformance[] => {\n    const sessionMap = new Map<string, DashboardTrade[]>();\n\n    // Group trades by session\n    trades.forEach(trade => {\n      const session = trade.session || 'Unknown';\n      if (!sessionMap.has(session)) {\n        sessionMap.set(session, []);\n      }\n      sessionMap.get(session)?.push(trade);\n    });\n\n    // Calculate metrics for each session\n    return Array.from(sessionMap.entries())\n      .map(([name, sessionTrades]) => {\n        const totalTrades = sessionTrades.length;\n        const winningTrades = sessionTrades.filter(trade => trade.win).length;\n        const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n        const pnl = sessionTrades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);\n        const avgRMultiple =\n          totalTrades > 0\n            ? sessionTrades.reduce((sum, trade) => sum + (trade.rMultiple || 0), 0) / totalTrades\n            : 0;\n\n        return {\n          name,\n          winRate,\n          avgRMultiple,\n          totalTrades,\n          pnl,\n        };\n      })\n      .sort((a, b) => b.pnl - a.pnl); // Sort by P&L descending\n  };\n\n  // Generate chart data from trades\n  const generateChartData = (trades: DashboardTrade[]): ChartDataPoint[] => {\n    const tradesByDate = new Map<string, number>();\n\n    // Group trades by date and sum P&L\n    trades.forEach(trade => {\n      const date = new Date(trade.date).toLocaleDateString('en-US', {\n        month: 'numeric',\n        day: 'numeric',\n      });\n      const currentPnl = tradesByDate.get(date) || 0;\n      tradesByDate.set(date, currentPnl + (trade.pnl || 0));\n    });\n\n    // Convert to chart data with cumulative P&L\n    let cumulative = 0;\n    return Array.from(tradesByDate.entries())\n      .sort((a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime())\n      .map(([date, pnl]) => {\n        cumulative += pnl;\n        return { date, pnl, cumulative };\n      });\n  };\n\n  // Convert CompleteTradeData to dashboard Trade format\n  const convertToTradeFormat = (completeTradeData: CompleteTradeData[]): DashboardTrade[] => {\n    return completeTradeData.map(data => {\n      const trade = data.trade;\n\n      // Convert setup components to display string\n      let setupDisplay = 'No setup';\n      if (trade.setupComponents) {\n        setupDisplay = SetupTransformer.getShortDisplayString(trade.setupComponents);\n      } else if (trade.setup) {\n        setupDisplay = trade.setup;\n      }\n\n      return {\n        id: trade.id?.toString() || '0',\n        date: trade.date,\n        model: trade.model_type || 'Unknown',\n        session: trade.session || 'Unknown',\n        setup: setupDisplay,\n        entry: trade.entry_time || '00:00:00',\n        exit: trade.exit_time || '00:00:00',\n        direction: trade.direction,\n        market: trade.market || 'MNQ',\n        rMultiple: trade.r_multiple || 0,\n        patternQuality: trade.pattern_quality_rating || 0,\n        win: trade.win_loss === 'Win',\n        entryPrice: trade.entry_price || 0,\n        exitPrice: trade.exit_price || 0,\n        risk: trade.risk_points || 0,\n        pnl: trade.achieved_pl || 0,\n        dolTarget: trade.dol_target || '',\n        rdType: trade.rd_type || '',\n        entryVersion: data.fvg_details?.entry_version || '',\n        drawOnLiquidity: data.fvg_details?.draw_on_liquidity || '',\n      };\n    });\n  };\n\n  // Fetch dashboard data\n  const fetchDashboardData = useCallback(async () => {\n    setState(prev => ({ ...prev, isLoading: true, error: null }));\n\n    try {\n      // Fetch real trades from IndexedDB\n      const fetchedCompleteTradeData = await tradeStorageService.getAllTrades();\n\n      // Store original data for RecentTradesTable (preserves nested format)\n      setCompleteTradeData(fetchedCompleteTradeData);\n\n      // Convert to dashboard format for metrics calculations (preserves existing logic)\n      const trades = convertToTradeFormat(fetchedCompleteTradeData);\n\n      // Process the data (no changes to existing calculations)\n      const performanceMetrics = calculateMetrics(trades);\n      const setupPerformance = calculateSetupPerformance(trades);\n      const sessionPerformance = calculateSessionPerformance(trades);\n\n      // Generate chart data from real trades (no changes)\n      const chartData = generateChartData(trades);\n\n      setState({\n        trades,\n        performanceMetrics,\n        chartData,\n        setupPerformance,\n        sessionPerformance,\n        isLoading: false,\n        error: null,\n      });\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Failed to load dashboard data',\n      }));\n    }\n  }, []);\n\n  // Fetch data on initial load\n  useEffect(() => {\n    fetchDashboardData();\n  }, [fetchDashboardData]);\n\n  return {\n    ...state,\n    fetchDashboardData,\n    completeTradeData, // Provide original data for RecentTradesTable\n  };\n};\n\nexport default useTradingDashboard;\n", "/**\n * F1DashboardHeader Component\n * \n * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)\n * F1 racing-themed header for the trading dashboard feature.\n * \n * BENEFITS:\n * - Focused responsibility (header only)\n * - F1 racing theme with live session indicators\n * - Consistent with other F1Header components\n * - Better separation of concerns\n * - Reusable across dashboard contexts\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport interface F1DashboardHeaderProps {\n  /** Custom className */\n  className?: string;\n  /** Whether data is loading */\n  isLoading?: boolean;\n  /** Whether refresh is in progress */\n  isRefreshing?: boolean;\n  /** Current session number */\n  sessionNumber?: number;\n  /** Whether session is live */\n  isLiveSession?: boolean;\n  /** Refresh handler */\n  onRefresh?: () => void;\n  /** Custom title */\n  title?: string;\n}\n\nconst HeaderContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst F1Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: linear-gradient(\n    135deg,\n    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,\n    rgba(75, 85, 99, 0.1) 100%\n  );\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  position: relative;\n  overflow: hidden;\n\n  /* F1 Racing accent line */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg, \n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%, \n      transparent 100%\n    );\n  }\n`;\n\nconst F1Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 2px;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  span {\n    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    font-weight: 800;\n  }\n`;\n\nconst LiveIndicator = styled.div<{ $isLive: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  color: ${({ $isLive, theme }) => \n    $isLive \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  border: 1px solid ${({ $isLive, theme }) => \n    $isLive \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ $isLive, theme }) => \n    $isLive \n      ? `${theme.colors?.primary || 'var(--primary-color)'}20`\n      : 'transparent'};\n\n  &::before {\n    content: '●';\n    animation: ${({ $isLive }) => $isLive ? 'pulse 2s infinite' : 'none'};\n    font-size: 12px;\n  }\n\n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.5; }\n  }\n`;\n\nconst SubHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst SubTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};\n  margin: 0;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-weight: 600;\n`;\n\nconst StatusBadge = styled.span<{ $sessionNumber: number }>`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  padding: ${({ theme }) => theme.spacing?.xxs || '2px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst RefreshButton = styled.button<{ $isLoading: boolean }>`\n  background: ${({ $isLoading, theme }) => \n    $isLoading \n      ? theme.colors?.border || 'var(--border-primary)'\n      : theme.colors?.primary || 'var(--primary-color)'};\n  color: ${({ theme }) => theme.colors?.textInverse || '#ffffff'};\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  cursor: ${({ $isLoading }) => $isLoading ? 'not-allowed' : 'pointer'};\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  min-width: 120px;\n  justify-content: center;\n\n  &:hover:not(:disabled) {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n    box-shadow: 0 4px 8px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n  }\n`;\n\nconst RefreshIcon = styled.span<{ $isLoading: boolean }>`\n  display: inline-block;\n  animation: ${({ $isLoading }) => $isLoading ? 'spin 1s linear infinite' : 'none'};\n  \n  @keyframes spin {\n    from { transform: rotate(0deg); }\n    to { transform: rotate(360deg); }\n  }\n`;\n\n/**\n * F1DashboardHeader Component\n * \n * PATTERN: F1 Header Pattern\n * - Racing-inspired styling with live session indicators\n * - Animated status indicators and refresh button\n * - Consistent with F1 design system\n * - Accessible and responsive\n * - Professional trading dashboard appearance\n */\nexport const F1DashboardHeader: React.FC<F1DashboardHeaderProps> = ({\n  className,\n  isLoading = false,\n  isRefreshing = false,\n  sessionNumber = 1,\n  isLiveSession = true,\n  onRefresh,\n  title = 'Trading Dashboard',\n}) => {\n  return (\n    <HeaderContainer className={className}>\n      {/* F1 Racing Header */}\n      <F1Header>\n        <F1Title>\n          🏎️ TRADING <span>2025</span> DASHBOARD\n        </F1Title>\n        <LiveIndicator $isLive={isLiveSession}>\n          {isLiveSession ? 'LIVE SESSION' : 'OFFLINE'}\n        </LiveIndicator>\n      </F1Header>\n\n      {/* Sub Header */}\n      <SubHeader>\n        <TitleSection>\n          <SubTitle>{title}</SubTitle>\n          <StatusBadge $sessionNumber={sessionNumber}>\n            SESSION {sessionNumber}\n          </StatusBadge>\n        </TitleSection>\n        \n        {onRefresh && (\n          <RefreshButton\n            onClick={onRefresh}\n            disabled={isLoading}\n            $isLoading={isLoading}\n            title={isLoading ? 'Refreshing data...' : 'Refresh dashboard data'}\n          >\n            <RefreshIcon $isLoading={isLoading || isRefreshing}>\n              {isLoading || isRefreshing ? '⏳' : '🔄'}\n            </RefreshIcon>\n            {isLoading ? 'Refreshing...' : 'Refresh Data'}\n          </RefreshButton>\n        )}\n      </SubHeader>\n    </HeaderContainer>\n  );\n};\n\nexport default F1DashboardHeader;\n", "/**\n * F1DashboardTabs Component\n * \n * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)\n * F1 racing-themed tabs for dashboard navigation.\n * \n * BENEFITS:\n * - Focused responsibility (tab navigation only)\n * - F1 racing theme with smooth animations\n * - Consistent with other F1Tab components\n * - Better separation of concerns\n * - Reusable tab navigation pattern\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type DashboardTab = 'summary' | 'trades' | 'setups' | 'analytics';\n\nexport interface F1DashboardTabsProps {\n  /** Currently active tab */\n  activeTab: DashboardTab;\n  /** Tab change handler */\n  onTabChange: (tab: DashboardTab) => void;\n  /** Whether tabs are disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst TabsContainer = styled.div`\n  display: flex;\n  gap: 0;\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0 ${({ theme }) => theme.spacing?.xl || '32px'} 0;\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  position: relative;\n`;\n\nconst Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};\n  border: none;\n  background: transparent;\n  color: ${({ $isActive, theme }) => \n    $isActive \n      ? theme.colors?.textPrimary || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};\n  transition: all 0.2s ease;\n  font-weight: ${({ $isActive }) => $isActive ? '600' : '400'};\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  position: relative;\n  border-bottom: 2px solid transparent;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  /* F1 Racing active indicator */\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    transform: scaleX(${({ $isActive }) => $isActive ? 1 : 0});\n    transition: transform 0.2s ease;\n    transform-origin: center;\n  }\n\n  /* F1 Racing hover effect */\n  &:hover:not(:disabled) {\n    color: ${({ $isActive, theme }) => \n      $isActive \n        ? theme.colors?.textPrimary || '#ffffff'\n        : theme.colors?.textPrimary || '#ffffff'};\n    transform: translateY(-1px);\n\n    &::after {\n      transform: scaleX(1);\n      background: ${({ $isActive, theme }) => \n        $isActive \n          ? theme.colors?.primary || 'var(--primary-color)'\n          : theme.colors?.textSecondary || 'var(--text-secondary)'};\n    }\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  /* Disabled styling */\n  ${({ $disabled }) =>\n    $disabled &&\n    `\n    opacity: 0.5;\n    cursor: not-allowed;\n  `}\n\n  /* Mobile responsive */\n  @media (max-width: 768px) {\n    padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  }\n`;\n\nconst TabIcon = styled.span`\n  margin-right: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-size: 16px;\n  \n  @media (max-width: 768px) {\n    margin-right: 0;\n    font-size: 14px;\n  }\n`;\n\nconst TabLabel = styled.span`\n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\n/**\n * Tab configuration with icons and labels\n */\nconst TAB_CONFIG: Record<DashboardTab, { icon: string; label: string; description: string }> = {\n  summary: {\n    icon: '📊',\n    label: 'Summary',\n    description: 'Performance overview and key metrics',\n  },\n  trades: {\n    icon: '📋',\n    label: 'Trades',\n    description: 'Recent trades and transaction history',\n  },\n  setups: {\n    icon: '🎯',\n    label: 'Setups',\n    description: 'Setup analysis and performance breakdown',\n  },\n  analytics: {\n    icon: '📈',\n    label: 'Analytics',\n    description: 'Advanced analytics and quick trade entry',\n  },\n};\n\n/**\n * F1DashboardTabs Component\n * \n * PATTERN: F1 Tabs Pattern\n * - Racing-inspired styling with red accents\n * - Smooth hover animations and transitions\n * - Clear visual feedback for active state\n * - Accessible keyboard navigation\n * - Responsive design for mobile\n */\nexport const F1DashboardTabs: React.FC<F1DashboardTabsProps> = ({\n  activeTab,\n  onTabChange,\n  disabled = false,\n  className,\n}) => {\n  const handleTabClick = (tab: DashboardTab) => {\n    if (!disabled) {\n      onTabChange(tab);\n    }\n  };\n  \n  const handleKeyDown = (event: React.KeyboardEvent, tab: DashboardTab) => {\n    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {\n      event.preventDefault();\n      onTabChange(tab);\n    }\n  };\n  \n  return (\n    <TabsContainer className={className} role=\"tablist\">\n      {(Object.keys(TAB_CONFIG) as DashboardTab[]).map((tab) => {\n        const config = TAB_CONFIG[tab];\n        const isActive = activeTab === tab;\n        \n        return (\n          <Tab\n            key={tab}\n            $isActive={isActive}\n            $disabled={disabled}\n            onClick={() => handleTabClick(tab)}\n            onKeyDown={(e) => handleKeyDown(e, tab)}\n            disabled={disabled}\n            role=\"tab\"\n            aria-selected={isActive}\n            aria-controls={`dashboard-panel-${tab}`}\n            tabIndex={disabled ? -1 : 0}\n            title={config.description}\n          >\n            <TabIcon>{config.icon}</TabIcon>\n            <TabLabel>{config.label}</TabLabel>\n          </Tab>\n        );\n      })}\n    </TabsContainer>\n  );\n};\n\nexport default F1DashboardTabs;\n", "/**\n * useDashboardNavigation Hook\n * \n * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)\n * Hook for managing dashboard navigation and tab state.\n * \n * BENEFITS:\n * - Focused responsibility (navigation only)\n * - Persistent tab state with localStorage\n * - Type-safe navigation handling\n * - Reusable across dashboard components\n * - Better separation of concerns\n */\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { DashboardTab } from './F1DashboardTabs';\n\nexport interface UseDashboardNavigationProps {\n  /** Default tab to show */\n  defaultTab?: DashboardTab;\n  /** Storage key for persistence */\n  storageKey?: string;\n}\n\nexport interface UseDashboardNavigationReturn {\n  /** Current active tab */\n  activeTab: DashboardTab;\n  /** Change active tab */\n  setActiveTab: (tab: DashboardTab) => void;\n  /** Navigate to next tab */\n  nextTab: () => void;\n  /** Navigate to previous tab */\n  previousTab: () => void;\n  /** Check if tab is active */\n  isTabActive: (tab: DashboardTab) => boolean;\n  /** Get tab index */\n  getTabIndex: (tab: DashboardTab) => number;\n  /** Get all available tabs */\n  availableTabs: DashboardTab[];\n}\n\n/**\n * Available tabs in order\n */\nconst AVAILABLE_TABS: DashboardTab[] = ['summary', 'trades', 'setups', 'analytics'];\n\n/**\n * Default storage key\n */\nconst DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:dashboard:active-tab';\n\n/**\n * Load tab from localStorage\n */\nconst loadTabFromStorage = (storageKey: string, defaultTab: DashboardTab): DashboardTab => {\n  try {\n    const stored = localStorage.getItem(storageKey);\n    if (stored && AVAILABLE_TABS.includes(stored as DashboardTab)) {\n      return stored as DashboardTab;\n    }\n  } catch (error) {\n    console.warn('Failed to load dashboard tab from localStorage:', error);\n  }\n  return defaultTab;\n};\n\n/**\n * Save tab to localStorage\n */\nconst saveTabToStorage = (storageKey: string, tab: DashboardTab): void => {\n  try {\n    localStorage.setItem(storageKey, tab);\n  } catch (error) {\n    console.warn('Failed to save dashboard tab to localStorage:', error);\n  }\n};\n\n/**\n * useDashboardNavigation Hook\n * \n * Manages tab navigation state with persistence and keyboard navigation.\n */\nexport const useDashboardNavigation = ({\n  defaultTab = 'summary',\n  storageKey = DEFAULT_STORAGE_KEY,\n}: UseDashboardNavigationProps = {}): UseDashboardNavigationReturn => {\n  \n  // Initialize active tab from storage or default\n  const [activeTab, setActiveTabState] = useState<DashboardTab>(() =>\n    loadTabFromStorage(storageKey, defaultTab)\n  );\n  \n  /**\n   * Set active tab with persistence\n   */\n  const setActiveTab = useCallback((tab: DashboardTab) => {\n    if (AVAILABLE_TABS.includes(tab)) {\n      setActiveTabState(tab);\n      saveTabToStorage(storageKey, tab);\n    }\n  }, [storageKey]);\n  \n  /**\n   * Navigate to next tab\n   */\n  const nextTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;\n    setActiveTab(AVAILABLE_TABS[nextIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Navigate to previous tab\n   */\n  const previousTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;\n    setActiveTab(AVAILABLE_TABS[previousIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Check if tab is active\n   */\n  const isTabActive = useCallback((tab: DashboardTab): boolean => {\n    return activeTab === tab;\n  }, [activeTab]);\n  \n  /**\n   * Get tab index\n   */\n  const getTabIndex = useCallback((tab: DashboardTab): number => {\n    return AVAILABLE_TABS.indexOf(tab);\n  }, []);\n  \n  // Handle keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Only handle if no input is focused\n      if (document.activeElement?.tagName === 'INPUT' || \n          document.activeElement?.tagName === 'TEXTAREA' ||\n          document.activeElement?.tagName === 'SELECT') {\n        return;\n      }\n      \n      // Handle Ctrl/Cmd + Arrow keys for tab navigation\n      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault();\n            previousTab();\n            break;\n          case 'ArrowRight':\n            event.preventDefault();\n            nextTab();\n            break;\n        }\n      }\n      \n      // Handle number keys for direct tab navigation\n      if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {\n        const tabIndex = parseInt(event.key) - 1;\n        if (tabIndex < AVAILABLE_TABS.length) {\n          event.preventDefault();\n          setActiveTab(AVAILABLE_TABS[tabIndex]);\n        }\n      }\n      \n      // Handle Alt + Tab keys for dashboard-specific navigation\n      if (event.altKey && !event.ctrlKey && !event.metaKey) {\n        switch (event.key.toLowerCase()) {\n          case 's':\n            event.preventDefault();\n            setActiveTab('summary');\n            break;\n          case 't':\n            event.preventDefault();\n            setActiveTab('trades');\n            break;\n          case 'u':\n            event.preventDefault();\n            setActiveTab('setups');\n            break;\n          case 'a':\n            event.preventDefault();\n            setActiveTab('analytics');\n            break;\n        }\n      }\n    };\n    \n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [nextTab, previousTab, setActiveTab]);\n  \n  return {\n    activeTab,\n    setActiveTab,\n    nextTab,\n    previousTab,\n    isTabActive,\n    getTabIndex,\n    availableTabs: AVAILABLE_TABS,\n  };\n};\n\nexport default useDashboardNavigation;\n", "/**\n * Metrics Panel Component\n *\n * Displays key trading metrics in a card format\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { PerformanceMetric } from '../types';\n\ninterface MetricsPanelProps {\n  metrics: PerformanceMetric[];\n  isLoading?: boolean;\n}\n\n// F1 Racing Performance Grid\nconst Container = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: ${({ theme }) => theme.spacing.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.xl};\n`;\n\n// F1 Racing Metric Card\nconst MetricCard = styled.div`\n  background: linear-gradient(\n    135deg,\n    ${({ theme }) => theme.colors.surface} 0%,\n    rgba(75, 85, 99, 0.05) 100%\n  );\n  border: 1px solid var(--border-primary);\n  border-radius: ${({ theme }) => theme.borderRadius.lg};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  min-height: 140px;\n  position: relative;\n  overflow: hidden;\n  transition: all ${({ theme }) => theme.transitions.normal};\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(90deg, var(--border-primary) 0%, transparent 100%);\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 10px;\n    right: 10px;\n    width: 40px;\n    height: 40px;\n    background: radial-gradient(circle, rgba(75, 85, 99, 0.1) 0%, transparent 70%);\n    border-radius: 50%;\n  }\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 12px 40px rgba(75, 85, 99, 0.2);\n    border-color: var(--text-secondary);\n  }\n`;\n\n// F1 Racing Metric Title\nconst MetricTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n  text-transform: uppercase;\n  letter-spacing: 1.5px;\n  font-weight: bold;\n  position: relative;\n  z-index: 2;\n`;\n\n// F1 Racing Metric Value\nconst MetricValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.h3};\n  font-weight: bold;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-family: 'Orbitron', 'Inter', monospace;\n  position: relative;\n  z-index: 2;\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n`;\n\n// F1 Racing Metric Change Indicator\nconst MetricChange = styled.div<{ isPositive?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme, isPositive }) => (isPositive ? theme.colors.success : theme.colors.error)};\n  display: flex;\n  align-items: center;\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  font-weight: bold;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  position: relative;\n  z-index: 2;\n\n  &::before {\n    content: ${({ isPositive }) => (isPositive ? \"'▲'\" : \"'▼'\")};\n    margin-right: ${({ theme }) => theme.spacing.xs};\n    font-size: 0.8em;\n  }\n`;\n\n// F1 Racing Loading Indicator\nconst LoadingIndicator = styled.div`\n  color: var(--text-secondary);\n  font-weight: bold;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  position: relative;\n  z-index: 2;\n\n  &::after {\n    content: '...';\n    animation: loading 1.5s infinite;\n  }\n\n  @keyframes loading {\n    0%,\n    33% {\n      content: '.';\n    }\n    34%,\n    66% {\n      content: '..';\n    }\n    67%,\n    100% {\n      content: '...';\n    }\n  }\n`;\n\n/**\n * F1 Racing MetricsPanel Component\n *\n * Displays key trading metrics in F1-inspired performance cards\n */\nexport const MetricsPanel: React.FC<MetricsPanelProps> = ({ metrics, isLoading = false }) => {\n  if (isLoading) {\n    return (\n      <Container>\n        {[1, 2, 3, 4].map((i) => (\n          <MetricCard key={i}>\n            <MetricTitle>LOADING DATA</MetricTitle>\n            <LoadingIndicator>FETCHING TELEMETRY</LoadingIndicator>\n          </MetricCard>\n        ))}\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      {metrics.map((metric, index) => (\n        <MetricCard key={index}>\n          <MetricTitle>{metric.title}</MetricTitle>\n          <MetricValue>{metric.value}</MetricValue>\n          {metric.change !== undefined && (\n            <MetricChange isPositive={metric.isPositive}>\n              {Math.abs(metric.change)}% {metric.isPositive ? 'GAIN' : 'LOSS'}\n            </MetricChange>\n          )}\n        </MetricCard>\n      ))}\n    </Container>\n  );\n};\n\nexport default MetricsPanel;\n", "/**\n * Performance Chart Component\n *\n * Displays a chart showing trading performance over time\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { \n  LineChart, \n  Line, \n  XAxis, \n  YAxis, \n  CartesianGrid, \n  Tooltip, \n  ResponsiveContainer,\n  Legend\n} from 'recharts';\nimport { ChartDataPoint } from '../types';\n\ninterface PerformanceChartProps {\n  data: ChartDataPoint[];\n  isLoading?: boolean;\n}\n\nconst ChartContainer = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.md};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n  height: 300px;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ChartTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst NoDataContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n/**\n * Custom tooltip for the chart\n */\nconst CustomTooltip: React.FC<any> = ({ active, payload, label }) => {\n  if (active && payload && payload.length) {\n    return (\n      <div\n        style={{\n          backgroundColor: '#252a37',\n          padding: '10px',\n          border: '1px solid #333',\n          borderRadius: '4px',\n        }}\n      >\n        <p style={{ margin: 0 }}>{`Date: ${label}`}</p>\n        <p style={{ margin: 0, color: 'var(--primary-color)' }}>\n          {`Daily P&L: $${payload[0].value.toFixed(2)}`}\n        </p>\n        <p style={{ margin: 0, color: 'var(--info-color)' }}>\n          {`Cumulative P&L: $${payload[1].value.toFixed(2)}`}\n        </p>\n      </div>\n    );\n  }\n\n  return null;\n};\n\n/**\n * PerformanceChart Component\n * \n * Displays a chart showing trading performance over time\n */\nexport const PerformanceChart: React.FC<PerformanceChartProps> = ({\n  data,\n  isLoading = false\n}) => {\n  if (isLoading) {\n    return (\n      <ChartContainer>\n        <ChartTitle>Performance</ChartTitle>\n        <LoadingContainer>Loading chart data...</LoadingContainer>\n      </ChartContainer>\n    );\n  }\n\n  if (!data || data.length === 0) {\n    return (\n      <ChartContainer>\n        <ChartTitle>Performance</ChartTitle>\n        <NoDataContainer>No performance data available</NoDataContainer>\n      </ChartContainer>\n    );\n  }\n\n  return (\n    <ChartContainer>\n      <ChartTitle>Performance</ChartTitle>\n      <ResponsiveContainer width=\"100%\" height=\"90%\">\n        <LineChart\n          data={data}\n          margin={{\n            top: 5,\n            right: 30,\n            left: 20,\n            bottom: 5,\n          }}\n        >\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"rgba(255, 255, 255, 0.1)\" />\n          <XAxis \n            dataKey=\"date\" \n            stroke=\"#aaaaaa\" \n            tick={{ fill: '#aaaaaa' }} \n          />\n          <YAxis \n            stroke=\"#aaaaaa\" \n            tick={{ fill: '#aaaaaa' }} \n            tickFormatter={(value) => `$${value}`} \n          />\n          <Tooltip content={<CustomTooltip />} />\n          <Legend />\n          <Line\n            type=\"monotone\"\n            dataKey=\"pnl\"\n            name=\"Daily P&L\"\n            stroke=\"var(--primary-color)\"\n            activeDot={{ r: 8 }}\n            strokeWidth={2}\n          />\n          <Line\n            type=\"monotone\"\n            dataKey=\"cumulative\"\n            name=\"Cumulative P&L\"\n            stroke=\"var(--info-color)\"\n            strokeWidth={2}\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </ChartContainer>\n  );\n};\n\nexport default PerformanceChart;\n", "/**\n * Recent Trades Table Component\n *\n * Displays a table of recent trades\n */\n\nimport React, { useState, useMemo, useCallback } from 'react';\nimport styled from 'styled-components';\nimport { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\ninterface RecentTradesTableProps {\n  trades: CompleteTradeData[];\n  isLoading?: boolean;\n}\n\ntype SortField =\n  | 'date'\n  | 'setup'\n  | 'session'\n  | 'direction'\n  | 'market'\n  | 'entry'\n  | 'exit'\n  | 'rMultiple'\n  | 'pnl';\ntype SortDirection = 'asc' | 'desc';\n\nconst TableContainer = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.md};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n  overflow-x: auto;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst TableTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  min-width: 800px;\n`;\n\nconst TableHead = styled.thead`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst TableHeader = styled.th<{ sortable?: boolean; active?: boolean }>`\n  text-align: left;\n  padding: ${({ theme }) => theme.spacing.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  text-transform: uppercase;\n  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};\n  user-select: none;\n  position: relative;\n\n  ${({ sortable, theme }) =>\n    sortable &&\n    `\n    &:hover {\n      color: ${theme.colors.textPrimary};\n      background-color: rgba(255, 255, 255, 0.05);\n    }\n  `}\n\n  ${({ active, theme }) =>\n    active &&\n    `\n    color: ${theme.colors.primary};\n    font-weight: 600;\n  `}\n`;\n\nconst SortIcon = styled.span<{ direction?: SortDirection }>`\n  margin-left: 4px;\n  font-size: 12px;\n  opacity: 0.7;\n\n  ${({ direction }) => {\n    if (direction === 'asc') return 'content: \"↑\";';\n    if (direction === 'desc') return 'content: \"↓\";';\n    return 'content: \"↕\"; opacity: 0.3;';\n  }}\n\n  &::after {\n    ${({ direction }) => {\n      if (direction === 'asc') return 'content: \"↑\";';\n      if (direction === 'desc') return 'content: \"↓\";';\n      return 'content: \"↕\";';\n    }}\n  }\n`;\n\nconst TableRow = styled.tr`\n  border-bottom: 1px solid var(--border-primary);\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.05);\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\nconst DirectionCell = styled(TableCell)<{ direction: 'Long' | 'Short' }>`\n  color: ${({ theme, direction }) =>\n    direction === 'Long' ? theme.colors.success : theme.colors.error};\n`;\n\nconst ResultCell = styled(TableCell)<{ win: boolean }>`\n  color: ${({ theme, win }) => (win ? theme.colors.success : theme.colors.error)};\n`;\n\nconst PnlCell = styled(TableCell)<{ value: number }>`\n  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst NoDataContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n/**\n * Debug function for comprehensive trade sorting analysis\n */\nconst debugTradesSorting = (\n  trades: CompleteTradeData[],\n  sortBy: SortField,\n  sortOrder: SortDirection\n) => {\n  console.group('🔍 Recent Trades Sorting Debug');\n\n  // Log raw data\n  console.log('📊 Raw trades count:', trades.length);\n  console.log('📊 Sort criteria:', { sortBy, sortOrder });\n\n  // Log first few trades to understand structure\n  console.log('📋 Sample raw trades (first 3):');\n  trades.slice(0, 3).forEach((trade, index) => {\n    // Add null checks to prevent runtime errors\n    if (!trade || !trade.trade) {\n      console.log(`Trade ${index + 1}: INVALID - trade or trade.trade is null/undefined`);\n      return;\n    }\n\n    console.log(`Trade ${index + 1}:`, {\n      id: trade.trade.id,\n      market: trade.trade.market,\n      date: trade.trade.date,\n      direction: trade.trade.direction,\n      entry: trade.trade.entry_price,\n      exit: trade.trade.exit_price,\n      // Log the field we're sorting by\n      [sortBy]: (trade.trade as any)[sortBy],\n    });\n  });\n\n  console.groupEnd();\n};\n\n/**\n * RecentTradesTable Component\n *\n * Displays a table of recent trades with sorting functionality\n */\nexport const RecentTradesTable: React.FC<RecentTradesTableProps> = ({\n  trades,\n  isLoading = false,\n}) => {\n  // IMMEDIATE DEBUG: Log that the enhanced component is being used\n  console.log('🔥 ENHANCED RecentTradesTable component is being executed!');\n  const [sortField, setSortField] = useState<SortField>('date');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n\n  // Debug component mounting and data\n  React.useEffect(() => {\n    console.log('🚀 RecentTradesTable component mounted/updated');\n    console.log('📊 Props received:', {\n      tradesCount: trades?.length || 0,\n      isLoading,\n      tradesType: typeof trades,\n      tradesIsArray: Array.isArray(trades),\n      tradesValue: trades, // Log the actual trades value\n    });\n\n    if (trades && trades.length > 0) {\n      // Check if first trade is valid before accessing properties\n      const firstTrade = trades[0];\n      const lastTrade = trades[trades.length - 1];\n\n      if (firstTrade && firstTrade.trade) {\n        console.log('🎯 RecentTradesTable received trades:', {\n          count: trades.length,\n          firstTrade: {\n            id: firstTrade.trade.id,\n            date: firstTrade.trade.date,\n            market: firstTrade.trade.market,\n            // Check for missing properties that cause TypeScript errors\n            setup: firstTrade.trade.setup,\n            session: firstTrade.trade.session,\n          },\n          lastTrade:\n            lastTrade && lastTrade.trade\n              ? {\n                  id: lastTrade.trade.id,\n                  date: lastTrade.trade.date,\n                  market: lastTrade.trade.market,\n                }\n              : 'Invalid last trade',\n        });\n      } else {\n        console.log('⚠️ RecentTradesTable: Trades array exists but contains invalid data');\n        console.log('🔍 First trade:', firstTrade);\n      }\n    } else {\n      console.log('⚠️ RecentTradesTable: No trades data or empty array');\n      console.log('🔍 Trades value:', trades);\n      console.log('🔍 Is loading:', isLoading);\n    }\n  }, [trades, isLoading]);\n\n  // Handle column sorting\n  const handleSort = useCallback(\n    (field: SortField) => {\n      console.log(`🔄 Sorting by ${field}`);\n\n      if (sortField === field) {\n        // Toggle direction if same field\n        const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';\n        setSortDirection(newDirection);\n        console.log(`🔄 Toggled direction to ${newDirection}`);\n      } else {\n        // Set new field and default direction\n        setSortField(field);\n        setSortDirection('desc');\n        console.log(`🔄 New field ${field} with desc direction`);\n      }\n    },\n    [sortField, sortDirection]\n  );\n\n  // Sort trades with comprehensive debugging\n  const sortedTrades = useMemo(() => {\n    if (!trades || trades.length === 0) return [];\n\n    // Filter out any invalid trades before processing\n    const validTrades = trades.filter((trade) => trade && trade.trade);\n\n    if (validTrades.length === 0) {\n      console.warn('⚠️ No valid trades found after filtering');\n      return [];\n    }\n\n    debugTradesSorting(validTrades, sortField, sortDirection);\n\n    const sorted = [...validTrades].sort((a, b) => {\n      // Additional safety checks\n      if (!a || !a.trade || !b || !b.trade) {\n        console.warn('⚠️ Invalid trade data in sort function');\n        return 0;\n      }\n\n      let aValue: any, bValue: any;\n\n      // Extract values based on sortField\n      switch (sortField) {\n        case 'date':\n          aValue = new Date(a.trade.date || '');\n          bValue = new Date(b.trade.date || '');\n          break;\n        case 'setup':\n          aValue = (a.trade as any).setup || '';\n          bValue = (b.trade as any).setup || '';\n          break;\n        case 'session':\n          aValue = (a.trade as any).session || '';\n          bValue = (b.trade as any).session || '';\n          break;\n        case 'direction':\n          aValue = a.trade.direction?.toLowerCase() || '';\n          bValue = b.trade.direction?.toLowerCase() || '';\n          break;\n        case 'market':\n          aValue = (a.trade as any).market || '';\n          bValue = (b.trade as any).market || '';\n          break;\n        case 'entry':\n          aValue = parseFloat(String(a.trade.entry_price || 0));\n          bValue = parseFloat(String(b.trade.entry_price || 0));\n          break;\n        case 'exit':\n          aValue = parseFloat(String(a.trade.exit_price || 0));\n          bValue = parseFloat(String(b.trade.exit_price || 0));\n          break;\n        case 'rMultiple':\n          aValue = parseFloat(String(a.trade.r_multiple || 0));\n          bValue = parseFloat(String(b.trade.r_multiple || 0));\n          break;\n        case 'pnl':\n          aValue = parseFloat(String(a.trade.achieved_pl || 0));\n          bValue = parseFloat(String(b.trade.achieved_pl || 0));\n          break;\n        default:\n          aValue = (a.trade as any)[sortField];\n          bValue = (b.trade as any)[sortField];\n      }\n\n      console.log(`🔄 Comparing: ${aValue} vs ${bValue} (${sortField})`);\n\n      // Handle different data types\n      if (aValue instanceof Date && bValue instanceof Date) {\n        const comparison = aValue.getTime() - bValue.getTime();\n        return sortDirection === 'asc' ? comparison : -comparison;\n      }\n\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        const comparison = aValue - bValue;\n        return sortDirection === 'asc' ? comparison : -comparison;\n      }\n\n      // String comparison\n      const comparison = String(aValue).localeCompare(String(bValue));\n      return sortDirection === 'asc' ? comparison : -comparison;\n    });\n\n    // Log sorted results\n    console.log('✅ Sorted trades (first 3):');\n    sorted.slice(0, 3).forEach((trade, index) => {\n      if (trade && trade.trade) {\n        console.log(`Sorted ${index + 1}:`, {\n          id: trade.trade.id,\n          date: trade.trade.date,\n          [sortField]: (trade.trade as any)[sortField],\n        });\n      } else {\n        console.log(`Sorted ${index + 1}: INVALID TRADE`);\n      }\n    });\n\n    return sorted;\n  }, [trades, sortField, sortDirection]);\n  if (isLoading) {\n    return (\n      <TableContainer>\n        <TableTitle>Recent Trades</TableTitle>\n        <LoadingContainer>Loading trades data...</LoadingContainer>\n      </TableContainer>\n    );\n  }\n\n  if (!trades || trades.length === 0) {\n    return (\n      <TableContainer>\n        <TableTitle>Recent Trades</TableTitle>\n        <NoDataContainer>No trades data available</NoDataContainer>\n      </TableContainer>\n    );\n  }\n\n  return (\n    <TableContainer>\n      <TableTitle>Recent Trades ({sortedTrades.length})</TableTitle>\n      <Table>\n        <TableHead>\n          <tr>\n            <TableHeader sortable active={sortField === 'date'} onClick={() => handleSort('date')}>\n              Date\n              <SortIcon direction={sortField === 'date' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'setup'}\n              onClick={() => handleSort('setup')}\n            >\n              Setup\n              <SortIcon direction={sortField === 'setup' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'session'}\n              onClick={() => handleSort('session')}\n            >\n              Session\n              <SortIcon direction={sortField === 'session' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'direction'}\n              onClick={() => handleSort('direction')}\n            >\n              Direction\n              <SortIcon direction={sortField === 'direction' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'market'}\n              onClick={() => handleSort('market')}\n            >\n              Market\n              <SortIcon direction={sortField === 'market' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'entry'}\n              onClick={() => handleSort('entry')}\n            >\n              Entry\n              <SortIcon direction={sortField === 'entry' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader sortable active={sortField === 'exit'} onClick={() => handleSort('exit')}>\n              Exit\n              <SortIcon direction={sortField === 'exit' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader\n              sortable\n              active={sortField === 'rMultiple'}\n              onClick={() => handleSort('rMultiple')}\n            >\n              R-Multiple\n              <SortIcon direction={sortField === 'rMultiple' ? sortDirection : undefined} />\n            </TableHeader>\n            <TableHeader sortable active={sortField === 'pnl'} onClick={() => handleSort('pnl')}>\n              P&L\n              <SortIcon direction={sortField === 'pnl' ? sortDirection : undefined} />\n            </TableHeader>\n          </tr>\n        </TableHead>\n        <tbody>\n          {sortedTrades.map((trade, index) => {\n            // Additional safety check for rendering\n            if (!trade || !trade.trade) {\n              console.warn(`⚠️ Skipping invalid trade at index ${index}`);\n              return null;\n            }\n\n            return (\n              <TableRow key={trade.trade.id || `trade-${index}`}>\n                <TableCell>{trade.trade.date || 'N/A'}</TableCell>\n                <TableCell>{trade.trade.setup || 'N/A'}</TableCell>\n                <TableCell>{trade.trade.session || 'N/A'}</TableCell>\n                <DirectionCell direction={trade.trade.direction as 'Long' | 'Short'}>\n                  {trade.trade.direction || 'N/A'}\n                </DirectionCell>\n                <TableCell>{trade.trade.market || 'N/A'}</TableCell>\n                <TableCell>{trade.trade.entry_price || 'N/A'}</TableCell>\n                <TableCell>{trade.trade.exit_price || 'N/A'}</TableCell>\n                <ResultCell win={trade.trade.win_loss === 'Win'}>\n                  {(trade.trade.r_multiple || 0).toFixed(2)}\n                </ResultCell>\n                <PnlCell value={trade.trade.achieved_pl || 0}>\n                  ${(trade.trade.achieved_pl || 0).toFixed(2)}\n                </PnlCell>\n              </TableRow>\n            );\n          })}\n        </tbody>\n      </Table>\n    </TableContainer>\n  );\n};\n\nexport default RecentTradesTable;\n", "/**\n * Setup Analysis Component\n *\n * Displays performance metrics for different trading setups\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { SetupPerformance, SessionPerformance } from '../types';\n\ninterface SetupAnalysisProps {\n  setupPerformance: SetupPerformance[];\n  sessionPerformance: SessionPerformance[];\n  isLoading?: boolean;\n}\n\nconst Container = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: ${({ theme }) => theme.spacing.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst AnalysisCard = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.md};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst CardTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n`;\n\nconst TableHead = styled.thead`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst TableHeader = styled.th`\n  text-align: left;\n  padding: ${({ theme }) => theme.spacing.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  text-transform: uppercase;\n`;\n\nconst TableRow = styled.tr`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.05);\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\nconst WinRateCell = styled(TableCell)<{ value: number }>`\n  color: ${({ theme, value }) => {\n    if (value >= 70) return theme.colors.success;\n    if (value >= 50) return theme.colors.warning;\n    return theme.colors.error;\n  }};\n`;\n\nconst PnlCell = styled(TableCell)<{ value: number }>`\n  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst NoDataContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n/**\n * SetupAnalysis Component\n *\n * Displays performance metrics for different trading setups\n */\nexport const SetupAnalysis: React.FC<SetupAnalysisProps> = ({\n  setupPerformance,\n  sessionPerformance,\n  isLoading = false,\n}) => {\n  if (isLoading) {\n    return (\n      <Container>\n        <AnalysisCard>\n          <CardTitle>Setup Performance</CardTitle>\n          <LoadingContainer>Loading setup data...</LoadingContainer>\n        </AnalysisCard>\n        <AnalysisCard>\n          <CardTitle>Session Performance</CardTitle>\n          <LoadingContainer>Loading session data...</LoadingContainer>\n        </AnalysisCard>\n      </Container>\n    );\n  }\n\n  return (\n    <Container>\n      <AnalysisCard>\n        <CardTitle>Setup Performance</CardTitle>\n        {setupPerformance.length === 0 ? (\n          <NoDataContainer>No setup data available</NoDataContainer>\n        ) : (\n          <Table>\n            <TableHead>\n              <tr>\n                <TableHeader>Setup</TableHeader>\n                <TableHeader>Win Rate</TableHeader>\n                <TableHeader>Avg R</TableHeader>\n                <TableHeader>Trades</TableHeader>\n                <TableHeader>P&L</TableHeader>\n              </tr>\n            </TableHead>\n            <tbody>\n              {setupPerformance.map((setup, index) => (\n                <TableRow key={index}>\n                  <TableCell>{setup.name}</TableCell>\n                  <WinRateCell value={setup.winRate}>{setup.winRate.toFixed(1)}%</WinRateCell>\n                  <TableCell>{setup.avgRMultiple.toFixed(2)}</TableCell>\n                  <TableCell>{setup.totalTrades}</TableCell>\n                  <PnlCell value={setup.pnl}>${setup.pnl.toFixed(2)}</PnlCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        )}\n      </AnalysisCard>\n\n      <AnalysisCard>\n        <CardTitle>Session Performance</CardTitle>\n        {sessionPerformance.length === 0 ? (\n          <NoDataContainer>No session data available</NoDataContainer>\n        ) : (\n          <Table>\n            <TableHead>\n              <tr>\n                <TableHeader>Session</TableHeader>\n                <TableHeader>Win Rate</TableHeader>\n                <TableHeader>Avg R</TableHeader>\n                <TableHeader>Trades</TableHeader>\n                <TableHeader>P&L</TableHeader>\n              </tr>\n            </TableHead>\n            <tbody>\n              {sessionPerformance.map((session, index) => (\n                <TableRow key={index}>\n                  <TableCell>{session.name}</TableCell>\n                  <WinRateCell value={session.winRate}>{session.winRate.toFixed(1)}%</WinRateCell>\n                  <TableCell>{session.avgRMultiple.toFixed(2)}</TableCell>\n                  <TableCell>{session.totalTrades}</TableCell>\n                  <PnlCell value={session.pnl}>${session.pnl.toFixed(2)}</PnlCell>\n                </TableRow>\n              ))}\n            </tbody>\n          </Table>\n        )}\n      </AnalysisCard>\n    </Container>\n  );\n};\n\nexport default SetupAnalysis;\n", "/**\n * Dashboard Tab Configuration\n *\n * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)\n * Centralized configuration for dashboard tabs and their content.\n *\n * BENEFITS:\n * - Single source of truth for tab definitions\n * - Easy to maintain and extend\n * - Type-safe tab configurations\n * - Reusable across different dashboard views\n * - Clear content mapping\n */\n\nimport React from 'react';\nimport { DashboardTab } from './F1DashboardTabs';\nimport MetricsPanel from '../components/MetricsPanel';\nimport PerformanceChart from '../components/PerformanceChart';\nimport RecentTradesTable from '../components/RecentTradesTable';\nimport SetupAnalysis from '../components/SetupAnalysis';\nimport TradeFormBasicFields from '../../trade-journal/components/trade-form/TradeFormBasicFields';\nimport styled from 'styled-components';\n\nexport interface DashboardTabConfig {\n  id: DashboardTab;\n  title: string;\n  description: string;\n  icon: string;\n  component: React.ComponentType<any>;\n  showInMobile: boolean;\n  requiresData: boolean;\n}\n\nexport interface DashboardTabContentProps {\n  /** Current active tab */\n  activeTab: DashboardTab;\n  /** Dashboard data */\n  data: {\n    trades: any[]; // DashboardTrade[] for metrics\n    performanceMetrics: any[];\n    chartData: any[];\n    setupPerformance: any[];\n    sessionPerformance: any[];\n    completeTradeData: any[]; // CompleteTradeData[] for RecentTradesTable\n  };\n  /** Loading state */\n  isLoading: boolean;\n  /** Error state */\n  error: string | null;\n  /** Trade form values for analytics tab */\n  tradeFormValues?: any;\n  /** Trade form change handler */\n  handleTradeFormChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n}\n\n// Styled components for analytics layout\nconst AnalyticsContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 400px;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints?.lg || '1024px'}) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ChartsSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst TradeFormSection = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  height: fit-content;\n`;\n\nconst FormTitle = styled.h3`\n  margin: 0 0 ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n`;\n\n/**\n * Summary Tab Content\n */\nconst SummaryTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {\n  // Use completeTradeData for RecentTradesTable (correct nested format)\n  // Sort by date descending and take first 5\n  const recentTrades =\n    data.completeTradeData\n      ?.sort((a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime())\n      ?.slice(0, 5) || [];\n\n  return (\n    <>\n      <MetricsPanel metrics={data.performanceMetrics} isLoading={isLoading} />\n      <PerformanceChart data={data.chartData} isLoading={isLoading} />\n      <RecentTradesTable trades={recentTrades} isLoading={isLoading} />\n    </>\n  );\n};\n\n/**\n * Trades Tab Content\n */\nconst TradesTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {\n  // Use completeTradeData for RecentTradesTable (correct nested format)\n  // Sort by date descending for full trade list\n  const sortedTrades =\n    data.completeTradeData?.sort(\n      (a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()\n    ) || [];\n\n  return <RecentTradesTable trades={sortedTrades} isLoading={isLoading} />;\n};\n\n/**\n * Setups Tab Content\n */\nconst SetupsTabContent: React.FC<DashboardTabContentProps> = ({ data, isLoading }) => {\n  return (\n    <SetupAnalysis\n      setupPerformance={data.setupPerformance}\n      sessionPerformance={data.sessionPerformance}\n      isLoading={isLoading}\n    />\n  );\n};\n\n/**\n * Analytics Tab Content\n */\nconst AnalyticsTabContent: React.FC<DashboardTabContentProps> = ({\n  data,\n  isLoading,\n  tradeFormValues,\n  handleTradeFormChange,\n}) => {\n  return (\n    <AnalyticsContainer>\n      <ChartsSection>\n        <MetricsPanel metrics={data.performanceMetrics} isLoading={isLoading} />\n        <PerformanceChart data={data.chartData} isLoading={isLoading} />\n        <SetupAnalysis\n          setupPerformance={data.setupPerformance}\n          sessionPerformance={data.sessionPerformance}\n          isLoading={isLoading}\n        />\n      </ChartsSection>\n\n      <TradeFormSection>\n        <FormTitle>🏎️ Quick Trade Entry</FormTitle>\n        {tradeFormValues && handleTradeFormChange && (\n          <TradeFormBasicFields\n            formValues={tradeFormValues}\n            handleChange={(\n              e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n            ) =>\n              handleTradeFormChange(e as React.ChangeEvent<HTMLInputElement | HTMLSelectElement>)\n            }\n            validationErrors={{}}\n          />\n        )}\n      </TradeFormSection>\n    </AnalyticsContainer>\n  );\n};\n\n/**\n * Tab configuration with components and metadata\n */\nexport const DASHBOARD_TAB_CONFIG: Record<DashboardTab, DashboardTabConfig> = {\n  summary: {\n    id: 'summary',\n    title: 'Performance Summary',\n    description: 'Overview of trading performance and key metrics',\n    icon: '📊',\n    component: SummaryTabContent,\n    showInMobile: true,\n    requiresData: true,\n  },\n  trades: {\n    id: 'trades',\n    title: 'Recent Trades',\n    description: 'Complete list of recent trading activity',\n    icon: '📋',\n    component: TradesTabContent,\n    showInMobile: true,\n    requiresData: true,\n  },\n  setups: {\n    id: 'setups',\n    title: 'Setup Analysis',\n    description: 'Performance breakdown by trading setups and sessions',\n    icon: '🎯',\n    component: SetupsTabContent,\n    showInMobile: true,\n    requiresData: true,\n  },\n  analytics: {\n    id: 'analytics',\n    title: 'Advanced Analytics',\n    description: 'Comprehensive analytics with quick trade entry',\n    icon: '📈',\n    component: AnalyticsTabContent,\n    showInMobile: false,\n    requiresData: true,\n  },\n};\n\n/**\n * Get tab configuration by ID\n */\nexport const getTabConfig = (tabId: DashboardTab): DashboardTabConfig => {\n  return DASHBOARD_TAB_CONFIG[tabId];\n};\n\n/**\n * Get all tab configurations\n */\nexport const getAllTabConfigs = (): DashboardTabConfig[] => {\n  return Object.values(DASHBOARD_TAB_CONFIG);\n};\n\n/**\n * Get mobile-friendly tabs\n */\nexport const getMobileTabConfigs = (): DashboardTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.showInMobile);\n};\n\n/**\n * Get tabs that require data\n */\nexport const getDataRequiredTabConfigs = (): DashboardTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.requiresData);\n};\n\n/**\n * Tab Content Renderer Component\n */\nexport const DashboardTabContentRenderer: React.FC<DashboardTabContentProps> = props => {\n  const { activeTab } = props;\n  const config = getTabConfig(activeTab);\n\n  if (!config) {\n    return (\n      <div\n        style={{\n          padding: '48px',\n          textAlign: 'center',\n          color: 'var(--error-color)',\n        }}\n      >\n        ❌ Unknown tab: {activeTab}\n      </div>\n    );\n  }\n\n  const TabComponent = config.component;\n\n  return (\n    <div\n      id={`dashboard-panel-${activeTab}`}\n      role='tabpanel'\n      aria-labelledby={`dashboard-tab-${activeTab}`}\n    >\n      <TabComponent {...props} />\n    </div>\n  );\n};\n\nexport default DashboardTabContentRenderer;\n", "/**\n * F1DashboardContainer Component\n *\n * REFACTORED FROM: TradingDashboard.tsx (388 lines → focused components)\n * Main orchestrator for trading dashboard with F1 container pattern.\n *\n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and loading states\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense, useState } from 'react';\nimport styled from 'styled-components';\nimport { TradeFormData } from '@adhd-trading-dashboard/shared';\nimport { useTradingDashboard } from '../hooks/useTradingDashboard';\nimport { F1DashboardHeader } from './F1DashboardHeader';\nimport { F1DashboardTabs } from './F1DashboardTabs';\nimport { useDashboardNavigation } from './useDashboardNavigation';\nimport { DashboardTabContentRenderer } from './dashboardTabConfig';\n\nexport interface F1DashboardContainerProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  min-height: 100vh;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  max-width: 1400px;\n  margin: 0 auto;\n`;\n\nconst ContentArea = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  flex: 1;\n`;\n\nconst TabContentContainer = styled.div`\n  animation: fadeIn 0.3s ease-in-out;\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(10px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n`;\n\nconst LoadingIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 0.7;\n    }\n    50% {\n      opacity: 0.3;\n    }\n  }\n`;\n\nconst LoadingText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;\n  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n`;\n\nconst ErrorTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst ErrorMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst RetryButton = styled.button`\n  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n    ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  color: white;\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n  }\n`;\n\nconst LoadingOverlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: ${({ theme }) => theme.zIndex?.modal || 1000};\n`;\n\nconst LoadingSpinner = styled.div`\n  border: 4px solid rgba(255, 255, 255, 0.3);\n  border-top: 4px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  border-radius: 50%;\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\n/**\n * LoadingFallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <LoadingState>\n    <LoadingIcon>🏎️</LoadingIcon>\n    <LoadingText>Loading Trading Dashboard...</LoadingText>\n  </LoadingState>\n);\n\n/**\n * ErrorFallback Component\n */\nconst ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (\n  <ErrorState>\n    <ErrorIcon>⚠️</ErrorIcon>\n    <ErrorTitle>Dashboard Error</ErrorTitle>\n    <ErrorMessage>{error}</ErrorMessage>\n    <RetryButton onClick={onRetry}>Try Again</RetryButton>\n  </ErrorState>\n);\n\n/**\n * DashboardContent Component\n */\nconst DashboardContent: React.FC<F1DashboardContainerProps> = ({ initialTab }) => {\n  const {\n    trades,\n    performanceMetrics,\n    chartData,\n    setupPerformance,\n    sessionPerformance,\n    isLoading,\n    error,\n    fetchDashboardData,\n    completeTradeData, // Get original data for RecentTradesTable\n  } = useTradingDashboard();\n\n  const { activeTab, setActiveTab } = useDashboardNavigation({\n    defaultTab: initialTab || 'summary',\n  });\n\n  // Trade form state for Analytics tab\n  const [tradeFormValues, setTradeFormValues] = useState<TradeFormData>({\n    date: new Date().toISOString().split('T')[0],\n    symbol: 'MNQ',\n    direction: 'long',\n    quantity: '1',\n    entryPrice: '0',\n    exitPrice: '0',\n    profit: '0',\n    model: '',\n    session: '',\n    setup: '',\n    patternQuality: '',\n    dolTarget: '',\n    rdType: '',\n    drawOnLiquidity: '',\n    entryVersion: '',\n    notes: '',\n    tags: [],\n    result: 'win',\n  });\n\n  const handleTradeFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    setTradeFormValues((prev) => ({\n      ...prev,\n      [name]: type === 'number' ? value : value,\n    }));\n  };\n\n  // Prepare data and handlers for tab content\n  const tabContentProps = {\n    activeTab,\n    data: {\n      trades, // Keep DashboardTrade[] for metrics calculations\n      performanceMetrics,\n      chartData,\n      setupPerformance,\n      sessionPerformance,\n      completeTradeData, // Add CompleteTradeData[] for RecentTradesTable\n    },\n    isLoading,\n    error,\n    tradeFormValues,\n    handleTradeFormChange,\n  };\n\n  if (error) {\n    return <ErrorFallback error={error} onRetry={fetchDashboardData} />;\n  }\n\n  return (\n    <Container>\n      {/* F1 Racing Header */}\n      <F1DashboardHeader\n        isLoading={isLoading}\n        sessionNumber={1}\n        isLiveSession={true}\n        onRefresh={fetchDashboardData}\n      />\n\n      {/* F1 Racing Tabs */}\n      <F1DashboardTabs activeTab={activeTab} onTabChange={setActiveTab} disabled={isLoading} />\n\n      {/* Tab Content */}\n      <ContentArea>\n        <TabContentContainer>\n          <Suspense fallback={<LoadingFallback />}>\n            <DashboardTabContentRenderer {...tabContentProps} />\n          </Suspense>\n        </TabContentContainer>\n      </ContentArea>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <LoadingOverlay>\n          <LoadingSpinner />\n        </LoadingOverlay>\n      )}\n    </Container>\n  );\n};\n\n/**\n * F1DashboardContainer Component\n *\n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const F1DashboardContainer: React.FC<F1DashboardContainerProps> = (props) => {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <DashboardContent {...props} />\n    </Suspense>\n  );\n};\n\nexport default F1DashboardContainer;\n", "/**\n * Trading Dashboard Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 388 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 95% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { F1DashboardContainer } from './components/F1DashboardContainer';\n\nexport interface TradingDashboardProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';\n}\n\n/**\n * Trading Dashboard Component\n *\n * Simple wrapper that renders the container.\n * Follows the proven architecture pattern.\n */\nexport const TradingDashboard: React.FC<TradingDashboardProps> = ({ className, initialTab }) => {\n  return <F1DashboardContainer className={className} initialTab={initialTab} />;\n};\n\nexport default TradingDashboard;\n"], "names": ["useTradingDashboard", "state", "setState", "useState", "trades", "performanceMetrics", "chartData", "setupPerformance", "sessionPerformance", "isLoading", "error", "completeTradeData", "setCompleteTradeData", "calculateMetrics", "totalTrades", "length", "winningTrades", "filter", "trade", "win", "winRate", "totalPnl", "reduce", "sum", "pnl", "avgRMultiple", "rMultiple", "title", "value", "toFixed", "calculateSetupPerformance", "setupMap", "Map", "for<PERSON>ach", "setup", "has", "set", "get", "push", "Array", "from", "entries", "map", "name", "setupTrades", "sort", "a", "b", "calculateSessionPerformance", "sessionMap", "session", "sessionTrades", "generateChartData", "tradesByDate", "date", "Date", "toLocaleDateString", "month", "day", "currentPnl", "cumulative", "getTime", "convertToTradeFormat", "data", "setupDisplay", "setupComponents", "SetupTransformer", "getShortDisplayString", "id", "toString", "model", "model_type", "entry", "entry_time", "exit", "exit_time", "direction", "market", "r_multiple", "patternQuality", "pattern_quality_rating", "win_loss", "entryPrice", "entry_price", "exitPrice", "exit_price", "risk", "risk_points", "achieved_pl", "do<PERSON><PERSON><PERSON><PERSON>", "dol_target", "rdType", "rd_type", "entryVersion", "fvg_details", "entry_version", "drawOnLiquidity", "draw_on_liquidity", "fetchDashboardData", "useCallback", "prev", "fetchedCompleteTradeData", "tradeStorageService", "getAllTrades", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "lg", "xl", "<PERSON><PERSON><PERSON><PERSON>", "colors", "surface", "border", "borderRadius", "primary", "F1Title", "h1", "fontSizes", "h2", "textPrimary", "LiveIndicator", "xs", "$isLive", "textSecondary", "sm", "full", "SubHeader", "md", "TitleSection", "SubTitle", "xxl", "StatusBadge", "span", "xxs", "RefreshButton", "button", "$isLoading", "textInverse", "primaryDark", "RefreshIcon", "F1DashboardHeader", "className", "isRefreshing", "sessionNumber", "isLiveSession", "onRefresh", "jsxs", "jsx", "TabsContainer", "Tab", "$isActive", "$disabled", "TabIcon", "TabLabel", "TAB_CONFIG", "summary", "icon", "label", "description", "setups", "analytics", "F1DashboardTabs", "activeTab", "onTabChange", "disabled", "handleTabClick", "tab", "handleKeyDown", "event", "key", "preventDefault", "Object", "keys", "config", "isActive", "e", "AVAILABLE_TABS", "DEFAULT_STORAGE_KEY", "loadTabFromStorage", "storageKey", "defaultTab", "stored", "localStorage", "getItem", "includes", "warn", "saveTabToStorage", "setItem", "useDashboardNavigation", "setActiveTabState", "setActiveTab", "nextTab", "nextIndex", "indexOf", "previousTab", "currentIndex", "previousIndex", "isTabActive", "getTabIndex", "document", "activeElement", "tagName", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "tabIndex", "parseInt", "altKey", "toLowerCase", "addEventListener", "window", "removeEventListener", "availableTabs", "Container", "MetricCard", "transitions", "normal", "MetricTitle", "h3", "MetricValue", "MetricChange", "isPositive", "success", "LoadingIndicator", "MetricsPanel", "metrics", "i", "metric", "index", "change", "undefined", "Math", "abs", "ChartContainer", "shadows", "ChartTitle", "LoadingContainer", "NoDataContainer", "CustomTooltip", "active", "payload", "backgroundColor", "padding", "margin", "color", "Performance<PERSON>hart", "ResponsiveContainer", "Line<PERSON>hart", "top", "right", "left", "bottom", "Cartesian<PERSON><PERSON>", "XAxis", "fill", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "Legend", "Line", "r", "TableContainer", "TableTitle", "Table", "table", "TableHead", "thead", "TableHeader", "th", "sortable", "SortIcon", "TableRow", "tr", "TableCell", "td", "DirectionCell", "styled", "ResultCell", "PnlCell", "debugTradesSorting", "sortBy", "sortOrder", "console", "group", "log", "slice", "groupEnd", "RecentTradesTable", "sortField", "setSortField", "sortDirection", "setSortDirection", "React", "tradesCount", "tradesType", "tradesIsArray", "isArray", "tradesValue", "firstTrade", "lastTrade", "count", "handleSort", "field", "newDirection", "sortedTrades", "useMemo", "validTrades", "sorted", "aValue", "bValue", "parseFloat", "String", "comparison", "localeCompare", "breakpoints", "AnalysisCard", "CardTitle", "WinRateCell", "warning", "SetupAnalysis", "AnalyticsContainer", "ChartsSection", "TradeFormSection", "FormTitle", "Summary<PERSON><PERSON><PERSON><PERSON><PERSON>", "recentTrades", "Fragment", "TradesTabContent", "SetupsTabContent", "AnalyticsTab<PERSON><PERSON>nt", "tradeFormValues", "handleTradeFormChange", "TradeFormBasicFields", "DASHBOARD_TAB_CONFIG", "component", "showInMobile", "requiresData", "getTabConfig", "tabId", "DashboardTabContentRenderer", "props", "textAlign", "TabComponent", "background", "ContentArea", "TabContentContainer", "LoadingState", "LoadingIcon", "LoadingText", "p", "ErrorState", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorMessage", "RetryButton", "LoadingOverlay", "zIndex", "modal", "LoadingSpinner", "LoadingFallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRetry", "DashboardContent", "initialTab", "setTradeFormValues", "toISOString", "split", "symbol", "quantity", "profit", "notes", "tags", "result", "tabContentProps", "type", "target", "Suspense", "F1DashboardContainer", "TradingDashboard"], "mappings": "4YAyBO,MAAMA,GAAsBA,IAG9B,CACH,KAAM,CAACC,EAAOC,CAAQ,EAAIC,WAAyB,CACjDC,OAAQ,CAAE,EACVC,mBAAoB,CAAE,EACtBC,UAAW,CAAE,EACbC,iBAAkB,CAAE,EACpBC,mBAAoB,CAAE,EACtBC,UAAW,GACXC,MAAO,IAAA,CACR,EAGK,CAACC,EAAmBC,CAAoB,EAAIT,EAAAA,SAA8B,CAAE,CAAA,EAG5EU,EAAoBT,GAAkD,CAC1E,MAAMU,EAAcV,EAAOW,OACrBC,EAAgBZ,EAAOa,OAAgBC,GAAAA,EAAMC,GAAG,EAAEJ,OAClDK,EAAUN,EAAc,EAAKE,EAAgBF,EAAe,IAAM,EAClEO,EAAWjB,EAAOkB,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMM,KAAO,GAAI,CAAC,EAClEC,EACJX,EAAc,EACVV,EAAOkB,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMQ,WAAa,GAAI,CAAC,EAAIZ,EACjE,EAEN,MAAO,CACL,CAAEa,MAAO,WAAYC,MAAO,GAAGR,EAAQS,QAAQ,CAAC,IAAA,EAChD,CAAEF,MAAO,YAAaC,MAAO,IAAIP,EAASQ,QAAQ,CAAC,GAAA,EACnD,CAAEF,MAAO,iBAAkBC,MAAOH,EAAaI,QAAQ,CAAC,CAAA,EACxD,CAAEF,MAAO,eAAgBC,MAAOd,CAAAA,CAAa,CAAA,EAK3CgB,EAA6B1B,GAAiD,CAC5E2B,MAAAA,MAAeC,IAGrB5B,OAAAA,EAAO6B,QAAiBf,GAAA,OAChBgB,MAAAA,EAAQhB,EAAMgB,OAAS,UACxBH,EAASI,IAAID,CAAK,GACZE,EAAAA,IAAIF,EAAO,CAAA,CAAE,GAExBH,EAAAA,EAASM,IAAIH,CAAK,IAAlBH,MAAAA,EAAqBO,KAAKpB,EAAK,CAChC,EAGMqB,MAAMC,KAAKT,EAASU,QAAQ,CAAC,EACjCC,IAAI,CAAC,CAACC,EAAMC,CAAW,IAAM,CAC5B,MAAM9B,EAAc8B,EAAY7B,OAC1BC,EAAgB4B,EAAY3B,OAAgBC,GAAAA,EAAMC,GAAG,EAAEJ,OACvDK,EAAUN,EAAc,EAAKE,EAAgBF,EAAe,IAAM,EAClEU,EAAMoB,EAAYtB,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMM,KAAO,GAAI,CAAC,EAClEC,EACJX,EAAc,EACV8B,EAAYtB,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMQ,WAAa,GAAI,CAAC,EAAIZ,EACtE,EAEC,MAAA,CACL6B,KAAAA,EACAvB,QAAAA,EACAK,aAAAA,EACAX,YAAAA,EACAU,IAAAA,CAAAA,CACF,CACD,EACAqB,KAAK,CAACC,EAAGC,IAAMA,EAAEvB,IAAMsB,EAAEtB,GAAG,CAAA,EAI3BwB,EAA+B5C,GAAmD,CAChF6C,MAAAA,MAAiBjB,IAGvB5B,OAAAA,EAAO6B,QAAiBf,GAAA,OAChBgC,MAAAA,EAAUhC,EAAMgC,SAAW,UAC5BD,EAAWd,IAAIe,CAAO,GACdd,EAAAA,IAAIc,EAAS,CAAA,CAAE,GAE5BD,EAAAA,EAAWZ,IAAIa,CAAO,IAAtBD,MAAAA,EAAyBX,KAAKpB,EAAK,CACpC,EAGMqB,MAAMC,KAAKS,EAAWR,QAAQ,CAAC,EACnCC,IAAI,CAAC,CAACC,EAAMQ,CAAa,IAAM,CAC9B,MAAMrC,EAAcqC,EAAcpC,OAC5BC,EAAgBmC,EAAclC,OAAgBC,GAAAA,EAAMC,GAAG,EAAEJ,OACzDK,EAAUN,EAAc,EAAKE,EAAgBF,EAAe,IAAM,EAClEU,EAAM2B,EAAc7B,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMM,KAAO,GAAI,CAAC,EACpEC,EACJX,EAAc,EACVqC,EAAc7B,OAAO,CAACC,EAAKL,IAAUK,GAAOL,EAAMQ,WAAa,GAAI,CAAC,EAAIZ,EACxE,EAEC,MAAA,CACL6B,KAAAA,EACAvB,QAAAA,EACAK,aAAAA,EACAX,YAAAA,EACAU,IAAAA,CAAAA,CACF,CACD,EACAqB,KAAK,CAACC,EAAGC,IAAMA,EAAEvB,IAAMsB,EAAEtB,GAAG,CAAA,EAI3B4B,EAAqBhD,GAA+C,CAClEiD,MAAAA,MAAmBrB,IAGzB5B,EAAO6B,QAAiBf,GAAA,CACtB,MAAMoC,EAAO,IAAIC,KAAKrC,EAAMoC,IAAI,EAAEE,mBAAmB,QAAS,CAC5DC,MAAO,UACPC,IAAK,SAAA,CACN,EACKC,EAAaN,EAAahB,IAAIiB,CAAI,GAAK,EAC7CD,EAAajB,IAAIkB,EAAMK,GAAczC,EAAMM,KAAO,EAAE,CAAA,CACrD,EAGD,IAAIoC,EAAa,EACjB,OAAOrB,MAAMC,KAAKa,EAAaZ,QAAAA,CAAS,EACrCI,KAAK,CAACC,EAAGC,IAAM,IAAIQ,KAAKT,EAAE,CAAC,CAAC,EAAEe,QAAQ,EAAI,IAAIN,KAAKR,EAAE,CAAC,CAAC,EAAEc,QAAS,CAAA,EAClEnB,IAAI,CAAC,CAACY,EAAM9B,CAAG,KACAA,GAAAA,EACP,CAAE8B,KAAAA,EAAM9B,IAAAA,EAAKoC,WAAAA,CAAAA,EACrB,CAAA,EAICE,EAAwBnD,GACrBA,EAAkB+B,IAAYqB,GAAA,WACnC,MAAM7C,EAAQ6C,EAAK7C,MAGnB,IAAI8C,EAAe,WACnB,OAAI9C,EAAM+C,gBACOC,EAAAA,GAAiBC,sBAAsBjD,EAAM+C,eAAe,EAClE/C,EAAMgB,QACf8B,EAAe9C,EAAMgB,OAGhB,CACLkC,KAAIlD,EAAAA,EAAMkD,KAANlD,YAAAA,EAAUmD,aAAc,IAC5Bf,KAAMpC,EAAMoC,KACZgB,MAAOpD,EAAMqD,YAAc,UAC3BrB,QAAShC,EAAMgC,SAAW,UAC1BhB,MAAO8B,EACPQ,MAAOtD,EAAMuD,YAAc,WAC3BC,KAAMxD,EAAMyD,WAAa,WACzBC,UAAW1D,EAAM0D,UACjBC,OAAQ3D,EAAM2D,QAAU,MACxBnD,UAAWR,EAAM4D,YAAc,EAC/BC,eAAgB7D,EAAM8D,wBAA0B,EAChD7D,IAAKD,EAAM+D,WAAa,MACxBC,WAAYhE,EAAMiE,aAAe,EACjCC,UAAWlE,EAAMmE,YAAc,EAC/BC,KAAMpE,EAAMqE,aAAe,EAC3B/D,IAAKN,EAAMsE,aAAe,EAC1BC,UAAWvE,EAAMwE,YAAc,GAC/BC,OAAQzE,EAAM0E,SAAW,GACzBC,eAAc9B,EAAAA,EAAK+B,cAAL/B,YAAAA,EAAkBgC,gBAAiB,GACjDC,kBAAiBjC,EAAAA,EAAK+B,cAAL/B,YAAAA,EAAkBkC,oBAAqB,EAAA,CAC1D,CACD,EAIGC,EAAqBC,EAAAA,YAAY,SAAY,CACjDjG,EAAkBkG,IAAA,CAAE,GAAGA,EAAM3F,UAAW,GAAMC,MAAO,IAAO,EAAA,EAExD,GAAA,CAEI2F,MAAAA,EAA2B,MAAMC,GAAoBC,eAG3D3F,EAAqByF,CAAwB,EAGvCjG,MAAAA,EAAS0D,EAAqBuC,CAAwB,EAGtDhG,EAAqBQ,EAAiBT,CAAM,EAC5CG,EAAmBuB,EAA0B1B,CAAM,EACnDI,EAAqBwC,EAA4B5C,CAAM,EAGvDE,EAAY8C,EAAkBhD,CAAM,EAEjCF,EAAA,CACPE,OAAAA,EACAC,mBAAAA,EACAC,UAAAA,EACAC,iBAAAA,EACAC,mBAAAA,EACAC,UAAW,GACXC,MAAO,IAAA,CACR,QACMA,GACCA,QAAAA,MAAM,iCAAkCA,CAAK,EACrDR,EAAkBkG,IAAA,CAChB,GAAGA,EACH3F,UAAW,GACXC,MAAO,+BACP,EAAA,CACJ,CACF,EAAG,CAAE,CAAA,EAGL8F,OAAAA,EAAAA,UAAU,IAAM,CACKN,GAAA,EAClB,CAACA,CAAkB,CAAC,EAEhB,CACL,GAAGjG,EACHiG,mBAAAA,EACAvF,kBAAAA,CAAAA,CAEJ,ECpNM8F,GAAyBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAGzB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC1B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAAM,EAGvDC,GAAkBR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,uCAAA,kDAAA,kBAAA,+IAAA,0BAAA,EAId,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAG3C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAGxB,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAcpD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAMhEC,GAAiBC,EAAAA,GAAEd,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,oIAAA,oBAAA,EACV,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBa,KAAM,UAE1C,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAO1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAKrEM,GAAuBnB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,UAAA,0EAAA,YAAA,IAAA,kBAAA,qBAAA,eAAA,oCAAA,yEAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAClC,CAAC,CAAEC,QAAAA,EAASjB,MAAAA,CAAM,aACzBiB,OAAAA,IACIjB,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,yBACzBT,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,gBAAiB,yBAIxB,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBmB,KAAM,YACxC,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAAS,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,OAC3E,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBoB,OAAQ,UACxC,CAAC,CAAEH,QAAAA,EAASjB,MAAAA,CAAM,aACpCiB,OAAAA,IACIjB,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,yBACzBT,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAChB,CAAC,CAAEU,QAAAA,EAASjB,MAAAA,CAAM,WAC9BiB,OAAAA,EACI,KAAGjB,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,2BAC5B,eAIS,CAAC,CAAEQ,QAAAA,CAAQ,IAAMA,EAAU,oBAAsB,MAAM,EAUlEI,GAAmBzB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gFAAA,4BAAA,GAAA,EAIR,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAC3B,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAGrFgB,GAAsB3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGtB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,OAAM,EAG7CE,GAAkBX,EAAAA,GAAEhB,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,mBAAA,mBAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiByB,MAAO,YAE3C,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,UAAS,EAI1DY,GAAqBC,EAAAA,KAAI9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,UAAA,YAAA,IAAA,kBAAA,cAAA,qCAAA,iDAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAC7C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe4B,MAAO,OAAS,CAAC,CAAE5B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,OAC5E,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBoB,OAAQ,UAC/C,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBgB,KAAM,WAE/B,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAK9EsB,GAAuBC,EAAAA,OAAMjC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,UAAA,8BAAA,YAAA,IAAA,cAAA,2BAAA,wCAAA,sJAAA,oDAAA,+EAAA,EACnB,CAAC,CAAEgC,WAAAA,EAAY/B,MAAAA,CAAM,aACjC+B,OAAAA,IACI/B,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,0BACxBP,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBACtB,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcgC,cAAe,WAEpC,CAAC,CAAEhC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBsB,KAAM,OAC/C,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,OAAS,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAC/E,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBmB,KAAM,YAEzC,CAAC,CAAEY,WAAAA,CAAW,IAAMA,EAAa,cAAgB,UAGpD,CAAC,CAAE/B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAQ3B,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAciC,cAAe,uBAElC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAYpFyB,GAAqBP,EAAAA,KAAI9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,kCAAA,8EAAA,EAEhB,CAAC,CAAEgC,WAAAA,CAAW,IAAMA,EAAa,0BAA4B,MAAM,EAkBrEI,GAAsDA,CAAC,CAClEC,UAAAA,EACAzI,UAAAA,EAAY,GACZ0I,aAAAA,EAAe,GACfC,cAAAA,EAAgB,EAChBC,cAAAA,EAAgB,GAChBC,UAAAA,EACA3H,MAAAA,EAAQ,mBACV,IAEI4H,OAAC9C,IAAgB,UAAAyC,EAEf,SAAA,CAAAK,OAACrC,GACC,CAAA,SAAA,CAAAqC,OAAC/B,GAAO,CAAA,SAAA,CAAA,eACMgC,EAAAA,IAAC,QAAK,SAAI,MAAA,CAAA,EAAO,YAAA,EAC/B,QACC3B,GAAc,CAAA,QAASwB,EACrBA,SAAAA,EAAgB,eAAiB,UACpC,CAAA,EACF,SAGClB,GACC,CAAA,SAAA,CAAAoB,OAAClB,GACC,CAAA,SAAA,CAAAmB,EAAAA,IAAClB,IAAU3G,SAAMA,CAAA,CAAA,EACjB4H,EAAAA,KAACf,GAAY,CAAA,eAAgBY,EAAc,SAAA,CAAA,WAChCA,CAAAA,EACX,CAAA,EACF,EAECE,GACCC,EAAA,KAACZ,GACC,CAAA,QAASW,EACT,SAAU7I,EACV,WAAYA,EACZ,MAAOA,EAAY,qBAAuB,yBAE1C,SAAA,CAAA+I,EAAAA,IAACR,IAAY,WAAYvI,GAAa0I,EACnC1I,SAAa0I,GAAAA,EAAe,IAAM,IACrC,CAAA,EACC1I,EAAY,gBAAkB,cAAA,EACjC,CAAA,EAEJ,CACF,CAAA,CAAA,ECjOEgJ,GAAuB/C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6BAAA,MAAA,8BAAA,qBAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAAY,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QACnE,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAIrFqC,GAAad,EAAAA,OAAMjC,WAAA,CAAAC,YAAA,MAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,6CAAA,WAAA,yCAAA,cAAA,iQAAA,qBAAA,0FAAA,uEAAA,uDAAA,qCAAA,IAAA,cAAA,IAAA,EACZ,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAAU,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAGpF,CAAC,CAAE2C,UAAAA,EAAW7C,MAAAA,CAAM,aAC3B6C,OAAAA,IACI7C,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,YAC7Bd,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,gBAAiB,yBAC3B,CAAC,CAAE4B,UAAAA,CAAU,IAAMA,EAAY,cAAgB,UAE1C,CAAC,CAAED,UAAAA,CAAU,IAAMA,EAAY,MAAQ,MACzC,CAAC,CAAE7C,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBsB,KAAM,QAenC,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBAClC,CAAC,CAAEoC,UAAAA,CAAU,IAAMA,EAAY,EAAI,EAO9C,CAAC,CAAEA,UAAAA,EAAW7C,MAAAA,CAAM,aAC3B6C,OAAAA,IACI7C,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,YAC7Bd,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAKnB,CAAC,CAAE+B,UAAAA,EAAW7C,MAAAA,CAAM,aAChC6C,OAAAA,IACI7C,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,yBACzBT,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,gBAAiB,yBASvC,CAAC,CAAE4B,UAAAA,CAAU,IACbA,GACA;AAAA;AAAA;AAAA,IAOW,CAAC,CAAE9C,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,OAAS,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAC/E,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBmB,KAAM,WAAU,EAI3D4B,GAAiBpB,EAAAA,KAAI9B,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gBAAA,0EAAA,EACT,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,MAAK,EASrDgC,GAAkBrB,EAAAA,KAAI9B,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAI3B,EAAA,CAAA,yCAAA,CAAA,EAKKkD,EAAyF,CAC7FC,QAAS,CACPC,KAAM,KACNC,MAAO,UACPC,YAAa,sCACf,EACA/J,OAAQ,CACN6J,KAAM,KACNC,MAAO,SACPC,YAAa,uCACf,EACAC,OAAQ,CACNH,KAAM,KACNC,MAAO,SACPC,YAAa,0CACf,EACAE,UAAW,CACTJ,KAAM,KACNC,MAAO,YACPC,YAAa,0CACf,CACF,EAYaG,GAAkDA,CAAC,CAC9DC,UAAAA,EACAC,YAAAA,EACAC,SAAAA,EAAW,GACXvB,UAAAA,CACF,IAAM,CACEwB,MAAAA,EAAkBC,GAAsB,CACvCF,GACHD,EAAYG,CAAG,CACjB,EAGIC,EAAgBA,CAACC,EAA4BF,IAAsB,EAClEE,EAAMC,MAAQ,SAAWD,EAAMC,MAAQ,MAAQ,CAACL,IACnDI,EAAME,eAAe,EACrBP,EAAYG,CAAG,EACjB,EAIA,OAAAnB,EAAA,IAACC,GAAc,CAAA,UAAAP,EAAsB,KAAK,UACtC8B,gBAAOC,KAAKlB,CAAU,EAAqBrH,IAAaiI,GAAA,CAClDO,MAAAA,EAASnB,EAAWY,CAAG,EACvBQ,EAAWZ,IAAcI,EAE/B,OACGpB,EAAAA,KAAAG,GAAA,CAEC,UAAWyB,EACX,UAAWV,EACX,QAAS,IAAMC,EAAeC,CAAG,EACjC,aAAkBC,EAAcQ,EAAGT,CAAG,EACtC,SAAAF,EACA,KAAK,MACL,gBAAeU,EACf,gBAAe,mBAAmBR,IAClC,SAAUF,EAAW,GAAK,EAC1B,MAAOS,EAAOf,YAEd,SAAA,CAACX,EAAAA,IAAAK,GAAA,CAASqB,WAAOjB,IAAK,CAAA,EACtBT,EAAAA,IAACM,GAAUoB,CAAAA,SAAAA,EAAOhB,KAAM,CAAA,CAAA,CAAA,EAbnBS,CAcP,CAEH,CAAA,CACH,CAAA,CAEJ,EChKMU,EAAiC,CAAC,UAAW,SAAU,SAAU,WAAW,EAK5EC,GAAsB,8CAKtBC,GAAqBA,CAACC,EAAoBC,IAA2C,CACrF,GAAA,CACIC,MAAAA,EAASC,aAAaC,QAAQJ,CAAU,EAC9C,GAAIE,GAAUL,EAAeQ,SAASH,CAAsB,EACnDA,OAAAA,QAEFhL,GACCoL,QAAAA,KAAK,kDAAmDpL,CAAK,CACvE,CACO+K,OAAAA,CACT,EAKMM,GAAmBA,CAACP,EAAoBb,IAA4B,CACpE,GAAA,CACWqB,aAAAA,QAAQR,EAAYb,CAAG,QAC7BjK,GACCoL,QAAAA,KAAK,gDAAiDpL,CAAK,CACrE,CACF,EAOauL,GAAyBA,CAAC,CACrCR,WAAAA,EAAa,UACbD,WAAAA,EAAaF,EACc,EAAI,KAAqC,CAG9D,KAAA,CAACf,EAAW2B,CAAiB,EAAI/L,EAAAA,SAAuB,IAC5DoL,GAAmBC,EAAYC,CAAU,CAC3C,EAKMU,EAAehG,cAAawE,GAAsB,CAClDU,EAAeQ,SAASlB,CAAG,IAC7BuB,EAAkBvB,CAAG,EACrBoB,GAAiBP,EAAYb,CAAG,EAClC,EACC,CAACa,CAAU,CAAC,EAKTY,EAAUjG,EAAAA,YAAY,IAAM,CAE1BkG,MAAAA,GADehB,EAAeiB,QAAQ/B,CAAS,EACnB,GAAKc,EAAetK,OACzCsK,EAAAA,EAAegB,CAAS,CAAC,CAAA,EACrC,CAAC9B,EAAW4B,CAAY,CAAC,EAKtBI,EAAcpG,EAAAA,YAAY,IAAM,CAC9BqG,MAAAA,EAAenB,EAAeiB,QAAQ/B,CAAS,EAC/CkC,EAAgBD,IAAiB,EAAInB,EAAetK,OAAS,EAAIyL,EAAe,EACzEnB,EAAAA,EAAeoB,CAAa,CAAC,CAAA,EACzC,CAAClC,EAAW4B,CAAY,CAAC,EAKtBO,EAAcvG,cAAawE,GACxBJ,IAAcI,EACpB,CAACJ,CAAS,CAAC,EAKRoC,EAAcxG,cAAawE,GACxBU,EAAeiB,QAAQ3B,CAAG,EAChC,CAAE,CAAA,EAGLnE,OAAAA,EAAAA,UAAU,IAAM,CACRoE,MAAAA,EAAiBC,GAAyB,WAE1C+B,GAAAA,IAAAA,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,WACpCF,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,cACpCF,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,UAKxC,KAAKjC,EAAMkC,SAAWlC,EAAMmC,UAAY,CAACnC,EAAMoC,SAC7C,OAAQpC,EAAMC,IAAG,CACf,IAAK,YACHD,EAAME,eAAe,EACTwB,IACZ,MACF,IAAK,aACH1B,EAAME,eAAe,EACbqB,IACR,KACJ,CAIEvB,GAAAA,EAAMC,KAAO,KAAOD,EAAMC,KAAO,KAAO,CAACD,EAAMkC,SAAW,CAAClC,EAAMmC,QAAS,CAC5E,MAAME,EAAWC,SAAStC,EAAMC,GAAG,EAAI,EACnCoC,EAAW7B,EAAetK,SAC5B8J,EAAME,eAAe,EACRM,EAAAA,EAAe6B,CAAQ,CAAC,GAKzC,GAAIrC,EAAMuC,QAAU,CAACvC,EAAMkC,SAAW,CAAClC,EAAMmC,QACnCnC,OAAAA,EAAMC,IAAIuC,YAAa,EAAA,CAC7B,IAAK,IACHxC,EAAME,eAAe,EACrBoB,EAAa,SAAS,EACtB,MACF,IAAK,IACHtB,EAAME,eAAe,EACrBoB,EAAa,QAAQ,EACrB,MACF,IAAK,IACHtB,EAAME,eAAe,EACrBoB,EAAa,QAAQ,EACrB,MACF,IAAK,IACHtB,EAAME,eAAe,EACrBoB,EAAa,WAAW,EACxB,KACJ,EACF,EAGKmB,cAAAA,iBAAiB,UAAW1C,CAAa,EACzC,IAAM2C,OAAOC,oBAAoB,UAAW5C,CAAa,CAC/D,EAAA,CAACwB,EAASG,EAAaJ,CAAY,CAAC,EAEhC,CACL5B,UAAAA,EACA4B,aAAAA,EACAC,QAAAA,EACAG,YAAAA,EACAG,YAAAA,EACAC,YAAAA,EACAc,cAAepC,CAAAA,CAEnB,EC3LMqC,EAAmBhH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,kBAAA,GAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GACnB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAI5C0G,EAAoBjH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,sCAAA,uFAAA,YAAA,6KAAA,qbAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOC,QAIf,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMQ,aAAaN,GACxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAQtB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAM8G,YAAYC,MAAM,EA+BrDC,EAAqBC,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,gGAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,GACnC,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,cACvB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,EAAE,EASzC+F,GAAqBtH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,2BAAA,8GAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUqG,GAEnC,CAAC,CAAEjH,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,WAAW,EAQ5CqG,GAAsBvH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,+CAAA,+GAAA,iBAAA,oBAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,GACnC,CAAC,CAAEnB,MAAAA,EAAOoH,WAAAA,CAAW,IAAOA,EAAapH,EAAMK,OAAOgH,QAAUrH,EAAMK,OAAOzG,MAGxE,CAAC,CAAEoG,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,GAQ9B,CAAC,CAAEiG,WAAAA,CAAW,IAAOA,EAAa,MAAQ,MACrC,CAAC,CAAEpH,MAAAA,CAAM,IAAMA,EAAMC,QAAQe,EAAE,EAM7CsG,GAA0B1H,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0DAAA,yNAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,EAAE,EAgCnCoG,EAA4CA,CAAC,CAAEC,QAAAA,EAAS7N,UAAAA,EAAY,EAAM,IACjFA,EAEA+I,EAAAA,IAACkE,EACE,CAAA,SAAA,CAAC,EAAG,EAAG,EAAG,CAAC,EAAEhL,IACZ6L,GAAAhF,EAAAA,KAACoE,EACC,CAAA,SAAA,CAAAnE,EAAAA,IAACsE,GAAY,SAAY,cAAA,CAAA,EACzBtE,EAAAA,IAAC4E,IAAiB,SAAkB,oBAAA,CAAA,CAAA,GAFrBG,CAGjB,CACD,CACH,CAAA,EAKF/E,MAACkE,GACEY,SAAQ5L,EAAAA,IAAI,CAAC8L,EAAQC,WACnBd,EACC,CAAA,SAAA,CAACnE,EAAAA,IAAAsE,EAAA,CAAaU,WAAO7M,KAAM,CAAA,EAC3B6H,EAAAA,IAACwE,GAAaQ,CAAAA,SAAAA,EAAO5M,KAAM,CAAA,EAC1B4M,EAAOE,SAAWC,eAChBV,GAAa,CAAA,WAAYO,EAAON,WAC9BU,SAAAA,CAAKC,KAAAA,IAAIL,EAAOE,MAAM,EAAE,KAAGF,EAAON,WAAa,OAAS,MAAA,EAC3D,CAAA,GANaO,CAQjB,CACD,CACH,CAAA,ECvJEK,EAAwBpI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,+BAAA,GAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOC,QAC/B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMQ,aAAac,GACxC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,GAC1B,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMiI,QAAQ9G,GAE1B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAG5CgI,EAAoBjB,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUU,GACnC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YACvB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,EAAE,EAGzC6G,GAA0BvI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,4EAAA,GAAA,EAKxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAG9CkH,GAAyBxI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,4EAAA,GAAA,EAKvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAM9CmH,GAA+BA,CAAC,CAAEC,OAAAA,EAAQC,QAAAA,EAASnF,MAAAA,CAAM,IACzDkF,GAAUC,GAAWA,EAAQtO,OAE7BwI,EAAA,KAAC,OACC,MAAO,CACL+F,gBAAiB,UACjBC,QAAS,OACTlI,OAAQ,iBACRC,aAAc,KAGhB,EAAA,SAAA,CAAAkC,MAAC,KAAE,MAAO,CAAEgG,OAAQ,CAAE,EAAI,kBAAStF,GAAQ,CAAA,EAC3CV,MAAC,KAAE,MAAO,CAAEgG,OAAQ,EAAGC,MAAO,sBAAuB,EAClD,wBAAeJ,EAAQ,CAAC,EAAEzN,MAAMC,QAAQ,CAAC,IAC5C,EACA2H,MAAC,KAAE,MAAO,CAAEgG,OAAQ,EAAGC,MAAO,mBAAoB,EAC/C,6BAAoBJ,EAAQ,CAAC,EAAEzN,MAAMC,QAAQ,CAAC,IACjD,CACF,CAAA,CAAA,EAIG,KAQI6N,EAAoDA,CAAC,CAChE3L,KAAAA,EACAtD,UAAAA,EAAY,EACd,IACMA,SAECqO,EACC,CAAA,SAAA,CAAAtF,EAAAA,IAACwF,GAAW,SAAW,aAAA,CAAA,EACvBxF,EAAAA,IAACyF,IAAiB,SAAqB,uBAAA,CAAA,CACzC,CAAA,CAAA,EAIA,CAAClL,GAAQA,EAAKhD,SAAW,SAExB+N,EACC,CAAA,SAAA,CAAAtF,EAAAA,IAACwF,GAAW,SAAW,aAAA,CAAA,EACvBxF,EAAAA,IAAC0F,IAAgB,SAA6B,+BAAA,CAAA,CAChD,CAAA,CAAA,SAKDJ,EACC,CAAA,SAAA,CAAAtF,EAAAA,IAACwF,GAAW,SAAW,aAAA,CAAA,EACvBxF,EAAAA,IAACmG,IAAoB,MAAM,OAAO,OAAO,MACvC,SAAApG,EAAAA,KAACqG,GACC,CAAA,KAAA7L,EACA,OAAQ,CACN8L,IAAK,EACLC,MAAO,GACPC,KAAM,GACNC,OAAQ,CAGV,EAAA,SAAA,CAAAxG,EAAA,IAACyG,GAAc,CAAA,gBAAgB,MAAM,OAAO,2BAA0B,QACrEC,GACC,CAAA,QAAQ,OACR,OAAO,UACP,KAAM,CAAEC,KAAM,SAAA,EAAY,EAE3B3G,EAAAA,IAAA4G,GAAA,CACC,OAAO,UACP,KAAM,CAAED,KAAM,SAAA,EACd,cAA0BvO,GAAA,IAAIA,IAAQ,EAEvC4H,EAAA,IAAA6G,GAAA,CAAQ,QAAS7G,EAAA,IAAC2F,IAAa,CAAA,EAAI,QACnCmB,GAAM,EAAA,EACP9G,EAAAA,IAAC+G,EACC,CAAA,KAAK,WACL,QAAQ,MACR,KAAK,YACL,OAAO,uBACP,UAAW,CAAEC,EAAG,CAAA,EAChB,YAAa,EAAE,EAEjBhH,EAAAA,IAAC+G,EACC,CAAA,KAAK,WACL,QAAQ,aACR,KAAK,iBACL,OAAO,oBACP,YAAa,CAAE,CAAA,CAAA,CAAA,CAEnB,CACF,CAAA,CACF,CAAA,CAAA,EC/HEE,EAAwB/J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,kCAAA,GAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOC,QAC/B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMQ,aAAac,GACxC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,GAC1B,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMiI,QAAQ9G,GAE1B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAG5C0J,EAAoB3C,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUU,GACnC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YACvB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,EAAE,EAGzCuI,GAAeC,EAAAA,MAAKjK,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAIzB,EAAA,CAAA,sDAAA,CAAA,EAEKgK,GAAmBC,EAAAA,MAAKnK,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,GAAA,EACD,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOE,MAAM,EAGzD0J,EAAqBC,EAAAA,GAAErK,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,UAAA,cAAA,oDAAA,uCAAA,IAAA,EAAA,EAEhB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,GAC/B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,cACxB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,GAGlC,CAAC,CAAEgJ,SAAAA,CAAS,IAAOA,EAAW,UAAY,UAIlD,CAAC,CAAEA,SAAAA,EAAUnK,MAAAA,CAAM,IACnBmK,GACA;AAAA;AAAA,eAEWnK,EAAMK,OAAOS;AAAAA;AAAAA;AAAAA,IAKxB,CAAC,CAAEwH,OAAAA,EAAQtI,MAAAA,CAAM,IACjBsI,GACA;AAAA,aACStI,EAAMK,OAAOI;AAAAA;AAAAA,GAEvB,EAGG2J,EAAkBzI,EAAAA,KAAI9B,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8CAAA,aAAA,GAAA,EAKxB,CAAC,CAAEjC,UAAAA,CAAU,IACTA,IAAc,MAAc,gBAC5BA,IAAc,OAAe,gBAC1B,8BAIL,CAAC,CAAEA,UAAAA,CAAU,IACTA,IAAc,MAAc,gBAC5BA,IAAc,OAAe,gBAC1B,eACR,EAICuM,GAAkBC,EAAAA,GAAEzK,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAMzB,EAAA,CAAA,iGAAA,CAAA,EAEKwK,EAAmBC,EAAAA,GAAE3K,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,UAAA,cAAA,GAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,GAC/B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YACxB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,EAAE,EAG1CsJ,GAAgBC,EAAOH,CAAS,EAAC1K,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EAC5B,CAAC,CAAEC,MAAAA,EAAOlC,UAAAA,CAAU,IAC3BA,IAAc,OAASkC,EAAMK,OAAOgH,QAAUrH,EAAMK,OAAOzG,KAAK,EAG9D+Q,GAAaD,EAAOH,CAAS,EAAC1K,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EACzB,CAAC,CAAEC,MAAAA,EAAO3F,IAAAA,CAAI,IAAOA,EAAM2F,EAAMK,OAAOgH,QAAUrH,EAAMK,OAAOzG,KAAM,EAG1EgR,GAAUF,EAAOH,CAAS,EAAC1K,WAAA,CAAAC,YAAA,UAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EACtB,CAAC,CAAEC,MAAAA,EAAOlF,MAAAA,CAAM,IAAOA,GAAS,EAAIkF,EAAMK,OAAOgH,QAAUrH,EAAMK,OAAOzG,KAAM,EAGnFuO,GAA0BvI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAKxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAG9CkH,GAAyBxI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAKvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAM9C2J,GAAqBA,CACzBvR,EACAwR,EACAC,IACG,CACHC,QAAQC,MAAM,gCAAgC,EAGtCC,QAAAA,IAAI,uBAAwB5R,EAAOW,MAAM,EACjD+Q,QAAQE,IAAI,oBAAqB,CAAEJ,OAAAA,EAAQC,UAAAA,CAAAA,CAAW,EAGtDC,QAAQE,IAAI,iCAAiC,EAC7C5R,EAAO6R,MAAM,EAAG,CAAC,EAAEhQ,QAAQ,CAACf,EAAOuN,IAAU,CAE3C,GAAI,CAACvN,GAAS,CAACA,EAAMA,MAAO,CAClB8Q,QAAAA,IAAI,SAASvD,EAAQ,qDAAqD,EAClF,OAGMuD,QAAAA,IAAI,SAASvD,EAAQ,KAAM,CACjCrK,GAAIlD,EAAMA,MAAMkD,GAChBS,OAAQ3D,EAAMA,MAAM2D,OACpBvB,KAAMpC,EAAMA,MAAMoC,KAClBsB,UAAW1D,EAAMA,MAAM0D,UACvBJ,MAAOtD,EAAMA,MAAMiE,YACnBT,KAAMxD,EAAMA,MAAMmE,WAElB,CAACuM,CAAM,EAAI1Q,EAAMA,MAAc0Q,CAAM,CAAA,CACtC,CAAA,CACF,EAEDE,QAAQI,SAAS,CACnB,EAOaC,EAAsDA,CAAC,CAClE/R,OAAAA,EACAK,UAAAA,EAAY,EACd,IAAM,CAEJqR,QAAQE,IAAI,4DAA4D,EACxE,KAAM,CAACI,EAAWC,CAAY,EAAIlS,WAAoB,MAAM,EACtD,CAACmS,EAAeC,CAAgB,EAAIpS,WAAwB,MAAM,EAGxEqS,GAAMhM,UAAU,IAAM,CAUhBpG,GATJ0R,QAAQE,IAAI,gDAAgD,EAC5DF,QAAQE,IAAI,qBAAsB,CAChCS,aAAarS,GAAAA,YAAAA,EAAQW,SAAU,EAC/BN,UAAAA,EACAiS,WAAY,OAAOtS,EACnBuS,cAAepQ,MAAMqQ,QAAQxS,CAAM,EACnCyS,YAAazS,CAAAA,CACd,EAEGA,GAAUA,EAAOW,OAAS,EAAG,CAEzB+R,MAAAA,EAAa1S,EAAO,CAAC,EACrB2S,EAAY3S,EAAOA,EAAOW,OAAS,CAAC,EAEtC+R,GAAcA,EAAW5R,MAC3B4Q,QAAQE,IAAI,wCAAyC,CACnDgB,MAAO5S,EAAOW,OACd+R,WAAY,CACV1O,GAAI0O,EAAW5R,MAAMkD,GACrBd,KAAMwP,EAAW5R,MAAMoC,KACvBuB,OAAQiO,EAAW5R,MAAM2D,OAEzB3C,MAAO4Q,EAAW5R,MAAMgB,MACxBgB,QAAS4P,EAAW5R,MAAMgC,OAC5B,EACA6P,UACEA,GAAaA,EAAU7R,MACnB,CACEkD,GAAI2O,EAAU7R,MAAMkD,GACpBd,KAAMyP,EAAU7R,MAAMoC,KACtBuB,OAAQkO,EAAU7R,MAAM2D,MAAAA,EAE1B,oBAAA,CACP,GAEDiN,QAAQE,IAAI,qEAAqE,EACzEA,QAAAA,IAAI,kBAAmBc,CAAU,QAG3ChB,QAAQE,IAAI,qDAAqD,EACzDA,QAAAA,IAAI,mBAAoB5R,CAAM,EAC9B4R,QAAAA,IAAI,iBAAkBvR,CAAS,CACzC,EACC,CAACL,EAAQK,CAAS,CAAC,EAGhBwS,MAAAA,EAAa9M,cAChB+M,GAAqB,CAGpB,GAFQlB,QAAAA,IAAI,iBAAiBkB,GAAO,EAEhCd,IAAcc,EAAO,CAEjBC,MAAAA,EAAeb,IAAkB,MAAQ,OAAS,MACxDC,EAAiBY,CAAY,EACrBnB,QAAAA,IAAI,2BAA2BmB,GAAc,OAGrDd,EAAaa,CAAK,EAClBX,EAAiB,MAAM,EACfP,QAAAA,IAAI,gBAAgBkB,uBAA2B,CACzD,EAEF,CAACd,EAAWE,CAAa,CAC3B,EAGMc,EAAeC,EAAAA,QAAQ,IAAM,CAC7B,GAAA,CAACjT,GAAUA,EAAOW,SAAW,EAAG,MAAO,GAG3C,MAAMuS,EAAclT,EAAOa,OAAkBC,GAAAA,GAASA,EAAMA,KAAK,EAE7DoS,GAAAA,EAAYvS,SAAW,EACzB+Q,eAAQhG,KAAK,0CAA0C,EAChD,GAGUwH,GAAAA,EAAalB,EAAWE,CAAa,EAElDiB,MAAAA,EAAS,CAAC,GAAGD,CAAW,EAAEzQ,KAAK,CAACC,EAAGC,IAAM,SAEzC,GAAA,CAACD,GAAK,CAACA,EAAE5B,OAAS,CAAC6B,GAAK,CAACA,EAAE7B,MAC7B4Q,eAAQhG,KAAK,wCAAwC,EAC9C,EAGT,IAAI0H,EAAaC,EAGjB,OAAQrB,EAAS,CACf,IAAK,OACHoB,EAAS,IAAIjQ,KAAKT,EAAE5B,MAAMoC,MAAQ,EAAE,EACpCmQ,EAAS,IAAIlQ,KAAKR,EAAE7B,MAAMoC,MAAQ,EAAE,EACpC,MACF,IAAK,QACOR,EAAAA,EAAE5B,MAAcgB,OAAS,GACzBa,EAAAA,EAAE7B,MAAcgB,OAAS,GACnC,MACF,IAAK,UACOY,EAAAA,EAAE5B,MAAcgC,SAAW,GAC3BH,EAAAA,EAAE7B,MAAcgC,SAAW,GACrC,MACF,IAAK,YACHsQ,IAAS1Q,EAAAA,EAAE5B,MAAM0D,YAAR9B,YAAAA,EAAmBuK,gBAAiB,GAC7CoG,IAAS1Q,EAAAA,EAAE7B,MAAM0D,YAAR7B,YAAAA,EAAmBsK,gBAAiB,GAC7C,MACF,IAAK,SACOvK,EAAAA,EAAE5B,MAAc2D,QAAU,GAC1B9B,EAAAA,EAAE7B,MAAc2D,QAAU,GACpC,MACF,IAAK,QACH2O,EAASE,WAAWC,OAAO7Q,EAAE5B,MAAMiE,aAAe,CAAC,CAAC,EACpDsO,EAASC,WAAWC,OAAO5Q,EAAE7B,MAAMiE,aAAe,CAAC,CAAC,EACpD,MACF,IAAK,OACHqO,EAASE,WAAWC,OAAO7Q,EAAE5B,MAAMmE,YAAc,CAAC,CAAC,EACnDoO,EAASC,WAAWC,OAAO5Q,EAAE7B,MAAMmE,YAAc,CAAC,CAAC,EACnD,MACF,IAAK,YACHmO,EAASE,WAAWC,OAAO7Q,EAAE5B,MAAM4D,YAAc,CAAC,CAAC,EACnD2O,EAASC,WAAWC,OAAO5Q,EAAE7B,MAAM4D,YAAc,CAAC,CAAC,EACnD,MACF,IAAK,MACH0O,EAASE,WAAWC,OAAO7Q,EAAE5B,MAAMsE,aAAe,CAAC,CAAC,EACpDiO,EAASC,WAAWC,OAAO5Q,EAAE7B,MAAMsE,aAAe,CAAC,CAAC,EACpD,MACF,QACY1C,EAAAA,EAAE5B,MAAckR,CAAS,EACzBrP,EAAAA,EAAE7B,MAAckR,CAAS,CACvC,CAKIoB,GAHJ1B,QAAQE,IAAI,iBAAiBwB,QAAaC,MAAWrB,IAAY,EAG7DoB,aAAkBjQ,MAAQkQ,aAAkBlQ,KAAM,CACpD,MAAMqQ,EAAaJ,EAAO3P,QAAQ,EAAI4P,EAAO5P,QAAQ,EAC9CyO,OAAAA,IAAkB,MAAQsB,EAAa,CAACA,EAGjD,GAAI,OAAOJ,GAAW,UAAY,OAAOC,GAAW,SAAU,CAC5D,MAAMG,EAAaJ,EAASC,EACrBnB,OAAAA,IAAkB,MAAQsB,EAAa,CAACA,EAIjD,MAAMA,EAAaD,OAAOH,CAAM,EAAEK,cAAcF,OAAOF,CAAM,CAAC,EACvDnB,OAAAA,IAAkB,MAAQsB,EAAa,CAACA,CAAAA,CAChD,EAGD9B,eAAQE,IAAI,4BAA4B,EACxCuB,EAAOtB,MAAM,EAAG,CAAC,EAAEhQ,QAAQ,CAACf,EAAOuN,IAAU,CACvCvN,GAASA,EAAMA,MACT8Q,QAAAA,IAAI,UAAUvD,EAAQ,KAAM,CAClCrK,GAAIlD,EAAMA,MAAMkD,GAChBd,KAAMpC,EAAMA,MAAMoC,KAClB,CAAC8O,CAAS,EAAIlR,EAAMA,MAAckR,CAAS,CAAA,CAC5C,EAEOJ,QAAAA,IAAI,UAAUvD,EAAQ,kBAAkB,CAClD,CACD,EAEM8E,CACN,EAAA,CAACnT,EAAQgS,EAAWE,CAAa,CAAC,EACrC,OAAI7R,SAECgQ,EACC,CAAA,SAAA,CAAAjH,EAAAA,IAACkH,GAAW,SAAa,eAAA,CAAA,EACzBlH,EAAAA,IAACyF,IAAiB,SAAsB,wBAAA,CAAA,CAC1C,CAAA,CAAA,EAIA,CAAC7O,GAAUA,EAAOW,SAAW,SAE5B0P,EACC,CAAA,SAAA,CAAAjH,EAAAA,IAACkH,GAAW,SAAa,eAAA,CAAA,EACzBlH,EAAAA,IAAC0F,IAAgB,SAAwB,0BAAA,CAAA,CAC3C,CAAA,CAAA,SAKDuB,EACC,CAAA,SAAA,CAAAlH,OAACmH,EAAW,CAAA,SAAA,CAAA,kBAAgB0C,EAAarS,OAAO,GAAA,EAAC,SAChD4P,GACC,CAAA,SAAA,CAACnH,EAAA,IAAAqH,GAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAACtH,EAAAA,KAAAwH,EAAA,CAAY,SAAQ,GAAC,OAAQqB,IAAc,OAAQ,QAAS,IAAMa,EAAW,MAAM,EAAE,SAAA,CAAA,aAEnF/B,EAAS,CAAA,UAAWkB,IAAc,OAASE,EAAgB3D,OAAU,CAAA,EACxE,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,QACtB,QAAS,IAAMa,EAAW,OAAO,EAAE,SAAA,CAAA,cAGlC/B,EAAS,CAAA,UAAWkB,IAAc,QAAUE,EAAgB3D,OAAU,CAAA,EACzE,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,UACtB,QAAS,IAAMa,EAAW,SAAS,EAAE,SAAA,CAAA,gBAGpC/B,EAAS,CAAA,UAAWkB,IAAc,UAAYE,EAAgB3D,OAAU,CAAA,EAC3E,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,YACtB,QAAS,IAAMa,EAAW,WAAW,EAAE,SAAA,CAAA,kBAGtC/B,EAAS,CAAA,UAAWkB,IAAc,YAAcE,EAAgB3D,OAAU,CAAA,EAC7E,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,SACtB,QAAS,IAAMa,EAAW,QAAQ,EAAE,SAAA,CAAA,eAGnC/B,EAAS,CAAA,UAAWkB,IAAc,SAAWE,EAAgB3D,OAAU,CAAA,EAC1E,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,QACtB,QAAS,IAAMa,EAAW,OAAO,EAAE,SAAA,CAAA,cAGlC/B,EAAS,CAAA,UAAWkB,IAAc,QAAUE,EAAgB3D,OAAU,CAAA,EACzE,EACApF,EAAAA,KAACwH,EAAY,CAAA,SAAQ,GAAC,OAAQqB,IAAc,OAAQ,QAAS,IAAMa,EAAW,MAAM,EAAE,SAAA,CAAA,aAEnF/B,EAAS,CAAA,UAAWkB,IAAc,OAASE,EAAgB3D,OAAU,CAAA,EACxE,EACApF,EAAAA,KAACwH,EACC,CAAA,SAAQ,GACR,OAAQqB,IAAc,YACtB,QAAS,IAAMa,EAAW,WAAW,EAAE,SAAA,CAAA,mBAGtC/B,EAAS,CAAA,UAAWkB,IAAc,YAAcE,EAAgB3D,OAAU,CAAA,EAC7E,EACApF,EAAAA,KAACwH,EAAY,CAAA,SAAQ,GAAC,OAAQqB,IAAc,MAAO,QAAS,IAAMa,EAAW,KAAK,EAAE,SAAA,CAAA,YAEjF/B,EAAS,CAAA,UAAWkB,IAAc,MAAQE,EAAgB3D,OAAU,CAAA,EACvE,CAAA,CAAA,CACF,CACF,CAAA,QACC,QACEyE,CAAAA,SAAAA,EAAa1Q,IAAI,CAACxB,EAAOuN,IAEpB,CAACvN,GAAS,CAACA,EAAMA,OACX4K,QAAAA,KAAK,sCAAsC2C,GAAO,EACnD,aAIN0C,GACC,CAAA,SAAA,CAAA3H,EAAA,IAAC6H,EAAWnQ,CAAAA,SAAAA,EAAMA,MAAMoC,MAAQ,MAAM,EACrCkG,EAAA,IAAA6H,EAAA,CAAWnQ,SAAMA,EAAAA,MAAMgB,OAAS,MAAM,EACtCsH,EAAA,IAAA6H,EAAA,CAAWnQ,SAAMA,EAAAA,MAAMgC,SAAW,MAAM,EACzCsG,EAAAA,IAAC+H,IAAc,UAAWrQ,EAAMA,MAAM0D,UACnC1D,SAAAA,EAAMA,MAAM0D,WAAa,KAC5B,CAAA,EACC4E,EAAA,IAAA6H,EAAA,CAAWnQ,SAAMA,EAAAA,MAAM2D,QAAU,MAAM,EACvC2E,EAAA,IAAA6H,EAAA,CAAWnQ,SAAMA,EAAAA,MAAMiE,aAAe,MAAM,EAC5CqE,EAAA,IAAA6H,EAAA,CAAWnQ,SAAMA,EAAAA,MAAMmE,YAAc,MAAM,EAC3CmE,EAAA,IAAAiI,GAAA,CAAW,IAAKvQ,EAAMA,MAAM+D,WAAa,MACtC/D,UAAAA,EAAMA,MAAM4D,YAAc,GAAGjD,QAAQ,CAAC,EAC1C,SACC6P,GAAQ,CAAA,MAAOxQ,EAAMA,MAAMsE,aAAe,EAAE,SAAA,CAAA,KACxCtE,EAAMA,MAAMsE,aAAe,GAAG3D,QAAQ,CAAC,CAAA,EAC5C,CAAA,CAAA,EAfaX,EAAMA,MAAMkD,IAAM,SAASqK,GAgB1C,CAEH,EACH,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EC/cMf,EAAmBhH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kDAAA,kBAAA,sBAAA,+BAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GACnB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAEzB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMgN,YAAY1L,EAAE,EAKpD2L,EAAsBrN,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACT,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOC,QAC/B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMQ,aAAac,GACxC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,GAC1B,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMiI,QAAQ9G,EAAE,EAGzC+L,EAAmBjG,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,KAAA,EACZ,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMY,UAAUU,GACnC,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YACvB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQqB,EAAE,EAGzCuI,EAAeC,EAAAA,MAAKjK,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAGzB,EAAA,CAAA,sCAAA,CAAA,EAEKgK,EAAmBC,EAAAA,MAAKnK,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,GAAA,EACD,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOE,MAAM,EAGzD0J,EAAqBC,EAAAA,GAAErK,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,UAAA,cAAA,4CAAA,EAEhB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,GAC/B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,cACxB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,EAAE,EAK1CkJ,EAAkBC,EAAAA,GAAEzK,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,oDAAA,EACG,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOE,MAAM,EAOzDgK,EAAmBC,EAAAA,GAAE3K,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,UAAA,cAAA,GAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQkB,GAC/B,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YACxB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMY,UAAUO,EAAE,EAG1CgM,EAAczC,EAAOH,CAAS,EAAC1K,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EAC1B,CAAC,CAAEC,MAAAA,EAAOlF,MAAAA,CAAM,IACnBA,GAAS,GAAWkF,EAAMK,OAAOgH,QACjCvM,GAAS,GAAWkF,EAAMK,OAAO+M,QAC9BpN,EAAMK,OAAOzG,KACrB,EAGGgR,EAAUF,EAAOH,CAAS,EAAC1K,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,GAAA,EACtB,CAAC,CAAEC,MAAAA,EAAOlF,MAAAA,CAAM,IAAOA,GAAS,EAAIkF,EAAMK,OAAOgH,QAAUrH,EAAMK,OAAOzG,KAAM,EAGnFuO,EAA0BvI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAKxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAG9CkH,EAAyBxI,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAKvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMK,OAAOa,aAAa,EAQvCmM,EAA8CA,CAAC,CAC1D5T,iBAAAA,EACAC,mBAAAA,EACAC,UAAAA,EAAY,EACd,IACMA,SAECiN,EACC,CAAA,SAAA,CAAAnE,OAACwK,EACC,CAAA,SAAA,CAAAvK,EAAAA,IAACwK,GAAU,SAAiB,mBAAA,CAAA,EAC5BxK,EAAAA,IAACyF,GAAiB,SAAqB,uBAAA,CAAA,CAAA,EACzC,SACC8E,EACC,CAAA,SAAA,CAAAvK,EAAAA,IAACwK,GAAU,SAAmB,qBAAA,CAAA,EAC9BxK,EAAAA,IAACyF,GAAiB,SAAuB,yBAAA,CAAA,CAAA,EAC3C,CACF,CAAA,CAAA,SAKDvB,EACC,CAAA,SAAA,CAAAnE,OAACwK,EACC,CAAA,SAAA,CAAAvK,EAAAA,IAACwK,GAAU,SAAiB,mBAAA,CAAA,EAC3BzT,EAAiBQ,SAAW,EAC3ByI,EAAAA,IAAC0F,GAAgB,SAAuB,yBAAA,CAAA,SAEvCyB,EACC,CAAA,SAAA,CAACnH,EAAA,IAAAqH,EAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAArH,EAAAA,IAACuH,GAAY,SAAK,OAAA,CAAA,EAClBvH,EAAAA,IAACuH,GAAY,SAAQ,UAAA,CAAA,EACrBvH,EAAAA,IAACuH,GAAY,SAAK,OAAA,CAAA,EAClBvH,EAAAA,IAACuH,GAAY,SAAM,QAAA,CAAA,EACnBvH,EAAAA,IAACuH,GAAY,SAAG,KAAA,CAAA,CAAA,CAAA,CAClB,CACF,CAAA,EACAvH,EAAAA,IAAC,SACEjJ,SAAiBmC,EAAAA,IAAI,CAACR,EAAOuM,WAC3B0C,EACC,CAAA,SAAA,CAAC3H,EAAAA,IAAA6H,EAAA,CAAWnP,WAAMS,IAAK,CAAA,EACtB4G,EAAA,KAAA0K,EAAA,CAAY,MAAO/R,EAAMd,QAAUc,SAAAA,CAAMd,EAAAA,QAAQS,QAAQ,CAAC,EAAE,GAAA,EAAC,QAC7DwP,EAAWnP,CAAAA,SAAAA,EAAMT,aAAaI,QAAQ,CAAC,EAAE,EAC1C2H,EAAAA,IAAC6H,EAAWnP,CAAAA,SAAAA,EAAMpB,WAAY,CAAA,EAC7ByI,EAAA,KAAAmI,EAAA,CAAQ,MAAOxP,EAAMV,IAAK,SAAA,CAAA,IAAEU,EAAMV,IAAIK,QAAQ,CAAC,CAAA,EAAE,CAAA,GALrC4M,CAMf,CACD,EACH,CAAA,EACF,CAAA,EAEJ,SAECsF,EACC,CAAA,SAAA,CAAAvK,EAAAA,IAACwK,GAAU,SAAmB,qBAAA,CAAA,EAC7BxT,EAAmBO,SAAW,EAC7ByI,EAAAA,IAAC0F,GAAgB,SAAyB,2BAAA,CAAA,SAEzCyB,EACC,CAAA,SAAA,CAACnH,EAAA,IAAAqH,EAAA,CACC,gBAAC,KACC,CAAA,SAAA,CAAArH,EAAAA,IAACuH,GAAY,SAAO,SAAA,CAAA,EACpBvH,EAAAA,IAACuH,GAAY,SAAQ,UAAA,CAAA,EACrBvH,EAAAA,IAACuH,GAAY,SAAK,OAAA,CAAA,EAClBvH,EAAAA,IAACuH,GAAY,SAAM,QAAA,CAAA,EACnBvH,EAAAA,IAACuH,GAAY,SAAG,KAAA,CAAA,CAAA,CAAA,CAClB,CACF,CAAA,EACAvH,EAAAA,IAAC,SACEhJ,SAAmBkC,EAAAA,IAAI,CAACQ,EAASuL,WAC/B0C,EACC,CAAA,SAAA,CAAC3H,EAAAA,IAAA6H,EAAA,CAAWnO,WAAQP,IAAK,CAAA,EACxB4G,EAAA,KAAA0K,EAAA,CAAY,MAAO/Q,EAAQ9B,QAAU8B,SAAAA,CAAQ9B,EAAAA,QAAQS,QAAQ,CAAC,EAAE,GAAA,EAAC,QACjEwP,EAAWnO,CAAAA,SAAAA,EAAQzB,aAAaI,QAAQ,CAAC,EAAE,EAC5C2H,EAAAA,IAAC6H,EAAWnO,CAAAA,SAAAA,EAAQpC,WAAY,CAAA,EAC/ByI,EAAA,KAAAmI,EAAA,CAAQ,MAAOxO,EAAQ1B,IAAK,SAAA,CAAA,IAAE0B,EAAQ1B,IAAIK,QAAQ,CAAC,CAAA,EAAE,CAAA,GALzC4M,CAMf,CACD,EACH,CAAA,EACF,CAAA,EAEJ,CACF,CAAA,CAAA,ECnIE2F,GAA4B1N,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,sBAAA,+BAAA,EAG5B,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QAEtB,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMgN,cAANhN,YAAAA,EAAmBE,KAAM,SAAQ,EAKjEqN,GAAuB3N,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAG7CsN,GAA0B5N,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,kBAAA,YAAA,qBAAA,sBAAA,EACnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBACrC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAC/C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC3B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAI9EkN,GAAmBxG,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,YAAA,cAAA,mBAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QACzC,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WACxC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,WAAU,EAOzDwN,GAAwDA,CAAC,CAAEzQ,KAAAA,EAAMtD,UAAAA,CAAU,IAAM,SAGrF,MAAMgU,IACJ1Q,GAAAA,EAAAA,EAAKpD,oBAALoD,YAAAA,EACIlB,KAAK,CAACC,EAAGC,IAAM,IAAIQ,KAAKR,EAAE7B,MAAMoC,IAAI,EAAEO,QAAY,EAAA,IAAIN,KAAKT,EAAE5B,MAAMoC,IAAI,EAAEO,QAAS,KADtFE,YAAAA,EAEIkO,MAAM,EAAG,KAAM,CAAA,EAErB,OAEI1I,EAAA,KAAAmL,WAAA,CAAA,SAAA,CAAAlL,EAAA,IAAC6E,EAAa,CAAA,QAAStK,EAAK1D,mBAAoB,UAAAI,EAAqB,EACpE+I,EAAA,IAAAkG,EAAA,CAAiB,KAAM3L,EAAKzD,UAAW,UAAAG,EAAqB,EAC5D+I,EAAAA,IAAA2I,EAAA,CAAkB,OAAQsC,EAAc,UAAAhU,CAAqB,CAAA,CAChE,CAAA,CAAA,CAEJ,EAKMkU,GAAuDA,CAAC,CAAE5Q,KAAAA,EAAMtD,UAAAA,CAAU,IAAM,OAG9E2S,MAAAA,IACJrP,EAAAA,EAAKpD,oBAALoD,YAAAA,EAAwBlB,KACtB,CAACC,EAAGC,IAAM,IAAIQ,KAAKR,EAAE7B,MAAMoC,IAAI,EAAEO,QAAQ,EAAI,IAAIN,KAAKT,EAAE5B,MAAMoC,IAAI,EAAEO,aACjE,GAEP,OAAQ2F,EAAAA,IAAA2I,EAAA,CAAkB,OAAQiB,EAAc,UAAA3S,CAAwB,CAAA,CAC1E,EAKMmU,GAAuDA,CAAC,CAAE7Q,KAAAA,EAAMtD,UAAAA,CAAU,IAE5E+I,MAAC2K,GACC,iBAAkBpQ,EAAKxD,iBACvB,mBAAoBwD,EAAKvD,mBACzB,UAAAC,CACA,CAAA,EAOAoU,GAA0DA,CAAC,CAC/D9Q,KAAAA,EACAtD,UAAAA,EACAqU,gBAAAA,EACAC,sBAAAA,CACF,WAEKX,GACC,CAAA,SAAA,CAAA7K,OAAC8K,GACC,CAAA,SAAA,CAAA7K,EAAA,IAAC6E,EAAa,CAAA,QAAStK,EAAK1D,mBAAoB,UAAAI,EAAqB,EACpE+I,EAAA,IAAAkG,EAAA,CAAiB,KAAM3L,EAAKzD,UAAW,UAAAG,EAAqB,EAC7D+I,MAAC2K,GACC,iBAAkBpQ,EAAKxD,iBACvB,mBAAoBwD,EAAKvD,mBACzB,UAAAC,EAAqB,CAAA,EAEzB,SAEC6T,GACC,CAAA,SAAA,CAAA9K,EAAAA,IAAC+K,IAAU,SAAqB,uBAAA,CAAA,EAC/BO,GAAmBC,GACjBvL,EAAAA,IAAAwL,GAAA,CACC,WAAYF,EACZ,aACE1J,GAEA2J,EAAsB3J,CAA4D,EAEpF,iBAAkB,CAErB,CAAA,CAAA,CAAA,EACH,CACF,CAAA,CAAA,EAOS6J,GAAiE,CAC5EjL,QAAS,CACP5F,GAAI,UACJzC,MAAO,sBACPwI,YAAa,kDACbF,KAAM,KACNiL,UAAWV,GACXW,aAAc,GACdC,aAAc,EAChB,EACAhV,OAAQ,CACNgE,GAAI,SACJzC,MAAO,gBACPwI,YAAa,2CACbF,KAAM,KACNiL,UAAWP,GACXQ,aAAc,GACdC,aAAc,EAChB,EACAhL,OAAQ,CACNhG,GAAI,SACJzC,MAAO,iBACPwI,YAAa,uDACbF,KAAM,KACNiL,UAAWN,GACXO,aAAc,GACdC,aAAc,EAChB,EACA/K,UAAW,CACTjG,GAAI,YACJzC,MAAO,qBACPwI,YAAa,iDACbF,KAAM,KACNiL,UAAWL,GACXM,aAAc,GACdC,aAAc,EAChB,CACF,EAKaC,GAAgBC,GACpBL,GAAqBK,CAAK,EA2BtBC,GAA2EC,GAAA,CAChF,KAAA,CAAEjL,UAAAA,CAAciL,EAAAA,EAChBtK,EAASmK,GAAa9K,CAAS,EAErC,GAAI,CAACW,EAED,OAAA3B,EAAA,KAAC,OACC,MAAO,CACLgG,QAAS,OACTkG,UAAW,SACXhG,MAAO,oBACP,EAAA,SAAA,CAAA,kBAEclF,CAClB,CAAA,CAAA,EAIJ,MAAMmL,EAAexK,EAAOgK,UAE5B,OACG1L,EAAA,IAAA,MAAA,CACC,GAAI,mBAAmBe,IACvB,KAAK,WACL,kBAAiB,iBAAiBA,IAElC,SAAAf,EAAAA,IAACkM,EAAa,CAAA,GAAIF,EAAM,CAC1B,CAAA,CAEJ,ECrPM9H,GAAmBhH,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,UAAA,6BAAA,kCAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC7B,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAc6O,aAAc,qBAChD,CAAC,CAAE7O,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAE1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAKjD4O,GAAqBlP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,UAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAI7C6O,GAA6BnP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAarC,EAAA,CAAA,wIAAA,CAAA,EAEKiP,GAAsBpP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,sCAAA,EAKlB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAAM,EAKjD8O,GAAqBrP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,mGAAA,EAEX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,OAAM,EAevD4N,GAAqBC,EAAAA,EAACtP,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,YAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,gBAAiB,wBAAuB,EAI1EkO,GAAoBxP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,uBAAA,oBAAA,WAAA,KAAA,EAKhB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QAGjC,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcpG,QAAS,sBAChC,CAAC,CAAEoG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcpG,QAAS,sBACzC,CAAC,CAAEoG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAChD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAGhDmP,GAAmBzP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,UAAA,GAAA,EAET,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAC5C,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcpG,QAAS,qBAAoB,EAG/D0V,GAAoBrI,EAAAA,GAAEpH,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcpG,QAAS,sBACjC,CAAC,CAAEoG,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,MAAK,EAGnDoO,GAAsBJ,EAAAA,EAACtP,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBmB,KAAM,YAC1C,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,gBAAiB,wBAAuB,EAK1EsO,GAAqB1N,EAAAA,OAAMjC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,eAAA,0CAAA,+EAAA,+BAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QACvC,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAemB,KAAM,OAC3C,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAesB,KAAM,QAC1B,CAAC,CAAEtB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBAGrC,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBsB,KAAM,OAM1C,CAAC,CAAEtB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAciC,cAAe,sBAAqB,EAK7EwN,GAAwB7P,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gJAAA,GAAA,EAUpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAM0P,SAAN1P,YAAAA,EAAc2P,QAAS,IAAI,EAGjDC,GAAwBhQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+DAAA,yJAAA,EAEP,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAmBlFoP,EAA4BA,IAChCpN,EAAAA,KAACuM,GACC,CAAA,SAAA,CAAAtM,EAAAA,IAACuM,IAAY,SAAG,KAAA,CAAA,EAChBvM,EAAAA,IAACwM,IAAY,SAA4B,8BAAA,CAAA,CAAA,CAC3C,CAAA,EAMIY,GAAkEA,CAAC,CAAElW,MAAAA,EAAOmW,QAAAA,CAAQ,WACvFX,GACC,CAAA,SAAA,CAAA1M,EAAAA,IAAC2M,IAAU,SAAE,IAAA,CAAA,EACb3M,EAAAA,IAAC4M,IAAW,SAAe,iBAAA,CAAA,EAC3B5M,EAAAA,IAAC6M,IAAc3V,SAAMA,CAAA,CAAA,EACpB8I,EAAA,IAAA8M,GAAA,CAAY,QAASO,EAAS,SAAS,YAAA,CAAA,CAC1C,CAAA,EAMIC,GAAwDA,CAAC,CAAEC,WAAAA,CAAW,IAAM,CAC1E,KAAA,CACJ3W,OAAAA,EACAC,mBAAAA,EACAC,UAAAA,EACAC,iBAAAA,EACAC,mBAAAA,EACAC,UAAAA,EACAC,MAAAA,EACAwF,mBAAAA,EACAvF,kBAAAA,GACEX,GAAoB,EAElB,CAAEuK,UAAAA,EAAW4B,aAAAA,GAAiBF,GAAuB,CACzDR,WAAYsL,GAAc,SAAA,CAC3B,EAGK,CAACjC,EAAiBkC,CAAkB,EAAI7W,WAAwB,CACpEmD,SAAUC,OAAO0T,YAAcC,EAAAA,MAAM,GAAG,EAAE,CAAC,EAC3CC,OAAQ,MACRvS,UAAW,OACXwS,SAAU,IACVlS,WAAY,IACZE,UAAW,IACXiS,OAAQ,IACR/S,MAAO,GACPpB,QAAS,GACThB,MAAO,GACP6C,eAAgB,GAChBU,UAAW,GACXE,OAAQ,GACRK,gBAAiB,GACjBH,aAAc,GACdyR,MAAO,GACPC,KAAM,CAAE,EACRC,OAAQ,KAAA,CACT,EAWKC,EAAkB,CACtBlN,UAAAA,EACAxG,KAAM,CACJ3D,OAAAA,EACAC,mBAAAA,EACAC,UAAAA,EACAC,iBAAAA,EACAC,mBAAAA,EACAG,kBAAAA,CACF,EACAF,UAAAA,EACAC,MAAAA,EACAoU,gBAAAA,EACAC,sBAtB6B3J,GAA+D,CACtF,KAAA,CAAEzI,KAAAA,EAAMf,MAAAA,EAAO8V,KAAAA,CAAAA,EAAStM,EAAEuM,OAChCX,EAA8B5Q,IAAA,CAC5B,GAAGA,EACH,CAACzD,CAAI,EAAuBf,CAC5B,EAAA,CAAA,CAiBFmT,EAGF,OAAIrU,EACM8I,EAAAA,IAAAoN,GAAA,CAAc,MAAAlW,EAAc,QAASwF,CAAsB,CAAA,SAIlEwH,GAEC,CAAA,SAAA,CAAAlE,MAACP,IACC,UAAAxI,EACA,cAAe,EACf,cAAe,GACf,UAAWyF,EAAmB,QAI/BoE,GAAgB,CAAA,UAAAC,EAAsB,YAAa4B,EAAc,SAAU1L,EAAU,QAGrFmV,GACC,CAAA,SAAApM,MAACqM,GACC,CAAA,SAAArM,EAAA,IAACoO,YAAS,SAAUpO,EAAAA,IAACmN,EAAe,EAAA,EAClC,eAACpB,GAA4B,CAAA,GAAIkC,EAAgB,CAAA,CACnD,CACF,CAAA,EACF,EAGChX,GACC+I,EAAAA,IAAC+M,GACC,CAAA,SAAA/M,EAAAA,IAACkN,IAAc,CAAA,EACjB,CAEJ,CAAA,CAAA,CAEJ,EAWamB,GAAuErC,GAEhFhM,EAAAA,IAACoO,EAAAA,SAAS,CAAA,SAAWpO,EAAA,IAAAmN,EAAA,CAAe,CAAA,EAClC,SAACnN,EAAAA,IAAAsN,GAAA,CAAiB,GAAItB,CAAM,CAAA,CAC9B,CAAA,EChSSsC,GAAoDA,CAAC,CAAE5O,UAAAA,EAAW6N,WAAAA,CAAW,IACjFvN,EAAA,IAACqO,GAAqB,CAAA,UAAA3O,EAAsB,WAAA6N,CAA0B,CAAA"}