var _e=Object.defineProperty;var We=(e,t,n)=>t in e?_e(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ne=(e,t,n)=>(We(e,typeof t!="symbol"?t+"":t,n),n);import{j as r}from"./client-d6fc67cc.js";import{r as C,R as we}from"./react-25c2faed.js";import{t as Oe}from"./tradeStorage-a5c0ed9a.js";import{C as u,s as c,U as te}from"./styled-components-00fe3932.js";import{C as ve}from"./Card-1e58b487.js";const Be={small:u(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:u(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:u(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},qe=(e,t,n=!1)=>u(["",""],({theme:o})=>{let s,a,i;switch(e){case"primary":s=t?o.colors.primary:`${o.colors.primary}20`,a=t?o.colors.textInverse:o.colors.primary,i=o.colors.primary;break;case"secondary":s=t?o.colors.secondary:`${o.colors.secondary}20`,a=t?o.colors.textInverse:o.colors.secondary,i=o.colors.secondary;break;case"success":s=t?o.colors.success:`${o.colors.success}20`,a=t?o.colors.textInverse:o.colors.success,i=o.colors.success;break;case"warning":s=t?o.colors.warning:`${o.colors.warning}20`,a=t?o.colors.textInverse:o.colors.warning,i=o.colors.warning;break;case"error":s=t?o.colors.error:`${o.colors.error}20`,a=t?o.colors.textInverse:o.colors.error,i=o.colors.error;break;case"info":s=t?o.colors.info:`${o.colors.info}20`,a=t?o.colors.textInverse:o.colors.info,i=o.colors.info;break;case"neutral":s=t?o.colors.textSecondary:`${o.colors.textSecondary}10`,a=t?o.colors.textInverse:o.colors.textSecondary,i=o.colors.textSecondary;break;default:s=t?o.colors.textSecondary:`${o.colors.textSecondary}20`,a=t?o.colors.textInverse:o.colors.textSecondary,i=o.colors.textSecondary}return n?`
          background-color: transparent;
          color: ${i};
          border: 1px solid ${i};
        `:`
        background-color: ${s};
        color: ${a};
        border: 1px solid transparent;
      `}),Ce=c.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),He=c(Ce).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),Ue=c(Ce).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),Ve=c.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:t,dot:n})=>n?"50%":t?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>Be[e],({variant:e,solid:t,outlined:n})=>qe(e,t,n||!1),({dot:e})=>e&&u(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&u(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&u(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),ae=({children:e,variant:t="default",size:n="medium",solid:o=!1,className:s="",style:a,onClick:i,rounded:l=!1,dot:d=!1,counter:p=!1,outlined:m=!1,startIcon:g,endIcon:y,max:w,inline:h=!0})=>{let v=e;return p&&typeof e=="number"&&w!==void 0&&e>w&&(v=`${w}+`),r.jsx(Ve,{variant:t,size:n,solid:o,clickable:!!i,className:s,style:a,onClick:i,rounded:l,dot:d,counter:p,outlined:m,inline:h,children:!d&&r.jsxs(r.Fragment,{children:[g&&r.jsx(He,{children:g}),v,y&&r.jsx(Ue,{children:y})]})})},Ye=te(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Ge=c.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],Ye,({theme:e})=>e.spacing.xs),Je={small:u(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:u(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:u(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},Ke={primary:u(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:u(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:u(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:","0d;transform:translateY(-1px);}&:active:not(:disabled){background-color:","1a;transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:u(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:","0d;}&:active:not(:disabled){background-color:","1a;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),success:u(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:u(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},Xe=c.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||500},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({size:e="medium"})=>Je[e],({variant:e="primary"})=>Ke[e],({fullWidth:e})=>e&&u(["width:100%;"]),({$hasStartIcon:e})=>e&&u(["& > *:first-child{margin-right:",";}"],({theme:t})=>t.spacing.xs),({$hasEndIcon:e})=>e&&u(["& > *:last-child{margin-left:",";}"],({theme:t})=>t.spacing.xs)),Qe=c.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),je=({children:e,variant:t="primary",disabled:n=!1,loading:o=!1,size:s="medium",fullWidth:a=!1,startIcon:i,endIcon:l,onClick:d,className:p="",type:m="button",...g})=>r.jsx(Xe,{variant:t,disabled:n||o,size:s,fullWidth:a,onClick:d,className:p,type:m,$hasStartIcon:!!i&&!o,$hasEndIcon:!!l&&!o,...g,children:r.jsxs(Qe,{children:[o&&r.jsx(Ge,{}),!o&&i,e,!o&&l]})}),ce={small:u(["height:100px;"]),medium:u(["height:200px;"]),large:u(["height:300px;"]),custom:e=>u(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Ze={default:u(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:u(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:u(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:u(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},et=te(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),tt=c.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:t,customWidth:n})=>e==="custom"?ce.custom({customHeight:t,customWidth:n}):ce[e],({variant:e})=>Ze[e]),rt=c.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,et,({theme:e})=>e.spacing.sm),ot=c.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),nt=({variant:e="default",size:t="medium",height:n="200px",width:o="",text:s="Loading...",showSpinner:a=!0,className:i=""})=>r.jsxs(tt,{variant:e,size:t,customHeight:n,customWidth:o,className:i,children:[a&&r.jsx(rt,{}),s&&r.jsx(ot,{children:s})]}),st={small:u(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:u(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:u(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},at=e=>u(["",""],({theme:t})=>{let n,o,s;switch(e){case"primary":n=`${t.colors.primary}10`,o=t.colors.primary,s=`${t.colors.primary}30`;break;case"secondary":n=`${t.colors.secondary}10`,o=t.colors.secondary,s=`${t.colors.secondary}30`;break;case"success":n=`${t.colors.success}10`,o=t.colors.success,s=`${t.colors.success}30`;break;case"warning":n=`${t.colors.warning}10`,o=t.colors.warning,s=`${t.colors.warning}30`;break;case"error":n=`${t.colors.error}10`,o=t.colors.error,s=`${t.colors.error}30`;break;case"info":n=`${t.colors.info}10`,o=t.colors.info,s=`${t.colors.info}30`;break;default:n=`${t.colors.textSecondary}10`,o=t.colors.textSecondary,s=`${t.colors.textSecondary}30`}return`
        background-color: ${n};
        color: ${o};
        border: 1px solid ${s};
      `}),it=c.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>st[e],({variant:e})=>at(e),({clickable:e})=>e&&u(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:t})=>t.transitions.fast)),ct=c.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:t})=>{const n={small:"12px",medium:"14px",large:"16px"};return`
      width: ${n[e]};
      height: ${n[e]};
      font-size: ${t.fontSizes.xs};
    `}),ee=({children:e,variant:t="default",size:n="medium",removable:o=!1,onRemove:s,className:a="",onClick:i})=>{const l=d=>{d.stopPropagation(),s==null||s()};return r.jsxs(it,{variant:t,size:n,clickable:!!i,className:a,onClick:i,children:[e,o&&r.jsx(ct,{size:n,onClick:l,children:"×"})]})},lt=te(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),dt=te(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]),pt=c.div.withConfig({displayName:"StyledSpinner",componentId:"sc-1hoaoss-0"})(["display:inline-block;position:relative;"," &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;"," animation:"," ","s linear infinite;}",""],({$size:e})=>{switch(e){case"xs":return u(["width:16px;height:16px;"]);case"sm":return u(["width:20px;height:20px;"]);case"md":return u(["width:32px;height:32px;"]);case"lg":return u(["width:48px;height:48px;"]);case"xl":return u(["width:64px;height:64px;"]);default:return u(["width:32px;height:32px;"])}},({$variant:e,theme:t})=>{var n,o,s,a,i,l;switch(e){case"primary":return u(["border-top-color:",";border-right-color:",";"],((n=t.colors)==null?void 0:n.primary)||"#dc2626",((o=t.colors)==null?void 0:o.primary)||"#dc2626");case"secondary":return u(["border-top-color:",";border-right-color:",";"],((s=t.colors)==null?void 0:s.textSecondary)||"#9ca3af",((a=t.colors)==null?void 0:a.textSecondary)||"#9ca3af");case"white":return u(["border-top-color:#ffffff;border-right-color:#ffffff;"]);case"red":return u(["border-top-color:#dc2626;border-right-color:#dc2626;"]);default:return u(["border-top-color:",";border-right-color:",";"],((i=t.colors)==null?void 0:i.primary)||"#dc2626",((l=t.colors)==null?void 0:l.primary)||"#dc2626")}},lt,({$speed:e})=>1/e,({$showStripes:e,$variant:t})=>e&&u(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:",";background-size:8px 8px;animation:"," ","s linear infinite;}"],t==="red"||t==="primary"?"linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)":"linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)",dt,n=>2/n.$speed)),mt=c.div.withConfig({displayName:"SpinnerContainer",componentId:"sc-1hoaoss-1"})(["display:inline-flex;align-items:center;justify-content:center;"]),ft=e=>{const{size:t="md",variant:n="primary",className:o,"aria-label":s,speed:a=1,showStripes:i=!1}=e;return r.jsx(mt,{className:o,children:r.jsx(pt,{$size:t,$variant:n,$speed:a,$showStripes:i,role:"status","aria-label":s||"Loading","aria-live":"polite"})})},gt=ft,ht=c.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:t})=>{const n={small:t.fontSizes.md,medium:t.fontSizes.lg,large:t.fontSizes.xl};return u(["font-size:",";"],n[e])}),ut=c.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:t})=>{const n={small:t.fontSizes.sm,medium:t.fontSizes.md,large:t.fontSizes.lg};return u(["font-size:",";"],n[e])}),yt={default:u(["background-color:transparent;"]),compact:u(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:u(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},xt=c.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>yt[e],({size:e,theme:t})=>{switch(e){case"small":return u(["padding:",";min-height:120px;"],t.spacing.md);case"large":return u(["padding:",";min-height:300px;"],t.spacing.xl);default:return u(["padding:",";min-height:200px;"],t.spacing.lg)}}),bt=c.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:t})=>{const n={small:"32px",medium:"48px",large:"64px"};return u(["font-size:",";svg{width:",";height:",";color:",";}"],n[e],n[e],n[e],t.colors.textSecondary)}),wt=c.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),vt=c.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),le=({title:e="",description:t="",icon:n,actionText:o="",onAction:s,variant:a="default",size:i="medium",className:l="",children:d})=>r.jsxs(xt,{variant:a,size:i,className:l,children:[n&&r.jsx(bt,{size:i,children:n}),e&&r.jsx(ht,{size:i,children:e}),t&&r.jsx(ut,{size:i,children:t}),o&&s&&r.jsx(wt,{children:r.jsx(je,{variant:"primary",size:i==="small"?"small":"medium",onClick:s,children:o})}),d&&r.jsx(vt,{children:d})]}),Ct=c.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),L=({title:e,children:t,isLoading:n=!1,hasError:o=!1,errorMessage:s="An error occurred while loading data",showRetry:a=!0,onRetry:i,isEmpty:l=!1,emptyMessage:d="No data available",emptyActionText:p,onEmptyAction:m,actionButton:g,className:y,...w})=>{const h=r.jsx(Ct,{children:g});let v;return n?v=r.jsx(nt,{variant:"card",text:"Loading data..."}):o?v=r.jsx(le,{title:"Error",description:s,variant:"compact",actionText:a?"Retry":void 0,onAction:a?i:void 0}):l?v=r.jsx(le,{title:"No Data",description:d,variant:"compact",actionText:p,onAction:m}):v=t,r.jsx(ve,{title:e,actions:h,className:y,...w,children:v})};function jt(e,t){const n=()=>{if(typeof window>"u")return t;try{const i=window.localStorage.getItem(e);return i?JSON.parse(i):t}catch(i){return console.warn(`Error reading localStorage key "${e}":`,i),t}},[o,s]=C.useState(n),a=i=>{try{const l=i instanceof Function?i(o):i;s(l),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(l))}catch(l){console.warn(`Error setting localStorage key "${e}":`,l)}};return C.useEffect(()=>{const i=l=>{l.key===e&&l.newValue&&s(JSON.parse(l.newValue))};return window.addEventListener("storage",i),()=>window.removeEventListener("storage",i)},[e]),[o,a]}const St=e=>{if(e.length===0)return Lt();const t=e.map(x=>x.trade),n=t.filter(x=>(x.achieved_pl||0)>0),o=t.filter(x=>(x.achieved_pl||0)<0),s=t.filter(x=>(x.achieved_pl||0)===0),a=t.reduce((x,k)=>x+(k.achieved_pl||0),0),i=n.reduce((x,k)=>x+(k.achieved_pl||0),0),l=Math.abs(o.reduce((x,k)=>x+(k.achieved_pl||0),0)),d=n.length>0?i/n.length:0,p=o.length>0?l/o.length:0,m=n.length>0?Math.max(...n.map(x=>x.achieved_pl||0)):0,g=o.length>0?Math.min(...o.map(x=>x.achieved_pl||0)):0,y=Mt(t),w=y.length>0?y.reduce((x,k)=>x+k,0)/y.length:0,h=e.length>0?n.length/e.length:0,v=l>0?i/l:i>0?1/0:0,b=h*d-(1-h)*p;return{totalTrades:e.length,winningTrades:n.length,losingTrades:o.length,breakeven:s.length,winRate:Math.round(h*1e4)/100,averageWin:Math.round(d*100)/100,averageLoss:Math.round(p*100)/100,profitFactor:Math.round(v*100)/100,totalProfitLoss:Math.round(a*100)/100,largestWin:Math.round(m*100)/100,largestLoss:Math.round(g*100)/100,averageDuration:Math.round(w*100)/100,expectancy:Math.round(b*100)/100}},Tt=(e,t)=>{if(e.length===0)return[];const n=new Map;e.forEach(s=>{const a=s.trade;let i;switch(t){case"market":i=a.market||"Unknown";break;case"model_type":i=a.model_type||"Unknown";break;case"session":i=a.session||"Unknown";break;case"setup":i=a.setup||"Unknown";break;case"direction":i=a.direction||"Unknown";break;default:i="Unknown"}n.has(i)||n.set(i,[]),n.get(i).push(s)});const o=[];return n.forEach((s,a)=>{const i=s.map(g=>g.trade),l=i.filter(g=>(g.achieved_pl||0)>0),d=i.reduce((g,y)=>g+(y.achieved_pl||0),0),p=s.length>0?l.length/s.length:0,m=s.length>0?d/s.length:0;o.push({category:t,value:a,trades:s.length,winRate:Math.round(p*1e4)/100,profitLoss:Math.round(d*100)/100,averageProfitLoss:Math.round(m*100)/100})}),o.sort((s,a)=>a.profitLoss-s.profitLoss)},kt=(e,t)=>{if(e.length===0)return[];let n;switch(t){case"timeOfDay":n=["9:30-10:30","10:30-11:30","11:30-12:30","12:30-13:30","13:30-14:30","14:30-15:30","15:30-16:00"];break;case"dayOfWeek":n=["Monday","Tuesday","Wednesday","Thursday","Friday"];break;case"monthly":n=["January","February","March","April","May","June","July","August","September","October","November","December"];break;default:return[]}const o=n.map(s=>({timeSlot:s,trades:0,winRate:0,profitLoss:0}));return e.forEach(s=>{const a=s.trade,i=new Date(a.date);let l=-1;if(t==="timeOfDay"&&a.entry_time)l=de(a.entry_time);else if(t==="dayOfWeek"){const d=i.getDay();d>=1&&d<=5&&(l=d-1)}else t==="monthly"&&(l=i.getMonth());if(l>=0&&l<o.length){const d=o[l];d.trades++,d.profitLoss+=a.achieved_pl||0}}),o.forEach((s,a)=>{if(s.trades>0){const i=e.filter(d=>{const p=d.trade,m=new Date(p.date);return t==="timeOfDay"&&p.entry_time?de(p.entry_time)===a:t==="dayOfWeek"?m.getDay()===a+1:t==="monthly"?m.getMonth()===a:!1}),l=i.filter(d=>(d.trade.achieved_pl||0)>0);s.winRate=i.length>0?l.length/i.length*100:0}}),o.filter(s=>s.trades>0).map(s=>({...s,winRate:Math.round(s.winRate*100)/100,profitLoss:Math.round(s.profitLoss*100)/100}))},Dt=e=>{if(e.length===0)return[];const t=[...e].sort((s,a)=>new Date(s.trade.date).getTime()-new Date(a.trade.date).getTime()),n=[];let o=0;return t.forEach((s,a)=>{const i=s.trade;o+=i.achieved_pl||0,n.push({date:i.date,equity:Math.round(o*100)/100,baseline:0,balance:Math.round(o*100)/100,tradeNumber:a+1,profitLoss:i.achieved_pl||0})}),n},It=e=>{if(e.length===0)return[];const t=[{min:-1/0,max:-1e3,label:"< -$1000"},{min:-1e3,max:-500,label:"-$1000 to -$500"},{min:-500,max:-100,label:"-$500 to -$100"},{min:-100,max:0,label:"-$100 to $0"},{min:0,max:100,label:"$0 to $100"},{min:100,max:500,label:"$100 to $500"},{min:500,max:1e3,label:"$500 to $1000"},{min:1e3,max:1/0,label:"> $1000"}],n=t.map(o=>({range:o.label,count:0,percentage:0,totalPnL:0,isWin:o.min>=0}));return e.forEach(o=>{const s=o.trade.achieved_pl||0,a=t.findIndex(i=>s>i.min&&s<=i.max);a>=0&&(n[a].count++,n[a].totalPnL+=s)}),n.forEach(o=>{o.percentage=e.length>0?o.count/e.length*100:0,o.percentage=Math.round(o.percentage*100)/100,o.totalPnL=Math.round(o.totalPnL*100)/100}),n.filter(o=>o.count>0)},Lt=()=>({totalTrades:0,winningTrades:0,losingTrades:0,breakeven:0,winRate:0,averageWin:0,averageLoss:0,profitFactor:0,totalProfitLoss:0,largestWin:0,largestLoss:0,averageDuration:0,expectancy:0}),Mt=e=>e.filter(t=>t.entry_time&&t.exit_time).map(t=>{const n=new Date(t.entry_time).getTime();return(new Date(t.exit_time).getTime()-n)/(1e3*60)}),de=e=>{const t=new Date(e),n=t.getHours(),o=t.getMinutes(),s=n+o/60;return s<9.5||s>=16?-1:s<10.5?0:s<11.5?1:s<12.5?2:s<13.5?3:s<14.5?4:s<15.5?5:6};class Pt{constructor(){ne(this,"cache",new Map);ne(this,"config",{maxAge:5*60*1e3,maxSize:100})}generateCacheKey(t,n,o){const s=t.map(i=>i.trade.id).sort().join(","),a=o?JSON.stringify(o):"";return`${n}:${s}:${a}`}getCached(t){const n=this.cache.get(t);return n?Date.now()-n.timestamp>this.config.maxAge?(this.cache.delete(t),null):n.data:null}setCached(t,n){if(this.cache.size>=this.config.maxSize){const o=this.cache.keys().next().value;o&&this.cache.delete(o)}this.cache.set(t,{data:n,timestamp:Date.now(),hash:t})}clearCache(){this.cache.clear()}clearExpiredCache(){const t=Date.now();for(const[n,o]of this.cache.entries())t-o.timestamp>this.config.maxAge&&this.cache.delete(n)}getCacheStats(){let t=null;for(const n of this.cache.values())(t===null||n.timestamp<t)&&(t=n.timestamp);return{size:this.cache.size,maxSize:this.config.maxSize,hitRate:0,oldestEntry:t}}async getCachedPerformanceMetrics(t,n){const o=this.generateCacheKey(t,"performance-metrics"),s=this.getCached(o);if(s)return console.log("📊 Performance metrics retrieved from cache"),s;console.log("🧮 Calculating performance metrics...");const a=n(t);return this.setCached(o,a),a}async getCachedCategoryPerformance(t,n,o){const s=this.generateCacheKey(t,"category-performance",{category:n}),a=this.getCached(s);if(a)return console.log(`📈 Category performance (${n}) retrieved from cache`),a;console.log(`📈 Calculating category performance for ${n}...`);const i=o(t,n);return this.setCached(s,i),i}async getCachedTimePerformance(t,n,o){const s=this.generateCacheKey(t,"time-performance",{timeType:n}),a=this.getCached(s);if(a)return console.log(`⏰ Time performance (${n}) retrieved from cache`),a;console.log(`⏰ Calculating time performance for ${n}...`);const i=o(t,n);return this.setCached(s,i),i}async getCachedEquityCurve(t,n){const o=this.generateCacheKey(t,"equity-curve"),s=this.getCached(o);if(s)return console.log("📊 Equity curve retrieved from cache"),s;console.log("📊 Calculating equity curve...");const a=n(t);return this.setCached(o,a),a}async getCachedDistributionData(t,n){const o=this.generateCacheKey(t,"distribution-data"),s=this.getCached(o);if(s)return console.log("📊 Distribution data retrieved from cache"),s;console.log("📊 Calculating distribution data...");const a=n(t);return this.setCached(o,a),a}}const D=new Pt,$t=async(e,t)=>{console.log("🚀 Starting batch analytics calculation...");const[n,o,s,a,i,l,d,p,m,g,y]=await Promise.all([D.getCachedPerformanceMetrics(e,t.performanceMetrics),D.getCachedCategoryPerformance(e,"market",t.categoryPerformance),D.getCachedCategoryPerformance(e,"model_type",t.categoryPerformance),D.getCachedCategoryPerformance(e,"session",t.categoryPerformance),D.getCachedCategoryPerformance(e,"setup",t.categoryPerformance),D.getCachedCategoryPerformance(e,"direction",t.categoryPerformance),D.getCachedTimePerformance(e,"timeOfDay",t.timePerformance),D.getCachedTimePerformance(e,"dayOfWeek",t.timePerformance),D.getCachedTimePerformance(e,"monthly",t.timePerformance),D.getCachedEquityCurve(e,t.equityCurve),D.getCachedDistributionData(e,t.distributionData)]);return console.log("✅ Batch analytics calculation completed"),{metrics:n,symbolPerformance:o,strategyPerformance:s,sessionPerformance:a,setupPerformance:i,directionPerformance:l,timeOfDayPerformance:d,dayOfWeekPerformance:p,monthlyPerformance:m,equityCurve:g,distributionData:y}},K={async measureTime(e,t){const n=performance.now(),o=await t(),s=performance.now();return console.log(`⏱️ ${e} took ${(s-n).toFixed(2)}ms`),o},logMemoryUsage(e){if("memory"in performance){const t=performance.memory;console.log(`🧠 Memory usage (${e}):`,{used:`${(t.usedJSHeapSize/1024/1024).toFixed(2)} MB`,total:`${(t.totalJSHeapSize/1024/1024).toFixed(2)} MB`,limit:`${(t.jsHeapSizeLimit/1024/1024).toFixed(2)} MB`})}},getCacheStats(){return D.getCacheStats()}},Nt=async e=>{try{console.log("🔄 Fetching real trade analysis data with filters:",e);const t=await Oe.getAllTrades();console.log(`📊 Retrieved ${t.length} trades from storage`);const n=Rt(t,e);console.log(`🔍 After client-side filtering: ${n.length} trades`);const o=await Et(n);return console.log("✅ Trade analysis data generated successfully"),o}catch(t){throw console.error("❌ Error fetching real trade analysis data:",t),new Error(`Failed to fetch trade analysis data: ${t instanceof Error?t.message:"Unknown error"}`)}},Et=async e=>{console.log("🧮 Starting performance analysis with caching..."),K.logMemoryUsage("before analysis");const t=await K.measureTime("Batch Analytics Calculation",()=>$t(e,{performanceMetrics:St,categoryPerformance:Tt,timePerformance:kt,equityCurve:Dt,distributionData:It})),{metrics:n,symbolPerformance:o,strategyPerformance:s,sessionPerformance:a,setupPerformance:i,directionPerformance:l,timeOfDayPerformance:d,dayOfWeekPerformance:p,monthlyPerformance:m,equityCurve:g,distributionData:y}=t;K.logMemoryUsage("after analysis");const w=K.getCacheStats();return console.log("📊 Cache statistics:",w),{trades:e.map(zt),metrics:n,symbolPerformance:o,strategyPerformance:s,timeframePerformance:[],sessionPerformance:a,setupPerformance:i,directionPerformance:l,timeOfDayPerformance:d,dayOfWeekPerformance:p,monthlyPerformance:m,equityCurve:g,distributionData:y,totalTrades:e.length,dateRange:{start:e.length>0?Math.min(...e.map(h=>new Date(h.trade.date).getTime())):Date.now(),end:e.length>0?Math.max(...e.map(h=>new Date(h.trade.date).getTime())):Date.now()},lastUpdated:new Date().toISOString()}},Rt=(e,t)=>e.filter(n=>{var s;const o=n.trade;if(t.symbols&&t.symbols.length>1&&!t.symbols.some(i=>{var l;return(l=o.market)==null?void 0:l.toLowerCase().includes(i.toLowerCase())}))return!1;if(t.directions&&t.directions.length>1){const a=(s=o.direction)==null?void 0:s.toLowerCase();if(!t.directions.some(l=>l===a))return!1}if(t.sessions&&t.sessions.length>1){const a=o.session||"";if(!t.sessions.includes(a))return!1}if(t.strategies&&t.strategies.length>1&&!t.strategies.includes(o.model_type||""))return!1;if(t.statuses&&t.statuses.length>0){const a=Se(o.achieved_pl||0);if(!t.statuses.includes(a))return!1}return t.tags&&t.tags.length>0,!0}),zt=e=>{var n,o;const t=e.trade;return{id:((n=t.id)==null?void 0:n.toString())||`${t.date}-${t.market}`,symbol:t.market||"Unknown",direction:((o=t.direction)==null?void 0:o.toLowerCase())||"unknown",entryPrice:t.entry_price||0,exitPrice:t.exit_price||0,quantity:t.no_of_contracts||0,entryTime:t.entry_time||t.date,exitTime:t.exit_time||t.date,status:Se(t.achieved_pl||0),profitLoss:t.achieved_pl||0,profitLossPercent:Ft(t),timeframe:"15m",session:t.session||"regular",strategy:t.model_type||"Unknown",setup:t.setup||"Unknown",rMultiple:t.r_multiple||0,tags:[],notes:t.notes||"",patternQuality:t.pattern_quality_rating||0,dolTarget:t.dol_target||"",rdType:t.rd_type||"",drawOnLiquidity:t.draw_on_liquidity||""}},Se=e=>e>0?"win":e<0?"loss":"breakeven",Ft=e=>{if(!e.entry_price||e.entry_price===0)return 0;const t=e.achieved_pl||0,n=e.entry_price*(e.no_of_contracts||1);return n>0?t/n*100:0},At=!0,_t=async e=>{console.log(`🔄 Fetching trade analysis data (Real Data: ${At})`);try{return await Nt(e)}catch(t){return console.error("❌ Error fetching real trade data, falling back to mock data:",t),Wt(e)}},Wt=e=>{const t=Ot(e),n=Bt(t),o=X(t,"symbol"),s=X(t,"strategy"),a=X(t,"timeframe"),i=X(t,"session"),l=pe(t,"timeOfDay"),d=pe(t,"dayOfWeek");return{trades:t,metrics:n,symbolPerformance:o,strategyPerformance:s,timeframePerformance:a,sessionPerformance:i,timeOfDayPerformance:l,dayOfWeekPerformance:d}},Ot=e=>{const{dateRange:t}=e,n=new Date(t.startDate),o=new Date(t.endDate),s=Math.floor((o.getTime()-n.getTime())/(1e3*60*60*24)),a=Math.max(1,s)*(1+Math.floor(Math.random()*5)),i=[],l=["AAPL","MSFT","GOOGL","AMZN","TSLA","META","NFLX","NVDA"],d=["Breakout","Reversal","Trend Following","Gap and Go","VWAP Bounce","Support/Resistance"],p=["1m","5m","15m","30m","1h","4h","daily"],m=["pre-market","regular","after-hours"],g=["High Volume","Low Float","Earnings","News","Technical","Momentum","Oversold","Overbought"];for(let y=0;y<a;y++){const w=new Date(n.getTime()+Math.random()*(o.getTime()-n.getTime())),h=9+Math.floor(Math.random()*7),v=Math.floor(Math.random()*60),b=new Date(w);b.setHours(h,v,0,0);const x=5+Math.floor(Math.random()*120),k=new Date(b.getTime()+x*60*1e3),J=l[Math.floor(Math.random()*l.length)],S=Math.random()>.5?"long":"short",T=100+Math.random()*900,f=Math.random()<.6,I=!f&&Math.random()<.1;let j,P,A,U;if(I)j=T+(Math.random()*.2-.1),U="breakeven";else if(f){const _=.5+Math.random()*4.5;j=S==="long"?T*(1+_/100):T*(1-_/100),U="win"}else{const _=.5+Math.random()*2.5;j=S==="long"?T*(1-_/100):T*(1+_/100),U="loss"}const re=10+Math.floor(Math.random()*90);S==="long"?(P=(j-T)*re,A=(j/T-1)*100):(P=(T-j)*re,A=(T/j-1)*100),P=Math.round(P*100)/100,A=Math.round(A*100)/100;const Ee=p[Math.floor(Math.random()*p.length)],Re=m[Math.floor(Math.random()*m.length)],ze=d[Math.floor(Math.random()*d.length)],Fe=Math.floor(Math.random()*4),oe=[];for(let _=0;_<Fe;_++){const ie=g[Math.floor(Math.random()*g.length)];oe.includes(ie)||oe.push(ie)}const Ae={id:`trade-${y}`,symbol:J,direction:S,entryPrice:T,exitPrice:j,quantity:re,entryTime:b.toISOString(),exitTime:k.toISOString(),status:U,profitLoss:P,profitLossPercent:A,timeframe:Ee,session:Re,strategy:ze,tags:oe,notes:Math.random()>.7?`Sample note for ${J} ${S} trade`:void 0};i.push(Ae)}return i.sort((y,w)=>new Date(w.entryTime).getTime()-new Date(y.entryTime).getTime())},Bt=e=>{const t=e.filter(b=>b.status==="win"),n=e.filter(b=>b.status==="loss"),o=e.filter(b=>b.status==="breakeven"),s=e.reduce((b,x)=>b+x.profitLoss,0),a=t.reduce((b,x)=>b+x.profitLoss,0),i=Math.abs(n.reduce((b,x)=>b+x.profitLoss,0)),l=t.length>0?a/t.length:0,d=n.length>0?i/n.length:0,p=t.length>0?Math.max(...t.map(b=>b.profitLoss)):0,m=n.length>0?Math.min(...n.map(b=>b.profitLoss)):0,g=e.map(b=>{const x=new Date(b.entryTime).getTime();return(new Date(b.exitTime).getTime()-x)/(1e3*60)}),y=g.length>0?g.reduce((b,x)=>b+x,0)/g.length:0,w=i>0?a/i:a>0?1/0:0,h=e.length>0?t.length/e.length:0,v=h*l-(1-h)*d;return{totalTrades:e.length,winningTrades:t.length,losingTrades:n.length,breakeven:o.length,winRate:Math.round(h*1e4)/100,averageWin:Math.round(l*100)/100,averageLoss:Math.round(d*100)/100,profitFactor:Math.round(w*100)/100,totalProfitLoss:Math.round(s*100)/100,largestWin:Math.round(p*100)/100,largestLoss:Math.round(m*100)/100,averageDuration:Math.round(y*100)/100,expectancy:Math.round(v*100)/100}},X=(e,t)=>{const n=new Map;e.forEach(s=>{const a=s[t];n.has(a)||n.set(a,[]),n.get(a).push(s)});const o=[];return n.forEach((s,a)=>{const i=s.filter(m=>m.status==="win"),l=s.reduce((m,g)=>m+g.profitLoss,0),d=s.length>0?i.length/s.length:0,p=s.length>0?l/s.length:0;o.push({category:t,value:a,trades:s.length,winRate:Math.round(d*1e4)/100,profitLoss:Math.round(l*100)/100,averageProfitLoss:Math.round(p*100)/100})}),o.sort((s,a)=>a.profitLoss-s.profitLoss)},pe=(e,t)=>{let n;t==="timeOfDay"?n=["9:30-10:30","10:30-11:30","11:30-12:30","12:30-13:30","13:30-14:30","14:30-15:30","15:30-16:00"]:n=["Monday","Tuesday","Wednesday","Thursday","Friday"];const o=n.map(s=>({timeSlot:s,trades:0,winRate:0,profitLoss:0}));return e.forEach(s=>{const a=new Date(s.entryTime);let i;if(t==="timeOfDay"){const m=a.getHours(),g=a.getMinutes(),y=m+g/60;if(y<9.5||y>=16)return;y<10.5?i=0:y<11.5?i=1:y<12.5?i=2:y<13.5?i=3:y<14.5?i=4:y<15.5?i=5:i=6}else{const m=a.getDay();if(m===0||m===6)return;i=m-1}const l=o[i];l.trades++,l.profitLoss+=s.profitLoss;const d=e.filter(m=>{const g=new Date(m.entryTime);if(t==="timeOfDay"){const y=g.getHours(),w=g.getMinutes(),h=y+w/60;return i===0?h>=9.5&&h<10.5:i===1?h>=10.5&&h<11.5:i===2?h>=11.5&&h<12.5:i===3?h>=12.5&&h<13.5:i===4?h>=13.5&&h<14.5:i===5?h>=14.5&&h<15.5:i===6?h>=15.5&&h<16:!1}else return g.getDay()===i+1}),p=d.filter(m=>m.status==="win");l.winRate=d.length>0?p.length/d.length*100:0}),o.filter(s=>s.trades>0).map(s=>({...s,winRate:Math.round(s.winRate*100)/100,profitLoss:Math.round(s.profitLoss*100)/100}))},Te=()=>{const e=new Date,t=new Date;return t.setMonth(e.getMonth()-1),{startDate:t.toISOString().split("T")[0],endDate:e.toISOString().split("T")[0]}},me={data:null,filters:{dateRange:Te()},preferences:{defaultDateRange:"month",defaultView:"summary",chartTypes:{performance:"bar",distribution:"pie",timeAnalysis:"bar"},tableColumns:["symbol","direction","entryTime","exitTime","profitLoss","status","strategy"],favoriteStrategies:[],favoriteTags:[]},isLoading:!1,error:null,selectedTradeId:null},qt=(e,t)=>{switch(t.type){case"FETCH_DATA_START":return{...e,isLoading:!0,error:null};case"FETCH_DATA_SUCCESS":return{...e,data:t.payload,isLoading:!1,error:null};case"FETCH_DATA_ERROR":return{...e,isLoading:!1,error:t.payload};case"UPDATE_FILTERS":return{...e,filters:{...e.filters,...t.payload}};case"UPDATE_PREFERENCES":return{...e,preferences:{...e.preferences,...t.payload}};case"SELECT_TRADE":return{...e,selectedTradeId:t.payload};case"RESET_FILTERS":return{...e,filters:{dateRange:Te()}};default:return e}},ke=C.createContext(void 0),Ht=({children:e})=>{const[t]=jt("trade-analysis-preferences",{}),n={...me,preferences:{...me.preferences,...t}},[o,s]=C.useReducer(qt,n);C.useEffect(()=>{localStorage.setItem("trade-analysis-preferences",JSON.stringify(o.preferences))},[o.preferences]);const a=C.useCallback(async()=>{s({type:"FETCH_DATA_START"});try{const g=await _t(o.filters);s({type:"FETCH_DATA_SUCCESS",payload:g})}catch(g){s({type:"FETCH_DATA_ERROR",payload:g instanceof Error?g.message:"An unknown error occurred"})}},[o.filters]),i=C.useCallback(g=>{s({type:"UPDATE_FILTERS",payload:g})},[]),l=C.useCallback(g=>{s({type:"UPDATE_PREFERENCES",payload:g})},[]),d=C.useCallback(g=>{s({type:"SELECT_TRADE",payload:g})},[]),p=C.useCallback(()=>{s({type:"RESET_FILTERS"})},[]);C.useEffect(()=>{a()},[a,o.filters]);const m={...o,fetchData:a,updateFilters:i,updatePreferences:l,selectTrade:d,resetFilters:p};return r.jsx(ke.Provider,{value:m,children:e})},B=()=>{const e=C.useContext(ke);if(e===void 0)throw new Error("useTradeAnalysis must be used within a TradeAnalysisProvider");return e},Ut=c.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1tqg3lb-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid var(--border-primary);margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"}),Vt=c.div.withConfig({displayName:"TitleSection",componentId:"sc-1tqg3lb-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),Yt=c.h1.withConfig({displayName:"Title",componentId:"sc-1tqg3lb-2"})(["font-size:",";font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xxl)||"2rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),Gt=c.div.withConfig({displayName:"Subtitle",componentId:"sc-1tqg3lb-3"})(["font-size:",";color:var(--text-secondary);font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"}),Jt=c.div.withConfig({displayName:"ActionsSection",componentId:"sc-1tqg3lb-4"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),Kt=c(je).withConfig({displayName:"RefreshButton",componentId:"sc-1tqg3lb-5"})(["min-width:100px;position:relative;",""],({isRefreshing:e})=>e&&`
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `),Xt=c.div.withConfig({displayName:"StatusIndicator",componentId:"sc-1tqg3lb-6"})(["display:flex;align-items:center;gap:",";padding:"," ",";background:var(--model-card-bg);border:1px solid var(--model-card-border);border-radius:",";font-size:",";font-weight:600;color:var(--model-name-color);text-transform:uppercase;letter-spacing:0.05em;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"}),Qt=c.div.withConfig({displayName:"StatusDot",componentId:"sc-1tqg3lb-7"})(["width:6px;height:6px;background:var(--primary-color);border-radius:50%;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),Zt=({onRefresh:e,isRefreshing:t=!1,className:n})=>r.jsxs(Ut,{className:n,children:[r.jsxs(Vt,{children:[r.jsx(Yt,{children:"Trade Analysis"}),r.jsx(Gt,{children:"Performance Metrics & Insights"})]}),r.jsxs(Jt,{children:[r.jsxs(Xt,{children:[r.jsx(Qt,{}),"LIVE DATA"]}),e&&r.jsx(Kt,{variant:"outline",size:"small",onClick:e,disabled:t,isRefreshing:t,children:t?"Refreshing...":"Refresh"})]})]}),er=c.div.withConfig({displayName:"TabsContainer",componentId:"sc-ila0jb-0"})(["display:flex;gap:",";margin-bottom:",";border-bottom:1px solid var(--border-primary);padding-bottom:",";overflow-x:auto;scrollbar-width:none;-ms-overflow-style:none;&::-webkit-scrollbar{display:none;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),tr=c.button.withConfig({displayName:"Tab",componentId:"sc-ila0jb-1"})(["background:none;border:none;padding:"," ",";font-size:",";font-weight:",";color:",";cursor:pointer;border-bottom:2px solid ",";transition:all ",";white-space:nowrap;text-transform:uppercase;letter-spacing:0.025em;position:relative;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;&:hover{color:",";background:rgba(220,38,38,0.05);}&:focus{outline:none;box-shadow:0 0 0 2px rgba(220,38,38,0.2);border-radius:",";}",""],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e,active:t})=>{var n,o;return t?((n=e.fontWeights)==null?void 0:n.semibold)||"600":((o=e.fontWeights)==null?void 0:o.medium)||"500"},({theme:e,active:t})=>{var n;return t?((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)":"var(--text-secondary)"},({theme:e,active:t})=>{var n;return t?((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)":"transparent"},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"0.2s ease"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.xs)||"2px"},({active:e,theme:t})=>{var n;return e&&`
    &::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 2px;
      background: ${((n=t.colors)==null?void 0:n.primary)||"var(--primary-color)"};
      animation: slideIn 0.3s ease-out;
    }
    
    @keyframes slideIn {
      from {
        transform: scaleX(0);
      }
      to {
        transform: scaleX(1);
      }
    }
  `}),rr=c.span.withConfig({displayName:"TabBadge",componentId:"sc-ila0jb-2"})(["display:inline-block;margin-left:",";padding:2px 6px;background:rgba(220,38,38,0.1);color:var(--primary-color);border-radius:",";font-size:",";font-weight:600;line-height:1;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.xs)||"2px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"}),or={summary:{label:"Summary"},trades:{label:"Trades"},symbols:{label:"Symbols"},strategies:{label:"Strategies"},timeframes:{label:"Timeframes"},time:{label:"Time Analysis"}},nr=({activeTab:e,onTabChange:t,className:n})=>{const o=s=>{t(s)};return r.jsx(er,{className:n,children:Object.entries(or).map(([s,a])=>r.jsxs(tr,{active:e===s,onClick:()=>o(s),"aria-selected":e===s,role:"tab",children:[a.label,a.badge&&r.jsx(rr,{children:a.badge})]},s))})},sr=c.div.withConfig({displayName:"Container",componentId:"sc-wstfmv-0"})(["background:var(--bg-primary);border:1px solid var(--border-primary);border-radius:8px;margin-bottom:24px;overflow:hidden;"]),ar=c.div.withConfig({displayName:"FilterHeader",componentId:"sc-wstfmv-1"})(["display:flex;justify-content:space-between;align-items:center;padding:20px 24px;background:linear-gradient(135deg,var(--bg-primary) 0%,#2a2a2a 100%);border-bottom:1px solid var(--border-primary);"]),ir=c.div.withConfig({displayName:"HeaderTitle",componentId:"sc-wstfmv-2"})(["display:flex;align-items:center;gap:12px;"]),cr=c.h3.withConfig({displayName:"Title",componentId:"sc-wstfmv-3"})(["color:#ffffff;font-size:18px;font-weight:600;margin:0;letter-spacing:0.5px;"]),lr=c.div.withConfig({displayName:"LiveIndicator",componentId:"sc-wstfmv-4"})(["display:flex;align-items:center;gap:6px;color:var(--error-color);font-size:12px;font-weight:500;text-transform:uppercase;letter-spacing:1px;&::before{content:'';width:8px;height:8px;background:var(--error-color);border-radius:50%;animation:pulse 2s infinite;}@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),dr=c.div.withConfig({displayName:"HeaderActions",componentId:"sc-wstfmv-5"})(["display:flex;gap:12px;"]),pr=c.div.withConfig({displayName:"FilterContent",componentId:"sc-wstfmv-6"})(["padding:24px;"]),mr=c.div.withConfig({displayName:"FilterGrid",componentId:"sc-wstfmv-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:24px;margin-bottom:24px;@media (max-width:768px){grid-template-columns:1fr;gap:20px;}"]),W=c.div.withConfig({displayName:"FilterGroup",componentId:"sc-wstfmv-8"})(["background:var(--bg-primary);border:1px solid var(--border-primary);border-radius:6px;padding:16px;transition:border-color 0.2s ease;&:hover{border-color:var(--text-secondary);}"]),O=c.div.withConfig({displayName:"FilterLabel",componentId:"sc-wstfmv-9"})(["color:var(--text-secondary);font-size:12px;font-weight:500;text-transform:uppercase;letter-spacing:1px;margin-bottom:12px;display:flex;align-items:center;gap:8px;&::before{content:'';width:3px;height:12px;background:var(--error-color);border-radius:2px;}"]),fr=c.div.withConfig({displayName:"DateRangeContainer",componentId:"sc-wstfmv-10"})(["display:grid;grid-template-columns:1fr 1fr;gap:12px;"]),fe=c.input.withConfig({displayName:"DateInput",componentId:"sc-wstfmv-11"})(["background:var(--bg-primary);border:1px solid var(--border-primary);border-radius:4px;padding:10px 12px;color:#ffffff;font-size:14px;font-family:'Inter',sans-serif;transition:all 0.2s ease;&:focus{outline:none;border-color:var(--error-color);box-shadow:0 0 0 2px rgba(239,68,68,0.1);}&::-webkit-calendar-picker-indicator{filter:invert(1);cursor:pointer;}"]),q=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-wstfmv-12"})(["display:flex;flex-wrap:wrap;gap:8px;"]),H=c(ee).withConfig({displayName:"FilterTag",componentId:"sc-wstfmv-13"})(["cursor:pointer;opacity:",";background:",";color:",";border:1px solid ",";transition:all 0.2s ease;&:hover{opacity:0.8;transform:translateY(-1px);}"],({selected:e})=>e?1:.6,({selected:e,variant:t})=>{if(!e)return"var(--border-primary)";switch(t){case"success":return"var(--success-color)";case"error":return"var(--error-color)";case"info":return"var(--info-color)";case"primary":return"#8b5cf6";case"secondary":return"var(--warning-color)";default:return"var(--text-secondary)"}},({selected:e})=>e?"#ffffff":"var(--text-secondary)",({selected:e,variant:t})=>{if(!e)return"var(--border-primary)";switch(t){case"success":return"var(--success-color)";case"error":return"var(--error-color)";case"info":return"var(--info-color)";case"primary":return"#8b5cf6";case"secondary":return"var(--warning-color)";default:return"var(--text-secondary)"}}),gr=c.div.withConfig({displayName:"ActionBar",componentId:"sc-wstfmv-14"})(["display:flex;justify-content:space-between;align-items:center;padding:20px 24px;background:var(--bg-primary);border-top:1px solid var(--border-primary);"]),hr=c.div.withConfig({displayName:"FilterStats",componentId:"sc-wstfmv-15"})(["color:var(--text-secondary);font-size:12px;font-weight:500;"]),ur=c.div.withConfig({displayName:"ActionButtons",componentId:"sc-wstfmv-16"})(["display:flex;gap:12px;"]),ge=c.button.withConfig({displayName:"ActionButton",componentId:"sc-wstfmv-17"})(["background:",";color:",";border:1px solid ",";border-radius:4px;padding:8px 16px;font-size:12px;font-weight:500;text-transform:uppercase;letter-spacing:0.5px;cursor:pointer;transition:all 0.2s ease;outline:none;&:hover{background:",";transform:translateY(-1px);}&:active{transform:translateY(0);}"],({variant:e})=>e==="primary"?"var(--error-color)":"transparent",({variant:e})=>e==="primary"?"#ffffff":"var(--text-secondary)",({variant:e})=>e==="primary"?"var(--error-color)":"var(--border-primary)",({variant:e})=>e==="primary"?"var(--primary-color)":"var(--border-primary)"),yr=({className:e})=>{var T;const{filters:t,updateFilters:n,resetFilters:o,data:s}=B(),[a,i]=C.useState(t),l=s!=null&&s.trades?[...new Set(s.trades.map(f=>f.symbol))]:[],d=s!=null&&s.trades?[...new Set(s.trades.map(f=>f.strategy))]:[],p=s!=null&&s.trades?[...new Set(s.trades.flatMap(f=>f.tags||[]))]:[],m=["long","short"],g=["win","loss","breakeven"],y=["1m","5m","15m","30m","1h","4h","daily"],w=["pre-market","regular","after-hours"],h=Object.values(a).filter(f=>Array.isArray(f)?f.length>0:typeof f=="object"&&f!==null?Object.values(f).some(I=>I):f!=null&&f!=="").length,v=((T=s==null?void 0:s.trades)==null?void 0:T.length)||0,b=(f,I)=>{i(j=>({...j,dateRange:{...j.dateRange,[f]:I}}))},x=(f,I)=>{i(j=>{const P=j[f]||[],A=P.includes(I)?P.filter(U=>U!==I):[...P,I];return{...j,[f]:A.length>0?A:void 0}})},k=()=>{n(a)},J=()=>{o(),i(t)},S=(f,I)=>{const j=a[f];return j?j.includes(I):!1};return r.jsxs(sr,{className:e,children:[r.jsxs(ar,{children:[r.jsxs(ir,{children:[r.jsx(cr,{children:"Trade Analysis"}),r.jsx(lr,{children:"LIVE"})]}),r.jsx(dr,{children:r.jsx(ge,{variant:"secondary",onClick:J,children:"Reset"})})]}),r.jsx(pr,{children:r.jsxs(mr,{children:[r.jsxs(W,{children:[r.jsx(O,{children:"Date Range"}),r.jsxs(fr,{children:[r.jsx(fe,{type:"date",value:a.dateRange.startDate,onChange:f=>b("startDate",f.target.value),placeholder:"Start Date"}),r.jsx(fe,{type:"date",value:a.dateRange.endDate,onChange:f=>b("endDate",f.target.value),placeholder:"End Date"})]})]}),r.jsxs(W,{children:[r.jsx(O,{children:"Direction"}),r.jsx(q,{children:m.map(f=>r.jsx(H,{variant:f==="long"?"success":"error",selected:S("directions",f),onClick:()=>x("directions",f),children:f},f))})]}),r.jsxs(W,{children:[r.jsx(O,{children:"Status"}),r.jsx(q,{children:g.map(f=>r.jsx(H,{variant:f==="win"?"success":f==="loss"?"error":"info",selected:S("statuses",f),onClick:()=>x("statuses",f),children:f},f))})]}),l.length>0&&r.jsxs(W,{children:[r.jsx(O,{children:"Symbols"}),r.jsx(q,{children:l.map(f=>r.jsx(H,{variant:"primary",selected:S("symbols",f),onClick:()=>x("symbols",f),children:f},f))})]}),d.length>0&&r.jsxs(W,{children:[r.jsx(O,{children:"Strategies"}),r.jsx(q,{children:d.map(f=>r.jsx(H,{variant:"secondary",selected:S("strategies",f),onClick:()=>x("strategies",f),children:f},f))})]}),r.jsxs(W,{children:[r.jsx(O,{children:"Timeframe"}),r.jsx(q,{children:y.map(f=>r.jsx(H,{variant:"default",selected:S("timeframes",f),onClick:()=>x("timeframes",f),children:f},f))})]}),r.jsxs(W,{children:[r.jsx(O,{children:"Session"}),r.jsx(q,{children:w.map(f=>r.jsx(H,{variant:"default",selected:S("sessions",f),onClick:()=>x("sessions",f),children:f},f))})]}),p.length>0&&r.jsxs(W,{children:[r.jsx(O,{children:"Tags"}),r.jsx(q,{children:p.map(f=>r.jsx(H,{variant:"info",selected:S("tags",f),onClick:()=>x("tags",f),children:f},f))})]})]})}),r.jsxs(gr,{children:[r.jsx(hr,{children:h>0?`${h} filter${h>1?"s":""} active • ${v} trades`:`${v} trades • No filters applied`}),r.jsx(ur,{children:r.jsx(ge,{variant:"primary",onClick:k,children:"Apply Filters"})})]})]})},xr=c.div.withConfig({displayName:"Container",componentId:"sc-11ey2j3-0"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),$=c.div.withConfig({displayName:"MetricCard",componentId:"sc-11ey2j3-1"})(["background-color:",";border-radius:",";padding:",";display:flex;flex-direction:column;"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.md),N=c.div.withConfig({displayName:"MetricLabel",componentId:"sc-11ey2j3-2"})(["font-size:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xs),E=c.div.withConfig({displayName:"MetricValue",componentId:"sc-11ey2j3-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e,positive:t,negative:n})=>t?e.colors.profit:n?e.colors.loss:e.colors.textPrimary),he=c.div.withConfig({displayName:"MetricChange",componentId:"sc-11ey2j3-4"})(["font-size:",";margin-top:",";color:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs,({theme:e,positive:t,negative:n})=>t?e.colors.profit:n?e.colors.loss:e.colors.textSecondary),br=({className:e})=>{const{data:t}=B();if(!t)return null;const{metrics:n}=t,o=a=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(a),s=a=>`${a.toFixed(2)}%`;return r.jsxs(xr,{className:e,children:[r.jsxs($,{children:[r.jsx(N,{children:"Total P&L"}),r.jsx(E,{positive:n.totalProfitLoss>0,negative:n.totalProfitLoss<0,children:o(n.totalProfitLoss)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Win Rate"}),r.jsx(E,{positive:n.winRate>50,negative:n.winRate<50,children:s(n.winRate)}),r.jsxs(he,{children:[n.winningTrades," / ",n.totalTrades," trades"]})]}),r.jsxs($,{children:[r.jsx(N,{children:"Profit Factor"}),r.jsx(E,{positive:n.profitFactor>1,negative:n.profitFactor<1,children:n.profitFactor.toFixed(2)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Expectancy"}),r.jsx(E,{positive:n.expectancy>0,negative:n.expectancy<0,children:o(n.expectancy)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Average Win"}),r.jsx(E,{positive:!0,children:o(n.averageWin)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Average Loss"}),r.jsx(E,{negative:!0,children:o(-Math.abs(n.averageLoss))})]}),r.jsxs($,{children:[r.jsx(N,{children:"Largest Win"}),r.jsx(E,{positive:!0,children:o(n.largestWin)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Largest Loss"}),r.jsx(E,{negative:!0,children:o(n.largestLoss)})]}),r.jsxs($,{children:[r.jsx(N,{children:"Total Trades"}),r.jsx(E,{children:n.totalTrades}),r.jsxs(he,{children:["Avg Duration: ",n.averageDuration.toFixed(0)," min"]})]})]})},wr=c.thead.withConfig({displayName:"TableHead",componentId:"sc-a4169g-0"})(["background:",";position:sticky;top:0;z-index:10;border-bottom:2px solid ",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"var(--bg-primary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),vr=c.tr.withConfig({displayName:"TableRow",componentId:"sc-a4169g-1"})(["background:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"var(--bg-primary)"}),Cr=c.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-a4169g-2"})(["padding:"," ",";text-align:left;font-weight:600;font-size:",";color:",";cursor:",";user-select:none;transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;border-bottom:1px solid ",";&:hover{","}&:active{","}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e,$active:t})=>{var n,o;return t?((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)":((o=e.colors)==null?void 0:o.textPrimary)||"#ffffff"},({$sortable:e})=>e?"pointer":"default",({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({$sortable:e,theme:t})=>{var n,o;return e&&`
      color: ${((n=t.colors)==null?void 0:n.primary)||"var(--primary-color)"};
      background: ${((o=t.colors)==null?void 0:o.primary)||"var(--primary-color)"}08;
      transform: translateY(-1px);
    `},({$sortable:e})=>e&&`
      transform: translateY(0);
    `),jr=c.span.withConfig({displayName:"SortIcon",componentId:"sc-a4169g-3"})(["display:inline-block;margin-left:",";font-size:12px;opacity:",";transition:all 0.2s ease;color:",";&::after{content:'","';}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$active:e})=>e?1:.5,({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({$direction:e})=>e==="asc"?"↑":"↓"),Sr=c.div.withConfig({displayName:"HeaderContent",componentId:"sc-a4169g-4"})(["display:flex;align-items:center;justify-content:space-between;min-height:20px;"]),Tr=c.span.withConfig({displayName:"HeaderText",componentId:"sc-a4169g-5"})(["flex:1;"]),kr=[{key:"entryTime",label:"Date/Time",sortable:!0,width:"140px"},{key:"symbol",label:"Symbol",sortable:!0,width:"80px"},{key:"direction",label:"Direction",sortable:!0,width:"90px"},{key:null,label:"Entry/Exit",sortable:!1,width:"120px"},{key:"profitLoss",label:"P&L",sortable:!0,width:"100px"},{key:"profitLossPercent",label:"P&L %",sortable:!0,width:"80px"},{key:"status",label:"Status",sortable:!0,width:"90px"},{key:null,label:"Strategy",sortable:!1,width:"120px"},{key:null,label:"Tags",sortable:!1,width:"150px"}],Dr=({sortField:e,sortDirection:t,onSort:n})=>{const o=a=>{a&&n&&n(a)},s=(a,i)=>{(a.key==="Enter"||a.key===" ")&&i&&(a.preventDefault(),n(i))};return r.jsx(wr,{children:r.jsx(vr,{children:kr.map((a,i)=>r.jsx(Cr,{$sortable:a.sortable,$active:a.key===e,style:{width:a.width},onClick:()=>o(a.key),onKeyDown:l=>s(l,a.key),tabIndex:a.sortable?0:-1,role:a.sortable?"button":void 0,"aria-sort":a.key===e?t==="asc"?"ascending":"descending":void 0,title:a.sortable?`Sort by ${a.label} ${a.key===e?t==="asc"?"(descending)":"(ascending)":""}`:void 0,children:r.jsxs(Sr,{children:[r.jsx(Tr,{children:a.label}),a.sortable&&r.jsx(jr,{$direction:t,$active:a.key===e})]})},i))})})},Ir=c.tr.withConfig({displayName:"TableRow",componentId:"sc-1v0tsx0-0"})(["border-bottom:1px solid ",";background:",";cursor:pointer;transition:all 0.2s ease;&:hover{background:",";transform:translateY(-1px);box-shadow:0 2px 4px ","20;}&:active{transform:translateY(0);}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e,$isSelected:t})=>{var n;return t?`${((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)"}15`:"transparent"},({theme:e,$isSelected:t})=>{var n,o;return t?`${((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)"}20`:`${((o=e.colors)==null?void 0:o.primary)||"var(--primary-color)"}08`},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),F=c.td.withConfig({displayName:"TableCell",componentId:"sc-1v0tsx0-1"})(["padding:",";vertical-align:middle;font-size:",";color:",";border-right:1px solid ","40;&:last-child{border-right:none;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),Lr=c(ae).withConfig({displayName:"DirectionBadge",componentId:"sc-1v0tsx0-2"})(["text-transform:capitalize;font-weight:600;min-width:60px;justify-content:center;"]),Mr=c(ae).withConfig({displayName:"StatusBadge",componentId:"sc-1v0tsx0-3"})(["text-transform:capitalize;font-weight:600;min-width:70px;justify-content:center;"]),ue=c.span.withConfig({displayName:"ProfitLoss",componentId:"sc-1v0tsx0-4"})(["color:",";font-weight:",";font-size:",";"],({theme:e,$value:t})=>{var n,o,s;return t>0?((n=e.colors)==null?void 0:n.success)||"var(--success-color)":t<0?((o=e.colors)==null?void 0:o.error)||"var(--error-color)":((s=e.colors)==null?void 0:s.textSecondary)||"var(--text-secondary)"},({$value:e})=>e!==0?600:400,({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"}),Pr=c.span.withConfig({displayName:"PriceRange",componentId:"sc-1v0tsx0-5"})(["font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:",";color:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),$r=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-1v0tsx0-6"})(["display:flex;flex-wrap:wrap;gap:",";max-width:150px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"}),Nr=c(F).withConfig({displayName:"SymbolCell",componentId:"sc-1v0tsx0-7"})(["font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-weight:600;color:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),Er=c(F).withConfig({displayName:"DateCell",componentId:"sc-1v0tsx0-8"})(["font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:",";color:",";min-width:140px;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),De=we.memo(({trade:e,isSelected:t,onClick:n,formatters:o,handlers:s})=>{var m,g;const{formatDate:a,formatCurrency:i}=o,{getDirectionVariant:l,getStatusVariant:d}=s,p=y=>y>0?"win":y<0?"loss":"breakeven";return r.jsxs(Ir,{$isSelected:t,onClick:n,role:"button",tabIndex:0,onKeyDown:y=>{(y.key==="Enter"||y.key===" ")&&(y.preventDefault(),n())},"aria-selected":t,title:`Trade ${e.symbol} - Click to ${t?"deselect":"select"}`,children:[r.jsx(Er,{children:a(e.date)}),r.jsx(Nr,{children:e.symbol}),r.jsx(F,{children:r.jsx(Lr,{$direction:e.direction.toLowerCase(),variant:l(e.direction),size:"small",children:e.direction})}),r.jsx(F,{children:r.jsxs(Pr,{children:[e.entry," → ",e.exit]})}),r.jsx(F,{children:r.jsx(ue,{$value:e.profitLoss,children:i(e.profitLoss)})}),r.jsx(F,{children:r.jsx(ue,{$value:e.profitLoss||0,children:i(e.profitLoss||0)})}),r.jsx(F,{children:r.jsx(Mr,{$status:p(e.profitLoss),variant:d(p(e.profitLoss)),size:"small",children:p(e.profitLoss)})}),r.jsx(F,{children:e.strategy||"-"}),r.jsx(F,{children:r.jsxs($r,{children:[(m=e.tags)==null?void 0:m.slice(0,3).map((y,w)=>r.jsx(ee,{size:"small",variant:"default",children:y},w)),((g=e.tags)==null?void 0:g.length)>3&&r.jsxs(ee,{size:"small",variant:"secondary",children:["+",e.tags.length-3]})]})})]})});De.displayName="TradesTableRow";const ye=c.tbody.withConfig({displayName:"TableBody",componentId:"sc-1wq4aw0-0"})(["background:",";"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"}),Rr=c.tr.withConfig({displayName:"EmptyRow",componentId:"sc-1wq4aw0-1"})(["background:transparent;"]),zr=c.td.withConfig({displayName:"EmptyCell",componentId:"sc-1wq4aw0-2"})(["padding:",";text-align:center;color:",";font-style:italic;colspan:9;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Ie=we.memo(({trades:e,selectedTradeId:t,onRowClick:n,formatters:o,handlers:s})=>!e||e.length===0?r.jsx(ye,{children:r.jsx(Rr,{children:r.jsx(zr,{colSpan:9,children:"No trades found for the selected filters."})})}):r.jsx(ye,{children:e.map(a=>r.jsx(De,{trade:a,isSelected:a.id===t,onClick:()=>n(a.id),formatters:o,handlers:s},a.id))}));Ie.displayName="TradesTableBody";const Fr=e=>{const[t,n]=C.useState("entryTime"),[o,s]=C.useState("desc"),a=C.useCallback(p=>{t===p?s(m=>m==="asc"?"desc":"asc"):(n(p),s("desc"))},[t]),i=C.useMemo(()=>!e||e.length===0?[]:[...e].sort((p,m)=>{let g=0;switch(t){case"entryTime":g=new Date(p.date).getTime()-new Date(m.date).getTime();break;case"symbol":g=p.symbol.localeCompare(m.symbol);break;case"direction":g=p.direction.localeCompare(m.direction);break;case"profitLoss":g=p.profitLoss-m.profitLoss;break;case"profitLossPercent":g=p.profitLoss-m.profitLoss;break;case"status":const y=p.profitLoss>0?"win":p.profitLoss<0?"loss":"breakeven",w=m.profitLoss>0?"win":m.profitLoss<0?"loss":"breakeven";g=y.localeCompare(w);break;default:g=0}return o==="asc"?g:-g}),[e,t,o]),l=C.useMemo(()=>({formatDate:p=>{try{const m=new Date(p);return isNaN(m.getTime())?"Invalid Date":m.toLocaleDateString("en-US",{month:"short",day:"numeric",year:"2-digit"})+" "+m.toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!1})}catch(m){return console.warn("Error formatting date:",p,m),"Invalid Date"}},formatCurrency:p=>{try{return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(p)}catch(m){return console.warn("Error formatting currency:",p,m),`$${p.toFixed(2)}`}},formatPercent:p=>{try{return`${p>0?"+":""}${p.toFixed(2)}%`}catch(m){return console.warn("Error formatting percent:",p,m),`${p}%`}}}),[]),d=C.useMemo(()=>({getDirectionVariant:p=>{switch(p.toLowerCase()){case"long":return"success";case"short":return"error";default:return"default"}},getStatusVariant:p=>{switch(p.toLowerCase()){case"win":return"success";case"loss":return"error";case"breakeven":return"warning";case"open":return"info";default:return"default"}}}),[]);return{sortedTrades:i,sortField:t,sortDirection:o,handleSort:a,formatters:l,handlers:d}},Ar=c.div.withConfig({displayName:"Container",componentId:"sc-hvot2l-0"})(["display:flex;flex-direction:column;height:100%;background:",";border-radius:",";border:1px solid ",";overflow:hidden;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),_r=c.div.withConfig({displayName:"TableWrapper",componentId:"sc-hvot2l-1"})(["flex:1;overflow:auto;position:relative;"]),Wr=c.table.withConfig({displayName:"Table",componentId:"sc-hvot2l-2"})(["width:100%;border-collapse:collapse;font-size:",";background:",";"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"}),Le=c.div.withConfig({displayName:"EmptyState",componentId:"sc-hvot2l-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;color:",";min-height:200px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Me=c.div.withConfig({displayName:"EmptyIcon",componentId:"sc-hvot2l-4"})(["font-size:48px;margin-bottom:",";opacity:0.5;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),Pe=c.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-hvot2l-5"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),$e=c.p.withConfig({displayName:"EmptyDescription",componentId:"sc-hvot2l-6"})(["font-size:",";color:",";margin:0;max-width:300px;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Or=()=>r.jsxs(Le,{children:[r.jsx(Me,{children:"⏳"}),r.jsx(Pe,{children:"Loading Trades"}),r.jsx($e,{children:"Please wait while we load your trading data..."})]}),Br=()=>{const{data:e,selectedTradeId:t,selectTrade:n}=B(),{sortedTrades:o,sortField:s,sortDirection:a,handleSort:i,formatters:l,handlers:d}=Fr((e==null?void 0:e.trades)||[]);if(!e||!e.trades||e.trades.length===0)return r.jsxs(Le,{children:[r.jsx(Me,{children:"📊"}),r.jsx(Pe,{children:"No Trades Found"}),r.jsx($e,{children:"No trades match your current filters. Try adjusting your search criteria or add some trades to get started."})]});const p=m=>{n(m===t?null:m)};return r.jsx(_r,{children:r.jsxs(Wr,{children:[r.jsx(Dr,{sortField:s,sortDirection:a,onSort:i}),r.jsx(Ie,{trades:o,selectedTradeId:t,onRowClick:p,formatters:l,handlers:d})]})})},qr=({className:e})=>r.jsx(Ar,{className:e,children:r.jsx(C.Suspense,{fallback:r.jsx(Or,{}),children:r.jsx(Br,{})})}),Hr=e=>r.jsx(qr,{...e}),Ur=c.div.withConfig({displayName:"Container",componentId:"sc-aabl70-0"})(["overflow-x:auto;"]),Vr=c.table.withConfig({displayName:"Table",componentId:"sc-aabl70-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e})=>e.fontSizes.sm),Yr=c.thead.withConfig({displayName:"TableHead",componentId:"sc-aabl70-2"})(["background-color:",";"],({theme:e})=>e.colors.background),Gr=c.tbody.withConfig({displayName:"TableBody",componentId:"sc-aabl70-3"})([""]),xe=c.tr.withConfig({displayName:"TableRow",componentId:"sc-aabl70-4"})(["border-bottom:1px solid ",";&:hover{background-color:",";}"],({theme:e})=>e.colors.border,({theme:e})=>e.colors.background),V=c.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-aabl70-5"})(["padding:",";text-align:left;font-weight:",";color:",";cursor:",";&:hover{","}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontWeights.semibold,({theme:e,active:t})=>t?e.colors.primary:e.colors.textPrimary,({sortable:e})=>e?"pointer":"default",({sortable:e,theme:t})=>e&&`
      color: ${t.colors.primary};
    `),Y=c.td.withConfig({displayName:"TableCell",componentId:"sc-aabl70-6"})(["padding:",";"],({theme:e})=>e.spacing.sm),G=c.span.withConfig({displayName:"SortIcon",componentId:"sc-aabl70-7"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":"↓"),Jr=c.div.withConfig({displayName:"BarContainer",componentId:"sc-aabl70-8"})(["height:8px;background-color:",";border-radius:",";overflow:hidden;margin-top:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.pill,({theme:e})=>e.spacing.xs),Kr=c.div.withConfig({displayName:"Bar",componentId:"sc-aabl70-9"})(["height:100%;width:",";background-color:",";"],({width:e})=>`${e}%`,({theme:e,positive:t})=>t?e.colors.profit:e.colors.loss),be=c.span.withConfig({displayName:"ProfitLoss",componentId:"sc-aabl70-10"})(["color:",";font-weight:",";"],({theme:e,value:t})=>t>0?e.colors.profit:t<0?e.colors.loss:e.colors.textSecondary,({theme:e,value:t})=>t!==0?e.fontWeights.medium:e.fontWeights.regular),Xr=c.div.withConfig({displayName:"EmptyState",componentId:"sc-aabl70-11"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),Q=({className:e,category:t,title:n})=>{const{data:o}=B(),[s,a]=C.useState("profitLoss"),[i,l]=C.useState("desc");if(!o)return null;let d=[];switch(t){case"symbol":d=o.symbolPerformance;break;case"strategy":d=o.strategyPerformance;break;case"timeframe":d=o.timeframePerformance;break;case"session":d=o.sessionPerformance;break}if(!d||d.length===0)return r.jsxs(Xr,{children:["No ",t," performance data available."]});const p=h=>{s===h?l(i==="asc"?"desc":"asc"):(a(h),l("desc"))},m=[...d].sort((h,v)=>{let b=0;switch(s){case"value":b=h.value.localeCompare(v.value);break;case"trades":b=h.trades-v.trades;break;case"winRate":b=h.winRate-v.winRate;break;case"profitLoss":b=h.profitLoss-v.profitLoss;break;case"averageProfitLoss":b=h.averageProfitLoss-v.averageProfitLoss;break;default:b=0}return i==="asc"?b:-b}),g=Math.max(...d.map(h=>Math.abs(h.profitLoss))),y=h=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(h),w=h=>`${h.toFixed(2)}%`;return r.jsx(Ur,{className:e,children:r.jsxs(Vr,{children:[r.jsx(Yr,{children:r.jsxs(xe,{children:[r.jsxs(V,{sortable:!0,active:s==="value",onClick:()=>p("value"),children:[n,s==="value"&&r.jsx(G,{direction:i})]}),r.jsxs(V,{sortable:!0,active:s==="trades",onClick:()=>p("trades"),children:["Trades",s==="trades"&&r.jsx(G,{direction:i})]}),r.jsxs(V,{sortable:!0,active:s==="winRate",onClick:()=>p("winRate"),children:["Win Rate",s==="winRate"&&r.jsx(G,{direction:i})]}),r.jsxs(V,{sortable:!0,active:s==="profitLoss",onClick:()=>p("profitLoss"),children:["P&L",s==="profitLoss"&&r.jsx(G,{direction:i})]}),r.jsxs(V,{sortable:!0,active:s==="averageProfitLoss",onClick:()=>p("averageProfitLoss"),children:["Avg P&L",s==="averageProfitLoss"&&r.jsx(G,{direction:i})]})]})}),r.jsx(Gr,{children:m.map((h,v)=>r.jsxs(xe,{children:[r.jsx(Y,{children:h.value}),r.jsx(Y,{children:h.trades}),r.jsx(Y,{children:w(h.winRate)}),r.jsxs(Y,{children:[r.jsx(be,{value:h.profitLoss,children:y(h.profitLoss)}),r.jsx(Jr,{children:r.jsx(Kr,{width:Math.min(100,Math.abs(h.profitLoss)/g*100),positive:h.profitLoss>=0})})]}),r.jsx(Y,{children:r.jsx(be,{value:h.averageProfitLoss,children:y(h.averageProfitLoss)})})]},v))})]})})},Qr=c.div.withConfig({displayName:"Container",componentId:"sc-1khhabm-0"})([""]),Zr=c.div.withConfig({displayName:"ChartContainer",componentId:"sc-1khhabm-1"})(["display:flex;flex-direction:column;gap:",";margin-top:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md),eo=c.div.withConfig({displayName:"TimeSlot",componentId:"sc-1khhabm-2"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),to=c.div.withConfig({displayName:"TimeSlotHeader",componentId:"sc-1khhabm-3"})(["display:flex;justify-content:space-between;align-items:center;"]),ro=c.div.withConfig({displayName:"TimeSlotLabel",componentId:"sc-1khhabm-4"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),oo=c.div.withConfig({displayName:"TimeSlotMetrics",componentId:"sc-1khhabm-5"})(["display:flex;gap:",";font-size:",";color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textSecondary),se=c.span.withConfig({displayName:"MetricValue",componentId:"sc-1khhabm-6"})(["color:",";font-weight:",";"],({theme:e,positive:t,negative:n})=>t?e.colors.profit:n?e.colors.loss:e.colors.textSecondary,({theme:e})=>e.fontWeights.medium),no=c.div.withConfig({displayName:"BarContainer",componentId:"sc-1khhabm-7"})(["height:24px;background-color:",";border-radius:",";overflow:hidden;position:relative;"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm),so=c.div.withConfig({displayName:"Bar",componentId:"sc-1khhabm-8"})(["height:100%;width:",";background-color:",";transition:width 0.3s ease;"],({width:e})=>`${e}%`,({theme:e,positive:t})=>t?e.colors.profit:e.colors.loss),ao=c.div.withConfig({displayName:"BarLabel",componentId:"sc-1khhabm-9"})(["position:absolute;top:0;left:",";height:100%;display:flex;align-items:center;font-size:",";color:",";font-weight:",";text-shadow:0 0 2px rgba(0,0,0,0.5);"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.textInverse,({theme:e})=>e.fontWeights.medium),io=c.div.withConfig({displayName:"EmptyState",componentId:"sc-1khhabm-10"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),Z=({className:e,timeType:t})=>{const{data:n}=B();if(!n)return null;const o=t==="timeOfDay"?n.timeOfDayPerformance:n.dayOfWeekPerformance;if(!o||o.length===0)return r.jsxs(io,{children:["No ",t," performance data available."]});const s=Math.max(...o.map(l=>Math.abs(l.profitLoss))),a=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(l),i=l=>`${l.toFixed(2)}%`;return r.jsx(Qr,{className:e,children:r.jsx(Zr,{children:o.map((l,d)=>r.jsxs(eo,{children:[r.jsxs(to,{children:[r.jsx(ro,{children:l.timeSlot}),r.jsxs(oo,{children:[r.jsxs("div",{children:["Trades: ",r.jsx(se,{children:l.trades})]}),r.jsxs("div",{children:["Win Rate:"," ",r.jsx(se,{positive:l.winRate>50,negative:l.winRate<50,children:i(l.winRate)})]}),r.jsxs("div",{children:["P&L:"," ",r.jsx(se,{positive:l.profitLoss>0,negative:l.profitLoss<0,children:a(l.profitLoss)})]})]})]}),r.jsx(no,{children:r.jsx(so,{width:Math.min(100,Math.abs(l.profitLoss)/s*100),positive:l.profitLoss>=0,children:l.profitLoss!==0&&r.jsx(ao,{children:a(l.profitLoss)})})})]},d))})})},co=c(ve).withConfig({displayName:"Container",componentId:"sc-1an4q20-0"})(["margin-top:",";"],({theme:e})=>e.spacing.md),lo=c.div.withConfig({displayName:"DetailGrid",componentId:"sc-1an4q20-1"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),R=c.div.withConfig({displayName:"DetailSection",componentId:"sc-1an4q20-2"})(["margin-bottom:",";"],({theme:e})=>e.spacing.md),M=c.div.withConfig({displayName:"DetailLabel",componentId:"sc-1an4q20-3"})(["font-size:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs),z=c.div.withConfig({displayName:"DetailValue",componentId:"sc-1an4q20-4"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),po=c.div.withConfig({displayName:"ProfitLoss",componentId:"sc-1an4q20-5"})(["font-size:",";font-weight:",";color:",";margin-bottom:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e,value:t})=>t>0?e.colors.profit:t<0?e.colors.loss:e.colors.textSecondary,({theme:e})=>e.spacing.sm),mo=c.div.withConfig({displayName:"TagsContainer",componentId:"sc-1an4q20-6"})(["display:flex;flex-wrap:wrap;gap:",";margin-top:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs),fo=c.div.withConfig({displayName:"Notes",componentId:"sc-1an4q20-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";white-space:pre-wrap;"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),go=c.div.withConfig({displayName:"EmptyState",componentId:"sc-1an4q20-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary),ho=({className:e})=>{const{data:t,selectedTradeId:n}=B();if(!t||!n)return null;const o=t.trades.find(l=>l.id===n);if(!o)return r.jsx(go,{children:"Trade not found."});const s=l=>{const d=new Date(l);return d.toLocaleDateString()+" "+d.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},a=l=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(l),i=l=>l==="long"?"success":"error";return r.jsxs(co,{className:e,title:"Trade Details",variant:"default",padding:"medium",children:[r.jsx(po,{value:o.profitLoss||0,children:a(o.profitLoss||0)}),r.jsxs(lo,{children:[r.jsxs(R,{children:[r.jsx(M,{children:"Direction"}),r.jsx(z,{children:r.jsx(ae,{variant:i(o.direction),size:"small",children:o.direction})})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Symbol"}),r.jsx(z,{children:o.symbol})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Entry Time"}),r.jsx(z,{children:s(o.date)})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Entry Price"}),r.jsxs(z,{children:["$",o.entry]})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Exit Price"}),r.jsxs(z,{children:["$",o.exit]})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Size"}),r.jsx(z,{children:o.size})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Strategy"}),r.jsx(z,{children:o.strategy})]}),r.jsxs(R,{children:[r.jsx(M,{children:"Strategy"}),r.jsx(z,{children:o.strategy})]})]}),o.tags&&o.tags.length>0&&r.jsxs(R,{children:[r.jsx(M,{children:"Tags"}),r.jsx(mo,{children:o.tags.map((l,d)=>r.jsx(ee,{variant:"info",size:"small",children:l},d))})]}),o.notes&&r.jsxs(fo,{children:[r.jsx(M,{children:"Notes"}),r.jsx(z,{children:o.notes})]})]})},uo=({activeTab:e,data:t,isLoading:n,error:o})=>{const{selectedTradeId:s}=B(),a=()=>{switch(e){case"summary":return r.jsxs(r.Fragment,{children:[r.jsx(L,{title:"Performance Summary",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.metrics),emptyMessage:"No performance data available for the selected filters.",children:r.jsx(br,{})}),r.jsx(L,{title:"Performance by Time of Day",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.timeOfDayPerformance)||t.timeOfDayPerformance.length===0,emptyMessage:"No time of day performance data available for the selected filters.",children:r.jsx(Z,{timeType:"timeOfDay",title:"Time of Day"})}),r.jsx(L,{title:"Performance by Day of Week",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.dayOfWeekPerformance)||t.dayOfWeekPerformance.length===0,emptyMessage:"No day of week performance data available for the selected filters.",children:r.jsx(Z,{timeType:"dayOfWeek",title:"Day of Week"})})]});case"trades":return r.jsxs(r.Fragment,{children:[r.jsx(L,{title:"Trades",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.trades)||t.trades.length===0,emptyMessage:"No trades available for the selected filters.",children:r.jsx(Hr,{})}),s&&r.jsx(ho,{})]});case"symbols":return r.jsx(L,{title:"Performance by Symbol",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.symbolPerformance)||t.symbolPerformance.length===0,emptyMessage:"No symbol performance data available for the selected filters.",children:r.jsx(Q,{category:"symbol",title:"Symbol"})});case"strategies":return r.jsx(L,{title:"Performance by Strategy",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.strategyPerformance)||t.strategyPerformance.length===0,emptyMessage:"No strategy performance data available for the selected filters.",children:r.jsx(Q,{category:"strategy",title:"Strategy"})});case"timeframes":return r.jsxs(r.Fragment,{children:[r.jsx(L,{title:"Performance by Timeframe",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.timeframePerformance)||t.timeframePerformance.length===0,emptyMessage:"No timeframe performance data available for the selected filters.",children:r.jsx(Q,{category:"timeframe",title:"Timeframe"})}),r.jsx(L,{title:"Performance by Session",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.sessionPerformance)||t.sessionPerformance.length===0,emptyMessage:"No session performance data available for the selected filters.",children:r.jsx(Q,{category:"session",title:"Session"})})]});case"time":return r.jsxs(r.Fragment,{children:[r.jsx(L,{title:"Performance by Time of Day",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.timeOfDayPerformance)||t.timeOfDayPerformance.length===0,emptyMessage:"No time of day performance data available for the selected filters.",children:r.jsx(Z,{timeType:"timeOfDay",title:"Time of Day"})}),r.jsx(L,{title:"Performance by Day of Week",isLoading:n,hasError:!!o,errorMessage:o||"",isEmpty:!(t!=null&&t.dayOfWeekPerformance)||t.dayOfWeekPerformance.length===0,emptyMessage:"No day of week performance data available for the selected filters.",children:r.jsx(Z,{timeType:"dayOfWeek",title:"Day of Week"})})]});default:return r.jsx(L,{title:"Unknown Tab",hasError:!0,errorMessage:`Unknown tab: ${e}`,children:r.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"var(--text-secondary)"},children:["Tab content not found: ",e]})})}};return r.jsx(r.Fragment,{children:a()})},yo=c.div.withConfig({displayName:"AnalysisLayout",componentId:"sc-y0wqjr-0"})(["display:flex;flex-direction:column;gap:",";max-width:1400px;margin:0 auto;padding:",";min-height:100vh;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"}),xo=c.div.withConfig({displayName:"ContentArea",componentId:"sc-y0wqjr-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"}),bo=c.div.withConfig({displayName:"TabContentContainer",componentId:"sc-y0wqjr-2"})(["animation:fadeIn 0.3s ease-in-out;@keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}"]),wo=c.div.withConfig({displayName:"ErrorFallback",componentId:"sc-y0wqjr-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";background:",";border-radius:",";border:1px solid ",";color:",";text-align:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),vo=c.button.withConfig({displayName:"RetryButton",componentId:"sc-y0wqjr-4"})(["padding:"," ",";background:",";color:white;border:none;border-radius:",";cursor:pointer;font-weight:",";transition:",";&:hover{background:",";transform:translateY(-1px);}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"16px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.sm)||"4px"},({theme:e})=>{var t;return((t=e.fontWeights)==null?void 0:t.medium)||"500"},({theme:e})=>{var t;return((t=e.transitions)==null?void 0:t.fast)||"all 0.2s ease"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primaryDark)||"var(--primary-dark)"}),Ne=()=>r.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"400px",gap:"16px"},children:[r.jsx(gt,{size:"lg"}),r.jsx("div",{style:{color:"var(--text-secondary)"},children:"Loading Trade Analysis..."})]}),Co=()=>{const{data:e,isLoading:t,error:n,preferences:o,updatePreferences:s,fetchData:a}=B(),i=l=>{s({defaultView:l})};return n?r.jsxs(wo,{children:[r.jsx("div",{children:"❌ Analysis Loading Error"}),r.jsx("div",{children:n}),r.jsx(vo,{onClick:a,children:"Retry Analysis"})]}):r.jsxs(yo,{children:[r.jsx(Zt,{onRefresh:a,isRefreshing:t}),r.jsx(yr,{}),r.jsx(nr,{activeTab:o.defaultView||"summary",onTabChange:i}),r.jsx(xo,{children:r.jsx(C.Suspense,{fallback:r.jsx(Ne,{}),children:r.jsx(bo,{children:r.jsx(uo,{activeTab:o.defaultView||"summary",data:e,isLoading:t,error:n})})})})]})},jo=({className:e})=>r.jsx("div",{className:e,children:r.jsx(C.Suspense,{fallback:r.jsx(Ne,{}),children:r.jsx(Co,{})})}),Mo=()=>r.jsx(Ht,{children:r.jsx(jo,{})});export{Mo as default};
//# sourceMappingURL=TradeAnalysis-ccd21b25.js.map
