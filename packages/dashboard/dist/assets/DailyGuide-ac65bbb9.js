import{j as n}from"./client-d6fc67cc.js";import{r as T}from"./react-25c2faed.js";import{s as d}from"./styled-components-00fe3932.js";import{t as ee}from"./tradeStorage-a5c0ed9a.js";import{C as K}from"./Card-1e58b487.js";const qe=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,He=(e,t=new Date)=>{const s=new Intl.DateTimeFormat("en",{timeZone:e,timeZoneName:"short"}).formatToParts(t).find(o=>o.type==="timeZoneName");return(s==null?void 0:s.value)||e},Ke=(e,t)=>{const i=t||qe(),[r,s]=e.split(":").map(Number),o=new Date,a=o.getFullYear(),m=String(o.getMonth()+1).padStart(2,"0"),l=String(o.getDate()).padStart(2,"0"),p=`${String(r).padStart(2,"0")}:${String(s).padStart(2,"0")}:00`,c=`${a}-${m}-${l}T${p}`,v=new Date(c),b=new Date,g=new Date(b.toLocaleString("en-US",{timeZone:"America/New_York"})),u=new Date(b.toLocaleString("en-US",{timeZone:i})).getTime()-g.getTime();return new Date(v.getTime()+u).toLocaleTimeString("en-GB",{timeZone:i,hour:"2-digit",minute:"2-digit",hour12:!1})},ae=e=>{const t=e||qe(),i=new Date,r=i.toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),s=i.toLocaleTimeString("en-GB",{timeZone:t,hour:"2-digit",minute:"2-digit",hour12:!1}),o=He("America/New_York",i),a=He(t,i);return{nyTime:r,localTime:s,nyTimezone:o,localTimezone:a,formatted:`${r} ${o} | ${s} ${a}`}},Nt=()=>{const t=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1});return kt(t)},xt=e=>{const t=Math.floor(e/60),i=e%60;let r="";return t>0?r=i>0?`${t}h ${i}m`:`${t}h`:r=`${i}m`,{totalMinutes:e,hours:t,minutes:i,formatted:r}},Ze=e=>{const i=new Date().toLocaleString("en-US",{timeZone:"America/New_York"}),r=new Date(i),[s,o]=e.split(":").map(Number),a=new Date(r);a.setHours(s,o,0,0),a<=r&&a.setDate(a.getDate()+1);const m=a.getTime()-r.getTime(),l=Math.floor(m/(1e3*60));return xt(l)},yt=(e,t,i)=>{const r=i||qe(),s=Ke(e,r),o=Ke(t,r),a=He(r);return{nyStart:e,nyEnd:t,localStart:s,localEnd:o,formatted:`${e}-${t} NY | ${s}-${o} ${a}`}},kt=e=>{const[t,i]=e.split(":").map(Number);return t*60+i},Mt=e=>{const t=e.localTimezone.includes("GMT")?"🇮🇪":"🌍";return`${e.localTime} ${t} | ${e.nyTime} 🇺🇸`},Z=d.div.withConfig({displayName:"TimeContainer",componentId:"sc-10dqpqu-0"})(["display:flex;align-items:center;gap:",";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"],({format:e})=>e==="mobile"?"4px":"8px"),re=d.span.withConfig({displayName:"NYTime",componentId:"sc-10dqpqu-1"})(["color:#3b82f6;font-size:inherit;"]),ie=d.span.withConfig({displayName:"LocalTime",componentId:"sc-10dqpqu-2"})(["color:#10b981;font-size:inherit;"]),se=d.span.withConfig({displayName:"Separator",componentId:"sc-10dqpqu-3"})(["color:#6b7280;font-size:inherit;"]),ve=d.span.withConfig({displayName:"Timezone",componentId:"sc-10dqpqu-4"})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]),be=d.span.withConfig({displayName:"LiveIndicator",componentId:"sc-10dqpqu-5"})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),Qe=d.div.withConfig({displayName:"CountdownContainer",componentId:"sc-10dqpqu-6"})(["display:flex;align-items:center;gap:8px;"]),Xe=d.span.withConfig({displayName:"CountdownValue",componentId:"sc-10dqpqu-7"})(["color:#f59e0b;font-weight:bold;"]),Se=d.span.withConfig({displayName:"CountdownLabel",componentId:"sc-10dqpqu-8"})(["color:#9ca3af;font-size:0.9em;"]),At=({format:e,showLive:t,updateInterval:i})=>{const[r,s]=T.useState(ae());return T.useEffect(()=>{const o=setInterval(()=>{s(ae())},i*1e3);return()=>clearInterval(o)},[i]),e==="mobile"?n.jsxs(Z,{format:e,children:[n.jsx("span",{children:Mt(r)}),t&&n.jsx(be,{children:"LIVE"})]}):e==="compact"?n.jsxs(Z,{format:e,children:[n.jsx(re,{children:r.nyTime}),n.jsx(se,{children:"|"}),n.jsx(ie,{children:r.localTime}),t&&n.jsx(be,{children:"LIVE"})]}):n.jsxs(Z,{format:e,children:[n.jsx(re,{children:r.nyTime}),n.jsx(ve,{children:r.nyTimezone}),n.jsx(se,{children:"|"}),n.jsx(ie,{children:r.localTime}),n.jsx(ve,{children:r.localTimezone}),t&&n.jsx(be,{children:"LIVE"})]})},Pt=({nyTime:e,format:t})=>{const i=ae(),r=yt(e,e);return t==="mobile"?n.jsx(Z,{format:t,children:n.jsxs("span",{children:[r.localStart," 🇮🇪 | ",e," 🇺🇸"]})}):t==="compact"?n.jsxs(Z,{format:t,children:[n.jsx(re,{children:e}),n.jsx(se,{children:"|"}),n.jsx(ie,{children:r.localStart})]}):n.jsxs(Z,{format:t,children:[n.jsx(re,{children:e}),n.jsx(ve,{children:i.nyTimezone}),n.jsx(se,{children:"|"}),n.jsx(ie,{children:r.localStart}),n.jsx(ve,{children:i.localTimezone})]})},Lt=({targetNYTime:e,format:t,updateInterval:i})=>{const[r,s]=T.useState(Ze(e));return T.useEffect(()=>{const o=setInterval(()=>{s(Ze(e))},i*1e3);return()=>clearInterval(o)},[e,i]),t==="mobile"?n.jsxs(Qe,{children:[n.jsx(Xe,{children:r.formatted}),n.jsxs(Se,{children:["until ",e]})]}):n.jsxs(Qe,{children:[n.jsx(Se,{children:"Next in:"}),n.jsx(Xe,{children:r.formatted}),n.jsxs(Se,{children:["(",e," NY)"]})]})},Ft=({sessionStart:e,sessionEnd:t,format:i})=>{const r=yt(e,t);return i==="mobile"?n.jsx(Z,{format:i,children:n.jsx("span",{children:r.formatted})}):i==="compact"?n.jsxs(Z,{format:i,children:[n.jsxs(re,{children:[e,"-",t]}),n.jsx(se,{children:"|"}),n.jsxs(ie,{children:[r.localStart,"-",r.localEnd]})]}):n.jsxs(Z,{format:i,children:[n.jsx("div",{children:n.jsxs(re,{children:[e,"-",t," NY"]})}),n.jsx(se,{children:"|"}),n.jsx("div",{children:n.jsxs(ie,{children:[r.localStart,"-",r.localEnd," Local"]})})]})},ue=e=>{const{mode:t="current",nyTime:i,targetNYTime:r,sessionStart:s,sessionEnd:o,format:a="desktop",showLive:m=!1,className:l,updateInterval:p=1}=e,c={className:l,style:{fontSize:a==="mobile"?"14px":a==="compact"?"13px":"14px"}};switch(t){case"static":return i?n.jsx("div",{...c,children:n.jsx(Pt,{nyTime:i,format:a})}):(console.warn("DualTimeDisplay: nyTime is required for static mode"),null);case"countdown":return r?n.jsx("div",{...c,children:n.jsx(Lt,{targetNYTime:r,format:a,updateInterval:p})}):(console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"),null);case"session":return!s||!o?(console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"),null):n.jsx("div",{...c,children:n.jsx(Ft,{sessionStart:s,sessionEnd:o,format:a})});case"current":default:return n.jsx("div",{...c,children:n.jsx(At,{format:a,showLive:m,updateInterval:p})})}};function zt(e,t,i="StoreContext"){const r=T.createContext(void 0);r.displayName=i;const s=({children:p,initialState:c})=>{const[v,b]=T.useReducer(e,c||t),g=T.useMemo(()=>({state:v,dispatch:b}),[v]);return n.jsx(r.Provider,{value:g,children:p})};function o(){const p=T.useContext(r);if(p===void 0)throw new Error(`use${i} must be used within a ${i}Provider`);return p}function a(p){const{state:c}=o();return p(c)}function m(p){const{dispatch:c}=o();return T.useMemo(()=>(...v)=>{c(p(...v))},[c,p])}function l(p){const{dispatch:c}=o();return T.useMemo(()=>{const v={};for(const b in p)v[b]=(...g)=>{c(p[b](...g))};return v},[c,p])}return{Context:r,Provider:s,useStore:o,useSelector:a,useAction:m,useActions:l}}function z(...e){const t=e.pop(),i=e;let r=null,s=null;return o=>{const a=i.map(m=>m(o));return(r===null||a.length!==r.length||a.some((m,l)=>m!==r[l]))&&(s=t(...a),r=a),s}}function Wt(e,t){const{key:i,initialState:r,version:s=1,migrate:o,serialize:a=JSON.stringify,deserialize:m=JSON.parse,filter:l=S=>S,merge:p=(S,j)=>({...j,...S}),debug:c=!1}=t,v=()=>{try{const S=localStorage.getItem(i);if(S===null)return null;const{state:j,version:f}=m(S);return f!==s&&o?(c&&console.log(`Migrating state from version ${f} to ${s}`),o(j,f)):j}catch(S){return c&&console.error("Error loading state from local storage:",S),null}},b=S=>{try{const j=l(S),f=a({state:j,version:s});localStorage.setItem(i,f)}catch(j){c&&console.error("Error saving state to local storage:",j)}},g=()=>{try{localStorage.removeItem(i)}catch(S){c&&console.error("Error clearing state from local storage:",S)}},y=v(),u=y?p(y,r):r;return c&&y&&(console.log("Loaded persisted state:",y),console.log("Merged initial state:",u)),{reducer:(S,j)=>{const f=e(S,j);return b(f),f},initialState:u,clear:g}}const vt={data:{marketOverview:null,tradingPlan:null,keyPriceLevels:[],watchlist:[],marketNews:[]},isLoading:!1,error:null,selectedDate:new Date().toISOString().split("T")[0]},_t=(e,t)=>{switch(t.type){case"dailyGuide/FETCH_DATA_START":return{...e,isLoading:!0,error:null};case"dailyGuide/FETCH_DATA_SUCCESS":return{...e,data:t.payload,isLoading:!1,error:null};case"dailyGuide/FETCH_DATA_ERROR":return{...e,isLoading:!1,error:t.payload};case"dailyGuide/UPDATE_TRADING_PLAN_ITEM":return e.data.tradingPlan?{...e,data:{...e.data,tradingPlan:{...e.data.tradingPlan,items:e.data.tradingPlan.items.map(i=>i.id===t.payload.id?{...i,completed:t.payload.completed}:i)}}}:e;case"dailyGuide/ADD_TRADING_PLAN_ITEM":return e.data.tradingPlan?{...e,data:{...e.data,tradingPlan:{...e.data.tradingPlan,items:[...e.data.tradingPlan.items,t.payload]}}}:{...e,data:{...e.data,tradingPlan:{items:[t.payload],strategy:"",riskManagement:{maxRiskPerTrade:0,maxDailyLoss:0,maxTrades:0,positionSizing:""},notes:""}}};case"dailyGuide/REMOVE_TRADING_PLAN_ITEM":return e.data.tradingPlan?{...e,data:{...e.data,tradingPlan:{...e.data.tradingPlan,items:e.data.tradingPlan.items.filter(i=>i.id!==t.payload)}}}:e;case"dailyGuide/UPDATE_SELECTED_DATE":return{...e,selectedDate:t.payload};case"dailyGuide/UPDATE_MARKET_OVERVIEW":return{...e,data:{...e.data,marketOverview:t.payload}};case"dailyGuide/UPDATE_KEY_PRICE_LEVELS":return{...e,data:{...e.data,keyPriceLevels:t.payload}};case"dailyGuide/RESET_STATE":return{...vt,selectedDate:e.selectedDate};default:return e}},{reducer:Ot,initialState:$t}=Wt(_t,{key:"dailyGuide",initialState:vt,version:1,filter:e=>({selectedDate:e.selectedDate})}),{Context:ts,Provider:Gt,useStore:ns,useSelector:$,useAction:rs,useActions:Ht}=zt(Ot,$t,"DailyGuideContext"),qt={fetchDataStart:()=>({type:"dailyGuide/FETCH_DATA_START"}),fetchDataSuccess:e=>({type:"dailyGuide/FETCH_DATA_SUCCESS",payload:e}),fetchDataError:e=>({type:"dailyGuide/FETCH_DATA_ERROR",payload:e}),updateTradingPlanItem:(e,t)=>({type:"dailyGuide/UPDATE_TRADING_PLAN_ITEM",payload:{id:e,completed:t}}),addTradingPlanItem:e=>({type:"dailyGuide/ADD_TRADING_PLAN_ITEM",payload:e}),removeTradingPlanItem:e=>({type:"dailyGuide/REMOVE_TRADING_PLAN_ITEM",payload:e}),updateSelectedDate:e=>({type:"dailyGuide/UPDATE_SELECTED_DATE",payload:e}),updateMarketOverview:e=>({type:"dailyGuide/UPDATE_MARKET_OVERVIEW",payload:e}),updateKeyPriceLevels:e=>({type:"dailyGuide/UPDATE_KEY_PRICE_LEVELS",payload:e}),resetState:()=>({type:"dailyGuide/RESET_STATE"})},Vt=e=>e.data,te=e=>e.data.marketOverview,Ve=e=>e.data.tradingPlan,Ue=e=>e.data.keyPriceLevels,wt=e=>e.data.watchlist,Be=e=>e.data.marketNews,Ut=e=>e.isLoading,Bt=e=>e.error,Yt=e=>e.selectedDate,ce=z(Ve,e=>(e==null?void 0:e.items)||[]),bt=z(ce,e=>e.filter(t=>t.completed)),Kt=z(ce,e=>e.filter(t=>!t.completed)),Zt=z(ce,bt,(e,t)=>e.length===0?0:t.length/e.length),Qt=z(ce,e=>{const t={high:[],medium:[],low:[]};return e.forEach(i=>{t[i.priority].push(i)}),t}),Ye=z(te,e=>(e==null?void 0:e.indices)||[]),Xt=z(Ye,e=>e.filter(t=>t.change>0)),Jt=z(Ye,e=>e.filter(t=>t.change<0)),en=z(te,e=>(e==null?void 0:e.sentiment)||"neutral"),tn=z(te,e=>(e==null?void 0:e.summary)||""),St=z(te,e=>(e==null?void 0:e.economicEvents)||[]),nn=z(St,e=>e.filter(t=>t.importance==="high")),rn=z(Ue,e=>{const t={};return e.forEach(i=>{t[i.symbol]=i}),t}),sn=z(wt,e=>{const t={};return e.forEach(i=>{t[i.symbol]=i}),t}),on=z(Be,e=>e.filter(t=>t.impact==="high")),an=z(Be,e=>{const t={};return e.forEach(i=>{const r=new Date(i.timestamp).toISOString().split("T")[0];t[r]||(t[r]=[]),t[r].push(i)}),t}),cn=z(te,Ve,Ue,(e,t,i)=>!!e||!!t||i.length>0),ln=z(te,e=>(e==null?void 0:e.lastUpdated)||null),G={selectDailyGuideData:Vt,selectMarketOverview:te,selectTradingPlan:Ve,selectKeyPriceLevels:Ue,selectWatchlist:wt,selectMarketNews:Be,selectIsLoading:Ut,selectError:Bt,selectSelectedDate:Yt,selectTradingPlanItems:ce,selectCompletedTradingPlanItems:bt,selectIncompleteTradingPlanItems:Kt,selectTradingPlanCompletion:Zt,selectTradingPlanItemsByPriority:Qt,selectMarketIndices:Ye,selectPositiveIndices:Xt,selectNegativeIndices:Jt,selectMarketSentiment:en,selectMarketSummary:tn,selectEconomicEvents:St,selectHighImpactEconomicEvents:nn,selectKeyPriceLevelsBySymbol:rn,selectWatchlistBySymbol:sn,selectHighImpactMarketNews:on,selectMarketNewsByDate:an,selectHasData:cn,selectLastUpdated:ln},dn=()=>{const e={sentiment:["bullish","bearish","neutral"][Math.floor(Math.random()*3)],summary:"Markets are showing mixed signals with tech stocks outperforming the broader indices. Watch for resistance at key levels.",indices:[{symbol:"SPY",name:"S&P 500",value:4500+Math.random()*100,change:Math.random()*2-1,changePercent:(Math.random()*2-1)/100},{symbol:"QQQ",name:"Nasdaq",value:14e3+Math.random()*500,change:Math.random()*2-1,changePercent:(Math.random()*2-1)/100},{symbol:"DIA",name:"Dow Jones",value:35e3+Math.random()*500,change:Math.random()*2-1,changePercent:(Math.random()*2-1)/100}],economicEvents:[{title:"Fed Interest Rate Decision",time:"14:00",importance:"high",expected:"5.25%",previous:"5.25%"},{title:"Unemployment Claims",time:"08:30",importance:"medium",expected:"235K",previous:"240K",actual:"232K"},{title:"GDP Growth Rate",time:"08:30",importance:"high",expected:"2.1%",previous:"2.0%"}],news:[{id:"1",title:"Fed signals potential rate cuts in upcoming meeting",source:"Bloomberg",timestamp:new Date().toISOString(),url:"https://example.com/news/1",impact:"high"},{id:"2",title:"Tech stocks rally on positive earnings surprises",source:"CNBC",timestamp:new Date().toISOString(),url:"https://example.com/news/2",impact:"medium"},{id:"3",title:"Oil prices drop on increased supply concerns",source:"Reuters",timestamp:new Date().toISOString(),url:"https://example.com/news/3",impact:"medium"}],lastUpdated:new Date().toISOString()};return{marketOverview:e,tradingPlan:{items:[{id:"1",description:"Wait for market open before placing any trades",priority:"high",completed:!1},{id:"2",description:"Focus on tech sector for long opportunities",priority:"medium",completed:!1},{id:"3",description:"Use tight stop losses due to expected volatility",priority:"high",completed:!1},{id:"4",description:"Review earnings reports for potential opportunities",priority:"medium",completed:!1},{id:"5",description:"Avoid over-trading in the first hour",priority:"low",completed:!1}],strategy:"Focus on momentum plays in tech sector with tight risk management",riskManagement:{maxRiskPerTrade:1,maxDailyLoss:3,maxTrades:5,positionSizing:"2% of account per trade"},notes:"Market is showing signs of volatility. Be cautious and wait for clear setups."},keyPriceLevels:[{symbol:"SPY",support:["450.00","445.75","442.30"],resistance:["455.50","460.00","462.75"],pivotPoint:"452.25"},{symbol:"QQQ",support:["365.20","360.00","355.50"],resistance:["370.00","375.35","380.00"],pivotPoint:"367.50"},{symbol:"AAPL",support:["175.00","170.50","165.75"],resistance:["180.00","185.50","190.25"],pivotPoint:"177.25"}],watchlist:[{symbol:"AAPL",name:"Apple Inc.",price:175.5,reason:"Earnings report coming up",setup:"Breakout above 180",entryPrice:180.25,stopLoss:175,takeProfit:190},{symbol:"MSFT",name:"Microsoft Corp.",price:340.75,reason:"Strong technical pattern",setup:"Bull flag on daily chart",entryPrice:342.5,stopLoss:335,takeProfit:355},{symbol:"NVDA",name:"NVIDIA Corp.",price:450.25,reason:"AI momentum continues",setup:"Pullback to support",entryPrice:445,stopLoss:435,takeProfit:475}],marketNews:e.news}};function pn(){const e=Ht(qt),t=$(G.selectSelectedDate),i=$(G.selectMarketOverview),r=$(G.selectTradingPlan),s=$(G.selectKeyPriceLevels),o=$(G.selectWatchlist),a=$(G.selectMarketNews),m=$(G.selectIsLoading),l=$(G.selectError),p=$(G.selectTradingPlanItems),c=$(G.selectTradingPlanCompletion),v=$(G.selectMarketSentiment),b=$(G.selectMarketSummary),g=$(G.selectLastUpdated),u=new Date(t).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),x=T.useCallback(async()=>{try{e.fetchDataStart(),await new Promise(E=>setTimeout(E,800));const I=dn();e.fetchDataSuccess(I)}catch(I){const E=I instanceof Error?I.message:"Unknown error";console.error("Error fetching daily guide data:",I),e.fetchDataError(E)}},[e]);T.useEffect(()=>{x()},[x]),T.useEffect(()=>{x()},[t,x]);const S=T.useCallback(I=>{e.updateSelectedDate(I)},[e]),j=T.useCallback((I,E)=>{e.updateTradingPlanItem(I,E)},[e]),f=T.useCallback(I=>{e.addTradingPlanItem(I)},[e]),h=T.useCallback(I=>{e.removeTradingPlanItem(I)},[e]),w=T.useCallback(()=>{x()},[x]);return{selectedDate:t,marketOverview:i,tradingPlan:r,keyPriceLevels:s,watchlist:o,marketNews:a,isLoading:m,error:l,tradingPlanItems:p,tradingPlanCompletion:c,marketSentiment:v,marketSummary:b,lastUpdated:g,currentDate:u,onDateChange:S,onTradingPlanItemToggle:j,onAddTradingPlanItem:f,onRemoveTradingPlanItem:h,onRefresh:w}}const Ce=()=>{const e=new Date,t=e.getTime()+e.getTimezoneOffset()*6e4;return new Date(t+-5*36e5).getHours()},Ie=e=>e>=4&&e<9?"Pre-Market":e>=9&&e<11?"NY Open":e>=11&&e<14?"Mid-Day":e>=14&&e<16?"NY Close":e>=16&&e<20?"After Hours":"Overnight",mn=(e,t)=>t<3?"average":e>=70?"excellent":e>=60?"good":e>=45?"average":e>=30?"poor":"avoid",un=e=>{switch(e){case"excellent":return"high";case"good":return"medium";case"average":return"medium";case"poor":return"low";case"avoid":return"avoid";default:return"medium"}},gn=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);return T.useEffect(()=>{(async()=>{try{r(!0),o(null);const l=await ee.getAllTrades();t(l)}catch(l){console.error("Error fetching trades for session analytics:",l),o("Failed to load trade data")}finally{r(!1)}})()},[]),{analytics:T.useMemo(()=>{if(e.length===0)return{sessionPerformance:[],currentRecommendation:{currentHour:Ce(),sessionLabel:Ie(Ce()),recommendation:"medium",winRate:0,bestSetups:[],riskLevel:"medium",actionItems:["Insufficient data for recommendations"]},bestPerformingHours:[],worstPerformingHours:[],totalAnalyzedTrades:0,lastUpdated:new Date};const m={};e.forEach(f=>{if(f.trade.entry_time){const h=parseInt(f.trade.entry_time.split(":")[0]);m[h]||(m[h]=[]),m[h].push(f)}});const l=[];for(let f=0;f<24;f++){const h=m[f]||[],w=h.filter(C=>C.trade.win_loss==="Win"),I=h.length>0?w.length/h.length*100:0,E=h.length>0?h.reduce((C,N)=>C+(N.trade.r_multiple||0),0)/h.length:0,F=h.reduce((C,N)=>C+(N.trade.achieved_pl||0),0),D={};h.forEach(C=>{const N=C.trade.model_type||"Unknown";D[N]=(D[N]||0)+1});const A=Object.keys(D).reduce((C,N)=>D[C]>D[N]?C:N,"None");l.push({hour:f,label:Ie(f),winRate:I,totalTrades:h.length,avgRMultiple:E,totalPnL:F,bestSetup:A,performance:mn(I,h.length)})}const p=Ce(),c=l[p],v=m[p]||[],b={};v.forEach(f=>{const h=f.trade.model_type||"Unknown";b[h]||(b[h]={wins:0,total:0}),b[h].total++,f.trade.win_loss==="Win"&&b[h].wins++});const g=Object.entries(b).filter(([f,h])=>h.total>=2).sort(([f,h],[w,I])=>I.wins/I.total-h.wins/h.total).slice(0,3).map(([f])=>f),y=[];c.performance==="excellent"?(y.push("🎯 Prime trading time - consider larger position sizes"),y.push("📈 Focus on your best setups")):c.performance==="good"?(y.push("✅ Good performance window - trade with confidence"),y.push("🎯 Stick to proven setups")):c.performance==="poor"||c.performance==="avoid"?(y.push("⚠️ Low performance period - reduce position sizes"),y.push("🛡️ Focus on risk management"),y.push("📚 Consider paper trading or review")):(y.push("📊 Average performance - trade with standard risk"),y.push("🎯 Focus on high-probability setups"));const u={currentHour:p,sessionLabel:Ie(p),recommendation:un(c.performance),winRate:c.winRate,bestSetups:g,riskLevel:c.performance==="excellent"||c.performance==="good"?"low":c.performance==="average"?"medium":"high",actionItems:y},x=l.filter(f=>f.totalTrades>=3).sort((f,h)=>h.winRate-f.winRate),S=x.slice(0,3).map(f=>f.hour),j=x.slice(-3).map(f=>f.hour);return{sessionPerformance:l,currentRecommendation:u,bestPerformingHours:S,worstPerformingHours:j,totalAnalyzedTrades:e.length,lastUpdated:new Date}},[e]),isLoading:i,error:s,refresh:()=>{t([])}}},fn=d.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1lqkc4h-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"}),hn=d.div.withConfig({displayName:"F1Header",componentId:"sc-1lqkc4h-1"})(["display:flex;align-items:center;justify-content:space-between;padding:",";background:linear-gradient( 135deg,"," 0%,rgba(75,85,99,0.1) 100% );border:1px solid ",";border-radius:",";position:relative;overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,"," 0%,transparent 100% );}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),xn=d.h1.withConfig({displayName:"F1Title",componentId:"sc-1lqkc4h-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:2px;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;span{color:",";font-weight:800;}"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.h2)||"1.5rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),yn=d.div.withConfig({displayName:"GuideIndicator",componentId:"sc-1lqkc4h-3"})(["display:flex;align-items:center;gap:",";color:",";font-weight:700;text-transform:uppercase;letter-spacing:1px;font-size:",";padding:"," ",";border-radius:",";border:1px solid ",";background:",";&::before{content:'📅';font-size:12px;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$hasDate:e,theme:t})=>{var i,r;return e?((i=t.colors)==null?void 0:i.success)||"var(--success-color)":((r=t.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.full)||"9999px"},({$hasDate:e,theme:t})=>{var i,r;return e?((i=t.colors)==null?void 0:i.success)||"var(--success-color)":((r=t.colors)==null?void 0:r.border)||"var(--border-primary)"},({$hasDate:e,theme:t})=>{var i;return e?`${((i=t.colors)==null?void 0:i.success)||"var(--success-color)"}20`:"transparent"}),vn=d.div.withConfig({displayName:"SubHeader",componentId:"sc-1lqkc4h-4"})(["display:flex;justify-content:space-between;align-items:center;padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),wn=d.div.withConfig({displayName:"TitleSection",componentId:"sc-1lqkc4h-5"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),bn=d.h2.withConfig({displayName:"SubTitle",componentId:"sc-1lqkc4h-6"})(["font-size:",";margin:0;color:",";font-weight:600;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xxl)||"1.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"}),Sn=d.span.withConfig({displayName:"DateBadge",componentId:"sc-1lqkc4h-7"})(["background:",";color:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;border:1px solid ",";text-transform:uppercase;letter-spacing:0.5px;"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xxs)||"2px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.full)||"9999px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),Cn=d.div.withConfig({displayName:"ActionsContainer",componentId:"sc-1lqkc4h-8"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),In=d.div.withConfig({displayName:"DateSelector",componentId:"sc-1lqkc4h-9"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Tn=d.input.withConfig({displayName:"DateInput",componentId:"sc-1lqkc4h-10"})(["padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";font-weight:500;cursor:pointer;transition:all 0.2s ease;min-width:140px;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;transform:translateY(-1px);}&::-webkit-calendar-picker-indicator{filter:invert(1);cursor:pointer;}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"var(--bg-primary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),jn=d.button.withConfig({displayName:"ActionButton",componentId:"sc-1lqkc4h-11"})(["background:",";color:",";border:1px solid ",";border-radius:",";padding:"," ",";font-size:",";font-weight:600;cursor:pointer;display:flex;align-items:center;gap:",";transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;min-width:100px;justify-content:center;&:hover:not(:disabled){background:",";transform:translateY(-1px);box-shadow:0 4px 8px ",";}&:active:not(:disabled){transform:translateY(0);}&:disabled{opacity:0.5;cursor:not-allowed;}"],({$variant:e,theme:t})=>{var i;return e==="primary"?((i=t.colors)==null?void 0:i.primary)||"var(--primary-color)":"transparent"},({$variant:e,theme:t})=>{var i,r;return e==="primary"?((i=t.colors)==null?void 0:i.textInverse)||"#ffffff":((r=t.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"},({$variant:e,theme:t})=>{var i,r;return e==="primary"?((i=t.colors)==null?void 0:i.primary)||"var(--primary-color)":((r=t.colors)==null?void 0:r.border)||"var(--border-primary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({$variant:e,theme:t})=>{var i,r;return e==="primary"?((i=t.colors)==null?void 0:i.primaryDark)||"var(--primary-dark)":((r=t.colors)==null?void 0:r.surface)||"var(--bg-secondary)"},({$variant:e,theme:t})=>{var i;return e==="primary"?`${((i=t.colors)==null?void 0:i.primary)||"var(--primary-color)"}40`:"rgba(0, 0, 0, 0.1)"}),Rn=({className:e,isLoading:t=!1,isRefreshing:i=!1,currentDate:r,selectedDate:s,onDateChange:o,onRefresh:a,title:m="Daily Trading Guide"})=>{const l=!!r,p=new Date().toISOString().split("T")[0],c=v=>{o&&o(v.target.value)};return n.jsxs(fn,{className:e,children:[n.jsxs(hn,{children:[n.jsxs(xn,{children:["🏎️ DAILY ",n.jsx("span",{children:"GUIDE"})]}),n.jsx(yn,{$hasDate:l,children:l?r:"NO DATE"})]}),n.jsxs(vn,{children:[n.jsxs(wn,{children:[n.jsx(bn,{children:m}),l&&n.jsxs(Sn,{children:["📅 ",r]})]}),n.jsxs(Cn,{children:[n.jsx(In,{children:n.jsx(Tn,{type:"date",value:s||"",onChange:c,max:p,title:"Select date for trading guide"})}),a&&n.jsxs(jn,{$variant:"primary",onClick:a,disabled:t,title:t?"Refreshing guide...":"Refresh guide data",children:[t||i?"⏳":"🔄",t?"Refreshing":"Refresh"]})]})]})]})},Dn=d.div.withConfig({displayName:"TabsContainer",componentId:"sc-1hlloa2-0"})(["display:flex;gap:0;margin:"," 0 "," 0;border-bottom:1px solid ",";position:relative;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"32px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),En=d.button.withConfig({displayName:"Tab",componentId:"sc-1hlloa2-1"})(["padding:"," ",";border:none;background:transparent;color:",";cursor:",";transition:all 0.2s ease;font-weight:",";font-size:",";position:relative;border-bottom:2px solid transparent;text-transform:uppercase;letter-spacing:0.025em;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;display:flex;align-items:center;gap:",";&::after{content:'';position:absolute;bottom:-1px;left:0;right:0;height:2px;background:",";transform:scaleX(",");transition:transform 0.2s ease;transform-origin:center;}&:hover:not(:disabled){color:",";transform:translateY(-1px);&::after{transform:scaleX(1);background:",";}}&:active:not(:disabled){transform:translateY(0);}"," @media (max-width:768px){padding:"," ",";font-size:",";}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({$isActive:e,theme:t})=>{var i,r;return e?((i=t.colors)==null?void 0:i.textPrimary)||"#ffffff":((r=t.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"},({$disabled:e})=>e?"not-allowed":"pointer",({$isActive:e})=>e?"600":"400",({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.md)||"1rem"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xs)||"4px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({$isActive:e})=>e?1:0,({$isActive:e,theme:t})=>{var i,r;return e?((i=t.colors)==null?void 0:i.textPrimary)||"#ffffff":((r=t.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$isActive:e,theme:t})=>{var i,r;return e?((i=t.colors)==null?void 0:i.primary)||"var(--primary-color)":((r=t.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"},({$disabled:e})=>e&&`
    opacity: 0.5;
    cursor: not-allowed;
  `,({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"}),Nn=d.span.withConfig({displayName:"TabIcon",componentId:"sc-1hlloa2-2"})(["font-size:16px;@media (max-width:768px){font-size:14px;}"]),kn=d.span.withConfig({displayName:"TabLabel",componentId:"sc-1hlloa2-3"})(["@media (max-width:768px){display:none;}"]),Je={overview:{icon:"🕘",label:"Session Focus",description:"Enhanced ICT session intelligence and timing analysis"},plan:{icon:"🧠",label:"Elite Intelligence",description:"Advanced ICT trading intelligence with model selection and pattern scoring"},levels:{icon:"🎯",label:"PD Array Levels",description:"ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis"},news:{icon:"📰",label:"Market News",description:"Latest market news and events"}},Mn=({activeTab:e,onTabChange:t,disabled:i=!1,className:r})=>{const s=a=>{i||t(a)},o=(a,m)=>{(a.key==="Enter"||a.key===" ")&&!i&&(a.preventDefault(),t(m))};return n.jsx(Dn,{className:r,role:"tablist",children:Object.keys(Je).map(a=>{const m=Je[a],l=e===a;return n.jsxs(En,{$isActive:l,$disabled:i,onClick:()=>s(a),onKeyDown:p=>o(p,a),disabled:i,role:"tab","aria-selected":l,"aria-controls":`guide-panel-${a}`,tabIndex:i?-1:0,title:m.description,children:[n.jsx(Nn,{children:m.icon}),n.jsx(kn,{children:m.label})]},a)})})},B=["overview","plan","levels","news"],An="adhd-trading-dashboard:guide:active-tab",Pn=(e,t)=>{try{const i=localStorage.getItem(e);if(i&&B.includes(i))return i}catch(i){console.warn("Failed to load guide tab from localStorage:",i)}return t},Ln=(e,t)=>{try{localStorage.setItem(e,t)}catch(i){console.warn("Failed to save guide tab to localStorage:",i)}},Fn=({defaultTab:e="overview",storageKey:t=An}={})=>{const[i,r]=T.useState(()=>Pn(t,e)),s=T.useCallback(p=>{B.includes(p)&&(r(p),Ln(t,p))},[t]),o=T.useCallback(()=>{const c=(B.indexOf(i)+1)%B.length;s(B[c])},[i,s]),a=T.useCallback(()=>{const p=B.indexOf(i),c=p===0?B.length-1:p-1;s(B[c])},[i,s]),m=T.useCallback(p=>i===p,[i]),l=T.useCallback(p=>B.indexOf(p),[]);return T.useEffect(()=>{const p=c=>{var v,b,g,y,u;if(!(((v=document.activeElement)==null?void 0:v.tagName)==="INPUT"||((b=document.activeElement)==null?void 0:b.tagName)==="TEXTAREA"||((g=document.activeElement)==null?void 0:g.tagName)==="SELECT")){if((c.ctrlKey||c.metaKey)&&!c.shiftKey)switch(c.key){case"ArrowLeft":c.preventDefault(),a();break;case"ArrowRight":c.preventDefault(),o();break}if(c.key>="1"&&c.key<="4"&&!c.ctrlKey&&!c.metaKey){const x=parseInt(c.key)-1;x<B.length&&(c.preventDefault(),s(B[x]))}if(c.altKey&&!c.ctrlKey&&!c.metaKey)switch(c.key.toLowerCase()){case"o":c.preventDefault(),s("overview");break;case"p":c.preventDefault(),s("plan");break;case"l":c.preventDefault(),s("levels");break;case"n":c.preventDefault(),s("news");break}c.key.toLowerCase()==="g"&&!c.ctrlKey&&!c.metaKey&&!c.altKey&&((y=document.activeElement)==null?void 0:y.tagName)!=="INPUT"&&((u=document.activeElement)==null?void 0:u.tagName)!=="TEXTAREA"&&(c.preventDefault(),s("overview"))}};return window.addEventListener("keydown",p),()=>window.removeEventListener("keydown",p)},[o,a,s]),{activeTab:i,setActiveTab:s,nextTab:o,previousTab:a,isTabActive:m,getTabIndex:l,availableTabs:B}},et=e=>{const t=new Map;e.forEach(r=>{var o;const s=(o=r.setup)==null?void 0:o.primary_setup;s&&(t.has(s)||t.set(s,[]),t.get(s).push(r))});const i=[];return t.forEach((r,s)=>{const o=r.length,a=r.filter(u=>u.trade.win_loss==="Win").length,m=o>0?a/o*100:0,l=r.map(u=>u.trade.r_multiple).filter(u=>u!=null),p=l.length>0?l.reduce((u,x)=>u+x,0)/l.length:0,c=r.map(u=>u.trade.pattern_quality_rating).filter(u=>u!=null),v=c.length>0?c.reduce((u,x)=>u+x,0)/c.length:0,b=new Map;r.forEach(u=>{const x=u.trade.session||"Unknown";b.has(x)||b.set(x,{wins:0,total:0});const S=b.get(x);S.total++,u.trade.win_loss==="Win"&&S.wins++});let g="Unknown",y=0;b.forEach((u,x)=>{const S=u.total>0?u.wins/u.total*100:0;S>y&&u.total>=2&&(y=S,g=x)}),i.push({setupName:s,totalTrades:o,winRate:m,avgRMultiple:p,avgQuality:v,bestSession:g,successRate:m})}),i.filter(r=>r.totalTrades>=2).sort((r,s)=>s.winRate-r.winRate).slice(0,5)},zn=e=>{const t=new Map;e.forEach(r=>{var m,l,p;const s=((m=r.setup)==null?void 0:m.primary_setup)||"",o=((l=r.setup)==null?void 0:l.secondary_setup)||"",a=((p=r.setup)==null?void 0:p.liquidity_taken)||"";if(s&&o&&a){const c=`${s}|${o}|${a}`;t.has(c)||t.set(c,[]),t.get(c).push(r)}});const i=[];return t.forEach((r,s)=>{const[o,a,m]=s.split("|"),l=r.length;if(l<2)return;const c=r.filter(h=>h.trade.win_loss==="Win").length/l*100,v=r.map(h=>h.trade.r_multiple).filter(h=>h!=null),b=v.length>0?v.reduce((h,w)=>h+w,0)/v.length:0,g=r.map(h=>h.trade.pattern_quality_rating).filter(h=>h!=null),y=g.length>0?g.reduce((h,w)=>h+w,0)/g.length:0,u=r.map(h=>h.trade.achieved_pl||0).reduce((h,w)=>h+w,0),x=new Map;r.forEach(h=>{const w=h.trade.session||"Unknown";x.has(w)||x.set(w,{wins:0,total:0});const I=x.get(w);I.total++,h.trade.win_loss==="Win"&&I.wins++});const S=Array.from(x.entries()).map(([h,w])=>({sessionName:h,winRate:w.total>0?w.wins/w.total*100:0,trades:w.total}));let j="",f="LOW";c>=80&&l>=3?(j=`PRIORITIZE - Exceptional combination (${c.toFixed(0)}% win rate)`,f="HIGH"):c>=70&&b>=1.5?(j=`EXECUTE WITH CONFIDENCE - Strong performance (${c.toFixed(0)}% win rate, ${b.toFixed(1)}R avg)`,f="HIGH"):c>=60?(j=`SELECTIVE USE - Good combination (${c.toFixed(0)}% win rate)`,f="MEDIUM"):(j=`AVOID OR IMPROVE - Underperforming combination (${c.toFixed(0)}% win rate)`,f="LOW"),i.push({primary:o,secondary:a,liquidity:m,performance:{totalTrades:l,winRate:c,avgRMultiple:b,avgQuality:y,totalPnL:u},sessions:S,recommendation:j,priority:f})}),i.sort((r,s)=>s.performance.winRate-r.performance.winRate).slice(0,10)},Wn=e=>{const t=new Map;e.forEach(r=>{var o;const s=(o=r.setup)==null?void 0:o.liquidity_taken;s&&(t.has(s)||t.set(s,[]),t.get(s).push(r))});const i=[];return t.forEach((r,s)=>{const o=r.length;if(o<2)return;const m=r.filter(u=>u.trade.win_loss==="Win").length/o*100,l=r.map(u=>u.trade.r_multiple).filter(u=>u!=null),p=l.length>0?l.reduce((u,x)=>u+x,0)/l.length:0,c=new Map;r.forEach(u=>{const x=u.trade.model_type;c.has(x)||c.set(x,{wins:0,total:0});const S=c.get(x);S.total++,u.trade.win_loss==="Win"&&S.wins++});const v=Array.from(c.entries()).filter(([u,x])=>x.total>=2).sort((u,x)=>x[1].wins/x[1].total-u[1].wins/u[1].total).slice(0,2).map(([u,x])=>u),b=new Map;r.forEach(u=>{const x=u.trade.session||"Unknown";b.has(x)||b.set(x,{wins:0,total:0});const S=b.get(x);S.total++,u.trade.win_loss==="Win"&&S.wins++});const g=Array.from(b.entries()).filter(([u,x])=>x.total>=2).sort((u,x)=>x[1].wins/x[1].total-u[1].wins/u[1].total).slice(0,2).map(([u,x])=>u);let y="";m>=75?y=`PRIORITIZE ${s} - Excellent success rate (${m.toFixed(0)}%)`:m>=60?y=`TARGET ${s} - Good performance (${m.toFixed(0)}%)`:y=`SELECTIVE USE ${s} - Average performance (${m.toFixed(0)}%)`,i.push({liquidityTarget:s,performance:{totalTrades:o,winRate:m,avgRMultiple:p,successRate:m},bestModels:v,bestSessions:g,recommendation:y})}),i.sort((r,s)=>s.performance.winRate-r.performance.winRate).slice(0,5)},_n=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);return T.useEffect(()=>{(async()=>{try{r(!0),o(null);const l=await ee.getAllTrades();t(l)}catch(l){console.error("Error fetching trades for setup intelligence:",l),o("Failed to load trade data")}finally{r(!1)}})()},[]),{setupIntelligence:T.useMemo(()=>{var g,y,u,x;if(e.length===0)return{topPrimarySetups:[],topSecondarySetups:[],bestCombinations:[],liquidityIntelligence:[],currentRecommendations:{primarySetup:"Insufficient data",secondarySetup:"Insufficient data",liquidityTarget:"Insufficient data",reasoning:"Import more trade data to generate recommendations",expectedWinRate:50,expectedRMultiple:1}};const m=et(e),l=et(e),p=zn(e),c=Wn(e),v=p[0],b=v?{primarySetup:v.primary,secondarySetup:v.secondary,liquidityTarget:v.liquidity,reasoning:`Best performing combination: ${v.performance.winRate.toFixed(0)}% win rate with ${v.performance.avgRMultiple.toFixed(1)}R average`,expectedWinRate:v.performance.winRate,expectedRMultiple:v.performance.avgRMultiple}:{primarySetup:((g=m[0])==null?void 0:g.setupName)||"No data",secondarySetup:"No data",liquidityTarget:((y=c[0])==null?void 0:y.liquidityTarget)||"No data",reasoning:"Based on individual setup performance",expectedWinRate:((u=m[0])==null?void 0:u.winRate)||50,expectedRMultiple:((x=m[0])==null?void 0:x.avgRMultiple)||1};return{topPrimarySetups:m,topSecondarySetups:l,bestCombinations:p,liquidityIntelligence:c,currentRecommendations:b}},[e]),isLoading:i,error:s,refresh:()=>{t([])}}},On={NY_OPEN:[{start:"09:30",end:"09:45",label:"Market Open",description:"Initial volatility assessment"},{start:"09:45",end:"10:15",label:"OPTIMAL WINDOW",description:"Primary opportunity window"},{start:"10:15",end:"10:45",label:"Secondary Window",description:"Continuation opportunities"},{start:"10:45",end:"11:00",label:"Session Wind-down",description:"Reduced activity period"}],LUNCH_MACRO:[{start:"11:50",end:"12:10",label:"PRIMARY WINDOW",description:"Highest win rate period"},{start:"12:10",end:"12:30",label:"Continuation Window",description:"Follow-through opportunities"},{start:"12:30",end:"13:00",label:"Midday Consolidation",description:"Range-bound analysis"},{start:"13:00",end:"13:30",label:"Afternoon Transition",description:"Preparation for next session"}],MOC:[{start:"15:30",end:"15:45",label:"Pre-Close Setup",description:"End-of-day positioning"},{start:"15:45",end:"16:00",label:"CLOSE WINDOW",description:"Final momentum plays"}],PRE_MARKET:[{start:"08:00",end:"08:30",label:"Early Pre-Market",description:"News reaction analysis"},{start:"08:30",end:"09:00",label:"Pre-Market Prime",description:"Setup development"},{start:"09:00",end:"09:30",label:"Market Prep",description:"Final positioning"}]},L=e=>{const[t,i]=e.split(":").map(Number);return t*60+i},Ct=()=>{const e=new Date;return`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`},tt=(e,t)=>{const i=L(e),r=L(t.start),s=L(t.end);return i>=r&&i<=s},$n=(e,t)=>{const i=L(e),r=L(t.end);return Math.max(0,r-i)},Gn=e=>{const t=e.trade.session;if(t)return t;const i=e.trade.entry_time;if(!i)return null;const r=L(i);return r>=L("09:30")&&r<=L("11:00")?"NY Open":r>=L("11:50")&&r<=L("13:30")?"Lunch Macro":r>=L("15:30")&&r<=L("16:00")?"MOC":r>=L("08:00")&&r<=L("09:30")?"Pre-Market":null},ge=(e,t,i)=>{const r=On[e],s=i.filter(w=>Gn(w)===t),o=r.map(w=>{const I=s.filter(k=>{const Y=k.trade.entry_time;return Y?tt(Y,w):!1}),E=I.length,F=I.filter(k=>k.trade.win_loss==="Win").length,D=E>0?F/E*100:0,A=I.map(k=>k.trade.r_multiple).filter(k=>k!=null),C=A.length>0?A.reduce((k,Y)=>k+Y,0)/A.length:0,N=I.map(k=>k.trade.achieved_pl||0).reduce((k,Y)=>k+Y,0),P=I.filter(k=>k.trade.model_type==="RD-Cont"),_=I.filter(k=>k.trade.model_type==="FVG-RD"),ne=P.length>0?P.filter(k=>k.trade.win_loss==="Win").length/P.length*100:0,oe=_.length>0?_.filter(k=>k.trade.win_loss==="Win").length/_.length*100:0;let R=null;P.length>=2&&_.length>=2?R=ne>oe?"RD-Cont":"FVG-RD":P.length>=3?R="RD-Cont":_.length>=3&&(R="FVG-RD");const M=D>=70&&E>=3,W=w.label.includes("OPTIMAL")||w.label.includes("PRIMARY");let H="";return M?H=`PRIORITIZE - ${D.toFixed(0)}% win rate, ${C.toFixed(1)} avg R`:D>=60&&E>=2?H=`GOOD OPPORTUNITY - ${D.toFixed(0)}% win rate`:E<2?H="INSUFFICIENT DATA - Monitor for opportunities":H=`CAUTION - ${D.toFixed(0)}% win rate, consider reduced size`,{window:w,performance:{totalTrades:E,winningTrades:F,winRate:D,avgRMultiple:C,totalPnL:N},modelPreference:R,isOptimal:M,isPrimary:W,recommendation:H}}),a=s.length,m=s.filter(w=>w.trade.win_loss==="Win").length,l=a>0?m/a*100:0,p=s.map(w=>w.trade.r_multiple).filter(w=>w!=null),c=p.length>0?p.reduce((w,I)=>w+I,0)/p.length:0,v=o.filter(w=>w.performance.totalTrades>0),b=v.length>0?v.reduce((w,I)=>I.performance.winRate>w.performance.winRate?I:w).window:null,g=v.length>0?v.reduce((w,I)=>I.performance.winRate<w.performance.winRate?I:w).window:null,y=Ct(),u=r.find(w=>tt(y,w))||null,x=u?$n(y,u):0,S=L(y),j=r.find(w=>L(w.start)>S)||null,f=u!==null;let h="";if(f&&u){const w=o.find(I=>I.window===u);h=(w==null?void 0:w.recommendation)||"Monitor for opportunities"}else if(j){const w=L(j.start)-S;h=`Next window: ${j.label} in ${w} minutes`}else h="Session not active";return{sessionName:t,sessionType:e,windows:o,overallPerformance:{totalTrades:a,winRate:l,avgRMultiple:c,bestWindow:b,worstWindow:g},currentStatus:{isActive:f,currentWindow:u,timeRemaining:x,nextWindow:j,recommendation:h}}},Hn=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);T.useEffect(()=>{(async()=>{try{r(!0),o(null);const p=await ee.getAllTrades();t(p)}catch(p){console.error("Error fetching trades for session intelligence:",p),o("Failed to load trade data")}finally{r(!1)}})()},[]);const a=T.useMemo(()=>[ge("NY_OPEN","NY Open",e),ge("LUNCH_MACRO","Lunch Macro",e),ge("MOC","MOC",e),ge("PRE_MARKET","Pre-Market",e)],[e]),m=T.useMemo(()=>{const l=Ct(),p=a.find(u=>u.currentStatus.isActive)||null,c=L(l),v=a.filter(u=>!u.currentStatus.isActive).find(u=>{const x=u.windows[0];return L(x.window.start)>c})||null;let b=0,g="LOW";if(p){const u=p.windows.filter(S=>S.isOptimal||S.isPrimary),x=p.currentStatus.currentWindow;x&&u.some(S=>S.window===x)&&(b=p.currentStatus.timeRemaining,g=b<=15?"HIGH":"MEDIUM")}else if(v){const u=v.windows.find(x=>x.isOptimal||x.isPrimary);u&&(b=L(u.window.start)-c,g=b<=30?"MEDIUM":"LOW")}let y="";return p?y=p.currentStatus.recommendation:v&&b<=60?y=`Prepare for ${v.sessionName} in ${b} minutes`:y="No active trading sessions - Monitor for setups",{currentTime:l,activeSession:p,nextSession:v,timeToNextOptimal:b,currentRecommendation:y,urgencyLevel:g}},[a]);return{sessionAnalyses:a,liveGuidance:m,isLoading:i,error:s,refresh:()=>{t([])}}},qn=e=>{if(e.length<5)return"medium";const i=e.slice(-10).map(a=>a.trade.r_multiple).filter(a=>a!=null);if(i.length<3)return"medium";const r=i.reduce((a,m)=>a+Math.abs(m),0)/i.length,s=i.reduce((a,m)=>a+Math.pow(Math.abs(m)-r,2),0)/i.length,o=Math.sqrt(s);return o>1.5?"high":o>.8?"medium":"low"},Vn=e=>{if(e.length<3)return"mixed";const t=e.slice(-5);let i=0,r=0;return t.forEach(s=>{const o=(s.trade.notes||"").toLowerCase(),a=(s.trade.setup||"").toLowerCase(),m=`${o} ${a}`;(m.includes("void")||m.includes("gap")||m.includes("imbalance"))&&i++,(m.includes("reaction")||m.includes("liquidity")||m.includes("sweep"))&&r++}),i>r?"void":r>i?"reaction":"mixed"},Un=e=>{if(e.length<5)return"neutral";const i=e.slice(-10).filter(o=>o.trade.win_loss==="Win");if(i.length<3)return"neutral";const r=i.filter(o=>o.trade.direction==="Long").length,s=i.filter(o=>o.trade.direction==="Short").length;return r>s*1.5?"bullish":s>r*1.5?"bearish":"neutral"},Bn=e=>{if(e.length<3)return null;const t=new Date;t.setDate(t.getDate()-1);const i=e.filter(o=>new Date(o.trade.date)>=t);if(i.length<2)return null;const r=i.filter(o=>o.trade.model_type==="RD-Cont"&&o.trade.win_loss==="Win").length,s=i.filter(o=>o.trade.model_type==="FVG-RD"&&o.trade.win_loss==="Win").length;return r>s?"RD-Cont":s>r?"FVG-RD":null},Yn=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);T.useEffect(()=>{(async()=>{try{r(!0),o(null);const p=await ee.getAllTrades();t(p)}catch(p){console.error("Error fetching trades for model selection:",p),o("Failed to load trade data")}finally{r(!1)}})()},[]);const a=T.useMemo(()=>{const l=e.filter(v=>v.trade.model_type==="RD-Cont"),p=e.filter(v=>v.trade.model_type==="FVG-RD"),c=(v,b)=>{const g=v.length,y=v.filter(D=>D.trade.win_loss==="Win"),u=g>0?y.length/g*100:0,x=v.map(D=>D.trade.r_multiple).filter(D=>D!=null),S=x.length>0?x.reduce((D,A)=>D+A,0)/x.length:0,j=v.slice(-10),f=j.filter(D=>D.trade.win_loss==="Win").length,h=j.length>0?f/j.length*100:0,w={low:{winRate:0,avgR:0,trades:0},medium:{winRate:0,avgR:0,trades:0},high:{winRate:0,avgR:0,trades:0}},I=v.filter(D=>(D.trade.r_multiple||0)<=1),E=v.filter(D=>(D.trade.r_multiple||0)>=2),F=v.filter(D=>{const A=D.trade.r_multiple||0;return A>1&&A<2});return[{trades:I,key:"low"},{trades:F,key:"medium"},{trades:E,key:"high"}].forEach(({trades:D,key:A})=>{const C=D.filter(P=>P.trade.win_loss==="Win").length,N=D.map(P=>P.trade.r_multiple).filter(P=>P!==void 0);w[A]={winRate:D.length>0?C/D.length*100:0,avgR:N.length>0?N.reduce((P,_)=>P+_,0)/N.length:0,trades:D.length}}),{model:b,totalTrades:g,winRate:u,avgRMultiple:S,recentPerformance:h,volatilityPerformance:w}};return{"RD-Cont":c(l,"RD-Cont"),"FVG-RD":c(p,"FVG-RD")}},[e]);return{recommendation:T.useMemo(()=>{if(e.length<5)return{recommendedModel:"RD-Cont",probability:60,confidence:"LOW",reasoning:"Insufficient data for analysis. RD-Cont recommended as default.",alternativeModel:"FVG-RD",alternativeCondition:"if clear FVG setups present",marketConditions:{volatility:"medium",liquidityContext:"mixed",htfTrend:"neutral",previousSessionSuccess:null}};const l=qn(e),p=Vn(e),c=Un(e),v=Bn(e),b={volatility:l,liquidityContext:p,htfTrend:c,previousSessionSuccess:v};let g=0;const y=[];l==="high"?(g+=2,y.push("High volatility favors FVG-RD (higher R-multiple potential)")):l==="low"&&(g-=2,y.push("Low volatility favors RD-Cont (higher win rate)")),p==="void"?(g+=1.5,y.push("Liquidity voids present favor FVG-RD targeting fills")):p==="reaction"&&(g-=1.5,y.push("Strong liquidity reactions favor RD-Cont continuation patterns")),c!=="neutral"&&(g-=1,y.push("Clear HTF trend structure favors RD-Cont within established trends")),v==="RD-Cont"?(g-=.5,y.push("Recent RD-Cont success adds slight bias")):v==="FVG-RD"&&(g+=.5,y.push("Recent FVG-RD success adds slight bias"));const u=a["RD-Cont"],x=a["FVG-RD"];u.winRate>x.winRate+10?(g-=1,y.push(`RD-Cont shows superior win rate (${u.winRate.toFixed(1)}% vs ${x.winRate.toFixed(1)}%)`)):x.avgRMultiple>u.avgRMultiple+.5&&(g+=1,y.push(`FVG-RD shows superior R-multiple (${x.avgRMultiple.toFixed(1)} vs ${u.avgRMultiple.toFixed(1)})`));const S=g>0?"FVG-RD":"RD-Cont",j=g>0?"RD-Cont":"FVG-RD",f=Math.abs(g),h=Math.min(50+f*8,85);let w;f>=3?w="HIGH":f>=1.5?w="MEDIUM":w="LOW";const I=y.join("; ");return{recommendedModel:S,probability:h,confidence:w,reasoning:I,alternativeModel:j,alternativeCondition:S==="FVG-RD"?"if pattern quality <3.5 or low volatility":"if high volatility + clear FVG confluence",marketConditions:b}},[e,a]),modelStats:a,isLoading:i,error:s,refresh:()=>{t([])}}},Kn=e=>{var a,m,l;const t=[],i=((a=e.trade.model_type)==null?void 0:a.toLowerCase())||"",r=((m=e.trade.notes)==null?void 0:m.toLowerCase())||"",s=((l=e.trade.setup)==null?void 0:l.toLowerCase())||"",o=`${i} ${r} ${s}`;return(o.includes("fvg")||o.includes("fair value gap")||o.includes("imbalance"))&&t.push("FVG"),(o.includes("nwog")||o.includes("new week opening")||o.includes("weekly gap"))&&t.push("NWOG"),(o.includes("ndog")||o.includes("new day opening")||o.includes("daily gap"))&&t.push("NDOG"),(o.includes("liquidity")||o.includes("sweep")||o.includes("raid")||o.includes("hunt"))&&t.push("Liquidity"),t},Zn=e=>{const t=e.length;return t===0?0:t===1?.5:t===2?1.2:t===3?1.7:2},Qn=e=>{var o,a;const t=((o=e.trade.notes)==null?void 0:o.toLowerCase())||"",i=((a=e.trade.setup)==null?void 0:a.toLowerCase())||"",r=`${t} ${i}`;if(!r.includes("fvg")&&!r.includes("fair value gap")&&!r.includes("imbalance"))return 0;let s=.5;return(r.includes("clean")||r.includes("clear")||r.includes("perfect"))&&(s+=.3),(r.includes("htf")||r.includes("higher timeframe")||r.includes("daily")||r.includes("4h"))&&(s+=.3),(r.includes("fresh")||r.includes("new")||r.includes("untested"))&&(s+=.2),(r.includes("old")||r.includes("tested")||r.includes("multiple"))&&(s-=.2),Math.min(Math.max(s,0),1.5)},Xn=e=>{var a,m,l;const t=((a=e.trade.notes)==null?void 0:a.toLowerCase())||"",i=((m=e.trade.setup)==null?void 0:m.toLowerCase())||"",r=((l=e.trade.rd_type)==null?void 0:l.toLowerCase())||"",s=`${t} ${i} ${r}`;let o=0;return(s.includes("rd")||s.includes("reaction")||s.includes("displacement"))&&(o+=.3),(s.includes("fast")||s.includes("strong")||s.includes("aggressive")||s.includes("sharp"))&&(o+=.3),(s.includes("volume")||s.includes("high vol")||s.includes("heavy"))&&(o+=.2),(s.includes("clean")||s.includes("clear")||s.includes("obvious"))&&(o+=.2),Math.min(o,1)},Jn=e=>{var o,a;const t=((o=e.trade.notes)==null?void 0:o.toLowerCase())||"",i=((a=e.trade.setup)==null?void 0:a.toLowerCase())||"",r=`${t} ${i}`;let s=0;return(r.includes("doji")||r.includes("hammer")||r.includes("engulfing")||r.includes("pin bar"))&&(s+=.3),(r.includes("momentum")||r.includes("follow through")||r.includes("continuation"))&&(s+=.3),(r.includes("confluence")||r.includes("alignment")||r.includes("multiple tf"))&&(s+=.4),Math.min(s,1)},er=e=>{var s,o;const t=((s=e.trade.notes)==null?void 0:s.toLowerCase())||"",i=((o=e.trade.setup)==null?void 0:o.toLowerCase())||"",r=`${t} ${i}`;return r.includes("volume")||r.includes("vol")||r.includes("heavy")||r.includes("spike")?.5:0},Te=e=>{const t=Kn(e),i={pdArrayConfluence:Zn(t),fvgCharacteristics:Qn(e),rdStrength:Xn(e),confirmationSignals:Jn(e),volumeConfirmation:er(e)},r=Object.values(i).reduce((p,c)=>p+c,0),o=Math.min(r/6*5,5);let a;o>=4.5?a="EXCEPTIONAL":o>=3.5?a="EXCELLENT":o>=2.5?a="GOOD":o>=1.5?a="FAIR":a="POOR";let m;o>=4?m="PRIORITIZE EXECUTION - High probability setup":o>=3?m="EXECUTE WITH CONFIDENCE - Good setup quality":o>=2?m="PROCEED WITH CAUTION - Average setup":m="AVOID OR REDUCE SIZE - Low quality setup";const l=Math.min(40+o*12,90);return{totalScore:o,maxScore:5,breakdown:i,rating:a,recommendation:m,expectedWinProbability:l}},tr=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);return T.useEffect(()=>{(async()=>{try{r(!0),o(null);const l=await ee.getAllTrades();t(l)}catch(l){console.error("Error fetching trades for pattern quality scoring:",l),o("Failed to load trade data")}finally{r(!1)}})()},[]),{analysis:T.useMemo(()=>{if(e.length===0)return{currentScore:{totalScore:0,maxScore:5,breakdown:{pdArrayConfluence:0,fvgCharacteristics:0,rdStrength:0,confirmationSignals:0,volumeConfirmation:0},rating:"POOR",recommendation:"No data available for analysis",expectedWinProbability:50},historicalAccuracy:0,scoreDistribution:[]};const m=e.map(g=>({trade:g,score:Te(g),actualWin:g.trade.win_loss==="Win"})),l=m.filter(g=>g.score.totalScore>0);let p=0;l.forEach(g=>{g.score.expectedWinProbability>60===g.actualWin&&p++});const c=l.length>0?p/l.length*100:0,v=[1,2,3,4,5].map(g=>{const y=m.filter(f=>f.score.totalScore>=g-.5&&f.score.totalScore<g+.5),u=y.filter(f=>f.actualWin).length,x=y.length>0?u/y.length*100:0,S=y.map(f=>f.trade.trade.r_multiple).filter(f=>f!=null),j=S.length>0?S.reduce((f,h)=>f+h,0)/S.length:0;return{score:g,count:y.length,winRate:x,avgRMultiple:j}});return{currentScore:e.length>0?Te(e[e.length-1]):{totalScore:0,maxScore:5,breakdown:{pdArrayConfluence:0,fvgCharacteristics:0,rdStrength:0,confirmationSignals:0,volumeConfirmation:0},rating:"POOR",recommendation:"No current setup to analyze",expectedWinProbability:50},historicalAccuracy:c,scoreDistribution:v}},[e]),isLoading:i,error:s,calculatePatternQuality:Te,refresh:()=>{t([])}}},nr=()=>{const e=new Date,t=e.getHours(),i=["SUNDAY","MONDAY","TUESDAY","WEDNESDAY","THURSDAY","FRIDAY","SATURDAY"][e.getDay()];let r;t<9||t===9&&e.getMinutes()<30?r="PRE_MARKET":t>=16?r="AFTER_HOURS":r="REGULAR";const s=i==="FRIDAY"||i==="WEDNESDAY",o=s?"MEDIUM":"LOW",a=r==="REGULAR"&&t>=9&&t<=11?"HIGH":"NORMAL";return{isNewsDay:s,newsImpact:o,volumeProfile:a,marketHours:r,dayOfWeek:i}},rr=(e,t)=>{const i=t.find(r=>r.hour===e);return!i||i.totalTrades<2?0:i.winRate>=80?15:i.winRate>=70?10:i.winRate>=60?5:i.winRate<40?-10:0},ir=e=>e>=4.5?20:e>=4?15:e>=3.5?10:e>=3?5:e<2?-15:0,sr=(e,t)=>e.newsImpact==="LOW"?0:e.newsImpact==="HIGH"?t==="RD-Cont"?5:-10:t==="RD-Cont"?2:-5,or=e=>{switch(e.volumeProfile){case"HIGH":return 5;case"LOW":return-5;default:return 0}},ar=(e,t,i)=>{let r=0;return e==="HIGH"&&r++,(t==="EXCELLENT"||t==="EXCEPTIONAL")&&r++,i>=10&&r++,r>=3?10:r>=2?5:0},cr=(e,t,i)=>{let r,s,o,a;return e>=75&&t==="HIGH"&&i>=4?(r="AGGRESSIVE",s=2.5,o=1,a=[1.5,2.5,4]):e>=65&&t!=="LOW"?(r="STANDARD",s=2,o=1,a=[1.5,2,3]):(r="CONSERVATIVE",s=1,o=.8,a=[1,1.5,2]),{positionSizing:r,maxRiskPercent:s,stopLossMultiplier:o,takeProfitTargets:a}},lr=(e,t)=>{const[i,r]=T.useState([]),[s,o]=T.useState(!0),[a,m]=T.useState(null);return T.useEffect(()=>{(async()=>{try{o(!0),m(null);const c=await ee.getAllTrades();r(c)}catch(c){console.error("Error fetching trades for probability calculation:",c),m("Failed to load trade data")}finally{o(!1)}})()},[]),{successProbability:T.useMemo(()=>{if(i.length<5)return{finalProbability:50,confidence:"LOW",recommendation:"STANDARD",expectedRMultiple:{min:1,max:2,average:1.5},breakdown:{baseModelWinRate:50,sessionBonus:0,qualityBonus:0,newsImpact:0,volumeBonus:0,confluenceBonus:0},riskManagement:{positionSizing:"CONSERVATIVE",maxRiskPercent:1,stopLossMultiplier:1,takeProfitTargets:[1,1.5,2]}};const p=nr(),c=new Date().getHours(),v=i.filter(C=>C.trade.model_type===e.recommendedModel),b=v.length>0?v.filter(C=>C.trade.win_loss==="Win").length/v.length*100:e.probability,g=Array.from({length:24},(C,N)=>{const P=i.filter(_=>{const ne=_.trade.entry_time;return ne?parseInt(ne.split(":")[0])===N:!1});return{hour:N,winRate:P.length>0?P.filter(_=>_.trade.win_loss==="Win").length/P.length*100:0,totalTrades:P.length}}),y=rr(c,g),u=ir(t.totalScore),x=sr(p,e.recommendedModel),S=or(p),j=ar(e.confidence,t.rating,y),f=Math.min(Math.max(b+y+u+x+S+j,20),90);let h;const w=Math.abs(y+u+j);w>=25&&e.confidence==="HIGH"?h="HIGH":w>=15?h="MEDIUM":h="LOW";let I;f>=80&&h==="HIGH"?I="PRIORITIZE":f>=70?I="INCREASE_SIZE":f>=60?I="STANDARD":f>=45?I="REDUCE_SIZE":I="AVOID";const E=v.map(C=>C.trade.r_multiple).filter(C=>C!=null),F=E.length>0?E.reduce((C,N)=>C+N,0)/E.length:1.5,D={min:Math.max(F*.7,.5),max:F*1.5,average:F},A=cr(f,h,t.totalScore);return{finalProbability:f,confidence:h,recommendation:I,expectedRMultiple:D,breakdown:{baseModelWinRate:b,sessionBonus:y,qualityBonus:u,newsImpact:x,volumeBonus:S,confluenceBonus:j},riskManagement:A}},[i,e,t]),isLoading:s,error:a,refresh:()=>{r([])}}},je=d.div.withConfig({displayName:"PanelContainer",componentId:"sc-dkcwhp-0"})(["display:flex;flex-direction:column;gap:16px;"]),Re=d.div.withConfig({displayName:"ExpandableSection",componentId:"sc-dkcwhp-1"})(["background:var(--elite-card-bg);border:1px solid var(--elite-card-border);border-radius:var(--spacing-xs);overflow:hidden;margin-bottom:var(--spacing-md);box-shadow:var(--shadow-sm);"]),De=d.button.withConfig({displayName:"SectionHeader",componentId:"sc-dkcwhp-2"})(["width:100%;background:var(--surface-bg);border:none;padding:16px 20px;display:flex;justify-content:space-between;align-items:center;cursor:pointer;transition:all 0.2s ease;&:hover{background:var(--hover-bg);transform:translateY(-1px);}border-bottom:",";"],({$isExpanded:e})=>e?"1px solid var(--border-color)":"none"),Ee=d.div.withConfig({displayName:"SectionTitle",componentId:"sc-dkcwhp-3"})(["display:flex;align-items:center;gap:8px;font-size:16px;font-weight:600;color:var(--text-primary);"]),Ne=d.div.withConfig({displayName:"SectionSummary",componentId:"sc-dkcwhp-4"})(["font-size:12px;color:var(--text-secondary);margin-top:2px;"]),ke=d.span.withConfig({displayName:"ExpandIcon",componentId:"sc-dkcwhp-5"})(["font-size:14px;transition:transform 0.2s ease;transform:",";"],({$isExpanded:e})=>e?"rotate(180deg)":"rotate(0deg)"),Me=d.div.withConfig({displayName:"SectionContent",componentId:"sc-dkcwhp-6"})(["max-height:",";overflow:hidden;transition:max-height 0.3s ease;padding:",";"],({$isExpanded:e})=>e?"1000px":"0",({$isExpanded:e})=>e?"20px":"0 20px"),Ae=d.div.withConfig({displayName:"DetailGrid",componentId:"sc-dkcwhp-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px;margin-bottom:16px;"]),q=d.div.withConfig({displayName:"DetailItem",componentId:"sc-dkcwhp-8"})(["background:var(--elite-section-bg);border:1px solid var(--elite-card-border);border-radius:var(--spacing-xs);padding:var(--spacing-md);transition:all 0.2s ease;&:hover{border-color:var(--primary-color);transform:translateY(-2px);box-shadow:var(--shadow-md);}"]),V=d.div.withConfig({displayName:"DetailLabel",componentId:"sc-dkcwhp-9"})(["font-size:12px;font-weight:500;color:var(--text-secondary);text-transform:uppercase;letter-spacing:0.5px;margin-bottom:6px;"]),U=d.div.withConfig({displayName:"DetailValue",componentId:"sc-dkcwhp-10"})(["font-size:16px;font-weight:600;color:var(--text-primary);margin-bottom:4px;"]),O=d.div.withConfig({displayName:"DetailDescription",componentId:"sc-dkcwhp-11"})(["font-size:12px;color:var(--text-muted);line-height:1.4;"]),dr=({modelRecommendation:e,patternQuality:t,successProbability:i,isLoading:r=!1,error:s=null})=>{const[o,a]=T.useState(new Set(["model"])),m=p=>{const c=new Set(o);c.has(p)?c.delete(p):c.add(p),a(c)};if(r)return n.jsx(je,{children:n.jsx("div",{style:{textAlign:"center",padding:"40px",color:"var(--text-secondary)"},children:"Loading detailed analysis..."})});if(s)return n.jsx(je,{children:n.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"var(--error-text)"},children:["Error loading detailed analysis: ",s]})});const l=p=>o.has(p);return n.jsxs(je,{children:[n.jsxs(Re,{children:[n.jsxs(De,{$isExpanded:l("model"),onClick:()=>m("model"),children:[n.jsxs("div",{children:[n.jsx(Ee,{children:"🎯 Model Selection Analysis"}),n.jsxs(Ne,{children:[e.recommendedModel," • ",e.probability.toFixed(0),"% confidence"]})]}),n.jsx(ke,{$isExpanded:l("model"),children:"▼"})]}),n.jsxs(Me,{$isExpanded:l("model"),children:[n.jsxs(Ae,{children:[n.jsxs(q,{children:[n.jsx(V,{children:"Recommended Model"}),n.jsx(U,{children:e.recommendedModel}),n.jsxs(O,{children:[e.confidence," confidence"]})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Alternative Model"}),n.jsx(U,{children:e.alternativeModel}),n.jsx(O,{children:e.alternativeCondition})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Market Volatility"}),n.jsx(U,{children:e.marketConditions.volatility.toUpperCase()}),n.jsx(O,{children:"Current market volatility assessment"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"HTF Trend"}),n.jsx(U,{children:e.marketConditions.htfTrend.toUpperCase()}),n.jsx(O,{children:"Higher timeframe trend direction"})]})]}),n.jsxs(O,{style:{padding:"12px",background:"var(--surface-bg)",borderRadius:"8px"},children:[n.jsx("strong",{children:"Reasoning:"})," ",e.reasoning]})]})]}),n.jsxs(Re,{children:[n.jsxs(De,{$isExpanded:l("pattern"),onClick:()=>m("pattern"),children:[n.jsxs("div",{children:[n.jsx(Ee,{children:"📊 Pattern Quality Analysis"}),n.jsxs(Ne,{children:[t.totalScore.toFixed(1),"/5.0 • ",t.rating]})]}),n.jsx(ke,{$isExpanded:l("pattern"),children:"▼"})]}),n.jsxs(Me,{$isExpanded:l("pattern"),children:[n.jsxs(Ae,{children:[n.jsxs(q,{children:[n.jsx(V,{children:"PD Array Confluence"}),n.jsx(U,{children:t.breakdown.pdArrayConfluence.toFixed(1)}),n.jsx(O,{children:"Multiple PD array alignment"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"FVG Characteristics"}),n.jsx(U,{children:t.breakdown.fvgCharacteristics.toFixed(1)}),n.jsx(O,{children:"Fair value gap quality"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"RD Strength"}),n.jsx(U,{children:t.breakdown.rdStrength.toFixed(1)}),n.jsx(O,{children:"Reaction/displacement power"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Confirmation Signals"}),n.jsx(U,{children:t.breakdown.confirmationSignals.toFixed(1)}),n.jsx(O,{children:"Supporting technical signals"})]})]}),n.jsxs(O,{style:{padding:"12px",background:"var(--surface-bg)",borderRadius:"8px"},children:[n.jsx("strong",{children:"Recommendation:"})," ",t.recommendation]})]})]}),n.jsxs(Re,{children:[n.jsxs(De,{$isExpanded:l("probability"),onClick:()=>m("probability"),children:[n.jsxs("div",{children:[n.jsx(Ee,{children:"🎯 Success Probability Analysis"}),n.jsxs(Ne,{children:[i.finalProbability.toFixed(0),"% •"," ",i.recommendation.replace("_"," ")]})]}),n.jsx(ke,{$isExpanded:l("probability"),children:"▼"})]}),n.jsx(Me,{$isExpanded:l("probability"),children:n.jsxs(Ae,{children:[n.jsxs(q,{children:[n.jsx(V,{children:"Base Model Win Rate"}),n.jsxs(U,{children:[i.breakdown.baseModelWinRate.toFixed(0),"%"]}),n.jsx(O,{children:"Historical model performance"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Session Bonus"}),n.jsxs(U,{children:[i.breakdown.sessionBonus>0?"+":"",i.breakdown.sessionBonus.toFixed(0),"%"]}),n.jsx(O,{children:"Current session performance boost"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Quality Bonus"}),n.jsxs(U,{children:[i.breakdown.qualityBonus>0?"+":"",i.breakdown.qualityBonus.toFixed(0),"%"]}),n.jsx(O,{children:"Pattern quality enhancement"})]}),n.jsxs(q,{children:[n.jsx(V,{children:"Expected R-Multiple"}),n.jsxs(U,{children:[i.expectedRMultiple.min.toFixed(1)," -"," ",i.expectedRMultiple.max.toFixed(1)]}),n.jsxs(O,{children:["Risk-reward range (avg: ",i.expectedRMultiple.average.toFixed(1),")"]})]})]})})]})]})},pr=d.div.withConfig({displayName:"StateContainer",componentId:"sc-h36p2e-0"})(["display:flex;align-items:center;gap:8px;padding:8px 12px;border-radius:6px;font-size:12px;font-weight:600;text-transform:uppercase;letter-spacing:0.5px;background:",";border:1px solid ",";color:",";"],({$isOpen:e})=>e?"var(--success-bg, rgba(34, 197, 94, 0.1))":"var(--warning-bg, rgba(251, 191, 36, 0.1))",({$isOpen:e})=>e?"var(--success-border, rgba(34, 197, 94, 0.3))":"var(--warning-border, rgba(251, 191, 36, 0.3))",({$isOpen:e})=>e?"var(--success-text, #22c55e)":"var(--warning-text, #fbbf24)"),mr=d.div.withConfig({displayName:"StatusDot",componentId:"sc-h36p2e-1"})(["width:8px;height:8px;border-radius:50%;background:",";",""],({$isOpen:e})=>e?"var(--success-text, #22c55e)":"var(--warning-text, #fbbf24)",({$isOpen:e})=>e&&`
    animation: pulse 2s infinite;
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `),ur=d.span.withConfig({displayName:"StatusText",componentId:"sc-h36p2e-2"})(["font-size:12px;font-weight:600;"]),gr=d.div.withConfig({displayName:"DetailText",componentId:"sc-h36p2e-3"})(["font-size:11px;font-weight:400;color:var(--text-secondary);margin-top:4px;text-transform:none;letter-spacing:normal;"]),It=()=>{const e=new Date,t=new Date(e.toLocaleString("en-US",{timeZone:"America/New_York"})),i=t.getHours(),r=t.getMinutes(),s=t.getDay(),o=i*60+r,a=9*60+30,m=16*60;if(s===0||s===6){const l=new Date(t);return l.setDate(t.getDate()+(s===0?1:2)),l.setHours(9,30,0,0),{isOpen:!1,status:"CLOSED",nextOpen:l.toLocaleString("en-US",{weekday:"long",hour:"numeric",minute:"2-digit",timeZone:"America/New_York"}),timeZone:"ET",dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][s]}}if(o>=a&&o<m)return{isOpen:!0,status:"OPEN",timeZone:"ET",dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][s]};if(o<a){const l=new Date(t);return l.setHours(9,30,0,0),{isOpen:!1,status:"PRE_MARKET",nextOpen:l.toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",timeZone:"America/New_York"}),timeZone:"ET",dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][s]}}else{const l=new Date(t);return s===5?l.setDate(t.getDate()+3):l.setDate(t.getDate()+1),l.setHours(9,30,0,0),{isOpen:!1,status:"AFTER_HOURS",nextOpen:l.toLocaleString("en-US",{weekday:s===5?"long":void 0,hour:"numeric",minute:"2-digit",timeZone:"America/New_York"}),timeZone:"ET",dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][s]}}},Tt=({marketState:e,showDetails:t=!1,className:i})=>{const r=()=>{switch(e.status){case"OPEN":return"Markets Open";case"PRE_MARKET":return"Pre-Market";case"AFTER_HOURS":return"After Hours";case"CLOSED":return`Markets Closed - ${e.dayOfWeek}`;default:return"Unknown"}},s=()=>t?e.isOpen?"Live market data • Real-time analysis":e.nextOpen?`Next open: ${e.nextOpen} ${e.timeZone} • Using historical data`:"Using historical data for analysis":null;return n.jsxs(pr,{$isOpen:e.isOpen,className:i,children:[n.jsx(mr,{$isOpen:e.isOpen}),n.jsxs("div",{children:[n.jsx(ur,{children:r()}),t&&s()&&n.jsx(gr,{children:s()})]})]})},Pe=d.div.withConfig({displayName:"PanelContainer",componentId:"sc-11yhh8h-0"})(["background:var(--bg-secondary);border:2px solid var(--elite-card-border);border-radius:8px;padding:var(--spacing-lg);margin-bottom:var(--spacing-lg);box-shadow:0 4px 16px rgba(0,0,0,0.2);position:relative;overflow:hidden;border-radius:calc(8px + (12px * var(--racing-effects,0)));clip-path:polygon(0 0,calc(100% - calc(10px * var(--racing-effects,0))) 0,100% 100%,0 100%);&::before{content:'';position:absolute;top:0;left:0;right:0;height:2px;background:var(--primary-color);opacity:calc(0.6 + (0.4 * var(--racing-effects,0)));animation:racing-stripe var(--animation-speed) linear infinite;}&::after{content:'🏁';position:absolute;top:15px;right:20px;font-size:20px;opacity:calc(0.2 * var(--racing-effects,0));animation:subtle-glow var(--animation-speed) ease-in-out infinite;}@keyframes racing-stripe{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}@keyframes subtle-glow{0%,100%{opacity:calc(0.2 * var(--racing-effects,0));}50%{opacity:calc(0.4 * var(--racing-effects,0));}}"]),fr=d.div.withConfig({displayName:"PanelHeader",componentId:"sc-11yhh8h-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--spacing-lg);padding-bottom:var(--spacing-sm);border-bottom:1px solid rgba(255,255,255,0.1);"]),hr=d.h3.withConfig({displayName:"Title",componentId:"sc-11yhh8h-2"})(["margin:0;font-family:var(--font-primary);font-size:var(--font-size-2xl);font-weight:700;color:var(--text-primary);display:flex;align-items:center;gap:var(--spacing-xs);text-transform:uppercase;letter-spacing:1px;position:relative;text-shadow:0 2px 4px rgba(0,0,0,0.3);background:linear-gradient( 135deg,var(--text-primary) 0%,var(--primary-color) 50%,var(--text-primary) 100% );-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text;&::before{content:'';position:absolute;left:-10px;top:50%;transform:translateY(-50%);width:calc(2px + (2px * var(--racing-effects,0)));height:100%;background:var(--primary-color);border-radius:2px;opacity:calc(0.6 + (0.4 * var(--racing-effects,0)));}"]),xr=d.div.withConfig({displayName:"QuickGrid",componentId:"sc-11yhh8h-3"})(["display:grid;grid-template-columns:1fr 1fr 1fr;gap:var(--spacing-lg);margin-bottom:var(--spacing-md);align-items:stretch;@media (max-width:768px){grid-template-columns:1fr;gap:var(--spacing-md);}"]),yr=d.div.withConfig({displayName:"MarketContextBanner",componentId:"sc-11yhh8h-4"})(["background:",";border:2px solid ",";border-radius:var(--spacing-xs);padding:var(--spacing-md) var(--spacing-lg);margin-bottom:var(--spacing-md);font-size:var(--font-size-sm);font-weight:600;color:var(--text-primary);text-align:center;box-shadow:0 4px 16px rgba(0,0,0,0.2);text-shadow:0 1px 2px rgba(0,0,0,0.3);"],({$isWeekend:e})=>e?"linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%)":"linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)",({$isWeekend:e})=>e?"var(--warning-color)":"var(--info-color)"),vr=d.div.withConfig({displayName:"PrimaryRecommendation",componentId:"sc-11yhh8h-5"})(["text-align:center;padding:var(--spacing-lg);background:linear-gradient(135deg,var(--elite-section-bg) 0%,rgba(42,42,42,0.8) 100%);border-radius:0 15px 0 15px;border:2px solid var(--primary-color);position:relative;overflow:hidden;transform-style:preserve-3d;transition:all var(--transition-normal);&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient(90deg,transparent 0%,var(--primary-color) 50%,transparent 100%);animation:victory-pulse 2s ease-in-out infinite;}&::after{content:'#1';position:absolute;top:8px;right:12px;font-family:var(--racing-font);font-size:48px;font-weight:900;color:var(--accent-color);opacity:0.15;line-height:1;}&:hover{transform:translateY(-2px);box-shadow:0 12px 40px rgba(0,0,0,0.5);border-color:var(--accent-color);}@keyframes victory-pulse{0%,100%{opacity:0.6;}50%{opacity:1;}}"]),wr=d.div.withConfig({displayName:"ModelName",componentId:"sc-11yhh8h-6"})(["font-family:var(--font-primary);font-size:clamp(1.5rem,4vw,var(--font-size-3xl));font-weight:700;color:var(--text-primary);margin-bottom:var(--spacing-xs);line-height:1.2;text-transform:uppercase;letter-spacing:1px;position:relative;z-index:2;text-shadow:0 2px 4px rgba(0,0,0,0.5);filter:drop-shadow(0 0 calc(10px * var(--glow-opacity,0)) var(--primary-color));"]),br=d.span.withConfig({displayName:"ConfidenceBadge",componentId:"sc-11yhh8h-7"})(["background:var(--accent-color);color:var(--text-primary);padding:var(--spacing-xxs) var(--spacing-sm);border-radius:20px;font-weight:700;font-size:var(--font-size-xs);text-transform:uppercase;letter-spacing:0.5px;box-shadow:0 2px 8px rgba(0,0,0,0.3);"]),Sr=d.div.withConfig({displayName:"SetupRecommendation",componentId:"sc-11yhh8h-8"})(["padding:var(--spacing-lg);background:linear-gradient( 135deg,rgba(0,255,228,0.08) 0%,var(--elite-section-bg) 50%,rgba(0,128,255,0.05) 100% );border-radius:var(--spacing-xs);border:1px solid var(--elite-card-border);border-left:4px solid var(--primary-color);position:relative;clip-path:polygon(0 0,calc(100% - 15px) 0,100% 100%,0 100%);backdrop-filter:blur(5px);transition:all var(--transition-normal);&::before{content:'◢';position:absolute;top:8px;right:12px;color:var(--primary-color);font-size:18px;opacity:0.6;}&::after{content:'';position:absolute;top:0;left:0;right:0;height:1px;background:linear-gradient(90deg,transparent 0%,var(--primary-color) 50%,transparent 100%);}&:hover{transform:translateX(2px);border-left-color:var(--accent-color);box-shadow:-4px 0 20px rgba(0,255,228,0.2);}"]),Cr=d.div.withConfig({displayName:"SetupTitle",componentId:"sc-11yhh8h-9"})(["font-size:var(--font-size-xs);color:var(--accent-color);margin-bottom:var(--spacing-xs);font-weight:700;text-transform:uppercase;letter-spacing:1px;"]),Ir=d.div.withConfig({displayName:"SetupDescription",componentId:"sc-11yhh8h-10"})(["font-size:var(--font-size-md);line-height:1.4;color:var(--text-primary);margin-bottom:var(--spacing-xs);font-weight:600;"]),Tr=d.div.withConfig({displayName:"SetupStats",componentId:"sc-11yhh8h-11"})(["font-size:var(--font-size-xs);color:var(--accent-color);font-weight:600;letter-spacing:0.5px;"]),jr=d.div.withConfig({displayName:"ProbabilitySection",componentId:"sc-11yhh8h-12"})(["text-align:center;padding:var(--spacing-lg);background:radial-gradient( circle at center,var(--elite-section-bg) 0%,rgba(42,42,42,0.9) 100% );border-radius:50% 50% 0 0;border:2px solid var(--elite-card-border);border-bottom:4px solid var(--primary-color);position:relative;overflow:hidden;&::before{content:'';position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:80px;height:80px;border:3px solid var(--primary-color);border-radius:50%;opacity:0.3;}&::after{content:'⚡';position:absolute;top:10px;right:15px;font-size:20px;opacity:0.6;animation:electric-pulse 1.5s ease-in-out infinite;}@keyframes electric-pulse{0%,100%{opacity:0.6;transform:scale(1);}50%{opacity:1;transform:scale(1.1);}}"]),Rr=d.div.withConfig({displayName:"ProbabilityValue",componentId:"sc-11yhh8h-13"})(["font-family:var(--font-data);font-size:clamp(2rem,5vw,var(--font-size-4xl));font-weight:700;line-height:1;margin-bottom:var(--spacing-xxs);position:relative;z-index:2;color:",";text-shadow:0 2px 4px rgba(0,0,0,0.6);filter:drop-shadow(0 0 calc(15px * var(--glow-opacity,0)) currentColor);&::before{content:'';position:absolute;inset:-8px;background:radial-gradient( circle,"," 0%,transparent 70% );border-radius:50%;z-index:-1;opacity:var(--glow-opacity,0);animation:probability-glow var(--animation-speed) ease-in-out infinite;}@keyframes probability-glow{0%,100%{opacity:calc(0.4 * var(--glow-opacity,0));transform:scale(1);}50%{opacity:calc(0.8 * var(--glow-opacity,0));transform:scale(1.02);}}"],({$probability:e})=>e>=70?"var(--success-color)":e>=50?"var(--warning-color)":"var(--error-color)",({$probability:e})=>e>=70?"rgba(16, 185, 129, 0.1)":e>=50?"rgba(255, 165, 0, 0.1)":"rgba(255, 107, 107, 0.1)"),Dr=d.div.withConfig({displayName:"ProbabilityLabel",componentId:"sc-11yhh8h-14"})(["font-size:var(--font-size-xs);color:var(--text-secondary);font-weight:600;text-transform:uppercase;letter-spacing:1px;"]),Er=d.div.withConfig({displayName:"QualityAlert",componentId:"sc-11yhh8h-15"})(["display:",";background:linear-gradient( 135deg,var(--error-color) 0%,rgba(225,6,0,0.8) 50%,var(--error-color) 100% );color:var(--text-primary);padding:var(--spacing-md) var(--spacing-lg);border-radius:0 10px 0 10px;margin:var(--spacing-md) 0;text-align:center;font-family:var(--racing-font);font-weight:900;font-size:var(--font-size-sm);text-transform:uppercase;letter-spacing:1px;border:3px solid var(--error-color);box-shadow:0 8px 32px rgba(225,6,0,0.4),inset 0 1px 0 rgba(255,255,255,0.2);text-shadow:0 2px 4px rgba(0,0,0,0.8);position:relative;overflow:hidden;animation:urgent-flash 1.5s ease-in-out infinite;&::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient( 90deg,transparent 0%,rgba(255,255,255,0.3) 50%,transparent 100% );animation:warning-sweep 2s linear infinite;}&::after{content:'⚠️';position:absolute;top:50%;left:15px;transform:translateY(-50%);font-size:20px;animation:warning-blink 1s ease-in-out infinite;}@keyframes urgent-flash{0%,100%{opacity:1;transform:scale(1);box-shadow:0 8px 32px rgba(225,6,0,0.4);}50%{opacity:0.9;transform:scale(1.02);box-shadow:0 12px 40px rgba(225,6,0,0.6);}}@keyframes warning-sweep{0%{left:-100%;}100%{left:100%;}}@keyframes warning-blink{0%,100%{opacity:1;}50%{opacity:0.5;}}"],({$show:e})=>e?"block":"none"),Nr=d.div.withConfig({displayName:"ReasoningBox",componentId:"sc-11yhh8h-16"})(["background:linear-gradient(135deg,var(--elite-glass-bg) 0%,rgba(42,42,42,0.4) 100%);border:1px solid var(--elite-glass-border);border-radius:var(--spacing-xs);padding:var(--spacing-lg);margin-top:var(--spacing-md);position:relative;backdrop-filter:blur(10px);transition:all var(--transition-normal);&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,var(--primary-color) 0%,var(--accent-color) 50%,var(--primary-color) 100% );animation:data-flow 4s linear infinite;}&::after{content:'💡';position:absolute;top:15px;right:15px;font-size:16px;opacity:0.7;animation:insight-glow 3s ease-in-out infinite;}&:hover{border-color:var(--primary-color);box-shadow:0 8px 32px rgba(0,255,228,0.1);}@keyframes data-flow{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}@keyframes insight-glow{0%,100%{opacity:0.7;transform:scale(1);}50%{opacity:1;transform:scale(1.1);}}"]),kr=d.div.withConfig({displayName:"ReasoningTitle",componentId:"sc-11yhh8h-17"})(["color:var(--primary-color);font-family:var(--racing-font);font-weight:700;margin-bottom:var(--spacing-xs);font-size:var(--font-size-sm);text-transform:uppercase;letter-spacing:1px;display:flex;align-items:center;gap:var(--spacing-xs);&::before{content:'▶';font-size:12px;opacity:0.8;}"]),Mr=({modelRecommendation:e,patternQuality:t,successProbability:i,setupIntelligence:r,isLoading:s=!1,error:o=null})=>{const a=It(),m=a.dayOfWeek==="Saturday"||a.dayOfWeek==="Sunday",l=!a.isOpen;return s?n.jsx(Pe,{children:n.jsx("div",{style:{textAlign:"center",padding:"20px",color:"var(--text-secondary)"},children:"Analyzing market conditions..."})}):o?n.jsx(Pe,{children:n.jsx("div",{style:{textAlign:"center",padding:"20px",color:"var(--error-text)"},children:"Error loading intelligence data"})}):n.jsxs(Pe,{role:"region","aria-labelledby":"elite-intelligence-title","aria-describedby":"market-context",children:[n.jsxs(fr,{children:[n.jsx(hr,{id:"elite-intelligence-title",children:"🏁 Elite Intelligence Command Center"}),n.jsx(Tt,{marketState:a})]}),l&&n.jsx(yr,{$isWeekend:m,id:"market-context",role:"status","aria-live":"polite",children:m?"📅 Weekend Analysis - Using most recent trading session data for pattern evaluation":`⏰ ${a.status.replace("_"," ")} - Analysis based on ${a.status==="PRE_MARKET"?"previous session":"current session"} data`}),n.jsxs(xr,{role:"grid","aria-label":"Trading intelligence summary",children:[n.jsxs(vr,{role:"gridcell","aria-labelledby":"model-recommendation-title","aria-describedby":"model-confidence",children:[n.jsx(wr,{id:"model-recommendation-title","aria-label":`Recommended trading model: ${e.recommendedModel}`,children:e.recommendedModel}),n.jsxs(br,{$confidence:e.confidence,id:"model-confidence","aria-label":`Confidence level: ${e.probability.toFixed(0)} percent ${e.confidence}`,children:[e.probability.toFixed(0),"% ",e.confidence]})]}),n.jsxs(Sr,{role:"gridcell","aria-labelledby":"setup-title","aria-describedby":"setup-stats",children:[n.jsx(Cr,{id:"setup-title",children:"Recommended Setup"}),n.jsxs(Ir,{"aria-label":`Trading setup: ${r.currentRecommendations.primarySetup} plus ${r.currentRecommendations.secondarySetup}`,children:[r.currentRecommendations.primarySetup," +"," ",r.currentRecommendations.secondarySetup]}),n.jsxs(Tr,{id:"setup-stats","aria-label":`Expected performance: ${r.currentRecommendations.expectedWinRate.toFixed(0)} percent win rate, ${r.currentRecommendations.expectedRMultiple.toFixed(1)} R average return`,children:[r.currentRecommendations.expectedWinRate.toFixed(0),"% Win Rate |"," ",r.currentRecommendations.expectedRMultiple.toFixed(1),"R Avg"]})]}),n.jsxs(jr,{role:"gridcell","aria-labelledby":"probability-label","aria-describedby":"probability-value",children:[n.jsxs(Rr,{$probability:i.finalProbability,id:"probability-value","aria-label":`Success probability: ${i.finalProbability.toFixed(0)} percent`,children:[i.finalProbability.toFixed(0),"%"]}),n.jsx(Dr,{id:"probability-label",children:"Success Probability"})]})]}),n.jsxs(Er,{$show:t.totalScore<2,role:"alert","aria-live":"assertive","aria-label":`Pattern quality warning: ${t.rating} quality with score ${t.totalScore.toFixed(1)} out of 5. Recommendation: ${t.recommendation}`,children:["⚠️ Current Pattern Quality: ",t.rating," (",t.totalScore.toFixed(1),"/5.0) - ",t.recommendation]}),n.jsxs(Nr,{role:"region","aria-labelledby":"reasoning-title","aria-describedby":"reasoning-content",children:[n.jsxs(kr,{id:"reasoning-title",children:["Why ",e.recommendedModel,"?"]}),n.jsx("div",{id:"reasoning-content",style:{fontFamily:"var(--font-body)",fontSize:"var(--font-size-sm)",lineHeight:"1.5",color:"var(--text-secondary)",fontWeight:"500"},"aria-label":`Model reasoning: ${e.reasoning}`,children:e.reasoning})]})]})},Ar=d.div.withConfig({displayName:"LayoutContainer",componentId:"sc-gtofto-0"})(["display:flex;flex-direction:column;gap:16px;"]),Pr=d.div.withConfig({displayName:"HeaderRight",componentId:"sc-gtofto-1"})(["display:flex;align-items:center;gap:12px;@media (max-width:768px){justify-content:space-between;}"]),Le=d.button.withConfig({displayName:"RefreshButton",componentId:"sc-gtofto-2"})(["background:none;border:1px solid var(--border-color);border-radius:6px;padding:8px 12px;font-size:12px;color:var(--text-secondary);cursor:pointer;transition:all 0.2s ease;display:flex;align-items:center;gap:6px;&:hover{background:var(--hover-bg);color:var(--text-primary);border-color:var(--primary-color);}&:disabled{opacity:0.5;cursor:not-allowed;}"]),Lr=d.div.withConfig({displayName:"ContentContainer",componentId:"sc-gtofto-3"})(["display:flex;flex-direction:column;gap:var(--spacing-md);"]),Fr=d.div.withConfig({displayName:"ExpandedContent",componentId:"sc-gtofto-4"})(["overflow:hidden;transition:all 0.3s ease-in-out;opacity:",";max-height:",";margin-top:",";"],({$isExpanded:e})=>e?1:0,({$isExpanded:e})=>e?"2000px":"0",({$isExpanded:e})=>e?"var(--spacing-md)":"0"),zr=d.button.withConfig({displayName:"ExpandToggleButton",componentId:"sc-gtofto-5"})(["background:var(--elite-section-bg);color:var(--text-primary);border:1px solid var(--primary-color);border-radius:var(--spacing-xs);padding:var(--spacing-xs) var(--spacing-md);font-size:var(--font-size-sm);font-weight:600;cursor:pointer;transition:all 0.3s ease;display:flex;align-items:center;gap:var(--spacing-xs);box-shadow:var(--shadow-sm);text-shadow:0 1px 2px rgba(0,0,0,0.3);letter-spacing:0.5px;text-transform:uppercase;&:hover:not(:disabled){background:var(--elite-card-bg);transform:translateY(-2px);box-shadow:var(--shadow-md);border-color:var(--accent-color);}&:disabled{opacity:0.6;cursor:not-allowed;transform:none;}&:active{transform:translateY(0);}.expand-icon{transition:transform 0.3s ease;transform:",";font-size:var(--font-size-xs);}"],({$isExpanded:e})=>e?"rotate(180deg)":"rotate(0deg)"),Wr=d.div.withConfig({displayName:"LoadingState",componentId:"sc-gtofto-6"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;color:var(--text-secondary);text-align:center;"]),_r=d.div.withConfig({displayName:"ErrorState",componentId:"sc-gtofto-7"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:60px 20px;color:var(--error-text);text-align:center;"]),Or=d.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-gtofto-8"})(["width:32px;height:32px;border:3px solid var(--border-color);border-top:3px solid var(--primary-color);border-radius:50%;animation:spin 1s linear infinite;margin-bottom:16px;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),$r=({isLoading:e=!1,error:t=null,onRefresh:i,className:r})=>{const[s,o]=T.useState(!1),a=It(),{recommendation:m,isLoading:l,error:p}=Yn(),{analysis:c,isLoading:v,error:b}=tr(),{isLoading:g,error:y}=Hn(),{successProbability:u,isLoading:x}=lr(m,c.currentScore),{setupIntelligence:S,isLoading:j,error:f}=_n(),h=e||l||v||g||x||j,w=t||p||b||y||f;T.useEffect(()=>{localStorage.getItem("elite-intelligence-expanded")==="true"&&o(!0)},[]);const I=()=>{const E=!s;o(E),localStorage.setItem("elite-intelligence-expanded",E.toString())};return h?n.jsx(K,{title:"🧠 Elite ICT Trading Intelligence",className:r,actions:i&&n.jsx(Le,{onClick:i,disabled:h,children:"🔄 Refresh"}),children:n.jsxs(Wr,{children:[n.jsx(Or,{}),n.jsx("div",{style:{fontSize:"16px",fontWeight:"500",marginBottom:"8px"},children:"Analyzing Market Conditions"}),n.jsx("div",{style:{fontSize:"14px",opacity:.7},children:"Generating intelligent trading recommendations..."})]})}):w?n.jsx(K,{title:"🧠 Elite ICT Trading Intelligence",className:r,actions:i&&n.jsx(Le,{onClick:i,children:"🔄 Retry"}),children:n.jsxs(_r,{children:[n.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"⚠️"}),n.jsx("div",{style:{fontSize:"16px",fontWeight:"500",marginBottom:"8px"},children:"Error Loading Intelligence Data"}),n.jsx("div",{style:{fontSize:"14px",opacity:.7},children:w})]})}):n.jsx(K,{title:"🧠 Elite ICT Trading Intelligence",className:r,actions:n.jsxs(Pr,{children:[n.jsx(Tt,{marketState:a,showDetails:!0}),n.jsxs(zr,{$isExpanded:s,onClick:I,disabled:h,children:[s?"📊 Collapse":"📊 Detailed Analysis",n.jsx("span",{className:"expand-icon",children:"▼"})]}),i&&n.jsx(Le,{onClick:i,disabled:h,children:"🔄 Refresh"})]}),children:n.jsx(Ar,{children:n.jsxs(Lr,{children:[n.jsx(Mr,{modelRecommendation:m,patternQuality:c.currentScore,successProbability:u,setupIntelligence:S}),n.jsx(Fr,{$isExpanded:s,children:n.jsx(dr,{modelRecommendation:m,patternQuality:c.currentScore,successProbability:u,setupIntelligence:S})})]})})})},jt=e=>{var a,m,l;const t=[],i=((a=e.trade.notes)==null?void 0:a.toLowerCase())||"",r=((m=e.trade.setup)==null?void 0:m.toLowerCase())||"",s=((l=e.trade.model_type)==null?void 0:l.toLowerCase())||"",o=`${i} ${r} ${s}`;return(o.includes("fvg")||o.includes("fair value gap")||o.includes("imbalance"))&&t.push({type:"FVG",details:`FVG from ${e.trade.date}`}),(o.includes("nwog")||o.includes("new week opening")||o.includes("weekly gap"))&&t.push({type:"NWOG",details:`NWOG level from ${e.trade.date}`}),(o.includes("ndog")||o.includes("new day opening")||o.includes("daily gap"))&&t.push({type:"NDOG",details:`NDOG level from ${e.trade.date}`}),(o.includes("rd")||o.includes("reaction")||o.includes("displacement"))&&t.push({type:"RD",details:`RD level from ${e.trade.date}`}),(o.includes("liquidity")||o.includes("sweep")||o.includes("raid")||o.includes("hunt"))&&t.push({type:"Liquidity",details:`Liquidity target from ${e.trade.date}`}),t},Gr=e=>{const t=new Date(e),r=Math.abs(new Date().getTime()-t.getTime()),s=Math.ceil(r/(1e3*60*60*24));return s===1?"1 day":s<7?`${s} days`:s<30?`${Math.floor(s/7)} weeks`:`${Math.floor(s/30)} months`},Hr=e=>{var o,a;const t=((o=e.trade.notes)==null?void 0:o.toLowerCase())||"",i=((a=e.trade.setup)==null?void 0:a.toLowerCase())||"",r=`${t} ${i}`;if(r.includes("daily")||r.includes("1d"))return"Daily";if(r.includes("4h")||r.includes("4hr"))return"4H";if(r.includes("1h")||r.includes("1hr"))return"1H";if(r.includes("15m")||r.includes("15min"))return"15M";if(r.includes("5m")||r.includes("5min"))return"5M";if(r.includes("1m")||r.includes("1min"))return"1M";const s=e.trade.session;return s==="NY Open"||s==="Lunch Macro"?"15M":"5M"},qr=(e,t)=>{const i=e.trade.entry_price,r=e.trade.exit_price;if(!i)return"N/A";switch(t){case"FVG":const s=Math.abs((r||i)-i);return`${(i-s/2).toFixed(0)}-${(i+s/2).toFixed(0)}`;case"NWOG":case"NDOG":case"RD":case"Liquidity":return i.toFixed(0);default:return i.toFixed(0)}},Vr=(e,t)=>{const i=t.filter(c=>jt(c).some(b=>b.type===e)),r=i.length,s=i.filter(c=>c.trade.win_loss==="Win").length,o=r>0?s/r*100:0,a=i.map(c=>c.trade.r_multiple).filter(c=>c!=null),m=a.length>0?a.reduce((c,v)=>c+v,0)/a.length:0,l=i.filter(c=>c.trade.win_loss==="Win"||c.trade.r_multiple&&c.trade.r_multiple>0).length,p=r>0?l/r*100:0;return{totalTrades:r,winRate:o,avgRMultiple:m,successRate:p}},Ur=(e,t,i)=>{const{winRate:r,avgRMultiple:s,totalTrades:o}=t;let a="";switch(e){case"FVG":a="Target FVG fills with confluence";break;case"NWOG":a="High-probability parent PD array reactions";break;case"NDOG":a="Daily opening gap redelivery opportunities";break;case"RD":a="Reaction delivery continuation setups";break;case"Liquidity":a="Liquidity sweep and reversal opportunities";break}let m="MEDIUM",l="";return o>=3?r>=70&&s>=1.5?(m="HIGH",l=` (${r.toFixed(0)}% win rate, ${s.toFixed(1)}R avg)`):r>=60||s>=1.2?(m="MEDIUM",l=` (${r.toFixed(0)}% win rate)`):(m="LOW",l=` (${r.toFixed(0)}% win rate - use caution)`):o>0?l=` (${o} trade${o>1?"s":""} - limited data)`:(l=" (no historical data)",m="LOW"),i.includes("day")&&!i.includes("days")?l+=" - Fresh level":i.includes("week")&&(l+=" - Established level"),{recommendation:a+l,priority:m}},Br=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);return T.useEffect(()=>{(async()=>{try{r(!0),o(null);const l=await ee.getAllTrades();t(l)}catch(l){console.error("Error fetching trades for PD Array intelligence:",l),o("Failed to load trade data")}finally{r(!1)}})()},[]),{pdArrayIntelligence:T.useMemo(()=>{if(e.length===0)return null;const m=new Date;m.setDate(m.getDate()-30);const l=e.filter(f=>new Date(f.trade.date)>=m),p=new Map;l.forEach(f=>{jt(f).forEach(w=>{const I=`${w.type}-${f.trade.date}-${f.trade.entry_price}`;p.has(I)||p.set(I,{type:w.type,trade:f})})});const c=Array.from(p.values()).map(({type:f,trade:h})=>{const w=Vr(f,e),I=Gr(h.trade.date),E=Hr(h),F=qr(h,f),{recommendation:D,priority:A}=Ur(f,w,I),C=w.totalTrades>=2&&w.winRate>=50||w.totalTrades<2&&I.includes("day");return{type:f,level:F,timeframe:E,age:I,isActive:C,performance:w,recommendation:D,priority:A}});c.sort((f,h)=>{const w={HIGH:3,MEDIUM:2,LOW:1},I=w[f.priority],E=w[h.priority];return I!==E?E-I:h.performance.winRate-f.performance.winRate});const v=c.filter(f=>f.isActive).length,g=["FVG","NWOG","NDOG","RD","Liquidity"].map(f=>{const h=c.filter(I=>I.type===f),w=h.length>0?h.reduce((I,E)=>I+E.performance.winRate,0)/h.length:0;return{type:f,avgWinRate:w,count:h.length}}).reduce((f,h)=>h.avgWinRate>f.avgWinRate&&h.count>0?h:f,{type:"N/A",avgWinRate:0,count:0}),y=c.length>0?c.reduce((f,h)=>f+h.performance.successRate,0)/c.length:0,u=c.length>0?c.reduce((f,h)=>f+h.performance.avgRMultiple,0)/c.length:0,x=[];g.type!=="N/A"&&x.push(`${g.type} arrays show best performance (${g.avgWinRate.toFixed(0)}% avg win rate)`);const S=c.filter(f=>f.priority==="HIGH").length;S>0&&x.push(`${S} high-priority levels active`),y>=70?x.push("Strong PD Array performance overall"):y<50&&x.push("Focus on quality over quantity");const j={totalActiveLevels:v,bestPerformingType:g.type,overallSuccessRate:y,avgRMultiple:u,keyInsights:x};return{activePDArrays:c.slice(0,10),summary:j,lastUpdated:new Date().toISOString()}},[e]),isLoading:i,error:s,refresh:()=>{t([])}}},Yr=d.div.withConfig({displayName:"Container",componentId:"sc-1myzwi7-0"})(["display:flex;flex-direction:column;gap:24px;"]),nt=d.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1myzwi7-1"})(["color:var(--session-text-primary);font-size:18px;font-weight:700;margin-bottom:16px;display:flex;align-items:center;gap:8px;"]),rt=d.div.attrs(({arrayType:e,isActive:t})=>({className:`pd-array-card PDArrayCard ${t?"active":""}`,"data-array-type":e.toLowerCase(),"data-active":t})).withConfig({displayName:"PDArrayCard",componentId:"sc-1myzwi7-2"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-left:4px solid var(--session-card-accent);border-radius:12px;padding:16px;margin-bottom:12px;transition:all 0.2s ease;box-shadow:var(--shadow-sm);&[data-active='true']{border-left-color:var(--pd-array-accent,var(--session-card-accent));box-shadow:var(--shadow-sm),0 0 0 1px var(--pd-array-accent,var(--session-card-accent));}&[data-array-type='fvg'][data-active='true']{--pd-array-accent:var(--info-color);}&[data-array-type='nwog'][data-active='true']{--pd-array-accent:var(--success-color);}&[data-array-type='ndog'][data-active='true']{--pd-array-accent:var(--warning-color);}&[data-array-type='rd'][data-active='true']{--pd-array-accent:var(--error-color);}&[data-array-type='liquidity'][data-active='true']{--pd-array-accent:var(--secondary-color);}&[data-array-type='summary'][data-active='true']{--pd-array-accent:var(--primary-color);}"]),Kr=d.div.withConfig({displayName:"ArrayHeader",componentId:"sc-1myzwi7-3"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;"]),Zr=d.div.attrs(({arrayType:e})=>({className:"array-type ArrayType","data-array-type":e.toLowerCase()})).withConfig({displayName:"ArrayType",componentId:"sc-1myzwi7-4"})(["font-size:16px;font-weight:700;text-transform:uppercase;letter-spacing:0.5px;&[data-array-type='fvg']{color:var(--info-color);}&[data-array-type='nwog']{color:var(--success-color);}&[data-array-type='ndog']{color:var(--warning-color);}&[data-array-type='rd']{color:var(--error-color);}&[data-array-type='liquidity']{color:var(--secondary-color);}&[data-array-type='summary']{color:var(--primary-color);}color:var(--session-text-primary);"]),Qr=d.div.attrs(({isActive:e})=>({className:"array-status ArrayStatus","data-active":e})).withConfig({displayName:"ArrayStatus",componentId:"sc-1myzwi7-5"})(["background:",";color:",";padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;text-transform:uppercase;"],({isActive:e})=>e?"var(--success-color)":"var(--session-card-border)",({isActive:e})=>e?"var(--session-text-primary)":"var(--session-text-secondary)"),Xr=d.div.withConfig({displayName:"LevelInfo",componentId:"sc-1myzwi7-6"})(["display:grid;grid-template-columns:repeat(3,1fr);gap:12px;margin:12px 0;"]),Fe=d.div.withConfig({displayName:"LevelDetail",componentId:"sc-1myzwi7-7"})(["text-align:center;"]),ze=d.div.withConfig({displayName:"LevelValue",componentId:"sc-1myzwi7-8"})(["font-size:16px;font-weight:700;color:var(--session-text-primary);"]),We=d.div.withConfig({displayName:"LevelLabel",componentId:"sc-1myzwi7-9"})(["font-size:10px;color:var(--session-text-secondary);text-transform:uppercase;letter-spacing:0.5px;margin-top:2px;"]),Jr=d.div.attrs({className:"performance-stats PerformanceStats"}).withConfig({displayName:"PerformanceStats",componentId:"sc-1myzwi7-10"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-radius:8px;padding:12px;margin-top:12px;box-shadow:var(--shadow-sm);"]),it=d.div.withConfig({displayName:"StatsGrid",componentId:"sc-1myzwi7-11"})(["display:grid;grid-template-columns:repeat(4,1fr);gap:8px;"]),Q=d.div.withConfig({displayName:"StatItem",componentId:"sc-1myzwi7-12"})(["text-align:center;"]),X=d.div.withConfig({displayName:"StatValue",componentId:"sc-1myzwi7-13"})(["font-size:14px;font-weight:600;color:var(--session-text-primary);"]),J=d.div.withConfig({displayName:"StatLabel",componentId:"sc-1myzwi7-14"})(["font-size:9px;color:var(--session-text-secondary);text-transform:uppercase;"]),ei=d.div.attrs(({priority:e})=>({className:"priority-badge","data-priority":e})).withConfig({displayName:"PriorityBadge",componentId:"sc-1myzwi7-15"})(["background:",";color:",";padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;text-transform:uppercase;margin-left:8px;"],({priority:e})=>{switch(e){case"HIGH":return"var(--error-color)";case"MEDIUM":return"var(--warning-color)";default:return"var(--session-card-border)"}},({priority:e})=>e==="LOW"?"var(--session-text-secondary)":"var(--session-text-primary)"),st=d.div.withConfig({displayName:"RecommendationText",componentId:"sc-1myzwi7-16"})(["color:var(--session-text-secondary);font-size:12px;line-height:1.4;margin-top:8px;padding-top:8px;border-top:1px solid var(--session-card-border);"]),ti=d.div.withConfig({displayName:"EmptyState",componentId:"sc-1myzwi7-17"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-radius:12px;text-align:center;padding:40px 20px;color:var(--session-text-secondary);"]),ni=d.div.withConfig({displayName:"EmptyIcon",componentId:"sc-1myzwi7-18"})(["font-size:48px;margin-bottom:16px;"]),ri=d.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-1myzwi7-19"})(["color:var(--session-text-primary);font-size:18px;margin-bottom:8px;"]),ii=d.p.withConfig({displayName:"EmptyMessage",componentId:"sc-1myzwi7-20"})(["color:var(--session-text-secondary);font-size:14px;line-height:1.5;"]),si=({isLoading:e=!1,error:t=null,onRefresh:i,className:r})=>{const{pdArrayIntelligence:s,isLoading:o,error:a}=Br(),m=e||o,l=t||a;return m?n.jsx(K,{title:"🎯 PD Array Intelligence",children:n.jsx("div",{style:{padding:"24px",textAlign:"center"},children:"Analyzing PD Array levels and liquidity targets..."})}):l?n.jsx(K,{title:"🎯 PD Array Intelligence",children:n.jsxs("div",{style:{padding:"24px",textAlign:"center",color:"var(--error-color)"},children:["Error: ",l,i&&n.jsx("button",{onClick:i,style:{marginLeft:"16px",padding:"8px 16px",background:"var(--session-card-bg)",border:"1px solid var(--session-card-border)",borderRadius:"4px",cursor:"pointer",color:"var(--session-text-primary)"},children:"Retry"})]})}):!s||s.activePDArrays.length===0?n.jsx(K,{title:"🎯 PD Array Intelligence",children:n.jsxs(ti,{children:[n.jsx(ni,{children:"📊"}),n.jsx(ri,{children:"No PD Array Data Available"}),n.jsx(ii,{children:"Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and liquidity targets. The system will analyze your historical performance with each PD Array type."})]})}):n.jsx(K,{title:"🎯 PD Array Intelligence",actions:i?n.jsx("button",{onClick:i,style:{background:"none",border:"none",cursor:"pointer",color:"inherit"},children:"🔄 Refresh"}):void 0,children:n.jsxs(Yr,{className:r,children:[n.jsxs("div",{children:[n.jsxs(nt,{children:["🔥 Active PD Arrays",n.jsx(ei,{priority:"HIGH",children:"PRIORITY TARGETS"})]}),s.activePDArrays.map((p,c)=>n.jsxs(rt,{arrayType:p.type,isActive:p.isActive,children:[n.jsxs(Kr,{children:[n.jsx(Zr,{arrayType:p.type,children:p.type}),n.jsx(Qr,{isActive:p.isActive,children:p.isActive?"ACTIVE":"INACTIVE"})]}),n.jsxs(Xr,{children:[n.jsxs(Fe,{children:[n.jsx(ze,{children:p.level}),n.jsx(We,{children:"Level"})]}),n.jsxs(Fe,{children:[n.jsx(ze,{children:p.timeframe}),n.jsx(We,{children:"Timeframe"})]}),n.jsxs(Fe,{children:[n.jsx(ze,{children:p.age}),n.jsx(We,{children:"Age"})]})]}),n.jsx(Jr,{children:n.jsxs(it,{children:[n.jsxs(Q,{children:[n.jsx(X,{children:p.performance.totalTrades}),n.jsx(J,{children:"Trades"})]}),n.jsxs(Q,{children:[n.jsxs(X,{children:[p.performance.winRate.toFixed(0),"%"]}),n.jsx(J,{children:"Win Rate"})]}),n.jsxs(Q,{children:[n.jsxs(X,{children:[p.performance.avgRMultiple.toFixed(1),"R"]}),n.jsx(J,{children:"Avg R"})]}),n.jsxs(Q,{children:[n.jsxs(X,{children:[p.performance.successRate.toFixed(0),"%"]}),n.jsx(J,{children:"Success Rate"})]})]})}),n.jsxs(st,{children:[n.jsx("strong",{children:"Strategy:"})," ",p.recommendation]})]},c))]}),n.jsxs("div",{children:[n.jsx(nt,{children:"📊 PD Array Performance Summary"}),n.jsxs(rt,{arrayType:"summary",isActive:!0,children:[n.jsxs(it,{children:[n.jsxs(Q,{children:[n.jsx(X,{children:s.summary.totalActiveLevels}),n.jsx(J,{children:"Active Levels"})]}),n.jsxs(Q,{children:[n.jsx(X,{children:s.summary.bestPerformingType}),n.jsx(J,{children:"Best Type"})]}),n.jsxs(Q,{children:[n.jsxs(X,{children:[s.summary.overallSuccessRate.toFixed(0),"%"]}),n.jsx(J,{children:"Overall Success"})]}),n.jsxs(Q,{children:[n.jsxs(X,{children:[s.summary.avgRMultiple.toFixed(1),"R"]}),n.jsx(J,{children:"Avg R-Multiple"})]})]}),n.jsxs(st,{children:[n.jsx("strong",{children:"Key Insights:"})," ",s.summary.keyInsights.join(" • ")]})]})]})]})})},oi=e=>{if(e.trade.session)return e.trade.session;const t=e.trade.entry_time;if(!t)return null;const[i,r]=t.split(":").map(Number),s=i*60+r;return s>=480&&s<570?"Pre-Market":s>=570&&s<660?"NY Open":s>=710&&s<810?"Lunch Macro":s>=915&&s<960?"MOC":null},ai=(e,t)=>({"Pre-Market":[{start:"08:00",end:"08:30",description:"Early Pre-Market Setup"},{start:"08:30",end:"09:00",description:"Prime Pre-Market Window"},{start:"09:00",end:"09:30",description:"Market Prep Phase"}],"NY Open":[{start:"09:30",end:"09:45",description:"Market Open Volatility"},{start:"09:45",end:"10:15",description:"OPTIMAL WINDOW"},{start:"10:15",end:"10:45",description:"Secondary Opportunity"},{start:"10:45",end:"11:00",description:"Session Wind-down"}],"Lunch Macro":[{start:"11:50",end:"12:10",description:"PRIMARY WINDOW"},{start:"12:10",end:"12:30",description:"Continuation Phase"},{start:"12:30",end:"13:00",description:"Midday Consolidation"},{start:"13:00",end:"13:30",description:"Afternoon Transition"}],MOC:[{start:"15:15",end:"15:45",description:"Pre-Close Setup"},{start:"15:45",end:"16:00",description:"Final Momentum"}]}[t]||[]).map(s=>{const o=e.filter(l=>{const p=l.trade.entry_time;if(!p)return!1;const[c,v]=p.split(":").map(Number),b=c*60+v,[g,y]=s.start.split(":").map(Number),[u,x]=s.end.split(":").map(Number),S=g*60+y,j=u*60+x;return b>=S&&b<=j}),a=o.filter(l=>l.trade.win_loss==="Win").length,m=o.length>0?a/o.length*100:0;return{...s,winRate:m,trades:o.length}}),ci=(e,t)=>{const i=e,r=t.length,s=t.filter(C=>C.trade.win_loss==="Win").length,o=r>0?s/r*100:0,a=t.map(C=>C.trade.r_multiple).filter(C=>C!=null),m=a.length>0?a.reduce((C,N)=>C+N,0)/a.length:0,l=t.map(C=>C.trade.achieved_pl||0).reduce((C,N)=>C+N,0),p=t.map(C=>C.trade.risk_points||0).filter(C=>C>0),c=p.length>0?p.reduce((C,N)=>C+N,0)/p.length:0,v=t.filter(C=>C.trade.model_type==="RD-Cont"),b=t.filter(C=>C.trade.model_type==="FVG-RD"),g=v.filter(C=>C.trade.win_loss==="Win").length,y=b.filter(C=>C.trade.win_loss==="Win").length,u=v.length>0?g/v.length*100:0,x=b.length>0?y/b.length*100:0,S=v.map(C=>C.trade.r_multiple).filter(C=>C!==void 0),j=b.map(C=>C.trade.r_multiple).filter(C=>C!==void 0),f=S.length>0?S.reduce((C,N)=>C+N,0)/S.length:0,h=j.length>0?j.reduce((C,N)=>C+N,0)/j.length:0;let w="Either";v.length>=2&&b.length>=2?u>x+10?w="RD-Cont":x>u+10&&(w="FVG-RD"):v.length>=3?w="RD-Cont":b.length>=3&&(w="FVG-RD");const I=t.filter(C=>C.trade.win_loss==="Win").map(C=>C.trade.pattern_quality_rating).filter(C=>C!=null),E=I.length>0?I.reduce((C,N)=>C+N,0)/I.length:3.5,F=Math.max(3,E-.2),D=[];return w!=="Either"&&D.push(`Focus on ${w} setups (${w==="RD-Cont"?u.toFixed(0):x.toFixed(0)}% win rate)`),F>3&&D.push(`Maintain pattern quality >${F.toFixed(1)} (your success threshold)`),c>0&&D.push(`Target ${c.toFixed(0)} point risk (your historical average)`),{sessionName:e,sessionType:i,timeRange:{"Pre-Market":"08:00-09:30","NY Open":"09:30-11:00","Lunch Macro":"11:50-13:30",MOC:"15:15-16:00"}[i]||"",performance:{totalTrades:r,winningTrades:s,winRate:o,avgRMultiple:m,totalPnL:l,avgRisk:c},modelPreference:{rdCont:{trades:v.length,winRate:u,avgR:f},fvgRd:{trades:b.length,winRate:x,avgR:h},recommendation:w},optimalWindows:ai(t,i),qualityThreshold:F,recommendations:D}},li=()=>{const[e,t]=T.useState([]),[i,r]=T.useState(!0),[s,o]=T.useState(null);return T.useEffect(()=>{(async()=>{try{r(!0),o(null);const l=await ee.getAllTrades();t(l)}catch(l){console.error("Error fetching trades for enhanced session intelligence:",l),o("Failed to load trade data")}finally{r(!1)}})()},[]),{intelligence:T.useMemo(()=>{var oe;if(e.length===0){const R=ae();return{sessions:[],currentStatus:{currentTime:R.nyTime,currentTimeFormatted:R.formatted,activeSession:null,nextSession:null,timeToNext:0,timeToNextFormatted:"N/A",isOptimalWindow:!1,currentRecommendation:"No trading data available",urgency:"LOW"},weeklyInsights:{bestSession:"N/A",bestModel:"RD-Cont",avgQuality:0,qualityThreshold:3.5,recommendations:["Import trading data to begin analysis"]}}}const m=e.reduce((R,M)=>{const W=oi(M);return W&&(R[W]||(R[W]=[]),R[W].push(M)),R},{}),l=Object.entries(m).map(([R,M])=>ci(R,M)),p=ae(),c=p.nyTime,v=p.formatted,b=Nt(),g=l.find(R=>{if(!R.timeRange)return!1;const[M,W]=R.timeRange.split("-");if(!M||!W)return!1;const[H,k]=M.split(":").map(Number),[Y,we]=W.split(":").map(Number),Dt=H*60+k,Et=Y*60+we;return b>=Dt&&b<=Et})||null,y=(()=>{const R=l.find(M=>{if(!M.timeRange)return!1;const[W]=M.timeRange.split("-");if(!W)return!1;const[H,k]=W.split(":").map(Number);return H*60+k>b});return R||(l.length>0?l[0]:null)})(),u=y&&y.timeRange?(()=>{const[R]=y.timeRange.split("-");if(!R)return 0;const[M,W]=R.split(":").map(Number);let k=M*60+W-b;return k<=0&&(k=24*60+k),k})():0,x=u>0?xt(u).formatted:"N/A",S=g?g.optimalWindows.some(R=>{const[M,W]=R.start.split(":").map(Number),[H,k]=R.end.split(":").map(Number),Y=M*60+W,we=H*60+k;return b>=Y&&b<=we}):!1;let j="",f="LOW";g&&S?(j=`OPTIMAL WINDOW ACTIVE - ${g.sessionName} (${g.performance.winRate.toFixed(0)}% win rate)`,f="HIGH"):g?(j=`${g.sessionName} active - ${g.modelPreference.recommendation} focus`,f="MEDIUM"):y&&u<=30?(j=`${y.sessionName} starting in ${x}`,f="MEDIUM"):(j="No active trading session - Monitor for setups",f="LOW");const h=l.reduce((R,M)=>M.performance.winRate>R.performance.winRate?M:R,l[0]||{sessionName:"N/A",performance:{winRate:0}}).sessionName,w=e.filter(R=>R.trade.model_type==="RD-Cont"),I=e.filter(R=>R.trade.model_type==="FVG-RD"),E=w.length>0?w.filter(R=>R.trade.win_loss==="Win").length/w.length*100:0,F=I.length>0?I.filter(R=>R.trade.win_loss==="Win").length/I.length*100:0,D=E>=F?"RD-Cont":"FVG-RD",A=e.map(R=>R.trade.pattern_quality_rating).filter(R=>R!=null),C=A.length>0?A.reduce((R,M)=>R+M,0)/A.length:0,N=e.filter(R=>R.trade.win_loss==="Win").map(R=>R.trade.pattern_quality_rating).filter(R=>R!=null),P=N.length>0?N.reduce((R,M)=>R+M,0)/N.length:3.5,_=Math.max(3,P-.2),ne=[`Focus on ${h} session (${((oe=l.find(R=>R.sessionName===h))==null?void 0:oe.performance.winRate.toFixed(0))||0}% win rate)`,`Prioritize ${D} setups (${D==="RD-Cont"?E.toFixed(0):F.toFixed(0)}% success rate)`,`Maintain pattern quality >${_.toFixed(1)} (your success threshold)`];return{sessions:l,currentStatus:{currentTime:c,currentTimeFormatted:v,activeSession:g,nextSession:y,timeToNext:u,timeToNextFormatted:x,isOptimalWindow:S,currentRecommendation:j,urgency:f},weeklyInsights:{bestSession:h,bestModel:D,avgQuality:C,qualityThreshold:_,recommendations:ne}}},[e]),isLoading:i,error:s,refresh:()=>{t([])}}},di=d.div.withConfig({displayName:"Container",componentId:"sc-1t1scof-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"16px"}),pi=d.div.attrs({className:"session-card current-session-card"}).withConfig({displayName:"CurrentSessionCard",componentId:"sc-1t1scof-1"})(["background:var(--session-card-bg);border:1px solid var(--session-card-accent);border-left:4px solid var(--session-card-accent);border-radius:12px;padding:20px;position:relative;overflow:hidden;box-shadow:var(--shadow-md),0 0 0 1px var(--session-card-accent);&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,var(--session-card-accent),var(--session-card-active-accent),var(--session-card-accent) );animation:pulse 2s ease-in-out infinite;}@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.7;}}"]),mi=d.div.withConfig({displayName:"SessionHeader",componentId:"sc-1t1scof-2"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:16px;"]),ui=d.h3.withConfig({displayName:"SessionTitle",componentId:"sc-1t1scof-3"})(["color:var(--session-text-primary);font-size:18px;font-weight:600;margin:0;display:flex;align-items:center;gap:8px;"]),_e=d.span.withConfig({displayName:"LiveIndicatorSpan",componentId:"sc-1t1scof-4"})(["background:",";color:",";padding:4px 8px;border-radius:12px;font-size:10px;font-weight:700;text-transform:uppercase;letter-spacing:0.5px;animation:f1Pulse 2s ease-in-out infinite;box-shadow:0 0 8px ","40;@keyframes f1Pulse{0%,100%{opacity:1;transform:scale(1);}50%{opacity:0.8;transform:scale(1.05);}}"],({theme:e})=>`linear-gradient(135deg, ${e.colors.sessionActive}, ${e.colors.sessionOptimal})`,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.sessionActive),gi=d.div.withConfig({displayName:"RecommendationBadge",componentId:"sc-1t1scof-5"})(["padding:6px 12px;border-radius:6px;font-size:14px;font-weight:600;color:var(--session-text-primary);text-transform:uppercase;letter-spacing:0.5px;background:",";",""],({level:e,theme:t})=>{switch(e){case"high":return`linear-gradient(135deg, ${t.colors.sessionActive}, ${t.colors.sessionOptimal})`;case"medium":return`linear-gradient(135deg, ${t.colors.sessionCaution}, ${t.colors.sessionTransition})`;case"low":return`linear-gradient(135deg, ${t.colors.sessionTransition}, ${t.colors.performancePoor})`;case"avoid":return`linear-gradient(135deg, ${t.colors.performanceAvoid}, ${t.colors.error})`;default:return"var(--session-card-border)"}},({level:e})=>e==="high"&&`
    position: relative;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 3px;
      background: linear-gradient(180deg, var(--session-optimal), var(--session-active));
      border-radius: 6px 0 0 6px;
    }
  `),fi=d.div.withConfig({displayName:"MetricsGrid",componentId:"sc-1t1scof-6"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(120px,1fr));gap:16px;margin-bottom:16px;"]),Oe=d.div.withConfig({displayName:"MetricCard",componentId:"sc-1t1scof-7"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-radius:6px;padding:12px;text-align:center;box-shadow:var(--shadow-sm);"]),$e=d.div.withConfig({displayName:"MetricValue",componentId:"sc-1t1scof-8"})(["font-size:20px;font-weight:700;color:",";margin-bottom:4px;"],({theme:e})=>e.colors.textPrimary),Ge=d.div.withConfig({displayName:"MetricLabel",componentId:"sc-1t1scof-9"})(["font-size:12px;color:",";text-transform:uppercase;letter-spacing:0.5px;"],({theme:e})=>e.colors.textSecondary),ot=d.div.withConfig({displayName:"ActionItems",componentId:"sc-1t1scof-10"})(["background:",";border:2px solid ",";border-left:6px solid ",";border-radius:8px;padding:16px;position:relative;&::before{content:'';position:absolute;left:0;top:0;height:100%;width:4px;background:linear-gradient( 180deg,",","," );border-radius:8px 0 0 8px;}"],({theme:e})=>`linear-gradient(135deg, ${e.colors.sessionActive}20, ${e.colors.sessionOptimal}10)`,({theme:e})=>e.colors.sessionActive,({theme:e})=>e.colors.sessionActive,({theme:e})=>e.colors.sessionOptimal,({theme:e})=>e.colors.sessionActive),at=d.h4.withConfig({displayName:"ActionTitle",componentId:"sc-1t1scof-11"})(["color:",";font-size:14px;font-weight:700;margin:0 0 12px 0;text-transform:uppercase;letter-spacing:0.5px;text-shadow:0 0 4px ","40;"],({theme:e})=>e.colors.sessionActive,({theme:e})=>e.colors.sessionActive),ct=d.ul.withConfig({displayName:"ActionList",componentId:"sc-1t1scof-12"})(["margin:0;padding:0;list-style:none;"]),lt=d.li.withConfig({displayName:"ActionItem",componentId:"sc-1t1scof-13"})(["color:",";font-size:14px;margin-bottom:8px;padding-left:20px;position:relative;&::before{content:'▶';position:absolute;left:0;color:",";font-size:12px;text-shadow:0 0 4px ","40;}&:last-child{margin-bottom:0;}"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.sessionActive,({theme:e})=>e.colors.sessionActive),dt=d.div.attrs(({isActive:e,isOptimal:t})=>({className:`session-card ict-session-card ${e?"active":""} ${t?"optimal":""}`,"data-session-state":e&&t?"active-optimal":e?"active":"inactive"})).withConfig({displayName:"ICTSessionCard",componentId:"sc-1t1scof-14"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-left:4px solid var(--session-card-accent);border-radius:12px;padding:16px;margin-bottom:16px;transition:all 0.2s ease;box-shadow:var(--shadow-sm);&[data-session-state='active']{border-left-color:var(--session-active);border-color:var(--session-active);box-shadow:var(--shadow-md),0 0 0 1px var(--session-active);}&[data-session-state='active-optimal']{border-left-color:var(--session-optimal);border-color:var(--session-optimal);box-shadow:var(--shadow-md),0 0 0 1px var(--session-optimal),var(--shadow-accent);}"]),pt=d.div.withConfig({displayName:"ICTSessionHeader",componentId:"sc-1t1scof-15"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;"]),mt=d.div.withConfig({displayName:"ICTSessionName",componentId:"sc-1t1scof-16"})(["font-size:16px;font-weight:700;color:",";display:flex;align-items:center;gap:8px;text-shadow:none;"],({isActive:e})=>e?"var(--session-active)":"var(--session-text-primary)"),hi=d.div.withConfig({displayName:"ICTTimeRange",componentId:"sc-1t1scof-17"})(["font-size:12px;color:var(--session-text-secondary);"]),xi=d.div.withConfig({displayName:"ICTPerformanceGrid",componentId:"sc-1t1scof-18"})(["display:grid;grid-template-columns:repeat(4,1fr);gap:12px;margin:12px 0;"]),fe=d.div.withConfig({displayName:"ICTMetric",componentId:"sc-1t1scof-19"})(["text-align:center;"]),he=d.div.withConfig({displayName:"ICTMetricValue",componentId:"sc-1t1scof-20"})(["font-size:18px;font-weight:700;color:var(--session-text-primary);"]),xe=d.div.withConfig({displayName:"ICTMetricLabel",componentId:"sc-1t1scof-21"})(["font-size:10px;color:var(--session-text-secondary);text-transform:uppercase;letter-spacing:0.5px;"]),yi=d.div.withConfig({displayName:"ModelPreferenceHeader",componentId:"sc-1t1scof-22"})(["font-size:12px;color:var(--session-text-secondary);text-transform:uppercase;letter-spacing:0.5px;margin-bottom:8px;font-weight:600;"]),vi=d.div.withConfig({displayName:"OptimalWindowsGrid",componentId:"sc-1t1scof-23"})(["display:grid;grid-template-columns:repeat(2,1fr);gap:8px;margin-top:8px;"]),ut=d.div.attrs(({isOptimal:e})=>({className:`window-card optimal-window-card ${e?"optimal":""}`})).withConfig({displayName:"WindowCard",componentId:"sc-1t1scof-24"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-radius:6px;padding:8px;text-align:center;transition:all 0.2s ease;box-shadow:var(--shadow-sm);",""],({isOptimal:e})=>e&&`
    border-left: 3px solid var(--session-optimal);
    border-color: var(--session-optimal);
    box-shadow:
      var(--shadow-sm),
      0 0 0 1px var(--session-optimal);
  `),gt=d.div.attrs({className:"primary-text session-title"}).withConfig({displayName:"WindowTime",componentId:"sc-1t1scof-25"})(["font-size:11px;font-weight:600;color:var(--session-text-primary);"]),ft=d.div.attrs({className:"secondary-text session-description"}).withConfig({displayName:"WindowDescription",componentId:"sc-1t1scof-26"})(["font-size:9px;color:var(--session-text-secondary);margin:2px 0;"]),ht=d.div.attrs({className:"secondary-text metric-label"}).withConfig({displayName:"WindowStats",componentId:"sc-1t1scof-27"})(["font-size:9px;color:var(--session-text-secondary);"]),wi=d.div.withConfig({displayName:"WeeklyInsightsCard",componentId:"sc-1t1scof-28"})(["background:var(--session-card-bg);border:1px solid var(--secondary-color);border-left:4px solid var(--secondary-color);border-radius:12px;padding:16px;margin-top:16px;box-shadow:var(--shadow-sm);"]),bi=d.div.withConfig({displayName:"InsightsHeader",componentId:"sc-1t1scof-29"})(["font-size:14px;font-weight:700;color:var(--secondary-color);margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]),Si=d.div.withConfig({displayName:"InsightsList",componentId:"sc-1t1scof-30"})(["display:flex;flex-direction:column;gap:8px;"]),ye=d.div.withConfig({displayName:"InsightItem",componentId:"sc-1t1scof-31"})(["font-size:12px;color:var(--session-text-secondary);padding:6px 0;border-bottom:1px solid var(--session-card-border);&:last-child{border-bottom:none;}"]),Ci=({isLoading:e=!1,error:t=null,onRefresh:i,className:r})=>{var v,b;const{isLoading:s,error:o}=gn(),{intelligence:a,isLoading:m,error:l}=li(),p=e||s||m,c=t||o||l;return p?n.jsx(K,{title:"🎯 Session Focus",children:n.jsx("div",{style:{padding:"24px",textAlign:"center"},children:"Analyzing your trading sessions..."})}):c?n.jsx(K,{title:"🎯 Session Focus",children:n.jsxs("div",{style:{padding:"24px",textAlign:"center",color:"var(--error-color)"},children:["Error: ",c,i&&n.jsx("button",{onClick:i,style:{marginLeft:"16px",padding:"8px 16px",background:"var(--session-card-bg)",border:"1px solid var(--session-card-border)",borderRadius:"4px",cursor:"pointer",color:"var(--session-text-primary)"},children:"Retry"})]})}):n.jsx(K,{title:"🕘 Enhanced Session Intelligence",actions:i?n.jsx("button",{onClick:i,style:{background:"none",border:"none",cursor:"pointer",color:"inherit"},children:"🔄 Refresh"}):void 0,children:n.jsxs(di,{className:r,children:[n.jsxs(pi,{children:[n.jsxs(mi,{children:[n.jsxs(ui,{children:[a.currentStatus.currentRecommendation,a.currentStatus.activeSession&&n.jsx(_e,{children:"LIVE"})]}),n.jsx(gi,{level:a.currentStatus.urgency.toLowerCase(),children:a.currentStatus.urgency})]}),n.jsxs(fi,{children:[n.jsxs(Oe,{children:[n.jsx($e,{children:n.jsx(ue,{mode:"current",format:"compact",showLive:!0})}),n.jsx(Ge,{children:"Current Time"})]}),n.jsxs(Oe,{children:[n.jsx($e,{children:((v=a.currentStatus.activeSession)==null?void 0:v.sessionName)||"None"}),n.jsx(Ge,{children:"Active Session"})]}),n.jsxs(Oe,{children:[n.jsx($e,{children:a.currentStatus.timeToNextFormatted}),n.jsx(Ge,{children:"Next Session"})]})]})]}),a.currentStatus.activeSession&&n.jsxs(dt,{isActive:!0,isOptimal:!0,children:[n.jsx(pt,{children:n.jsxs(mt,{isActive:!0,children:["⏰ LIVE SESSION INTELLIGENCE",n.jsx(_e,{children:"LIVE SESSION"})]})}),n.jsxs("div",{style:{marginBottom:"16px"},children:[n.jsxs("h4",{style:{color:"var(--session-text-primary)",marginBottom:"12px",fontSize:"16px"},children:[a.currentStatus.activeSession.sessionName," - Active"]}),n.jsx("div",{style:{display:"grid",gap:"12px"},children:(b=a.sessions.find(g=>{var y;return g.sessionName===((y=a.currentStatus.activeSession)==null?void 0:y.sessionName)}))==null?void 0:b.optimalWindows.map((g,y)=>n.jsxs(ut,{isOptimal:g.winRate>=70&&g.trades>=2,children:[n.jsx(gt,{children:n.jsx(ue,{mode:"session",sessionStart:g.start,sessionEnd:g.end,format:"compact"})}),n.jsx(ft,{children:g.description}),n.jsx(ht,{children:g.trades>0?`${g.winRate.toFixed(0)}% win rate (${g.trades} trades)`:"No data"})]},y))})]})]}),a.sessions.map(g=>{var x;const y=((x=a.currentStatus.activeSession)==null?void 0:x.sessionName)===g.sessionName,u=g.optimalWindows.some(S=>S.winRate>=70);return n.jsxs(dt,{isActive:y,isOptimal:u,children:[n.jsxs(pt,{children:[n.jsxs(mt,{isActive:y,children:[g.sessionName,y&&n.jsx(_e,{children:"ACTIVE"})]}),n.jsx(hi,{children:g.timeRange?n.jsx(ue,{mode:"session",sessionStart:g.timeRange.split("-")[0],sessionEnd:g.timeRange.split("-")[1],format:"compact"}):n.jsx("span",{children:"Time range not available"})})]}),n.jsxs(xi,{children:[n.jsxs(fe,{children:[n.jsx(he,{children:g.performance.totalTrades}),n.jsx(xe,{children:"Trades"})]}),n.jsxs(fe,{children:[n.jsxs(he,{children:[g.performance.winRate.toFixed(0),"%"]}),n.jsx(xe,{children:"Win Rate"})]}),n.jsxs(fe,{children:[n.jsxs(he,{children:[g.performance.avgRMultiple.toFixed(1),"R"]}),n.jsx(xe,{children:"Avg R-Multiple"})]}),n.jsxs(fe,{children:[n.jsx(he,{children:g.performance.avgRisk.toFixed(0)}),n.jsx(xe,{children:"Avg Risk (pts)"})]})]}),n.jsxs("div",{children:[n.jsx(yi,{children:"Optimal Time Windows"}),n.jsx(vi,{children:g.optimalWindows.map((S,j)=>n.jsxs(ut,{isOptimal:S.winRate>=70&&S.trades>=2,children:[n.jsx(gt,{children:n.jsx(ue,{mode:"session",sessionStart:S.start,sessionEnd:S.end,format:"compact"})}),n.jsx(ft,{children:S.description}),n.jsx(ht,{children:S.trades>0?`${S.winRate.toFixed(0)}% (${S.trades} trades)`:"No data"})]},j))})]}),g.recommendations.length>0&&n.jsxs(ot,{children:[n.jsx(at,{children:"Session Recommendations"}),n.jsx(ct,{children:g.recommendations.map((S,j)=>n.jsx(lt,{children:S},j))})]})]},g.sessionName)}),n.jsxs(wi,{children:[n.jsx(bi,{children:"📊 Weekly Performance Insights"}),n.jsxs(Si,{children:[n.jsxs(ye,{children:[n.jsx("strong",{children:"Best Session:"})," ",a.weeklyInsights.bestSession]}),n.jsxs(ye,{children:[n.jsx("strong",{children:"Best Model:"})," ",a.weeklyInsights.bestModel]}),n.jsxs(ye,{children:[n.jsx("strong",{children:"Average Quality:"})," ",a.weeklyInsights.avgQuality.toFixed(1),"/5.0"]}),n.jsxs(ye,{children:[n.jsx("strong",{children:"Quality Threshold:"})," ",`>${a.weeklyInsights.qualityThreshold.toFixed(1)}`," for optimal performance"]})]}),n.jsxs(ot,{style:{marginTop:"12px"},children:[n.jsx(at,{children:"Key Recommendations"}),n.jsx(ct,{children:a.weeklyInsights.recommendations.map((g,y)=>n.jsx(lt,{children:g},y))})]})]})]})})},le=d.div.withConfig({displayName:"EmptyState",componentId:"sc-8egh8t-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:300px;background:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"48px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"}),de=d.div.withConfig({displayName:"EmptyIcon",componentId:"sc-8egh8t-1"})(["font-size:48px;margin-bottom:",";opacity:0.7;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),pe=d.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-8egh8t-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),me=d.p.withConfig({displayName:"EmptyMessage",componentId:"sc-8egh8t-3"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Ii=d.div.withConfig({displayName:"NewsContainer",componentId:"sc-8egh8t-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),Ti=d.div.withConfig({displayName:"NewsCard",componentId:"sc-8egh8t-5"})(["background:",";border:1px solid ",";border-radius:",";padding:",";transition:all 0.2s ease;&:hover{border-color:","40;transform:translateY(-2px);box-shadow:0 8px 25px -5px rgba(0,0,0,0.1);}"],({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.surface)||"var(--bg-secondary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"}),ji=d.h3.withConfig({displayName:"NewsTitle",componentId:"sc-8egh8t-6"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Ri=d.span.withConfig({displayName:"NewsTime",componentId:"sc-8egh8t-7"})(["font-size:",";color:",";text-transform:uppercase;letter-spacing:0.025em;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.xs)||"0.75rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Di=d.p.withConfig({displayName:"NewsContent",componentId:"sc-8egh8t-8"})(["font-size:",";color:",";margin:"," 0 0 0;line-height:1.6;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Ei=({isLoading:e,error:t,handlers:i})=>t?n.jsxs(le,{children:[n.jsx(de,{children:"⚠️"}),n.jsx(pe,{children:"Error Loading Session Analytics"}),n.jsx(me,{children:t})]}):n.jsx(Ci,{isLoading:e,error:t,onRefresh:i.onRefresh}),Ni=({isLoading:e,error:t,handlers:i})=>t?n.jsxs(le,{children:[n.jsx(de,{children:"⚠️"}),n.jsx(pe,{children:"Error Loading Elite ICT Intelligence"}),n.jsx(me,{children:t})]}):n.jsx($r,{isLoading:e,error:t,onRefresh:i.onRefresh}),ki=({isLoading:e,error:t,handlers:i})=>t?n.jsxs(le,{children:[n.jsx(de,{children:"⚠️"}),n.jsx(pe,{children:"Error Loading PD Array Intelligence"}),n.jsx(me,{children:t})]}):n.jsx(si,{isLoading:e,error:t,onRefresh:i.onRefresh}),Mi=({isLoading:e})=>{if(e)return n.jsxs(le,{children:[n.jsx(de,{children:"📰"}),n.jsx(pe,{children:"Loading Market News"}),n.jsx(me,{children:"Fetching the latest market updates..."})]});const t=[{id:1,title:"Federal Reserve Maintains Interest Rates",time:"2 hours ago",content:"The Federal Reserve announced it will maintain current interest rates, citing ongoing economic stability and controlled inflation metrics."},{id:2,title:"Tech Sector Shows Strong Pre-Market Activity",time:"4 hours ago",content:"Major technology stocks are showing positive momentum in pre-market trading, with several companies reporting better-than-expected earnings."},{id:3,title:"Oil Prices Stabilize After Recent Volatility",time:"6 hours ago",content:"Crude oil prices have stabilized following recent geopolitical tensions, with WTI trading within expected ranges."}];return n.jsx(Ii,{children:t.map(i=>n.jsxs(Ti,{children:[n.jsx(ji,{children:i.title}),n.jsx(Ri,{children:i.time}),n.jsx(Di,{children:i.content})]},i.id))})},Ai={overview:{id:"overview",title:"Session Focus",description:"Personalized session analytics and trading recommendations",icon:"🎯",component:Ei,showInMobile:!0,requiresData:!0},plan:{id:"plan",title:"Elite Intelligence",description:"Advanced ICT trading intelligence with model selection, pattern scoring, and session analysis",icon:"🧠",component:Ni,showInMobile:!0,requiresData:!1},levels:{id:"levels",title:"PD Array Levels",description:"ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis",icon:"🎯",component:ki,showInMobile:!0,requiresData:!1},news:{id:"news",title:"Market News",description:"Latest market news and economic events",icon:"📰",component:Mi,showInMobile:!1,requiresData:!1}},Pi=e=>Ai[e],Li=e=>{const{activeTab:t}=e,i=Pi(t);if(!i)return n.jsxs(le,{children:[n.jsx(de,{children:"❌"}),n.jsx(pe,{children:"Unknown Tab"}),n.jsxs(me,{children:['Tab "',t,'" not found.']})]});const r=i.component;return n.jsx("div",{id:`guide-panel-${t}`,role:"tabpanel","aria-labelledby":`guide-tab-${t}`,children:n.jsx(r,{...e})})},Fi=d.div.withConfig({displayName:"Container",componentId:"sc-1fqvgtv-0"})(["display:flex;flex-direction:column;gap:",";background:",";color:",";min-height:100vh;padding:",";max-width:1400px;margin:0 auto;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.background)||"var(--bg-primary)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),zi=d.div.withConfig({displayName:"ContentArea",componentId:"sc-1fqvgtv-1"})(["display:flex;flex-direction:column;gap:",";flex:1;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),Wi=d.div.withConfig({displayName:"TabContentContainer",componentId:"sc-1fqvgtv-2"})(["animation:fadeIn 0.3s ease-in-out;@keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}"]),_i=d.div.withConfig({displayName:"LoadingState",componentId:"sc-1fqvgtv-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"48px"}),Oi=d.div.withConfig({displayName:"LoadingIcon",componentId:"sc-1fqvgtv-4"})(["font-size:48px;margin-bottom:",";opacity:0.7;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:0.7;}50%{opacity:0.3;}}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"}),$i=d.p.withConfig({displayName:"LoadingText",componentId:"sc-1fqvgtv-5"})(["font-size:",";color:",";margin:0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Gi=d.div.withConfig({displayName:"ErrorState",componentId:"sc-1fqvgtv-6"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;background:","10;border:1px solid ","40;border-radius:",";margin:"," 0;"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.xl)||"48px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.lg)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.lg)||"24px"}),Hi=d.div.withConfig({displayName:"ErrorIcon",componentId:"sc-1fqvgtv-7"})(["font-size:48px;margin-bottom:",";color:",";"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"}),qi=d.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-1fqvgtv-8"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.lg)||"1.125rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.error)||"var(--error-color)"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"}),Vi=d.p.withConfig({displayName:"ErrorMessage",componentId:"sc-1fqvgtv-9"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:e})=>{var t;return((t=e.fontSizes)==null?void 0:t.sm)||"0.875rem"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.textSecondary)||"var(--text-secondary)"}),Ui=d.button.withConfig({displayName:"RetryButton",componentId:"sc-1fqvgtv-10"})(["margin-top:",";padding:"," ",";background:",";color:white;border:none;border-radius:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;&:hover{background:",";transform:translateY(-1px);}"],({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.sm)||"8px"},({theme:e})=>{var t;return((t=e.spacing)==null?void 0:t.md)||"12px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primary)||"var(--primary-color)"},({theme:e})=>{var t;return((t=e.borderRadius)==null?void 0:t.md)||"6px"},({theme:e})=>{var t;return((t=e.colors)==null?void 0:t.primaryDark)||"var(--primary-dark)"}),Rt=()=>n.jsxs(_i,{children:[n.jsx(Oi,{children:"📅"}),n.jsx($i,{children:"Loading Daily Guide..."})]}),Bi=({error:e,onRetry:t})=>n.jsxs(Gi,{children:[n.jsx(Hi,{children:"⚠️"}),n.jsx(qi,{children:"Guide Error"}),n.jsx(Vi,{children:e}),n.jsx(Ui,{onClick:t,children:"Try Again"})]}),Yi=({initialTab:e,title:t})=>{const{selectedDate:i,marketOverview:r,tradingPlan:s,keyPriceLevels:o,isLoading:a,error:m,currentDate:l,onDateChange:p,onTradingPlanItemToggle:c,onAddTradingPlanItem:v,onRemoveTradingPlanItem:b,onRefresh:g}=pn(),{activeTab:y,setActiveTab:u}=Fn({defaultTab:e||"overview"}),x={activeTab:y,data:{marketOverview:r,tradingPlan:s,keyPriceLevels:o,selectedDate:i,currentDate:l},isLoading:a,error:m,handlers:{onDateChange:p,onTradingPlanItemToggle:c,onAddTradingPlanItem:v,onRemoveTradingPlanItem:b,onRefresh:g}};return m?n.jsx(Bi,{error:m,onRetry:g}):n.jsxs(Fi,{children:[n.jsx(Rn,{isLoading:a,currentDate:l,selectedDate:i,onDateChange:p,onRefresh:g,title:t}),n.jsx(Mn,{activeTab:y,onTabChange:u,disabled:a}),n.jsx(zi,{children:n.jsx(Wi,{children:n.jsx(T.Suspense,{fallback:n.jsx(Rt,{}),children:n.jsx(Li,{...x})})})})]})},Ki=e=>n.jsx(T.Suspense,{fallback:n.jsx(Rt,{}),children:n.jsx(Yi,{...e})}),is=({className:e,initialTab:t,title:i})=>n.jsx(Gt,{children:n.jsx(Ki,{className:e,initialTab:t,title:i})});export{is as DailyGuide,is as default};
//# sourceMappingURL=DailyGuide-ac65bbb9.js.map
