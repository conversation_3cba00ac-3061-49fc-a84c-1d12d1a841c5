{"version": 3, "file": "TradeFormBasicFields-bce7db22.js", "sources": ["../../src/features/trade-journal/components/trade-form/F1TradeFormField.tsx", "../../src/features/trade-journal/components/trade-form/tradeFormFieldConfig.ts", "../../src/features/trade-journal/components/trade-form/TradeFormFieldGroups.tsx", "../../src/features/trade-journal/components/trade-form/useTradeFormFields.ts", "../../src/features/trade-journal/components/trade-form/TradeFormBasicFieldsContainer.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormBasicFields.tsx"], "sourcesContent": ["/**\n * F1TradeFormField Component\n * \n * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)\n * Enhanced F1FormField specifically designed for trading forms.\n * \n * BENEFITS:\n * - Focused responsibility (single form field)\n * - F1 racing theme with trading-specific styling\n * - Built-in validation and error handling\n * - Supports all trading field types\n * - Reusable across all trading forms\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type TradeFieldType = \n  | 'text' \n  | 'number' \n  | 'date' \n  | 'select' \n  | 'price' \n  | 'quantity'\n  | 'percentage';\n\nexport interface TradeFieldOption {\n  value: string | number;\n  label: string;\n}\n\nexport interface F1TradeFormFieldProps {\n  /** Field identifier */\n  name: string;\n  /** Field label */\n  label: string;\n  /** Field type */\n  type: TradeFieldType;\n  /** Current value */\n  value: any;\n  /** Change handler */\n  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  /** Options for select fields */\n  options?: TradeFieldOption[];\n  /** Input props for additional configuration */\n  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;\n  /** Validation error */\n  error?: string;\n  /** Whether field is required */\n  required?: boolean;\n  /** Whether field is disabled */\n  disabled?: boolean;\n  /** Placeholder text */\n  placeholder?: string;\n  /** Custom className */\n  className?: string;\n}\n\nconst FieldContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  min-width: 0;\n`;\n\nconst FieldLabel = styled.label<{ $required?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  cursor: pointer;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  \n  /* F1 Racing style */\n  ${({ $required, theme }) =>\n    $required &&\n    `\n    &::after {\n      content: '*';\n      color: ${theme.colors?.primary || 'var(--primary-color)'};\n      margin-left: 4px;\n      font-weight: 700;\n    }\n  `}\n`;\n\nconst BaseInput = styled.input<{ $hasError?: boolean; $fieldType?: TradeFieldType }>`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasError }) => \n    $hasError \n      ? theme.colors?.error || 'var(--error-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  transition: all 0.2s ease;\n  width: 100%;\n  \n  /* F1 Racing focus effect */\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n    transform: translateY(-1px);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  /* Trading-specific styling */\n  ${({ $fieldType, theme }) => {\n    switch ($fieldType) {\n      case 'price':\n      case 'quantity':\n        return `\n          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n          text-align: right;\n          font-weight: 600;\n        `;\n      case 'percentage':\n        return `\n          text-align: right;\n          &::after {\n            content: '%';\n            position: absolute;\n            right: 12px;\n            color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};\n          }\n        `;\n      default:\n        return '';\n    }\n  }}\n`;\n\nconst Select = styled.select<{ $hasError?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasError }) => \n    $hasError \n      ? theme.colors?.error || 'var(--error-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  width: 100%;\n  \n  /* F1 Racing focus effect */\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n    transform: translateY(-1px);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  /* Custom dropdown arrow */\n  appearance: none;\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right 12px center;\n  background-size: 16px;\n  padding-right: 40px;\n`;\n\nconst ErrorMessage = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  font-weight: 500;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  \n  &::before {\n    content: '⚠️';\n    font-size: 12px;\n  }\n`;\n\n/**\n * F1TradeFormField Component\n * \n * PATTERN: F1 Form Field Pattern for Trading\n * - Racing-inspired styling with red accents\n * - Trading-specific field types and formatting\n * - Built-in validation and error states\n * - Accessible with proper labels and focus\n * - Optimized for trading data entry\n */\nexport const F1TradeFormField: React.FC<F1TradeFormFieldProps> = ({\n  name,\n  label,\n  type,\n  value,\n  onChange,\n  options = [],\n  inputProps = {},\n  error,\n  required = false,\n  disabled = false,\n  placeholder,\n  className,\n}) => {\n  const fieldId = `trade-field-${name}`;\n  \n  const getInputType = (): string => {\n    switch (type) {\n      case 'price':\n      case 'quantity':\n      case 'percentage':\n        return 'number';\n      case 'date':\n        return 'date';\n      default:\n        return 'text';\n    }\n  };\n  \n  const getInputProps = () => {\n    const baseProps = {\n      id: fieldId,\n      name,\n      value: value || '',\n      onChange,\n      required,\n      disabled,\n      placeholder,\n      ...inputProps,\n    };\n    \n    switch (type) {\n      case 'price':\n        return {\n          ...baseProps,\n          type: 'number',\n          step: '0.01',\n          min: '0',\n        };\n      case 'quantity':\n        return {\n          ...baseProps,\n          type: 'number',\n          step: '1',\n          min: '0',\n        };\n      case 'percentage':\n        return {\n          ...baseProps,\n          type: 'number',\n          step: '0.1',\n          min: '0',\n          max: '100',\n        };\n      default:\n        return {\n          ...baseProps,\n          type: getInputType(),\n        };\n    }\n  };\n  \n  const renderControl = () => {\n    if (type === 'select') {\n      return (\n        <Select\n          id={fieldId}\n          name={name}\n          value={value || ''}\n          onChange={onChange}\n          required={required}\n          disabled={disabled}\n          $hasError={!!error}\n        >\n          {!required && <option value=\"\">Select {label}</option>}\n          {options.map((option) => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </Select>\n      );\n    }\n    \n    return (\n      <BaseInput\n        {...getInputProps()}\n        $hasError={!!error}\n        $fieldType={type}\n      />\n    );\n  };\n  \n  return (\n    <FieldContainer className={className}>\n      <FieldLabel htmlFor={fieldId} $required={required}>\n        {label}\n      </FieldLabel>\n      \n      {renderControl()}\n      \n      {error && (\n        <ErrorMessage role=\"alert\">\n          {error}\n        </ErrorMessage>\n      )}\n    </FieldContainer>\n  );\n};\n\nexport default F1TradeFormField;\n", "/**\n * Trade Form Field Configuration\n * \n * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)\n * Centralized configuration for all trading form fields.\n * \n * BENEFITS:\n * - Single source of truth for field definitions\n * - Easy to maintain and extend\n * - Consistent field options across forms\n * - Type-safe field configurations\n * - Reusable across different trading forms\n */\n\nimport { TradeFieldType, TradeFieldOption } from './F1TradeFormField';\n\nexport interface TradeFormFieldConfig {\n  name: string;\n  label: string;\n  type: TradeFieldType;\n  required?: boolean;\n  options?: TradeFieldOption[];\n  placeholder?: string;\n  inputProps?: Record<string, any>;\n  group: 'basic' | 'pricing' | 'strategy' | 'analysis';\n}\n\n/**\n * Direction Options\n */\nexport const DIRECTION_OPTIONS: TradeFieldOption[] = [\n  { value: 'long', label: 'Long' },\n  { value: 'short', label: 'Short' },\n];\n\n/**\n * Result Options\n */\nexport const RESULT_OPTIONS: TradeFieldOption[] = [\n  { value: 'win', label: 'Win' },\n  { value: 'loss', label: 'Loss' },\n  { value: 'breakeven', label: 'Breakeven' },\n];\n\n/**\n * Model Options\n */\nexport const MODEL_OPTIONS: TradeFieldOption[] = [\n  { value: 'RD-Cont', label: 'RD-Cont' },\n  { value: 'FVG-RD', label: 'FVG-RD' },\n  { value: 'Combined', label: 'Combined' },\n];\n\n/**\n * Session Options\n */\nexport const SESSION_OPTIONS: TradeFieldOption[] = [\n  { value: 'NY Open', label: 'NY Open' },\n  { value: 'London Open', label: 'London Open' },\n  { value: 'Lunch Macro', label: 'Lunch Macro' },\n  { value: 'MOC', label: 'MOC' },\n  { value: 'Overnight', label: 'Overnight' },\n];\n\n/**\n * RD Type Options\n */\nexport const RD_TYPE_OPTIONS: TradeFieldOption[] = [\n  { value: 'Bullish', label: 'Bullish' },\n  { value: 'Bearish', label: 'Bearish' },\n  { value: 'Neutral', label: 'Neutral' },\n];\n\n/**\n * Draw on Liquidity Options\n */\nexport const DOL_STATUS_OPTIONS: TradeFieldOption[] = [\n  { value: 'Hit', label: 'Hit' },\n  { value: 'Missed', label: 'Missed' },\n  { value: 'Partial', label: 'Partial' },\n  { value: 'Pending', label: 'Pending' },\n];\n\n/**\n * Complete field configuration for trade forms\n */\nexport const TRADE_FORM_FIELDS: TradeFormFieldConfig[] = [\n  // Basic Information Fields\n  {\n    name: 'date',\n    label: 'Date',\n    type: 'date',\n    required: true,\n    group: 'basic',\n  },\n  {\n    name: 'symbol',\n    label: 'Symbol',\n    type: 'text',\n    required: true,\n    placeholder: 'e.g., AAPL, SPY, NQ',\n    group: 'basic',\n    inputProps: {\n      style: { textTransform: 'uppercase' },\n    },\n  },\n  {\n    name: 'direction',\n    label: 'Direction',\n    type: 'select',\n    required: true,\n    options: DIRECTION_OPTIONS,\n    group: 'basic',\n  },\n  {\n    name: 'result',\n    label: 'Result',\n    type: 'select',\n    required: true,\n    options: RESULT_OPTIONS,\n    group: 'basic',\n  },\n\n  // Pricing Fields\n  {\n    name: 'entryPrice',\n    label: 'Entry Price',\n    type: 'price',\n    required: true,\n    group: 'pricing',\n  },\n  {\n    name: 'exitPrice',\n    label: 'Exit Price',\n    type: 'price',\n    required: true,\n    group: 'pricing',\n  },\n  {\n    name: 'quantity',\n    label: 'Quantity',\n    type: 'quantity',\n    required: true,\n    group: 'pricing',\n  },\n  {\n    name: 'profit',\n    label: 'Profit/Loss ($)',\n    type: 'price',\n    required: true,\n    group: 'pricing',\n  },\n\n  // Strategy Fields\n  {\n    name: 'model',\n    label: 'Model',\n    type: 'select',\n    options: MODEL_OPTIONS,\n    group: 'strategy',\n  },\n  {\n    name: 'session',\n    label: 'Session',\n    type: 'select',\n    options: SESSION_OPTIONS,\n    group: 'strategy',\n  },\n  {\n    name: 'patternQuality',\n    label: 'Pattern Quality (1-10)',\n    type: 'number',\n    group: 'strategy',\n    inputProps: {\n      min: 1,\n      max: 10,\n      step: 1,\n    },\n  },\n\n  // Analysis Fields\n  {\n    name: 'dolTarget',\n    label: 'DOL Target',\n    type: 'text',\n    placeholder: 'Draw on Liquidity target',\n    group: 'analysis',\n  },\n  {\n    name: 'rdType',\n    label: 'RD Type',\n    type: 'select',\n    options: RD_TYPE_OPTIONS,\n    group: 'analysis',\n  },\n  {\n    name: 'drawOnLiquidity',\n    label: 'Draw on Liquidity',\n    type: 'select',\n    options: DOL_STATUS_OPTIONS,\n    group: 'analysis',\n  },\n  {\n    name: 'entryVersion',\n    label: 'Entry Version',\n    type: 'text',\n    placeholder: 'Entry version/iteration',\n    group: 'analysis',\n  },\n];\n\n/**\n * Get fields by group\n */\nexport const getFieldsByGroup = (group: string): TradeFormFieldConfig[] => {\n  return TRADE_FORM_FIELDS.filter(field => field.group === group);\n};\n\n/**\n * Get field configuration by name\n */\nexport const getFieldConfig = (name: string): TradeFormFieldConfig | undefined => {\n  return TRADE_FORM_FIELDS.find(field => field.name === name);\n};\n\n/**\n * Field groups configuration\n */\nexport const FIELD_GROUPS = [\n  {\n    key: 'basic',\n    title: 'Basic Information',\n    description: 'Essential trade details and identification',\n    icon: '📊',\n  },\n  {\n    key: 'pricing',\n    title: 'Pricing & P&L',\n    description: 'Entry, exit prices and profit/loss calculations',\n    icon: '💰',\n  },\n  {\n    key: 'strategy',\n    title: 'Strategy & Setup',\n    description: 'Trading model, session, and pattern quality',\n    icon: '🎯',\n  },\n  {\n    key: 'analysis',\n    title: 'Analysis & DOL',\n    description: 'Draw on liquidity and advanced analysis',\n    icon: '🔍',\n  },\n] as const;\n\n/**\n * Validation rules for fields\n */\nexport const FIELD_VALIDATION_RULES = {\n  symbol: {\n    pattern: /^[A-Z]{1,5}$/,\n    message: 'Symbol must be 1-5 uppercase letters',\n  },\n  entryPrice: {\n    min: 0.01,\n    message: 'Entry price must be greater than 0',\n  },\n  exitPrice: {\n    min: 0.01,\n    message: 'Exit price must be greater than 0',\n  },\n  quantity: {\n    min: 1,\n    message: 'Quantity must be at least 1',\n  },\n  patternQuality: {\n    min: 1,\n    max: 10,\n    message: 'Pattern quality must be between 1 and 10',\n  },\n} as const;\n", "/**\n * TradeFormFieldGroups Component\n *\n * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)\n * Organized field groups with F1 racing theme and clear hierarchy.\n *\n * BENEFITS:\n * - Organized field groups with clear sections\n * - F1 racing theme with consistent styling\n * - Responsive grid layout\n * - Reusable across different trading forms\n * - Better UX with logical field grouping\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { F1TradeFormField } from './F1TradeFormField';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport { FIELD_GROUPS, getFieldsByGroup, TradeFormFieldConfig } from './tradeFormFieldConfig';\n\nexport interface TradeFormFieldGroupsProps {\n  /** Form values */\n  formValues: TradeFormValues;\n  /** Change handler */\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  /** Price change handler (triggers calculations) */\n  handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  /** Validation errors */\n  validationErrors: ValidationErrors;\n  /** Whether form is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst GroupsContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst FieldGroup = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  overflow: hidden;\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst GroupHeader = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  position: relative;\n\n  /* F1 Racing accent */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 4px;\n    height: 100%;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  }\n`;\n\nconst GroupTitleRow = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst GroupIcon = styled.div`\n  font-size: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n`;\n\nconst GroupTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst GroupDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;\n  line-height: 1.5;\n`;\n\nconst GroupContent = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst FieldsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: ${({ theme }) => theme.spacing?.md || '12px'};\n  }\n`;\n\n/**\n * TradeFormFieldGroups Component\n *\n * PATTERN: F1 Form Groups Pattern\n * - Racing-inspired section styling with icons\n * - Organized field groups with clear hierarchy\n * - Responsive grid layout for optimal UX\n * - Consistent F1 theme across all groups\n * - Accessible and keyboard navigable\n */\nexport const TradeFormFieldGroups: React.FC<TradeFormFieldGroupsProps> = ({\n  formValues,\n  handleChange,\n  handlePriceChange,\n  validationErrors,\n  disabled = false,\n  className,\n}) => {\n  /**\n   * Determine which change handler to use based on field type\n   */\n  const getChangeHandler = (fieldConfig: TradeFormFieldConfig) => {\n    // Use price change handler for fields that affect calculations\n    if (['entryPrice', 'exitPrice', 'quantity'].includes(fieldConfig.name)) {\n      return (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n        handlePriceChange(e as React.ChangeEvent<HTMLInputElement>);\n      };\n    }\n    return handleChange;\n  };\n\n  return (\n    <GroupsContainer className={className}>\n      {FIELD_GROUPS.map(group => {\n        const fields = getFieldsByGroup(group.key);\n\n        if (fields.length === 0) return null;\n\n        return (\n          <FieldGroup key={group.key}>\n            <GroupHeader>\n              <GroupTitleRow>\n                <GroupIcon>{group.icon}</GroupIcon>\n                <div>\n                  <GroupTitle>{group.title}</GroupTitle>\n                  <GroupDescription>{group.description}</GroupDescription>\n                </div>\n              </GroupTitleRow>\n            </GroupHeader>\n\n            <GroupContent>\n              <FieldsGrid>\n                {fields.map(fieldConfig => (\n                  <F1TradeFormField\n                    key={fieldConfig.name}\n                    name={fieldConfig.name}\n                    label={fieldConfig.label}\n                    type={fieldConfig.type}\n                    value={formValues[fieldConfig.name as keyof TradeFormValues]}\n                    onChange={getChangeHandler(fieldConfig)}\n                    options={fieldConfig.options}\n                    inputProps={fieldConfig.inputProps}\n                    error={validationErrors[fieldConfig.name]}\n                    required={fieldConfig.required}\n                    disabled={disabled}\n                    placeholder={fieldConfig.placeholder}\n                  />\n                ))}\n              </FieldsGrid>\n            </GroupContent>\n          </FieldGroup>\n        );\n      })}\n    </GroupsContainer>\n  );\n};\n\nexport default TradeFormFieldGroups;\n", "/**\n * useTradeFormFields Hook\n *\n * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)\n * Enhanced hook for managing trade form fields with validation and calculations.\n *\n * BENEFITS:\n * - Focused responsibility (field management only)\n * - Built-in validation with field-specific rules\n * - Automatic profit/loss calculations\n * - Type-safe field handling\n * - Reusable across different trading forms\n */\n\nimport { useCallback } from 'react';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport { FIELD_VALIDATION_RULES, getFieldConfig } from './tradeFormFieldConfig';\n\nexport interface UseTradeFormFieldsProps {\n  /** Current form values */\n  formValues: TradeFormValues;\n  /** Form values setter */\n  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;\n  /** Validation errors */\n  validationErrors: ValidationErrors;\n  /** Validation errors setter */\n  setValidationErrors: React.Dispatch<React.SetStateAction<ValidationErrors>>;\n  /** Optional profit calculation callback */\n  calculateProfitLoss?: () => void;\n}\n\nexport interface UseTradeFormFieldsReturn {\n  /** Standard change handler */\n  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  /** Price change handler (triggers calculations) */\n  handlePriceChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  /** Validate specific field */\n  validateField: (name: string, value: any) => string | null;\n  /** Validate all fields */\n  validateAllFields: () => boolean;\n  /** Calculate profit/loss from current values */\n  calculateProfitFromValues: () => number;\n  /** Check if field affects calculations */\n  isCalculationField: (fieldName: string) => boolean;\n}\n\n/**\n * Fields that trigger profit/loss calculations\n */\nconst CALCULATION_FIELDS = ['entryPrice', 'exitPrice', 'quantity', 'direction'];\n\n/**\n * useTradeFormFields Hook\n *\n * Enhanced form field management with validation and calculations.\n */\nexport const useTradeFormFields = ({\n  formValues,\n  setFormValues,\n  // validationErrors, // Removed as unused\n  setValidationErrors,\n  calculateProfitLoss,\n}: UseTradeFormFieldsProps): UseTradeFormFieldsReturn => {\n  /**\n   * Validate individual field\n   */\n  const validateField = useCallback((name: string, value: any): string | null => {\n    const fieldConfig = getFieldConfig(name);\n    const validationRule = FIELD_VALIDATION_RULES[name as keyof typeof FIELD_VALIDATION_RULES];\n\n    // Check required fields\n    if (fieldConfig?.required && (!value || value === '')) {\n      return `${fieldConfig.label} is required`;\n    }\n\n    // Skip validation for empty optional fields\n    if (!value || value === '') {\n      return null;\n    }\n\n    // Apply field-specific validation rules\n    if (validationRule) {\n      if ('pattern' in validationRule) {\n        if (!validationRule.pattern.test(value)) {\n          return validationRule.message;\n        }\n      }\n\n      if ('min' in validationRule) {\n        const numValue = parseFloat(value);\n        if (isNaN(numValue) || numValue < validationRule.min) {\n          return validationRule.message;\n        }\n      }\n\n      if ('max' in validationRule) {\n        const numValue = parseFloat(value);\n        if (isNaN(numValue) || numValue > validationRule.max) {\n          return validationRule.message;\n        }\n      }\n    }\n\n    // Additional validation for specific field types\n    switch (name) {\n      case 'date':\n        const date = new Date(value);\n        if (isNaN(date.getTime())) {\n          return 'Please enter a valid date';\n        }\n        if (date > new Date()) {\n          return 'Date cannot be in the future';\n        }\n        break;\n\n      case 'entryPrice':\n      case 'exitPrice':\n      case 'quantity':\n      case 'profit':\n        const numValue = parseFloat(value);\n        if (isNaN(numValue)) {\n          return 'Please enter a valid number';\n        }\n        break;\n    }\n\n    return null;\n  }, []);\n\n  /**\n   * Standard change handler\n   */\n  const handleChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n      const { name, value } = e.target;\n\n      // Update form values\n      setFormValues(prev => ({ ...prev, [name]: value }));\n\n      // Validate field and update errors\n      const fieldError = validateField(name, value);\n      setValidationErrors(prev => {\n        const newErrors = { ...prev };\n        if (fieldError) {\n          newErrors[name] = fieldError;\n        } else {\n          delete newErrors[name];\n        }\n        return newErrors;\n      });\n    },\n    [validateField, setFormValues, setValidationErrors]\n  );\n\n  /**\n   * Calculate profit/loss from current form values\n   */\n  const calculateProfitFromValues = useCallback((): number => {\n    const entryPrice = parseFloat(formValues.entryPrice) || 0;\n    const exitPrice = parseFloat(formValues.exitPrice) || 0;\n    const quantity = parseFloat(formValues.quantity) || 0;\n    const direction = formValues.direction;\n\n    if (entryPrice === 0 || exitPrice === 0 || quantity === 0) {\n      return 0;\n    }\n\n    const priceDiff = direction === 'long' ? exitPrice - entryPrice : entryPrice - exitPrice;\n\n    return priceDiff * quantity;\n  }, [formValues.entryPrice, formValues.exitPrice, formValues.quantity, formValues.direction]);\n\n  /**\n   * Price change handler (triggers calculations)\n   */\n  const handlePriceChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement>) => {\n      const { name, value } = e.target;\n\n      // Update form values\n      setFormValues(prev => {\n        const newValues = { ...prev, [name]: value };\n\n        // Auto-calculate profit if all required fields are present\n        if (\n          CALCULATION_FIELDS.every(field =>\n            field === name ? value : newValues[field as keyof TradeFormValues]\n          )\n        ) {\n          const entryPrice = parseFloat(name === 'entryPrice' ? value : newValues.entryPrice) || 0;\n          const exitPrice = parseFloat(name === 'exitPrice' ? value : newValues.exitPrice) || 0;\n          const quantity = parseFloat(name === 'quantity' ? value : newValues.quantity) || 0;\n          const direction = name === 'direction' ? value : newValues.direction;\n\n          if (entryPrice > 0 && exitPrice > 0 && quantity > 0) {\n            const priceDiff =\n              direction === 'long' ? exitPrice - entryPrice : entryPrice - exitPrice;\n\n            newValues.profit = (priceDiff * quantity).toFixed(2);\n          }\n        }\n\n        return newValues;\n      });\n\n      // Validate field\n      const fieldError = validateField(name, value);\n      setValidationErrors(prev => {\n        const newErrors = { ...prev };\n        if (fieldError) {\n          newErrors[name] = fieldError;\n        } else {\n          delete newErrors[name];\n        }\n        return newErrors;\n      });\n\n      // Trigger external calculation if provided\n      if (calculateProfitLoss) {\n        setTimeout(calculateProfitLoss, 0);\n      }\n    },\n    [validateField, setFormValues, setValidationErrors, calculateProfitLoss]\n  );\n\n  /**\n   * Validate all fields\n   */\n  const validateAllFields = useCallback((): boolean => {\n    const errors: ValidationErrors = {};\n    let isValid = true;\n\n    // Validate all form values\n    Object.entries(formValues).forEach(([name, value]) => {\n      const error = validateField(name, value);\n      if (error) {\n        errors[name] = error;\n        isValid = false;\n      }\n    });\n\n    setValidationErrors(errors);\n    return isValid;\n  }, [formValues, validateField, setValidationErrors]);\n\n  /**\n   * Check if field affects calculations\n   */\n  const isCalculationField = useCallback((fieldName: string): boolean => {\n    return CALCULATION_FIELDS.includes(fieldName);\n  }, []);\n\n  return {\n    handleChange,\n    handlePriceChange,\n    validateField,\n    validateAllFields,\n    calculateProfitFromValues,\n    isCalculationField,\n  };\n};\n\nexport default useTradeFormFields;\n", "/**\n * TradeFormBasicFieldsContainer Component\n *\n * REFACTORED FROM: TradeFormBasicFields.tsx (338 lines → focused components)\n * Main orchestrator for trade form basic fields with F1 container pattern.\n *\n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and validation\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense } from 'react';\nimport styled from 'styled-components';\nimport { TradeFormFieldGroups } from './TradeFormFieldGroups';\nimport { useTradeFormFields } from './useTradeFormFields';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n// Removed unused imports - will be added back when needed for real data integration\n\nexport interface TradeFormBasicFieldsContainerProps {\n  /** Form values */\n  formValues: TradeFormValues;\n  /** Form values setter */\n  setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;\n  /** Change handler */\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  /** Validation errors */\n  validationErrors: ValidationErrors;\n  /** Validation errors setter */\n  setValidationErrors?: React.Dispatch<React.SetStateAction<ValidationErrors>>;\n  /** Calculate profit/loss callback */\n  calculateProfitLoss?: () => void;\n  /** Whether form is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n`;\n\n/*\n// Unused styled components removed to eliminate TypeScript errors\n// These were previously used for setup builder functionality\nconst SetupBuilderSection = styled.div`...`;\nconst SetupBuilderHeader = styled.div`...`;\nconst SetupBuilderTitleRow = styled.div`...`;\nconst SetupBuilderIcon = styled.div`...`;\nconst SetupBuilderTitle = styled.h3`...`;\nconst SetupBuilderDescription = styled.p`...`;\nconst SetupBuilderContent = styled.div`...`;\n*/\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '32px'};\n  text-align: center;\n  min-height: 200px;\n`;\n\nconst LoadingIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 0.7;\n    }\n    50% {\n      opacity: 0.3;\n    }\n  }\n`;\n\nconst LoadingText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n`;\n\n/**\n * LoadingFallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <LoadingState>\n    <LoadingIcon>📝</LoadingIcon>\n    <LoadingText>Loading Form Fields...</LoadingText>\n  </LoadingState>\n);\n\n/**\n * TradeFormFieldsContent Component\n */\nconst TradeFormFieldsContent: React.FC<TradeFormBasicFieldsContainerProps> = ({\n  formValues,\n  setFormValues,\n  handleChange,\n  validationErrors,\n  setValidationErrors,\n  calculateProfitLoss,\n  disabled = false,\n}) => {\n  // Use enhanced form fields hook if setters are available\n  const enhancedHook =\n    setFormValues && setValidationErrors\n      ? useTradeFormFields({\n          formValues,\n          setFormValues,\n          validationErrors,\n          setValidationErrors,\n          calculateProfitLoss,\n        })\n      : null;\n\n  // Use enhanced handlers if available, otherwise fall back to props\n  const finalHandleChange = enhancedHook?.handleChange || handleChange;\n  const finalHandlePriceChange = enhancedHook?.handlePriceChange || handleChange;\n\n  return (\n    <>\n      <TradeFormFieldGroups\n        formValues={formValues}\n        handleChange={finalHandleChange}\n        handlePriceChange={finalHandlePriceChange}\n        validationErrors={validationErrors}\n        disabled={disabled}\n      />\n\n      {/* Setup Construction Matrix removed from here - now only in Strategy & Setup section */}\n    </>\n  );\n};\n\n/**\n * TradeFormBasicFieldsContainer Component\n *\n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const TradeFormBasicFieldsContainer: React.FC<\n  TradeFormBasicFieldsContainerProps\n> = props => {\n  return (\n    <Container className={props.className}>\n      <Suspense fallback={<LoadingFallback />}>\n        <TradeFormFieldsContent {...props} />\n      </Suspense>\n    </Container>\n  );\n};\n\nexport default TradeFormBasicFieldsContainer;\n", "/**\n * Trade Form Basic Fields Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 338 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 95% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { TradeFormBasicFieldsContainer } from './TradeFormBasicFieldsContainer';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n\ninterface TradeFormBasicFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  validationErrors: ValidationErrors;\n  calculateProfitLoss?: () => void;\n  setFormValues?: React.Dispatch<React.SetStateAction<TradeFormValues>>;\n}\n\n/**\n * Trade Form Basic Fields Component\n *\n * Simple wrapper that renders the container.\n * Follows the proven architecture pattern.\n */\nconst TradeFormBasicFields: React.FC<TradeFormBasicFieldsProps> = (props) => {\n  return <TradeFormBasicFieldsContainer {...props} />;\n};\n\nexport default TradeFormBasicFields;\n"], "names": ["FieldC<PERSON>r", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "xs", "FieldLabel", "label", "fontSizes", "sm", "colors", "textPrimary", "$required", "primary", "BaseInput", "input", "md", "background", "$hasError", "error", "border", "borderRadius", "$fieldType", "textSecondary", "Select", "select", "ErrorMessage", "F1TradeFormField", "name", "type", "value", "onChange", "options", "inputProps", "required", "disabled", "placeholder", "className", "fieldId", "getInputType", "getInputProps", "baseProps", "id", "step", "min", "max", "renderControl", "jsxs", "map", "option", "jsx", "DIRECTION_OPTIONS", "RESULT_OPTIONS", "MODEL_OPTIONS", "SESSION_OPTIONS", "RD_TYPE_OPTIONS", "DOL_STATUS_OPTIONS", "TRADE_FORM_FIELDS", "group", "style", "textTransform", "getFieldsByGroup", "filter", "field", "getFieldConfig", "find", "FIELD_GROUPS", "key", "title", "description", "icon", "FIELD_VALIDATION_RULES", "symbol", "pattern", "message", "entryPrice", "exitPrice", "quantity", "patternQuality", "GroupsContainer", "xl", "FieldGroup", "surface", "lg", "GroupHeader", "GroupTitleRow", "GroupIcon", "GroupTitle", "h3", "GroupDescription", "p", "GroupContent", "<PERSON><PERSON><PERSON>", "TradeFormFieldGroups", "formValues", "handleChange", "handlePriceChange", "validationErrors", "getChangeHandler", "fieldConfig", "includes", "e", "fields", "length", "CALCULATION_FIELDS", "useTradeFormFields", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setValidationErrors", "calculateProfitLoss", "validateField", "useCallback", "validationRule", "test", "numValue", "parseFloat", "isNaN", "date", "Date", "getTime", "target", "prev", "fieldError", "newErrors", "calculateProfitFromValues", "direction", "newValues", "every", "priceDiff", "profit", "toFixed", "setTimeout", "validate<PERSON>ll<PERSON>ields", "errors", "<PERSON><PERSON><PERSON><PERSON>", "entries", "for<PERSON>ach", "isCalculationField", "fieldName", "Container", "LoadingState", "LoadingIcon", "LoadingText", "LoadingFallback", "TradeFormFieldsContent", "enhancedHook", "finalHandleChange", "finalHandlePriceChange", "Fragment", "TradeFormBasicFieldsContainer", "props", "Suspense", "TradeFormBasicFields"], "mappings": "qIA0DA,MAAMA,EAAwBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,EAGxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,MAAK,EAI5CC,EAAoBC,EAAAA,MAAKP,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,mEAAA,EAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBM,KAAM,YAE1C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcQ,cAAe,WAMnD,CAAC,CAAEC,UAAAA,EAAWT,MAAAA,CAAM,IAAA,OACpBS,OAAAA,GACA;AAAA;AAAA;AAAA,iBAGWT,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW;AAAA;AAAA;AAAA;AAAA,IAIrC,EAGGC,EAAmBC,EAAAA,MAAKf,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,0EAAA,yBAAA,6EAAA,EAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,OAAS,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAea,KAAM,QAC9E,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcc,aAAc,qBACrC,CAAC,CAAEd,MAAAA,EAAOe,UAAAA,CAAU,aACtCA,OAAAA,IACIf,EAAAA,EAAMO,SAANP,YAAAA,EAAcgB,QAAS,uBACvBhB,EAAAA,EAAMO,SAANP,YAAAA,EAAciB,SAAU,yBACb,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMkB,eAANlB,YAAAA,EAAoBa,KAAM,OACjD,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcQ,cAAe,WACxC,CAAC,CAAER,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBM,KAAM,YAOjC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,wBAChC,CAAC,CAAEV,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,wBAUhE,CAAC,CAAES,WAAAA,EAAYnB,MAAAA,CAAM,IAAM,OAC3B,OAAQmB,EAAU,CAChB,IAAK,QACL,IAAK,WACI,MAAA;AAAA;AAAA;AAAA;AAAA,UAKT,IAAK,aACI,MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uBAMMnB,EAAAA,EAAMO,SAANP,YAAAA,EAAcoB,gBAAiB;AAAA;AAAA,UAG9C,QACS,MAAA,EACX,CACF,CAAC,EAGGC,EAAgBC,EAAAA,OAAMzB,WAAA,CAAAC,YAAA,SAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,yFAAA,yBAAA,8dAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,OAAS,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAea,KAAM,QAC9E,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcc,aAAc,qBACrC,CAAC,CAAEd,MAAAA,EAAOe,UAAAA,CAAU,aACtCA,OAAAA,IACIf,EAAAA,EAAMO,SAANP,YAAAA,EAAcgB,QAAS,uBACvBhB,EAAAA,EAAMO,SAANP,YAAAA,EAAciB,SAAU,yBACb,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMkB,eAANlB,YAAAA,EAAoBa,KAAM,OACjD,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcQ,cAAe,WACxC,CAAC,CAAER,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBM,KAAM,YAQjC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,wBAChC,CAAC,CAAEV,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,uBAAsB,EAkBpFa,EAAsB3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,wDAAA,0CAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBE,KAAM,WAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcgB,QAAS,sBAIxC,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,MAAK,EAkBrCsB,EAAoDA,CAAC,CAChEC,KAAAA,EACArB,MAAAA,EACAsB,KAAAA,EACAC,MAAAA,EACAC,SAAAA,EACAC,QAAAA,EAAU,CAAE,EACZC,WAAAA,EAAa,CAAC,EACdd,MAAAA,EACAe,SAAAA,EAAW,GACXC,SAAAA,EAAW,GACXC,YAAAA,EACAC,UAAAA,CACF,IAAM,CACJ,MAAMC,EAAU,eAAeV,IAEzBW,EAAeA,IAAc,CACjC,OAAQV,EAAI,CACV,IAAK,QACL,IAAK,WACL,IAAK,aACI,MAAA,SACT,IAAK,OACI,MAAA,OACT,QACS,MAAA,MACX,CAAA,EAGIW,EAAgBA,IAAM,CAC1B,MAAMC,EAAY,CAChBC,GAAIJ,EACJV,KAAAA,EACAE,MAAOA,GAAS,GAChBC,SAAAA,EACAG,SAAAA,EACAC,SAAAA,EACAC,YAAAA,EACA,GAAGH,CAAAA,EAGL,OAAQJ,EAAI,CACV,IAAK,QACI,MAAA,CACL,GAAGY,EACHZ,KAAM,SACNc,KAAM,OACNC,IAAK,GAAA,EAET,IAAK,WACI,MAAA,CACL,GAAGH,EACHZ,KAAM,SACNc,KAAM,IACNC,IAAK,GAAA,EAET,IAAK,aACI,MAAA,CACL,GAAGH,EACHZ,KAAM,SACNc,KAAM,MACNC,IAAK,IACLC,IAAK,KAAA,EAET,QACS,MAAA,CACL,GAAGJ,EACHZ,KAAMU,EAAa,CAAA,CAEzB,CAAA,EAGIO,EAAgBA,IAChBjB,IAAS,SAERkB,EAAAA,KAAAvB,EAAA,CACC,GAAIc,EACJ,KAAAV,EACA,MAAOE,GAAS,GAChB,SAAAC,EACA,SAAAG,EACA,SAAAC,EACA,UAAW,CAAC,CAAChB,EAEZ,SAAA,CAAA,CAACe,GAAYa,EAAAA,KAAC,SAAO,CAAA,MAAM,GAAG,SAAA,CAAA,UAAQxC,CAAAA,EAAM,EAC5CyB,EAAQgB,IACPC,GAAAC,EAAAA,IAAC,SAA0B,CAAA,MAAOD,EAAOnB,MACtCmB,SAAO1C,EAAAA,KAAAA,EADG0C,EAAOnB,KAEpB,CACD,CACH,CAAA,CAAA,EAKFoB,MAACpC,EACC,CAAA,GAAI0B,EAAc,EAClB,UAAW,CAAC,CAACrB,EACb,WAAYU,CACZ,CAAA,EAKJ,OAAAkB,OAACjD,GAAe,UAAAuC,EACd,SAAA,CAAAa,MAAC5C,EAAW,CAAA,QAASgC,EAAS,UAAWJ,EACtC3B,SACHA,EAAA,EAECuC,EAAc,EAEd3B,GACC+B,EAAA,IAACxB,EAAa,CAAA,KAAK,QAChBP,SACHA,EAAA,CAEJ,CAAA,CAAA,CAEJ,EC9RagC,EAAwC,CACnD,CAAErB,MAAO,OAAQvB,MAAO,MAAO,EAC/B,CAAEuB,MAAO,QAASvB,MAAO,OAAQ,CAAC,EAMvB6C,EAAqC,CAChD,CAAEtB,MAAO,MAAOvB,MAAO,KAAM,EAC7B,CAAEuB,MAAO,OAAQvB,MAAO,MAAO,EAC/B,CAAEuB,MAAO,YAAavB,MAAO,WAAY,CAAC,EAM/B8C,EAAoC,CAC/C,CAAEvB,MAAO,UAAWvB,MAAO,SAAU,EACrC,CAAEuB,MAAO,SAAUvB,MAAO,QAAS,EACnC,CAAEuB,MAAO,WAAYvB,MAAO,UAAW,CAAC,EAM7B+C,EAAsC,CACjD,CAAExB,MAAO,UAAWvB,MAAO,SAAU,EACrC,CAAEuB,MAAO,cAAevB,MAAO,aAAc,EAC7C,CAAEuB,MAAO,cAAevB,MAAO,aAAc,EAC7C,CAAEuB,MAAO,MAAOvB,MAAO,KAAM,EAC7B,CAAEuB,MAAO,YAAavB,MAAO,WAAY,CAAC,EAM/BgD,EAAsC,CACjD,CAAEzB,MAAO,UAAWvB,MAAO,SAAU,EACrC,CAAEuB,MAAO,UAAWvB,MAAO,SAAU,EACrC,CAAEuB,MAAO,UAAWvB,MAAO,SAAU,CAAC,EAM3BiD,EAAyC,CACpD,CAAE1B,MAAO,MAAOvB,MAAO,KAAM,EAC7B,CAAEuB,MAAO,SAAUvB,MAAO,QAAS,EACnC,CAAEuB,MAAO,UAAWvB,MAAO,SAAU,EACrC,CAAEuB,MAAO,UAAWvB,MAAO,SAAU,CAAC,EAM3BkD,EAA4C,CAEvD,CACE7B,KAAM,OACNrB,MAAO,OACPsB,KAAM,OACNK,SAAU,GACVwB,MAAO,OACT,EACA,CACE9B,KAAM,SACNrB,MAAO,SACPsB,KAAM,OACNK,SAAU,GACVE,YAAa,sBACbsB,MAAO,QACPzB,WAAY,CACV0B,MAAO,CAAEC,cAAe,WAAY,CACtC,CACF,EACA,CACEhC,KAAM,YACNrB,MAAO,YACPsB,KAAM,SACNK,SAAU,GACVF,QAASmB,EACTO,MAAO,OACT,EACA,CACE9B,KAAM,SACNrB,MAAO,SACPsB,KAAM,SACNK,SAAU,GACVF,QAASoB,EACTM,MAAO,OACT,EAGA,CACE9B,KAAM,aACNrB,MAAO,cACPsB,KAAM,QACNK,SAAU,GACVwB,MAAO,SACT,EACA,CACE9B,KAAM,YACNrB,MAAO,aACPsB,KAAM,QACNK,SAAU,GACVwB,MAAO,SACT,EACA,CACE9B,KAAM,WACNrB,MAAO,WACPsB,KAAM,WACNK,SAAU,GACVwB,MAAO,SACT,EACA,CACE9B,KAAM,SACNrB,MAAO,kBACPsB,KAAM,QACNK,SAAU,GACVwB,MAAO,SACT,EAGA,CACE9B,KAAM,QACNrB,MAAO,QACPsB,KAAM,SACNG,QAASqB,EACTK,MAAO,UACT,EACA,CACE9B,KAAM,UACNrB,MAAO,UACPsB,KAAM,SACNG,QAASsB,EACTI,MAAO,UACT,EACA,CACE9B,KAAM,iBACNrB,MAAO,yBACPsB,KAAM,SACN6B,MAAO,WACPzB,WAAY,CACVW,IAAK,EACLC,IAAK,GACLF,KAAM,CACR,CACF,EAGA,CACEf,KAAM,YACNrB,MAAO,aACPsB,KAAM,OACNO,YAAa,2BACbsB,MAAO,UACT,EACA,CACE9B,KAAM,SACNrB,MAAO,UACPsB,KAAM,SACNG,QAASuB,EACTG,MAAO,UACT,EACA,CACE9B,KAAM,kBACNrB,MAAO,oBACPsB,KAAM,SACNG,QAASwB,EACTE,MAAO,UACT,EACA,CACE9B,KAAM,eACNrB,MAAO,gBACPsB,KAAM,OACNO,YAAa,0BACbsB,MAAO,UACT,CAAC,EAMUG,EAAoBH,GACxBD,EAAkBK,OAAgBC,GAAAA,EAAML,QAAUA,CAAK,EAMnDM,EAAkBpC,GACtB6B,EAAkBQ,KAAcF,GAAAA,EAAMnC,OAASA,CAAI,EAM/CsC,EAAe,CAC1B,CACEC,IAAK,QACLC,MAAO,oBACPC,YAAa,6CACbC,KAAM,IACR,EACA,CACEH,IAAK,UACLC,MAAO,gBACPC,YAAa,kDACbC,KAAM,IACR,EACA,CACEH,IAAK,WACLC,MAAO,mBACPC,YAAa,8CACbC,KAAM,IACR,EACA,CACEH,IAAK,WACLC,MAAO,iBACPC,YAAa,0CACbC,KAAM,IACR,CAAC,EAMUC,EAAyB,CACpCC,OAAQ,CACNC,QAAS,eACTC,QAAS,sCACX,EACAC,WAAY,CACV/B,IAAK,IACL8B,QAAS,oCACX,EACAE,UAAW,CACThC,IAAK,IACL8B,QAAS,mCACX,EACAG,SAAU,CACRjC,IAAK,EACL8B,QAAS,6BACX,EACAI,eAAgB,CACdlC,IAAK,EACLC,IAAK,GACL6B,QAAS,0CACX,CACF,ECpPMK,EAAyBhF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGzB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe6E,KAAM,OAAM,EAG7CC,EAAoBlF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,kEAAA,iDAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAc+E,UAAW,uBAClC,CAAC,CAAE/E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAciB,SAAU,yBAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMkB,eAANlB,YAAAA,EAAoBgF,KAAM,OAKxC,CAAC,CAAEhF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,uBAAsB,EAK5EuE,EAAqBrF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,eAAA,2GAAA,IAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegF,KAAM,QACpB,CAAC,CAAEhF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAciB,SAAU,yBACpD,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcc,aAAc,qBAWzC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,uBAAsB,EAI1EwE,EAAuBtF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAea,KAAM,OAAM,EAG7CsE,EAAmBvF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2GAAA,oBAAA,qBAAA,KAAA,EAOZ,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,wBACrC,CAAC,CAAEV,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMkB,eAANlB,YAAAA,EAAoBa,KAAM,OACtC,CAAC,CAAEb,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcU,UAAW,uBAAsB,EAG9E0E,EAAoBC,EAAAA,GAAExF,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,4DAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBgF,KAAM,YAE1C,CAAC,CAAEhF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcQ,cAAe,UAAS,EAM1D8E,EAA0BC,EAAAA,EAAC1F,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,yBAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBM,KAAM,YAC1C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcoB,gBAAiB,yBAC7C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,MAAK,EAI/CsF,EAAsB5F,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegF,KAAM,OAAM,EAGjDS,EAAoB7F,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,2DAAA,IAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegF,KAAM,QAIlC,CAAC,CAAEhF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAea,KAAM,OAAM,EAcxC6E,EAA4DA,CAAC,CACxEC,WAAAA,EACAC,aAAAA,EACAC,kBAAAA,EACAC,iBAAAA,EACA9D,SAAAA,EAAW,GACXE,UAAAA,CACF,IAAM,CAIE6D,MAAAA,EAAoBC,GAEpB,CAAC,aAAc,YAAa,UAAU,EAAEC,SAASD,EAAYvE,IAAI,EAC3DyE,GAA+D,CACrEL,EAAkBK,CAAwC,CAAA,EAGvDN,EAGT,OACG7C,EAAA,IAAA6B,EAAA,CAAgB,UAAA1C,EACd6B,SAAAA,EAAalB,IAAaU,GAAA,CACnB4C,MAAAA,EAASzC,EAAiBH,EAAMS,GAAG,EAEzC,OAAImC,EAAOC,SAAW,EAAU,YAG7BtB,EACC,CAAA,SAAA,CAAC/B,EAAA,IAAAkC,EAAA,CACC,gBAACC,EACC,CAAA,SAAA,CAACnC,EAAAA,IAAAoC,EAAA,CAAW5B,WAAMY,IAAK,CAAA,SACtB,MACC,CAAA,SAAA,CAACpB,EAAAA,IAAAqC,EAAA,CAAY7B,WAAMU,KAAM,CAAA,EACzBlB,EAAAA,IAACuC,EAAkB/B,CAAAA,SAAAA,EAAMW,WAAY,CAAA,CAAA,EACvC,CAAA,CAAA,CACF,CACF,CAAA,EAEAnB,EAAAA,IAACyC,GACC,SAACzC,EAAA,IAAA0C,EAAA,CACEU,WAAOtD,IAAImD,GACTjD,EAAA,IAAAvB,EAAA,CAEC,KAAMwE,EAAYvE,KAClB,MAAOuE,EAAY5F,MACnB,KAAM4F,EAAYtE,KAClB,MAAOiE,EAAWK,EAAYvE,IAA6B,EAC3D,SAAUsE,EAAiBC,CAAW,EACtC,QAASA,EAAYnE,QACrB,WAAYmE,EAAYlE,WACxB,MAAOgE,EAAiBE,EAAYvE,IAAI,EACxC,SAAUuE,EAAYjE,SACtB,SAAAC,EACA,YAAagE,EAAY/D,aAXpB+D,EAAYvE,IAWoB,CAExC,CAAA,CACH,CACF,CAAA,CAAA,GA9Be8B,EAAMS,GA+BvB,CAEH,CAAA,CACH,CAAA,CAEJ,ECnJMqC,EAAqB,CAAC,aAAc,YAAa,WAAY,WAAW,EAOjEC,EAAqBA,CAAC,CACjCX,WAAAA,EACAY,cAAAA,EAEAC,oBAAAA,EACAC,oBAAAA,CACuB,IAAgC,CAIvD,MAAMC,EAAgBC,EAAAA,YAAY,CAAClF,EAAcE,IAA8B,CACvEqE,MAAAA,EAAcnC,EAAepC,CAAI,EACjCmF,EAAiBxC,EAAuB3C,CAA2C,EAGzF,GAAIuE,GAAAA,MAAAA,EAAajE,WAAa,CAACJ,GAASA,IAAU,IAChD,MAAO,GAAGqE,EAAY5F,oBAIpB,GAAA,CAACuB,GAASA,IAAU,GACf,OAAA,KAIT,GAAIiF,EAAgB,CAClB,GAAI,YAAaA,GACX,CAACA,EAAetC,QAAQuC,KAAKlF,CAAK,EACpC,OAAOiF,EAAerC,QAI1B,GAAI,QAASqC,EAAgB,CACrBE,MAAAA,EAAWC,WAAWpF,CAAK,EACjC,GAAIqF,MAAMF,CAAQ,GAAKA,EAAWF,EAAenE,IAC/C,OAAOmE,EAAerC,QAI1B,GAAI,QAASqC,EAAgB,CACrBE,MAAAA,EAAWC,WAAWpF,CAAK,EACjC,GAAIqF,MAAMF,CAAQ,GAAKA,EAAWF,EAAelE,IAC/C,OAAOkE,EAAerC,SAM5B,OAAQ9C,EAAI,CACV,IAAK,OACGwF,MAAAA,EAAO,IAAIC,KAAKvF,CAAK,EAC3B,GAAIqF,MAAMC,EAAKE,QAAQ,CAAC,EACf,MAAA,4BAELF,GAAAA,EAAWC,IAAAA,KACN,MAAA,+BAET,MAEF,IAAK,aACL,IAAK,YACL,IAAK,WACL,IAAK,SACGJ,MAAAA,EAAWC,WAAWpF,CAAK,EAC7BqF,GAAAA,MAAMF,CAAQ,EACT,MAAA,8BAET,KACJ,CAEO,OAAA,IACT,EAAG,CAAE,CAAA,EAKClB,EAAee,cAClBT,GAA+D,CACxD,KAAA,CAAEzE,KAAAA,EAAME,MAAAA,CAAAA,EAAUuE,EAAEkB,OAG1Bb,EAAuBc,IAAA,CAAE,GAAGA,EAAM,CAAC5F,CAAI,EAAGE,CAAQ,EAAA,EAG5C2F,MAAAA,EAAaZ,EAAcjF,EAAME,CAAK,EAC5C6E,EAA4Ba,GAAA,CAC1B,MAAME,EAAY,CAAE,GAAGF,CAAAA,EACvB,OAAIC,EACFC,EAAU9F,CAAI,EAAI6F,EAElB,OAAOC,EAAU9F,CAAI,EAEhB8F,CAAAA,CACR,CAEH,EAAA,CAACb,EAAeH,EAAeC,CAAmB,CACpD,EAKMgB,EAA4Bb,EAAAA,YAAY,IAAc,CAC1D,MAAMnC,EAAauC,WAAWpB,EAAWnB,UAAU,GAAK,EAClDC,EAAYsC,WAAWpB,EAAWlB,SAAS,GAAK,EAChDC,EAAWqC,WAAWpB,EAAWjB,QAAQ,GAAK,EAC9C+C,EAAY9B,EAAW8B,UAE7B,OAAIjD,IAAe,GAAKC,IAAc,GAAKC,IAAa,EAC/C,GAGS+C,IAAc,OAAShD,EAAYD,EAAaA,EAAaC,GAE5DC,CAAAA,EAClB,CAACiB,EAAWnB,WAAYmB,EAAWlB,UAAWkB,EAAWjB,SAAUiB,EAAW8B,SAAS,CAAC,EAKrF5B,EAAoBc,cACvBT,GAA2C,CACpC,KAAA,CAAEzE,KAAAA,EAAME,MAAAA,CAAAA,EAAUuE,EAAEkB,OAG1Bb,EAAsBc,GAAA,CACpB,MAAMK,EAAY,CAAE,GAAGL,EAAM,CAAC5F,CAAI,EAAGE,CAAAA,EAInC0E,GAAAA,EAAmBsB,MACjB/D,GAAAA,IAAUnC,EAAOE,EAAQ+F,EAAU9D,CAA8B,CACnE,EACA,CACA,MAAMY,EAAauC,WAAWtF,IAAS,aAAeE,EAAQ+F,EAAUlD,UAAU,GAAK,EACjFC,EAAYsC,WAAWtF,IAAS,YAAcE,EAAQ+F,EAAUjD,SAAS,GAAK,EAC9EC,EAAWqC,WAAWtF,IAAS,WAAaE,EAAQ+F,EAAUhD,QAAQ,GAAK,EAC3E+C,EAAYhG,IAAS,YAAcE,EAAQ+F,EAAUD,UAE3D,GAAIjD,EAAa,GAAKC,EAAY,GAAKC,EAAW,EAAG,CACnD,MAAMkD,EACJH,IAAc,OAAShD,EAAYD,EAAaA,EAAaC,EAE/DiD,EAAUG,QAAUD,EAAYlD,GAAUoD,QAAQ,CAAC,GAIhDJ,OAAAA,CAAAA,CACR,EAGKJ,MAAAA,EAAaZ,EAAcjF,EAAME,CAAK,EAC5C6E,EAA4Ba,GAAA,CAC1B,MAAME,EAAY,CAAE,GAAGF,CAAAA,EACvB,OAAIC,EACFC,EAAU9F,CAAI,EAAI6F,EAElB,OAAOC,EAAU9F,CAAI,EAEhB8F,CAAAA,CACR,EAGGd,GACFsB,WAAWtB,EAAqB,CAAC,GAGrC,CAACC,EAAeH,EAAeC,EAAqBC,CAAmB,CACzE,EAKMuB,EAAoBrB,EAAAA,YAAY,IAAe,CACnD,MAAMsB,EAA2B,CAAA,EACjC,IAAIC,EAAU,GAGPC,cAAAA,QAAQxC,CAAU,EAAEyC,QAAQ,CAAC,CAAC3G,EAAME,CAAK,IAAM,CAC9CX,MAAAA,EAAQ0F,EAAcjF,EAAME,CAAK,EACnCX,IACFiH,EAAOxG,CAAI,EAAIT,EACLkH,EAAA,GACZ,CACD,EAED1B,EAAoByB,CAAM,EACnBC,CACN,EAAA,CAACvC,EAAYe,EAAeF,CAAmB,CAAC,EAK7C6B,EAAqB1B,cAAa2B,GAC/BjC,EAAmBJ,SAASqC,CAAS,EAC3C,CAAE,CAAA,EAEE,MAAA,CACL1C,aAAAA,EACAC,kBAAAA,EACAa,cAAAA,EACAsB,kBAAAA,EACAR,0BAAAA,EACAa,mBAAAA,CAAAA,CAEJ,EC1NME,EAAmB3I,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,UAAA,GAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe6E,KAAM,QAC7B,CAAC,CAAE7E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcc,aAAc,qBAChD,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcQ,cAAe,UAAS,EAe1DgI,GAAsB5I,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,sCAAA,EAKlB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe6E,KAAM,OAAM,EAKjD4D,GAAqB7I,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,mGAAA,EAEX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAea,KAAM,OAAM,EAevD6H,GAAqBnD,EAAAA,EAAC1F,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,YAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,YAANL,YAAAA,EAAiBgF,KAAM,YAC1C,CAAC,CAAEhF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,SAANP,YAAAA,EAAcoB,gBAAiB,wBAAuB,EAO1EuH,GAA4BA,IAChC/F,EAAAA,KAAC4F,GACC,CAAA,SAAA,CAAAzF,EAAAA,IAAC0F,IAAY,SAAE,IAAA,CAAA,EACf1F,EAAAA,IAAC2F,IAAY,SAAsB,wBAAA,CAAA,CAAA,CACrC,CAAA,EAMIE,GAAuEA,CAAC,CAC5EjD,WAAAA,EACAY,cAAAA,EACAX,aAAAA,EACAE,iBAAAA,EACAU,oBAAAA,EACAC,oBAAAA,EACAzE,SAAAA,EAAW,EACb,IAAM,CAEE6G,MAAAA,EACJtC,GAAiBC,EACbF,EAAmB,CACjBX,WAAAA,EACAY,cAAAA,EACAT,iBAAAA,EACAU,oBAAAA,EACAC,oBAAAA,CACD,CAAA,EACD,KAGAqC,GAAoBD,GAAAA,YAAAA,EAAcjD,eAAgBA,EAClDmD,GAAyBF,GAAAA,YAAAA,EAAchD,oBAAqBD,EAGhE,OAAA7C,EAAAA,IAAAiG,EAAAA,SAAA,CACE,SAACjG,EAAA,IAAA2C,EAAA,CACC,WAAAC,EACA,aAAcmD,EACd,kBAAmBC,EACnB,iBAAAjD,EACA,SAAA9D,CAAA,CAAmB,CAIvB,CAAA,CAEJ,EAWaiH,GAEAC,SAERX,EAAU,CAAA,UAAWW,EAAMhH,UAC1B,eAACiH,EAAS,SAAA,CAAA,SAAWpG,MAAA4F,GAAA,CAAe,CAAA,EAClC,SAAC5F,MAAA6F,GAAA,CAAuB,GAAIM,CAAM,CAAA,EACpC,CACF,CAAA,ECnIEE,GAAuEF,GACpEnG,EAAA,IAACkG,GAAkCC,CAAAA,GAAAA,CAAS,CAAA"}