{"version": 3, "file": "patternQuality-31c523c9.js", "sources": ["../../src/features/trade-journal/constants/patternQuality.ts"], "sourcesContent": ["/**\n * Pattern Quality Assessment Constants\n *\n * Constants for the pattern quality assessment feature\n */\n\nimport { ScoreRange } from '../types';\n\n/**\n * Score values for each score range\n */\nexport const SCORE_VALUES: Record<ScoreRange, number> = {\n  excellent: 5,\n  good: 4,\n  average: 3,\n  poor: 2,\n  unacceptable: 1,\n};\n\n/**\n * Score range options for dropdowns\n */\nexport const SCORE_RANGE_OPTIONS = [\n  { value: 'excellent', label: 'Excellent (5)' },\n  { value: 'good', label: 'Good (4)' },\n  { value: 'average', label: 'Average (3)' },\n  { value: 'poor', label: 'Poor (2)' },\n  { value: 'unacceptable', label: 'Unacceptable (1)' },\n];\n\n/**\n * Pattern Quality Criteria Definitions\n */\nexport const PATTERN_QUALITY_CRITERIA = {\n  clarity: {\n    title: 'Pattern Clarity',\n    description: 'How clear and well-defined is the pattern?',\n    excellent: 'Pattern is extremely clear with perfect formation',\n    good: 'Pattern is clear with minor imperfections',\n    average: 'Pattern is recognizable but has some ambiguity',\n    poor: '<PERSON>tern is difficult to recognize with significant ambiguity',\n    unacceptable: 'Pattern is barely recognizable or completely ambiguous',\n  },\n  confluence: {\n    title: 'Confluence Factors',\n    description: 'How many supporting factors align with this pattern?',\n    excellent: 'Multiple strong confluence factors (5+ factors)',\n    good: 'Several good confluence factors (3-4 factors)',\n    average: 'Some confluence factors (2-3 factors)',\n    poor: 'Minimal confluence (1-2 weak factors)',\n    unacceptable: 'No confluence factors',\n  },\n  context: {\n    title: 'Market Context',\n    description: 'How well does the pattern fit within the broader market context?',\n    excellent: 'Perfect alignment with market structure and conditions',\n    good: 'Good alignment with market structure and conditions',\n    average: 'Reasonable alignment with some contradicting factors',\n    poor: 'Poor alignment with several contradicting factors',\n    unacceptable: 'Pattern contradicts the broader market context',\n  },\n  risk: {\n    title: 'Risk Profile',\n    description: 'How well-defined and manageable is the risk?',\n    excellent: 'Extremely clear stop location with minimal risk',\n    good: 'Clear stop location with reasonable risk',\n    average: 'Identifiable stop location but with moderate risk',\n    poor: 'Unclear stop location or high risk',\n    unacceptable: 'No clear stop location or extremely high risk',\n  },\n  reward: {\n    title: 'Reward Potential',\n    description: 'What is the potential reward relative to risk?',\n    excellent: 'Exceptional reward potential (5R+)',\n    good: 'Strong reward potential (3-5R)',\n    average: 'Reasonable reward potential (2-3R)',\n    poor: 'Limited reward potential (1-2R)',\n    unacceptable: 'Poor reward potential (<1R)',\n  },\n  timeframe: {\n    title: 'Timeframe Alignment',\n    description: 'How well does the pattern align across multiple timeframes?',\n    excellent: 'Strong alignment across all relevant timeframes',\n    good: 'Good alignment across most timeframes',\n    average: 'Alignment on primary timeframe with some higher/lower support',\n    poor: 'Limited alignment across timeframes',\n    unacceptable: 'Pattern only appears on a single timeframe with contradictions on others',\n  },\n  volume: {\n    title: 'Volume Profile',\n    description: 'How does volume support the pattern?',\n    excellent: 'Volume perfectly confirms the pattern',\n    good: 'Volume generally supports the pattern',\n    average: 'Volume is neutral or mixed',\n    poor: 'Volume somewhat contradicts the pattern',\n    unacceptable: 'Volume strongly contradicts the pattern',\n  },\n};\n\n/**\n * Calculate total score from criteria\n */\nexport const calculateTotalScore = (criteria: Record<string, ScoreRange>): number => {\n  return Object.values(criteria).reduce((total, score) => {\n    return total + (SCORE_VALUES[score] || 0);\n  }, 0);\n};\n\n/**\n * Convert total score to 1-10 rating\n */\nexport const convertScoreToRating = (totalScore: number): number => {\n  // Maximum possible score is 35 (7 criteria * 5 points)\n  // Convert to a 1-10 scale\n  const maxPossibleScore = Object.keys(PATTERN_QUALITY_CRITERIA).length * 5;\n  const rating = Math.round((totalScore / maxPossibleScore) * 10);\n\n  // Ensure rating is between 1 and 10\n  return Math.max(1, Math.min(10, rating));\n};\n\n/**\n * Get rating description based on rating value\n */\nexport const getRatingDescription = (rating: number): string => {\n  if (rating >= 9) return 'Exceptional';\n  if (rating >= 8) return 'Excellent';\n  if (rating >= 7) return 'Very Good';\n  if (rating >= 6) return 'Good';\n  if (rating >= 5) return 'Average';\n  if (rating >= 4) return 'Below Average';\n  if (rating >= 3) return 'Poor';\n  if (rating >= 2) return 'Very Poor';\n  return 'Unacceptable';\n};\n\n/**\n * Get color for rating\n */\nexport const getRatingColor = (rating: number): string => {\n  if (rating >= 8) return 'var(--success-color)'; // Green\n  if (rating >= 6) return '#8BC34A'; // Light Green\n  if (rating >= 5) return '#FFC107'; // Amber\n  if (rating >= 3) return '#FF9800'; // Orange\n  return 'var(--error-color)'; // Red\n};\n"], "names": ["SCORE_VALUES", "excellent", "good", "average", "poor", "unacceptable", "SCORE_RANGE_OPTIONS", "value", "label", "PATTERN_QUALITY_CRITERIA", "clarity", "title", "description", "confluence", "context", "risk", "reward", "timeframe", "volume", "calculateTotalScore", "criteria", "Object", "values", "reduce", "total", "score", "convertScoreToRating", "totalScore", "maxPossibleScore", "keys", "length", "rating", "Math", "round", "max", "min", "getRatingDescription", "getRatingColor"], "mappings": "AAWO,MAAMA,EAA2C,CACtDC,UAAW,EACXC,KAAM,EACNC,QAAS,EACTC,KAAM,EACNC,aAAc,CAChB,EAKaC,EAAsB,CACjC,CAAEC,MAAO,YAAaC,MAAO,eAAgB,EAC7C,CAAED,MAAO,OAAQC,MAAO,UAAW,EACnC,CAAED,MAAO,UAAWC,MAAO,aAAc,EACzC,CAAED,MAAO,OAAQC,MAAO,UAAW,EACnC,CAAED,MAAO,eAAgBC,MAAO,kBAAmB,CAAC,EAMzCC,EAA2B,CACtCC,QAAS,CACPC,MAAO,kBACPC,YAAa,6CACbX,UAAW,oDACXC,KAAM,4CACNC,QAAS,iDACTC,KAAM,+DACNC,aAAc,wDAChB,EACAQ,WAAY,CACVF,MAAO,qBACPC,YAAa,uDACbX,UAAW,kDACXC,KAAM,gDACNC,QAAS,wCACTC,KAAM,wCACNC,aAAc,uBAChB,EACAS,QAAS,CACPH,MAAO,iBACPC,YAAa,mEACbX,UAAW,yDACXC,KAAM,sDACNC,QAAS,uDACTC,KAAM,oDACNC,aAAc,gDAChB,EACAU,KAAM,CACJJ,MAAO,eACPC,YAAa,+CACbX,UAAW,kDACXC,KAAM,2CACNC,QAAS,oDACTC,KAAM,qCACNC,aAAc,+CAChB,EACAW,OAAQ,CACNL,MAAO,mBACPC,YAAa,iDACbX,UAAW,qCACXC,KAAM,iCACNC,QAAS,qCACTC,KAAM,kCACNC,aAAc,6BAChB,EACAY,UAAW,CACTN,MAAO,sBACPC,YAAa,8DACbX,UAAW,kDACXC,KAAM,wCACNC,QAAS,gEACTC,KAAM,sCACNC,aAAc,0EAChB,EACAa,OAAQ,CACNP,MAAO,iBACPC,YAAa,uCACbX,UAAW,wCACXC,KAAM,wCACNC,QAAS,6BACTC,KAAM,0CACNC,aAAc,yCAChB,CACF,EAKac,EAAuBC,GAC3BC,OAAOC,OAAOF,CAAQ,EAAEG,OAAO,CAACC,EAAOC,IACrCD,GAASxB,EAAayB,CAAK,GAAK,GACtC,CAAC,EAMOC,EAAwBC,GAA+B,CAGlE,MAAMC,EAAmBP,OAAOQ,KAAKpB,CAAwB,EAAEqB,OAAS,EAClEC,EAASC,KAAKC,MAAON,EAAaC,EAAoB,EAAE,EAG9D,OAAOI,KAAKE,IAAI,EAAGF,KAAKG,IAAI,GAAIJ,CAAM,CAAC,CACzC,EAKaK,EAAwBL,GAC/BA,GAAU,EAAU,cACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,gBACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,YACjB,eAMIM,EAAkBN,GACzBA,GAAU,EAAU,uBACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACjB"}