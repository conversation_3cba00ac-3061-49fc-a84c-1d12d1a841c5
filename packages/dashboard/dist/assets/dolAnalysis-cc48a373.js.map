{"version": 3, "mappings": "uKAegBA,WACdC,EACAC,EACA,CAEAC,mBAAU,IAAM,CACVF,KAAWG,YAAcH,EAAWI,OAAQ,CACxCD,QAAaE,WAAWL,EAAWG,UAAU,EAC7CC,EAASC,WAAWL,EAAWI,MAAM,EAE3C,GAAID,EAAa,EAAG,CAClB,MAAMG,GAAaF,EAASD,GAAYI,QAAQ,CAAC,EACjDN,EAAyBO,KACvB,GAAGA,EACHF,WACA,KAEN,EACC,CAACN,EAAWG,WAAYH,EAAWI,OAAQH,CAAa,CAAC,EAG5DC,YAAU,IAAM,GAIb,CAACF,EAAWS,UAAWT,EAAWU,SAAUV,EAAWW,MAAM,CAAC,EA8B1D,CACLC,oBAzB0BA,IAAM,CAChC,GAAIZ,EAAWa,YAAcb,EAAWc,WAAad,EAAWe,SAAU,CAClEF,QAAaR,WAAWL,EAAWa,UAAU,EAC7CC,EAAYT,WAAWL,EAAWc,SAAS,EAC3CC,EAAWV,WAAWL,EAAWe,QAAQ,EAE3C,IAACC,MAAMH,CAAU,GAAK,CAACG,MAAMF,CAAS,GAAK,CAACE,MAAMD,CAAQ,EAAG,CAC3DE,MAEAjB,EAAWkB,YAAc,OAC3BD,GAAcH,EAAYD,GAAcE,EAExCE,GAAcJ,EAAaC,GAAaC,EAG1Cd,EAAyBO,KACvB,GAAGA,EACHJ,OAAQa,EAAWV,QAAQ,CAAC,EAC5BY,OAAQF,EAAa,EAAI,MAAQA,EAAa,EAAI,OAAS,WAC3D,KAEN,CAIAL,CAEJ,CCjDgBQ,WACdpB,EACAqB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,MAAMC,EAAWC,IACX,CAACC,EAAcC,CAAe,EAAIC,WAAS,EAAK,EAoN/C,OACLC,aA/MmBC,cACnB,MAAOC,GAAuB,eAIxB,GAHJA,EAAEC,eAAe,EAGb,CAACd,IAAwB,CAC3BI,EAAS,gEAAgE,EAErED,GACFA,EAAa,OAAO,EAEtB,OAIF,GAAID,GAAaA,IAAc,SAAWD,GAAsB,CAACA,IAAsB,CACrFG,EAAS,4EAA4E,EACrF,OAGFK,EAAgB,EAAI,EACpBL,EAAS,IAAI,EACbC,EAAW,IAAI,EAEX,IAEF,MAAMU,EAAqB,CACzBC,QAASxC,EAAWyC,uBAAyB,GAC7CC,WAAY1C,EAAW2C,0BAA4B,GACnDC,QAAS5C,EAAW6C,uBAAyB,GAC7CC,KAAM9C,EAAW+C,oBAAsB,GACvCC,OAAQhD,EAAWiD,sBAAwB,GAC3CC,UAAWlD,EAAWmD,yBAA2B,GACjDC,OAAQpD,EAAWqD,sBAAwB,IAIvCC,EAAoBC,OAAOC,OAAOjB,CAAkB,EAAEkB,MAAMC,GAASA,IAAU,EAAE,EAGjFC,EAAmB,CAAE,GAAG3D,GAG9B,GAAIsD,EAAmB,CAEf,MAAEM,sBAAqBC,wBAAyB,MAAMC,aAC1D,8BACF,MAGMC,EAAaH,EAAoBrB,CAAyB,EAC1DyB,EAASH,EAAqBE,CAAU,EAG7CJ,EAAyBM,eAAiB,CACzCC,MAAOH,EACPC,SACAG,SAAU5B,EACV6B,MAAOpE,EAAWqE,qBAAuB,IAKzCrE,EAAWsE,UAEZX,EAAyBY,YAAc,CACtCD,QAAStE,EAAWsE,QACpBE,YAAaxE,EAAWwE,aAAe,GACvCC,YAAazE,EAAWyE,aAAe,GACvCC,WAAY1E,EAAW0E,YAAc,CAAE,EACvCC,YAAa3E,EAAW4E,gBAAkB,GAC1CC,cAAe7E,EAAW8E,kBAAoB,GAC9CC,UAAW/E,EAAWgF,cAAgB,GACtCC,gBAAiBjF,EAAWkF,oBAAsB,GAClDC,cAAenF,EAAWoF,iBAAmBC,SAASrF,EAAWoF,gBAAgB,EAAI,EACrFhB,MAAOpE,EAAWsF,UAAY,KAI1BC,YAAI,kBAAmB5B,CAAgB,EAG/C,MAAM6B,EAAc,CAClBC,KAAMzF,EAAWyF,KACjBC,WAAY1F,EAAW2F,WAAa,UACpCC,QAAS5F,EAAW4F,QACpB1E,UAAYlB,EAAWkB,YAAc,OAAS,OAAS,QACvD2E,OAAQ7F,EAAW6F,OACnBC,YAAazF,WAAWL,EAAWa,UAAU,GAAK,EAClDkF,WAAY1F,WAAWL,EAAWc,SAAS,GAAK,EAChDkF,WAAYhG,EAAWM,UAAYD,WAAWL,EAAWM,SAAS,EAAI2F,OACtEC,YAAa7F,WAAWL,EAAWI,MAAM,GAAK,EAC9C+F,SAAWnG,EAAWmB,SAAW,MAC7B,MACAnB,EAAWmB,SAAW,OACtB,OACA8E,OACJG,uBAAwBpG,EAAWiE,eAC/B5D,WAAWL,EAAWiE,cAAc,EACpCgC,OACJI,WAAYrG,EAAWS,UACvB6F,UAAWtG,EAAWU,SACtB6F,QAASvG,EAAWW,OACpB6F,YAAaxG,EAAWG,WAAaE,WAAWL,EAAWG,UAAU,EAAI8F,OACzEQ,gBAAiBpB,SAASrF,EAAWe,QAAQ,GAAK,EAClDqD,MAAOpE,EAAWoE,MAElBsC,gBAAgB1G,IAAW2G,kBAAX3G,cAA4B4G,SAC5CC,cAAc7G,IAAW2G,kBAAX3G,cAA4B8G,OAC1CC,gBAAgB/G,IAAW2G,kBAAX3G,cAA4BgH,SAC5CC,aAAajH,IAAW2G,kBAAX3G,cAA4BkH,MACzCP,gBAAiB3G,EAAW2G,iBAIxBQ,EAAanH,EAAWoH,aAC1B,CACEC,SAAU,EACVC,QAAStH,EAAW2F,UACpB4B,cAAevH,EAAWoH,aAC1BI,kBAAmBxH,EAAWyH,eAEhCxB,SAGEyB,EACJ1H,EAAW2H,kBAAoB3H,EAAW4H,eACtC,CACEP,SAAU,EACVQ,cAAe7H,EAAW2H,iBAC1BG,gBAAiB9H,EAAW+H,mBAC5BC,gBAAiBhI,EAAW4H,eAC5BK,iBAAiBjI,IAAWkI,iBAAXlI,cAA2BmI,KAAK,MACjDC,IAAKpI,EAAWqI,aAElBpC,SAGAqC,EACJtI,EAAWsE,SAAWtE,EAAWsF,SAC7B,CACE+B,SAAU,EACVkB,gBAAiBvI,EAAWqI,cAC5BG,aAAcxI,EAAW4E,eACzB6D,WAAYzI,EAAW8E,iBACvB4D,UAAW1I,EAAWsF,QAExBW,SAEA0C,EAAoB,CACxBC,MAAOpD,EACPqD,YAAa1B,EACb2B,MAAOpB,EACPqB,SAAUT,GAMZ,GAF+BjH,GAAcE,GAAaA,EAAUyH,GAExC,CAEpBC,QAAU,OAAO1H,EAAUyH,IAAO,SAAW3D,SAAS9D,EAAUyH,EAAE,EAAIzH,EAAUyH,GAChFE,QAAoBC,uBAAuBF,EAASN,CAAiB,EACnEpD,YAAI,0CAA2C0D,CAAO,MACzD,CAEL,MAAMG,EAAe,MAAMF,EAAoBG,qBAAqBV,CAAiB,EAC7EpD,YAAI,4CAA6C6D,CAAY,EAKrEvH,EADER,EACS,aAAarB,EAAWsJ,aAAatJ,EAAWyF,6BAGzD,iBAAiBzF,EAAWsJ,aAAatJ,EAAWyF,4BAHiC,EAQzF8D,WAAW,IAAM,CAEfC,QAAQjE,IAAI,6DAA6D,EACzEzD,EAAS,UAAU,GAClB,IAAI,QACA2H,GACP7H,EAAS,yCAAyC,EAC1C8H,cAAM,yBAA0BD,CAAG,SACnC,CACRxH,EAAgB,EAAK,CACvB,CAEF,GACEjC,EACAwB,EACAC,EACAC,EACAC,EACAC,EACAC,EACAR,EACAE,EACAO,CAAQ,CAEZ,EAIEE,eAEJ,CC5OO,SAAS2H,GAAqB,CACnC,KAAM,CAACC,EAAkBC,CAAmB,EAAI3H,WAA2B,CAAE,GAQvET,EAAqBW,cACzB,CAACpC,EAA6B0B,IAA+B,CAC3D,MAAMoI,EAA2B,GAGjC,OAAQpI,EAAS,CACf,IAAK,QAEE1B,EAAWyF,OAAMqE,EAAOrE,KAAO,oBAC/BzF,EAAWsJ,SAAQQ,EAAOR,OAAS,sBACnCtJ,EAAWa,aAAYiJ,EAAOjJ,WAAa,2BAC3Cb,EAAWc,YAAWgJ,EAAOhJ,UAAY,0BACzCd,EAAWe,WAAU+I,EAAO/I,SAAW,wBAC5C,MAEF,IAAK,SAECf,EAAWS,WAAaT,EAAWU,UACjCV,EAAWU,SAAWV,EAAWS,YACnCqJ,EAAOpJ,SAAW,sCAIlBV,EAAWW,QAAUX,EAAWS,WAC9BT,EAAWS,UAAYT,EAAWW,SACpCmJ,EAAOrJ,UAAY,8CAGvB,MAEF,IAAK,WAECT,EAAW+J,sBAAwB,CAAC/J,EAAW2H,mBACjDmC,EAAOnC,iBAAmB,sCAGxB3H,EAAWgK,wBAA0B,CAAChK,EAAW+H,qBACnD+B,EAAO/B,mBAAqB,wCAK5B/H,EAAW2H,kBACX3H,EAAW+H,oBACX/H,EAAW2H,mBAAqB3H,EAAW+H,qBAE3C+B,EAAO/B,mBAAqB,uDAE9B,MAEF,IAAK,eAEC/H,EAAWsE,UACRtE,EAAWwE,cACdsF,EAAOtF,YAAc,gCAGlBxE,EAAWyE,cACdqF,EAAOrF,YAAc,iCAGnB,CAACzE,EAAW0E,YAAc1E,EAAW0E,WAAWuF,SAAW,KAC7DH,EAAOpF,WAAa,2CAMtB1E,EAAWqI,eACXrI,EAAWqI,gBAAkB,aAC7B,CAACrI,EAAWyH,kBAEZqC,EAAOrC,gBAAkB,qCAE3B,KACJ,CAGAoC,SAA8BrJ,IAE5B,MAAM0J,EAAY,CAAE,GAAG1J,GAGvB+C,cAAO4G,KAAKL,CAAM,EAAEM,QAAiBC,IACzBA,GAAG,EAAIP,EAAOO,CAAG,EAC5B,EAEMH,EACR,EAEM3G,OAAO4G,KAAKL,CAAM,EAAEG,SAAW,CACxC,EACA,CACF,GAOMzI,EAAuBY,cAAapC,GAAyC,CACjF,MAAM8J,EAA2B,GAGjC,OAAK9J,EAAWyF,OAAMqE,EAAOrE,KAAO,oBAC/BzF,EAAWsJ,SAAQQ,EAAOR,OAAS,sBACnCtJ,EAAWa,aAAYiJ,EAAOjJ,WAAa,2BAC3Cb,EAAWc,YAAWgJ,EAAOhJ,UAAY,0BACzCd,EAAWe,WAAU+I,EAAO/I,SAAW,wBAG5C8I,EAA+BrJ,KAC7B,GAAGA,EACH,GAAGsJ,CACH,IAEKvG,OAAO4G,KAAKL,CAAM,EAAEG,SAAW,CACxC,EAAG,CAAE,GAMCK,EAAkBlI,cAAamI,GAAsB,CACzDV,EAA8BrJ,IAC5B,MAAM0J,EAAY,CAAE,GAAG1J,GACvB,cAAO0J,EAAUK,CAAS,EACnBL,EACR,CACH,EAAG,CAAE,GAKCM,EAAiBpI,cAAY,IAAM,CACvCyH,EAAoB,CAAE,EACxB,EAAG,CAAE,GAEE,OACLD,mBACAnI,qBACAD,uBACA8I,kBACAE,iBACAX,sBAEJ,CC5JO,MAAMY,EAAqB,CAChC,CAAE/G,MAAO,UAAWgH,MAAO,SAAU,EACrC,CAAEhH,MAAO,SAAUgH,MAAO,QAAS,EACnC,CAAEhH,MAAO,WAAYgH,MAAO,UAAW,CAAC,EAa7BC,EAAiB,CAC5B,CAAEjH,MAAO,SAAUgH,MAAO,QAAS,EACnC,CAAEhH,MAAO,UAAWgH,MAAO,SAAU,EACrC,CAAEhH,MAAO,UAAWgH,MAAO,SAAU,EACrC,CAAEhH,MAAO,QAASgH,MAAO,OAAQ,EACjC,CAAEhH,MAAO,SAAUgH,MAAO,QAAS,EACnC,CAAEhH,MAAO,QAASgH,MAAO,OAAQ,CAAC,EAWGE,MAAMC,KAAK,CAAEZ,OAAQ,EAAG,EAAG,CAACa,EAAGC,KAAO,CAC3ErH,MAAOsH,OAAOD,EAAI,CAAC,EACnBL,MAAOM,OAAOD,EAAI,CAAC,CACrB,EAAE,EAMWE,QAAgBhC,GAAqB,CAIxC1D,YAAI,gDAAgD0D,IAAU,EAGtE,MAAMiC,EAAcC,OAAOC,SAASC,KAAKC,UAAU,CAAC,EAC5C/F,YAAI,sCAAsC2F,GAAa,EAG/D,IAAIK,EAAmBtC,EACvB,GAAI,CAACsC,GAAoBL,EAAYM,SAAS,cAAc,EAAG,CAEvDC,QAAUP,EAAYQ,MAAM,yBAAyB,EACvDD,GAAWA,EAAQ,CAAC,IACtBF,EAAmBE,EAAQ,CAAC,EACpBlG,YAAI,gCAAgCgG,GAAkB,GAKlE,MAAMI,EAAaJ,IAAqB,OAASL,EAAYM,SAAS,YAAY,EAC5EnK,EACHkK,GAAoBA,IAAqB,OACzCL,EAAYM,SAAS,cAAc,GAAK,CAACN,EAAYM,SAAS,iBAAiB,EAE1EjG,YAAI,8BAA8BoG,kBAA2BtK,GAAY,EAGjF,KAAM,CAACrB,EAAYC,CAAa,EAAIiC,WAAc,CAAE,GAC9C,CAAC0J,CAAS,EAAI1J,WAAS,EAAK,EAC5B,CAACwH,EAAO9H,CAAQ,EAAIM,WAAwB,IAAI,EAChD,CAAC2J,EAAShK,CAAU,EAAIK,WAAwB,IAAI,EACpDX,EAAY,KAEZuK,EAAe1J,cAClBC,GAAqF,CAC9E,MAAE0J,OAAMrI,SAAUrB,EAAE2J,OAC1B/L,EAAeO,IAAe,CAC5B,GAAGA,EACH,CAACuL,CAAI,EAAGrI,CACR,GACJ,EACA,CACF,GAGM,CAAEkG,mBAAkBpI,wBAAyBmI,EAAmB,EAGhE,CAAE/I,uBAAwBb,EAAqBC,EAAYC,CAAa,EAGxEgM,EAAoBA,IAAMzK,EAAqBxB,CAAU,EAGzD,CAAEmC,eAAcH,gBAAiBZ,EACrCpB,EACAqB,EACAsK,EACApK,EACA0K,EACA,KACA,KACA,KACArK,EACAC,GAGK,OACL7B,aACAC,gBACA6L,eACA3J,eACAH,eACA4J,YACAlC,QACAmC,UACAjC,mBACA+B,aACA/K,sBAEJ,EClIasL,EAAmB,CAC9B,CAAExI,MAAO,QAASgH,MAAO,iDAAkD,EAC3E,CAAEhH,MAAO,MAAOgH,MAAO,iDAAkD,EACzE,CAAEhH,MAAO,WAAYgH,MAAO,+DAAgE,EAC5F,CAAEhH,MAAO,YAAagH,MAAO,oDAAqD,CAAC,EAMxEyB,EAAuB,CAClC,CAAEzI,MAAO,SAAUgH,MAAO,sDAAuD,EACjF,CAAEhH,MAAO,WAAYgH,MAAO,0DAA2D,EACvF,CAAEhH,MAAO,OAAQgH,MAAO,+CAAgD,CAAC,EAM9D0B,EAAuB,CAClC,CACE1I,MAAO,qBACPgH,MAAO,2DACT,EACA,CACEhH,MAAO,mBACPgH,MAAO,4DACT,EACA,CAAEhH,MAAO,gBAAiBgH,MAAO,iDAAkD,EACnF,CAAEhH,MAAO,eAAgBgH,MAAO,sDAAuD,CAAC,EAM7E2B,EAAsB,CACjC,CAAE3I,MAAO,mBAAoBgH,MAAO,kBAAmB,EACvD,CAAEhH,MAAO,kBAAmBgH,MAAO,iBAAkB,EACrD,CAAEhH,MAAO,OAAQgH,MAAO,gCAAiC,EACzD,CAAEhH,MAAO,UAAWgH,MAAO,qBAAsB,EACjD,CAAEhH,MAAO,wBAAyBgH,MAAO,uBAAwB,EACjE,CAAEhH,MAAO,yBAA0BgH,MAAO,wBAAyB,EACnE,CAAEhH,MAAO,0BAA2BgH,MAAO,yBAA0B,EACrE,CAAEhH,MAAO,eAAgBgH,MAAO,6BAA8B,EAC9D,CAAEhH,MAAO,kBAAmBgH,MAAO,sCAAuC,EAC1E,CAAEhH,MAAO,aAAcgH,MAAO,YAAa,EAC3C,CAAEhH,MAAO,QAASgH,MAAO,OAAQ,CAAC,EAMKE,MAAMC,KAAK,CAAEZ,OAAQ,EAAG,EAAG,CAACa,EAAGC,KAAO,CAC7ErH,MAAOsH,OAAOD,EAAI,CAAC,EACnBL,MAAOM,OAAOD,EAAI,CAAC,CACrB,EAAE,EAKK,MAAMuB,EAAgC,CAC3CC,MACE,iGACFC,IAAK,iGACLC,SACE,+FACFC,UACE,4FACJ,EAKaC,EAAkC,CAC7CC,OAAQ,0FACRC,SAAU,uFACVC,KAAM,mFACR,EAKaC,EACX,gJAMWC,GACX,wJA8BWC,GAA4BjJ,GACnCA,GAAU,EAAU,uBACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,UACjB,qBAMIkJ,GAAkClJ,GACzCA,GAAU,EAAU,cACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,YACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,UACpBA,GAAU,EAAU,gBACpBA,GAAU,EAAU,OACpBA,GAAU,EAAU,YACjB", "names": ["useTradeCalculations", "formValues", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "riskPoints", "profit", "parseFloat", "rMultiple", "toFixed", "prev", "entryTime", "exitTime", "rdTime", "calculateProfitLoss", "entryPrice", "exitPrice", "quantity", "isNaN", "profitLoss", "direction", "result", "useTradeSubmission", "isEditMode", "_isNewTrade", "tradeData", "validateBasicInfoTab", "validateCurrentTab", "activeTab", "setActiveTab", "setError", "setSuccess", "navigate", "useNavigate", "isSubmitting", "setIsSubmitting", "useState", "handleSubmit", "useCallback", "e", "preventDefault", "patternQualityData", "clarity", "patternQualityClarity", "confluence", "patternQualityConfluence", "context", "patternQualityContext", "risk", "patternQualityRisk", "reward", "patternQualityReward", "timeframe", "patternQualityTimeframe", "volume", "patternQualityVolume", "allCriteriaFilled", "Object", "values", "every", "value", "submission<PERSON><PERSON>ues", "calculateTotalScore", "convertScoreToRating", "__vitePreload", "totalScore", "rating", "patternQuality", "total", "criteria", "notes", "patternQualityNotes", "dolType", "dolAnalysis", "dol<PERSON><PERSON><PERSON><PERSON>", "dolReaction", "dolContext", "priceAction", "dolPriceAction", "volumeProfile", "dolVolumeProfile", "timeOfDay", "dolTimeOfDay", "marketStructure", "dolMarketStructure", "effectiveness", "dolEffectiveness", "parseInt", "dolNotes", "log", "tradeRecord", "date", "model_type", "modelType", "session", "market", "entry_price", "exit_price", "r_multiple", "undefined", "achieved_pl", "win_loss", "pattern_quality_rating", "entry_time", "exit_time", "rd_time", "risk_points", "no_of_contracts", "setup_constant", "setupComponents", "constant", "setup_action", "action", "setup_variable", "variable", "setup_entry", "entry", "fvgDetails", "entryVersion", "trade_id", "rd_type", "entry_version", "draw_on_liquidity", "specificDOLType", "setupData", "primarySetupType", "liquidityTaken", "primary_setup", "secondary_setup", "secondarySetupType", "liquidity_taken", "additional_fvgs", "additionalFVGs", "join", "dol", "dolTargetType", "analysisData", "dol_target_type", "path_quality", "clustering", "dol_notes", "completeTradeData", "trade", "fvg_details", "setup", "analysis", "id", "tradeId", "tradeStorageService", "updateTradeWithDetails", "savedTradeId", "saveTradeWithDetails", "symbol", "setTimeout", "console", "err", "error", "useTradeValidation", "validationErrors", "setValidationErrors", "errors", "primarySetupCategory", "secondarySetupCategory", "length", "newErrors", "keys", "for<PERSON>ach", "key", "clearFieldError", "fieldName", "clearAllErrors", "MODEL_TYPE_OPTIONS", "label", "MARKET_OPTIONS", "Array", "from", "_", "i", "String", "useTradeForm", "currentPath", "window", "location", "hash", "substring", "effectiveTradeId", "includes", "matches", "match", "isNewTrade", "isLoading", "success", "handleChange", "name", "target", "validateBasicInfo", "DOL_TYPE_OPTIONS", "DOL_STRENGTH_OPTIONS", "DOL_REACTION_OPTIONS", "DOL_CONTEXT_OPTIONS", "DOL_PRICE_ACTION_DESCRIPTIONS", "Sweep", "Tap", "Approach", "Rejection", "DOL_VOLUME_PROFILE_DESCRIPTIONS", "Strong", "Moderate", "Weak", "DOL_TIME_OF_DAY_DESCRIPTION", "DOL_MARKET_STRUCTURE_DESCRIPTION", "getDOLEffectivenessColor", "getDOLEffectivenessDescription"], "sources": ["../../src/features/trade-journal/hooks/useTradeCalculations.ts", "../../src/features/trade-journal/hooks/useTradeSubmission.ts", "../../src/features/trade-journal/hooks/useTradeValidation.ts", "../../src/features/trade-journal/hooks/useTradeForm.ts", "../../src/features/trade-journal/constants/dolAnalysis.ts"], "sourcesContent": ["/**\n * Trade Calculations Hook\n *\n * Custom hook for calculating trade metrics\n */\n\nimport { useEffect } from 'react';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeFormValues } from '../types';\n\n/**\n * Hook for calculating trade metrics\n * @param formValues The form values to calculate metrics for\n * @param setFormValues Function to update form values\n */\nexport function useTradeCalculations(\n  formValues: TradeFormValues,\n  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>\n) {\n  // Calculate R-Multiple when risk points and profit change\n  useEffect(() => {\n    if (formValues.riskPoints && formValues.profit) {\n      const riskPoints = parseFloat(formValues.riskPoints);\n      const profit = parseFloat(formValues.profit);\n\n      if (riskPoints > 0) {\n        const rMultiple = (profit / riskPoints).toFixed(2);\n        setFormValues((prev) => ({\n          ...prev,\n          rMultiple,\n        }));\n      }\n    }\n  }, [formValues.riskPoints, formValues.profit, setFormValues]);\n\n  // Validate time relationships\n  useEffect(() => {\n    // This effect doesn't update form values directly,\n    // but it's related to calculations and validation of time relationships\n    // The actual validation errors are handled in useTradeValidation\n  }, [formValues.entryTime, formValues.exitTime, formValues.rdTime]);\n\n  /**\n   * Calculate profit/loss based on entry price, exit price, and quantity\n   * This function can be called when those values change\n   */\n  const calculateProfitLoss = () => {\n    if (formValues.entryPrice && formValues.exitPrice && formValues.quantity) {\n      const entryPrice = parseFloat(formValues.entryPrice);\n      const exitPrice = parseFloat(formValues.exitPrice);\n      const quantity = parseFloat(formValues.quantity);\n\n      if (!isNaN(entryPrice) && !isNaN(exitPrice) && !isNaN(quantity)) {\n        let profitLoss: number;\n\n        if (formValues.direction === 'long') {\n          profitLoss = (exitPrice - entryPrice) * quantity;\n        } else {\n          profitLoss = (entryPrice - exitPrice) * quantity;\n        }\n\n        setFormValues((prev) => ({\n          ...prev,\n          profit: profitLoss.toFixed(2),\n          result: profitLoss > 0 ? 'win' : profitLoss < 0 ? 'loss' : 'breakeven',\n        }));\n      }\n    }\n  };\n\n  return {\n    calculateProfitLoss,\n  };\n}\n\nexport type TradeCalculationsHook = ReturnType<typeof useTradeCalculations>;\n", "/**\n * Trade Submission Hook\n *\n * Custom hook for handling trade form submission\n */\n\nimport { useState, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { TradeFormValues } from '../types'; // These now import from centralized types\nimport { tradeStorageService, Trade } from '@adhd-trading-dashboard/shared';\n\n/**\n * Hook for handling trade form submission\n * @param formValues The form values to submit\n * @param isEditMode Whether the form is in edit mode\n * @param isNewTrade Whether the form is for a new trade\n * @param tradeData The existing trade data (if editing)\n * @param validateBasicInfoTab Function to validate the basic info tab\n * @param validateCurrentTab Function to validate the current tab (optional for single-page form)\n * @param activeTab The active tab (optional for single-page form)\n * @param setActiveTab Function to set the active tab (optional for single-page form)\n * @param setError Function to set the error message\n * @param setSuccess Function to set the success message\n */\nexport function useTradeSubmission(\n  formValues: TradeFormValues,\n  isEditMode: boolean,\n  _isNewTrade: boolean, // Renamed to indicate unused parameter\n  tradeData: Trade | null,\n  validateBasicInfoTab: () => boolean,\n  validateCurrentTab: (() => boolean) | null,\n  activeTab: string | null,\n  setActiveTab: ((tab: string) => void) | null,\n  setError: (error: string | null) => void,\n  setSuccess: (success: string | null) => void\n) {\n  const navigate = useNavigate();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  /**\n   * Handle form submission\n   * @param e The form event\n   */\n  const handleSubmit = useCallback(\n    async (e: React.FormEvent) => {\n      e.preventDefault();\n\n      // Only validate the Basic Info tab for submission\n      if (!validateBasicInfoTab()) {\n        setError('Please complete the required fields in the Basic Info section.');\n        // For single-page form, we don't need to switch tabs\n        if (setActiveTab) {\n          setActiveTab('basic'); // Switch to basic tab to show errors (if tabs are used)\n        }\n        return;\n      }\n\n      // Validate current tab if it's not the basic tab (only for tabbed interface)\n      if (activeTab && activeTab !== 'basic' && validateCurrentTab && !validateCurrentTab()) {\n        setError('Please fix the validation errors in the current section before submitting.');\n        return;\n      }\n\n      setIsSubmitting(true);\n      setError(null);\n      setSuccess(null);\n\n      try {\n        // Prepare pattern quality score data if all criteria are filled\n        const patternQualityData = {\n          clarity: formValues.patternQualityClarity || '',\n          confluence: formValues.patternQualityConfluence || '',\n          context: formValues.patternQualityContext || '',\n          risk: formValues.patternQualityRisk || '',\n          reward: formValues.patternQualityReward || '',\n          timeframe: formValues.patternQualityTimeframe || '',\n          volume: formValues.patternQualityVolume || '',\n        };\n\n        // Check if all pattern quality criteria are filled\n        const allCriteriaFilled = Object.values(patternQualityData).every(value => value !== '');\n\n        // Create a copy of form values for submission\n        const submissionValues = { ...formValues };\n\n        // Add pattern quality score data if all criteria are filled\n        if (allCriteriaFilled) {\n          // Import calculation functions\n          const { calculateTotalScore, convertScoreToRating } = await import(\n            '../constants/patternQuality'\n          );\n\n          // Calculate total score and rating\n          const totalScore = calculateTotalScore(patternQualityData as any);\n          const rating = convertScoreToRating(totalScore);\n\n          // Add pattern quality score data to submission values\n          (submissionValues as any).patternQuality = {\n            total: totalScore,\n            rating,\n            criteria: patternQualityData,\n            notes: formValues.patternQualityNotes || '',\n          };\n        }\n\n        // Add DOL analysis data if type is selected\n        if (formValues.dolType) {\n          // Add DOL analysis data to submission values\n          (submissionValues as any).dolAnalysis = {\n            dolType: formValues.dolType,\n            dolStrength: formValues.dolStrength || '',\n            dolReaction: formValues.dolReaction || '',\n            dolContext: formValues.dolContext || [],\n            priceAction: formValues.dolPriceAction || '',\n            volumeProfile: formValues.dolVolumeProfile || '',\n            timeOfDay: formValues.dolTimeOfDay || '',\n            marketStructure: formValues.dolMarketStructure || '',\n            effectiveness: formValues.dolEffectiveness ? parseInt(formValues.dolEffectiveness) : 5,\n            notes: formValues.dolNotes || '',\n          };\n        }\n\n        console.log('Form submitted:', submissionValues);\n\n        // Prepare trade data object using the new schema format\n        const tradeRecord = {\n          date: formValues.date,\n          model_type: formValues.modelType || 'Unknown',\n          session: formValues.session,\n          direction: (formValues.direction === 'long' ? 'Long' : 'Short') as 'Long' | 'Short',\n          market: formValues.market,\n          entry_price: parseFloat(formValues.entryPrice) || 0,\n          exit_price: parseFloat(formValues.exitPrice) || 0,\n          r_multiple: formValues.rMultiple ? parseFloat(formValues.rMultiple) : undefined,\n          achieved_pl: parseFloat(formValues.profit) || 0,\n          win_loss: (formValues.result === 'win'\n            ? 'Win'\n            : formValues.result === 'loss'\n            ? 'Loss'\n            : undefined) as 'Win' | 'Loss' | undefined,\n          pattern_quality_rating: formValues.patternQuality\n            ? parseFloat(formValues.patternQuality)\n            : undefined,\n          entry_time: formValues.entryTime,\n          exit_time: formValues.exitTime,\n          rd_time: formValues.rdTime,\n          risk_points: formValues.riskPoints ? parseFloat(formValues.riskPoints) : undefined,\n          no_of_contracts: parseInt(formValues.quantity) || 0,\n          notes: formValues.notes,\n          // Setup Components (modular setup construction)\n          setup_constant: formValues.setupComponents?.constant,\n          setup_action: formValues.setupComponents?.action,\n          setup_variable: formValues.setupComponents?.variable,\n          setup_entry: formValues.setupComponents?.entry,\n          setupComponents: formValues.setupComponents,\n        };\n\n        // Prepare FVG details if available\n        const fvgDetails = formValues.entryVersion\n          ? {\n              trade_id: 0, // Will be set by the service\n              rd_type: formValues.modelType,\n              entry_version: formValues.entryVersion,\n              draw_on_liquidity: formValues.specificDOLType,\n            }\n          : undefined;\n\n        // Prepare setup classification if available\n        const setupData =\n          formValues.primarySetupType || formValues.liquidityTaken\n            ? {\n                trade_id: 0, // Will be set by the service\n                primary_setup: formValues.primarySetupType,\n                secondary_setup: formValues.secondarySetupType,\n                liquidity_taken: formValues.liquidityTaken,\n                additional_fvgs: formValues.additionalFVGs?.join(', '),\n                dol: formValues.dolTargetType,\n              }\n            : undefined;\n\n        // Prepare analysis data if available\n        const analysisData =\n          formValues.dolType || formValues.dolNotes\n            ? {\n                trade_id: 0, // Will be set by the service\n                dol_target_type: formValues.dolTargetType,\n                path_quality: formValues.dolPriceAction,\n                clustering: formValues.dolVolumeProfile,\n                dol_notes: formValues.dolNotes,\n              }\n            : undefined;\n\n        const completeTradeData = {\n          trade: tradeRecord,\n          fvg_details: fvgDetails,\n          setup: setupData,\n          analysis: analysisData,\n        };\n\n        // Determine if we're in edit mode by checking if tradeData has an ID\n        const isEditingExistingTrade = isEditMode && tradeData && tradeData.id;\n\n        if (isEditingExistingTrade) {\n          // Update existing trade\n          const tradeId = typeof tradeData.id === 'string' ? parseInt(tradeData.id) : tradeData.id;\n          await tradeStorageService.updateTradeWithDetails(tradeId, completeTradeData);\n          console.log('Trade updated in IndexedDB successfully', tradeId);\n        } else {\n          // Create new trade\n          const savedTradeId = await tradeStorageService.saveTradeWithDetails(completeTradeData);\n          console.log('New trade saved to IndexedDB successfully', savedTradeId);\n        }\n\n        // Set success message based on whether we're editing or creating\n        if (isEditMode) {\n          setSuccess(`Trade for ${formValues.symbol} on ${formValues.date} updated successfully!`);\n        } else {\n          setSuccess(\n            `New trade for ${formValues.symbol} on ${formValues.date} created successfully!`\n          );\n        }\n\n        // Wait a moment to show the success message before navigating\n        setTimeout(() => {\n          // Navigate back to journal page after successful submission\n          console.log('Navigating back to journal page after successful submission');\n          navigate('/journal');\n        }, 1500);\n      } catch (err) {\n        setError('Failed to save trade. Please try again.');\n        console.error('Error submitting form:', err);\n      } finally {\n        setIsSubmitting(false);\n      }\n    },\n    [\n      formValues,\n      validateBasicInfoTab,\n      validateCurrentTab,\n      activeTab,\n      setActiveTab,\n      setError,\n      setSuccess,\n      isEditMode,\n      tradeData,\n      navigate,\n    ]\n  );\n\n  return {\n    handleSubmit,\n    isSubmitting,\n  };\n}\n\nexport type TradeSubmissionHook = ReturnType<typeof useTradeSubmission>;\n", "/**\n * Trade Validation Hook\n *\n * Custom hook for validating trade form data\n */\n\nimport { useState, useCallback } from 'react';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeFormValues } from '../types';\n\nexport interface ValidationErrors {\n  [key: string]: string;\n}\n\n/**\n * Hook for validating trade form data\n */\nexport function useTradeValidation() {\n  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});\n\n  /**\n   * Validate only the current tab\n   * @param formValues The form values to validate\n   * @param activeTab The active tab to validate\n   * @returns Whether the validation passed\n   */\n  const validateCurrentTab = useCallback(\n    (formValues: TradeFormValues, activeTab: string): boolean => {\n      const errors: ValidationErrors = {};\n\n      // Validate based on active tab\n      switch (activeTab) {\n        case 'basic':\n          // Basic Info tab - required fields\n          if (!formValues.date) errors.date = 'Date is required';\n          if (!formValues.symbol) errors.symbol = 'Symbol is required';\n          if (!formValues.entryPrice) errors.entryPrice = 'Entry price is required';\n          if (!formValues.exitPrice) errors.exitPrice = 'Exit price is required';\n          if (!formValues.quantity) errors.quantity = 'Quantity is required';\n          break;\n\n        case 'timing':\n          // Timing tab - validate only if fields are filled\n          if (formValues.entryTime && formValues.exitTime) {\n            if (formValues.exitTime < formValues.entryTime) {\n              errors.exitTime = 'Exit time must be after entry time';\n            }\n          }\n\n          if (formValues.rdTime && formValues.entryTime) {\n            if (formValues.entryTime < formValues.rdTime) {\n              errors.entryTime = 'Entry time must be after RD formation time';\n            }\n          }\n          break;\n\n        case 'strategy':\n          // Strategy tab - validate only if fields are filled\n          if (formValues.primarySetupCategory && !formValues.primarySetupType) {\n            errors.primarySetupType = 'Please select a primary setup type';\n          }\n\n          if (formValues.secondarySetupCategory && !formValues.secondarySetupType) {\n            errors.secondarySetupType = 'Please select a secondary setup type';\n          }\n\n          // Validate primary and secondary setup types are different\n          if (\n            formValues.primarySetupType &&\n            formValues.secondarySetupType &&\n            formValues.primarySetupType === formValues.secondarySetupType\n          ) {\n            errors.secondarySetupType = 'Primary and secondary setup types must be different';\n          }\n          break;\n\n        case 'dol-analysis':\n          // DOL Analysis tab - validate only if fields are filled\n          if (formValues.dolType) {\n            if (!formValues.dolStrength) {\n              errors.dolStrength = 'Please select a DOL strength';\n            }\n\n            if (!formValues.dolReaction) {\n              errors.dolReaction = 'Please select a DOL reaction';\n            }\n\n            if (!formValues.dolContext || formValues.dolContext.length === 0) {\n              errors.dolContext = 'Please select at least one DOL context';\n            }\n          }\n\n          // Validate DOL target type and specific type\n          if (\n            formValues.dolTargetType &&\n            formValues.dolTargetType !== 'RD Target' &&\n            !formValues.specificDOLType\n          ) {\n            errors.specificDOLType = 'Please select a specific DOL type';\n          }\n          break;\n      }\n\n      // Update validation errors for current tab only\n      setValidationErrors((prev) => {\n        // Remove any previous errors for fields in the current tab\n        const newErrors = { ...prev };\n\n        // Add new errors for the current tab\n        Object.keys(errors).forEach((key) => {\n          newErrors[key] = errors[key];\n        });\n\n        return newErrors;\n      });\n\n      return Object.keys(errors).length === 0;\n    },\n    []\n  );\n\n  /**\n   * Validate only the Basic Info tab (required for submission)\n   * @param formValues The form values to validate\n   * @returns Whether the validation passed\n   */\n  const validateBasicInfoTab = useCallback((formValues: TradeFormValues): boolean => {\n    const errors: ValidationErrors = {};\n\n    // Required fields validation\n    if (!formValues.date) errors.date = 'Date is required';\n    if (!formValues.symbol) errors.symbol = 'Symbol is required';\n    if (!formValues.entryPrice) errors.entryPrice = 'Entry price is required';\n    if (!formValues.exitPrice) errors.exitPrice = 'Exit price is required';\n    if (!formValues.quantity) errors.quantity = 'Quantity is required';\n\n    // Update validation errors for basic info fields\n    setValidationErrors((prev) => ({\n      ...prev,\n      ...errors,\n    }));\n\n    return Object.keys(errors).length === 0;\n  }, []);\n\n  /**\n   * Clear validation errors for a specific field\n   * @param fieldName The field name to clear errors for\n   */\n  const clearFieldError = useCallback((fieldName: string) => {\n    setValidationErrors((prev) => {\n      const newErrors = { ...prev };\n      delete newErrors[fieldName];\n      return newErrors;\n    });\n  }, []);\n\n  /**\n   * Clear all validation errors\n   */\n  const clearAllErrors = useCallback(() => {\n    setValidationErrors({});\n  }, []);\n\n  return {\n    validationErrors,\n    validateCurrentTab,\n    validateBasicInfoTab,\n    clearFieldError,\n    clearAllErrors,\n    setValidationErrors,\n  };\n}\n\nexport type TradeValidationHook = ReturnType<typeof useTradeValidation>;\n", "/**\n * Trade Form Hook\n *\n * Custom hook for managing trade form state and logic\n */\n\nimport React, { useCallback, useState } from 'react';\n// Removed unused imports - will be added back when needed for real data integration\n// import { useNavigate } from 'react-router-dom'; // Removed as unused\n// Removed unused imports - will be added back when needed for real data integration\n// import { useTradeFormData } from './useTradeFormData'; // File removed\nimport { useTradeCalculations } from './useTradeCalculations';\nimport { useTradeSubmission } from './useTradeSubmission';\nimport { useTradeValidation } from './useTradeValidation';\n\n// Options for dropdowns - Using actual trading models (not AI-generated generic ones)\nexport const MODEL_TYPE_OPTIONS = [\n  { value: 'RD-Cont', label: 'RD-Cont' },\n  { value: 'FVG-RD', label: 'FVG-RD' },\n  { value: 'Combined', label: 'Combined' },\n];\n\nexport const SESSION_OPTIONS = [\n  { value: 'Pre-Market', label: 'Pre-Market' },\n  { value: 'Regular Hours', label: 'Regular Hours' },\n  { value: 'Power Hour', label: 'Power Hour' },\n  { value: 'After Hours', label: 'After Hours' },\n  { value: 'Overnight', label: 'Overnight' },\n];\n\n// SETUP_OPTIONS removed - using Setup Construction Matrix instead for modular setup building\n\nexport const MARKET_OPTIONS = [\n  { value: 'Stocks', label: 'Stocks' },\n  { value: 'Options', label: 'Options' },\n  { value: 'Futures', label: 'Futures' },\n  { value: 'Forex', label: 'Forex' },\n  { value: 'Crypto', label: 'Crypto' },\n  { value: 'Other', label: 'Other' },\n];\n\nexport const ENTRY_VERSION_OPTIONS = [\n  { value: 'First Entry', label: 'First Entry' },\n  { value: 'Re-Entry', label: 'Re-Entry' },\n  { value: 'Scale In', label: 'Scale In' },\n  { value: 'Averaging Down', label: 'Averaging Down' },\n  { value: 'Averaging Up', label: 'Averaging Up' },\n];\n\nexport const PATTERN_QUALITY_OPTIONS = Array.from({ length: 10 }, (_, i) => ({\n  value: String(i + 1),\n  label: String(i + 1),\n}));\n\n/**\n * Main hook for managing trade form state and logic\n * @param tradeId The ID of the trade to load (optional)\n */\nexport const useTradeForm = (tradeId?: string) => {\n  // const navigate = useNavigate(); // Removed as unused\n\n  // Enhanced debugging for tradeId parameter and hash-based routing\n  console.log(`useTradeForm hook initialized with tradeId: \"${tradeId}\"`);\n\n  // For hash-based routing, check the hash part of the URL\n  const currentPath = window.location.hash.substring(1); // Remove the leading #\n  console.log(`Current hash path in useTradeForm: ${currentPath}`);\n\n  // Extract trade ID from URL if not provided directly\n  let effectiveTradeId = tradeId;\n  if (!effectiveTradeId && currentPath.includes('/trade/edit/')) {\n    // Extract ID from URL path for edit mode\n    const matches = currentPath.match(/\\/trade\\/edit\\/([^\\/]+)/);\n    if (matches && matches[1]) {\n      effectiveTradeId = matches[1];\n      console.log(`Extracted trade ID from URL: ${effectiveTradeId}`);\n    }\n  }\n\n  // Determine if we're creating a new trade or editing an existing one\n  const isNewTrade = effectiveTradeId === 'new' || currentPath.includes('/trade/new');\n  const isEditMode =\n    (effectiveTradeId && effectiveTradeId !== 'new') ||\n    (currentPath.includes('/trade/edit/') && !currentPath.includes('/trade/edit/new'));\n\n  console.log(`useTradeForm - isNewTrade: ${isNewTrade}, isEditMode: ${isEditMode}`);\n\n  // Mock form state (replacing removed useTradeFormData)\n  const [formValues, setFormValues] = useState<any>({});\n  const [isLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const tradeData = null; // Mock trade data\n\n  const handleChange = useCallback(\n    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\n      const { name, value } = e.target;\n      setFormValues((prev: any) => ({\n        ...prev,\n        [name]: value,\n      }));\n    },\n    []\n  );\n\n  // Use the validation hook to validate form data\n  const { validationErrors, validateBasicInfoTab } = useTradeValidation();\n\n  // Use the calculations hook to calculate trade metrics\n  const { calculateProfitLoss } = useTradeCalculations(formValues, setFormValues);\n\n  // Wrapper function for validateBasicInfoTab to pass the current form values\n  const validateBasicInfo = () => validateBasicInfoTab(formValues);\n\n  // Use the submission hook to handle form submission\n  const { handleSubmit, isSubmitting } = useTradeSubmission(\n    formValues,\n    isEditMode,\n    isNewTrade,\n    tradeData as any, // Type assertion for interface compatibility\n    validateBasicInfo,\n    null, // No current tab validation needed\n    null, // No active tab\n    null, // No setActiveTab\n    setError,\n    setSuccess\n  );\n\n  return {\n    formValues,\n    setFormValues,\n    handleChange,\n    handleSubmit,\n    isSubmitting,\n    isLoading,\n    error,\n    success,\n    validationErrors,\n    isNewTrade,\n    calculateProfitLoss,\n  };\n};\n", "/**\n * DOL (Draw on Liquidity) Analysis Constants\n *\n * Constants for the DOL analysis feature\n */\n\n// Removed unused imports - will be added back when needed for real data integration\n\n/**\n * DOL Type Options\n */\nexport const DOL_TYPE_OPTIONS = [\n  { value: 'Sweep', label: 'Sweep - Price moves through the liquidity level' },\n  { value: 'Tap', label: 'Tap - Price touches the liquidity level exactly' },\n  { value: 'Approach', label: \"Approach - Price approaches but doesn't quite reach the level\" },\n  { value: 'Rejection', label: 'Rejection - Price rejects from the liquidity level' },\n];\n\n/**\n * DOL Strength Options\n */\nexport const DOL_STRENGTH_OPTIONS = [\n  { value: 'Strong', label: 'Strong - Significant price movement with high volume' },\n  { value: 'Moderate', label: 'Moderate - Noticeable price movement with average volume' },\n  { value: 'Weak', label: 'Weak - Minimal price movement with low volume' },\n];\n\n/**\n * DOL Reaction Options\n */\nexport const DOL_REACTION_OPTIONS = [\n  {\n    value: 'Immediate Reversal',\n    label: 'Immediate Reversal - Price reverses direction immediately',\n  },\n  {\n    value: 'Delayed Reversal',\n    label: 'Delayed Reversal - Price reverses after some consolidation',\n  },\n  { value: 'Consolidation', label: 'Consolidation - Price consolidates at the level' },\n  { value: 'Continuation', label: 'Continuation - Price continues in the same direction' },\n];\n\n/**\n * DOL Context Options\n */\nexport const DOL_CONTEXT_OPTIONS = [\n  { value: 'High Volume Node', label: 'High Volume Node' },\n  { value: 'Low Volume Node', label: 'Low Volume Node' },\n  { value: 'VPOC', label: 'Volume Point of Control (VPOC)' },\n  { value: 'VAH/VAL', label: 'Value Area High/Low' },\n  { value: 'Previous Day High/Low', label: 'Previous Day High/Low' },\n  { value: 'Previous Week High/Low', label: 'Previous Week High/Low' },\n  { value: 'Previous Month High/Low', label: 'Previous Month High/Low' },\n  { value: 'Round Number', label: 'Round Number (00, 50, etc.)' },\n  { value: 'Technical Level', label: 'Technical Level (Support/Resistance)' },\n  { value: 'News Event', label: 'News Event' },\n  { value: 'Other', label: 'Other' },\n];\n\n/**\n * DOL Effectiveness Rating Options\n */\nexport const DOL_EFFECTIVENESS_OPTIONS = Array.from({ length: 10 }, (_, i) => ({\n  value: String(i + 1),\n  label: String(i + 1),\n}));\n\n/**\n * DOL Price Action Descriptions\n */\nexport const DOL_PRICE_ACTION_DESCRIPTIONS = {\n  Sweep:\n    'Describe how price moved through the liquidity level. Was it a clean sweep or did it struggle?',\n  Tap: 'Describe how price interacted with the liquidity level. Was it a precise tap or did it linger?',\n  Approach:\n    'Describe how price approached the liquidity level. How close did it get and why did it stop?',\n  Rejection:\n    'Describe how price rejected from the liquidity level. Was it a sharp rejection or gradual?',\n};\n\n/**\n * DOL Volume Profile Descriptions\n */\nexport const DOL_VOLUME_PROFILE_DESCRIPTIONS = {\n  Strong: 'Describe the volume profile during the liquidity interaction. Was there a volume spike?',\n  Moderate: 'Describe the volume profile during the liquidity interaction. Was volume consistent?',\n  Weak: 'Describe the volume profile during the liquidity interaction. Why was volume low?',\n};\n\n/**\n * DOL Time of Day Significance\n */\nexport const DOL_TIME_OF_DAY_DESCRIPTION =\n  'Describe the significance of the time of day for this liquidity interaction. ' +\n  'Was it during a key market session or near a session transition?';\n\n/**\n * DOL Market Structure Description\n */\nexport const DOL_MARKET_STRUCTURE_DESCRIPTION =\n  'Describe the market structure context for this liquidity interaction. ' +\n  'Was price in an uptrend, downtrend, or range? Were there any key levels nearby?';\n\n/**\n * Get description for DOL type\n */\nexport const getDOLTypeDescription = (dolType: string): string => {\n  const option = DOL_TYPE_OPTIONS.find((option) => option.value === dolType);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get description for DOL strength\n */\nexport const getDOLStrengthDescription = (dolStrength: string): string => {\n  const option = DOL_STRENGTH_OPTIONS.find((option) => option.value === dolStrength);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get description for DOL reaction\n */\nexport const getDOLReactionDescription = (dolReaction: string): string => {\n  const option = DOL_REACTION_OPTIONS.find((option) => option.value === dolReaction);\n  return option ? option.label.split(' - ')[1] : '';\n};\n\n/**\n * Get color for DOL effectiveness rating\n */\nexport const getDOLEffectivenessColor = (rating: number): string => {\n  if (rating >= 8) return 'var(--success-color)'; // Green\n  if (rating >= 6) return '#8BC34A'; // Light Green\n  if (rating >= 5) return '#FFC107'; // Amber\n  if (rating >= 3) return '#FF9800'; // Orange\n  return 'var(--error-color)'; // Red\n};\n\n/**\n * Get description for DOL effectiveness rating\n */\nexport const getDOLEffectivenessDescription = (rating: number): string => {\n  if (rating >= 9) return 'Exceptional';\n  if (rating >= 8) return 'Excellent';\n  if (rating >= 7) return 'Very Good';\n  if (rating >= 6) return 'Good';\n  if (rating >= 5) return 'Average';\n  if (rating >= 4) return 'Below Average';\n  if (rating >= 3) return 'Poor';\n  if (rating >= 2) return 'Very Poor';\n  return 'Ineffective';\n};\n"], "file": "assets/dolAnalysis-cc48a373.js"}