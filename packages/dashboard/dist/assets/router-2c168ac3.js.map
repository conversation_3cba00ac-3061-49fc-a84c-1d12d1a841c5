{"version": 3, "file": "router-2c168ac3.js", "sources": ["../../../../node_modules/@remix-run/router/dist/router.js", "../../../../node_modules/react-router/dist/index.js", "../../../../node_modules/react-router-dom/dist/index.js"], "sourcesContent": ["/**\n * @remix-run/router v1.2.1\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nvar Action;\n\n(function (Action) {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Action[\"Pop\"] = \"POP\";\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n\n  Action[\"Push\"] = \"PUSH\";\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n\n  Action[\"Replace\"] = \"REPLACE\";\n})(Action || (Action = {}));\n\nconst PopStateEventType = \"popstate\";\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\n\nfunction createMemoryHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  let {\n    initialEntries = [\"/\"],\n    initialIndex,\n    v5Compat = false\n  } = options;\n  let entries; // Declare so we can access from createMemoryLocation\n\n  entries = initialEntries.map((entry, index) => createMemoryLocation(entry, typeof entry === \"string\" ? null : entry.state, index === 0 ? \"default\" : undefined));\n  let index = clampIndex(initialIndex == null ? entries.length - 1 : initialIndex);\n  let action = Action.Pop;\n  let listener = null;\n\n  function clampIndex(n) {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n\n  function getCurrentLocation() {\n    return entries[index];\n  }\n\n  function createMemoryLocation(to, state, key) {\n    if (state === void 0) {\n      state = null;\n    }\n\n    let location = createLocation(entries ? getCurrentLocation().pathname : \"/\", to, state, key);\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in memory history: \" + JSON.stringify(to));\n    return location;\n  }\n\n  let history = {\n    get index() {\n      return index;\n    },\n\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return getCurrentLocation();\n    },\n\n    createHref(to) {\n      return typeof to === \"string\" ? to : createPath(to);\n    },\n\n    encodeLocation(to) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\"\n      };\n    },\n\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation\n        });\n      }\n    },\n\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n\n      if (v5Compat && listener) {\n        listener({\n          action,\n          location: nextLocation\n        });\n      }\n    },\n\n    go(delta) {\n      action = Action.Pop;\n      index = clampIndex(index + delta);\n\n      if (listener) {\n        listener({\n          action,\n          location: getCurrentLocation()\n        });\n      }\n    },\n\n    listen(fn) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    }\n\n  };\n  return history;\n}\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\n\nfunction createBrowserHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  function createBrowserLocation(window, globalHistory) {\n    let {\n      pathname,\n      search,\n      hash\n    } = window.location;\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    }, // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n\n  function createBrowserHref(window, to) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(createBrowserLocation, createBrowserHref, null, options);\n}\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\n\nfunction createHashHistory(options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  function createHashLocation(window, globalHistory) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\"\n    } = parsePath(window.location.hash.substr(1));\n    return createLocation(\"\", {\n      pathname,\n      search,\n      hash\n    }, // state defaults to `null` because `window.history.state` does\n    globalHistory.state && globalHistory.state.usr || null, globalHistory.state && globalHistory.state.key || \"default\");\n  }\n\n  function createHashHref(window, to) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location, to) {\n    warning$1(location.pathname.charAt(0) === \"/\", \"relative pathnames are not supported in hash history.push(\" + JSON.stringify(to) + \")\");\n  }\n\n  return getUrlBasedHistory(createHashLocation, createHashHref, validateHashLocation, options);\n}\nfunction invariant(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nfunction warning$1(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n/**\n * For browser-based histories, we combine the state and key into an object\n */\n\n\nfunction getHistoryState(location) {\n  return {\n    usr: location.state,\n    key: location.key\n  };\n}\n/**\n * Creates a Location object with a unique key from the given Path\n */\n\n\nfunction createLocation(current, to, state, key) {\n  if (state === void 0) {\n    state = null;\n  }\n\n  let location = _extends({\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\"\n  }, typeof to === \"string\" ? parsePath(to) : to, {\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: to && to.key || key || createKey()\n  });\n\n  return location;\n}\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\n\nfunction createPath(_ref) {\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\"\n  } = _ref;\n  if (search && search !== \"?\") pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\") pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\n\nfunction parsePath(path) {\n  let parsedPath = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\nfunction createClientSideURL(location) {\n  // window.location.origin is \"null\" (the literal string value) in Firefox\n  // under certain conditions, notably when serving from a local HTML file\n  // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n  let base = typeof window !== \"undefined\" && typeof window.location !== \"undefined\" && window.location.origin !== \"null\" ? window.location.origin : window.location.href;\n  let href = typeof location === \"string\" ? location : createPath(location);\n  invariant(base, \"No window.location.(origin|href) available to create URL for href: \" + href);\n  return new URL(href, base);\n}\n\nfunction getUrlBasedHistory(getLocation, createHref, validateLocation, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  let {\n    window = document.defaultView,\n    v5Compat = false\n  } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener = null;\n\n  function handlePop() {\n    action = Action.Pop;\n\n    if (listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n\n  function push(to, state) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location); // try...catch because iOS limits us to 100 pushState calls :/\n\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n\n  function replace(to, state) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n    let historyState = getHistoryState(location);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({\n        action,\n        location: history.location\n      });\n    }\n  }\n\n  let history = {\n    get action() {\n      return action;\n    },\n\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n\n    listen(fn) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n\n    createHref(to) {\n      return createHref(window, to);\n    },\n\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createClientSideURL(typeof to === \"string\" ? to : createPath(to));\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash\n      };\n    },\n\n    push,\n    replace,\n\n    go(n) {\n      return globalHistory.go(n);\n    }\n\n  };\n  return history;\n} //#endregion\n\nvar ResultType;\n\n(function (ResultType) {\n  ResultType[\"data\"] = \"data\";\n  ResultType[\"deferred\"] = \"deferred\";\n  ResultType[\"redirect\"] = \"redirect\";\n  ResultType[\"error\"] = \"error\";\n})(ResultType || (ResultType = {}));\n\nfunction isIndexRoute(route) {\n  return route.index === true;\n} // Walk the route tree generating unique IDs where necessary so we are working\n// solely with AgnosticDataRouteObject's within the Router\n\n\nfunction convertRoutesToDataRoutes(routes, parentPath, allIds) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n\n  if (allIds === void 0) {\n    allIds = new Set();\n  }\n\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, index];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(route.index !== true || !route.children, \"Cannot specify children on an index route\");\n    invariant(!allIds.has(id), \"Found a route id collision on id \\\"\" + id + \"\\\".  Route \" + \"id's must be globally unique within Data Router usages\");\n    allIds.add(id);\n\n    if (isIndexRoute(route)) {\n      let indexRoute = _extends({}, route, {\n        id\n      });\n\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute = _extends({}, route, {\n        id,\n        children: route.children ? convertRoutesToDataRoutes(route.children, treePath, allIds) : undefined\n      });\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/utils/match-routes\n */\n\nfunction matchRoutes(routes, locationArg, basename) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n\n  let location = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n  let matches = null;\n\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    matches = matchRouteBranch(branches[i], // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    safelyDecodeURI(pathname));\n  }\n\n  return matches;\n}\n\nfunction flattenRoutes(routes, branches, parentsMeta, parentPath) {\n  if (branches === void 0) {\n    branches = [];\n  }\n\n  if (parentsMeta === void 0) {\n    parentsMeta = [];\n  }\n\n  if (parentPath === void 0) {\n    parentPath = \"\";\n  }\n\n  let flattenRoute = (route, index, relativePath) => {\n    let meta = {\n      relativePath: relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(meta.relativePath.startsWith(parentPath), \"Absolute route path \\\"\" + meta.relativePath + \"\\\" nested under path \" + (\"\\\"\" + parentPath + \"\\\" is not valid. An absolute child route path \") + \"must start with the combined path of all its parent routes.\");\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta); // Add the children before adding this route to the array so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n\n    if (route.children && route.children.length > 0) {\n      invariant( // Our types know better, but runtime JS may not!\n      // @ts-expect-error\n      route.index !== true, \"Index routes must not have child routes. Please remove \" + (\"all child routes from route path \\\"\" + path + \"\\\".\"));\n      flattenRoutes(route.children, branches, routesMeta, path);\n    } // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n\n\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta\n    });\n  };\n\n  routes.forEach((route, index) => {\n    var _route$path;\n\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !((_route$path = route.path) != null && _route$path.includes(\"?\"))) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n  return branches;\n}\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\n\n\nfunction explodeOptionalSegments(path) {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n  let [first, ...rest] = segments; // Optional path segments are denoted by a trailing `?`\n\n  let isOptional = first.endsWith(\"?\"); // Compute the corresponding required segment: `foo?` -> `foo`\n\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n  let result = []; // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explodes _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n\n  result.push(...restExploded.map(subpath => subpath === \"\" ? required : [required, subpath].join(\"/\"))); // Then if this is an optional value, add all child versions without\n\n  if (isOptional) {\n    result.push(...restExploded);\n  } // for absolute paths, ensure `/` instead of empty segment\n\n\n  return result.map(exploded => path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded);\n}\n\nfunction rankRouteBranches(branches) {\n  branches.sort((a, b) => a.score !== b.score ? b.score - a.score // Higher score first\n  : compareIndexes(a.routesMeta.map(meta => meta.childrenIndex), b.routesMeta.map(meta => meta.childrenIndex)));\n}\n\nconst paramRe = /^:\\w+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\n\nconst isSplat = s => s === \"*\";\n\nfunction computeScore(path, index) {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments.filter(s => !isSplat(s)).reduce((score, segment) => score + (paramRe.test(segment) ? dynamicSegmentValue : segment === \"\" ? emptySegmentValue : staticSegmentValue), initialScore);\n}\n\nfunction compareIndexes(a, b) {\n  let siblings = a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n  return siblings ? // If two routes are siblings, we should try to match the earlier sibling\n  // first. This allows people to have fine-grained control over the matching\n  // behavior by simply putting routes with identical paths in the order they\n  // want them tried.\n  a[a.length - 1] - b[b.length - 1] : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n  // so they sort equally.\n  0;\n}\n\nfunction matchRouteBranch(branch, pathname) {\n  let {\n    routesMeta\n  } = branch;\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches = [];\n\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname = matchedPathname === \"/\" ? pathname : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath({\n      path: meta.relativePath,\n      caseSensitive: meta.caseSensitive,\n      end\n    }, remainingPathname);\n    if (!match) return null;\n    Object.assign(matchedParams, match.params);\n    let route = meta.route;\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(joinPaths([matchedPathname, match.pathnameBase])),\n      route\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/utils/generate-path\n */\n\n\nfunction generatePath(originalPath, params) {\n  if (params === void 0) {\n    params = {};\n  }\n\n  let path = originalPath;\n\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(false, \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n    path = path.replace(/\\*$/, \"/*\");\n  }\n\n  return path.replace(/^:(\\w+)/g, (_, key) => {\n    invariant(params[key] != null, \"Missing \\\":\" + key + \"\\\" param\");\n    return params[key];\n  }).replace(/\\/:(\\w+)/g, (_, key) => {\n    invariant(params[key] != null, \"Missing \\\":\" + key + \"\\\" param\");\n    return \"/\" + params[key];\n  }).replace(/(\\/?)\\*/, (_, prefix, __, str) => {\n    const star = \"*\";\n\n    if (params[star] == null) {\n      // If no splat was provided, trim the trailing slash _unless_ it's\n      // the entire path\n      return str === \"/*\" ? \"/\" : \"\";\n    } // Apply the splat\n\n\n    return \"\" + prefix + params[star];\n  });\n}\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/utils/match-path\n */\n\nfunction matchPath(pattern, pathname) {\n  if (typeof pattern === \"string\") {\n    pattern = {\n      path: pattern,\n      caseSensitive: false,\n      end: true\n    };\n  }\n\n  let [matcher, paramNames] = compilePath(pattern.path, pattern.caseSensitive, pattern.end);\n  let match = pathname.match(matcher);\n  if (!match) return null;\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params = paramNames.reduce((memo, paramName, index) => {\n    // We need to compute the pathnameBase here using the raw splat value\n    // instead of using params[\"*\"] later because it will be decoded then\n    if (paramName === \"*\") {\n      let splatValue = captureGroups[index] || \"\";\n      pathnameBase = matchedPathname.slice(0, matchedPathname.length - splatValue.length).replace(/(.)\\/+$/, \"$1\");\n    }\n\n    memo[paramName] = safelyDecodeURIComponent(captureGroups[index] || \"\", paramName);\n    return memo;\n  }, {});\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern\n  };\n}\n\nfunction compilePath(path, caseSensitive, end) {\n  if (caseSensitive === void 0) {\n    caseSensitive = false;\n  }\n\n  if (end === void 0) {\n    end = true;\n  }\n\n  warning(path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"), \"Route path \\\"\" + path + \"\\\" will be treated as if it were \" + (\"\\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\" because the `*` character must \") + \"always follow a `/` in the pattern. To get rid of this warning, \" + (\"please change the route path to \\\"\" + path.replace(/\\*$/, \"/*\") + \"\\\".\"));\n  let paramNames = [];\n  let regexpSource = \"^\" + path.replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n  .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n  .replace(/[\\\\.*+^$?{}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n  .replace(/\\/:(\\w+)/g, (_, paramName) => {\n    paramNames.push(paramName);\n    return \"/([^\\\\/]+)\";\n  });\n\n  if (path.endsWith(\"*\")) {\n    paramNames.push(\"*\");\n    regexpSource += path === \"*\" || path === \"/*\" ? \"(.*)$\" // Already matched the initial /, just match the rest\n    : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else ;\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n  return [matcher, paramNames];\n}\n\nfunction safelyDecodeURI(value) {\n  try {\n    return decodeURI(value);\n  } catch (error) {\n    warning(false, \"The URL path \\\"\" + value + \"\\\" could not be decoded because it is is a \" + \"malformed URL segment. This is probably due to a bad percent \" + (\"encoding (\" + error + \").\"));\n    return value;\n  }\n}\n\nfunction safelyDecodeURIComponent(value, paramName) {\n  try {\n    return decodeURIComponent(value);\n  } catch (error) {\n    warning(false, \"The value for the URL param \\\"\" + paramName + \"\\\" will not be decoded because\" + (\" the string \\\"\" + value + \"\\\" is a malformed URL segment. This is probably\") + (\" due to a bad percent encoding (\" + error + \").\"));\n    return value;\n  }\n}\n/**\n * @private\n */\n\n\nfunction stripBasename(pathname, basename) {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  } // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n\n\n  let startIndex = basename.endsWith(\"/\") ? basename.length - 1 : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n/**\n * @private\n */\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/utils/resolve-path\n */\n\nfunction resolvePath(to, fromPathname) {\n  if (fromPathname === void 0) {\n    fromPathname = \"/\";\n  }\n\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\"\n  } = typeof to === \"string\" ? parsePath(to) : to;\n  let pathname = toPathname ? toPathname.startsWith(\"/\") ? toPathname : resolvePathname(toPathname, fromPathname) : fromPathname;\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash)\n  };\n}\n\nfunction resolvePathname(relativePath, fromPathname) {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n  relativeSegments.forEach(segment => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(char, field, dest, path) {\n  return \"Cannot include a '\" + char + \"' character in a manually specified \" + (\"`to.\" + field + \"` field [\" + JSON.stringify(path) + \"].  Please separate it out to the \") + (\"`to.\" + dest + \"` field. Alternatively you may provide the full path as \") + \"a string in <Link to=\\\"...\\\"> and the router will parse it for you.\";\n}\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\n\n\nfunction getPathContributingMatches(matches) {\n  return matches.filter((match, index) => index === 0 || match.route.path && match.route.path.length > 0);\n}\n/**\n * @private\n */\n\nfunction resolveTo(toArg, routePathnames, locationPathname, isPathRelative) {\n  if (isPathRelative === void 0) {\n    isPathRelative = false;\n  }\n\n  let to;\n\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = _extends({}, toArg);\n    invariant(!to.pathname || !to.pathname.includes(\"?\"), getInvalidPathError(\"?\", \"pathname\", \"search\", to));\n    invariant(!to.pathname || !to.pathname.includes(\"#\"), getInvalidPathError(\"#\", \"pathname\", \"hash\", to));\n    invariant(!to.search || !to.search.includes(\"#\"), getInvalidPathError(\"#\", \"search\", \"hash\", to));\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n  let from; // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n\n  if (isPathRelative || toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    if (toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\"); // Each leading .. segment means \"go up one route\" instead of \"go up one\n      // URL segment\".  This is a key difference from how <a href> works and a\n      // major reason we call this a \"to\" value instead of a \"href\".\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    } // If there are more \"..\" segments than parent routes, resolve relative to\n    // the root / URL.\n\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from); // Ensure the pathname has a trailing slash if the original \"to\" had one\n\n  let hasExplicitTrailingSlash = toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\"); // Or if this was a link to the current path which has a trailing slash\n\n  let hasCurrentTrailingSlash = (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n\n  if (!path.pathname.endsWith(\"/\") && (hasExplicitTrailingSlash || hasCurrentTrailingSlash)) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n/**\n * @private\n */\n\nfunction getToPathname(to) {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || to.pathname === \"\" ? \"/\" : typeof to === \"string\" ? parsePath(to).pathname : to.pathname;\n}\n/**\n * @private\n */\n\nconst joinPaths = paths => paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n/**\n * @private\n */\n\nconst normalizePathname = pathname => pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n/**\n * @private\n */\n\nconst normalizeSearch = search => !search || search === \"?\" ? \"\" : search.startsWith(\"?\") ? search : \"?\" + search;\n/**\n * @private\n */\n\nconst normalizeHash = hash => !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n */\n\nconst json = function json(data, init) {\n  if (init === void 0) {\n    init = {};\n  }\n\n  let responseInit = typeof init === \"number\" ? {\n    status: init\n  } : init;\n  let headers = new Headers(responseInit.headers);\n\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), _extends({}, responseInit, {\n    headers\n  }));\n};\nclass AbortedDeferredError extends Error {}\nclass DeferredData {\n  constructor(data) {\n    this.pendingKeys = new Set();\n    this.subscriber = undefined;\n    invariant(data && typeof data === \"object\" && !Array.isArray(data), \"defer() only accepts plain objects\"); // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n\n    let reject;\n    this.abortPromise = new Promise((_, r) => reject = r);\n    this.controller = new AbortController();\n\n    let onAbort = () => reject(new AbortedDeferredError(\"Deferred data aborted\"));\n\n    this.unlistenAbortSignal = () => this.controller.signal.removeEventListener(\"abort\", onAbort);\n\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n    this.data = Object.entries(data).reduce((acc, _ref) => {\n      let [key, value] = _ref;\n      return Object.assign(acc, {\n        [key]: this.trackPromise(key, value)\n      });\n    }, {});\n  }\n\n  trackPromise(key, value) {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.pendingKeys.add(key); // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n\n    let promise = Promise.race([value, this.abortPromise]).then(data => this.onSettle(promise, key, null, data), error => this.onSettle(promise, key, error)); // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n\n    promise.catch(() => {});\n    Object.defineProperty(promise, \"_tracked\", {\n      get: () => true\n    });\n    return promise;\n  }\n\n  onSettle(promise, key, error, data) {\n    if (this.controller.signal.aborted && error instanceof AbortedDeferredError) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeys.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    const subscriber = this.subscriber;\n\n    if (error) {\n      Object.defineProperty(promise, \"_error\", {\n        get: () => error\n      });\n      subscriber && subscriber(false);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", {\n      get: () => data\n    });\n    subscriber && subscriber(false);\n    return data;\n  }\n\n  subscribe(fn) {\n    this.subscriber = fn;\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeys.forEach((v, k) => this.pendingKeys.delete(k));\n    let subscriber = this.subscriber;\n    subscriber && subscriber(true);\n  }\n\n  async resolveData(signal) {\n    let aborted = false;\n\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise(resolve => {\n        this.subscribe(aborted => {\n          signal.removeEventListener(\"abort\", onAbort);\n\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeys.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(this.data !== null && this.done, \"Can only unwrap data on initialized and settled deferreds\");\n    return Object.entries(this.data).reduce((acc, _ref2) => {\n      let [key, value] = _ref2;\n      return Object.assign(acc, {\n        [key]: unwrapTrackedPromise(value)\n      });\n    }, {});\n  }\n\n}\n\nfunction isTrackedPromise(value) {\n  return value instanceof Promise && value._tracked === true;\n}\n\nfunction unwrapTrackedPromise(value) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n\n  return value._data;\n}\n\nfunction defer(data) {\n  return new DeferredData(data);\n}\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\n\nconst redirect = function redirect(url, init) {\n  if (init === void 0) {\n    init = 302;\n  }\n\n  let responseInit = init;\n\n  if (typeof responseInit === \"number\") {\n    responseInit = {\n      status: responseInit\n    };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n  return new Response(null, _extends({}, responseInit, {\n    headers\n  }));\n};\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n */\n\nclass ErrorResponse {\n  constructor(status, statusText, data, internal) {\n    if (internal === void 0) {\n      internal = false;\n    }\n\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n\n}\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response throw from an action/loader\n */\n\nfunction isRouteErrorResponse(e) {\n  return e instanceof ErrorResponse;\n}\n\nconst validMutationMethodsArr = [\"post\", \"put\", \"patch\", \"delete\"];\nconst validMutationMethods = new Set(validMutationMethodsArr);\nconst validRequestMethodsArr = [\"get\", ...validMutationMethodsArr];\nconst validRequestMethods = new Set(validRequestMethodsArr);\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\nconst IDLE_NAVIGATION = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst IDLE_FETCHER = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined\n};\nconst isBrowser = typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\";\nconst isServer = !isBrowser; //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\n\nfunction createRouter(init) {\n  invariant(init.routes.length > 0, \"You must provide a non-empty routes array to createRouter\");\n  let dataRoutes = convertRoutesToDataRoutes(init.routes); // Cleanup function for history\n\n  let unlistenHistory = null; // Externally-provided functions to call on all state changes\n\n  let subscribers = new Set(); // Externally-provided object to hold scroll restoration locations during routing\n\n  let savedScrollPositions = null; // Externally-provided function to get scroll restoration keys\n\n  let getScrollRestorationKey = null; // Externally-provided function to get current scroll position\n\n  let getScrollPosition = null; // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n\n  let initialScrollRestored = init.hydrationData != null;\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, init.basename);\n  let initialErrors = null;\n\n  if (initialMatches == null) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname\n    });\n    let {\n      matches,\n      route\n    } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = {\n      [route.id]: error\n    };\n  }\n\n  let initialized = !initialMatches.some(m => m.route.loader) || init.hydrationData != null;\n  let router;\n  let state = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: init.hydrationData && init.hydrationData.loaderData || {},\n    actionData: init.hydrationData && init.hydrationData.actionData || null,\n    errors: init.hydrationData && init.hydrationData.errors || initialErrors,\n    fetchers: new Map()\n  }; // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n\n  let pendingAction = Action.Pop; // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n\n  let pendingPreventScrollReset = false; // AbortController for the active navigation\n\n  let pendingNavigationController; // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n\n  let isUninterruptedRevalidation = false; // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidate()\n  //  - X-Remix-Revalidate (from redirect)\n\n  let isRevalidationRequired = false; // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n\n  let cancelledDeferredRoutes = []; // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n\n  let cancelledFetcherLoads = []; // AbortControllers for any in-flight fetchers\n\n  let fetchControllers = new Map(); // Track loads based on the order in which they started\n\n  let incrementingLoadId = 0; // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n\n  let pendingNavigationLoadId = -1; // Fetchers that triggered data reloads as a result of their actions\n\n  let fetchReloadIds = new Map(); // Fetchers that triggered redirect navigations from their actions\n\n  let fetchRedirectIds = new Set(); // Most recent href/match for fetcher.load calls for fetchers\n\n  let fetchLoadMatches = new Map(); // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n\n  let activeDeferreds = new Map(); // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(_ref => {\n      let {\n        action: historyAction,\n        location\n      } = _ref;\n      return startNavigation(historyAction, location);\n    }); // Kick off initial data load if needed.  Use Pop to avoid modifying history\n\n    if (!state.initialized) {\n      startNavigation(Action.Pop, state.location);\n    }\n\n    return router;\n  } // Clean up a router and it's side effects\n\n\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n  } // Subscribe to state updates for the router\n\n\n  function subscribe(fn) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  } // Update our state and notify the calling context of the change\n\n\n  function updateState(newState) {\n    state = _extends({}, state, newState);\n    subscribers.forEach(subscriber => subscriber(state));\n  } // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n\n\n  function completeNavigation(location, newState) {\n    var _location$state;\n\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload = state.actionData != null && state.navigation.formMethod != null && isMutationMethod(state.navigation.formMethod) && state.navigation.state === \"loading\" && ((_location$state = location.state) == null ? void 0 : _location$state._isRedirect) !== true;\n    let actionData;\n\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    } // Always preserve any existing loaderData from re-used routes\n\n\n    let loaderData = newState.loaderData ? mergeLoaderData(state.loaderData, newState.loaderData, newState.matches || [], newState.errors) : state.loaderData;\n    updateState(_extends({}, newState, {\n      actionData,\n      loaderData,\n      historyAction: pendingAction,\n      location,\n      initialized: true,\n      navigation: IDLE_NAVIGATION,\n      revalidation: \"idle\",\n      // Don't restore on submission navigations\n      restoreScrollPosition: state.navigation.formData ? false : getSavedScrollPosition(location, newState.matches || state.matches),\n      preventScrollReset: pendingPreventScrollReset\n    }));\n\n    if (isUninterruptedRevalidation) ; else if (pendingAction === Action.Pop) ; else if (pendingAction === Action.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === Action.Replace) {\n      init.history.replace(location, location.state);\n    } // Reset stateful navigation vars\n\n\n    pendingAction = Action.Pop;\n    pendingPreventScrollReset = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n    cancelledFetcherLoads = [];\n  } // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n\n\n  async function navigate(to, opts) {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let {\n      path,\n      submission,\n      error\n    } = normalizeNavigateOptions(to, opts);\n    let location = createLocation(state.location, path, opts && opts.state); // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n\n    location = _extends({}, location, init.history.encodeLocation(location));\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n    let historyAction = Action.Push;\n\n    if (userReplace === true) {\n      historyAction = Action.Replace;\n    } else if (userReplace === false) ; else if (submission != null && isMutationMethod(submission.formMethod) && submission.formAction === state.location.pathname + state.location.search) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = Action.Replace;\n    }\n\n    let preventScrollReset = opts && \"preventScrollReset\" in opts ? opts.preventScrollReset === true : undefined;\n    return await startNavigation(historyAction, location, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace\n    });\n  } // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n\n\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({\n      revalidation: \"loading\"\n    }); // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n\n    if (state.navigation.state === \"submitting\") {\n      return;\n    } // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n\n\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true\n      });\n      return;\n    } // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n\n\n    startNavigation(pendingAction || state.historyAction, state.navigation.location, {\n      overrideNavigation: state.navigation\n    });\n  } // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n\n\n  async function startNavigation(historyAction, location, opts) {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation = (opts && opts.startUninterruptedRevalidation) === true; // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches = matchRoutes(dataRoutes, location, init.basename); // Short circuit with a 404 on the root error boundary if we match nothing\n\n    if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes); // Cancel all pending deferred on 404s since we don't keep any routes\n\n      cancelActiveDeferreds();\n      completeNavigation(location, {\n        matches: notFoundMatches,\n        loaderData: {},\n        errors: {\n          [route.id]: error\n        }\n      });\n      return;\n    } // Short circuit if it's only a hash change\n\n\n    if (isHashChangeOnly(state.location, location)) {\n      completeNavigation(location, {\n        matches\n      });\n      return;\n    } // Create a controller/Request for this navigation\n\n\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(location, pendingNavigationController.signal, opts && opts.submission);\n    let pendingActionData;\n    let pendingError;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingError = {\n        [findNearestBoundary(matches).route.id]: opts.pendingError\n      };\n    } else if (opts && opts.submission && isMutationMethod(opts.submission.formMethod)) {\n      // Call action if we received an action submission\n      let actionOutput = await handleAction(request, location, opts.submission, matches, {\n        replace: opts.replace\n      });\n\n      if (actionOutput.shortCircuited) {\n        return;\n      }\n\n      pendingActionData = actionOutput.pendingActionData;\n      pendingError = actionOutput.pendingActionError;\n\n      let navigation = _extends({\n        state: \"loading\",\n        location\n      }, opts.submission);\n\n      loadingNavigation = navigation; // Create a GET request for the loaders\n\n      request = new Request(request.url, {\n        signal: request.signal\n      });\n    } // Call loaders\n\n\n    let {\n      shortCircuited,\n      loaderData,\n      errors\n    } = await handleLoaders(request, location, matches, loadingNavigation, opts && opts.submission, opts && opts.replace, pendingActionData, pendingError);\n\n    if (shortCircuited) {\n      return;\n    } // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n\n\n    pendingNavigationController = null;\n    completeNavigation(location, _extends({\n      matches\n    }, pendingActionData ? {\n      actionData: pendingActionData\n    } : {}, {\n      loaderData,\n      errors\n    }));\n  } // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n\n\n  async function handleAction(request, location, submission, matches, opts) {\n    interruptActiveLoads(); // Put us in a submitting state\n\n    let navigation = _extends({\n      state: \"submitting\",\n      location\n    }, submission);\n\n    updateState({\n      navigation\n    }); // Call our action and get the result\n\n    let result;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id\n        })\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, router.basename);\n\n      if (request.signal.aborted) {\n        return {\n          shortCircuited: true\n        };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace;\n\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        replace = result.location === state.location.pathname + state.location.search;\n      }\n\n      await startRedirectNavigation(state, result, {\n        submission,\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id); // By default, all submissions are REPLACE navigations, but if the\n      // action threw an error that'll be rendered in an errorElement, we fall\n      // back to PUSH so that the user can use the back button to get back to\n      // the pre-submission form location to try again\n\n      if ((opts && opts.replace) !== true) {\n        pendingAction = Action.Push;\n      }\n\n      return {\n        // Send back an empty object we can use to clear out any prior actionData\n        pendingActionData: {},\n        pendingActionError: {\n          [boundaryMatch.route.id]: result.error\n        }\n      };\n    }\n\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n\n    return {\n      pendingActionData: {\n        [actionMatch.route.id]: result.data\n      }\n    };\n  } // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n\n\n  async function handleLoaders(request, location, matches, overrideNavigation, submission, replace, pendingActionData, pendingError) {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation = overrideNavigation;\n\n    if (!loadingNavigation) {\n      let navigation = _extends({\n        state: \"loading\",\n        location,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined\n      }, submission);\n\n      loadingNavigation = navigation;\n    } // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n\n\n    let activeSubmission = submission ? submission : loadingNavigation.formMethod && loadingNavigation.formAction && loadingNavigation.formData && loadingNavigation.formEncType ? {\n      formMethod: loadingNavigation.formMethod,\n      formAction: loadingNavigation.formAction,\n      formData: loadingNavigation.formData,\n      formEncType: loadingNavigation.formEncType\n    } : undefined;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(state, matches, activeSubmission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches); // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n\n    cancelActiveDeferreds(routeId => !(matches && matches.some(m => m.route.id === routeId)) || matchesToLoad && matchesToLoad.some(m => m.route.id === routeId)); // Short circuit if we have no loaders to run\n\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      completeNavigation(location, _extends({\n        matches,\n        loaderData: {},\n        // Commit pending error if we're short circuiting\n        errors: pendingError || null\n      }, pendingActionData ? {\n        actionData: pendingActionData\n      } : {}));\n      return {\n        shortCircuited: true\n      };\n    } // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n\n\n    if (!isUninterruptedRevalidation) {\n      revalidatingFetchers.forEach(_ref2 => {\n        let [key] = _ref2;\n        let fetcher = state.fetchers.get(key);\n        let revalidatingFetcher = {\n          state: \"loading\",\n          data: fetcher && fetcher.data,\n          formMethod: undefined,\n          formAction: undefined,\n          formEncType: undefined,\n          formData: undefined,\n          \" _hasFetcherDoneAnything \": true\n        };\n        state.fetchers.set(key, revalidatingFetcher);\n      });\n      let actionData = pendingActionData || state.actionData;\n      updateState(_extends({\n        navigation: loadingNavigation\n      }, actionData ? Object.keys(actionData).length === 0 ? {\n        actionData: null\n      } : {\n        actionData\n      } : {}, revalidatingFetchers.length > 0 ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n    }\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n    revalidatingFetchers.forEach(_ref3 => {\n      let [key] = _ref3;\n      return fetchControllers.set(key, pendingNavigationController);\n    });\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, request);\n\n    if (request.signal.aborted) {\n      return {\n        shortCircuited: true\n      };\n    } // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n\n\n    revalidatingFetchers.forEach(_ref4 => {\n      let [key] = _ref4;\n      return fetchControllers.delete(key);\n    }); // If any loaders returned a redirect Response, start a new REPLACE navigation\n\n    let redirect = findRedirect(results);\n\n    if (redirect) {\n      await startRedirectNavigation(state, redirect, {\n        replace\n      });\n      return {\n        shortCircuited: true\n      };\n    } // Process and commit output from loaders\n\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, matches, matchesToLoad, loaderResults, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds); // Wire up subscribers to update loaderData as promises settle\n\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe(aborted => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n    markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    return _extends({\n      loaderData,\n      errors\n    }, didAbortFetchLoads || revalidatingFetchers.length > 0 ? {\n      fetchers: new Map(state.fetchers)\n    } : {});\n  }\n\n  function getFetcher(key) {\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  } // Trigger a fetcher load/submit for the given fetcher key\n\n\n  function fetch(key, routeId, href, opts) {\n    if (isServer) {\n      throw new Error(\"router.fetch() was called during the server render, but it shouldn't be. \" + \"You are likely calling a useFetcher() method in the body of your component. \" + \"Try moving it to a useEffect or a callback.\");\n    }\n\n    if (fetchControllers.has(key)) abortFetcher(key);\n    let matches = matchRoutes(dataRoutes, href, init.basename);\n\n    if (!matches) {\n      setFetcherError(key, routeId, getInternalRouterError(404, {\n        pathname: href\n      }));\n      return;\n    }\n\n    let {\n      path,\n      submission\n    } = normalizeNavigateOptions(href, opts, true);\n    let match = getTargetMatch(matches, path);\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(key, routeId, path, match, matches, submission);\n      return;\n    } // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n\n\n    fetchLoadMatches.set(key, [path, match, matches]);\n    handleFetcherLoader(key, routeId, path, match, matches, submission);\n  } // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n\n\n  async function handleFetcherAction(key, routeId, path, match, requestMatches, submission) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    if (!match.route.action) {\n      let error = getInternalRouterError(405, {\n        method: submission.formMethod,\n        pathname: path,\n        routeId: routeId\n      });\n      setFetcherError(key, routeId, error);\n      return;\n    } // Put this fetcher into it's submitting state\n\n\n    let existingFetcher = state.fetchers.get(key);\n\n    let fetcher = _extends({\n      state: \"submitting\"\n    }, submission, {\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true\n    });\n\n    state.fetchers.set(key, fetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the action for the fetcher\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(path, abortController.signal, submission);\n    fetchControllers.set(key, abortController);\n    let actionResult = await callLoaderOrAction(\"action\", fetchRequest, match, requestMatches, router.basename);\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by ou our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n\n      return;\n    }\n\n    if (isRedirectResult(actionResult)) {\n      fetchControllers.delete(key);\n      fetchRedirectIds.add(key);\n\n      let loadingFetcher = _extends({\n        state: \"loading\"\n      }, submission, {\n        data: undefined,\n        \" _hasFetcherDoneAnything \": true\n      });\n\n      state.fetchers.set(key, loadingFetcher);\n      updateState({\n        fetchers: new Map(state.fetchers)\n      });\n      return startRedirectNavigation(state, actionResult, {\n        isFetchActionRedirect: true\n      });\n    } // Process any non-redirect errors thrown\n\n\n    if (isErrorResult(actionResult)) {\n      setFetcherError(key, routeId, actionResult.error);\n      return;\n    }\n\n    if (isDeferredResult(actionResult)) {\n      invariant(false, \"defer() is not supported in actions\");\n    } // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n\n\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(nextLocation, abortController.signal);\n    let matches = state.navigation.state !== \"idle\" ? matchRoutes(dataRoutes, state.navigation.location, init.basename) : state.matches;\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = _extends({\n      state: \"loading\",\n      data: actionResult.data\n    }, submission, {\n      \" _hasFetcherDoneAnything \": true\n    });\n\n    state.fetchers.set(key, loadFetcher);\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(state, matches, submission, nextLocation, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, {\n      [match.route.id]: actionResult.data\n    }, undefined, // No need to send through errors since we short circuit above\n    fetchLoadMatches); // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n\n    revalidatingFetchers.filter(_ref5 => {\n      let [staleKey] = _ref5;\n      return staleKey !== key;\n    }).forEach(_ref6 => {\n      let [staleKey] = _ref6;\n      let existingFetcher = state.fetchers.get(staleKey);\n      let revalidatingFetcher = {\n        state: \"loading\",\n        data: existingFetcher && existingFetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(staleKey, revalidatingFetcher);\n      fetchControllers.set(staleKey, abortController);\n    });\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n    let {\n      results,\n      loaderResults,\n      fetcherResults\n    } = await callLoadersAndMaybeResolveData(state.matches, matches, matchesToLoad, revalidatingFetchers, revalidationRequest);\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach(_ref7 => {\n      let [staleKey] = _ref7;\n      return fetchControllers.delete(staleKey);\n    });\n    let redirect = findRedirect(results);\n\n    if (redirect) {\n      return startRedirectNavigation(state, redirect);\n    } // Process and commit output from loaders\n\n\n    let {\n      loaderData,\n      errors\n    } = processLoaderData(state, state.matches, matchesToLoad, loaderResults, undefined, revalidatingFetchers, fetcherResults, activeDeferreds);\n    let doneFetcher = {\n      state: \"idle\",\n      data: actionResult.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true\n    };\n    state.fetchers.set(key, doneFetcher);\n    let didAbortFetchLoads = abortStaleFetchLoads(loadId); // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n\n    if (state.navigation.state === \"loading\" && loadId > pendingNavigationLoadId) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers)\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState(_extends({\n        errors,\n        loaderData: mergeLoaderData(state.loaderData, loaderData, matches, errors)\n      }, didAbortFetchLoads ? {\n        fetchers: new Map(state.fetchers)\n      } : {}));\n      isRevalidationRequired = false;\n    }\n  } // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n\n\n  async function handleFetcherLoader(key, routeId, path, match, matches, submission) {\n    let existingFetcher = state.fetchers.get(key); // Put this fetcher into it's loading state\n\n    let loadingFetcher = _extends({\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined\n    }, submission, {\n      data: existingFetcher && existingFetcher.data,\n      \" _hasFetcherDoneAnything \": true\n    });\n\n    state.fetchers.set(key, loadingFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    }); // Call the loader for this fetcher route match\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(path, abortController.signal);\n    fetchControllers.set(key, abortController);\n    let result = await callLoaderOrAction(\"loader\", fetchRequest, match, matches, router.basename); // Deferred isn't supported or fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n\n    if (isDeferredResult(result)) {\n      result = (await resolveDeferredData(result, fetchRequest.signal, true)) || result;\n    } // We can delete this so long as we weren't aborted by ou our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n\n\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    } // If the loader threw a redirect Response, start a new REPLACE navigation\n\n\n    if (isRedirectResult(result)) {\n      await startRedirectNavigation(state, result);\n      return;\n    } // Process any non-redirect errors thrown\n\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, routeId);\n      state.fetchers.delete(key); // TODO: In remix, this would reset to IDLE_NAVIGATION if it was a catch -\n      // do we need to behave any differently with our non-redirect errors?\n      // What if it was a non-redirect Response?\n\n      updateState({\n        fetchers: new Map(state.fetchers),\n        errors: {\n          [boundaryMatch.route.id]: result.error\n        }\n      });\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\"); // Put the fetcher back into an idle state\n\n    let doneFetcher = {\n      state: \"idle\",\n      data: result.data,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      \" _hasFetcherDoneAnything \": true\n    };\n    state.fetchers.set(key, doneFetcher);\n    updateState({\n      fetchers: new Map(state.fetchers)\n    });\n  }\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n\n\n  async function startRedirectNavigation(state, redirect, _temp) {\n    var _window;\n\n    let {\n      submission,\n      replace,\n      isFetchActionRedirect\n    } = _temp === void 0 ? {} : _temp;\n\n    if (redirect.revalidate) {\n      isRevalidationRequired = true;\n    }\n\n    let redirectLocation = createLocation(state.location, redirect.location, // TODO: This can be removed once we get rid of useTransition in Remix v2\n    _extends({\n      _isRedirect: true\n    }, isFetchActionRedirect ? {\n      _isFetchActionRedirect: true\n    } : {}));\n    invariant(redirectLocation, \"Expected a location on the redirect navigation\"); // Check if this an external redirect that goes to a new origin\n\n    if (typeof ((_window = window) == null ? void 0 : _window.location) !== \"undefined\") {\n      let newOrigin = createClientSideURL(redirect.location).origin;\n\n      if (window.location.origin !== newOrigin) {\n        if (replace) {\n          window.location.replace(redirect.location);\n        } else {\n          window.location.assign(redirect.location);\n        }\n\n        return;\n      }\n    } // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n\n\n    pendingNavigationController = null;\n    let redirectHistoryAction = replace === true ? Action.Replace : Action.Push; // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n\n    let {\n      formMethod,\n      formAction,\n      formEncType,\n      formData\n    } = state.navigation;\n\n    if (!submission && formMethod && formAction && formData && formEncType) {\n      submission = {\n        formMethod,\n        formAction,\n        formEncType,\n        formData\n      };\n    } // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n\n\n    if (redirectPreserveMethodStatusCodes.has(redirect.status) && submission && isMutationMethod(submission.formMethod)) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: _extends({}, submission, {\n          formAction: redirect.location\n        })\n      });\n    } else {\n      // Otherwise, we kick off a new loading navigation, preserving the\n      // submission info for the duration of this navigation\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation: {\n          state: \"loading\",\n          location: redirectLocation,\n          formMethod: submission ? submission.formMethod : undefined,\n          formAction: submission ? submission.formAction : undefined,\n          formEncType: submission ? submission.formEncType : undefined,\n          formData: submission ? submission.formData : undefined\n        }\n      });\n    }\n  }\n\n  async function callLoadersAndMaybeResolveData(currentMatches, matches, matchesToLoad, fetchersToLoad, request) {\n    // Call all navigation loaders and revalidating fetcher loaders in parallel,\n    // then slice off the results into separate arrays so we can handle them\n    // accordingly\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, router.basename)), ...fetchersToLoad.map(_ref8 => {\n      let [, href, match, fetchMatches] = _ref8;\n      return callLoaderOrAction(\"loader\", createClientSideRequest(href, request.signal), match, fetchMatches, router.basename);\n    })]);\n    let loaderResults = results.slice(0, matchesToLoad.length);\n    let fetcherResults = results.slice(matchesToLoad.length);\n    await Promise.all([resolveDeferredResults(currentMatches, matchesToLoad, loaderResults, request.signal, false, state.loaderData), resolveDeferredResults(currentMatches, fetchersToLoad.map(_ref9 => {\n      let [,, match] = _ref9;\n      return match;\n    }), fetcherResults, request.signal, true)]);\n    return {\n      results,\n      loaderResults,\n      fetcherResults\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true; // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds()); // Abort in-flight fetcher loads\n\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.push(key);\n        abortFetcher(key);\n      }\n    });\n  }\n\n  function setFetcherError(key, routeId, error) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState({\n      errors: {\n        [boundaryMatch.route.id]: error\n      },\n      fetchers: new Map(state.fetchers)\n    });\n  }\n\n  function deleteFetcher(key) {\n    if (fetchControllers.has(key)) abortFetcher(key);\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function abortFetcher(key) {\n    let controller = fetchControllers.get(key);\n    invariant(controller, \"Expected fetch controller: \" + key);\n    controller.abort();\n    fetchControllers.delete(key);\n  }\n\n  function markFetchersDone(keys) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = {\n        state: \"idle\",\n        data: fetcher.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone() {\n    let doneKeys = [];\n\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, \"Expected fetcher: \" + key);\n\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n      }\n    }\n\n    markFetchersDone(doneKeys);\n  }\n\n  function abortStaleFetchLoads(landedId) {\n    let yeetedKeys = [];\n\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, \"Expected fetcher: \" + key);\n\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function cancelActiveDeferreds(predicate) {\n    let cancelledRouteIds = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  } // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n\n\n  function enableScrollRestoration(positions, getPosition, getKey) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n\n    getScrollRestorationKey = getKey || (location => location.key); // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n\n\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n\n      if (y != null) {\n        updateState({\n          restoreScrollPosition: y\n        });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function saveScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(location, matches) {\n    if (savedScrollPositions && getScrollRestorationKey && getScrollPosition) {\n      let userMatches = matches.map(m => createUseMatchesMatch(m, state.loaderData));\n      let key = getScrollRestorationKey(location, userMatches) || location.key;\n      let y = savedScrollPositions[key];\n\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n\n    return null;\n  }\n\n  router = {\n    get basename() {\n      return init.basename;\n    },\n\n    get state() {\n      return state;\n    },\n\n    get routes() {\n      return dataRoutes;\n    },\n\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: to => init.history.createHref(to),\n    encodeLocation: to => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher,\n    dispose,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds\n  };\n  return router;\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nfunction createStaticHandler(routes, opts) {\n  invariant(routes.length > 0, \"You must provide a non-empty routes array to createStaticHandler\");\n  let dataRoutes = convertRoutesToDataRoutes(routes);\n  let basename = (opts ? opts.basename : null) || \"/\";\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   */\n\n  async function query(request, _temp2) {\n    let {\n      requestContext\n    } = _temp2 === void 0 ? {} : _temp2;\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename); // SSR supports HEAD requests while SPA doesn't\n\n    if (!isValidMethod(method) && method !== \"head\") {\n      let error = getInternalRouterError(405, {\n        method\n      });\n      let {\n        matches: methodNotAllowedMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n      let {\n        matches: notFoundMatches,\n        route\n      } = getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext);\n\n    if (isResponse(result)) {\n      return result;\n    } // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n\n\n    return _extends({\n      location,\n      basename\n    }, result);\n  }\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   */\n\n\n  async function queryRoute(request, _temp3) {\n    let {\n      routeId,\n      requestContext\n    } = _temp3 === void 0 ? {} : _temp3;\n    let url = new URL(request.url);\n    let method = request.method.toLowerCase();\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename); // SSR supports HEAD requests while SPA doesn't\n\n    if (!isValidMethod(method) && method !== \"head\") {\n      throw getInternalRouterError(405, {\n        method\n      });\n    } else if (!matches) {\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n\n    let match = routeId ? matches.find(m => m.route.id === routeId) : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, {\n        pathname: location.pathname\n      });\n    }\n\n    let result = await queryImpl(request, location, matches, requestContext, match);\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    } // Pick off the right state value to return\n\n\n    let routeData = [result.actionData, result.loaderData].find(v => v);\n    return Object.values(routeData || {})[0];\n  }\n\n  async function queryImpl(request, location, matches, requestContext, routeMatch) {\n    invariant(request.signal, \"query()/queryRoute() requests must contain an AbortController signal\");\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(request, matches, routeMatch || getTargetMatch(matches, location), requestContext, routeMatch != null);\n        return result;\n      }\n\n      let result = await loadRouteData(request, matches, requestContext, routeMatch);\n      return isResponse(result) ? result : _extends({}, result, {\n        actionData: null,\n        actionHeaders: {}\n      });\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction, we throw\n      // it to bail out and then return or throw here based on whether the user\n      // returned or threw\n      if (isQueryRouteResponse(e)) {\n        if (e.type === ResultType.error && !isRedirectResponse(e.response)) {\n          throw e.response;\n        }\n\n        return e.response;\n      } // Redirects are always returned since they don't propagate to catch\n      // boundaries\n\n\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n\n      throw e;\n    }\n  }\n\n  async function submit(request, matches, actionMatch, requestContext, isRouteRequest) {\n    let result;\n\n    if (!actionMatch.route.action) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id\n      });\n\n      if (isRouteRequest) {\n        throw error;\n      }\n\n      result = {\n        type: ResultType.error,\n        error\n      };\n    } else {\n      result = await callLoaderOrAction(\"action\", request, actionMatch, matches, basename, true, isRouteRequest, requestContext);\n\n      if (request.signal.aborted) {\n        let method = isRouteRequest ? \"queryRoute\" : \"query\";\n        throw new Error(method + \"() call aborted\");\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.status,\n        headers: {\n          Location: result.location\n        }\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      throw new Error(\"defer() is not supported in actions\");\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: {\n          [actionMatch.route.id]: result.data\n        },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {}\n      };\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n      let context = await loadRouteData(request, matches, requestContext, undefined, {\n        [boundaryMatch.route.id]: result.error\n      }); // action status codes take precedence over loader status codes\n\n      return _extends({}, context, {\n        statusCode: isRouteErrorResponse(result.error) ? result.error.status : 500,\n        actionData: null,\n        actionHeaders: _extends({}, result.headers ? {\n          [actionMatch.route.id]: result.headers\n        } : {})\n      });\n    } // Create a GET request for the loaders\n\n\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal\n    });\n    let context = await loadRouteData(loaderRequest, matches, requestContext);\n    return _extends({}, context, result.statusCode ? {\n      statusCode: result.statusCode\n    } : {}, {\n      actionData: {\n        [actionMatch.route.id]: result.data\n      },\n      actionHeaders: _extends({}, result.headers ? {\n        [actionMatch.route.id]: result.headers\n      } : {})\n    });\n  }\n\n  async function loadRouteData(request, matches, requestContext, routeMatch, pendingActionError) {\n    let isRouteRequest = routeMatch != null; // Short circuit if we have no loaders to run (queryRoute())\n\n    if (isRouteRequest && !(routeMatch != null && routeMatch.route.loader)) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch == null ? void 0 : routeMatch.route.id\n      });\n    }\n\n    let requestMatches = routeMatch ? [routeMatch] : getLoaderMatchesUntilBoundary(matches, Object.keys(pendingActionError || {})[0]);\n    let matchesToLoad = requestMatches.filter(m => m.route.loader); // Short circuit if we have no loaders to run (query())\n\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce((acc, m) => Object.assign(acc, {\n          [m.route.id]: null\n        }), {}),\n        errors: pendingActionError || null,\n        statusCode: 200,\n        loaderHeaders: {}\n      };\n    }\n\n    let results = await Promise.all([...matchesToLoad.map(match => callLoaderOrAction(\"loader\", request, match, matches, basename, true, isRouteRequest, requestContext))]);\n\n    if (request.signal.aborted) {\n      let method = isRouteRequest ? \"queryRoute\" : \"query\";\n      throw new Error(method + \"() call aborted\");\n    }\n\n    let executedLoaders = new Set();\n    results.forEach((result, i) => {\n      executedLoaders.add(matchesToLoad[i].route.id); // Can't do anything with these without the Remix side of things, so just\n      // cancel them for now\n\n      if (isDeferredResult(result)) {\n        result.deferredData.cancel();\n      }\n    }); // Process and commit output from loaders\n\n    let context = processRouteLoaderData(matches, matchesToLoad, results, pendingActionError); // Add a null for any non-loader matches for proper revalidation on the client\n\n    matches.forEach(match => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n    return _extends({}, context, {\n      matches\n    });\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute\n  };\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\n\nfunction getStaticContextFromError(routes, context, error) {\n  let newContext = _extends({}, context, {\n    statusCode: 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error\n    }\n  });\n\n  return newContext;\n}\n\nfunction isSubmissionNavigation(opts) {\n  return opts != null && \"formData\" in opts;\n} // Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\n\n\nfunction normalizeNavigateOptions(to, opts, isFetcher) {\n  if (isFetcher === void 0) {\n    isFetcher = false;\n  }\n\n  let path = typeof to === \"string\" ? to : createPath(to); // Return location verbatim on non-submission navigations\n\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return {\n      path\n    };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, {\n        method: opts.formMethod\n      })\n    };\n  } // Create a Submission on non-GET navigations\n\n\n  let submission;\n\n  if (opts.formData) {\n    submission = {\n      formMethod: opts.formMethod || \"get\",\n      formAction: stripHashFromPath(path),\n      formEncType: opts && opts.formEncType || \"application/x-www-form-urlencoded\",\n      formData: opts.formData\n    };\n\n    if (isMutationMethod(submission.formMethod)) {\n      return {\n        path,\n        submission\n      };\n    }\n  } // Flatten submission onto URLSearchParams for GET submissions\n\n\n  let parsedPath = parsePath(path);\n\n  try {\n    let searchParams = convertFormDataToSearchParams(opts.formData); // Since fetcher GET submissions only run a single loader (as opposed to\n    // navigation GET submissions which run all loaders), we need to preserve\n    // any incoming ?index params\n\n    if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n      searchParams.append(\"index\", \"\");\n    }\n\n    parsedPath.search = \"?\" + searchParams;\n  } catch (e) {\n    return {\n      path,\n      error: getInternalRouterError(400)\n    };\n  }\n\n  return {\n    path: createPath(parsedPath),\n    submission\n  };\n} // Filter out all routes below any caught error as they aren't going to\n// render so we don't need to load them\n\n\nfunction getLoaderMatchesUntilBoundary(matches, boundaryId) {\n  let boundaryMatches = matches;\n\n  if (boundaryId) {\n    let index = matches.findIndex(m => m.route.id === boundaryId);\n\n    if (index >= 0) {\n      boundaryMatches = matches.slice(0, index);\n    }\n  }\n\n  return boundaryMatches;\n}\n\nfunction getMatchesToLoad(state, matches, submission, location, isRevalidationRequired, cancelledDeferredRoutes, cancelledFetcherLoads, pendingActionData, pendingError, fetchLoadMatches) {\n  let actionResult = pendingError ? Object.values(pendingError)[0] : pendingActionData ? Object.values(pendingActionData)[0] : undefined; // Pick navigation matches that are net-new or qualify for revalidation\n\n  let boundaryId = pendingError ? Object.keys(pendingError)[0] : undefined;\n  let boundaryMatches = getLoaderMatchesUntilBoundary(matches, boundaryId);\n  let navigationMatches = boundaryMatches.filter((match, index) => match.route.loader != null && (isNewLoader(state.loaderData, state.matches[index], match) || // If this route had a pending deferred cancelled it must be revalidated\n  cancelledDeferredRoutes.some(id => id === match.route.id) || shouldRevalidateLoader(state.location, state.matches[index], submission, location, match, isRevalidationRequired, actionResult))); // Pick fetcher.loads that need to be revalidated\n\n  let revalidatingFetchers = [];\n  fetchLoadMatches && fetchLoadMatches.forEach((_ref10, key) => {\n    let [href, match, fetchMatches] = _ref10;\n\n    // This fetcher was cancelled from a prior action submission - force reload\n    if (cancelledFetcherLoads.includes(key)) {\n      revalidatingFetchers.push([key, href, match, fetchMatches]);\n    } else if (isRevalidationRequired) {\n      let shouldRevalidate = shouldRevalidateLoader(href, match, submission, href, match, isRevalidationRequired, actionResult);\n\n      if (shouldRevalidate) {\n        revalidatingFetchers.push([key, href, match, fetchMatches]);\n      }\n    }\n  });\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction isNewLoader(currentLoaderData, currentMatch, match) {\n  let isNew = // [a] -> [a, b]\n  !currentMatch || // [a, b] -> [a, c]\n  match.route.id !== currentMatch.route.id; // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n\n  let isMissingData = currentLoaderData[match.route.id] === undefined; // Always load if this is a net-new route or we don't yet have data\n\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(currentMatch, match) {\n  let currentPath = currentMatch.route.path;\n  return (// param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname || // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    currentPath && currentPath.endsWith(\"*\") && currentMatch.params[\"*\"] !== match.params[\"*\"]\n  );\n}\n\nfunction shouldRevalidateLoader(currentLocation, currentMatch, submission, location, match, isRevalidationRequired, actionResult) {\n  let currentUrl = createClientSideURL(currentLocation);\n  let currentParams = currentMatch.params;\n  let nextUrl = createClientSideURL(location);\n  let nextParams = match.params; // This is the default implementation as to when we revalidate.  If the route\n  // provides it's own implementation, then we give them full control but\n  // provide this value so they can leverage it if needed after they check\n  // their own specific use cases\n  // Note that fetchers always provide the same current/next locations so the\n  // URL-based checks here don't apply to fetcher shouldRevalidate calls\n\n  let defaultShouldRevalidate = isNewRouteInstance(currentMatch, match) || // Clicked the same link, resubmitted a GET form\n  currentUrl.toString() === nextUrl.toString() || // Search params affect all loaders\n  currentUrl.search !== nextUrl.search || // Forced revalidation due to submission, useRevalidate, or X-Remix-Revalidate\n  isRevalidationRequired;\n\n  if (match.route.shouldRevalidate) {\n    let routeChoice = match.route.shouldRevalidate(_extends({\n      currentUrl,\n      currentParams,\n      nextUrl,\n      nextParams\n    }, submission, {\n      actionResult,\n      defaultShouldRevalidate\n    }));\n\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return defaultShouldRevalidate;\n}\n\nasync function callLoaderOrAction(type, request, match, matches, basename, isStaticRequest, isRouteRequest, requestContext) {\n  if (basename === void 0) {\n    basename = \"/\";\n  }\n\n  if (isStaticRequest === void 0) {\n    isStaticRequest = false;\n  }\n\n  if (isRouteRequest === void 0) {\n    isRouteRequest = false;\n  }\n\n  let resultType;\n  let result; // Setup a promise we can race against so that abort signals short circuit\n\n  let reject;\n  let abortPromise = new Promise((_, r) => reject = r);\n\n  let onReject = () => reject();\n\n  request.signal.addEventListener(\"abort\", onReject);\n\n  try {\n    let handler = match.route[type];\n    invariant(handler, \"Could not find the \" + type + \" to run on the \\\"\" + match.route.id + \"\\\" route\");\n    result = await Promise.race([handler({\n      request,\n      params: match.params,\n      context: requestContext\n    }), abortPromise]);\n    invariant(result !== undefined, \"You defined \" + (type === \"action\" ? \"an action\" : \"a loader\") + \" for route \" + (\"\\\"\" + match.route.id + \"\\\" but didn't return anything from your `\" + type + \"` \") + \"function. Please return a value or `null`.\");\n  } catch (e) {\n    resultType = ResultType.error;\n    result = e;\n  } finally {\n    request.signal.removeEventListener(\"abort\", onReject);\n  }\n\n  if (isResponse(result)) {\n    let status = result.status; // Process redirects\n\n    if (redirectStatusCodes.has(status)) {\n      let location = result.headers.get(\"Location\");\n      invariant(location, \"Redirects returned/thrown from loaders/actions must have a Location header\");\n      let isAbsolute = /^[a-z+]+:\\/\\//i.test(location) || location.startsWith(\"//\"); // Support relative routing in internal redirects\n\n      if (!isAbsolute) {\n        let activeMatches = matches.slice(0, matches.indexOf(match) + 1);\n        let routePathnames = getPathContributingMatches(activeMatches).map(match => match.pathnameBase);\n        let resolvedLocation = resolveTo(location, routePathnames, new URL(request.url).pathname);\n        invariant(createPath(resolvedLocation), \"Unable to resolve redirect location: \" + location); // Prepend the basename to the redirect location if we have one\n\n        if (basename) {\n          let path = resolvedLocation.pathname;\n          resolvedLocation.pathname = path === \"/\" ? basename : joinPaths([basename, path]);\n        }\n\n        location = createPath(resolvedLocation);\n      } // Don't process redirects in the router during static requests requests.\n      // Instead, throw the Response and let the server handle it with an HTTP\n      // redirect.  We also update the Location header in place in this flow so\n      // basename and relative routing is taken into account\n\n\n      if (isStaticRequest) {\n        result.headers.set(\"Location\", location);\n        throw result;\n      }\n\n      return {\n        type: ResultType.redirect,\n        status,\n        location,\n        revalidate: result.headers.get(\"X-Remix-Revalidate\") !== null\n      };\n    } // For SSR single-route requests, we want to hand Responses back directly\n    // without unwrapping.  We do this with the QueryRouteResponse wrapper\n    // interface so we can know whether it was returned or thrown\n\n\n    if (isRouteRequest) {\n      // eslint-disable-next-line no-throw-literal\n      throw {\n        type: resultType || ResultType.data,\n        response: result\n      };\n    }\n\n    let data;\n    let contentType = result.headers.get(\"Content-Type\"); // Check between word boundaries instead of startsWith() due to the last\n    // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n\n    if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n      data = await result.json();\n    } else {\n      data = await result.text();\n    }\n\n    if (resultType === ResultType.error) {\n      return {\n        type: resultType,\n        error: new ErrorResponse(status, result.statusText, data),\n        headers: result.headers\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers\n    };\n  }\n\n  if (resultType === ResultType.error) {\n    return {\n      type: resultType,\n      error: result\n    };\n  }\n\n  if (result instanceof DeferredData) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result\n    };\n  }\n\n  return {\n    type: ResultType.data,\n    data: result\n  };\n} // Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\n\n\nfunction createClientSideRequest(location, signal, submission) {\n  let url = createClientSideURL(stripHashFromPath(location)).toString();\n  let init = {\n    signal\n  };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let {\n      formMethod,\n      formEncType,\n      formData\n    } = submission;\n    init.method = formMethod.toUpperCase();\n    init.body = formEncType === \"application/x-www-form-urlencoded\" ? convertFormDataToSearchParams(formData) : formData;\n  } // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData) {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    invariant(typeof value === \"string\", 'File inputs are not supported with encType \"application/x-www-form-urlencoded\", ' + 'please use \"multipart/form-data\" instead.');\n    searchParams.append(key, value);\n  }\n\n  return searchParams;\n}\n\nfunction processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds) {\n  // Fill in loaderData/errors from our loaders\n  let loaderData = {};\n  let errors = null;\n  let statusCode;\n  let foundError = false;\n  let loaderHeaders = {}; // Process loader results into state.loaderData/state.errors\n\n  results.forEach((result, index) => {\n    let id = matchesToLoad[index].route.id;\n    invariant(!isRedirectResult(result), \"Cannot handle redirect results in processLoaderData\");\n\n    if (isErrorResult(result)) {\n      // Look upwards from the matched route for the closest ancestor\n      // error boundary, defaulting to the root match\n      let boundaryMatch = findNearestBoundary(matches, id);\n      let error = result.error; // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n\n      if (pendingError) {\n        error = Object.values(pendingError)[0];\n        pendingError = undefined;\n      }\n\n      errors = errors || {}; // Prefer higher error values if lower errors bubble to the same boundary\n\n      if (errors[boundaryMatch.route.id] == null) {\n        errors[boundaryMatch.route.id] = error;\n      } // Clear our any prior loaderData for the throwing route\n\n\n      loaderData[id] = undefined; // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error) ? result.error.status : 500;\n      }\n\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else if (isDeferredResult(result)) {\n      activeDeferreds && activeDeferreds.set(id, result.deferredData);\n      loaderData[id] = result.deferredData.data; // TODO: Add statusCode/headers once we wire up streaming in Remix\n    } else {\n      loaderData[id] = result.data; // Error status codes always override success status codes, but if all\n      // loaders are successful we take the deepest status code.\n\n      if (result.statusCode != null && result.statusCode !== 200 && !foundError) {\n        statusCode = result.statusCode;\n      }\n\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    }\n  }); // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n\n  if (pendingError) {\n    errors = pendingError;\n    loaderData[Object.keys(pendingError)[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders\n  };\n}\n\nfunction processLoaderData(state, matches, matchesToLoad, results, pendingError, revalidatingFetchers, fetcherResults, activeDeferreds) {\n  let {\n    loaderData,\n    errors\n  } = processRouteLoaderData(matches, matchesToLoad, results, pendingError, activeDeferreds); // Process results from our revalidating fetchers\n\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let [key,, match] = revalidatingFetchers[index];\n    invariant(fetcherResults !== undefined && fetcherResults[index] !== undefined, \"Did not find corresponding fetcher result\");\n    let result = fetcherResults[index]; // Process fetcher non-redirect errors\n\n    if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match.route.id);\n\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = _extends({}, errors, {\n          [boundaryMatch.route.id]: result.error\n        });\n      }\n\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      throw new Error(\"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      throw new Error(\"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = {\n        state: \"idle\",\n        data: result.data,\n        formMethod: undefined,\n        formAction: undefined,\n        formEncType: undefined,\n        formData: undefined,\n        \" _hasFetcherDoneAnything \": true\n      };\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  return {\n    loaderData,\n    errors\n  };\n}\n\nfunction mergeLoaderData(loaderData, newLoaderData, matches, errors) {\n  let mergedLoaderData = _extends({}, newLoaderData);\n\n  for (let match of matches) {\n    let id = match.route.id;\n\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      }\n    } else if (loaderData[id] !== undefined) {\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n\n  return mergedLoaderData;\n} // Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\n\n\nfunction findNearestBoundary(matches, routeId) {\n  let eligibleMatches = routeId ? matches.slice(0, matches.findIndex(m => m.route.id === routeId) + 1) : [...matches];\n  return eligibleMatches.reverse().find(m => m.route.hasErrorBoundary === true) || matches[0];\n}\n\nfunction getShortCircuitMatches(routes) {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route = routes.find(r => r.index || !r.path || r.path === \"/\") || {\n    id: \"__shim-error-route__\"\n  };\n  return {\n    matches: [{\n      params: {},\n      pathname: \"\",\n      pathnameBase: \"\",\n      route\n    }],\n    route\n  };\n}\n\nfunction getInternalRouterError(status, _temp4) {\n  let {\n    pathname,\n    routeId,\n    method\n  } = _temp4 === void 0 ? {} : _temp4;\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide a `loader` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else {\n      errorMessage = \"Cannot submit binary form data using GET\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = \"Route \\\"\" + routeId + \"\\\" does not match URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = \"No route matches URL \\\"\" + pathname + \"\\\"\";\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n\n    if (method && pathname && routeId) {\n      errorMessage = \"You made a \" + method.toUpperCase() + \" request to \\\"\" + pathname + \"\\\" but \" + (\"did not provide an `action` for route \\\"\" + routeId + \"\\\", \") + \"so there is no way to handle the request.\";\n    } else if (method) {\n      errorMessage = \"Invalid request method \\\"\" + method.toUpperCase() + \"\\\"\";\n    }\n  }\n\n  return new ErrorResponse(status || 500, statusText, new Error(errorMessage), true);\n} // Find any returned redirect errors, starting from the lowest match\n\n\nfunction findRedirect(results) {\n  for (let i = results.length - 1; i >= 0; i--) {\n    let result = results[i];\n\n    if (isRedirectResult(result)) {\n      return result;\n    }\n  }\n}\n\nfunction stripHashFromPath(path) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath(_extends({}, parsedPath, {\n    hash: \"\"\n  }));\n}\n\nfunction isHashChangeOnly(a, b) {\n  return a.pathname === b.pathname && a.search === b.search && a.hash !== b.hash;\n}\n\nfunction isDeferredResult(result) {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result) {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result) {\n  return (result && result.type) === ResultType.redirect;\n}\n\nfunction isResponse(value) {\n  return value != null && typeof value.status === \"number\" && typeof value.statusText === \"string\" && typeof value.headers === \"object\" && typeof value.body !== \"undefined\";\n}\n\nfunction isRedirectResponse(result) {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isQueryRouteResponse(obj) {\n  return obj && isResponse(obj.response) && (obj.type === ResultType.data || ResultType.error);\n}\n\nfunction isValidMethod(method) {\n  return validRequestMethods.has(method);\n}\n\nfunction isMutationMethod(method) {\n  return validMutationMethods.has(method);\n}\n\nasync function resolveDeferredResults(currentMatches, matchesToLoad, results, signal, isFetcher, currentLoaderData) {\n  for (let index = 0; index < results.length; index++) {\n    let result = results[index];\n    let match = matchesToLoad[index];\n    let currentMatch = currentMatches.find(m => m.route.id === match.route.id);\n    let isRevalidatingLoader = currentMatch != null && !isNewRouteInstance(currentMatch, match) && (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && (isFetcher || isRevalidatingLoader)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, isFetcher).then(result => {\n        if (result) {\n          results[index] = result || results[index];\n        }\n      });\n    }\n  }\n}\n\nasync function resolveDeferredData(result, signal, unwrap) {\n  if (unwrap === void 0) {\n    unwrap = false;\n  }\n\n  let aborted = await result.deferredData.resolveData(signal);\n\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data\n  };\n}\n\nfunction hasNakedIndexQuery(search) {\n  return new URLSearchParams(search).getAll(\"index\").some(v => v === \"\");\n} // Note: This should match the format exported by useMatches, so if you change\n// this please also change that :)  Eventually we'll DRY this up\n\n\nfunction createUseMatchesMatch(match, loaderData) {\n  let {\n    route,\n    pathname,\n    params\n  } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle\n  };\n}\n\nfunction getTargetMatch(matches, location) {\n  let search = typeof location === \"string\" ? parsePath(location).search : location.search;\n\n  if (matches[matches.length - 1].route.index && hasNakedIndexQuery(search || \"\")) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  } // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n\n\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n} //#endregion\n\nexport { AbortedDeferredError, Action, ErrorResponse, IDLE_FETCHER, IDLE_NAVIGATION, convertRoutesToDataRoutes as UNSAFE_convertRoutesToDataRoutes, getPathContributingMatches as UNSAFE_getPathContributingMatches, createBrowserHistory, createHashHistory, createMemoryHistory, createPath, createRouter, createStaticHandler, defer, generatePath, getStaticContextFromError, getToPathname, invariant, isRouteErrorResponse, joinPaths, json, matchPath, matchRoutes, normalizePathname, parsePath, redirect, resolvePath, resolveTo, stripBasename, warning };\n//# sourceMappingURL=router.js.map\n", "/**\n * React Router v6.6.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { invariant, joinPaths, matchPath, UNSAFE_getPathContributingMatches, warning, resolveTo, parsePath, matchRoutes, Action, isRouteErrorResponse, createMemoryHistory, stripBasename, AbortedDeferredError, createRouter } from '@remix-run/router';\nexport { AbortedDeferredError, Action as NavigationType, createPath, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, resolvePath } from '@remix-run/router';\nimport * as React from 'react';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n/**\n * inlined Object.is polyfill to avoid requiring consumers ship their own\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n */\n\nfunction isPolyfill(x, y) {\n  return x === y && (x !== 0 || 1 / x === 1 / y) || x !== x && y !== y // eslint-disable-line no-self-compare\n  ;\n}\n\nconst is = typeof Object.is === \"function\" ? Object.is : isPolyfill; // Intentionally not using named imports because Rollup uses dynamic\n// dispatch for CommonJS interop named imports.\n\nconst {\n  useState,\n  useEffect,\n  useLayoutEffect,\n  useDebugValue\n} = React;\nlet didWarnOld18Alpha = false;\nlet didWarnUncachedGetSnapshot = false; // Disclaimer: This shim breaks many of the rules of React, and only works\n// because of a very particular set of implementation details and assumptions\n// -- change any one of them and it will break. The most important assumption\n// is that updates are always synchronous, because concurrent rendering is\n// only available in versions of React that also have a built-in\n// useSyncExternalStore API. And we only use this shim when the built-in API\n// does not exist.\n//\n// Do not assume that the clever hacks used by this hook also work in general.\n// The point of this shim is to replace the need for hacks by other libraries.\n\nfunction useSyncExternalStore$2(subscribe, getSnapshot, // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n// React do not expose a way to check if we're hydrating. So users of the shim\n// will need to track that themselves and return the correct value\n// from `getSnapshot`.\ngetServerSnapshot) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!didWarnOld18Alpha) {\n      if (\"startTransition\" in React) {\n        didWarnOld18Alpha = true;\n        console.error(\"You are using an outdated, pre-release alpha of React 18 that \" + \"does not support useSyncExternalStore. The \" + \"use-sync-external-store shim will not work correctly. Upgrade \" + \"to a newer pre-release.\");\n      }\n    }\n  } // Read the current snapshot from the store on every render. Again, this\n  // breaks the rules of React, and only works here because of specific\n  // implementation details, most importantly that updates are\n  // always synchronous.\n\n\n  const value = getSnapshot();\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!didWarnUncachedGetSnapshot) {\n      const cachedValue = getSnapshot();\n\n      if (!is(value, cachedValue)) {\n        console.error(\"The result of getSnapshot should be cached to avoid an infinite loop\");\n        didWarnUncachedGetSnapshot = true;\n      }\n    }\n  } // Because updates are synchronous, we don't queue them. Instead we force a\n  // re-render whenever the subscribed state changes by updating an some\n  // arbitrary useState hook. Then, during render, we call getSnapshot to read\n  // the current value.\n  //\n  // Because we don't actually use the state returned by the useState hook, we\n  // can save a bit of memory by storing other stuff in that slot.\n  //\n  // To implement the early bailout, we need to track some things on a mutable\n  // object. Usually, we would put that in a useRef hook, but we can stash it in\n  // our useState hook instead.\n  //\n  // To force a re-render, we call forceUpdate({inst}). That works because the\n  // new object always fails an equality check.\n\n\n  const [{\n    inst\n  }, forceUpdate] = useState({\n    inst: {\n      value,\n      getSnapshot\n    }\n  }); // Track the latest getSnapshot function with a ref. This needs to be updated\n  // in the layout phase so we can access it during the tearing check that\n  // happens on subscribe.\n\n  useLayoutEffect(() => {\n    inst.value = value;\n    inst.getSnapshot = getSnapshot; // Whenever getSnapshot or subscribe changes, we need to check in the\n    // commit phase if there was an interleaved mutation. In concurrent mode\n    // this can happen all the time, but even in synchronous mode, an earlier\n    // effect may have mutated the store.\n\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst\n      });\n    } // eslint-disable-next-line react-hooks/exhaustive-deps\n\n  }, [subscribe, value, getSnapshot]);\n  useEffect(() => {\n    // Check for changes right before subscribing. Subsequent changes will be\n    // detected in the subscription handler.\n    if (checkIfSnapshotChanged(inst)) {\n      // Force a re-render.\n      forceUpdate({\n        inst\n      });\n    }\n\n    const handleStoreChange = () => {\n      // TODO: Because there is no cross-renderer API for batching updates, it's\n      // up to the consumer of this library to wrap their subscription event\n      // with unstable_batchedUpdates. Should we try to detect when this isn't\n      // the case and print a warning in development?\n      // The store changed. Check if the snapshot changed since the last time we\n      // read from the store.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({\n          inst\n        });\n      }\n    }; // Subscribe to the store and return a clean-up function.\n\n\n    return subscribe(handleStoreChange); // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribe]);\n  useDebugValue(value);\n  return value;\n}\n\nfunction checkIfSnapshotChanged(inst) {\n  const latestGetSnapshot = inst.getSnapshot;\n  const prevValue = inst.value;\n\n  try {\n    const nextValue = latestGetSnapshot();\n    return !is(prevValue, nextValue);\n  } catch (error) {\n    return true;\n  }\n}\n\n/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\nfunction useSyncExternalStore$1(subscribe, getSnapshot, getServerSnapshot) {\n  // Note: The shim does not use getServerSnapshot, because pre-18 versions of\n  // React do not expose a way to check if we're hydrating. So users of the shim\n  // will need to track that themselves and return the correct value\n  // from `getSnapshot`.\n  return getSnapshot();\n}\n\n/**\n * Inlined into the react-router repo since use-sync-external-store does not\n * provide a UMD-compatible package, so we need this to be able to distribute\n * UMD react-router bundles\n */\nconst canUseDOM = !!(typeof window !== \"undefined\" && typeof window.document !== \"undefined\" && typeof window.document.createElement !== \"undefined\");\nconst isServerEnvironment = !canUseDOM;\nconst shim = isServerEnvironment ? useSyncExternalStore$1 : useSyncExternalStore$2;\nconst useSyncExternalStore = \"useSyncExternalStore\" in React ? (module => module.useSyncExternalStore)(React) : shim;\n\nconst DataRouterContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterContext.displayName = \"DataRouter\";\n}\n\nconst DataRouterStateContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\n\nconst AwaitContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  AwaitContext.displayName = \"Await\";\n}\n\nconst NavigationContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  NavigationContext.displayName = \"Navigation\";\n}\n\nconst LocationContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  LocationContext.displayName = \"Location\";\n}\n\nconst RouteContext = /*#__PURE__*/React.createContext({\n  outlet: null,\n  matches: []\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  RouteContext.displayName = \"Route\";\n}\n\nconst RouteErrorContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== \"production\") {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/hooks/use-href\n */\n\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname; // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : joinPaths([basename, pathname]);\n  }\n\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n/**\n * Returns true if this component is a descendant of a <Router>.\n *\n * @see https://reactrouter.com/hooks/use-in-router-context\n */\n\nfunction useInRouterContext() {\n  return React.useContext(LocationContext) != null;\n}\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/hooks/use-location\n */\n\nfunction useLocation() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  return React.useContext(LocationContext).location;\n}\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/hooks/use-navigation-type\n */\n\nfunction useNavigationType() {\n  return React.useContext(LocationContext).navigationType;\n}\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * <NavLink>.\n *\n * @see https://reactrouter.com/hooks/use-match\n */\n\nfunction useMatch(pattern) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return React.useMemo(() => matchPath(pattern, pathname), [pathname, pattern]);\n}\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\n/**\n * Returns an imperative method for changing the location. Used by <Link>s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/hooks/use-navigate\n */\nfunction useNavigate() {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    basename,\n    navigator\n  } = React.useContext(NavigationContext);\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getPathContributingMatches(matches).map(match => match.pathnameBase));\n  let activeRef = React.useRef(false);\n  React.useEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = React.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(activeRef.current, \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\") : void 0;\n    if (!activeRef.current) return;\n\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n\n    let path = resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\"); // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history.  If this is a root navigation, then we\n    // navigate to the raw basename which allows the basename to have full\n    // control over the presence of a trailing slash on root links\n\n    if (basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n    }\n\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/React.createContext(null);\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/hooks/use-outlet-context\n */\n\nfunction useOutletContext() {\n  return React.useContext(OutletContext);\n}\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by <Outlet> to render child routes.\n *\n * @see https://reactrouter.com/hooks/use-outlet\n */\n\nfunction useOutlet(context) {\n  let outlet = React.useContext(RouteContext).outlet;\n\n  if (outlet) {\n    return /*#__PURE__*/React.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n\n  return outlet;\n}\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/hooks/use-params\n */\n\nfunction useParams() {\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/hooks/use-resolved-path\n */\n\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    matches\n  } = React.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify(UNSAFE_getPathContributingMatches(matches).map(match => match.pathnameBase));\n  return React.useMemo(() => resolveTo(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an <Outlet> to render their child route's\n * element.\n *\n * @see https://reactrouter.com/hooks/use-routes\n */\n\nfunction useRoutes(routes, locationArg) {\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  let {\n    navigator\n  } = React.useContext(NavigationContext);\n  let dataRouterStateContext = React.useContext(DataRouterStateContext);\n  let {\n    matches: parentMatches\n  } = React.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n\n  let locationFromContext = useLocation();\n  let location;\n\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n\n    let parsedLocationArg = typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : invariant(false) : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = parentPathnameBase === \"/\" ? pathname : pathname.slice(parentPathnameBase.length) || \"/\";\n  let matches = matchRoutes(routes, {\n    pathname: remainingPathname\n  });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    process.env.NODE_ENV !== \"production\" ? warning(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : void 0;\n    process.env.NODE_ENV !== \"production\" ? warning(matches == null || matches[matches.length - 1].route.element !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" does not have an element. \" + \"This means it will render an <Outlet /> with a null value by default resulting in an \\\"empty\\\" page.\") : void 0;\n  }\n\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: joinPaths([parentPathnameBase, // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : joinPaths([parentPathnameBase, // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterStateContext || undefined); // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n\n\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/React.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: Action.Pop\n      }\n    }, renderedMatches);\n  }\n\n  return renderedMatches;\n}\n\nfunction DefaultErrorElement() {\n  let error = useRouteError();\n  let message = isRouteErrorResponse(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"h2\", null, \"Unhandled Thrown Error!\"), /*#__PURE__*/React.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/React.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, /*#__PURE__*/React.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/React.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own\\xA0\", /*#__PURE__*/React.createElement(\"code\", {\n    style: codeStyles\n  }, \"errorElement\"), \" props on\\xA0\", /*#__PURE__*/React.createElement(\"code\", {\n    style: codeStyles\n  }, \"<Route>\")));\n}\n\nclass RenderErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      error: props.error\n    };\n  }\n\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location) {\n      return {\n        error: props.error,\n        location: props.location\n      };\n    } // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n\n\n    return {\n      error: props.error || state.error,\n      location: state.location\n    };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n\n  render() {\n    return this.state.error ? /*#__PURE__*/React.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/React.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n\n}\n\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = React.useContext(DataRouterContext); // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && match.route.errorElement) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n\n  return /*#__PURE__*/React.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\n\nfunction _renderMatches(matches, parentMatches, dataRouterState) {\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n\n  if (matches == null) {\n    if (dataRouterState != null && dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n\n  let renderedMatches = matches; // If we have data errors, trim matches to the highest error boundary\n\n  let errors = dataRouterState == null ? void 0 : dataRouterState.errors;\n\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]));\n    !(errorIndex >= 0) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not find a matching route for the current errors: \" + errors) : invariant(false) : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    let error = match.route.id ? errors == null ? void 0 : errors[match.route.id] : null; // Only data routers handle errors\n\n    let errorElement = dataRouterState ? match.route.errorElement || /*#__PURE__*/React.createElement(DefaultErrorElement, null) : null;\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n\n    let getChildren = () => /*#__PURE__*/React.createElement(RenderedRoute, {\n      match: match,\n      routeContext: {\n        outlet,\n        matches\n      }\n    }, error ? errorElement : match.route.element !== undefined ? match.route.element : outlet); // Only wrap in an error boundary within data router usages when we have an\n    // errorElement on this route.  Otherwise let it bubble up to an ancestor\n    // errorElement\n\n\n    return dataRouterState && (match.route.errorElement || index === 0) ? /*#__PURE__*/React.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook;\n\n(function (DataRouterHook) {\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n})(DataRouterHook || (DataRouterHook = {}));\n\nvar DataRouterStateHook;\n\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\n\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\n\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return ctx;\n}\n\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return state;\n}\n\nfunction useRouteContext(hookName) {\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return route;\n}\n\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ? process.env.NODE_ENV !== \"production\" ? invariant(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : invariant(false) : void 0;\n  return thisRoute.route.id;\n}\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\n\n\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\n\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return {\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  };\n}\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\n\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return React.useMemo(() => matches.map(match => {\n    let {\n      pathname,\n      params\n    } = match; // Note: This structure matches that created by createUseMatchesMatch\n    // in the @remix-run/router , so if you change this please also change\n    // that :)  Eventually we'll DRY this up\n\n    return {\n      id: match.route.id,\n      pathname,\n      params,\n      data: loaderData[match.route.id],\n      handle: match.route.handle\n    };\n  }), [matches, loaderData]);\n}\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\n\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n\n  return state.loaderData[routeId];\n}\n/**\n * Returns the loaderData for the given routeId\n */\n\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n/**\n * Returns the action data for the nearest ancestor Route action\n */\n\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let route = React.useContext(RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useActionData must be used inside a RouteContext\") : invariant(false) : void 0;\n  return Object.values((state == null ? void 0 : state.actionData) || {})[0];\n}\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * errorElement to display a proper error message.\n */\n\nfunction useRouteError() {\n  var _state$errors;\n\n  let error = React.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError); // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n\n  if (error) {\n    return error;\n  } // Otherwise look for errors from our data router state\n\n\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n/**\n * Returns the happy-path data from the nearest ancestor <Await /> value\n */\n\nfunction useAsyncValue() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n/**\n * Returns the error from the nearest ancestor <Await /> value\n */\n\nfunction useAsyncError() {\n  let value = React.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nconst alreadyWarned = {};\n\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned[key]) {\n    alreadyWarned[key] = true;\n    process.env.NODE_ENV !== \"production\" ? warning(false, message) : void 0;\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router\n  } = _ref;\n  // Sync router state to our component state to force re-renders\n  let state = useSyncExternalStore(router.subscribe, () => router.state, // We have to provide this so React@18 doesn't complain during hydration,\n  // but we pass our serialized hydration data into the router so state here\n  // is already synced with what the server saw\n  () => router.state);\n  let navigator = React.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\"; // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DataRouterContext.Provider, {\n    value: {\n      router,\n      navigator,\n      static: false,\n      // Do we need this?\n      basename\n    }\n  }, /*#__PURE__*/React.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/React.createElement(Router, {\n    basename: router.basename,\n    location: router.state.location,\n    navigationType: router.state.historyAction,\n    navigator: navigator\n  }, router.state.initialized ? /*#__PURE__*/React.createElement(Routes, null) : fallbackElement))), null);\n}\n\n/**\n * A <Router> that stores all entries in memory.\n *\n * @see https://reactrouter.com/router-components/memory-router\n */\nfunction MemoryRouter(_ref2) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex\n  } = _ref2;\n  let historyRef = React.useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createMemoryHistory({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/components/navigate\n */\nfunction Navigate(_ref3) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref3;\n  !useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : invariant(false) : void 0;\n  process.env.NODE_ENV !== \"production\" ? warning(!React.useContext(NavigationContext).static, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : void 0;\n  let dataRouterState = React.useContext(DataRouterStateContext);\n  let navigate = useNavigate();\n  React.useEffect(() => {\n    // Avoid kicking off multiple navigations if we're in the middle of a\n    // data-router navigation, since components get re-rendered when we enter\n    // a submitting/loading state\n    if (dataRouterState && dataRouterState.navigation.state !== \"idle\") {\n      return;\n    }\n\n    navigate(to, {\n      replace,\n      state,\n      relative\n    });\n  });\n  return null;\n}\n\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/components/route\n */\nfunction Route(_props) {\n  process.env.NODE_ENV !== \"production\" ? invariant(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : invariant(false) ;\n}\n\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a <Router> directly. Instead, you'll render a\n * router that is more specific to your environment such as a <BrowserRouter>\n * in web browsers or a <StaticRouter> for server rendering.\n *\n * @see https://reactrouter.com/router-components/router\n */\nfunction Router(_ref4) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = Action.Pop,\n    navigator,\n    static: staticProp = false\n  } = _ref4;\n  !!useInRouterContext() ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : invariant(false) : void 0; // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = React.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp\n  }), [basename, navigator, staticProp]);\n\n  if (typeof locationProp === \"string\") {\n    locationProp = parsePath(locationProp);\n  }\n\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let location = React.useMemo(() => {\n    let trailingPathname = stripBasename(pathname, basename);\n\n    if (trailingPathname == null) {\n      return null;\n    }\n\n    return {\n      pathname: trailingPathname,\n      search,\n      hash,\n      state,\n      key\n    };\n  }, [basename, pathname, search, hash, state, key]);\n  process.env.NODE_ENV !== \"production\" ? warning(location != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : void 0;\n\n  if (location == null) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/React.createElement(LocationContext.Provider, {\n    children: children,\n    value: {\n      location,\n      navigationType\n    }\n  }));\n}\n\n/**\n * A container for a nested tree of <Route> elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/components/routes\n */\nfunction Routes(_ref5) {\n  let {\n    children,\n    location\n  } = _ref5;\n  let dataRouterContext = React.useContext(DataRouterContext); // When in a DataRouterContext _without_ children, we use the router routes\n  // directly.  If we have children, then we're in a descendant tree and we\n  // need to use child routes.\n\n  let routes = dataRouterContext && !children ? dataRouterContext.router.routes : createRoutesFromChildren(children);\n  return useRoutes(routes, location);\n}\n\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref6) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref6;\n  return /*#__PURE__*/React.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/React.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus;\n\n(function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n})(AwaitRenderStatus || (AwaitRenderStatus = {}));\n\nconst neverSettledPromise = new Promise(() => {});\n\nclass AwaitErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = promise._error !== undefined ? AwaitRenderStatus.error : promise._data !== undefined ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n\n    if (status === AwaitRenderStatus.error && promise._error instanceof AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/React.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    } // Throw to the suspense boundary\n\n\n    throw promise;\n  }\n\n}\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on <Await>\n */\n\n\nfunction ResolveAwait(_ref7) {\n  let {\n    children\n  } = _ref7;\n  let data = useAsyncValue();\n\n  if (typeof children === \"function\") {\n    return children(data);\n  }\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, children);\n} ///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/utils/create-routes-from-children\n */\n\n\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n\n  let routes = [];\n  React.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/React.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n\n    if (element.type === React.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, parentPath));\n      return;\n    }\n\n    !(element.type === Route) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : invariant(false) : void 0;\n    !(!element.props.index || !element.props.children) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"An index route cannot have child routes.\") : invariant(false) : void 0;\n    let treePath = [...parentPath, index];\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      hasErrorBoundary: element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle\n    };\n\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n\n    routes.push(route);\n  });\n  return routes;\n}\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\n\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n/**\n * @private\n * Walk the route tree and add hasErrorBoundary if it's not provided, so that\n * users providing manual route arrays can just specify errorElement\n */\n\nfunction enhanceManualRouteObjects(routes) {\n  return routes.map(route => {\n    let routeClone = _extends({}, route);\n\n    if (routeClone.hasErrorBoundary == null) {\n      routeClone.hasErrorBoundary = routeClone.errorElement != null;\n    }\n\n    if (routeClone.children) {\n      routeClone.children = enhanceManualRouteObjects(routeClone.children);\n    }\n\n    return routeClone;\n  });\n}\n\nfunction createMemoryRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createMemoryHistory({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes: enhanceManualRouteObjects(routes)\n  }).initialize();\n} ///////////////////////////////////////////////////////////////////////////////\n\nexport { Await, MemoryRouter, Navigate, Outlet, Route, Router, RouterProvider, Routes, DataRouterContext as UNSAFE_DataRouterContext, DataRouterStateContext as UNSAFE_DataRouterStateContext, LocationContext as UNSAFE_LocationContext, NavigationContext as UNSAFE_NavigationContext, RouteContext as UNSAFE_RouteContext, enhanceManualRouteObjects as UNSAFE_enhanceManualRouteObjects, createMemoryRouter, createRoutesFromChildren, createRoutesFromChildren as createRoutesFromElements, renderMatches, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes };\n//# sourceMappingURL=index.js.map\n", "/**\n * React Router DOM v6.6.2\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport * as React from 'react';\nimport { UNSAFE_enhanceManualRouteObjects, Router, useHref, useResolvedPath, useLocation, UNSAFE_DataRouterStateContext, UNSAFE_NavigationContext, useNavigate, createPath, UNSAFE_RouteContext, useMatches, useNavigation, UNSAFE_DataRouterContext } from 'react-router';\nexport { AbortedDeferredError, Await, MemoryRouter, Navigate, NavigationType, Outlet, Route, Router, RouterProvider, Routes, UNSAFE_DataRouterContext, UNSAFE_DataRouterStateContext, UNSAFE_LocationContext, UNSAFE_NavigationContext, UNSAFE_RouteContext, UNSAFE_enhanceManualRouteObjects, createMemoryRouter, createPath, createRoutesFromChildren, createRoutesFromElements, defer, generatePath, isRouteErrorResponse, json, matchPath, matchRoutes, parsePath, redirect, renderMatches, resolvePath, useActionData, useAsyncError, useAsyncValue, useHref, useInRouterContext, useLoaderData, useLocation, useMatch, useMatches, useNavigate, useNavigation, useNavigationType, useOutlet, useOutletContext, useParams, useResolvedPath, useRevalidator, useRouteError, useRouteLoaderData, useRoutes } from 'react-router';\nimport { createRouter, createBrowserHistory, createHashHistory, ErrorResponse, invariant, joinPaths } from '@remix-run/router';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nconst defaultMethod = \"get\";\nconst defaultEncType = \"application/x-www-form-urlencoded\";\nfunction isHtmlElement(object) {\n  return object != null && typeof object.tagName === \"string\";\n}\nfunction isButtonElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\nfunction isFormElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\nfunction isInputElement(object) {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\nfunction isModifiedEvent(event) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nfunction shouldProcessLinkClick(event, target) {\n  return event.button === 0 && ( // Ignore everything but left clicks\n  !target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n  !isModifiedEvent(event) // Ignore clicks with modifier keys\n  ;\n}\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\n\nfunction createSearchParams(init) {\n  if (init === void 0) {\n    init = \"\";\n  }\n\n  return new URLSearchParams(typeof init === \"string\" || Array.isArray(init) || init instanceof URLSearchParams ? init : Object.keys(init).reduce((memo, key) => {\n    let value = init[key];\n    return memo.concat(Array.isArray(value) ? value.map(v => [key, v]) : [[key, value]]);\n  }, []));\n}\nfunction getSearchParamsForLocation(locationSearch, defaultSearchParams) {\n  let searchParams = createSearchParams(locationSearch);\n\n  for (let key of defaultSearchParams.keys()) {\n    if (!searchParams.has(key)) {\n      defaultSearchParams.getAll(key).forEach(value => {\n        searchParams.append(key, value);\n      });\n    }\n  }\n\n  return searchParams;\n}\nfunction getFormSubmissionInfo(target, defaultAction, options) {\n  let method;\n  let action;\n  let encType;\n  let formData;\n\n  if (isFormElement(target)) {\n    let submissionTrigger = options.submissionTrigger;\n    method = options.method || target.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(target);\n\n    if (submissionTrigger && submissionTrigger.name) {\n      formData.append(submissionTrigger.name, submissionTrigger.value);\n    }\n  } else if (isButtonElement(target) || isInputElement(target) && (target.type === \"submit\" || target.type === \"image\")) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\"Cannot submit a <button> or <input type=\\\"submit\\\"> without a <form>\");\n    } // <button>/<input type=\"submit\"> may override attributes of <form>\n\n\n    method = options.method || target.getAttribute(\"formmethod\") || form.getAttribute(\"method\") || defaultMethod;\n    action = options.action || target.getAttribute(\"formaction\") || form.getAttribute(\"action\") || defaultAction;\n    encType = options.encType || target.getAttribute(\"formenctype\") || form.getAttribute(\"enctype\") || defaultEncType;\n    formData = new FormData(form); // Include name + value from a <button>, appending in case the button name\n    // matches an existing input name\n\n    if (target.name) {\n      formData.append(target.name, target.value);\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\"Cannot submit element that is not <form>, <button>, or \" + \"<input type=\\\"submit|image\\\">\");\n  } else {\n    method = options.method || defaultMethod;\n    action = options.action || defaultAction;\n    encType = options.encType || defaultEncType;\n\n    if (target instanceof FormData) {\n      formData = target;\n    } else {\n      formData = new FormData();\n\n      if (target instanceof URLSearchParams) {\n        for (let [name, value] of target) {\n          formData.append(name, value);\n        }\n      } else if (target != null) {\n        for (let name of Object.keys(target)) {\n          formData.append(name, target[name]);\n        }\n      }\n    }\n  }\n\n  let {\n    protocol,\n    host\n  } = window.location;\n  let url = new URL(action, protocol + \"//\" + host);\n  return {\n    url,\n    method: method.toLowerCase(),\n    encType,\n    formData\n  };\n}\n\nconst _excluded = [\"onClick\", \"relative\", \"reloadDocument\", \"replace\", \"state\", \"target\", \"to\", \"preventScrollReset\"],\n      _excluded2 = [\"aria-current\", \"caseSensitive\", \"className\", \"end\", \"style\", \"to\", \"children\"],\n      _excluded3 = [\"reloadDocument\", \"replace\", \"method\", \"action\", \"onSubmit\", \"fetcherKey\", \"routeId\", \"relative\"];\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\nfunction createBrowserRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createBrowserHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\nfunction createHashRouter(routes, opts) {\n  return createRouter({\n    basename: opts == null ? void 0 : opts.basename,\n    history: createHashHistory({\n      window: opts == null ? void 0 : opts.window\n    }),\n    hydrationData: (opts == null ? void 0 : opts.hydrationData) || parseHydrationData(),\n    routes: UNSAFE_enhanceManualRouteObjects(routes)\n  }).initialize();\n}\n\nfunction parseHydrationData() {\n  var _window;\n\n  let state = (_window = window) == null ? void 0 : _window.__staticRouterHydrationData;\n\n  if (state && state.errors) {\n    state = _extends({}, state, {\n      errors: deserializeErrors(state.errors)\n    });\n  }\n\n  return state;\n}\n\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponse(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      let error = new Error(val.message); // Wipe away the client-side stack trace.  Nothing to fill it in with\n      // because we don't serialize SSR stack traces for security reasons\n\n      error.stack = \"\";\n      serialized[key] = error;\n    } else {\n      serialized[key] = val;\n    }\n  }\n\n  return serialized;\n}\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\n\n\nfunction BrowserRouter(_ref) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref;\n  let historyRef = React.useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({\n      window,\n      v5Compat: true\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\n\nfunction HashRouter(_ref2) {\n  let {\n    basename,\n    children,\n    window\n  } = _ref2;\n  let historyRef = React.useRef();\n\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({\n      window,\n      v5Compat: true\n    });\n  }\n\n  let history = historyRef.current;\n  let [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\n\nfunction HistoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    history\n  } = _ref3;\n  const [state, setState] = React.useState({\n    action: history.action,\n    location: history.location\n  });\n  React.useLayoutEffect(() => history.listen(setState), [history]);\n  return /*#__PURE__*/React.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history\n  });\n}\n\nif (process.env.NODE_ENV !== \"production\") {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n/**\n * The public API for rendering a history-aware <a>.\n */\n\nconst Link = /*#__PURE__*/React.forwardRef(function LinkWithRef(_ref4, ref) {\n  let {\n    onClick,\n    relative,\n    reloadDocument,\n    replace,\n    state,\n    target,\n    to,\n    preventScrollReset\n  } = _ref4,\n      rest = _objectWithoutPropertiesLoose(_ref4, _excluded);\n\n  let href = useHref(to, {\n    relative\n  });\n  let internalOnClick = useLinkClickHandler(to, {\n    replace,\n    state,\n    target,\n    preventScrollReset,\n    relative\n  });\n\n  function handleClick(event) {\n    if (onClick) onClick(event);\n\n    if (!event.defaultPrevented) {\n      internalOnClick(event);\n    }\n  }\n\n  return (\n    /*#__PURE__*/\n    // eslint-disable-next-line jsx-a11y/anchor-has-content\n    React.createElement(\"a\", _extends({}, rest, {\n      href: href,\n      onClick: reloadDocument ? onClick : handleClick,\n      ref: ref,\n      target: target\n    }))\n  );\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  Link.displayName = \"Link\";\n}\n/**\n * A <Link> wrapper that knows if it's \"active\" or not.\n */\n\n\nconst NavLink = /*#__PURE__*/React.forwardRef(function NavLinkWithRef(_ref5, ref) {\n  let {\n    \"aria-current\": ariaCurrentProp = \"page\",\n    caseSensitive = false,\n    className: classNameProp = \"\",\n    end = false,\n    style: styleProp,\n    to,\n    children\n  } = _ref5,\n      rest = _objectWithoutPropertiesLoose(_ref5, _excluded2);\n\n  let path = useResolvedPath(to, {\n    relative: rest.relative\n  });\n  let location = useLocation();\n  let routerState = React.useContext(UNSAFE_DataRouterStateContext);\n  let {\n    navigator\n  } = React.useContext(UNSAFE_NavigationContext);\n  let toPathname = navigator.encodeLocation ? navigator.encodeLocation(path).pathname : path.pathname;\n  let locationPathname = location.pathname;\n  let nextLocationPathname = routerState && routerState.navigation && routerState.navigation.location ? routerState.navigation.location.pathname : null;\n\n  if (!caseSensitive) {\n    locationPathname = locationPathname.toLowerCase();\n    nextLocationPathname = nextLocationPathname ? nextLocationPathname.toLowerCase() : null;\n    toPathname = toPathname.toLowerCase();\n  }\n\n  let isActive = locationPathname === toPathname || !end && locationPathname.startsWith(toPathname) && locationPathname.charAt(toPathname.length) === \"/\";\n  let isPending = nextLocationPathname != null && (nextLocationPathname === toPathname || !end && nextLocationPathname.startsWith(toPathname) && nextLocationPathname.charAt(toPathname.length) === \"/\");\n  let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n  let className;\n\n  if (typeof classNameProp === \"function\") {\n    className = classNameProp({\n      isActive,\n      isPending\n    });\n  } else {\n    // If the className prop is not a function, we use a default `active`\n    // class for <NavLink />s that are active. In v5 `active` was the default\n    // value for `activeClassName`, but we are removing that API and can still\n    // use the old default behavior for a cleaner upgrade path and keep the\n    // simple styling rules working as they currently do.\n    className = [classNameProp, isActive ? \"active\" : null, isPending ? \"pending\" : null].filter(Boolean).join(\" \");\n  }\n\n  let style = typeof styleProp === \"function\" ? styleProp({\n    isActive,\n    isPending\n  }) : styleProp;\n  return /*#__PURE__*/React.createElement(Link, _extends({}, rest, {\n    \"aria-current\": ariaCurrent,\n    className: className,\n    ref: ref,\n    style: style,\n    to: to\n  }), typeof children === \"function\" ? children({\n    isActive,\n    isPending\n  }) : children);\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  NavLink.displayName = \"NavLink\";\n}\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\n\n\nconst Form = /*#__PURE__*/React.forwardRef((props, ref) => {\n  return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n    ref: ref\n  }));\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  Form.displayName = \"Form\";\n}\n\nconst FormImpl = /*#__PURE__*/React.forwardRef((_ref6, forwardedRef) => {\n  let {\n    reloadDocument,\n    replace,\n    method = defaultMethod,\n    action,\n    onSubmit,\n    fetcherKey,\n    routeId,\n    relative\n  } = _ref6,\n      props = _objectWithoutPropertiesLoose(_ref6, _excluded3);\n\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let formMethod = method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n  let formAction = useFormAction(action, {\n    relative\n  });\n\n  let submitHandler = event => {\n    onSubmit && onSubmit(event);\n    if (event.defaultPrevented) return;\n    event.preventDefault();\n    let submitter = event.nativeEvent.submitter;\n    let submitMethod = (submitter == null ? void 0 : submitter.getAttribute(\"formmethod\")) || method;\n    submit(submitter || event.currentTarget, {\n      method: submitMethod,\n      replace,\n      relative\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(\"form\", _extends({\n    ref: forwardedRef,\n    method: formMethod,\n    action: formAction,\n    onSubmit: reloadDocument ? onSubmit : submitHandler\n  }, props));\n});\n\nif (process.env.NODE_ENV !== \"production\") {\n  FormImpl.displayName = \"FormImpl\";\n}\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\n\n\nfunction ScrollRestoration(_ref7) {\n  let {\n    getKey,\n    storageKey\n  } = _ref7;\n  useScrollRestoration({\n    getKey,\n    storageKey\n  });\n  return null;\n}\n\nif (process.env.NODE_ENV !== \"production\") {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar DataRouterHook;\n\n(function (DataRouterHook) {\n  DataRouterHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n  DataRouterHook[\"UseSubmitImpl\"] = \"useSubmitImpl\";\n  DataRouterHook[\"UseFetcher\"] = \"useFetcher\";\n})(DataRouterHook || (DataRouterHook = {}));\n\nvar DataRouterStateHook;\n\n(function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseFetchers\"] = \"useFetchers\";\n  DataRouterStateHook[\"UseScrollRestoration\"] = \"useScrollRestoration\";\n})(DataRouterStateHook || (DataRouterStateHook = {}));\n\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.\";\n}\n\nfunction useDataRouterContext(hookName) {\n  let ctx = React.useContext(UNSAFE_DataRouterContext);\n  !ctx ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return ctx;\n}\n\nfunction useDataRouterState(hookName) {\n  let state = React.useContext(UNSAFE_DataRouterStateContext);\n  !state ? process.env.NODE_ENV !== \"production\" ? invariant(false, getDataRouterConsoleError(hookName)) : invariant(false) : void 0;\n  return state;\n}\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\n\n\nfunction useLinkClickHandler(to, _temp) {\n  let {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, {\n    relative\n  });\n  return React.useCallback(event => {\n    if (shouldProcessLinkClick(event, target)) {\n      event.preventDefault(); // If the URL hasn't changed, a regular <a> will do a replace instead of\n      // a push, so do the same here unless the replace prop is explicitly set\n\n      let replace = replaceProp !== undefined ? replaceProp : createPath(location) === createPath(path);\n      navigate(to, {\n        replace,\n        state,\n        preventScrollReset,\n        relative\n      });\n    }\n  }, [location, navigate, path, replaceProp, state, target, to, preventScrollReset, relative]);\n}\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\n\nfunction useSearchParams(defaultInit) {\n  process.env.NODE_ENV !== \"production\" ? warning(typeof URLSearchParams !== \"undefined\", \"You cannot use the `useSearchParams` hook in a browser that does not \" + \"support the URLSearchParams API. If you need to support Internet \" + \"Explorer 11, we recommend you load a polyfill such as \" + \"https://github.com/ungap/url-search-params\\n\\n\" + \"If you're unsure how to load polyfills, we recommend you check out \" + \"https://polyfill.io/v3/ which provides some recommendations about how \" + \"to load polyfills only for users that need them, instead of for every \" + \"user.\") : void 0;\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let location = useLocation();\n  let searchParams = React.useMemo(() => getSearchParamsForLocation(location.search, defaultSearchParamsRef.current), [location.search]);\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback((nextInit, navigateOptions) => {\n    const newSearchParams = createSearchParams(typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit);\n    navigate(\"?\" + newSearchParams, navigateOptions);\n  }, [navigate, searchParams]);\n  return [searchParams, setSearchParams];\n}\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\n\nfunction useSubmit() {\n  return useSubmitImpl();\n}\n\nfunction useSubmitImpl(fetcherKey, routeId) {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseSubmitImpl);\n  let defaultAction = useFormAction();\n  return React.useCallback(function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n\n    if (typeof document === \"undefined\") {\n      throw new Error(\"You are calling submit during the server render. \" + \"Try calling submit within a `useEffect` or callback instead.\");\n    }\n\n    let {\n      method,\n      encType,\n      formData,\n      url\n    } = getFormSubmissionInfo(target, defaultAction, options);\n    let href = url.pathname + url.search;\n    let opts = {\n      replace: options.replace,\n      formData,\n      formMethod: method,\n      formEncType: encType\n    };\n\n    if (fetcherKey) {\n      !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for useFetcher()\") : invariant(false) : void 0;\n      router.fetch(fetcherKey, routeId, href, opts);\n    } else {\n      router.navigate(href, opts);\n    }\n  }, [defaultAction, router, fetcherKey, routeId]);\n}\n\nfunction useFormAction(action, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    basename\n  } = React.useContext(UNSAFE_NavigationContext);\n  let routeContext = React.useContext(UNSAFE_RouteContext);\n  !routeContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFormAction must be used inside a RouteContext\") : invariant(false) : void 0;\n  let [match] = routeContext.matches.slice(-1); // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n\n  let path = _extends({}, useResolvedPath(action ? action : \".\", {\n    relative\n  })); // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params and the hash of the resolved\n  // URL. This is the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n\n\n  let location = useLocation();\n\n  if (action == null) {\n    // Safe to write to these directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    // or hash\n    path.search = location.search;\n    path.hash = location.hash; // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? \"?\" + params.toString() : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search ? path.search.replace(/^\\?/, \"?index&\") : \"?index\";\n  } // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n\n\n  if (basename !== \"/\") {\n    path.pathname = path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey, routeId) {\n  let FetcherForm = /*#__PURE__*/React.forwardRef((props, ref) => {\n    return /*#__PURE__*/React.createElement(FormImpl, _extends({}, props, {\n      ref: ref,\n      fetcherKey: fetcherKey,\n      routeId: routeId\n    }));\n  });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\n\nfunction useFetcher() {\n  var _route$matches;\n\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseFetcher);\n  let route = React.useContext(UNSAFE_RouteContext);\n  !route ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher must be used inside a RouteContext\") : invariant(false) : void 0;\n  let routeId = (_route$matches = route.matches[route.matches.length - 1]) == null ? void 0 : _route$matches.route.id;\n  !(routeId != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"useFetcher can only be used on routes that contain a unique \\\"id\\\"\") : invariant(false) : void 0;\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.Form()\") : invariant(false) : void 0;\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => href => {\n    !router ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No router available for fetcher.load()\") : invariant(false) : void 0;\n    !routeId ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"No routeId available for fetcher.load()\") : invariant(false) : void 0;\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitImpl(fetcherKey, routeId);\n  let fetcher = router.getFetcher(fetcherKey);\n  let fetcherWithComponents = React.useMemo(() => _extends({\n    Form,\n    submit,\n    load\n  }, fetcher), [fetcher, Form, submit, load]);\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(\"No fetcher available to clean up from useFetcher()\");\n        return;\n      }\n\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n  return fetcherWithComponents;\n}\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\n\nfunction useFetchers() {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions = {};\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\n\nfunction useScrollRestoration(_temp3) {\n  let {\n    getKey,\n    storageKey\n  } = _temp3 === void 0 ? {} : _temp3;\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let {\n    restoreScrollPosition,\n    preventScrollReset\n  } = useDataRouterState(DataRouterStateHook.UseScrollRestoration);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation(); // Trigger manual scroll restoration while we're active\n\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []); // Save positions on unload\n\n  useBeforeUnload(React.useCallback(() => {\n    if (navigation.state === \"idle\") {\n      let key = (getKey ? getKey(location, matches) : null) || location.key;\n      savedScrollPositions[key] = window.scrollY;\n    }\n\n    sessionStorage.setItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY, JSON.stringify(savedScrollPositions));\n    window.history.scrollRestoration = \"auto\";\n  }, [storageKey, getKey, navigation.state, location, matches])); // Read in any saved scroll locations\n\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(storageKey || SCROLL_RESTORATION_STORAGE_KEY);\n\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {// no-op, use default empty object\n      }\n    }, [storageKey]); // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n\n    React.useLayoutEffect(() => {\n      let disableScrollRestoration = router == null ? void 0 : router.enableScrollRestoration(savedScrollPositions, () => window.scrollY, getKey);\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, getKey]); // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      } // been here before, scroll to it\n\n\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      } // try to scroll to the hash\n\n\n      if (location.hash) {\n        let el = document.getElementById(location.hash.slice(1));\n\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      } // Opt out of scroll reset if this link requested it\n\n\n      if (preventScrollReset === true) {\n        return;\n      } // otherwise go to the top on new locations\n\n\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\n\n\nfunction useBeforeUnload(callback) {\n  React.useEffect(() => {\n    window.addEventListener(\"beforeunload\", callback);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback);\n    };\n  }, [callback]);\n} //#endregion\n////////////////////////////////////////////////////////////////////////////////\n//#region Utils\n////////////////////////////////////////////////////////////////////////////////\n\nfunction warning(cond, message) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging React Router!\n      //\n      // This error is thrown as a convenience so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message); // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n} //#endregion\n\nexport { BrowserRouter, Form, HashRouter, Link, NavLink, ScrollRestoration, useScrollRestoration as UNSAFE_useScrollRestoration, createBrowserRouter, createHashRouter, createSearchParams, HistoryRouter as unstable_HistoryRouter, useBeforeUnload, useFetcher, useFetchers, useFormAction, useLinkClickHandler, useSearchParams, useSubmit };\n//# sourceMappingURL=index.js.map\n"], "names": ["_extends", "target", "i", "source", "key", "Action", "PopStateEventType", "createBrowserHistory", "options", "createBrowserLocation", "window", "globalHistory", "pathname", "search", "hash", "createLocation", "createBrowserHref", "to", "createPath", "getUrlBasedHistory", "invariant", "value", "message", "create<PERSON><PERSON>", "getHistoryState", "location", "current", "state", "parsePath", "_ref", "path", "parsed<PERSON><PERSON>", "hashIndex", "searchIndex", "createClientSideURL", "base", "href", "getLocation", "createHref", "validateLocation", "v5Compat", "action", "listener", "handlePop", "history", "push", "historyState", "url", "replace", "fn", "n", "ResultType", "matchRoutes", "routes", "locationArg", "basename", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "matchRouteBranch", "safelyDecodeURI", "parents<PERSON>eta", "parentPath", "flattenRoute", "route", "index", "relativePath", "meta", "joinPaths", "routesMeta", "computeScore", "_route$path", "exploded", "explodeOptionalSegments", "segments", "first", "rest", "isOptional", "required", "restExploded", "result", "subpath", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "score", "segment", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "match", "matchPath", "normalizePathname", "pattern", "matcher", "paramNames", "compilePath", "pathnameBase", "captureGroups", "memo", "paramName", "splatValue", "safelyDecodeURIComponent", "caseSensitive", "warning", "regexpSource", "_", "error", "startIndex", "nextChar", "cond", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "paths", "ErrorResponse", "status", "statusText", "data", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validRequestMethodsArr", "isPolyfill", "x", "y", "is", "useState", "useEffect", "useLayoutEffect", "useDebugValue", "React", "useSyncExternalStore$2", "subscribe", "getSnapshot", "getServerSnapshot", "inst", "forceUpdate", "checkIfSnapshotChanged", "latestGetSnapshot", "prevValue", "nextValue", "useSyncExternalStore$1", "canUseDOM", "isServerEnvironment", "shim", "module", "DataRouterContext", "React.createContext", "DataRouterStateContext", "NavigationContext", "LocationContext", "RouteContext", "RouteErrorContext", "useHref", "_temp", "relative", "useInRouterContext", "navigator", "React.useContext", "useResolvedPath", "joinedPathname", "useLocation", "useNavigate", "routePathnamesJson", "UNSAFE_getPathContributingMatches", "activeRef", "React.useRef", "React.useEffect", "React.useCallback", "OutletContext", "useOutlet", "context", "outlet", "React.createElement", "useParams", "routeMatch", "_temp2", "React.useMemo", "useRoutes", "dataRouterStateContext", "parentMatches", "parentParams", "parentPathnameBase", "locationFromContext", "_parsedLocationArg$pa", "parsedLocationArg", "renderedMatches", "_renderMatches", "DefaultErrorElement", "useRouteError", "stack", "<PERSON><PERSON>rey", "preStyles", "codeStyles", "React.Fragment", "RenderErrorBoundary", "React.Component", "props", "errorInfo", "RenderedRoute", "routeContext", "children", "dataRouterContext", "dataRouterState", "errors", "errorIndex", "m", "errorElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DataRouterHook", "DataRouterStateHook", "useDataRouterState", "<PERSON><PERSON><PERSON>", "useRouteContext", "useCurrentRouteId", "thisRoute", "_state$errors", "routeId", "Navigate", "_ref3", "navigate", "Outlet", "Route", "_props", "Router", "_ref4", "basenameProp", "locationProp", "navigationType", "staticProp", "navigationContext", "trailingPathname", "Routes", "_ref5", "createRoutesFromChildren", "AwaitRenderStatus", "React.Children", "element", "React.isValidElement", "treePath", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "isModifiedEvent", "event", "shouldProcessLinkClick", "_excluded", "_excluded2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "historyRef", "setState", "React.useState", "React.useLayoutEffect", "Link", "React.forwardRef", "ref", "onClick", "reloadDocument", "preventScrollReset", "internalOnClick", "useLinkClickHandler", "handleClick", "NavLink", "ariaCurrentProp", "classNameProp", "styleProp", "routerState", "UNSAFE_DataRouterStateContext", "UNSAFE_NavigationContext", "nextLocationPathname", "isActive", "isPending", "aria<PERSON>urrent", "className", "style", "replaceProp"], "mappings": "+CAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAUA,SAASA,GAAW,CAClBA,OAAAA,EAAW,OAAO,OAAS,OAAO,OAAO,KAAI,EAAK,SAAUC,EAAQ,CAClE,QAASC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,EAExB,QAASE,KAAOD,EACV,OAAO,UAAU,eAAe,KAAKA,EAAQC,CAAG,IAClDH,EAAOG,CAAG,EAAID,EAAOC,CAAG,GAK9B,OAAOH,CACX,EACSD,EAAS,MAAM,KAAM,SAAS,CACvC,CASA,IAAIK,GAEH,SAAUA,EAAQ,CAQjBA,EAAO,IAAS,MAOhBA,EAAO,KAAU,OAMjBA,EAAO,QAAa,SACtB,GAAGA,IAAWA,EAAS,CAAE,EAAC,EAE1B,MAAMC,EAAoB,WA4H1B,SAASC,GAAqBC,EAAS,CACjCA,IAAY,SACdA,EAAU,CAAA,GAGZ,SAASC,EAAsBC,EAAQC,EAAe,CACpD,GAAI,CACF,SAAAC,EACA,OAAAC,EACA,KAAAC,CACN,EAAQJ,EAAO,SACX,OAAOK,EAAe,GAAI,CACxB,SAAAH,EACA,OAAAC,EACA,KAAAC,CACD,EACDH,EAAc,OAASA,EAAc,MAAM,KAAO,KAAMA,EAAc,OAASA,EAAc,MAAM,KAAO,SAAS,CACpH,CAED,SAASK,EAAkBN,EAAQO,EAAI,CACrC,OAAO,OAAOA,GAAO,SAAWA,EAAKC,EAAWD,CAAE,CACnD,CAED,OAAOE,GAAmBV,EAAuBO,EAAmB,KAAMR,CAAO,CACnF,CAgDA,SAASY,EAAUC,EAAOC,EAAS,CACjC,GAAID,IAAU,IAASA,IAAU,MAAQ,OAAOA,EAAU,IACxD,MAAM,IAAI,MAAMC,CAAO,CAE3B,CAkBA,SAASC,IAAY,CACnB,OAAO,KAAK,OAAM,EAAG,SAAS,EAAE,EAAE,OAAO,EAAG,CAAC,CAC/C,CAMA,SAASC,EAAgBC,EAAU,CACjC,MAAO,CACL,IAAKA,EAAS,MACd,IAAKA,EAAS,GAClB,CACA,CAMA,SAASV,EAAeW,EAAST,EAAIU,EAAOvB,EAAK,CAC/C,OAAIuB,IAAU,SACZA,EAAQ,MAGK3B,EAAS,CACtB,SAAU,OAAO0B,GAAY,SAAWA,EAAUA,EAAQ,SAC1D,OAAQ,GACR,KAAM,EACV,EAAK,OAAOT,GAAO,SAAWW,EAAUX,CAAE,EAAIA,EAAI,CAC9C,MAAAU,EAKA,IAAKV,GAAMA,EAAG,KAAOb,GAAOmB,GAAW,CAC3C,CAAG,CAGH,CAKA,SAASL,EAAWW,EAAM,CACxB,GAAI,CACF,SAAAjB,EAAW,IACX,OAAAC,EAAS,GACT,KAAAC,EAAO,EACR,EAAGe,EACJ,OAAIhB,GAAUA,IAAW,MAAKD,GAAYC,EAAO,OAAO,CAAC,IAAM,IAAMA,EAAS,IAAMA,GAChFC,GAAQA,IAAS,MAAKF,GAAYE,EAAK,OAAO,CAAC,IAAM,IAAMA,EAAO,IAAMA,GACrEF,CACT,CAKA,SAASgB,EAAUE,EAAM,CACvB,IAAIC,EAAa,CAAA,EAEjB,GAAID,EAAM,CACR,IAAIE,EAAYF,EAAK,QAAQ,GAAG,EAE5BE,GAAa,IACfD,EAAW,KAAOD,EAAK,OAAOE,CAAS,EACvCF,EAAOA,EAAK,OAAO,EAAGE,CAAS,GAGjC,IAAIC,EAAcH,EAAK,QAAQ,GAAG,EAE9BG,GAAe,IACjBF,EAAW,OAASD,EAAK,OAAOG,CAAW,EAC3CH,EAAOA,EAAK,OAAO,EAAGG,CAAW,GAG/BH,IACFC,EAAW,SAAWD,GAI1B,OAAOC,CACT,CACA,SAASG,GAAoBT,EAAU,CAIrC,IAAIU,EAAO,OAAO,OAAW,KAAe,OAAO,OAAO,SAAa,KAAe,OAAO,SAAS,SAAW,OAAS,OAAO,SAAS,OAAS,OAAO,SAAS,KAC/JC,EAAO,OAAOX,GAAa,SAAWA,EAAWP,EAAWO,CAAQ,EACxE,OAAAL,EAAUe,EAAM,sEAAwEC,CAAI,EACrF,IAAI,IAAIA,EAAMD,CAAI,CAC3B,CAEA,SAAShB,GAAmBkB,EAAaC,EAAYC,EAAkB/B,EAAS,CAC1EA,IAAY,SACdA,EAAU,CAAA,GAGZ,GAAI,CACF,OAAAE,EAAS,SAAS,YAClB,SAAA8B,EAAW,EACZ,EAAGhC,EACAG,EAAgBD,EAAO,QACvB+B,EAASpC,EAAO,IAChBqC,EAAW,KAEf,SAASC,GAAY,CACnBF,EAASpC,EAAO,IAEZqC,GACFA,EAAS,CACP,OAAAD,EACA,SAAUG,EAAQ,QAC1B,CAAO,CAEJ,CAED,SAASC,EAAK5B,EAAIU,EAAO,CACvBc,EAASpC,EAAO,KAChB,IAAIoB,EAAWV,EAAe6B,EAAQ,SAAU3B,EAAIU,CAAK,EACrDY,GAAkBA,EAAiBd,EAAUR,CAAE,EACnD,IAAI6B,EAAetB,EAAgBC,CAAQ,EACvCsB,EAAMH,EAAQ,WAAWnB,CAAQ,EAErC,GAAI,CACFd,EAAc,UAAUmC,EAAc,GAAIC,CAAG,CAC9C,MAAC,CAGArC,EAAO,SAAS,OAAOqC,CAAG,CAC3B,CAEGP,GAAYE,GACdA,EAAS,CACP,OAAAD,EACA,SAAUG,EAAQ,QAC1B,CAAO,CAEJ,CAED,SAASI,EAAQ/B,EAAIU,EAAO,CAC1Bc,EAASpC,EAAO,QAChB,IAAIoB,EAAWV,EAAe6B,EAAQ,SAAU3B,EAAIU,CAAK,EACrDY,GAAkBA,EAAiBd,EAAUR,CAAE,EACnD,IAAI6B,EAAetB,EAAgBC,CAAQ,EACvCsB,EAAMH,EAAQ,WAAWnB,CAAQ,EACrCd,EAAc,aAAamC,EAAc,GAAIC,CAAG,EAE5CP,GAAYE,GACdA,EAAS,CACP,OAAAD,EACA,SAAUG,EAAQ,QAC1B,CAAO,CAEJ,CAED,IAAIA,EAAU,CACZ,IAAI,QAAS,CACX,OAAOH,CACR,EAED,IAAI,UAAW,CACb,OAAOJ,EAAY3B,EAAQC,CAAa,CACzC,EAED,OAAOsC,EAAI,CACT,GAAIP,EACF,MAAM,IAAI,MAAM,4CAA4C,EAG9D,OAAAhC,EAAO,iBAAiBJ,EAAmBqC,CAAS,EACpDD,EAAWO,EACJ,IAAM,CACXvC,EAAO,oBAAoBJ,EAAmBqC,CAAS,EACvDD,EAAW,IACnB,CACK,EAED,WAAWzB,EAAI,CACb,OAAOqB,EAAW5B,EAAQO,CAAE,CAC7B,EAED,eAAeA,EAAI,CAEjB,IAAI8B,EAAMb,GAAoB,OAAOjB,GAAO,SAAWA,EAAKC,EAAWD,CAAE,CAAC,EAC1E,MAAO,CACL,SAAU8B,EAAI,SACd,OAAQA,EAAI,OACZ,KAAMA,EAAI,IAClB,CACK,EAED,KAAAF,EACA,QAAAG,EAEA,GAAGE,EAAG,CACJ,OAAOvC,EAAc,GAAGuC,CAAC,CAC1B,CAEL,EACE,OAAON,CACT,CAEA,IAAIO,GAEH,SAAUA,EAAY,CACrBA,EAAW,KAAU,OACrBA,EAAW,SAAc,WACzBA,EAAW,SAAc,WACzBA,EAAW,MAAW,OACxB,GAAGA,IAAeA,EAAa,CAAE,EAAC,EA8ClC,SAASC,GAAYC,EAAQC,EAAaC,EAAU,CAC9CA,IAAa,SACfA,EAAW,KAGb,IAAI9B,EAAW,OAAO6B,GAAgB,SAAW1B,EAAU0B,CAAW,EAAIA,EACtE1C,EAAW4C,GAAc/B,EAAS,UAAY,IAAK8B,CAAQ,EAE/D,GAAI3C,GAAY,KACd,OAAO,KAGT,IAAI6C,EAAWC,EAAcL,CAAM,EACnCM,GAAkBF,CAAQ,EAC1B,IAAIG,EAAU,KAEd,QAAS,EAAI,EAAGA,GAAW,MAAQ,EAAIH,EAAS,OAAQ,EAAE,EACxDG,EAAUC,GAAiBJ,EAAS,CAAC,EAMrCK,GAAgBlD,CAAQ,CAAC,EAG3B,OAAOgD,CACT,CAEA,SAASF,EAAcL,EAAQI,EAAUM,EAAaC,EAAY,CAC5DP,IAAa,SACfA,EAAW,CAAA,GAGTM,IAAgB,SAClBA,EAAc,CAAA,GAGZC,IAAe,SACjBA,EAAa,IAGf,IAAIC,EAAe,CAACC,EAAOC,EAAOC,IAAiB,CACjD,IAAIC,EAAO,CACT,aAAcD,IAAiB,OAAYF,EAAM,MAAQ,GAAKE,EAC9D,cAAeF,EAAM,gBAAkB,GACvC,cAAeC,EACf,MAAAD,CACN,EAEQG,EAAK,aAAa,WAAW,GAAG,IAClCjD,EAAUiD,EAAK,aAAa,WAAWL,CAAU,EAAG,wBAA2BK,EAAK,aAAe,wBAA2B,IAAOL,EAAa,iDAAoD,6DAA6D,EACnQK,EAAK,aAAeA,EAAK,aAAa,MAAML,EAAW,MAAM,GAG/D,IAAIlC,EAAOwC,EAAU,CAACN,EAAYK,EAAK,YAAY,CAAC,EAChDE,EAAaR,EAAY,OAAOM,CAAI,EAIpCH,EAAM,UAAYA,EAAM,SAAS,OAAS,IAC5C9C,EAEA8C,EAAM,QAAU,GAAM,2DAA6D,qCAAwCpC,EAAO,KAAM,EACxI4B,EAAcQ,EAAM,SAAUT,EAAUc,EAAYzC,CAAI,GAKtD,EAAAoC,EAAM,MAAQ,MAAQ,CAACA,EAAM,QAIjCT,EAAS,KAAK,CACZ,KAAA3B,EACA,MAAO0C,GAAa1C,EAAMoC,EAAM,KAAK,EACrC,WAAAK,CACN,CAAK,CACL,EAEE,OAAAlB,EAAO,QAAQ,CAACa,EAAOC,IAAU,CAC/B,IAAIM,EAGJ,GAAIP,EAAM,OAAS,IAAM,GAAGO,EAAcP,EAAM,OAAS,MAAQO,EAAY,SAAS,GAAG,GACvFR,EAAaC,EAAOC,CAAK,MAEzB,SAASO,KAAYC,EAAwBT,EAAM,IAAI,EACrDD,EAAaC,EAAOC,EAAOO,CAAQ,CAG3C,CAAG,EACMjB,CACT,CAiBA,SAASkB,EAAwB7C,EAAM,CACrC,IAAI8C,EAAW9C,EAAK,MAAM,GAAG,EAC7B,GAAI8C,EAAS,SAAW,EAAG,MAAO,CAAA,EAClC,GAAI,CAACC,EAAO,GAAGC,CAAI,EAAIF,EAEnBG,EAAaF,EAAM,SAAS,GAAG,EAE/BG,EAAWH,EAAM,QAAQ,MAAO,EAAE,EAEtC,GAAIC,EAAK,SAAW,EAGlB,OAAOC,EAAa,CAACC,EAAU,EAAE,EAAI,CAACA,CAAQ,EAGhD,IAAIC,EAAeN,EAAwBG,EAAK,KAAK,GAAG,CAAC,EACrDI,EAAS,CAAA,EAQb,OAAAA,EAAO,KAAK,GAAGD,EAAa,IAAIE,GAAWA,IAAY,GAAKH,EAAW,CAACA,EAAUG,CAAO,EAAE,KAAK,GAAG,CAAC,CAAC,EAEjGJ,GACFG,EAAO,KAAK,GAAGD,CAAY,EAItBC,EAAO,IAAIR,GAAY5C,EAAK,WAAW,GAAG,GAAK4C,IAAa,GAAK,IAAMA,CAAQ,CACxF,CAEA,SAASf,GAAkBF,EAAU,CACnCA,EAAS,KAAK,CAAC2B,EAAGC,IAAMD,EAAE,QAAUC,EAAE,MAAQA,EAAE,MAAQD,EAAE,MACxDE,GAAeF,EAAE,WAAW,IAAIf,GAAQA,EAAK,aAAa,EAAGgB,EAAE,WAAW,IAAIhB,GAAQA,EAAK,aAAa,CAAC,CAAC,CAC9G,CAEA,MAAMkB,GAAU,SACVC,GAAsB,EACtBC,GAAkB,EAClBC,GAAoB,EACpBC,GAAqB,GACrBC,GAAe,GAEfC,EAAUC,GAAKA,IAAM,IAE3B,SAAStB,GAAa1C,EAAMqC,EAAO,CACjC,IAAIS,EAAW9C,EAAK,MAAM,GAAG,EACzBiE,EAAenB,EAAS,OAE5B,OAAIA,EAAS,KAAKiB,CAAO,IACvBE,GAAgBH,IAGdzB,IACF4B,GAAgBN,IAGXb,EAAS,OAAOkB,GAAK,CAACD,EAAQC,CAAC,CAAC,EAAE,OAAO,CAACE,EAAOC,IAAYD,GAAST,GAAQ,KAAKU,CAAO,EAAIT,GAAsBS,IAAY,GAAKP,GAAoBC,IAAqBI,CAAY,CACnM,CAEA,SAAST,GAAeF,EAAGC,EAAG,CAE5B,OADeD,EAAE,SAAWC,EAAE,QAAUD,EAAE,MAAM,EAAG,EAAE,EAAE,MAAM,CAAC,EAAGlF,IAAM,IAAMmF,EAAEnF,CAAC,CAAC,EAKjFkF,EAAEA,EAAE,OAAS,CAAC,EAAIC,EAAEA,EAAE,OAAS,CAAC,EAEhC,CACF,CAEA,SAASxB,GAAiBqC,EAAQtF,EAAU,CAC1C,GAAI,CACF,WAAA2D,CACD,EAAG2B,EACAC,EAAgB,CAAA,EAChBC,EAAkB,IAClBxC,EAAU,CAAA,EAEd,QAAS1D,EAAI,EAAGA,EAAIqE,EAAW,OAAQ,EAAErE,EAAG,CAC1C,IAAImE,EAAOE,EAAWrE,CAAC,EACnBmG,EAAMnG,IAAMqE,EAAW,OAAS,EAChC+B,EAAoBF,IAAoB,IAAMxF,EAAWA,EAAS,MAAMwF,EAAgB,MAAM,GAAK,IACnGG,EAAQC,GAAU,CACpB,KAAMnC,EAAK,aACX,cAAeA,EAAK,cACpB,IAAAgC,CACD,EAAEC,CAAiB,EACpB,GAAI,CAACC,EAAO,OAAO,KACnB,OAAO,OAAOJ,EAAeI,EAAM,MAAM,EACzC,IAAIrC,EAAQG,EAAK,MACjBT,EAAQ,KAAK,CAEX,OAAQuC,EACR,SAAU7B,EAAU,CAAC8B,EAAiBG,EAAM,QAAQ,CAAC,EACrD,aAAcE,GAAkBnC,EAAU,CAAC8B,EAAiBG,EAAM,YAAY,CAAC,CAAC,EAChF,MAAArC,CACN,CAAK,EAEGqC,EAAM,eAAiB,MACzBH,EAAkB9B,EAAU,CAAC8B,EAAiBG,EAAM,YAAY,CAAC,GAIrE,OAAO3C,CACT,CA8CA,SAAS4C,GAAUE,EAAS9F,EAAU,CAChC,OAAO8F,GAAY,WACrBA,EAAU,CACR,KAAMA,EACN,cAAe,GACf,IAAK,EACX,GAGE,GAAI,CAACC,EAASC,CAAU,EAAIC,GAAYH,EAAQ,KAAMA,EAAQ,cAAeA,EAAQ,GAAG,EACpFH,EAAQ3F,EAAS,MAAM+F,CAAO,EAClC,GAAI,CAACJ,EAAO,OAAO,KACnB,IAAIH,EAAkBG,EAAM,CAAC,EACzBO,EAAeV,EAAgB,QAAQ,UAAW,IAAI,EACtDW,EAAgBR,EAAM,MAAM,CAAC,EAYjC,MAAO,CACL,OAZWK,EAAW,OAAO,CAACI,EAAMC,EAAW9C,IAAU,CAGzD,GAAI8C,IAAc,IAAK,CACrB,IAAIC,EAAaH,EAAc5C,CAAK,GAAK,GACzC2C,EAAeV,EAAgB,MAAM,EAAGA,EAAgB,OAASc,EAAW,MAAM,EAAE,QAAQ,UAAW,IAAI,EAG7G,OAAAF,EAAKC,CAAS,EAAIE,GAAyBJ,EAAc5C,CAAK,GAAK,GAAI8C,CAAS,EACzED,CACR,EAAE,CAAE,CAAA,EAGH,SAAUZ,EACV,aAAAU,EACA,QAAAJ,CACJ,CACA,CAEA,SAASG,GAAY/E,EAAMsF,EAAef,EAAK,CACzCe,IAAkB,SACpBA,EAAgB,IAGdf,IAAQ,SACVA,EAAM,IAGRgB,EAAQvF,IAAS,KAAO,CAACA,EAAK,SAAS,GAAG,GAAKA,EAAK,SAAS,IAAI,EAAG,eAAkBA,EAAO,oCAAuC,IAAOA,EAAK,QAAQ,MAAO,IAAI,EAAI,qCAAwC,oEAAsE,oCAAuCA,EAAK,QAAQ,MAAO,IAAI,EAAI,KAAM,EAC9V,IAAI8E,EAAa,CAAA,EACbU,EAAe,IAAMxF,EAAK,QAAQ,UAAW,EAAE,EAClD,QAAQ,OAAQ,GAAG,EACnB,QAAQ,sBAAuB,MAAM,EACrC,QAAQ,YAAa,CAACyF,EAAGN,KACxBL,EAAW,KAAKK,CAAS,EAClB,aACR,EAED,OAAInF,EAAK,SAAS,GAAG,GACnB8E,EAAW,KAAK,GAAG,EACnBU,GAAgBxF,IAAS,KAAOA,IAAS,KAAO,QAC9C,qBACOuE,EAETiB,GAAgB,QACPxF,IAAS,IAAMA,IAAS,MAQjCwF,GAAgB,iBAIX,CADO,IAAI,OAAOA,EAAcF,EAAgB,OAAY,GAAG,EACrDR,CAAU,CAC7B,CAEA,SAAS9C,GAAgBzC,EAAO,CAC9B,GAAI,CACF,OAAO,UAAUA,CAAK,CACvB,OAAQmG,EAAP,CACA,OAAAH,EAAQ,GAAO,iBAAoBhG,EAAQ,2GAAmH,aAAemG,EAAQ,KAAK,EACnLnG,CACR,CACH,CAEA,SAAS8F,GAAyB9F,EAAO4F,EAAW,CAClD,GAAI,CACF,OAAO,mBAAmB5F,CAAK,CAChC,OAAQmG,EAAP,CACA,OAAAH,EAAQ,GAAO,gCAAmCJ,EAAY,iCAAoC,gBAAmB5F,EAAQ,mDAAsD,mCAAqCmG,EAAQ,KAAK,EAC9NnG,CACR,CACH,CAMA,SAASmC,GAAc5C,EAAU2C,EAAU,CACzC,GAAIA,IAAa,IAAK,OAAO3C,EAE7B,GAAI,CAACA,EAAS,YAAa,EAAC,WAAW2C,EAAS,YAAW,CAAE,EAC3D,OAAO,KAKT,IAAIkE,EAAalE,EAAS,SAAS,GAAG,EAAIA,EAAS,OAAS,EAAIA,EAAS,OACrEmE,EAAW9G,EAAS,OAAO6G,CAAU,EAEzC,OAAIC,GAAYA,IAAa,IAEpB,KAGF9G,EAAS,MAAM6G,CAAU,GAAK,GACvC,CAKA,SAASJ,EAAQM,EAAMrG,EAAS,CAC9B,GAAI,CAACqG,EAAM,CAEL,OAAO,QAAY,KAAa,QAAQ,KAAKrG,CAAO,EAExD,GAAI,CAMF,MAAM,IAAI,MAAMA,CAAO,CAC7B,MAAM,CAAY,EAElB,CAOA,SAASsG,GAAY3G,EAAI4G,EAAc,CACjCA,IAAiB,SACnBA,EAAe,KAGjB,GAAI,CACF,SAAUC,EACV,OAAAjH,EAAS,GACT,KAAAC,EAAO,EACX,EAAM,OAAOG,GAAO,SAAWW,EAAUX,CAAE,EAAIA,EAE7C,MAAO,CACL,SAFa6G,EAAaA,EAAW,WAAW,GAAG,EAAIA,EAAaC,GAAgBD,EAAYD,CAAY,EAAIA,EAGhH,OAAQG,GAAgBnH,CAAM,EAC9B,KAAMoH,GAAcnH,CAAI,CAC5B,CACA,CAEA,SAASiH,GAAgB3D,EAAcyD,EAAc,CACnD,IAAIjD,EAAWiD,EAAa,QAAQ,OAAQ,EAAE,EAAE,MAAM,GAAG,EAEzD,OADuBzD,EAAa,MAAM,GAAG,EAC5B,QAAQ6B,GAAW,CAC9BA,IAAY,KAEVrB,EAAS,OAAS,GAAGA,EAAS,IAAG,EAC5BqB,IAAY,KACrBrB,EAAS,KAAKqB,CAAO,CAE3B,CAAG,EACMrB,EAAS,OAAS,EAAIA,EAAS,KAAK,GAAG,EAAI,GACpD,CAEA,SAASsD,EAAoBC,EAAMC,EAAOC,EAAMvG,EAAM,CACpD,MAAO,qBAAuBqG,EAAO,wCAA0C,OAASC,EAAQ,YAAc,KAAK,UAAUtG,CAAI,EAAI,uCAAyC,OAASuG,EAAO,4DAA8D,mEAC9P,CA0BA,SAASC,GAA2B1E,EAAS,CAC3C,OAAOA,EAAQ,OAAO,CAAC2C,EAAOpC,IAAUA,IAAU,GAAKoC,EAAM,MAAM,MAAQA,EAAM,MAAM,KAAK,OAAS,CAAC,CACxG,CAKA,SAASgC,GAAUC,EAAOC,EAAgBC,EAAkBC,EAAgB,CACtEA,IAAmB,SACrBA,EAAiB,IAGnB,IAAI1H,EAEA,OAAOuH,GAAU,SACnBvH,EAAKW,EAAU4G,CAAK,GAEpBvH,EAAKjB,EAAS,GAAIwI,CAAK,EACvBpH,EAAU,CAACH,EAAG,UAAY,CAACA,EAAG,SAAS,SAAS,GAAG,EAAGiH,EAAoB,IAAK,WAAY,SAAUjH,CAAE,CAAC,EACxGG,EAAU,CAACH,EAAG,UAAY,CAACA,EAAG,SAAS,SAAS,GAAG,EAAGiH,EAAoB,IAAK,WAAY,OAAQjH,CAAE,CAAC,EACtGG,EAAU,CAACH,EAAG,QAAU,CAACA,EAAG,OAAO,SAAS,GAAG,EAAGiH,EAAoB,IAAK,SAAU,OAAQjH,CAAE,CAAC,GAGlG,IAAI2H,EAAcJ,IAAU,IAAMvH,EAAG,WAAa,GAC9C6G,EAAac,EAAc,IAAM3H,EAAG,SACpC4H,EAUJ,GAAIF,GAAkBb,GAAc,KAClCe,EAAOH,MACF,CACL,IAAII,EAAqBL,EAAe,OAAS,EAEjD,GAAIX,EAAW,WAAW,IAAI,EAAG,CAC/B,IAAIiB,EAAajB,EAAW,MAAM,GAAG,EAIrC,KAAOiB,EAAW,CAAC,IAAM,MACvBA,EAAW,MAAK,EAChBD,GAAsB,EAGxB7H,EAAG,SAAW8H,EAAW,KAAK,GAAG,EAKnCF,EAAOC,GAAsB,EAAIL,EAAeK,CAAkB,EAAI,IAGxE,IAAIhH,EAAO8F,GAAY3G,EAAI4H,CAAI,EAE3BG,EAA2BlB,GAAcA,IAAe,KAAOA,EAAW,SAAS,GAAG,EAEtFmB,GAA2BL,GAAed,IAAe,MAAQY,EAAiB,SAAS,GAAG,EAElG,MAAI,CAAC5G,EAAK,SAAS,SAAS,GAAG,IAAMkH,GAA4BC,KAC/DnH,EAAK,UAAY,KAGZA,CACT,CAaA,MAAMwC,EAAY4E,GAASA,EAAM,KAAK,GAAG,EAAE,QAAQ,SAAU,GAAG,EAK1DzC,GAAoB7F,GAAYA,EAAS,QAAQ,OAAQ,EAAE,EAAE,QAAQ,OAAQ,GAAG,EAKhFoH,GAAkBnH,GAAU,CAACA,GAAUA,IAAW,IAAM,GAAKA,EAAO,WAAW,GAAG,EAAIA,EAAS,IAAMA,EAKrGoH,GAAgBnH,GAAQ,CAACA,GAAQA,IAAS,IAAM,GAAKA,EAAK,WAAW,GAAG,EAAIA,EAAO,IAAMA,EAsM/F,MAAMqI,EAAc,CAClB,YAAYC,EAAQC,EAAYC,EAAMC,EAAU,CAC1CA,IAAa,SACfA,EAAW,IAGb,KAAK,OAASH,EACd,KAAK,WAAaC,GAAc,GAChC,KAAK,SAAWE,EAEZD,aAAgB,OAClB,KAAK,KAAOA,EAAK,WACjB,KAAK,MAAQA,GAEb,KAAK,KAAOA,CAEf,CAEH,CAMA,SAASE,GAAqB,EAAG,CAC/B,OAAO,aAAaL,EACtB,CAEA,MAAMM,GAA0B,CAAC,OAAQ,MAAO,QAAS,QAAQ,EACpC,IAAI,IAAIA,EAAuB,EAC5D,MAAMC,GAAyB,CAAC,MAAO,GAAGD,EAAuB,EACrC,IAAI,IAAIC,EAAsB,EC5yC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAcA,SAAS1J,GAAW,CAClBA,OAAAA,EAAW,OAAO,OAAS,OAAO,OAAO,KAAI,EAAK,SAAUC,EAAQ,CAClE,QAASC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,EAExB,QAASE,KAAOD,EACV,OAAO,UAAU,eAAe,KAAKA,EAAQC,CAAG,IAClDH,EAAOG,CAAG,EAAID,EAAOC,CAAG,GAK9B,OAAOH,CACX,EACSD,EAAS,MAAM,KAAM,SAAS,CACvC,CAaA,SAAS2J,GAAWC,EAAGC,EAAG,CACxB,OAAOD,IAAMC,IAAMD,IAAM,GAAK,EAAIA,IAAM,EAAIC,IAAMD,IAAMA,GAAKC,IAAMA,CAErE,CAEA,MAAMC,GAAK,OAAO,OAAO,IAAO,WAAa,OAAO,GAAKH,GAGnD,CACJ,SAAAI,GACA,UAAAC,GACA,gBAAAC,GACA,cAAAC,EACF,EAAIC,EAaJ,SAASC,GAAuBC,EAAWC,EAI3CC,EAAmB,CAcjB,MAAMlJ,EAAQiJ,IA2BR,CAAC,CACL,KAAAE,CACJ,EAAKC,CAAW,EAAIV,GAAS,CACzB,KAAM,CACJ,MAAA1I,EACA,YAAAiJ,CACD,CACL,CAAG,EAID,OAAAL,GAAgB,IAAM,CACpBO,EAAK,MAAQnJ,EACbmJ,EAAK,YAAcF,EAKfI,EAAuBF,CAAI,GAE7BC,EAAY,CACV,KAAAD,CACR,CAAO,CAGJ,EAAE,CAACH,EAAWhJ,EAAOiJ,CAAW,CAAC,EAClCN,GAAU,KAGJU,EAAuBF,CAAI,GAE7BC,EAAY,CACV,KAAAD,CACR,CAAO,EAmBIH,EAhBmB,IAAM,CAO1BK,EAAuBF,CAAI,GAE7BC,EAAY,CACV,KAAAD,CACV,CAAS,CAET,CAGsC,GACjC,CAACH,CAAS,CAAC,EACdH,GAAc7I,CAAK,EACZA,CACT,CAEA,SAASqJ,EAAuBF,EAAM,CACpC,MAAMG,EAAoBH,EAAK,YACzBI,EAAYJ,EAAK,MAEvB,GAAI,CACF,MAAMK,EAAYF,IAClB,MAAO,CAACb,GAAGc,EAAWC,CAAS,CAChC,MAAC,CACA,MAAO,EACR,CACH,CAUA,SAASC,GAAuBT,EAAWC,EAAaC,EAAmB,CAKzE,OAAOD,EAAW,CACpB,CAOA,MAAMS,GAAe,OAAO,OAAW,KAAe,OAAO,OAAO,SAAa,KAAe,OAAO,OAAO,SAAS,cAAkB,IACnIC,GAAsB,CAACD,GACvBE,GAAOD,GAAsBF,GAAyBV,GAC/B,yBAA0BD,IAASe,GAAUA,EAAO,sBAAsBf,CAAK,EAE5G,MAAMgB,GAAiCC,EAAAA,cAAoB,IAAI,EAMzDC,EAAsCD,EAAAA,cAAoB,IAAI,EAY9DE,EAAiCF,EAAAA,cAAoB,IAAI,EAMzDG,EAA+BH,EAAAA,cAAoB,IAAI,EAMvDI,EAA4BJ,EAAAA,cAAoB,CACpD,OAAQ,KACR,QAAS,CAAE,CACb,CAAC,EAMKK,GAAiCL,EAAAA,cAAoB,IAAI,EAa/D,SAASM,GAAQzK,EAAI0K,EAAO,CAC1B,GAAI,CACF,SAAAC,CACD,EAAGD,IAAU,OAAS,CAAA,EAAKA,EAC3BE,EAAkB,GAEqDzK,EAAU,EAAK,EACvF,GAAI,CACF,SAAAmC,EACA,UAAAuI,CACJ,EAAMC,EAAAA,WAAiBT,CAAiB,EAClC,CACF,KAAAxK,EACA,SAAAF,EACA,OAAAC,CACJ,EAAMmL,EAAgB/K,EAAI,CACtB,SAAA2K,CACJ,CAAG,EACGK,EAAiBrL,EAKrB,OAAI2C,IAAa,MACf0I,EAAiBrL,IAAa,IAAM2C,EAAWe,EAAU,CAACf,EAAU3C,CAAQ,CAAC,GAGxEkL,EAAU,WAAW,CAC1B,SAAUG,EACV,OAAApL,EACA,KAAAC,CACJ,CAAG,CACH,CAOA,SAAS+K,GAAqB,CAC5B,OAAOE,EAAgB,WAACR,CAAe,GAAK,IAC9C,CAYA,SAASW,GAAc,CACrB,OAACL,EAAkB,GAEyDzK,EAAU,EAAK,EACpF2K,EAAgB,WAACR,CAAe,EAAE,QAC3C,CAsCA,SAASY,IAAc,CACpBN,EAAkB,GAEyDzK,EAAU,EAAK,EAC3F,GAAI,CACF,SAAAmC,EACA,UAAAuI,CACJ,EAAMC,EAAAA,WAAiBT,CAAiB,EAClC,CACF,QAAA1H,CACJ,EAAMmI,EAAAA,WAAiBP,CAAY,EAC7B,CACF,SAAU9C,CACX,EAAGwD,EAAW,EACXE,EAAqB,KAAK,UAAUC,GAAkCzI,CAAO,EAAE,IAAI2C,GAASA,EAAM,YAAY,CAAC,EAC/G+F,EAAYC,SAAa,EAAK,EAClCC,OAAAA,EAAAA,UAAgB,IAAM,CACpBF,EAAU,QAAU,EACxB,CAAG,EACcG,EAAAA,YAAkB,SAAUxL,EAAIT,EAAS,CAMtD,GALIA,IAAY,SACdA,EAAU,CAAA,GAIR,CAAC8L,EAAU,QAAS,OAExB,GAAI,OAAOrL,GAAO,SAAU,CAC1B6K,EAAU,GAAG7K,CAAE,EACf,OAGF,IAAIa,EAAOyG,GAAUtH,EAAI,KAAK,MAAMmL,CAAkB,EAAG1D,EAAkBlI,EAAQ,WAAa,MAAM,EAKlG+C,IAAa,MACfzB,EAAK,SAAWA,EAAK,WAAa,IAAMyB,EAAWe,EAAU,CAACf,EAAUzB,EAAK,QAAQ,CAAC,IAGrFtB,EAAQ,QAAUsL,EAAU,QAAUA,EAAU,MAAMhK,EAAMtB,EAAQ,MAAOA,CAAO,CACtF,EAAE,CAAC+C,EAAUuI,EAAWM,EAAoB1D,CAAgB,CAAC,CAEhE,CACA,MAAMgE,GAA6BtB,EAAAA,cAAoB,IAAI,EAiB3D,SAASuB,GAAUC,EAAS,CAC1B,IAAIC,EAASd,EAAAA,WAAiBP,CAAY,EAAE,OAE5C,OAAIqB,GACkBC,EAAmB,cAACJ,GAAc,SAAU,CAC9D,MAAOE,CACR,EAAEC,CAAM,CAIb,CAQA,SAASE,IAAY,CACnB,GAAI,CACF,QAAAnJ,CACJ,EAAMmI,EAAAA,WAAiBP,CAAY,EAC7BwB,EAAapJ,EAAQA,EAAQ,OAAS,CAAC,EAC3C,OAAOoJ,EAAaA,EAAW,OAAS,EAC1C,CAOA,SAAShB,EAAgB/K,EAAIgM,EAAQ,CACnC,GAAI,CACF,SAAArB,CACD,EAAGqB,IAAW,OAAS,CAAA,EAAKA,EACzB,CACF,QAAArJ,CACJ,EAAMmI,EAAAA,WAAiBP,CAAY,EAC7B,CACF,SAAU9C,CACX,EAAGwD,EAAW,EACXE,EAAqB,KAAK,UAAUC,GAAkCzI,CAAO,EAAE,IAAI2C,GAASA,EAAM,YAAY,CAAC,EACnH,OAAO2G,EAAAA,QAAc,IAAM3E,GAAUtH,EAAI,KAAK,MAAMmL,CAAkB,EAAG1D,EAAkBkD,IAAa,MAAM,EAAG,CAAC3K,EAAImL,EAAoB1D,EAAkBkD,CAAQ,CAAC,CACvK,CAUA,SAASuB,GAAU9J,EAAQC,EAAa,CACrCuI,EAAkB,GAEuDzK,EAAU,EAAK,EACzF,GAAI,CACF,UAAA0K,CACJ,EAAMC,EAAAA,WAAiBT,CAAiB,EAClC8B,EAAyBrB,aAAiBV,CAAsB,EAChE,CACF,QAASgC,CACb,EAAMtB,EAAAA,WAAiBP,CAAY,EAC7BwB,EAAaK,EAAcA,EAAc,OAAS,CAAC,EACnDC,EAAeN,EAAaA,EAAW,OAAS,CAAA,EAC/BA,GAAaA,EAAW,SAC7C,IAAIO,EAAqBP,EAAaA,EAAW,aAAe,IAC9CA,GAAcA,EAAW,MA2B3C,IAAIQ,EAAsBtB,IACtBzK,EAEJ,GAAI6B,EAAa,CACf,IAAImK,EAEJ,IAAIC,EAAoB,OAAOpK,GAAgB,SAAW1B,EAAU0B,CAAW,EAAIA,EACjFiK,IAAuB,MAASE,EAAwBC,EAAkB,WAAa,MAAgBD,EAAsB,WAAWF,CAAkB,GAA+anM,EAAU,EAAK,EAC1lBK,EAAWiM,OAEXjM,EAAW+L,EAGb,IAAI5M,EAAWa,EAAS,UAAY,IAChC6E,EAAoBiH,IAAuB,IAAM3M,EAAWA,EAAS,MAAM2M,EAAmB,MAAM,GAAK,IACzG3J,EAAUR,GAAYC,EAAQ,CAChC,SAAUiD,CACd,CAAG,EAOGqH,EAAkBC,GAAehK,GAAWA,EAAQ,IAAI2C,GAAS,OAAO,OAAO,CAAE,EAAEA,EAAO,CAC5F,OAAQ,OAAO,OAAO,CAAE,EAAE+G,EAAc/G,EAAM,MAAM,EACpD,SAAUjC,EAAU,CAACiJ,EACrBzB,EAAU,eAAiBA,EAAU,eAAevF,EAAM,QAAQ,EAAE,SAAWA,EAAM,QAAQ,CAAC,EAC9F,aAAcA,EAAM,eAAiB,IAAMgH,EAAqBjJ,EAAU,CAACiJ,EAC3EzB,EAAU,eAAiBA,EAAU,eAAevF,EAAM,YAAY,EAAE,SAAWA,EAAM,YAAY,CAAC,CACvG,CAAA,CAAC,EAAG8G,EAAeD,GAA0B,MAAS,EAKvD,OAAI9J,GAAeqK,EACGb,EAAmB,cAACvB,EAAgB,SAAU,CAChE,MAAO,CACL,SAAUvL,EAAS,CACjB,SAAU,IACV,OAAQ,GACR,KAAM,GACN,MAAO,KACP,IAAK,SACN,EAAEyB,CAAQ,EACX,eAAgBpB,EAAO,GACxB,CACF,EAAEsN,CAAe,EAGbA,CACT,CAEA,SAASE,IAAsB,CAC7B,IAAIrG,EAAQsG,KACRxM,EAAUkI,GAAqBhC,CAAK,EAAIA,EAAM,OAAS,IAAMA,EAAM,WAAaA,aAAiB,MAAQA,EAAM,QAAU,KAAK,UAAUA,CAAK,EAC7IuG,EAAQvG,aAAiB,MAAQA,EAAM,MAAQ,KAC/CwG,EAAY,yBACZC,EAAY,CACd,QAAS,SACT,gBAAiBD,CACrB,EACME,EAAa,CACf,QAAS,UACT,gBAAiBF,CACrB,EACE,OAAoBlB,EAAmB,cAACqB,WAAgB,KAAmBrB,EAAmB,cAAC,KAAM,KAAM,yBAAyB,EAAgBA,EAAmB,cAAC,KAAM,CAC5K,MAAO,CACL,UAAW,QACZ,CACF,EAAExL,CAAO,EAAGyM,EAAqBjB,EAAAA,cAAoB,MAAO,CAC3D,MAAOmB,CACR,EAAEF,CAAK,EAAI,KAAmBjB,EAAAA,cAAoB,IAAK,KAAM,qBAAyC,EAAgBA,EAAAA,cAAoB,IAAK,KAAM,+FAAgHA,EAAAA,cAAoB,OAAQ,CAChS,MAAOoB,CACR,EAAE,cAAc,EAAG,aAA8BpB,EAAAA,cAAoB,OAAQ,CAC5E,MAAOoB,CACX,EAAK,SAAS,CAAC,CAAC,CAChB,CAEA,MAAME,WAA4BC,EAAAA,SAAgB,CAChD,YAAYC,EAAO,CACjB,MAAMA,CAAK,EACX,KAAK,MAAQ,CACX,SAAUA,EAAM,SAChB,MAAOA,EAAM,KACnB,CACG,CAED,OAAO,yBAAyB9G,EAAO,CACrC,MAAO,CACL,MAAOA,CACb,CACG,CAED,OAAO,yBAAyB8G,EAAO3M,EAAO,CAS5C,OAAIA,EAAM,WAAa2M,EAAM,SACpB,CACL,MAAOA,EAAM,MACb,SAAUA,EAAM,QACxB,EAOW,CACL,MAAOA,EAAM,OAAS3M,EAAM,MAC5B,SAAUA,EAAM,QACtB,CACG,CAED,kBAAkB6F,EAAO+G,EAAW,CAClC,QAAQ,MAAM,wDAAyD/G,EAAO+G,CAAS,CACxF,CAED,QAAS,CACP,OAAO,KAAK,MAAM,MAAqBzB,EAAAA,cAAoBtB,EAAa,SAAU,CAChF,MAAO,KAAK,MAAM,YACxB,EAAoBsB,EAAmB,cAACrB,GAAkB,SAAU,CAC9D,MAAO,KAAK,MAAM,MAClB,SAAU,KAAK,MAAM,SACtB,CAAA,CAAC,EAAI,KAAK,MAAM,QAClB,CAEH,CAEA,SAAS+C,GAAc3M,EAAM,CAC3B,GAAI,CACF,aAAA4M,EACA,MAAAlI,EACA,SAAAmI,CACD,EAAG7M,EACA8M,EAAoB5C,aAAiBZ,EAAiB,EAG1D,OAAIwD,GAAqBA,EAAkB,QAAUA,EAAkB,eAAiBpI,EAAM,MAAM,eAClGoI,EAAkB,cAAc,2BAA6BpI,EAAM,MAAM,IAGvDuG,EAAmB,cAACtB,EAAa,SAAU,CAC7D,MAAOiD,CACR,EAAEC,CAAQ,CACb,CAEA,SAASd,GAAehK,EAASyJ,EAAeuB,EAAiB,CAK/D,GAJIvB,IAAkB,SACpBA,EAAgB,CAAA,GAGdzJ,GAAW,KACb,GAAIgL,GAAmB,MAAQA,EAAgB,OAG7ChL,EAAUgL,EAAgB,YAE1B,QAAO,KAIX,IAAIjB,EAAkB/J,EAElBiL,EAASD,GAAmB,KAAO,OAASA,EAAgB,OAEhE,GAAIC,GAAU,KAAM,CAClB,IAAIC,EAAanB,EAAgB,UAAUoB,GAAKA,EAAE,MAAM,KAAOF,GAAU,KAAO,OAASA,EAAOE,EAAE,MAAM,EAAE,EAAE,EAC1GD,GAAc,GAAqI1N,EAAU,EAAK,EACpKuM,EAAkBA,EAAgB,MAAM,EAAG,KAAK,IAAIA,EAAgB,OAAQmB,EAAa,CAAC,CAAC,EAG7F,OAAOnB,EAAgB,YAAY,CAACd,EAAQtG,EAAOpC,IAAU,CAC3D,IAAIqD,EAAQjB,EAAM,MAAM,GAAKsI,GAAU,KAAO,OAASA,EAAOtI,EAAM,MAAM,EAAE,EAAI,KAE5EyI,EAAeJ,EAAkBrI,EAAM,MAAM,cAA6BuG,EAAAA,cAAoBe,GAAqB,IAAI,EAAI,KAC3HjK,EAAUyJ,EAAc,OAAOM,EAAgB,MAAM,EAAGxJ,EAAQ,CAAC,CAAC,EAElE8K,EAAc,IAAmBnC,EAAmB,cAAC0B,GAAe,CACtE,MAAOjI,EACP,aAAc,CACZ,OAAAsG,EACA,QAAAjJ,CACD,CACF,EAAE4D,EAAQwH,EAAezI,EAAM,MAAM,UAAY,OAAYA,EAAM,MAAM,QAAUsG,CAAM,EAK1F,OAAO+B,IAAoBrI,EAAM,MAAM,cAAgBpC,IAAU,GAAkB2I,EAAmB,cAACsB,GAAqB,CAC1H,SAAUQ,EAAgB,SAC1B,UAAWI,EACX,MAAOxH,EACP,SAAUyH,EAAa,EACvB,aAAc,CACZ,OAAQ,KACR,QAAArL,CACD,CACP,CAAK,EAAIqL,EAAW,CACjB,EAAE,IAAI,CACT,CACA,IAAIC,GAEH,SAAUA,EAAgB,CACzBA,EAAe,eAAoB,gBACrC,GAAGA,IAAmBA,EAAiB,CAAE,EAAC,EAE1C,IAAIC,GAEH,SAAUA,EAAqB,CAC9BA,EAAoB,cAAmB,gBACvCA,EAAoB,cAAmB,gBACvCA,EAAoB,cAAmB,gBACvCA,EAAoB,cAAmB,gBACvCA,EAAoB,mBAAwB,qBAC5CA,EAAoB,WAAgB,aACpCA,EAAoB,eAAoB,gBAC1C,GAAGA,IAAwBA,EAAsB,CAAE,EAAC,EAYpD,SAASC,GAAmBC,EAAU,CACpC,IAAI1N,EAAQoK,aAAiBV,CAAsB,EACnD,OAAC1J,GAAwGP,EAAU,EAAK,EACjHO,CACT,CAEA,SAAS2N,GAAgBD,EAAU,CACjC,IAAInL,EAAQ6H,aAAiBP,CAAY,EACzC,OAACtH,GAAwG9C,EAAU,EAAK,EACjH8C,CACT,CAEA,SAASqL,GAAkBF,EAAU,CACnC,IAAInL,EAAQoL,GAAwB,EAChCE,EAAYtL,EAAM,QAAQA,EAAM,QAAQ,OAAS,CAAC,EACtD,OAACsL,EAAU,MAAM,IAAuIpO,EAAU,EAAK,EAChKoO,EAAU,MAAM,EACzB,CA0FA,SAAS1B,IAAgB,CACvB,IAAI2B,EAEJ,IAAIjI,EAAQuE,aAAiBN,EAAiB,EAC1C9J,EAAQyN,GAAmBD,EAAoB,aAAa,EAC5DO,EAAUH,GAAkBJ,EAAoB,aAAa,EAGjE,OAAI3H,KAKIiI,EAAgB9N,EAAM,SAAW,KAAO,OAAS8N,EAAcC,CAAO,EAChF,CA8HA,SAASC,GAASC,EAAO,CACvB,GAAI,CACF,GAAA3O,EACA,QAAA+B,EACA,MAAArB,EACA,SAAAiK,CACD,EAAGgE,EACH/D,EAAkB,GAEsDzK,EAAU,EAAK,EAExF,IAAIwN,EAAkB7C,aAAiBV,CAAsB,EACzDwE,EAAW1D,KACfK,OAAAA,EAAAA,UAAgB,IAAM,CAIhBoC,GAAmBA,EAAgB,WAAW,QAAU,QAI5DiB,EAAS5O,EAAI,CACX,QAAA+B,EACA,MAAArB,EACA,SAAAiK,CACN,CAAK,CACL,CAAG,EACM,IACT,CAOA,SAASkE,GAAOxB,EAAO,CACrB,OAAO3B,GAAU2B,EAAM,OAAO,CAChC,CAOA,SAASyB,GAAMC,EAAQ,CACmL5O,EAAU,EAAK,CACzN,CAWA,SAAS6O,GAAOC,EAAO,CACrB,GAAI,CACF,SAAUC,EAAe,IACzB,SAAAzB,EAAW,KACX,SAAU0B,EACV,eAAAC,EAAiBhQ,EAAO,IACxB,UAAAyL,EACA,OAAQwE,EAAa,EACtB,EAAGJ,EACFrE,EAAkB,GAA+KzK,EAAU,EAAK,EAGlN,IAAImC,EAAW4M,EAAa,QAAQ,OAAQ,GAAG,EAC3CI,EAAoBrD,EAAAA,QAAc,KAAO,CAC3C,SAAA3J,EACA,UAAAuI,EACA,OAAQwE,CACT,GAAG,CAAC/M,EAAUuI,EAAWwE,CAAU,CAAC,EAEjC,OAAOF,GAAiB,WAC1BA,EAAexO,EAAUwO,CAAY,GAGvC,GAAI,CACF,SAAAxP,EAAW,IACX,OAAAC,EAAS,GACT,KAAAC,EAAO,GACP,MAAAa,EAAQ,KACR,IAAAvB,EAAM,SACP,EAAGgQ,EACA3O,EAAWyL,EAAAA,QAAc,IAAM,CACjC,IAAIsD,EAAmBhN,GAAc5C,EAAU2C,CAAQ,EAEvD,OAAIiN,GAAoB,KACf,KAGF,CACL,SAAUA,EACV,OAAA3P,EACA,KAAAC,EACA,MAAAa,EACA,IAAAvB,CACN,CACA,EAAK,CAACmD,EAAU3C,EAAUC,EAAQC,EAAMa,EAAOvB,CAAG,CAAC,EAGjD,OAAIqB,GAAY,KACP,KAGWqL,EAAmB,cAACxB,EAAkB,SAAU,CAClE,MAAOiF,CACX,EAAkBzD,EAAmB,cAACvB,EAAgB,SAAU,CAC5D,SAAUmD,EACV,MAAO,CACL,SAAAjN,EACA,eAAA4O,CACD,CACF,CAAA,CAAC,CACJ,CAQA,SAASI,GAAOC,EAAO,CACrB,GAAI,CACF,SAAAhC,EACA,SAAAjN,CACD,EAAGiP,EACA/B,EAAoB5C,aAAiBZ,EAAiB,EAItD9H,EAASsL,GAAqB,CAACD,EAAWC,EAAkB,OAAO,OAASgC,EAAyBjC,CAAQ,EACjH,OAAOvB,GAAU9J,EAAQ5B,CAAQ,CACnC,CAiBA,IAAImP,GAEH,SAAUA,EAAmB,CAC5BA,EAAkBA,EAAkB,QAAa,CAAC,EAAI,UACtDA,EAAkBA,EAAkB,QAAa,CAAC,EAAI,UACtDA,EAAkBA,EAAkB,MAAW,CAAC,EAAI,OACtD,GAAGA,IAAsBA,EAAoB,CAAE,EAAC,EAEpB,IAAI,QAAQ,IAAM,CAAA,CAAE,EAiIhD,SAASD,EAAyBjC,EAAU1K,EAAY,CAClDA,IAAe,SACjBA,EAAa,CAAA,GAGf,IAAIX,EAAS,CAAA,EACbwN,OAAAA,EAAAA,SAAe,QAAQnC,EAAU,CAACoC,EAAS3M,IAAU,CACnD,GAAI,CAAe4M,EAAAA,eAAqBD,CAAO,EAG7C,OAGF,GAAIA,EAAQ,OAAS3C,WAAgB,CAEnC9K,EAAO,KAAK,MAAMA,EAAQsN,EAAyBG,EAAQ,MAAM,SAAU9M,CAAU,CAAC,EACtF,OAGA8M,EAAQ,OAASf,IAA4P3O,EAAU,EAAK,EAC5R,CAAC0P,EAAQ,MAAM,OAAS,CAACA,EAAQ,MAAM,UAAmH1P,EAAU,EAAK,EAC3K,IAAI4P,EAAW,CAAC,GAAGhN,EAAYG,CAAK,EAChCD,EAAQ,CACV,GAAI4M,EAAQ,MAAM,IAAME,EAAS,KAAK,GAAG,EACzC,cAAeF,EAAQ,MAAM,cAC7B,QAASA,EAAQ,MAAM,QACvB,MAAOA,EAAQ,MAAM,MACrB,KAAMA,EAAQ,MAAM,KACpB,OAAQA,EAAQ,MAAM,OACtB,OAAQA,EAAQ,MAAM,OACtB,aAAcA,EAAQ,MAAM,aAC5B,iBAAkBA,EAAQ,MAAM,cAAgB,KAChD,iBAAkBA,EAAQ,MAAM,iBAChC,OAAQA,EAAQ,MAAM,MAC5B,EAEQA,EAAQ,MAAM,WAChB5M,EAAM,SAAWyM,EAAyBG,EAAQ,MAAM,SAAUE,CAAQ,GAG5E3N,EAAO,KAAKa,CAAK,CACrB,CAAG,EACMb,CACT,CCjzCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAeA,SAASrD,GAAW,CAClB,OAAAA,EAAW,OAAO,OAAS,OAAO,OAAO,KAAI,EAAK,SAAUC,EAAQ,CAClE,QAASC,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CACzC,IAAIC,EAAS,UAAUD,CAAC,EAExB,QAASE,KAAOD,EACV,OAAO,UAAU,eAAe,KAAKA,EAAQC,CAAG,IAClDH,EAAOG,CAAG,EAAID,EAAOC,CAAG,GAK9B,OAAOH,CACX,EACSD,EAAS,MAAM,KAAM,SAAS,CACvC,CAEA,SAASiR,GAA8B9Q,EAAQ+Q,EAAU,CACvD,GAAI/Q,GAAU,KAAM,MAAO,GAC3B,IAAIF,EAAS,CAAA,EACTkR,EAAa,OAAO,KAAKhR,CAAM,EAC/BC,EAAKF,EAET,IAAKA,EAAI,EAAGA,EAAIiR,EAAW,OAAQjR,IACjCE,EAAM+Q,EAAWjR,CAAC,EACd,EAAAgR,EAAS,QAAQ9Q,CAAG,GAAK,KAC7BH,EAAOG,CAAG,EAAID,EAAOC,CAAG,GAG1B,OAAOH,CACT,CAiBA,SAASmR,GAAgBC,EAAO,CAC9B,MAAO,CAAC,EAAEA,EAAM,SAAWA,EAAM,QAAUA,EAAM,SAAWA,EAAM,SACpE,CAEA,SAASC,GAAuBD,EAAOpR,EAAQ,CAC7C,OAAOoR,EAAM,SAAW,IACxB,CAACpR,GAAUA,IAAW,UACtB,CAACmR,GAAgBC,CAAK,CAExB,CAoHK,MAACE,GAAY,CAAC,UAAW,WAAY,iBAAkB,UAAW,QAAS,SAAU,KAAM,oBAAoB,EAC9GC,GAAa,CAAC,eAAgB,gBAAiB,YAAa,MAAO,QAAS,KAAM,UAAU,EAoElG,SAASC,GAAc5P,EAAM,CAC3B,GAAI,CACF,SAAA0B,EACA,SAAAmL,EACA,OAAAhO,CACD,EAAGmB,EACA6P,EAAanF,EAAAA,SAEbmF,EAAW,SAAW,OACxBA,EAAW,QAAUnR,GAAqB,CACxC,OAAAG,EACA,SAAU,EAChB,CAAK,GAGH,IAAIkC,EAAU8O,EAAW,QACrB,CAAC/P,EAAOgQ,CAAQ,EAAIC,WAAe,CACrC,OAAQhP,EAAQ,OAChB,SAAUA,EAAQ,QACtB,CAAG,EACDiP,OAAAA,EAAqB,gBAAC,IAAMjP,EAAQ,OAAO+O,CAAQ,EAAG,CAAC/O,CAAO,CAAC,EAC3CkK,EAAAA,cAAoBmD,GAAQ,CAC9C,SAAU1M,EACV,SAAUmL,EACV,SAAU/M,EAAM,SAChB,eAAgBA,EAAM,OACtB,UAAWiB,CACf,CAAG,CACH,CAqEK,MAACkP,GAAoBC,EAAAA,WAAiB,SAAqB7B,EAAO8B,EAAK,CAC1E,GAAI,CACF,QAAAC,EACA,SAAArG,EACA,eAAAsG,EACA,QAAAlP,EACA,MAAArB,EACA,OAAA1B,EACA,GAAAgB,EACA,mBAAAkR,CACJ,EAAMjC,EACApL,EAAOmM,GAA8Bf,EAAOqB,EAAS,EAErDnP,EAAOsJ,GAAQzK,EAAI,CACrB,SAAA2K,CACJ,CAAG,EACGwG,EAAkBC,GAAoBpR,EAAI,CAC5C,QAAA+B,EACA,MAAArB,EACA,OAAA1B,EACA,mBAAAkS,EACA,SAAAvG,CACJ,CAAG,EAED,SAAS0G,EAAYjB,EAAO,CACtBY,GAASA,EAAQZ,CAAK,EAErBA,EAAM,kBACTe,EAAgBf,CAAK,CAExB,CAED,OAGEvE,EAAAA,cAAoB,IAAK9M,EAAS,CAAA,EAAI8E,EAAM,CAC1C,KAAM1C,EACN,QAAS8P,EAAiBD,EAAUK,EACpC,IAAKN,EACL,OAAQ/R,CACd,CAAK,CAAC,CAEN,CAAC,EAUKsS,GAAuBR,EAAAA,WAAiB,SAAwBrB,EAAOsB,EAAK,CAChF,GAAI,CACF,eAAgBQ,EAAkB,OAClC,cAAApL,EAAgB,GAChB,UAAWqL,EAAgB,GAC3B,IAAApM,EAAM,GACN,MAAOqM,EACP,GAAAzR,EACA,SAAAyN,CACJ,EAAMgC,EACA5L,EAAOmM,GAA8BP,EAAOc,EAAU,EAEtD1P,EAAOkK,EAAgB/K,EAAI,CAC7B,SAAU6D,EAAK,QACnB,CAAG,EACGrD,EAAWyK,IACXyG,EAAc5G,aAAiB6G,CAA6B,EAC5D,CACF,UAAA9G,CACJ,EAAMC,EAAAA,WAAiB8G,CAAwB,EACzC/K,EAAagE,EAAU,eAAiBA,EAAU,eAAehK,CAAI,EAAE,SAAWA,EAAK,SACvF4G,EAAmBjH,EAAS,SAC5BqR,EAAuBH,GAAeA,EAAY,YAAcA,EAAY,WAAW,SAAWA,EAAY,WAAW,SAAS,SAAW,KAE5IvL,IACHsB,EAAmBA,EAAiB,cACpCoK,EAAuBA,EAAuBA,EAAqB,YAAW,EAAK,KACnFhL,EAAaA,EAAW,eAG1B,IAAIiL,EAAWrK,IAAqBZ,GAAc,CAACzB,GAAOqC,EAAiB,WAAWZ,CAAU,GAAKY,EAAiB,OAAOZ,EAAW,MAAM,IAAM,IAChJkL,EAAYF,GAAwB,OAASA,IAAyBhL,GAAc,CAACzB,GAAOyM,EAAqB,WAAWhL,CAAU,GAAKgL,EAAqB,OAAOhL,EAAW,MAAM,IAAM,KAC9LmL,GAAcF,EAAWP,EAAkB,OAC3CU,EAEA,OAAOT,GAAkB,WAC3BS,EAAYT,EAAc,CACxB,SAAAM,EACA,UAAAC,CACN,CAAK,EAODE,EAAY,CAACT,EAAeM,EAAW,SAAW,KAAMC,EAAY,UAAY,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,GAAG,EAGhH,IAAIG,GAAQ,OAAOT,GAAc,WAAaA,EAAU,CACtD,SAAAK,EACA,UAAAC,CACD,CAAA,EAAIN,EACL,OAAoB5F,EAAmB,cAACgF,GAAM9R,EAAS,CAAA,EAAI8E,EAAM,CAC/D,eAAgBmO,GAChB,UAAWC,EACX,IAAKlB,EACL,MAAOmB,GACP,GAAIlS,CACL,CAAA,EAAG,OAAOyN,GAAa,WAAaA,EAAS,CAC5C,SAAAqE,EACA,UAAAC,CACJ,CAAG,EAAItE,CAAQ,CACf,CAAC,EA4FD,IAAIQ,GAEH,SAAUA,EAAgB,CACzBA,EAAe,qBAA0B,uBACzCA,EAAe,cAAmB,gBAClCA,EAAe,WAAgB,YACjC,GAAGA,IAAmBA,EAAiB,CAAE,EAAC,EAE1C,IAAIC,GAEH,SAAUA,EAAqB,CAC9BA,EAAoB,YAAiB,cACrCA,EAAoB,qBAA0B,sBAChD,GAAGA,IAAwBA,EAAsB,CAAE,EAAC,EAwBpD,SAASkD,GAAoBpR,EAAI0K,EAAO,CACtC,GAAI,CACF,OAAA1L,EACA,QAASmT,EACT,MAAAzR,EACA,mBAAAwQ,EACA,SAAAvG,CACD,EAAGD,IAAU,OAAS,CAAA,EAAKA,EACxBkE,EAAW1D,KACX1K,EAAWyK,IACXpK,EAAOkK,EAAgB/K,EAAI,CAC7B,SAAA2K,CACJ,CAAG,EACD,OAAOa,EAAAA,YAAkB4E,GAAS,CAChC,GAAIC,GAAuBD,EAAOpR,CAAM,EAAG,CACzCoR,EAAM,eAAc,EAGpB,IAAIrO,EAAUoQ,IAAgB,OAAYA,EAAclS,EAAWO,CAAQ,IAAMP,EAAWY,CAAI,EAChG+N,EAAS5O,EAAI,CACX,QAAA+B,EACA,MAAArB,EACA,mBAAAwQ,EACA,SAAAvG,CACR,CAAO,EAEJ,EAAE,CAACnK,EAAUoO,EAAU/N,EAAMsR,EAAazR,EAAO1B,EAAQgB,EAAIkR,EAAoBvG,CAAQ,CAAC,CAC7F", "x_google_ignoreList": [0, 1, 2]}