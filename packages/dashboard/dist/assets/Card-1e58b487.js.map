{"version": 3, "file": "Card-1e58b487.js", "sources": ["../../../shared/src/components/molecules/Card.tsx"], "sourcesContent": ["/**\n * Card Component\n *\n * A customizable card component that follows the design system.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\n\nexport type CardVariant = 'default' | 'primary' | 'secondary' | 'outlined' | 'elevated';\nexport type CardPadding = 'none' | 'small' | 'medium' | 'large';\n\nexport interface CardProps {\n  /** The content to display inside the card */\n  children: React.ReactNode;\n  /** The title of the card */\n  title?: string;\n  /** The subtitle of the card */\n  subtitle?: string;\n  /** Whether the card has a border */\n  bordered?: boolean;\n  /** The variant of the card */\n  variant?: CardVariant;\n  /** The padding size of the card */\n  padding?: CardPadding;\n  /** Additional CSS class names */\n  className?: string;\n  /** Optional footer content */\n  footer?: React.ReactNode;\n  /** Optional action buttons for the header */\n  actions?: React.ReactNode;\n  /** Whether the card is in a loading state */\n  isLoading?: boolean;\n  /** Whether the card has an error */\n  hasError?: boolean;\n  /** Error message to display */\n  errorMessage?: string;\n  /** Whether the card is clickable */\n  clickable?: boolean;\n  /** Function called when the card is clicked */\n  onClick?: () => void;\n}\n\n// Padding styles\nconst paddingStyles = {\n  none: css`\n    padding: 0;\n  `,\n  small: css`\n    padding: ${({ theme }) => theme.spacing.sm};\n  `,\n  medium: css`\n    padding: ${({ theme }) => theme.spacing.md};\n  `,\n  large: css`\n    padding: ${({ theme }) => theme.spacing.lg};\n  `,\n};\n\n// Variant styles\nconst variantStyles = {\n  default: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n  `,\n  primary: css`\n    background-color: ${({ theme }) => theme.colors.primary}10;\n    border-color: ${({ theme }) => theme.colors.primary}30;\n  `,\n  secondary: css`\n    background-color: ${({ theme }) => theme.colors.secondary}10;\n    border-color: ${({ theme }) => theme.colors.secondary}30;\n  `,\n  outlined: css`\n    background-color: transparent;\n    border: 1px solid ${({ theme }) => theme.colors.border};\n  `,\n  elevated: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n    box-shadow: ${({ theme }) => theme.shadows.md};\n    border: none;\n  `,\n};\n\nconst CardContainer = styled.div<{\n  bordered: boolean;\n  variant: CardVariant;\n  padding: CardPadding;\n  clickable: boolean;\n}>`\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  overflow: hidden;\n  transition: all ${({ theme }) => theme.transitions.fast};\n  position: relative;\n\n  /* Border styles */\n  ${({ bordered, theme }) =>\n    bordered &&\n    css`\n      border: 1px solid ${theme.colors.border};\n    `}\n\n  /* Apply padding styles */\n  ${({ padding }) => paddingStyles[padding]}\n\n  /* Apply variant styles */\n  ${({ variant }) => variantStyles[variant]}\n\n  /* Clickable styles */\n  ${({ clickable }) =>\n    clickable &&\n    css`\n      cursor: pointer;\n\n      &:hover {\n        transform: translateY(-2px);\n        box-shadow: ${({ theme }) => theme.shadows.md};\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    `}\n`;\n\nconst CardHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst HeaderContent = styled.div`\n  flex: 1;\n`;\n\nconst CardTitle = styled.h3`\n  margin: 0;\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst CardSubtitle = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst ActionsContainer = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CardContent = styled.div``;\n\nconst CardFooter = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  padding-top: ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst LoadingOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: ${({ theme }) => `${theme.colors.background}80`};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1;\n`;\n\nconst ErrorContainer = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.error}10;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.error};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 32px;\n  height: 32px;\n  border: 3px solid ${({ theme }) => theme.colors.background};\n  border-top: 3px solid ${({ theme }) => theme.colors.primary};\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\n/**\n * Card Component\n *\n * A customizable card component that follows the design system.\n */\nexport const Card: React.FC<CardProps> = ({\n  children,\n  title = '',\n  subtitle = '',\n  bordered = true,\n  variant = 'default' as CardVariant,\n  padding = 'medium' as CardPadding,\n  className = '',\n  footer,\n  actions,\n  isLoading = false,\n  hasError = false,\n  errorMessage = 'An error occurred',\n  clickable = false,\n  onClick,\n  ...rest\n}) => {\n  const hasHeader = title || subtitle || actions;\n\n  return (\n    <CardContainer\n      bordered={bordered}\n      variant={variant}\n      padding={padding}\n      clickable={clickable}\n      className={className}\n      onClick={clickable ? onClick : undefined}\n      {...rest}\n    >\n      {isLoading && (\n        <LoadingOverlay>\n          <LoadingSpinner />\n        </LoadingOverlay>\n      )}\n\n      {hasHeader && (\n        <CardHeader>\n          <HeaderContent>\n            {title && <CardTitle>{title}</CardTitle>}\n            {subtitle && <CardSubtitle>{subtitle}</CardSubtitle>}\n          </HeaderContent>\n          {actions && <ActionsContainer>{actions}</ActionsContainer>}\n        </CardHeader>\n      )}\n\n      {hasError && (\n        <ErrorContainer>\n          <p>{errorMessage}</p>\n        </ErrorContainer>\n      )}\n\n      <CardContent>{children}</CardContent>\n\n      {footer && <CardFooter>{footer}</CardFooter>}\n    </CardContainer>\n  );\n};\n"], "names": ["paddingStyles", "none", "css", "small", "theme", "spacing", "sm", "medium", "md", "large", "lg", "variantStyles", "default", "colors", "surface", "primary", "secondary", "outlined", "border", "elevated", "shadows", "CardContainer", "div", "withConfig", "displayName", "componentId", "borderRadius", "transitions", "fast", "bordered", "padding", "variant", "clickable", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CardTitle", "h3", "fontSizes", "fontWeights", "semibold", "textPrimary", "CardSubtitle", "xs", "textSecondary", "ActionsContainer", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>er", "LoadingOverlay", "background", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "LoadingSpinner", "Card", "children", "title", "subtitle", "className", "footer", "actions", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "onClick", "rest", "<PERSON><PERSON><PERSON><PERSON>", "jsxs", "undefined", "jsx"], "mappings": "oGA2CA,MAAMA,EAAgB,CACpBC,KAAMC,EAEL,CAAA,YAAA,CAAA,EACDC,MAAOD,EAAG,CAAA,WAAA,GAAA,EACG,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMC,QAAQC,EAAE,EAE5CC,OAAQL,EAAG,CAAA,WAAA,GAAA,EACE,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMC,QAAQG,EAAE,EAE5CC,MAAOP,EAAG,CAAA,WAAA,GAAA,EACG,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMC,QAAQK,EAAE,CAE9C,EAGMC,EAAgB,CACpBC,QAASV,EAAG,CAAA,oBAAA,GAAA,EACU,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMS,OAAOC,OAAO,EAEzDC,QAASb,EAAG,CAAA,oBAAA,mBAAA,KAAA,EACU,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMS,OAAOE,QAChC,CAAC,CAAEX,MAAAA,CAAAA,IAAYA,EAAMS,OAAOE,OAAO,EAErDC,UAAWd,EAAG,CAAA,oBAAA,mBAAA,KAAA,EACQ,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMS,OAAOG,UAChC,CAAC,CAAEZ,MAAAA,CAAAA,IAAYA,EAAMS,OAAOG,SAAS,EAEvDC,SAAUf,EAAG,CAAA,iDAAA,GAAA,EAES,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMS,OAAOK,MAAM,EAExDC,SAAUjB,EAAG,CAAA,oBAAA,eAAA,eAAA,EACS,CAAC,CAAEE,MAAAA,CAAAA,IAAYA,EAAMS,OAAOC,QAClC,CAAC,CAAEV,MAAAA,CAAAA,IAAYA,EAAMgB,QAAQZ,EAAE,CAGjD,EAEMa,EAAuBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,mCAAA,sBAAA,IAAA,IAAA,IAAA,EAAA,EAMb,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMsB,aAAalB,GAEjC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMuB,YAAYC,KAIjD,CAAC,CAAEC,SAAAA,EAAUzB,MAAAA,CAAM,IACnByB,GACA3B,EACsBE,CAAAA,oBAAAA,GAAAA,EAAAA,EAAMS,OAAOK,MAAM,EAIzC,CAAC,CAAEY,QAAAA,CAAQ,IAAM9B,EAAc8B,CAAO,EAGtC,CAAC,CAAEC,QAAAA,CAAQ,IAAMpB,EAAcoB,CAAO,EAGtC,CAAC,CAAEC,UAAAA,CAAU,IACbA,GACA9B,2GAKkB,CAAC,CAAEE,MAAAA,CAAM,IAAMA,EAAMgB,QAAQZ,EAAE,CAMhD,EAGCyB,EAAoBX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,mFAAA,GAAA,EAIV,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMC,QAAQG,EAAE,EAG5C0B,EAAuBZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAE/B,EAAA,CAAA,SAAA,CAAA,EAEKU,EAAmBC,EAAAA,GAAEb,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,sBAAA,gBAAA,UAAA,GAAA,EAEZ,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMiC,UAAU3B,GAC7B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMkC,YAAYC,SACvC,CAAC,CAAEnC,MAAAA,CAAM,IAAMA,EAAMS,OAAO2B,WAAW,EAG5CC,EAAsBnB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,cAAA,UAAA,GAAA,EACf,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMC,QAAQqC,GAC9B,CAAC,CAAEtC,MAAAA,CAAM,IAAMA,EAAMiC,UAAU/B,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMS,OAAO8B,aAAa,EAG9CC,EAA0BtB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAE1B,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAGlCuC,EAAqBvB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAE1BqB,EAAoBxB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,gBAAA,yBAAA,GAAA,EACb,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMC,QAAQG,GAC5B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMC,QAAQG,GACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMS,OAAOK,MAAM,EAGtD6B,EAAwBzB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oEAAA,oEAAA,EAMX,CAAC,CAAErB,MAAAA,CAAM,IAAM,GAAGA,EAAMS,OAAOmC,cAAc,EAO7DC,EAAwB3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,oBAAA,UAAA,kBAAA,GAAA,EACpB,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMC,QAAQG,GACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMS,OAAOqC,MAC/B,CAAC,CAAE9C,MAAAA,CAAM,IAAMA,EAAMsB,aAAapB,GAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMS,OAAOqC,MACpB,CAAC,CAAE9C,MAAAA,CAAM,IAAMA,EAAMC,QAAQG,EAAE,EAG5C2C,EAAwB7B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2CAAA,yBAAA,kIAAA,EAGX,CAAC,CAAErB,MAAAA,CAAM,IAAMA,EAAMS,OAAOmC,WACxB,CAAC,CAAE5C,MAAAA,CAAM,IAAMA,EAAMS,OAAOE,OAAO,EAmBhDqC,EAA4BA,CAAC,CACxCC,SAAAA,EACAC,MAAAA,EAAQ,GACRC,SAAAA,EAAW,GACX1B,SAAAA,EAAW,GACXE,QAAAA,EAAU,UACVD,QAAAA,EAAU,SACV0B,UAAAA,EAAY,GACZC,OAAAA,EACAC,QAAAA,EACAC,UAAAA,EAAY,GACZC,SAAAA,EAAW,GACXC,aAAAA,EAAe,oBACf7B,UAAAA,EAAY,GACZ8B,QAAAA,EACA,GAAGC,CACL,IAAM,CACEC,MAAAA,EAAYV,GAASC,GAAYG,EAEvC,OACGO,EAAAA,KAAA5C,EAAA,CACC,SAAAQ,EACA,QAAAE,EACA,QAAAD,EACA,UAAAE,EACA,UAAAwB,EACA,QAASxB,EAAY8B,EAAUI,OAC3BH,GAAAA,EAEHJ,SAAAA,CAAAA,GACEQ,EAAAA,IAAApB,EAAA,CACC,SAACoB,EAAAA,IAAAhB,EAAA,CAAc,CAAA,EACjB,EAGDa,UACE/B,EACC,CAAA,SAAA,CAAAgC,OAAC/B,EACEoB,CAAAA,SAAAA,CAASA,GAAAa,EAAAA,IAAChC,GAAWmB,SAAMA,CAAA,CAAA,EAC3BC,GAAaY,EAAAA,IAAA1B,EAAA,CAAcc,SAASA,CAAA,CAAA,CAAA,EACvC,EACCG,GAAYS,EAAAA,IAAAvB,EAAA,CAAkBc,SAAQA,CAAA,CAAA,CAAA,EACzC,EAGDE,GACEO,EAAA,IAAAlB,EAAA,CACC,SAACkB,EAAAA,IAAA,IAAA,CAAGN,UAAa,CAAA,EACnB,EAGFM,MAACtB,GAAaQ,SAAAA,EAAS,EAEtBI,GAAWU,EAAAA,IAAArB,EAAA,CAAYW,SAAOA,CAAA,CAAA,CACjC,CAAA,CAAA,CAEJ"}