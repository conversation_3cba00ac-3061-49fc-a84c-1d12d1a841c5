{"version": 3, "file": "TradeAnalysis-ccd21b25.js", "sources": ["../../../shared/src/components/atoms/Badge.tsx", "../../../shared/src/components/atoms/Button.tsx", "../../../shared/src/components/atoms/LoadingPlaceholder.tsx", "../../../shared/src/components/atoms/Tag.tsx", "../../../shared/src/components/atoms/LoadingSpinner.tsx", "../../../shared/src/components/molecules/EmptyState.tsx", "../../../shared/src/components/organisms/DataCard.tsx", "../../../shared/src/hooks/useLocalStorage.ts", "../../src/features/trade-analysis/services/tradeAnalysisCalculations.ts", "../../src/features/trade-analysis/services/performanceCache.ts", "../../src/features/trade-analysis/services/realTradeAnalysisApi.ts", "../../src/features/trade-analysis/services/tradeAnalysisApi.ts", "../../src/features/trade-analysis/hooks/TradeAnalysisContext.tsx", "../../src/features/trade-analysis/components/AnalysisHeader.tsx", "../../src/features/trade-analysis/components/AnalysisTabs.tsx", "../../src/features/trade-analysis/components/FilterPanel.tsx", "../../src/features/trade-analysis/components/PerformanceSummary.tsx", "../../src/features/trade-analysis/components/TradesTableHeader.tsx", "../../src/features/trade-analysis/components/TradesTableRow.tsx", "../../src/features/trade-analysis/components/TradesTableBody.tsx", "../../src/features/trade-analysis/hooks/useTradesTableData.ts", "../../src/features/trade-analysis/components/TradesTableContainer.tsx", "../../src/features/trade-analysis/components/TradesTable.tsx", "../../src/features/trade-analysis/components/CategoryPerformanceChart.tsx", "../../src/features/trade-analysis/components/TimePerformanceChart.tsx", "../../src/features/trade-analysis/components/TradeDetail.tsx", "../../src/features/trade-analysis/components/TabContentRenderer.tsx", "../../src/features/trade-analysis/components/TradeAnalysisContainer.tsx", "../../src/features/trade-analysis/TradeAnalysis.tsx"], "sourcesContent": ["/**\n * Badge Component\n *\n * A customizable badge component for displaying status, labels, or counts.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\n\nexport type BadgeVariant =\n  | 'default'\n  | 'primary'\n  | 'secondary'\n  | 'success'\n  | 'warning'\n  | 'error'\n  | 'info'\n  | 'neutral';\nexport type BadgeSize = 'small' | 'medium' | 'large';\n\nexport interface BadgeProps {\n  /** The content to display inside the badge */\n  children: React.ReactNode;\n  /** The variant of the badge */\n  variant?: BadgeVariant;\n  /** The size of the badge */\n  size?: BadgeSize;\n  /** Whether the badge has a solid background */\n  solid?: boolean;\n  /** Additional CSS class names */\n  className?: string;\n  /** Inline styles */\n  style?: React.CSSProperties;\n  /** Optional click handler */\n  onClick?: () => void;\n  /** Whether the badge is rounded (pill-shaped) */\n  rounded?: boolean;\n  /** Whether the badge is a dot (no content) */\n  dot?: boolean;\n  /** Whether the badge is a counter */\n  counter?: boolean;\n  /** Whether the badge is outlined */\n  outlined?: boolean;\n  /** Icon to display before the badge text */\n  startIcon?: React.ReactNode;\n  /** Icon to display after the badge text */\n  endIcon?: React.ReactNode;\n  /** Maximum number to display (for counter badges) */\n  max?: number;\n  /** Whether the badge is inline */\n  inline?: boolean;\n}\n\n// Size styles\nconst sizeStyles = {\n  small: css<{ dot?: boolean }>`\n    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};\n    font-size: ${({ theme }) => theme.fontSizes.xs};\n    min-height: 20px;\n    min-width: ${({ dot }) => (dot ? '8px' : '20px')};\n  `,\n  medium: css<{ dot?: boolean }>`\n    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};\n    font-size: ${({ theme }) => theme.fontSizes.sm};\n    min-height: 24px;\n    min-width: ${({ dot }) => (dot ? '10px' : '24px')};\n  `,\n  large: css<{ dot?: boolean }>`\n    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};\n    font-size: ${({ theme }) => theme.fontSizes.md};\n    min-height: 32px;\n    min-width: ${({ dot }) => (dot ? '12px' : '32px')};\n  `,\n};\n\n// Variant styles\nconst getVariantStyles = (variant: BadgeVariant, solid: boolean, outlined: boolean = false) => {\n  return css`\n    ${({ theme }) => {\n      // Get the appropriate colors based on the variant\n      let bgColor, textColor, borderColor;\n\n      switch (variant) {\n        case 'primary':\n          bgColor = solid ? theme.colors.primary : `${theme.colors.primary}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.primary;\n          borderColor = theme.colors.primary;\n          break;\n        case 'secondary':\n          bgColor = solid ? theme.colors.secondary : `${theme.colors.secondary}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.secondary;\n          borderColor = theme.colors.secondary;\n          break;\n        case 'success':\n          bgColor = solid ? theme.colors.success : `${theme.colors.success}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.success;\n          borderColor = theme.colors.success;\n          break;\n        case 'warning':\n          bgColor = solid ? theme.colors.warning : `${theme.colors.warning}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.warning;\n          borderColor = theme.colors.warning;\n          break;\n        case 'error':\n          bgColor = solid ? theme.colors.error : `${theme.colors.error}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.error;\n          borderColor = theme.colors.error;\n          break;\n        case 'info':\n          bgColor = solid ? theme.colors.info : `${theme.colors.info}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.info;\n          borderColor = theme.colors.info;\n          break;\n        case 'neutral':\n          bgColor = solid ? theme.colors.textSecondary : `${theme.colors.textSecondary}10`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.textSecondary;\n          borderColor = theme.colors.textSecondary;\n          break;\n        default: // 'default'\n          bgColor = solid ? theme.colors.textSecondary : `${theme.colors.textSecondary}20`;\n          textColor = solid ? theme.colors.textInverse : theme.colors.textSecondary;\n          borderColor = theme.colors.textSecondary;\n      }\n\n      if (outlined) {\n        return `\n          background-color: transparent;\n          color: ${borderColor};\n          border: 1px solid ${borderColor};\n        `;\n      }\n\n      return `\n        background-color: ${bgColor};\n        color: ${textColor};\n        border: 1px solid transparent;\n      `;\n    }}\n  `;\n};\n\nconst IconContainer = styled.span`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst StartIcon = styled(IconContainer)`\n  margin-right: ${({ theme }) => theme.spacing.xxs};\n`;\n\nconst EndIcon = styled(IconContainer)`\n  margin-left: ${({ theme }) => theme.spacing.xxs};\n`;\n\nconst StyledBadge = styled.span<{\n  variant: BadgeVariant;\n  size: BadgeSize;\n  solid: boolean;\n  clickable: boolean;\n  rounded?: boolean;\n  dot?: boolean;\n  counter?: boolean;\n  outlined?: boolean;\n  inline?: boolean;\n}>`\n  display: ${({ inline }) => (inline ? 'inline-flex' : 'flex')};\n  align-items: center;\n  justify-content: center;\n  border-radius: ${({ theme, rounded, dot }) =>\n    dot ? '50%' : rounded ? '9999px' : theme.borderRadius.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  white-space: nowrap;\n\n  /* Apply size styles */\n  ${({ size }) => sizeStyles[size]}\n\n  /* Apply variant styles */\n  ${({ variant, solid, outlined }) => getVariantStyles(variant, solid, outlined || false)}\n\n  /* Dot style */\n  ${({ dot }) =>\n    dot &&\n    css`\n      padding: 0;\n      height: 8px;\n      width: 8px;\n    `}\n\n  /* Counter style */\n  ${({ counter }) =>\n    counter &&\n    css`\n      min-width: 1.5em;\n      height: 1.5em;\n      padding: 0 0.5em;\n      border-radius: 1em;\n    `}\n\n  /* Clickable styles */\n  ${({ clickable }) =>\n    clickable &&\n    css`\n      cursor: pointer;\n      transition: opacity ${({ theme }) => theme.transitions.fast};\n\n      &:hover {\n        opacity: 0.8;\n      }\n\n      &:active {\n        opacity: 0.6;\n      }\n    `}\n`;\n\n/**\n * Badge Component\n *\n * A customizable badge component for displaying status, labels, or counts.\n */\nexport const Badge: React.FC<BadgeProps> = ({\n  children,\n  variant = 'default' as BadgeVariant,\n  size = 'medium' as BadgeSize,\n  solid = false,\n  className = '',\n  style,\n  onClick,\n  rounded = false,\n  dot = false,\n  counter = false,\n  outlined = false,\n  startIcon,\n  endIcon,\n  max,\n  inline = true,\n}) => {\n  // Format content for counter badges with max value\n  let content = children;\n  if (counter && typeof children === 'number' && max !== undefined && children > max) {\n    content = `${max}+`;\n  }\n\n  return (\n    <StyledBadge\n      variant={variant}\n      size={size}\n      solid={solid}\n      clickable={!!onClick}\n      className={className}\n      style={style}\n      onClick={onClick}\n      rounded={rounded}\n      dot={dot}\n      counter={counter}\n      outlined={outlined}\n      inline={inline}\n    >\n      {!dot && (\n        <>\n          {startIcon && <StartIcon>{startIcon}</StartIcon>}\n          {content}\n          {endIcon && <EndIcon>{endIcon}</EndIcon>}\n        </>\n      )}\n    </StyledBadge>\n  );\n};\n", "/**\n * Button Component\n *\n * A customizable button component that follows the design system.\n */\nimport React from 'react';\nimport styled, { css, keyframes } from 'styled-components';\n\nexport type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'text' | 'success' | 'danger';\nexport type ButtonSize = 'small' | 'medium' | 'large';\n\nexport interface ButtonProps\n  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'size' | 'type'> {\n  /** The content to display inside the button */\n  children: React.ReactNode;\n  /** The variant of the button */\n  variant?: ButtonVariant;\n  /** Whether the button is disabled */\n  disabled?: boolean;\n  /** Whether the button is in a loading state */\n  loading?: boolean;\n  /** The size of the button */\n  size?: ButtonSize;\n  /** Whether the button is full width */\n  fullWidth?: boolean;\n  /** Icon to display before the button text */\n  startIcon?: React.ReactNode;\n  /** Icon to display after the button text */\n  endIcon?: React.ReactNode;\n  /** Function called when the button is clicked */\n  onClick?: () => void;\n  /** Additional CSS class names */\n  className?: string;\n  /** Button type */\n  type?: 'button' | 'submit' | 'reset';\n}\n\nconst spin = keyframes`\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: #fff;\n  animation: ${spin} 0.8s linear infinite;\n  margin-right: ${({ theme }) => theme.spacing.xs};\n`;\n\n// Size styles\nconst sizeStyles = {\n  small: css`\n    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.sm}`};\n    font-size: ${({ theme }) => theme.fontSizes.xs};\n    min-height: 32px;\n  `,\n  medium: css`\n    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.md}`};\n    font-size: ${({ theme }) => theme.fontSizes.sm};\n    min-height: 40px;\n  `,\n  large: css`\n    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.lg}`};\n    font-size: ${({ theme }) => theme.fontSizes.md};\n    min-height: 48px;\n  `,\n};\n\n// Variant styles\nconst variantStyles = {\n  primary: css`\n    background-color: ${({ theme }) => theme.colors.primary};\n    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};\n    border: none;\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primaryDark};\n      transform: translateY(-1px);\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    }\n\n    &:active:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primaryDark};\n      transform: translateY(0);\n      box-shadow: none;\n    }\n  `,\n  secondary: css`\n    background-color: ${({ theme }) => theme.colors.secondary};\n    color: ${({ theme }) => theme.colors.textPrimary || theme.colors.textInverse || '#fff'};\n    border: none;\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.secondaryDark};\n      transform: translateY(-1px);\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    }\n\n    &:active:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.secondaryDark};\n      transform: translateY(0);\n      box-shadow: none;\n    }\n  `,\n  outline: css`\n    background-color: transparent;\n    color: ${({ theme }) => theme.colors.primary};\n    border: 1px solid ${({ theme }) => theme.colors.primary};\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */\n      transform: translateY(-1px);\n    }\n\n    &:active:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */\n      transform: translateY(0);\n    }\n  `,\n  text: css`\n    background-color: transparent;\n    color: ${({ theme }) => theme.colors.primary};\n    border: none;\n    padding-left: ${({ theme }) => theme.spacing.xs};\n    padding-right: ${({ theme }) => theme.spacing.xs};\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primary}0d; /* 5% opacity */\n    }\n\n    &:active:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.primary}1a; /* 10% opacity */\n    }\n  `,\n  success: css`\n    background-color: ${({ theme }) => theme.colors.success};\n    color: ${({ theme }) => theme.colors.textInverse || '#fff'};\n    border: none;\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.success}dd;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    }\n\n    &:active:not(:disabled) {\n      transform: translateY(0);\n      box-shadow: none;\n    }\n  `,\n  danger: css`\n    background-color: ${({ theme }) => theme.colors.error};\n    color: ${({ theme }) => theme.colors.textInverse || '#fff'};\n    border: none;\n\n    &:hover:not(:disabled) {\n      background-color: ${({ theme }) => theme.colors.error}dd;\n      transform: translateY(-1px);\n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    }\n\n    &:active:not(:disabled) {\n      transform: translateY(0);\n      box-shadow: none;\n    }\n  `,\n};\n\n// Define the props that StyledButton will accept\ntype StyledButtonProps = {\n  variant?: ButtonVariant;\n  size?: ButtonSize;\n  disabled?: boolean;\n  fullWidth?: boolean;\n  $hasStartIcon?: boolean;\n  $hasEndIcon?: boolean;\n};\n\nconst StyledButton = styled.button<StyledButtonProps>`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};\n  cursor: pointer;\n  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};\n  position: relative;\n  overflow: hidden;\n\n  /* Apply size styles */\n  ${({ size = 'medium' }) => sizeStyles[size]}\n\n  /* Apply variant styles */\n  ${({ variant = 'primary' }) => variantStyles[variant]}\n\n  /* Full width style */\n  ${({ fullWidth }) =>\n    fullWidth &&\n    css`\n      width: 100%;\n    `}\n\n  /* Disabled state */\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    box-shadow: none;\n    transform: translateY(0);\n  }\n\n  /* Icon spacing */\n  ${({ $hasStartIcon }) =>\n    $hasStartIcon &&\n    css`\n      & > *:first-child {\n        margin-right: ${({ theme }) => theme.spacing.xs};\n      }\n    `}\n\n  ${({ $hasEndIcon }) =>\n    $hasEndIcon &&\n    css`\n      & > *:last-child {\n        margin-left: ${({ theme }) => theme.spacing.xs};\n      }\n    `}\n`;\n\nconst ButtonContent = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\n/**\n * Button Component\n *\n * A customizable button component that follows the design system.\n */\nexport const Button: React.FC<ButtonProps> = ({\n  children,\n  variant = 'primary' as ButtonVariant,\n  disabled = false,\n  loading = false,\n  size = 'medium' as ButtonSize,\n  fullWidth = false,\n  startIcon,\n  endIcon,\n  onClick,\n  className = '',\n  type = 'button' as 'button' | 'submit' | 'reset',\n  ...rest\n}) => {\n  return (\n    <StyledButton\n      variant={variant}\n      disabled={disabled || loading}\n      size={size}\n      fullWidth={fullWidth}\n      onClick={onClick}\n      className={className}\n      type={type}\n      $hasStartIcon={!!startIcon && !loading}\n      $hasEndIcon={!!endIcon && !loading}\n      {...rest}\n    >\n      <ButtonContent>\n        {loading && <LoadingSpinner />}\n        {!loading && startIcon}\n        {children}\n        {!loading && endIcon}\n      </ButtonContent>\n    </StyledButton>\n  );\n};\n", "/**\n * Loading Placeholder Component\n *\n * A component for displaying loading states with customizable appearance.\n */\nimport React from 'react';\nimport styled, { css, keyframes } from 'styled-components';\n\nexport type LoadingPlaceholderVariant = 'default' | 'card' | 'text' | 'list';\nexport type LoadingPlaceholderSize = 'small' | 'medium' | 'large' | 'custom';\n\nexport interface LoadingPlaceholderProps {\n  /** The variant of the loading placeholder */\n  variant?: LoadingPlaceholderVariant;\n  /** The size of the loading placeholder */\n  size?: LoadingPlaceholderSize;\n  /** Custom height (only used when size is 'custom') */\n  height?: string;\n  /** Custom width (only used when size is 'custom') */\n  width?: string;\n  /** Text to display in the loading placeholder */\n  text?: string;\n  /** Whether to show a spinner */\n  showSpinner?: boolean;\n  /** Additional CSS class names */\n  className?: string;\n}\n\n// Size styles\nconst sizeStyles = {\n  small: css`\n    height: 100px;\n  `,\n  medium: css`\n    height: 200px;\n  `,\n  large: css`\n    height: 300px;\n  `,\n  custom: (props: { customHeight: string; customWidth?: string }) => css`\n    height: ${props.customHeight};\n    width: ${props.customWidth || '100%'};\n  `,\n};\n\n// Variant styles\nconst variantStyles = {\n  default: css`\n    background-color: ${({ theme }) => theme.colors.background};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n  `,\n  card: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n    box-shadow: ${({ theme }) => theme.shadows.sm};\n  `,\n  text: css`\n    background-color: transparent;\n    height: auto !important;\n    min-height: 1.5em;\n  `,\n  list: css`\n    background-color: ${({ theme }) => theme.colors.background};\n    border-radius: ${({ theme }) => theme.borderRadius.sm};\n    margin-bottom: ${({ theme }) => theme.spacing.sm};\n  `,\n};\n\n// Spinner animation\nconst spin = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\n\nconst Container = styled.div<{\n  variant: LoadingPlaceholderVariant;\n  size: LoadingPlaceholderSize;\n  customHeight: string;\n  customWidth?: string;\n}>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* Apply size styles */\n  ${({ size, customHeight, customWidth }) => {\n    if (size === 'custom') {\n      return sizeStyles.custom({ customHeight, customWidth });\n    }\n    return sizeStyles[size];\n  }}\n\n  /* Apply variant styles */\n  ${({ variant }) => variantStyles[variant]}\n`;\n\nconst Spinner = styled.div`\n  width: 32px;\n  height: 32px;\n  border: 3px solid ${({ theme }) => theme.colors.background};\n  border-top: 3px solid ${({ theme }) => theme.colors.primary};\n  border-radius: 50%;\n  animation: ${spin} 1s linear infinite;\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst Text = styled.div`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\n/**\n * Loading Placeholder Component\n *\n * A component for displaying loading states with customizable appearance.\n */\nexport const LoadingPlaceholder: React.FC<LoadingPlaceholderProps> = ({\n  variant = 'default' as LoadingPlaceholderVariant,\n  size = 'medium' as LoadingPlaceholderSize,\n  height = '200px',\n  width = '',\n  text = 'Loading...',\n  showSpinner = true,\n  className = '',\n}) => {\n  return (\n    <Container\n      variant={variant}\n      size={size}\n      customHeight={height}\n      customWidth={width}\n      className={className}\n    >\n      {showSpinner && <Spinner />}\n      {text && <Text>{text}</Text>}\n    </Container>\n  );\n};\n", "/**\n * Tag Component\n *\n * A customizable tag component for categorizing content.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\n\nexport type TagVariant =\n  | 'default'\n  | 'primary'\n  | 'secondary'\n  | 'success'\n  | 'warning'\n  | 'error'\n  | 'info';\nexport type TagSize = 'small' | 'medium' | 'large';\n\nexport interface TagProps {\n  /** The content to display inside the tag */\n  children: React.ReactNode;\n  /** The variant of the tag */\n  variant?: TagVariant;\n  /** The size of the tag */\n  size?: TagSize;\n  /** Whether the tag is removable */\n  removable?: boolean;\n  /** Function called when the remove button is clicked */\n  onRemove?: () => void;\n  /** Additional CSS class names */\n  className?: string;\n  /** Optional click handler */\n  onClick?: () => void;\n}\n\n// Size styles\nconst sizeStyles = {\n  small: css`\n    padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};\n    font-size: ${({ theme }) => theme.fontSizes.xs};\n  `,\n  medium: css`\n    padding: ${({ theme }) => `${theme.spacing.xs} ${theme.spacing.sm}`};\n    font-size: ${({ theme }) => theme.fontSizes.sm};\n  `,\n  large: css`\n    padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.md}`};\n    font-size: ${({ theme }) => theme.fontSizes.md};\n  `,\n};\n\n// Variant styles\nconst getVariantStyles = (variant: TagVariant) => {\n  return css`\n    ${({ theme }) => {\n      // Get the appropriate colors based on the variant\n      let bgColor, textColor, borderColor;\n\n      switch (variant) {\n        case 'primary':\n          bgColor = `${theme.colors.primary}10`;\n          textColor = theme.colors.primary;\n          borderColor = `${theme.colors.primary}30`;\n          break;\n        case 'secondary':\n          bgColor = `${theme.colors.secondary}10`;\n          textColor = theme.colors.secondary;\n          borderColor = `${theme.colors.secondary}30`;\n          break;\n        case 'success':\n          bgColor = `${theme.colors.success}10`;\n          textColor = theme.colors.success;\n          borderColor = `${theme.colors.success}30`;\n          break;\n        case 'warning':\n          bgColor = `${theme.colors.warning}10`;\n          textColor = theme.colors.warning;\n          borderColor = `${theme.colors.warning}30`;\n          break;\n        case 'error':\n          bgColor = `${theme.colors.error}10`;\n          textColor = theme.colors.error;\n          borderColor = `${theme.colors.error}30`;\n          break;\n        case 'info':\n          bgColor = `${theme.colors.info}10`;\n          textColor = theme.colors.info;\n          borderColor = `${theme.colors.info}30`;\n          break;\n        default: // 'default'\n          bgColor = `${theme.colors.textSecondary}10`;\n          textColor = theme.colors.textSecondary;\n          borderColor = `${theme.colors.textSecondary}30`;\n      }\n\n      return `\n        background-color: ${bgColor};\n        color: ${textColor};\n        border: 1px solid ${borderColor};\n      `;\n    }}\n  `;\n};\n\nconst StyledTag = styled.span<{\n  variant: TagVariant;\n  size: TagSize;\n  clickable: boolean;\n}>`\n  display: inline-flex;\n  align-items: center;\n  border-radius: ${({ theme }) => theme.borderRadius.pill};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n\n  /* Apply size styles */\n  ${({ size }) => sizeStyles[size]}\n\n  /* Apply variant styles */\n  ${({ variant }) => getVariantStyles(variant)}\n\n  /* Clickable styles */\n  ${({ clickable }) =>\n    clickable &&\n    css`\n      cursor: pointer;\n      transition: opacity ${({ theme }) => theme.transitions.fast};\n\n      &:hover {\n        opacity: 0.8;\n      }\n\n      &:active {\n        opacity: 0.6;\n      }\n    `}\n`;\n\nconst RemoveButton = styled.button<{ size: TagSize }>`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  opacity: 0.7;\n  margin-left: ${({ theme }) => theme.spacing.xs};\n  padding: 0;\n\n  /* Size-specific styles */\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: '12px',\n      medium: '14px',\n      large: '16px',\n    };\n\n    return `\n      width: ${sizeMap[size]};\n      height: ${sizeMap[size]};\n      font-size: ${theme.fontSizes.xs};\n    `;\n  }}\n\n  &:hover {\n    opacity: 1;\n  }\n`;\n\n/**\n * Tag Component\n *\n * A customizable tag component for categorizing content.\n */\nexport const Tag: React.FC<TagProps> = ({\n  children,\n  variant = 'default' as TagVariant,\n  size = 'medium' as TagSize,\n  removable = false,\n  onRemove,\n  className = '',\n  onClick,\n}) => {\n  const handleRemoveClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    onRemove?.();\n  };\n\n  return (\n    <StyledTag\n      variant={variant}\n      size={size}\n      clickable={!!onClick}\n      className={className}\n      onClick={onClick}\n    >\n      {children}\n      {removable && (\n        <RemoveButton size={size} onClick={handleRemoveClick}>\n          ×\n        </RemoveButton>\n      )}\n    </StyledTag>\n  );\n};\n", "/**\n * LoadingSpinner Component\n *\n * EXTRACTED FROM: Multiple components with loading states\n * Standardized loading spinner with F1 racing theme and consistent sizing.\n *\n * BENEFITS:\n * - Consistent loading animations across the app\n * - F1 racing theme integration\n * - Multiple size variants\n * - Accessibility support\n * - Customizable colors and speeds\n */\n\nimport React from 'react';\nimport styled, { keyframes, css } from 'styled-components';\n\nexport type LoadingSpinnerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\nexport type LoadingSpinnerVariant = 'primary' | 'secondary' | 'white' | 'red';\n\nexport interface LoadingSpinnerProps {\n  /** Size variant */\n  size?: LoadingSpinnerSize;\n  /** Color variant */\n  variant?: LoadingSpinnerVariant;\n  /** Custom className */\n  className?: string;\n  /** Accessibility label */\n  'aria-label'?: string;\n  /** Animation speed (1 = normal, 2 = fast, 0.5 = slow) */\n  speed?: number;\n  /** Whether to show the F1 racing stripes */\n  showStripes?: boolean;\n}\n\n// F1-inspired spinning animation\nconst spin = keyframes`\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n`;\n\n// F1 racing stripes animation\nconst racingStripes = keyframes`\n  0% {\n    background-position: 0% 0%;\n  }\n  100% {\n    background-position: 100% 100%;\n  }\n`;\n\ninterface StyledSpinnerProps {\n  $size: LoadingSpinnerSize;\n  $variant: LoadingSpinnerVariant;\n  $speed: number;\n  $showStripes: boolean;\n}\n\nconst StyledSpinner = styled.div<StyledSpinnerProps>`\n  display: inline-block;\n  position: relative;\n\n  ${({ $size }) => {\n    switch ($size) {\n      case 'xs':\n        return css`\n          width: 16px;\n          height: 16px;\n        `;\n      case 'sm':\n        return css`\n          width: 20px;\n          height: 20px;\n        `;\n      case 'md':\n        return css`\n          width: 32px;\n          height: 32px;\n        `;\n      case 'lg':\n        return css`\n          width: 48px;\n          height: 48px;\n        `;\n      case 'xl':\n        return css`\n          width: 64px;\n          height: 64px;\n        `;\n      default:\n        return css`\n          width: 32px;\n          height: 32px;\n        `;\n    }\n  }}\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    border-radius: 50%;\n    border: 2px solid transparent;\n\n    ${({ $variant, theme }) => {\n      switch ($variant) {\n        case 'primary':\n          return css`\n            border-top-color: ${theme.colors?.primary || '#dc2626'};\n            border-right-color: ${theme.colors?.primary || '#dc2626'};\n          `;\n        case 'secondary':\n          return css`\n            border-top-color: ${theme.colors?.textSecondary || '#9ca3af'};\n            border-right-color: ${theme.colors?.textSecondary || '#9ca3af'};\n          `;\n        case 'white':\n          return css`\n            border-top-color: #ffffff;\n            border-right-color: #ffffff;\n          `;\n        case 'red':\n          return css`\n            border-top-color: #dc2626;\n            border-right-color: #dc2626;\n          `;\n        default:\n          return css`\n            border-top-color: ${theme.colors?.primary || '#dc2626'};\n            border-right-color: ${theme.colors?.primary || '#dc2626'};\n          `;\n      }\n    }}\n\n    animation: ${spin} ${({ $speed }) => 1 / $speed}s linear infinite;\n  }\n\n  ${({ $showStripes, $variant }) =>\n    $showStripes &&\n    css`\n      &::after {\n        content: '';\n        position: absolute;\n        top: 2px;\n        left: 2px;\n        right: 2px;\n        bottom: 2px;\n        border-radius: 50%;\n        background: ${$variant === 'red' || $variant === 'primary'\n          ? 'linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)'\n          : 'linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)'};\n        background-size: 8px 8px;\n        animation: ${racingStripes} ${(props: any) => 2 / props.$speed}s linear infinite;\n      }\n    `}\n`;\n\nconst SpinnerContainer = styled.div`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n`;\n\n/**\n * LoadingSpinner Component\n *\n * A standardized loading spinner with F1 racing theme integration.\n * Provides consistent loading animations across the application.\n *\n * @example\n * ```typescript\n * // Basic usage\n * <LoadingSpinner />\n *\n * // Large red spinner with racing stripes\n * <LoadingSpinner\n *   size=\"lg\"\n *   variant=\"red\"\n *   showStripes={true}\n *   speed={1.5}\n * />\n *\n * // Small secondary spinner\n * <LoadingSpinner size=\"sm\" variant=\"secondary\" />\n * ```\n */\nexport const LoadingSpinner: React.FC<LoadingSpinnerProps> = (props) => {\n  const {\n    size = 'md',\n    variant = 'primary',\n    className,\n    'aria-label': ariaLabel,\n    speed = 1,\n    showStripes = false,\n  } = props;\n  return (\n    <SpinnerContainer className={className}>\n      <StyledSpinner\n        $size={size as LoadingSpinnerSize}\n        $variant={variant as LoadingSpinnerVariant}\n        $speed={speed}\n        $showStripes={showStripes}\n        role=\"status\"\n        aria-label={ariaLabel || 'Loading'}\n        aria-live=\"polite\"\n      />\n    </SpinnerContainer>\n  );\n};\n\nexport default LoadingSpinner;\n", "/**\n * Empty State Component\n *\n * A component for displaying empty states with customizable appearance.\n */\nimport React from 'react';\nimport styled, { css } from 'styled-components';\nimport { Button } from '../atoms/Button';\n\nexport type EmptyStateVariant = 'default' | 'compact' | 'card';\nexport type EmptyStateSize = 'small' | 'medium' | 'large';\n\nexport interface EmptyStateProps {\n  /** The title of the empty state */\n  title?: string;\n  /** The description of the empty state */\n  description?: string;\n  /** The icon to display (as a component) */\n  icon?: React.ReactNode;\n  /** The action button text */\n  actionText?: string;\n  /** Function called when the action button is clicked */\n  onAction?: () => void;\n  /** The variant of the empty state */\n  variant?: EmptyStateVariant;\n  /** The size of the empty state */\n  size?: EmptyStateSize;\n  /** Additional CSS class names */\n  className?: string;\n  /** Additional content to display */\n  children?: React.ReactNode;\n}\n\n// Define styled components first to avoid reference errors\nconst Title = styled.h3<{ size: EmptyStateSize }>`\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: theme.fontSizes.md,\n      medium: theme.fontSizes.lg,\n      large: theme.fontSizes.xl,\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n    `;\n  }}\n`;\n\nconst Description = styled.p<{ size: EmptyStateSize }>`\n  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;\n  color: ${({ theme }) => theme.colors.textSecondary};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: theme.fontSizes.sm,\n      medium: theme.fontSizes.md,\n      large: theme.fontSizes.lg,\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n    `;\n  }}\n`;\n\n// Size styles - removed nested component references to avoid circular dependencies\n\n// Variant styles\nconst variantStyles = {\n  default: css`\n    background-color: transparent;\n  `,\n  compact: css`\n    background-color: transparent;\n    text-align: left;\n    align-items: flex-start;\n  `,\n  card: css`\n    background-color: ${({ theme }) => theme.colors.surface};\n    border-radius: ${({ theme }) => theme.borderRadius.md};\n    box-shadow: ${({ theme }) => theme.shadows.sm};\n  `,\n};\n\nconst Container = styled.div<{\n  variant: EmptyStateVariant;\n  size: EmptyStateSize;\n}>`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  width: 100%;\n\n  /* Apply variant styles */\n  ${({ variant }) => variantStyles[variant]}\n\n  /* Apply size styles - using a function to avoid styled-components warning about circular references */\n  ${({ size, theme }) => {\n    switch (size) {\n      case 'small':\n        return css`\n          padding: ${theme.spacing.md};\n          min-height: 120px;\n        `;\n      case 'large':\n        return css`\n          padding: ${theme.spacing.xl};\n          min-height: 300px;\n        `;\n      default: // 'medium'\n        return css`\n          padding: ${theme.spacing.lg};\n          min-height: 200px;\n        `;\n    }\n  }}\n`;\n\nconst IconContainer = styled.div<{ size: EmptyStateSize }>`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n\n  ${({ size, theme }) => {\n    const sizeMap = {\n      small: '32px',\n      medium: '48px',\n      large: '64px',\n    };\n\n    return css`\n      font-size: ${sizeMap[size]};\n\n      svg {\n        width: ${sizeMap[size]};\n        height: ${sizeMap[size]};\n        color: ${theme.colors.textSecondary};\n      }\n    `;\n  }}\n`;\n\n// Title and Description components are defined at the top of the file\n\nconst ActionContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ChildrenContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n  width: 100%;\n`;\n\n/**\n * Empty State Component\n *\n * A component for displaying empty states with customizable appearance.\n */\nexport const EmptyState: React.FC<EmptyStateProps> = ({\n  title = '',\n  description = '',\n  icon,\n  actionText = '',\n  onAction,\n  variant = 'default' as EmptyStateVariant,\n  size = 'medium' as EmptyStateSize,\n  className = '',\n  children,\n}) => {\n  return (\n    <Container variant={variant} size={size} className={className}>\n      {icon && <IconContainer size={size}>{icon}</IconContainer>}\n\n      {title && <Title size={size}>{title}</Title>}\n      {description && <Description size={size}>{description}</Description>}\n\n      {actionText && onAction && (\n        <ActionContainer>\n          <Button variant=\"primary\" size={size === 'small' ? 'small' : 'medium'} onClick={onAction}>\n            {actionText}\n          </Button>\n        </ActionContainer>\n      )}\n\n      {children && <ChildrenContainer>{children}</ChildrenContainer>}\n    </Container>\n  );\n};\n", "/**\n * DataCard Component\n *\n * A specialized card component for displaying data sections with loading and error states.\n */\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Card, CardProps } from '../molecules/Card';\n// Button import removed as it's not used\nimport { LoadingPlaceholder } from '../atoms/LoadingPlaceholder';\nimport { EmptyState } from '../molecules/EmptyState';\n\nexport interface DataCardProps extends Omit<CardProps, 'isLoading' | 'hasError' | 'errorMessage'> {\n  /** The title of the data card */\n  title: string;\n  /** The content to display inside the data card */\n  children: React.ReactNode;\n  /** Whether the data is loading */\n  isLoading?: boolean;\n  /** Whether there was an error loading the data */\n  hasError?: boolean;\n  /** Error message to display */\n  errorMessage?: string;\n  /** Whether to show a retry button when there's an error */\n  showRetry?: boolean;\n  /** Function called when the retry button is clicked */\n  onRetry?: () => void;\n  /** Whether the data is empty */\n  isEmpty?: boolean;\n  /** Empty state message */\n  emptyMessage?: string;\n  /** Empty state action text */\n  emptyActionText?: string;\n  /** Function called when the empty state action button is clicked */\n  onEmptyAction?: () => void;\n  /** Action button to display in the header */\n  actionButton?: React.ReactNode;\n  /** Additional CSS class names */\n  className?: string;\n}\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\n/**\n * DataCard Component\n *\n * A specialized card component for displaying data sections with loading and error states.\n */\nexport const DataCard: React.FC<DataCardProps> = ({\n  title,\n  children,\n  isLoading = false,\n  hasError = false,\n  errorMessage = 'An error occurred while loading data',\n  showRetry = true,\n  onRetry,\n  isEmpty = false,\n  emptyMessage = 'No data available',\n  emptyActionText,\n  onEmptyAction,\n  actionButton,\n  className,\n  ...cardProps\n}) => {\n  // Create actions for the card header\n  const headerActions = <HeaderActions>{actionButton}</HeaderActions>;\n\n  // Determine what content to show based on state\n  let content;\n\n  if (isLoading) {\n    content = <LoadingPlaceholder variant=\"card\" text=\"Loading data...\" />;\n  } else if (hasError) {\n    content = (\n      <EmptyState\n        title=\"Error\"\n        description={errorMessage}\n        variant=\"compact\"\n        actionText={showRetry ? 'Retry' : undefined}\n        onAction={showRetry ? onRetry : undefined}\n      />\n    );\n  } else if (isEmpty) {\n    content = (\n      <EmptyState\n        title=\"No Data\"\n        description={emptyMessage}\n        variant=\"compact\"\n        actionText={emptyActionText}\n        onAction={onEmptyAction}\n      />\n    );\n  } else {\n    content = children;\n  }\n\n  return (\n    <Card title={title} actions={headerActions} className={className} {...cardProps}>\n      {content}\n    </Card>\n  );\n};\n", "/**\n * useLocalStorage Hook\n *\n * Custom hook for managing localStorage.\n */\n\nimport { useState, useEffect } from \"react\";\n\n/**\n * Custom hook for managing localStorage\n * @param key - The localStorage key\n * @param initialValue - The initial value\n * @returns [storedValue, setValue] - The stored value and a function to update it\n */\nexport function useLocalStorage<T>(\n  key: string,\n  initialValue: T\n): [T, (value: T | ((val: T) => T)) => void] {\n  // Get from localStorage then\n  // parse stored json or return initialValue\n  const readValue = (): T => {\n    // Prevent build error \"window is undefined\" but keep working\n    if (typeof window === \"undefined\") {\n      return initialValue;\n    }\n\n    try {\n      const item = window.localStorage.getItem(key);\n      return item ? (JSON.parse(item) as T) : initialValue;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \"${key}\":`, error);\n      return initialValue;\n    }\n  };\n\n  // State to store our value\n  // Pass initial state function to useState so logic is only executed once\n  const [storedValue, setStoredValue] = useState<T>(readValue);\n\n  // Return a wrapped version of useState's setter function that ...\n  // ... persists the new value to localStorage.\n  const setValue = (value: T | ((val: T) => T)) => {\n    try {\n      // Allow value to be a function so we have the same API as useState\n      const valueToStore =\n        value instanceof Function ? value(storedValue) : value;\n\n      // Save to state\n      setStoredValue(valueToStore);\n\n      // Save to localStorage\n      if (typeof window !== \"undefined\") {\n        window.localStorage.setItem(key, JSON.stringify(valueToStore));\n      }\n    } catch (error) {\n      console.warn(`Error setting localStorage key \"${key}\":`, error);\n    }\n  };\n\n  // Listen to changes in localStorage\n  useEffect(() => {\n    const handleStorageChange = (event: StorageEvent) => {\n      if (event.key === key && event.newValue) {\n        setStoredValue(JSON.parse(event.newValue) as T);\n      }\n    };\n\n    // Listen for changes to this localStorage key\n    window.addEventListener(\"storage\", handleStorageChange);\n\n    // Remove event listener on cleanup\n    return () => window.removeEventListener(\"storage\", handleStorageChange);\n  }, [key]);\n\n  return [storedValue, setValue];\n}\n", "/**\n * Trade Analysis Calculations Service\n *\n * This service provides real-time calculation of trading metrics from CompleteTradeData.\n * Replaces mock data generation with actual trade analysis calculations.\n */\n\nimport { CompleteTradeData, TradeRecord } from '@adhd-trading-dashboard/shared';\nimport {\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n  EquityPoint,\n  DistributionBar,\n} from '../types';\n\n/**\n * Calculate comprehensive performance metrics from real trade data\n */\nexport const calculateRealPerformanceMetrics = (\n  trades: CompleteTradeData[]\n): PerformanceMetrics => {\n  if (trades.length === 0) {\n    return getEmptyMetrics();\n  }\n\n  // Extract trade records for easier processing\n  const tradeRecords = trades.map(t => t.trade);\n\n  // Basic trade categorization\n  const winningTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) > 0);\n  const losingTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) < 0);\n  const breakEvenTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) === 0);\n\n  // Profit/Loss calculations\n  const totalProfitLoss = tradeRecords.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);\n  const totalWinAmount = winningTrades.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);\n  const totalLossAmount = Math.abs(\n    losingTrades.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0)\n  );\n\n  // Average calculations\n  const averageWin = winningTrades.length > 0 ? totalWinAmount / winningTrades.length : 0;\n  const averageLoss = losingTrades.length > 0 ? totalLossAmount / losingTrades.length : 0;\n\n  // Extreme values\n  const largestWin =\n    winningTrades.length > 0 ? Math.max(...winningTrades.map(trade => trade.achieved_pl || 0)) : 0;\n  const largestLoss =\n    losingTrades.length > 0 ? Math.min(...losingTrades.map(trade => trade.achieved_pl || 0)) : 0;\n\n  // Duration calculations (if timing data available)\n  const durations = calculateTradeDurations(tradeRecords);\n  const averageDuration =\n    durations.length > 0\n      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length\n      : 0;\n\n  // Key performance ratios\n  const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;\n  const profitFactor =\n    totalLossAmount > 0 ? totalWinAmount / totalLossAmount : totalWinAmount > 0 ? Infinity : 0;\n  const expectancy = winRate * averageWin - (1 - winRate) * averageLoss;\n\n  // R-Multiple analysis (commented out as not used in PerformanceMetrics interface)\n  // const averageRMultiple =\n  //   tradeRecords.length > 0\n  //     ? tradeRecords.reduce((sum, trade) => sum + (trade.r_multiple || 0), 0) / tradeRecords.length\n  //     : 0;\n\n  // Advanced metrics (commented out as not used in PerformanceMetrics interface)\n  // const { maxDrawdown, maxDrawdownPercent } = calculateDrawdown(trades);\n  // const sharpeRatio = calculateSharpeRatio(tradeRecords);\n  // const calmarRatio = calculateCalmarRatio(totalProfitLoss, maxDrawdownPercent);\n\n  return {\n    totalTrades: trades.length,\n    winningTrades: winningTrades.length,\n    losingTrades: losingTrades.length,\n    breakeven: breakEvenTrades.length,\n    winRate: Math.round(winRate * 10000) / 100, // Percentage with 2 decimals\n    averageWin: Math.round(averageWin * 100) / 100,\n    averageLoss: Math.round(averageLoss * 100) / 100,\n    profitFactor: Math.round(profitFactor * 100) / 100,\n    totalProfitLoss: Math.round(totalProfitLoss * 100) / 100,\n    largestWin: Math.round(largestWin * 100) / 100,\n    largestLoss: Math.round(largestLoss * 100) / 100,\n    averageDuration: Math.round(averageDuration * 100) / 100,\n    expectancy: Math.round(expectancy * 100) / 100,\n    // averageRMultiple: Math.round(averageRMultiple * 100) / 100, // Property doesn't exist in PerformanceMetrics interface\n    // maxDrawdown: Math.round(maxDrawdown * 100) / 100, // Property doesn't exist in PerformanceMetrics interface\n    // maxDrawdownPercent: Math.round(maxDrawdownPercent * 100) / 100, // Property doesn't exist in PerformanceMetrics interface\n    // sharpeRatio: Math.round(sharpeRatio * 100) / 100, // Property doesn't exist in PerformanceMetrics interface\n    // calmarRatio: Math.round(calmarRatio * 100) / 100, // Property doesn't exist in PerformanceMetrics interface\n  };\n};\n\n/**\n * Calculate performance by category (symbol, strategy, session, etc.)\n */\nexport const calculateCategoryPerformance = (\n  trades: CompleteTradeData[],\n  category: 'market' | 'model_type' | 'session' | 'setup' | 'direction'\n): CategoryPerformance[] => {\n  if (trades.length === 0) return [];\n\n  // Group trades by category\n  const categories = new Map<string, CompleteTradeData[]>();\n\n  trades.forEach(tradeData => {\n    const trade = tradeData.trade;\n    let categoryValue: string;\n\n    switch (category) {\n      case 'market':\n        categoryValue = trade.market || 'Unknown';\n        break;\n      case 'model_type':\n        categoryValue = trade.model_type || 'Unknown';\n        break;\n      case 'session':\n        categoryValue = trade.session || 'Unknown';\n        break;\n      case 'setup':\n        categoryValue = trade.setup || 'Unknown';\n        break;\n      case 'direction':\n        categoryValue = trade.direction || 'Unknown';\n        break;\n      default:\n        categoryValue = 'Unknown';\n    }\n\n    if (!categories.has(categoryValue)) {\n      categories.set(categoryValue, []);\n    }\n    categories.get(categoryValue)!.push(tradeData);\n  });\n\n  // Calculate performance for each category\n  const performance: CategoryPerformance[] = [];\n\n  categories.forEach((categoryTrades, categoryValue) => {\n    const tradeRecords = categoryTrades.map(t => t.trade);\n    const winningTrades = tradeRecords.filter(trade => (trade.achieved_pl || 0) > 0);\n    const totalProfitLoss = tradeRecords.reduce((sum, trade) => sum + (trade.achieved_pl || 0), 0);\n    const winRate = categoryTrades.length > 0 ? winningTrades.length / categoryTrades.length : 0;\n    const averageProfitLoss =\n      categoryTrades.length > 0 ? totalProfitLoss / categoryTrades.length : 0;\n\n    performance.push({\n      category,\n      value: categoryValue,\n      trades: categoryTrades.length,\n      winRate: Math.round(winRate * 10000) / 100,\n      profitLoss: Math.round(totalProfitLoss * 100) / 100,\n      averageProfitLoss: Math.round(averageProfitLoss * 100) / 100,\n    });\n  });\n\n  // Sort by profit/loss (descending)\n  return performance.sort((a, b) => b.profitLoss - a.profitLoss);\n};\n\n/**\n * Calculate performance by time periods\n */\nexport const calculateTimePerformance = (\n  trades: CompleteTradeData[],\n  timeType: 'timeOfDay' | 'dayOfWeek' | 'monthly'\n): TimePerformance[] => {\n  if (trades.length === 0) return [];\n\n  let timeSlots: string[];\n\n  switch (timeType) {\n    case 'timeOfDay':\n      timeSlots = [\n        '9:30-10:30',\n        '10:30-11:30',\n        '11:30-12:30',\n        '12:30-13:30',\n        '13:30-14:30',\n        '14:30-15:30',\n        '15:30-16:00',\n      ];\n      break;\n    case 'dayOfWeek':\n      timeSlots = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n      break;\n    case 'monthly':\n      timeSlots = [\n        'January',\n        'February',\n        'March',\n        'April',\n        'May',\n        'June',\n        'July',\n        'August',\n        'September',\n        'October',\n        'November',\n        'December',\n      ];\n      break;\n    default:\n      return [];\n  }\n\n  // Initialize time performance data\n  const timePerformance: TimePerformance[] = timeSlots.map(timeSlot => ({\n    timeSlot,\n    trades: 0,\n    winRate: 0,\n    profitLoss: 0,\n  }));\n\n  // Process each trade\n  trades.forEach(tradeData => {\n    const trade = tradeData.trade;\n    const tradeDate = new Date(trade.date);\n    let slotIndex: number = -1;\n\n    if (timeType === 'timeOfDay' && trade.entry_time) {\n      slotIndex = getTimeOfDaySlot(trade.entry_time);\n    } else if (timeType === 'dayOfWeek') {\n      const dayOfWeek = tradeDate.getDay();\n      if (dayOfWeek >= 1 && dayOfWeek <= 5) {\n        // Monday to Friday\n        slotIndex = dayOfWeek - 1;\n      }\n    } else if (timeType === 'monthly') {\n      slotIndex = tradeDate.getMonth();\n    }\n\n    if (slotIndex >= 0 && slotIndex < timePerformance.length) {\n      const slot = timePerformance[slotIndex];\n      slot.trades++;\n      slot.profitLoss += trade.achieved_pl || 0;\n    }\n  });\n\n  // Calculate win rates for each slot\n  timePerformance.forEach((slot, index) => {\n    if (slot.trades > 0) {\n      const slotTrades = trades.filter(tradeData => {\n        const trade = tradeData.trade;\n        const tradeDate = new Date(trade.date);\n\n        if (timeType === 'timeOfDay' && trade.entry_time) {\n          return getTimeOfDaySlot(trade.entry_time) === index;\n        } else if (timeType === 'dayOfWeek') {\n          const dayOfWeek = tradeDate.getDay();\n          return dayOfWeek === index + 1;\n        } else if (timeType === 'monthly') {\n          return tradeDate.getMonth() === index;\n        }\n        return false;\n      });\n\n      const winningSlotTrades = slotTrades.filter(\n        tradeData => (tradeData.trade.achieved_pl || 0) > 0\n      );\n      slot.winRate =\n        slotTrades.length > 0 ? (winningSlotTrades.length / slotTrades.length) * 100 : 0;\n    }\n  });\n\n  // Filter out empty slots and round numbers\n  return timePerformance\n    .filter(slot => slot.trades > 0)\n    .map(slot => ({\n      ...slot,\n      winRate: Math.round(slot.winRate * 100) / 100,\n      profitLoss: Math.round(slot.profitLoss * 100) / 100,\n    }));\n};\n\n/**\n * Generate equity curve data from trades\n */\nexport const generateEquityCurve = (trades: CompleteTradeData[]): EquityPoint[] => {\n  if (trades.length === 0) return [];\n\n  // Sort trades by date\n  const sortedTrades = [...trades].sort(\n    (a, b) => new Date(a.trade.date).getTime() - new Date(b.trade.date).getTime()\n  );\n\n  const equityPoints: EquityPoint[] = [];\n  let runningBalance = 0;\n\n  sortedTrades.forEach((tradeData, index) => {\n    const trade = tradeData.trade;\n    runningBalance += trade.achieved_pl || 0;\n\n    equityPoints.push({\n      date: trade.date,\n      equity: Math.round(runningBalance * 100) / 100,\n      baseline: 0,\n      balance: Math.round(runningBalance * 100) / 100,\n      tradeNumber: index + 1,\n      profitLoss: trade.achieved_pl || 0,\n    });\n  });\n\n  return equityPoints;\n};\n\n/**\n * Generate profit/loss distribution data\n */\nexport const generateDistributionData = (trades: CompleteTradeData[]): DistributionBar[] => {\n  if (trades.length === 0) return [];\n\n  // Define P&L ranges\n  const ranges = [\n    { min: -Infinity, max: -1000, label: '< -$1000' },\n    { min: -1000, max: -500, label: '-$1000 to -$500' },\n    { min: -500, max: -100, label: '-$500 to -$100' },\n    { min: -100, max: 0, label: '-$100 to $0' },\n    { min: 0, max: 100, label: '$0 to $100' },\n    { min: 100, max: 500, label: '$100 to $500' },\n    { min: 500, max: 1000, label: '$500 to $1000' },\n    { min: 1000, max: Infinity, label: '> $1000' },\n  ];\n\n  const distribution: DistributionBar[] = ranges.map(range => ({\n    range: range.label,\n    count: 0,\n    percentage: 0,\n    totalPnL: 0,\n    isWin: range.min >= 0, // Ranges with min >= 0 are winning ranges\n  }));\n\n  // Categorize trades into ranges\n  trades.forEach(tradeData => {\n    const pnl = tradeData.trade.achieved_pl || 0;\n    const rangeIndex = ranges.findIndex(range => pnl > range.min && pnl <= range.max);\n\n    if (rangeIndex >= 0) {\n      distribution[rangeIndex].count++;\n      distribution[rangeIndex].totalPnL += pnl;\n    }\n  });\n\n  // Calculate percentages\n  distribution.forEach(bar => {\n    bar.percentage = trades.length > 0 ? (bar.count / trades.length) * 100 : 0;\n    bar.percentage = Math.round(bar.percentage * 100) / 100;\n    bar.totalPnL = Math.round(bar.totalPnL * 100) / 100;\n  });\n\n  return distribution.filter(bar => bar.count > 0);\n};\n\n// Helper functions\nconst getEmptyMetrics = (): PerformanceMetrics => ({\n  totalTrades: 0,\n  winningTrades: 0,\n  losingTrades: 0,\n  breakeven: 0,\n  winRate: 0,\n  averageWin: 0,\n  averageLoss: 0,\n  profitFactor: 0,\n  totalProfitLoss: 0,\n  largestWin: 0,\n  largestLoss: 0,\n  averageDuration: 0,\n  expectancy: 0,\n});\n\nconst calculateTradeDurations = (trades: TradeRecord[]): number[] => {\n  return trades\n    .filter(trade => trade.entry_time && trade.exit_time)\n    .map(trade => {\n      const entryTime = new Date(trade.entry_time!).getTime();\n      const exitTime = new Date(trade.exit_time!).getTime();\n      return (exitTime - entryTime) / (1000 * 60); // Duration in minutes\n    });\n};\n\nconst getTimeOfDaySlot = (timeString: string): number => {\n  const time = new Date(timeString);\n  const hour = time.getHours();\n  const minute = time.getMinutes();\n  const timeValue = hour + minute / 60;\n\n  if (timeValue < 9.5 || timeValue >= 16) return -1; // Outside market hours\n  if (timeValue < 10.5) return 0;\n  if (timeValue < 11.5) return 1;\n  if (timeValue < 12.5) return 2;\n  if (timeValue < 13.5) return 3;\n  if (timeValue < 14.5) return 4;\n  if (timeValue < 15.5) return 5;\n  return 6;\n};\n\n// calculateDrawdown function removed as unused\n\n// calculateSharpeRatio and calculateCalmarRatio functions removed as unused\n", "/**\n * Performance Cache Service\n *\n * This service provides caching and optimization for trade analysis calculations\n * to improve performance when dealing with large datasets.\n */\n\nimport { CompleteTradeData } from '@adhd-trading-dashboard/shared';\nimport {\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n  EquityPoint,\n  DistributionBar,\n} from '../types';\n\ninterface CacheEntry<T> {\n  data: T;\n  timestamp: number;\n  hash: string;\n}\n\ninterface CacheConfig {\n  maxAge: number; // Cache expiry time in milliseconds\n  maxSize: number; // Maximum number of cache entries\n}\n\n/**\n * Performance Cache Manager\n */\nclass PerformanceCacheManager {\n  private cache = new Map<string, CacheEntry<any>>();\n  private config: CacheConfig = {\n    maxAge: 5 * 60 * 1000, // 5 minutes\n    maxSize: 100, // 100 cache entries\n  };\n\n  /**\n   * Generate a hash key for cache lookup\n   */\n  private generateCacheKey(trades: CompleteTradeData[], operation: string, params?: any): string {\n    // Create a simple hash based on trade IDs, operation, and parameters\n    const tradeIds = trades\n      .map(t => t.trade.id)\n      .sort()\n      .join(',');\n    const paramsStr = params ? JSON.stringify(params) : '';\n    return `${operation}:${tradeIds}:${paramsStr}`;\n  }\n\n  /**\n   * Get cached result if available and not expired\n   */\n  private getCached<T>(key: string): T | null {\n    const entry = this.cache.get(key);\n\n    if (!entry) {\n      return null;\n    }\n\n    // Check if cache entry has expired\n    if (Date.now() - entry.timestamp > this.config.maxAge) {\n      this.cache.delete(key);\n      return null;\n    }\n\n    return entry.data as T;\n  }\n\n  /**\n   * Store result in cache\n   */\n  private setCached<T>(key: string, data: T): void {\n    // Clean up old entries if cache is full\n    if (this.cache.size >= this.config.maxSize) {\n      const oldestKey = this.cache.keys().next().value;\n      if (oldestKey) {\n        this.cache.delete(oldestKey);\n      }\n    }\n\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now(),\n      hash: key,\n    });\n  }\n\n  /**\n   * Clear all cache entries\n   */\n  clearCache(): void {\n    this.cache.clear();\n  }\n\n  /**\n   * Clear expired cache entries\n   */\n  clearExpiredCache(): void {\n    const now = Date.now();\n    for (const [key, entry] of this.cache.entries()) {\n      if (now - entry.timestamp > this.config.maxAge) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  /**\n   * Get cache statistics\n   */\n  getCacheStats(): {\n    size: number;\n    maxSize: number;\n    hitRate: number;\n    oldestEntry: number | null;\n  } {\n    let oldestTimestamp: number | null = null;\n\n    for (const entry of this.cache.values()) {\n      if (oldestTimestamp === null || entry.timestamp < oldestTimestamp) {\n        oldestTimestamp = entry.timestamp;\n      }\n    }\n\n    return {\n      size: this.cache.size,\n      maxSize: this.config.maxSize,\n      hitRate: 0, // TODO: Implement hit rate tracking\n      oldestEntry: oldestTimestamp,\n    };\n  }\n\n  /**\n   * Cached performance metrics calculation\n   */\n  async getCachedPerformanceMetrics(\n    trades: CompleteTradeData[],\n    calculator: (trades: CompleteTradeData[]) => PerformanceMetrics\n  ): Promise<PerformanceMetrics> {\n    const cacheKey = this.generateCacheKey(trades, 'performance-metrics');\n\n    // Try to get from cache first\n    const cached = this.getCached<PerformanceMetrics>(cacheKey);\n    if (cached) {\n      console.log('📊 Performance metrics retrieved from cache');\n      return cached;\n    }\n\n    // Calculate and cache the result\n    console.log('🧮 Calculating performance metrics...');\n    const result = calculator(trades);\n    this.setCached(cacheKey, result);\n\n    return result;\n  }\n\n  /**\n   * Cached category performance calculation\n   */\n  async getCachedCategoryPerformance(\n    trades: CompleteTradeData[],\n    category: string,\n    calculator: (trades: CompleteTradeData[], category: any) => CategoryPerformance[]\n  ): Promise<CategoryPerformance[]> {\n    const cacheKey = this.generateCacheKey(trades, 'category-performance', { category });\n\n    // Try to get from cache first\n    const cached = this.getCached<CategoryPerformance[]>(cacheKey);\n    if (cached) {\n      console.log(`📈 Category performance (${category}) retrieved from cache`);\n      return cached;\n    }\n\n    // Calculate and cache the result\n    console.log(`📈 Calculating category performance for ${category}...`);\n    const result = calculator(trades, category as any);\n    this.setCached(cacheKey, result);\n\n    return result;\n  }\n\n  /**\n   * Cached time performance calculation\n   */\n  async getCachedTimePerformance(\n    trades: CompleteTradeData[],\n    timeType: string,\n    calculator: (trades: CompleteTradeData[], timeType: any) => TimePerformance[]\n  ): Promise<TimePerformance[]> {\n    const cacheKey = this.generateCacheKey(trades, 'time-performance', { timeType });\n\n    // Try to get from cache first\n    const cached = this.getCached<TimePerformance[]>(cacheKey);\n    if (cached) {\n      console.log(`⏰ Time performance (${timeType}) retrieved from cache`);\n      return cached;\n    }\n\n    // Calculate and cache the result\n    console.log(`⏰ Calculating time performance for ${timeType}...`);\n    const result = calculator(trades, timeType as any);\n    this.setCached(cacheKey, result);\n\n    return result;\n  }\n\n  /**\n   * Cached equity curve calculation\n   */\n  async getCachedEquityCurve(\n    trades: CompleteTradeData[],\n    calculator: (trades: CompleteTradeData[]) => EquityPoint[]\n  ): Promise<EquityPoint[]> {\n    const cacheKey = this.generateCacheKey(trades, 'equity-curve');\n\n    // Try to get from cache first\n    const cached = this.getCached<EquityPoint[]>(cacheKey);\n    if (cached) {\n      console.log('📊 Equity curve retrieved from cache');\n      return cached;\n    }\n\n    // Calculate and cache the result\n    console.log('📊 Calculating equity curve...');\n    const result = calculator(trades);\n    this.setCached(cacheKey, result);\n\n    return result;\n  }\n\n  /**\n   * Cached distribution data calculation\n   */\n  async getCachedDistributionData(\n    trades: CompleteTradeData[],\n    calculator: (trades: CompleteTradeData[]) => DistributionBar[]\n  ): Promise<DistributionBar[]> {\n    const cacheKey = this.generateCacheKey(trades, 'distribution-data');\n\n    // Try to get from cache first\n    const cached = this.getCached<DistributionBar[]>(cacheKey);\n    if (cached) {\n      console.log('📊 Distribution data retrieved from cache');\n      return cached;\n    }\n\n    // Calculate and cache the result\n    console.log('📊 Calculating distribution data...');\n    const result = calculator(trades);\n    this.setCached(cacheKey, result);\n\n    return result;\n  }\n}\n\n// Export singleton instance\nexport const performanceCache = new PerformanceCacheManager();\n\n/**\n * Batch calculation optimization\n *\n * This function optimizes multiple calculations by batching them together\n * and using cached results where possible.\n */\nexport const batchCalculateAnalytics = async (\n  trades: CompleteTradeData[],\n  calculators: {\n    performanceMetrics: (trades: CompleteTradeData[]) => PerformanceMetrics;\n    categoryPerformance: (trades: CompleteTradeData[], category: any) => CategoryPerformance[];\n    timePerformance: (trades: CompleteTradeData[], timeType: any) => TimePerformance[];\n    equityCurve: (trades: CompleteTradeData[]) => EquityPoint[];\n    distributionData: (trades: CompleteTradeData[]) => DistributionBar[];\n  }\n): Promise<{\n  metrics: PerformanceMetrics;\n  symbolPerformance: CategoryPerformance[];\n  strategyPerformance: CategoryPerformance[];\n  sessionPerformance: CategoryPerformance[];\n  setupPerformance: CategoryPerformance[];\n  directionPerformance: CategoryPerformance[];\n  timeOfDayPerformance: TimePerformance[];\n  dayOfWeekPerformance: TimePerformance[];\n  monthlyPerformance: TimePerformance[];\n  equityCurve: EquityPoint[];\n  distributionData: DistributionBar[];\n}> => {\n  console.log('🚀 Starting batch analytics calculation...');\n\n  // Use Promise.all for parallel execution of cached calculations\n  const [\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    sessionPerformance,\n    setupPerformance,\n    directionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n    monthlyPerformance,\n    equityCurve,\n    distributionData,\n  ] = await Promise.all([\n    performanceCache.getCachedPerformanceMetrics(trades, calculators.performanceMetrics),\n    performanceCache.getCachedCategoryPerformance(\n      trades,\n      'market',\n      calculators.categoryPerformance\n    ),\n    performanceCache.getCachedCategoryPerformance(\n      trades,\n      'model_type',\n      calculators.categoryPerformance\n    ),\n    performanceCache.getCachedCategoryPerformance(\n      trades,\n      'session',\n      calculators.categoryPerformance\n    ),\n    performanceCache.getCachedCategoryPerformance(trades, 'setup', calculators.categoryPerformance),\n    performanceCache.getCachedCategoryPerformance(\n      trades,\n      'direction',\n      calculators.categoryPerformance\n    ),\n    performanceCache.getCachedTimePerformance(trades, 'timeOfDay', calculators.timePerformance),\n    performanceCache.getCachedTimePerformance(trades, 'dayOfWeek', calculators.timePerformance),\n    performanceCache.getCachedTimePerformance(trades, 'monthly', calculators.timePerformance),\n    performanceCache.getCachedEquityCurve(trades, calculators.equityCurve),\n    performanceCache.getCachedDistributionData(trades, calculators.distributionData),\n  ]);\n\n  console.log('✅ Batch analytics calculation completed');\n\n  return {\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    sessionPerformance,\n    setupPerformance,\n    directionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n    monthlyPerformance,\n    equityCurve,\n    distributionData,\n  };\n};\n\n/**\n * Performance monitoring utilities\n */\nexport const performanceMonitor = {\n  /**\n   * Measure execution time of a function\n   */\n  async measureTime<T>(name: string, fn: () => Promise<T> | T): Promise<T> {\n    const start = performance.now();\n    const result = await fn();\n    const end = performance.now();\n    console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);\n    return result;\n  },\n\n  /**\n   * Log memory usage (if available)\n   */\n  logMemoryUsage(context: string): void {\n    if ('memory' in performance) {\n      const memory = (performance as any).memory;\n      console.log(`🧠 Memory usage (${context}):`, {\n        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,\n        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,\n        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,\n      });\n    }\n  },\n\n  /**\n   * Get cache statistics\n   */\n  getCacheStats() {\n    return performanceCache.getCacheStats();\n  },\n};\n", "/**\n * Real Trade Analysis API Service\n *\n * This service replaces the mock data generation with real trade data integration\n * from the trade journal system using the trade storage service.\n */\n\nimport {\n  tradeStorageService,\n  CompleteTradeData,\n  // TradeFilters, // Removed as unused\n} from '@adhd-trading-dashboard/shared';\nimport { TradeAnalysisData, TradeFilters as AnalysisFilters } from '../types';\nimport {\n  calculateRealPerformanceMetrics,\n  calculateCategoryPerformance,\n  calculateTimePerformance,\n  generateEquityCurve,\n  generateDistributionData,\n} from './tradeAnalysisCalculations';\nimport { batchCalculateAnalytics, performanceMonitor } from './performanceCache';\n\n/**\n * Fetch real trade analysis data from the trade storage service\n */\nexport const fetchRealTradeAnalysisData = async (\n  filters: AnalysisFilters\n): Promise<TradeAnalysisData> => {\n  try {\n    console.log('🔄 Fetching real trade analysis data with filters:', filters);\n\n    // Convert analysis filters to storage service filters (for future use)\n    // const storageFilters = convertToStorageFilters(filters);\n\n    // Fetch trades from IndexedDB\n    const trades = await tradeStorageService.getAllTrades();\n\n    console.log(`📊 Retrieved ${trades.length} trades from storage`);\n\n    // Apply additional client-side filtering if needed\n    const filteredTrades = applyClientSideFilters(trades, filters);\n\n    console.log(`🔍 After client-side filtering: ${filteredTrades.length} trades`);\n\n    // Calculate all metrics and performance data\n    const analysisData = await generateAnalysisData(filteredTrades);\n\n    console.log('✅ Trade analysis data generated successfully');\n    return analysisData;\n  } catch (error) {\n    console.error('❌ Error fetching real trade analysis data:', error);\n    throw new Error(\n      `Failed to fetch trade analysis data: ${\n        error instanceof Error ? error.message : 'Unknown error'\n      }`\n    );\n  }\n};\n\n/**\n * Generate comprehensive analysis data from filtered trades\n */\nconst generateAnalysisData = async (trades: CompleteTradeData[]): Promise<TradeAnalysisData> => {\n  console.log('🧮 Starting performance analysis with caching...');\n\n  // Log memory usage before calculation\n  performanceMonitor.logMemoryUsage('before analysis');\n\n  // Use batch calculation with caching for optimal performance\n  const analyticsResults = await performanceMonitor.measureTime('Batch Analytics Calculation', () =>\n    batchCalculateAnalytics(trades, {\n      performanceMetrics: calculateRealPerformanceMetrics,\n      categoryPerformance: calculateCategoryPerformance,\n      timePerformance: calculateTimePerformance,\n      equityCurve: generateEquityCurve,\n      distributionData: generateDistributionData,\n    })\n  );\n\n  // Extract results from batch calculation\n  const {\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    sessionPerformance,\n    setupPerformance,\n    directionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n    monthlyPerformance,\n    equityCurve,\n    distributionData,\n  } = analyticsResults;\n\n  // Log memory usage after calculation\n  performanceMonitor.logMemoryUsage('after analysis');\n\n  // Log cache statistics\n  const cacheStats = performanceMonitor.getCacheStats();\n  console.log('📊 Cache statistics:', cacheStats);\n\n  return {\n    trades: trades.map(convertToAnalysisTradeFormat),\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    timeframePerformance: [], // Not applicable with current schema\n    sessionPerformance,\n    setupPerformance,\n    directionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n    monthlyPerformance,\n    equityCurve,\n    distributionData,\n    // Additional analysis data\n    totalTrades: trades.length,\n    dateRange: {\n      start:\n        trades.length > 0\n          ? Math.min(...trades.map(t => new Date(t.trade.date).getTime()))\n          : Date.now(),\n      end:\n        trades.length > 0\n          ? Math.max(...trades.map(t => new Date(t.trade.date).getTime()))\n          : Date.now(),\n    },\n    lastUpdated: new Date().toISOString(),\n  };\n};\n\n/**\n * Convert analysis filters to trade storage service filters\n */\n// convertToStorageFilters function removed as unused\n/*\nconst convertToStorageFilters = (filters: AnalysisFilters): TradeFilters => {\n  const storageFilters: TradeFilters = {};\n\n  // Date range filters\n  if (filters.dateRange) {\n    storageFilters.dateFrom = filters.dateRange.startDate;\n    storageFilters.dateTo = filters.dateRange.endDate;\n  }\n\n  // Symbol filter (maps to market in our schema)\n  if (filters.symbols && filters.symbols.length > 0) {\n    // Note: Storage service might need enhancement to support multiple symbols\n    storageFilters.market = filters.symbols[0]; // For now, use first symbol\n  }\n\n  // Direction filter\n  if (filters.directions && filters.directions.length > 0) {\n    // Convert 'long'/'short' to 'Long'/'Short'\n    const direction = filters.directions[0];\n    storageFilters.direction = direction === 'long' ? 'Long' : 'Short';\n  }\n\n  // Session filter\n  if (filters.sessions && filters.sessions.length > 0) {\n    storageFilters.session = filters.sessions[0];\n  }\n\n  // Strategy filter (maps to model_type in our schema)\n  if (filters.strategies && filters.strategies.length > 0) {\n    storageFilters.model_type = filters.strategies[0];\n  }\n\n  // Profit/Loss filters - TradeFilters doesn't have these properties\n  // TODO: Add profit/loss filtering support to TradeFilters interface\n  // if (filters.minProfitLoss !== undefined) {\n  //   storageFilters.min_achieved_pl = filters.minProfitLoss;\n  // }\n  // if (filters.maxProfitLoss !== undefined) {\n  //   storageFilters.max_achieved_pl = filters.maxProfitLoss;\n  // }\n\n  return storageFilters;\n};\n*/\n\n/**\n * Apply additional client-side filtering that can't be done at the database level\n */\nconst applyClientSideFilters = (\n  trades: CompleteTradeData[],\n  filters: AnalysisFilters\n): CompleteTradeData[] => {\n  return trades.filter(tradeData => {\n    const trade = tradeData.trade;\n\n    // Multiple symbols filter\n    if (filters.symbols && filters.symbols.length > 1) {\n      const hasMatchingSymbol = filters.symbols.some(symbol =>\n        trade.market?.toLowerCase().includes(symbol.toLowerCase())\n      );\n      if (!hasMatchingSymbol) return false;\n    }\n\n    // Multiple directions filter\n    if (filters.directions && filters.directions.length > 1) {\n      const tradeDirection = trade.direction?.toLowerCase();\n      const hasMatchingDirection = filters.directions.some(\n        direction => direction === tradeDirection\n      );\n      if (!hasMatchingDirection) return false;\n    }\n\n    // Multiple sessions filter\n    if (filters.sessions && filters.sessions.length > 1) {\n      const tradeSession = trade.session || '';\n      if (!filters.sessions.includes(tradeSession as any)) return false;\n    }\n\n    // Multiple strategies filter\n    if (filters.strategies && filters.strategies.length > 1) {\n      if (!filters.strategies.includes(trade.model_type || '')) return false;\n    }\n\n    // Status filter (win/loss/breakeven)\n    if (filters.statuses && filters.statuses.length > 0) {\n      const tradeStatus = getTradeStatus(trade.achieved_pl || 0);\n      if (!filters.statuses.includes(tradeStatus)) return false;\n    }\n\n    // Tags filter (if we add tags support in the future)\n    if (filters.tags && filters.tags.length > 0) {\n      // TODO: Implement tags filtering when tags are added to schema\n    }\n\n    return true;\n  });\n};\n\n/**\n * Convert CompleteTradeData to the format expected by analysis components\n */\nconst convertToAnalysisTradeFormat = (tradeData: CompleteTradeData): any => {\n  const trade = tradeData.trade;\n\n  return {\n    id: trade.id?.toString() || `${trade.date}-${trade.market}`,\n    symbol: trade.market || 'Unknown',\n    direction: trade.direction?.toLowerCase() || 'unknown',\n    entryPrice: trade.entry_price || 0,\n    exitPrice: trade.exit_price || 0,\n    quantity: trade.no_of_contracts || 0,\n    entryTime: trade.entry_time || trade.date,\n    exitTime: trade.exit_time || trade.date,\n    status: getTradeStatus(trade.achieved_pl || 0),\n    profitLoss: trade.achieved_pl || 0,\n    profitLossPercent: calculateProfitLossPercent(trade),\n    timeframe: '15m', // Default since not in current schema\n    session: trade.session || 'regular',\n    strategy: trade.model_type || 'Unknown',\n    setup: trade.setup || 'Unknown',\n    rMultiple: trade.r_multiple || 0,\n    tags: [], // TODO: Add tags support\n    notes: trade.notes || '',\n    // Additional fields from our schema\n    patternQuality: trade.pattern_quality_rating || 0,\n    dolTarget: trade.dol_target || '',\n    rdType: trade.rd_type || '',\n    drawOnLiquidity: trade.draw_on_liquidity || '',\n  };\n};\n\n/**\n * Determine trade status based on P&L\n */\nconst getTradeStatus = (profitLoss: number): 'win' | 'loss' | 'breakeven' => {\n  if (profitLoss > 0) return 'win';\n  if (profitLoss < 0) return 'loss';\n  return 'breakeven';\n};\n\n/**\n * Calculate profit/loss percentage\n */\nconst calculateProfitLossPercent = (trade: any): number => {\n  if (!trade.entry_price || trade.entry_price === 0) return 0;\n\n  const profitLoss = trade.achieved_pl || 0;\n  const entryValue = trade.entry_price * (trade.no_of_contracts || 1);\n\n  return entryValue > 0 ? (profitLoss / entryValue) * 100 : 0;\n};\n\n/**\n * Get available filter options from real trade data\n */\nexport const getFilterOptions = async (): Promise<{\n  symbols: string[];\n  strategies: string[];\n  sessions: string[];\n  setups: string[];\n}> => {\n  try {\n    const trades = await tradeStorageService.getAllTrades();\n\n    const symbols = [\n      ...new Set(trades.map(t => t.trade.market).filter((x): x is string => Boolean(x))),\n    ];\n    const strategies = [\n      ...new Set(trades.map(t => t.trade.model_type).filter((x): x is string => Boolean(x))),\n    ];\n    const sessions = [\n      ...new Set(trades.map(t => t.trade.session).filter((x): x is string => Boolean(x))),\n    ];\n    const setups = [\n      ...new Set(trades.map(t => t.trade.setup).filter((x): x is string => Boolean(x))),\n    ];\n\n    return {\n      symbols: symbols.sort(),\n      strategies: strategies.sort(),\n      sessions: sessions.sort(),\n      setups: setups.sort(),\n    };\n  } catch (error) {\n    console.error('Error getting filter options:', error);\n    return {\n      symbols: [],\n      strategies: [],\n      sessions: [],\n      setups: [],\n    };\n  }\n};\n\n/**\n * Get trade statistics for dashboard summary\n */\nexport const getTradeStatistics = async (): Promise<{\n  totalTrades: number;\n  todayTrades: number;\n  weekTrades: number;\n  monthTrades: number;\n  lastTradeDate: string | null;\n}> => {\n  try {\n    const trades = await tradeStorageService.getAllTrades();\n\n    const now = new Date();\n    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n    const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());\n\n    const todayTrades = trades.filter(t => new Date(t.trade.date) >= today).length;\n    const weekTrades = trades.filter(t => new Date(t.trade.date) >= weekAgo).length;\n    const monthTrades = trades.filter(t => new Date(t.trade.date) >= monthAgo).length;\n\n    const lastTradeDate =\n      trades.length > 0 ? Math.max(...trades.map(t => new Date(t.trade.date).getTime())) : null;\n\n    return {\n      totalTrades: trades.length,\n      todayTrades,\n      weekTrades,\n      monthTrades,\n      lastTradeDate: lastTradeDate ? new Date(lastTradeDate).toISOString() : null,\n    };\n  } catch (error) {\n    console.error('Error getting trade statistics:', error);\n    return {\n      totalTrades: 0,\n      todayTrades: 0,\n      weekTrades: 0,\n      monthTrades: 0,\n      lastTradeDate: null,\n    };\n  }\n};\n", "/**\n * Trade Analysis API\n *\n * API functions for the trade analysis feature\n * Updated to support both real data and mock data\n */\n\nimport { Trade } from '@adhd-trading-dashboard/shared';\nimport {\n  TradeAnalysisData,\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n  PerformanceMetrics,\n  CategoryPerformance,\n  TimePerformance,\n} from '../types';\n\n// Import real data service\nimport {\n  fetchRealTradeAnalysisData,\n  getFilterOptions,\n  getTradeStatistics,\n} from './realTradeAnalysisApi';\n\n/**\n * Feature flag to control whether to use real data or mock data\n * Set to true to use real trade data from IndexedDB\n * Set to false to use mock data for development/testing\n */\nconst USE_REAL_DATA = true;\n\n/**\n * Fetch trade analysis data\n *\n * Uses real trade data from IndexedDB when USE_REAL_DATA is true,\n * otherwise falls back to mock data generation.\n */\nexport const fetchTradeAnalysisData = async (filters: TradeFilters): Promise<TradeAnalysisData> => {\n  console.log(`🔄 Fetching trade analysis data (Real Data: ${USE_REAL_DATA})`);\n\n  if (USE_REAL_DATA) {\n    try {\n      return await fetchRealTradeAnalysisData(filters);\n    } catch (error) {\n      console.error('❌ Error fetching real trade data, falling back to mock data:', error);\n      // Fall back to mock data if real data fails\n      return generateMockData(filters);\n    }\n  } else {\n    // Simulate API call delay for mock data\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    // Randomly decide if we should throw an error (for testing error handling)\n    const shouldError = Math.random() < 0.05; // 5% chance of error\n    if (shouldError) {\n      throw new Error('Failed to fetch trade analysis data');\n    }\n\n    return generateMockData(filters);\n  }\n};\n\n/**\n * Get available filter options\n */\nexport const fetchFilterOptions = async () => {\n  if (USE_REAL_DATA) {\n    return await getFilterOptions();\n  } else {\n    // Return mock filter options\n    return {\n      symbols: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NFLX', 'NVDA'],\n      strategies: [\n        'Breakout',\n        'Reversal',\n        'Trend Following',\n        'Gap and Go',\n        'VWAP Bounce',\n        'Support/Resistance',\n      ],\n      sessions: ['pre-market', 'regular', 'after-hours'],\n      setups: ['FVG', 'OB', 'Liquidity Sweep', 'BOS', 'CHoCH'],\n    };\n  }\n};\n\n/**\n * Get trade statistics for dashboard\n */\nexport const fetchTradeStatistics = async () => {\n  if (USE_REAL_DATA) {\n    return await getTradeStatistics();\n  } else {\n    // Return mock statistics\n    return {\n      totalTrades: 150,\n      todayTrades: 3,\n      weekTrades: 12,\n      monthTrades: 45,\n      lastTradeDate: new Date().toISOString(),\n    };\n  }\n};\n\n/**\n * Generate mock data for development and testing\n */\nconst generateMockData = (filters: TradeFilters): TradeAnalysisData => {\n  // Generate trades\n  const trades = generateMockTrades(filters);\n\n  // Calculate metrics\n  const metrics = calculateMetrics(trades);\n\n  // Generate performance by category\n  const symbolPerformance = calculateCategoryPerformance(trades, 'symbol');\n  const strategyPerformance = calculateCategoryPerformance(trades, 'strategy');\n  const timeframePerformance = calculateCategoryPerformance(trades, 'timeframe');\n  const sessionPerformance = calculateCategoryPerformance(trades, 'session');\n\n  // Generate performance by time\n  const timeOfDayPerformance = calculateTimePerformance(trades, 'timeOfDay');\n  const dayOfWeekPerformance = calculateTimePerformance(trades, 'dayOfWeek');\n\n  return {\n    trades,\n    metrics,\n    symbolPerformance,\n    strategyPerformance,\n    timeframePerformance,\n    sessionPerformance,\n    timeOfDayPerformance,\n    dayOfWeekPerformance,\n  };\n};\n\n/**\n * Generate mock trades\n */\nconst generateMockTrades = (filters: TradeFilters): Trade[] => {\n  const { dateRange } = filters;\n  const startDate = new Date(dateRange.startDate);\n  const endDate = new Date(dateRange.endDate);\n\n  // Calculate number of days in the date range\n  const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));\n\n  // Generate between 1-5 trades per day\n  const numTrades = Math.max(1, daysDiff) * (1 + Math.floor(Math.random() * 5));\n\n  const trades: Trade[] = [];\n  const symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NFLX', 'NVDA'];\n  const strategies = [\n    'Breakout',\n    'Reversal',\n    'Trend Following',\n    'Gap and Go',\n    'VWAP Bounce',\n    'Support/Resistance',\n  ];\n  const timeframes: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];\n  const sessions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];\n  const tags = [\n    'High Volume',\n    'Low Float',\n    'Earnings',\n    'News',\n    'Technical',\n    'Momentum',\n    'Oversold',\n    'Overbought',\n  ];\n\n  for (let i = 0; i < numTrades; i++) {\n    // Generate random trade date within the range\n    const tradeDate = new Date(\n      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())\n    );\n\n    // Generate entry time (market hours)\n    const entryHour = 9 + Math.floor(Math.random() * 7); // 9 AM to 4 PM\n    const entryMinute = Math.floor(Math.random() * 60);\n    const entryTime = new Date(tradeDate);\n    entryTime.setHours(entryHour, entryMinute, 0, 0);\n\n    // Generate exit time (after entry time)\n    const durationMinutes = 5 + Math.floor(Math.random() * 120); // 5 to 120 minutes\n    const exitTime = new Date(entryTime.getTime() + durationMinutes * 60 * 1000);\n\n    // Generate random trade details\n    const symbol = symbols[Math.floor(Math.random() * symbols.length)];\n    const direction: TradeDirection = Math.random() > 0.5 ? 'long' : 'short';\n    const entryPrice = 100 + Math.random() * 900; // $100 to $1000\n\n    // Determine if win or loss (60% win rate)\n    const isWin = Math.random() < 0.6;\n    const isBreakeven = !isWin && Math.random() < 0.1; // 10% of losses are breakeven\n\n    // Calculate exit price based on win/loss\n    let exitPrice, profitLoss, profitLossPercent, status: TradeStatus;\n\n    if (isBreakeven) {\n      exitPrice = entryPrice + (Math.random() * 0.2 - 0.1); // Small fluctuation around entry\n      status = 'breakeven';\n    } else if (isWin) {\n      const winPercent = 0.5 + Math.random() * 4.5; // 0.5% to 5% win\n      exitPrice =\n        direction === 'long'\n          ? entryPrice * (1 + winPercent / 100)\n          : entryPrice * (1 - winPercent / 100);\n      status = 'win';\n    } else {\n      const lossPercent = 0.5 + Math.random() * 2.5; // 0.5% to 3% loss\n      exitPrice =\n        direction === 'long'\n          ? entryPrice * (1 - lossPercent / 100)\n          : entryPrice * (1 + lossPercent / 100);\n      status = 'loss';\n    }\n\n    // Calculate P&L\n    const quantity = 10 + Math.floor(Math.random() * 90); // 10 to 100 shares\n\n    if (direction === 'long') {\n      profitLoss = (exitPrice - entryPrice) * quantity;\n      profitLossPercent = (exitPrice / entryPrice - 1) * 100;\n    } else {\n      profitLoss = (entryPrice - exitPrice) * quantity;\n      profitLossPercent = (entryPrice / exitPrice - 1) * 100;\n    }\n\n    // Round to 2 decimal places\n    profitLoss = Math.round(profitLoss * 100) / 100;\n    profitLossPercent = Math.round(profitLossPercent * 100) / 100;\n\n    // Generate random trade metadata\n    const timeframe = timeframes[Math.floor(Math.random() * timeframes.length)];\n    const session = sessions[Math.floor(Math.random() * sessions.length)];\n    const strategy = strategies[Math.floor(Math.random() * strategies.length)];\n\n    // Generate random tags (0-3 tags)\n    const numTags = Math.floor(Math.random() * 4);\n    const tradeTags: string[] = [];\n    for (let j = 0; j < numTags; j++) {\n      const tag = tags[Math.floor(Math.random() * tags.length)];\n      if (!tradeTags.includes(tag)) {\n        tradeTags.push(tag);\n      }\n    }\n\n    // Create trade object - using any to bypass interface mismatch in deprecated mock API\n    const trade: any = {\n      id: `trade-${i}`,\n      symbol,\n      direction,\n      entryPrice,\n      exitPrice,\n      quantity,\n      entryTime: entryTime.toISOString(),\n      exitTime: exitTime.toISOString(),\n      status,\n      profitLoss,\n      profitLossPercent,\n      timeframe,\n      session,\n      strategy,\n      tags: tradeTags,\n      notes: Math.random() > 0.7 ? `Sample note for ${symbol} ${direction} trade` : undefined,\n    };\n\n    trades.push(trade);\n  }\n\n  // Sort trades by date (newest first) - using any to bypass interface mismatch\n  return trades.sort(\n    (a: any, b: any) => new Date(b.entryTime).getTime() - new Date(a.entryTime).getTime()\n  );\n};\n\n/**\n * Calculate performance metrics from trades\n */\nconst calculateMetrics = (trades: any[]): PerformanceMetrics => {\n  const winningTrades = trades.filter(trade => trade.status === 'win');\n  const losingTrades = trades.filter(trade => trade.status === 'loss');\n  const breakeven = trades.filter(trade => trade.status === 'breakeven');\n\n  const totalProfitLoss = trades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n  const totalWinAmount = winningTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n  const totalLossAmount = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.profitLoss, 0));\n\n  const averageWin = winningTrades.length > 0 ? totalWinAmount / winningTrades.length : 0;\n\n  const averageLoss = losingTrades.length > 0 ? totalLossAmount / losingTrades.length : 0;\n\n  const largestWin =\n    winningTrades.length > 0 ? Math.max(...winningTrades.map(trade => trade.profitLoss)) : 0;\n\n  const largestLoss =\n    losingTrades.length > 0 ? Math.min(...losingTrades.map(trade => trade.profitLoss)) : 0;\n\n  // Calculate average duration in minutes\n  const durations = trades.map(trade => {\n    const entryTime = new Date(trade.entryTime).getTime();\n    const exitTime = new Date(trade.exitTime).getTime();\n    return (exitTime - entryTime) / (1000 * 60); // Convert to minutes\n  });\n\n  const averageDuration =\n    durations.length > 0\n      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length\n      : 0;\n\n  // Calculate profit factor and expectancy\n  const profitFactor =\n    totalLossAmount > 0 ? totalWinAmount / totalLossAmount : totalWinAmount > 0 ? Infinity : 0;\n\n  const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;\n\n  const expectancy = winRate * averageWin - (1 - winRate) * averageLoss;\n\n  return {\n    totalTrades: trades.length,\n    winningTrades: winningTrades.length,\n    losingTrades: losingTrades.length,\n    breakeven: breakeven.length,\n    winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places\n    averageWin: Math.round(averageWin * 100) / 100,\n    averageLoss: Math.round(averageLoss * 100) / 100,\n    profitFactor: Math.round(profitFactor * 100) / 100,\n    totalProfitLoss: Math.round(totalProfitLoss * 100) / 100,\n    largestWin: Math.round(largestWin * 100) / 100,\n    largestLoss: Math.round(largestLoss * 100) / 100,\n    averageDuration: Math.round(averageDuration * 100) / 100,\n    expectancy: Math.round(expectancy * 100) / 100,\n  };\n};\n\n/**\n * Calculate performance by category\n */\nconst calculateCategoryPerformance = (\n  trades: any[],\n  category: 'symbol' | 'strategy' | 'timeframe' | 'session'\n): CategoryPerformance[] => {\n  // Group trades by category\n  const categories = new Map<string, any[]>();\n\n  trades.forEach(trade => {\n    const categoryValue = trade[category] as string;\n    if (!categories.has(categoryValue)) {\n      categories.set(categoryValue, []);\n    }\n    categories.get(categoryValue)!.push(trade);\n  });\n\n  // Calculate performance for each category\n  const performance: CategoryPerformance[] = [];\n\n  categories.forEach((categoryTrades, categoryValue) => {\n    const winningTrades = categoryTrades.filter(trade => trade.status === 'win');\n    const totalProfitLoss = categoryTrades.reduce((sum, trade) => sum + trade.profitLoss, 0);\n    const winRate = categoryTrades.length > 0 ? winningTrades.length / categoryTrades.length : 0;\n    const averageProfitLoss =\n      categoryTrades.length > 0 ? totalProfitLoss / categoryTrades.length : 0;\n\n    performance.push({\n      category,\n      value: categoryValue,\n      trades: categoryTrades.length,\n      winRate: Math.round(winRate * 10000) / 100, // Convert to percentage with 2 decimal places\n      profitLoss: Math.round(totalProfitLoss * 100) / 100,\n      averageProfitLoss: Math.round(averageProfitLoss * 100) / 100,\n    });\n  });\n\n  // Sort by profit/loss (descending)\n  return performance.sort((a, b) => b.profitLoss - a.profitLoss);\n};\n\n/**\n * Calculate performance by time\n */\nconst calculateTimePerformance = (\n  trades: any[],\n  timeType: 'timeOfDay' | 'dayOfWeek'\n): TimePerformance[] => {\n  // Define time slots\n  let timeSlots: string[];\n\n  if (timeType === 'timeOfDay') {\n    timeSlots = [\n      '9:30-10:30',\n      '10:30-11:30',\n      '11:30-12:30',\n      '12:30-13:30',\n      '13:30-14:30',\n      '14:30-15:30',\n      '15:30-16:00',\n    ];\n  } else {\n    timeSlots = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];\n  }\n\n  // Group trades by time slot\n  const timePerformance: TimePerformance[] = timeSlots.map(timeSlot => ({\n    timeSlot,\n    trades: 0,\n    winRate: 0,\n    profitLoss: 0,\n  }));\n\n  trades.forEach(trade => {\n    const entryTime = new Date(trade.entryTime);\n    let slotIndex: number;\n\n    if (timeType === 'timeOfDay') {\n      // Get hour and determine slot\n      const hour = entryTime.getHours();\n      const minute = entryTime.getMinutes();\n      const timeValue = hour + minute / 60;\n\n      if (timeValue < 9.5 || timeValue >= 16) {\n        return; // Outside regular market hours\n      }\n\n      if (timeValue < 10.5) slotIndex = 0;\n      else if (timeValue < 11.5) slotIndex = 1;\n      else if (timeValue < 12.5) slotIndex = 2;\n      else if (timeValue < 13.5) slotIndex = 3;\n      else if (timeValue < 14.5) slotIndex = 4;\n      else if (timeValue < 15.5) slotIndex = 5;\n      else slotIndex = 6;\n    } else {\n      // Get day of week (0 = Sunday, 1 = Monday, etc.)\n      const dayOfWeek = entryTime.getDay();\n      if (dayOfWeek === 0 || dayOfWeek === 6) {\n        return; // Weekend\n      }\n      slotIndex = dayOfWeek - 1;\n    }\n\n    // Update time slot data\n    const slot = timePerformance[slotIndex];\n    slot.trades++;\n    slot.profitLoss += trade.profitLoss;\n\n    // Recalculate win rate\n    const slotTrades = trades.filter(t => {\n      const tEntryTime = new Date(t.entryTime);\n      if (timeType === 'timeOfDay') {\n        const tHour = tEntryTime.getHours();\n        const tMinute = tEntryTime.getMinutes();\n        const tTimeValue = tHour + tMinute / 60;\n\n        if (slotIndex === 0) return tTimeValue >= 9.5 && tTimeValue < 10.5;\n        if (slotIndex === 1) return tTimeValue >= 10.5 && tTimeValue < 11.5;\n        if (slotIndex === 2) return tTimeValue >= 11.5 && tTimeValue < 12.5;\n        if (slotIndex === 3) return tTimeValue >= 12.5 && tTimeValue < 13.5;\n        if (slotIndex === 4) return tTimeValue >= 13.5 && tTimeValue < 14.5;\n        if (slotIndex === 5) return tTimeValue >= 14.5 && tTimeValue < 15.5;\n        if (slotIndex === 6) return tTimeValue >= 15.5 && tTimeValue < 16;\n        return false;\n      } else {\n        return tEntryTime.getDay() === slotIndex + 1;\n      }\n    });\n\n    const winningSlotTrades = slotTrades.filter(t => t.status === 'win');\n    slot.winRate = slotTrades.length > 0 ? (winningSlotTrades.length / slotTrades.length) * 100 : 0;\n  });\n\n  // Round numbers and filter out empty slots\n  return timePerformance\n    .filter(slot => slot.trades > 0)\n    .map(slot => ({\n      ...slot,\n      winRate: Math.round(slot.winRate * 100) / 100,\n      profitLoss: Math.round(slot.profitLoss * 100) / 100,\n    }));\n};\n", "/**\n * Trade Analysis Context\n *\n * Context for managing trade analysis state\n */\n\nimport React, {\n  createContext,\n  useContext,\n  useReducer,\n  ReactNode,\n  useCallback,\n  useEffect,\n} from 'react';\nimport { useLocalStorage } from '@adhd-trading-dashboard/shared';\nimport { TradeAnalysisState, TradeAnalysisAction, TradeFilters, UserPreferences } from '../types';\nimport { fetchTradeAnalysisData } from '../services/tradeAnalysisApi';\n\n// Get default date range\nconst getDefaultDateRange = () => {\n  const today = new Date();\n  const startDate = new Date();\n  startDate.setMonth(today.getMonth() - 1); // Default to last month\n\n  return {\n    startDate: startDate.toISOString().split('T')[0],\n    endDate: today.toISOString().split('T')[0],\n  };\n};\n\n// Initial state\nconst initialState: TradeAnalysisState = {\n  data: null,\n  filters: {\n    dateRange: getDefaultDateRange(),\n  },\n  preferences: {\n    defaultDateRange: 'month',\n    defaultView: 'summary',\n    chartTypes: {\n      performance: 'bar',\n      distribution: 'pie',\n      timeAnalysis: 'bar',\n    },\n    tableColumns: [\n      'symbol',\n      'direction',\n      'entryTime',\n      'exitTime',\n      'profitLoss',\n      'status',\n      'strategy',\n    ],\n    favoriteStrategies: [],\n    favoriteTags: [],\n  },\n  isLoading: false,\n  error: null,\n  selectedTradeId: null,\n};\n\n// Reducer function\nconst tradeAnalysisReducer = (\n  state: TradeAnalysisState,\n  action: TradeAnalysisAction\n): TradeAnalysisState => {\n  switch (action.type) {\n    case 'FETCH_DATA_START':\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case 'FETCH_DATA_SUCCESS':\n      return {\n        ...state,\n        data: action.payload,\n        isLoading: false,\n        error: null,\n      };\n    case 'FETCH_DATA_ERROR':\n      return {\n        ...state,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'UPDATE_FILTERS':\n      return {\n        ...state,\n        filters: {\n          ...state.filters,\n          ...action.payload,\n        },\n      };\n    case 'UPDATE_PREFERENCES':\n      return {\n        ...state,\n        preferences: {\n          ...state.preferences,\n          ...action.payload,\n        },\n      };\n    case 'SELECT_TRADE':\n      return {\n        ...state,\n        selectedTradeId: action.payload,\n      };\n    case 'RESET_FILTERS':\n      return {\n        ...state,\n        filters: {\n          dateRange: getDefaultDateRange(),\n        },\n      };\n    default:\n      return state;\n  }\n};\n\n// Context\ninterface TradeAnalysisContextType extends TradeAnalysisState {\n  fetchData: () => Promise<void>;\n  updateFilters: (filters: Partial<TradeFilters>) => void;\n  updatePreferences: (preferences: Partial<UserPreferences>) => void;\n  selectTrade: (tradeId: string | null) => void;\n  resetFilters: () => void;\n}\n\nconst TradeAnalysisContext = createContext<TradeAnalysisContextType | undefined>(undefined);\n\n// Provider component\ninterface TradeAnalysisProviderProps {\n  children: ReactNode;\n}\n\nexport const TradeAnalysisProvider: React.FC<TradeAnalysisProviderProps> = ({ children }) => {\n  // Load saved preferences from localStorage\n  const [savedPreferences] = useLocalStorage<Partial<UserPreferences>>(\n    'trade-analysis-preferences',\n    {}\n  );\n\n  // Merge saved preferences with initial state\n  const mergedInitialState = {\n    ...initialState,\n    preferences: {\n      ...initialState.preferences,\n      ...savedPreferences,\n    },\n  };\n\n  const [state, dispatch] = useReducer(tradeAnalysisReducer, mergedInitialState);\n\n  // Save preferences to localStorage when they change\n  useEffect(() => {\n    localStorage.setItem('trade-analysis-preferences', JSON.stringify(state.preferences));\n  }, [state.preferences]);\n\n  const fetchData = useCallback(async () => {\n    dispatch({ type: 'FETCH_DATA_START' });\n    try {\n      const data = await fetchTradeAnalysisData(state.filters);\n      dispatch({ type: 'FETCH_DATA_SUCCESS', payload: data });\n    } catch (error) {\n      dispatch({\n        type: 'FETCH_DATA_ERROR',\n        payload: error instanceof Error ? error.message : 'An unknown error occurred',\n      });\n    }\n  }, [state.filters]);\n\n  const updateFilters = useCallback((filters: Partial<TradeFilters>) => {\n    dispatch({ type: 'UPDATE_FILTERS', payload: filters });\n  }, []);\n\n  const updatePreferences = useCallback((preferences: Partial<UserPreferences>) => {\n    dispatch({ type: 'UPDATE_PREFERENCES', payload: preferences });\n  }, []);\n\n  const selectTrade = useCallback((tradeId: string | null) => {\n    dispatch({ type: 'SELECT_TRADE', payload: tradeId });\n  }, []);\n\n  const resetFilters = useCallback(() => {\n    dispatch({ type: 'RESET_FILTERS' });\n  }, []);\n\n  // Fetch data when filters change\n  useEffect(() => {\n    fetchData();\n  }, [fetchData, state.filters]);\n\n  const value = {\n    ...state,\n    fetchData,\n    updateFilters,\n    updatePreferences,\n    selectTrade,\n    resetFilters,\n  };\n\n  return <TradeAnalysisContext.Provider value={value}>{children}</TradeAnalysisContext.Provider>;\n};\n\n// Custom hook for using the context\nexport const useTradeAnalysis = (): TradeAnalysisContextType => {\n  const context = useContext(TradeAnalysisContext);\n  if (context === undefined) {\n    throw new Error('useTradeAnalysis must be used within a TradeAnalysisProvider');\n  }\n  return context;\n};\n", "/**\n * AnalysisHeader Component\n *\n * F1-themed header for Trade Analysis following the proven F1Header pattern.\n * Provides consistent branding and navigation across the application.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Button } from '@adhd-trading-dashboard/shared';\n\nexport interface AnalysisHeaderProps {\n  /** Function called when refresh is clicked */\n  onRefresh?: () => void;\n  /** Whether refresh is in progress */\n  isRefreshing?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst HeaderContainer = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing?.lg || '16px'} 0;\n  border-bottom: 2px solid var(--border-primary);\n  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  letter-spacing: -0.025em;\n\n  /* F1 Racing aesthetic */\n  text-transform: uppercase;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n`;\n\nconst Subtitle = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: var(--text-secondary);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n`;\n\nconst ActionsSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst RefreshButton = styled(Button)<{ isRefreshing?: boolean }>`\n  min-width: 100px;\n  position: relative;\n\n  ${({ isRefreshing }) =>\n    isRefreshing &&\n    `\n    &::after {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      width: 16px;\n      height: 16px;\n      margin: -8px 0 0 -8px;\n      border: 2px solid transparent;\n      border-top: 2px solid currentColor;\n      border-radius: 50%;\n      animation: spin 1s linear infinite;\n    }\n\n    @keyframes spin {\n      0% { transform: rotate(0deg); }\n      100% { transform: rotate(360deg); }\n    }\n  `}\n`;\n\nconst StatusIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  background: var(--model-card-bg);\n  border: 1px solid var(--model-card-border);\n  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  color: var(--model-name-color);\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n`;\n\nconst StatusDot = styled.div`\n  width: 6px;\n  height: 6px;\n  background: var(--primary-color);\n  border-radius: 50%;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 1;\n    }\n    50% {\n      opacity: 0.5;\n    }\n  }\n`;\n\n/**\n * AnalysisHeader Component\n *\n * F1-themed header that provides consistent branding and actions\n * for the Trade Analysis feature.\n */\nexport const AnalysisHeader: React.FC<AnalysisHeaderProps> = ({\n  onRefresh,\n  isRefreshing = false,\n  className,\n}) => {\n  return (\n    <HeaderContainer className={className}>\n      <TitleSection>\n        <Title>Trade Analysis</Title>\n        <Subtitle>Performance Metrics & Insights</Subtitle>\n      </TitleSection>\n\n      <ActionsSection>\n        <StatusIndicator>\n          <StatusDot />\n          LIVE DATA\n        </StatusIndicator>\n\n        {onRefresh && (\n          <RefreshButton\n            variant=\"outline\"\n            size=\"small\"\n            onClick={onRefresh}\n            disabled={isRefreshing}\n            isRefreshing={isRefreshing}\n          >\n            {isRefreshing ? 'Refreshing...' : 'Refresh'}\n          </RefreshButton>\n        )}\n      </ActionsSection>\n    </HeaderContainer>\n  );\n};\n\nexport default AnalysisHeader;\n", "/**\n * AnalysisTabs Component\n * \n * F1-themed tab navigation for Trade Analysis following the proven DashboardTabs pattern.\n * Provides consistent navigation experience across the application.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type AnalysisTabType = 'summary' | 'trades' | 'symbols' | 'strategies' | 'timeframes' | 'time';\n\nexport interface AnalysisTabsProps {\n  /** Currently active tab */\n  activeTab: string;\n  /** Function called when tab changes */\n  onTabChange: (tab: AnalysisTabType) => void;\n  /** Custom className */\n  className?: string;\n}\n\nconst TabsContainer = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};\n  border-bottom: 1px solid var(--border-primary);\n  padding-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};\n  overflow-x: auto;\n  \n  /* Hide scrollbar but keep functionality */\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  &::-webkit-scrollbar {\n    display: none;\n  }\n`;\n\nconst Tab = styled.button<{ active: boolean }>`\n  background: none;\n  border: none;\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: ${({ theme, active }) =>\n    active ? theme.fontWeights?.semibold || '600' : theme.fontWeights?.medium || '500'};\n  color: ${({ theme, active }) => (active ? theme.colors?.primary || 'var(--primary-color)' : 'var(--text-secondary)')};\n  cursor: pointer;\n  border-bottom: 2px solid ${({ theme, active }) => \n    (active ? theme.colors?.primary || 'var(--primary-color)' : 'transparent')};\n  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};\n  white-space: nowrap;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  position: relative;\n  \n  /* F1 Racing aesthetic */\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  &:hover {\n    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    background: rgba(220, 38, 38, 0.05);\n  }\n\n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);\n    border-radius: ${({ theme }) => theme.borderRadius?.xs || '2px'};\n  }\n\n  /* Active tab indicator */\n  ${({ active, theme }) => active && `\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: -2px;\n      left: 0;\n      right: 0;\n      height: 2px;\n      background: ${theme.colors?.primary || 'var(--primary-color)'};\n      animation: slideIn 0.3s ease-out;\n    }\n    \n    @keyframes slideIn {\n      from {\n        transform: scaleX(0);\n      }\n      to {\n        transform: scaleX(1);\n      }\n    }\n  `}\n`;\n\nconst TabBadge = styled.span`\n  display: inline-block;\n  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};\n  padding: 2px 6px;\n  background: rgba(220, 38, 38, 0.1);\n  color: var(--primary-color);\n  border-radius: ${({ theme }) => theme.borderRadius?.xs || '2px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  line-height: 1;\n`;\n\n/**\n * Tab configuration with labels and optional badges\n */\nconst TAB_CONFIG: Record<AnalysisTabType, { label: string; badge?: string }> = {\n  summary: { label: 'Summary' },\n  trades: { label: 'Trades' },\n  symbols: { label: 'Symbols' },\n  strategies: { label: 'Strategies' },\n  timeframes: { label: 'Timeframes' },\n  time: { label: 'Time Analysis' },\n};\n\n/**\n * AnalysisTabs Component\n * \n * F1-themed tab navigation that provides consistent navigation\n * experience across the Trade Analysis feature.\n */\nexport const AnalysisTabs: React.FC<AnalysisTabsProps> = ({\n  activeTab,\n  onTabChange,\n  className,\n}) => {\n  const handleTabClick = (tab: AnalysisTabType) => {\n    onTabChange(tab);\n  };\n\n  return (\n    <TabsContainer className={className}>\n      {(Object.entries(TAB_CONFIG) as [AnalysisTabType, typeof TAB_CONFIG[AnalysisTabType]][]).map(\n        ([tabKey, config]) => (\n          <Tab\n            key={tabKey}\n            active={activeTab === tabKey}\n            onClick={() => handleTabClick(tabKey)}\n            aria-selected={activeTab === tabKey}\n            role=\"tab\"\n          >\n            {config.label}\n            {config.badge && <TabBadge>{config.badge}</TabBadge>}\n          </Tab>\n        )\n      )}\n    </TabsContainer>\n  );\n};\n\nexport default AnalysisTabs;\n", "/**\n * Enhanced Filter Panel Component\n *\n * Modern, structured filtering interface for trade analysis\n * with improved visual hierarchy and responsive design.\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport {\n  TradeFilters,\n  TradeDirection,\n  TradeStatus,\n  TradeTimeframe,\n  TradingSession,\n} from '../types';\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\nimport { Tag } from '@adhd-trading-dashboard/shared';\n\ninterface FilterPanelProps {\n  className?: string;\n}\n\n// Main container with F1-inspired design\nconst Container = styled.div`\n  background: var(--bg-primary);\n  border: 1px solid var(--border-primary);\n  border-radius: 8px;\n  margin-bottom: 24px;\n  overflow: hidden;\n`;\n\n// Header section with title and actions\nconst FilterHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: linear-gradient(135deg, var(--bg-primary) 0%, #2a2a2a 100%);\n  border-bottom: 1px solid var(--border-primary);\n`;\n\nconst HeaderTitle = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n`;\n\nconst Title = styled.h3`\n  color: #ffffff;\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0;\n  letter-spacing: 0.5px;\n`;\n\nconst LiveIndicator = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  color: var(--error-color);\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n\n  &::before {\n    content: '';\n    width: 8px;\n    height: 8px;\n    background: var(--error-color);\n    border-radius: 50%;\n    animation: pulse 2s infinite;\n  }\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 1;\n    }\n    50% {\n      opacity: 0.5;\n    }\n  }\n`;\n\nconst HeaderActions = styled.div`\n  display: flex;\n  gap: 12px;\n`;\n\n// Filter content area\nconst FilterContent = styled.div`\n  padding: 24px;\n`;\n\nconst FilterGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: 24px;\n  margin-bottom: 24px;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: 20px;\n  }\n`;\n\nconst FilterGroup = styled.div`\n  background: var(--bg-primary);\n  border: 1px solid var(--border-primary);\n  border-radius: 6px;\n  padding: 16px;\n  transition: border-color 0.2s ease;\n\n  &:hover {\n    border-color: var(--text-secondary);\n  }\n`;\n\nconst FilterLabel = styled.div`\n  color: var(--text-secondary);\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  margin-bottom: 12px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  &::before {\n    content: '';\n    width: 3px;\n    height: 12px;\n    background: var(--error-color);\n    border-radius: 2px;\n  }\n`;\n\nconst DateRangeContainer = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 12px;\n`;\n\nconst DateInput = styled.input`\n  background: var(--bg-primary);\n  border: 1px solid var(--border-primary);\n  border-radius: 4px;\n  padding: 10px 12px;\n  color: #ffffff;\n  font-size: 14px;\n  font-family: 'Inter', sans-serif;\n  transition: all 0.2s ease;\n\n  &:focus {\n    outline: none;\n    border-color: var(--error-color);\n    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.1);\n  }\n\n  &::-webkit-calendar-picker-indicator {\n    filter: invert(1);\n    cursor: pointer;\n  }\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n`;\n\nconst FilterTag = styled(Tag)<{ selected: boolean }>`\n  cursor: pointer;\n  opacity: ${({ selected }) => (selected ? 1 : 0.6)};\n  background: ${({ selected, variant }) => {\n    if (!selected) return 'var(--border-primary)';\n    switch (variant) {\n      case 'success':\n        return 'var(--success-color)';\n      case 'error':\n        return 'var(--error-color)';\n      case 'info':\n        return 'var(--info-color)';\n      case 'primary':\n        return '#8b5cf6';\n      case 'secondary':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--text-secondary)';\n    }\n  }};\n  color: ${({ selected }) => (selected ? '#ffffff' : 'var(--text-secondary)')};\n  border: 1px solid\n    ${({ selected, variant }) => {\n      if (!selected) return 'var(--border-primary)';\n      switch (variant) {\n        case 'success':\n          return 'var(--success-color)';\n        case 'error':\n          return 'var(--error-color)';\n        case 'info':\n          return 'var(--info-color)';\n        case 'primary':\n          return '#8b5cf6';\n        case 'secondary':\n          return 'var(--warning-color)';\n        default:\n          return 'var(--text-secondary)';\n      }\n    }};\n  transition: all 0.2s ease;\n\n  &:hover {\n    opacity: 0.8;\n    transform: translateY(-1px);\n  }\n`;\n\nconst ActionBar = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px 24px;\n  background: var(--bg-primary);\n  border-top: 1px solid var(--border-primary);\n`;\n\nconst FilterStats = styled.div`\n  color: var(--text-secondary);\n  font-size: 12px;\n  font-weight: 500;\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: 12px;\n`;\n\nconst ActionButton = styled.button<{ variant: 'primary' | 'secondary' }>`\n  background: ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'transparent')};\n  color: ${({ variant }) => (variant === 'primary' ? '#ffffff' : 'var(--text-secondary)')};\n  border: 1px solid\n    ${({ variant }) => (variant === 'primary' ? 'var(--error-color)' : 'var(--border-primary)')};\n  border-radius: 4px;\n  padding: 8px 16px;\n  font-size: 12px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  outline: none;\n\n  &:hover {\n    background: ${({ variant }) =>\n      variant === 'primary' ? 'var(--primary-color)' : 'var(--border-primary)'};\n    transform: translateY(-1px);\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nexport const FilterPanel: React.FC<FilterPanelProps> = ({ className }) => {\n  const { filters, updateFilters, resetFilters, data } = useTradeAnalysis();\n\n  // Local state for filter values\n  const [localFilters, setLocalFilters] = useState<TradeFilters>(filters);\n\n  // Available options from data\n  const availableSymbols = data?.trades ? [...new Set(data.trades.map(trade => trade.symbol))] : [];\n\n  const availableStrategies = data?.trades\n    ? [...new Set(data.trades.map(trade => trade.strategy))]\n    : [];\n\n  const availableTags = data?.trades\n    ? [...new Set(data.trades.flatMap(trade => trade.tags || []))]\n    : [];\n\n  // Direction options\n  const directionOptions: TradeDirection[] = ['long', 'short'];\n\n  // Status options\n  const statusOptions: TradeStatus[] = ['win', 'loss', 'breakeven'];\n\n  // Timeframe options\n  const timeframeOptions: TradeTimeframe[] = ['1m', '5m', '15m', '30m', '1h', '4h', 'daily'];\n\n  // Session options\n  const sessionOptions: TradingSession[] = ['pre-market', 'regular', 'after-hours'];\n\n  // Calculate filter stats\n  const activeFilterCount = Object.values(localFilters).filter(value => {\n    if (Array.isArray(value)) return value.length > 0;\n    if (typeof value === 'object' && value !== null) return Object.values(value).some(v => v);\n    return value !== undefined && value !== null && value !== '';\n  }).length;\n\n  const totalTrades = data?.trades?.length || 0;\n\n  // Handle date range change\n  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {\n    setLocalFilters(prev => ({\n      ...prev,\n      dateRange: {\n        ...prev.dateRange,\n        [field]: value,\n      },\n    }));\n  };\n\n  // Handle array filter toggle\n  const handleToggleFilter = <T extends string>(\n    field: keyof Pick<\n      TradeFilters,\n      'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'\n    >,\n    value: T\n  ) => {\n    setLocalFilters(prev => {\n      const currentValues = (prev[field] as T[]) || [];\n      const newValues = currentValues.includes(value)\n        ? currentValues.filter(v => v !== value)\n        : [...currentValues, value];\n\n      return {\n        ...prev,\n        [field]: newValues.length > 0 ? newValues : undefined,\n      };\n    });\n  };\n\n  // Apply filters\n  const applyFilters = () => {\n    updateFilters(localFilters);\n  };\n\n  // Reset filters\n  const handleResetFilters = () => {\n    resetFilters();\n    setLocalFilters(filters);\n  };\n\n  // Check if a filter value is selected\n  const isSelected = <T extends string>(\n    field: keyof Pick<\n      TradeFilters,\n      'symbols' | 'directions' | 'statuses' | 'timeframes' | 'sessions' | 'strategies' | 'tags'\n    >,\n    value: T\n  ): boolean => {\n    const values = localFilters[field] as T[] | undefined;\n    return values ? values.includes(value) : false;\n  };\n\n  return (\n    <Container className={className}>\n      <FilterHeader>\n        <HeaderTitle>\n          <Title>Trade Analysis</Title>\n          <LiveIndicator>LIVE</LiveIndicator>\n        </HeaderTitle>\n        <HeaderActions>\n          <ActionButton variant='secondary' onClick={handleResetFilters}>\n            Reset\n          </ActionButton>\n        </HeaderActions>\n      </FilterHeader>\n\n      <FilterContent>\n        <FilterGrid>\n          <FilterGroup>\n            <FilterLabel>Date Range</FilterLabel>\n            <DateRangeContainer>\n              <DateInput\n                type='date'\n                value={localFilters.dateRange.startDate}\n                onChange={e => handleDateChange('startDate', e.target.value)}\n                placeholder='Start Date'\n              />\n              <DateInput\n                type='date'\n                value={localFilters.dateRange.endDate}\n                onChange={e => handleDateChange('endDate', e.target.value)}\n                placeholder='End Date'\n              />\n            </DateRangeContainer>\n          </FilterGroup>\n\n          <FilterGroup>\n            <FilterLabel>Direction</FilterLabel>\n            <TagsContainer>\n              {directionOptions.map(direction => (\n                <FilterTag\n                  key={direction}\n                  variant={direction === 'long' ? 'success' : 'error'}\n                  selected={isSelected('directions', direction)}\n                  onClick={() => handleToggleFilter('directions', direction)}\n                >\n                  {direction}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterGroup>\n\n          <FilterGroup>\n            <FilterLabel>Status</FilterLabel>\n            <TagsContainer>\n              {statusOptions.map(status => (\n                <FilterTag\n                  key={status}\n                  variant={status === 'win' ? 'success' : status === 'loss' ? 'error' : 'info'}\n                  selected={isSelected('statuses', status)}\n                  onClick={() => handleToggleFilter('statuses', status)}\n                >\n                  {status}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterGroup>\n\n          {availableSymbols.length > 0 && (\n            <FilterGroup>\n              <FilterLabel>Symbols</FilterLabel>\n              <TagsContainer>\n                {availableSymbols.map(symbol => (\n                  <FilterTag\n                    key={symbol}\n                    variant='primary'\n                    selected={isSelected('symbols', symbol)}\n                    onClick={() => handleToggleFilter('symbols', symbol)}\n                  >\n                    {symbol}\n                  </FilterTag>\n                ))}\n              </TagsContainer>\n            </FilterGroup>\n          )}\n\n          {availableStrategies.length > 0 && (\n            <FilterGroup>\n              <FilterLabel>Strategies</FilterLabel>\n              <TagsContainer>\n                {availableStrategies.map(strategy => (\n                  <FilterTag\n                    key={strategy}\n                    variant='secondary'\n                    selected={isSelected('strategies', strategy)}\n                    onClick={() => handleToggleFilter('strategies', strategy)}\n                  >\n                    {strategy}\n                  </FilterTag>\n                ))}\n              </TagsContainer>\n            </FilterGroup>\n          )}\n\n          <FilterGroup>\n            <FilterLabel>Timeframe</FilterLabel>\n            <TagsContainer>\n              {timeframeOptions.map(timeframe => (\n                <FilterTag\n                  key={timeframe}\n                  variant='default'\n                  selected={isSelected('timeframes', timeframe)}\n                  onClick={() => handleToggleFilter('timeframes', timeframe)}\n                >\n                  {timeframe}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterGroup>\n\n          <FilterGroup>\n            <FilterLabel>Session</FilterLabel>\n            <TagsContainer>\n              {sessionOptions.map(session => (\n                <FilterTag\n                  key={session}\n                  variant='default'\n                  selected={isSelected('sessions', session)}\n                  onClick={() => handleToggleFilter('sessions', session)}\n                >\n                  {session}\n                </FilterTag>\n              ))}\n            </TagsContainer>\n          </FilterGroup>\n\n          {availableTags.length > 0 && (\n            <FilterGroup>\n              <FilterLabel>Tags</FilterLabel>\n              <TagsContainer>\n                {availableTags.map(tag => (\n                  <FilterTag\n                    key={tag}\n                    variant='info'\n                    selected={isSelected('tags', tag)}\n                    onClick={() => handleToggleFilter('tags', tag)}\n                  >\n                    {tag}\n                  </FilterTag>\n                ))}\n              </TagsContainer>\n            </FilterGroup>\n          )}\n        </FilterGrid>\n      </FilterContent>\n\n      <ActionBar>\n        <FilterStats>\n          {activeFilterCount > 0\n            ? `${activeFilterCount} filter${\n                activeFilterCount > 1 ? 's' : ''\n              } active • ${totalTrades} trades`\n            : `${totalTrades} trades • No filters applied`}\n        </FilterStats>\n        <ActionButtons>\n          <ActionButton variant='primary' onClick={applyFilters}>\n            Apply Filters\n          </ActionButton>\n        </ActionButtons>\n      </ActionBar>\n    </Container>\n  );\n};\n", "/**\n * Performance Summary Component\n *\n * Displays a summary of trading performance metrics\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n// PerformanceMetrics import removed as unused\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\n\ninterface PerformanceSummaryProps {\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst MetricCard = styled.div`\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  padding: ${({ theme }) => theme.spacing.md};\n  display: flex;\n  flex-direction: column;\n`;\n\nconst MetricLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst MetricValue = styled.div<{ positive?: boolean; negative?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textPrimary;\n  }};\n`;\n\nconst MetricChange = styled.div<{ positive?: boolean; negative?: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textSecondary;\n  }};\n`;\n\nexport const PerformanceSummary: React.FC<PerformanceSummaryProps> = ({ className }) => {\n  const { data } = useTradeAnalysis();\n\n  if (!data) {\n    return null;\n  }\n\n  const { metrics } = data;\n\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n\n  return (\n    <Container className={className}>\n      <MetricCard>\n        <MetricLabel>Total P&L</MetricLabel>\n        <MetricValue positive={metrics.totalProfitLoss > 0} negative={metrics.totalProfitLoss < 0}>\n          {formatCurrency(metrics.totalProfitLoss)}\n        </MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Win Rate</MetricLabel>\n        <MetricValue positive={metrics.winRate > 50} negative={metrics.winRate < 50}>\n          {formatPercent(metrics.winRate)}\n        </MetricValue>\n        <MetricChange>\n          {metrics.winningTrades} / {metrics.totalTrades} trades\n        </MetricChange>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Profit Factor</MetricLabel>\n        <MetricValue positive={metrics.profitFactor > 1} negative={metrics.profitFactor < 1}>\n          {metrics.profitFactor.toFixed(2)}\n        </MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Expectancy</MetricLabel>\n        <MetricValue positive={metrics.expectancy > 0} negative={metrics.expectancy < 0}>\n          {formatCurrency(metrics.expectancy)}\n        </MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Average Win</MetricLabel>\n        <MetricValue positive={true}>{formatCurrency(metrics.averageWin)}</MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Average Loss</MetricLabel>\n        <MetricValue negative={true}>{formatCurrency(-Math.abs(metrics.averageLoss))}</MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Largest Win</MetricLabel>\n        <MetricValue positive={true}>{formatCurrency(metrics.largestWin)}</MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Largest Loss</MetricLabel>\n        <MetricValue negative={true}>{formatCurrency(metrics.largestLoss)}</MetricValue>\n      </MetricCard>\n\n      <MetricCard>\n        <MetricLabel>Total Trades</MetricLabel>\n        <MetricValue>{metrics.totalTrades}</MetricValue>\n        <MetricChange>Avg Duration: {metrics.averageDuration.toFixed(0)} min</MetricChange>\n      </MetricCard>\n    </Container>\n  );\n};\n", "/**\n * TradesTableHeader Component\n *\n * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)\n * Sortable table header with F1 racing theme integration.\n *\n * BENEFITS:\n * - Focused responsibility (header logic only)\n * - Reusable sorting functionality\n * - F1 theme colors and interactions\n * - Better accessibility and UX\n * - Clean separation from table body\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type SortField =\n  | 'entryTime'\n  | 'symbol'\n  | 'direction'\n  | 'profitLoss'\n  | 'profitLossPercent'\n  | 'status';\n\nexport type SortDirection = 'asc' | 'desc';\n\nexport interface TradesTableHeaderProps {\n  /** Current sort field */\n  sortField: SortField;\n  /** Current sort direction */\n  sortDirection: SortDirection;\n  /** Sort handler */\n  onSort: (field: SortField) => void;\n}\n\nconst TableHead = styled.thead`\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  border-bottom: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n`;\n\nconst TableRow = styled.tr`\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n`;\n\nconst TableHeaderCell = styled.th<{ $sortable?: boolean; $active?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'}\n    ${({ theme }) => theme.spacing?.sm || '8px'};\n  text-align: left;\n  font-weight: 600;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme, $active }) =>\n    $active\n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.textPrimary || '#ffffff'};\n  cursor: ${({ $sortable }) => ($sortable ? 'pointer' : 'default')};\n  user-select: none;\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n\n  /* F1 Racing hover effect */\n  &:hover {\n    ${({ $sortable, theme }) =>\n      $sortable &&\n      `\n      color: ${theme.colors?.primary || 'var(--primary-color)'};\n      background: ${theme.colors?.primary || 'var(--primary-color)'}08;\n      transform: translateY(-1px);\n    `}\n  }\n\n  &:active {\n    ${({ $sortable }) =>\n      $sortable &&\n      `\n      transform: translateY(0);\n    `}\n  }\n`;\n\nconst SortIcon = styled.span<{ $direction: SortDirection; $active: boolean }>`\n  display: inline-block;\n  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-size: 12px;\n  opacity: ${({ $active }) => ($active ? 1 : 0.5)};\n  transition: all 0.2s ease;\n  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n\n  &::after {\n    content: '${({ $direction }) => ($direction === 'asc' ? '↑' : '↓')}';\n  }\n`;\n\nconst HeaderContent = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  min-height: 20px;\n`;\n\nconst HeaderText = styled.span`\n  flex: 1;\n`;\n\n/**\n * Column Configuration\n *\n * Defines the table columns with their properties.\n */\nconst COLUMNS = [\n  {\n    key: 'entryTime' as SortField,\n    label: 'Date/Time',\n    sortable: true,\n    width: '140px',\n  },\n  {\n    key: 'symbol' as SortField,\n    label: 'Symbol',\n    sortable: true,\n    width: '80px',\n  },\n  {\n    key: 'direction' as SortField,\n    label: 'Direction',\n    sortable: true,\n    width: '90px',\n  },\n  {\n    key: null,\n    label: 'Entry/Exit',\n    sortable: false,\n    width: '120px',\n  },\n  {\n    key: 'profitLoss' as SortField,\n    label: 'P&L',\n    sortable: true,\n    width: '100px',\n  },\n  {\n    key: 'profitLossPercent' as SortField,\n    label: 'P&L %',\n    sortable: true,\n    width: '80px',\n  },\n  {\n    key: 'status' as SortField,\n    label: 'Status',\n    sortable: true,\n    width: '90px',\n  },\n  {\n    key: null,\n    label: 'Strategy',\n    sortable: false,\n    width: '120px',\n  },\n  {\n    key: null,\n    label: 'Tags',\n    sortable: false,\n    width: '150px',\n  },\n] as const;\n\n/**\n * TradesTableHeader Component\n *\n * PATTERN: F1 Component Pattern\n * - Racing-inspired styling with red accents\n * - Smooth hover animations and transitions\n * - Clear visual feedback for sorting\n * - Accessible keyboard navigation\n * - Consistent with F1 design system\n */\nexport const TradesTableHeader: React.FC<TradesTableHeaderProps> = ({\n  sortField,\n  sortDirection,\n  onSort,\n}) => {\n  const handleHeaderClick = (columnKey: SortField | null) => {\n    if (columnKey && onSort) {\n      onSort(columnKey);\n    }\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent, columnKey: SortField | null) => {\n    if ((event.key === 'Enter' || event.key === ' ') && columnKey) {\n      event.preventDefault();\n      onSort(columnKey);\n    }\n  };\n\n  return (\n    <TableHead>\n      <TableRow>\n        {COLUMNS.map((column, index) => (\n          <TableHeaderCell\n            key={index}\n            $sortable={column.sortable}\n            $active={column.key === sortField}\n            style={{ width: column.width }}\n            onClick={() => handleHeaderClick(column.key)}\n            onKeyDown={(e) => handleKeyDown(e, column.key)}\n            tabIndex={column.sortable ? 0 : -1}\n            role={column.sortable ? 'button' : undefined}\n            aria-sort={\n              column.key === sortField\n                ? sortDirection === 'asc'\n                  ? 'ascending'\n                  : 'descending'\n                : undefined\n            }\n            title={\n              column.sortable\n                ? `Sort by ${column.label} ${\n                    column.key === sortField\n                      ? sortDirection === 'asc'\n                        ? '(descending)'\n                        : '(ascending)'\n                      : ''\n                  }`\n                : undefined\n            }\n          >\n            <HeaderContent>\n              <HeaderText>{column.label}</HeaderText>\n              {column.sortable && (\n                <SortIcon $direction={sortDirection} $active={column.key === sortField} />\n              )}\n            </HeaderContent>\n          </TableHeaderCell>\n        ))}\n      </TableRow>\n    </TableHead>\n  );\n};\n\nexport default TradesTableHeader;\n", "/**\n * TradesTableRow Component\n *\n * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)\n * Individual table row with F1 racing theme and optimized rendering.\n *\n * BENEFITS:\n * - Focused responsibility (single row rendering)\n * - Optimized with React.memo for performance\n * - F1 racing theme with smooth animations\n * - Reusable across different table contexts\n * - Clean prop interface and type safety\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Trade, TradeDirection } from '../types';\nimport { Badge, Tag } from '@adhd-trading-dashboard/shared';\n\nexport interface TradesTableRowProps {\n  /** Trade data */\n  trade: Trade;\n  /** Whether this row is selected */\n  isSelected: boolean;\n  /** Click handler */\n  onClick: () => void;\n  /** Formatting utilities */\n  formatters: {\n    formatDate: (dateString: string) => string;\n    formatCurrency: (value: number) => string;\n    formatPercent: (value: number) => string;\n  };\n  /** Event handlers */\n  handlers: {\n    getDirectionVariant: (direction: string) => string;\n    getStatusVariant: (status: string) => string;\n  };\n}\n\nconst TableRow = styled.tr<{ $isSelected?: boolean }>`\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ theme, $isSelected }) =>\n    $isSelected ? `${theme.colors?.primary || 'var(--primary-color)'}15` : 'transparent'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  /* F1 Racing hover effect */\n  &:hover {\n    background: ${({ theme, $isSelected }) =>\n      $isSelected\n        ? `${theme.colors?.primary || 'var(--primary-color)'}20`\n        : `${theme.colors?.primary || 'var(--primary-color)'}08`};\n    transform: translateY(-1px);\n    box-shadow: 0 2px 4px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'};\n  vertical-align: middle;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  border-right: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'}40;\n\n  &:last-child {\n    border-right: none;\n  }\n`;\n\nconst DirectionBadge = styled(Badge)<{ $direction: TradeDirection }>`\n  text-transform: capitalize;\n  font-weight: 600;\n  min-width: 60px;\n  justify-content: center;\n`;\n\nconst StatusBadge = styled(Badge)<{ $status: string }>`\n  text-transform: capitalize;\n  font-weight: 600;\n  min-width: 70px;\n  justify-content: center;\n`;\n\nconst ProfitLoss = styled.span<{ $value: number }>`\n  color: ${({ theme, $value }) =>\n    $value > 0\n      ? theme.colors?.success || 'var(--success-color)'\n      : $value < 0\n      ? theme.colors?.error || 'var(--error-color)'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-weight: ${({ $value }) => ($value !== 0 ? 600 : 400)};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n`;\n\nconst PriceRange = styled.span`\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  max-width: 150px;\n`;\n\nconst SymbolCell = styled(TableCell)`\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n`;\n\nconst DateCell = styled(TableCell)`\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  min-width: 140px;\n`;\n\n/**\n * TradesTableRow Component\n *\n * PATTERN: F1 Component Pattern\n * - Racing-inspired hover effects and animations\n * - Optimized rendering with React.memo\n * - Consistent F1 color scheme\n * - Accessible interaction patterns\n * - Clean separation of concerns\n */\nexport const TradesTableRow: React.FC<TradesTableRowProps> = React.memo(\n  ({ trade, isSelected, onClick, formatters, handlers }) => {\n    const { formatDate, formatCurrency } = formatters;\n    const { getDirectionVariant, getStatusVariant } = handlers;\n\n    const getTradeStatus = (profitLoss: number): string => {\n      if (profitLoss > 0) return 'win';\n      if (profitLoss < 0) return 'loss';\n      return 'breakeven';\n    };\n\n    return (\n      <TableRow\n        $isSelected={isSelected}\n        onClick={onClick}\n        role='button'\n        tabIndex={0}\n        onKeyDown={e => {\n          if (e.key === 'Enter' || e.key === ' ') {\n            e.preventDefault();\n            onClick();\n          }\n        }}\n        aria-selected={isSelected}\n        title={`Trade ${trade.symbol} - Click to ${isSelected ? 'deselect' : 'select'}`}\n      >\n        {/* Date/Time */}\n        <DateCell>{formatDate(trade.date)}</DateCell>\n\n        {/* Symbol */}\n        <SymbolCell>{trade.symbol}</SymbolCell>\n\n        {/* Direction */}\n        <TableCell>\n          <DirectionBadge\n            $direction={trade.direction.toLowerCase() as TradeDirection}\n            variant={getDirectionVariant(trade.direction) as any}\n            size='small'\n          >\n            {trade.direction}\n          </DirectionBadge>\n        </TableCell>\n\n        {/* Entry/Exit Prices */}\n        <TableCell>\n          <PriceRange>\n            {trade.entry} → {trade.exit}\n          </PriceRange>\n        </TableCell>\n\n        {/* Profit/Loss */}\n        <TableCell>\n          <ProfitLoss $value={trade.profitLoss}>{formatCurrency(trade.profitLoss)}</ProfitLoss>\n        </TableCell>\n\n        {/* Profit/Loss Percentage */}\n        <TableCell>\n          <ProfitLoss $value={trade.profitLoss || 0}>\n            {formatCurrency(trade.profitLoss || 0)}\n          </ProfitLoss>\n        </TableCell>\n\n        {/* Status */}\n        <TableCell>\n          <StatusBadge\n            $status={getTradeStatus(trade.profitLoss)}\n            variant={getStatusVariant(getTradeStatus(trade.profitLoss)) as any}\n            size='small'\n          >\n            {getTradeStatus(trade.profitLoss)}\n          </StatusBadge>\n        </TableCell>\n\n        {/* Strategy */}\n        <TableCell>{trade.strategy || '-'}</TableCell>\n\n        {/* Tags */}\n        <TableCell>\n          <TagsContainer>\n            {trade.tags?.slice(0, 3).map((tag, index) => (\n              <Tag key={index} size='small' variant='default'>\n                {tag}\n              </Tag>\n            ))}\n            {trade.tags?.length > 3 && (\n              <Tag size='small' variant='secondary'>\n                +{trade.tags.length - 3}\n              </Tag>\n            )}\n          </TagsContainer>\n        </TableCell>\n      </TableRow>\n    );\n  }\n);\n\n// Display name for debugging\nTradesTableRow.displayName = 'TradesTableRow';\n\nexport default TradesTableRow;\n", "/**\n * TradesTableBody Component\n * \n * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)\n * Table body with optimized row rendering and F1 styling.\n * \n * BENEFITS:\n * - Focused responsibility (body rendering only)\n * - Optimized for performance with React.memo\n * - F1 racing theme with hover effects\n * - Clean separation from header logic\n * - Reusable row components\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Trade } from '../types';\nimport { TradesTableRow } from './TradesTableRow';\n\nexport interface TradesTableBodyProps {\n  /** Array of trades to display */\n  trades: Trade[];\n  /** Currently selected trade ID */\n  selectedTradeId: string | null;\n  /** Row click handler */\n  onRowClick: (tradeId: string) => void;\n  /** Formatting utilities */\n  formatters: {\n    formatDate: (dateString: string) => string;\n    formatCurrency: (value: number) => string;\n    formatPercent: (value: number) => string;\n  };\n  /** Event handlers */\n  handlers: {\n    getDirectionVariant: (direction: string) => string;\n    getStatusVariant: (status: string) => string;\n  };\n}\n\nconst TableBody = styled.tbody`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n`;\n\nconst EmptyRow = styled.tr`\n  background: transparent;\n`;\n\nconst EmptyCell = styled.td`\n  padding: ${({ theme }) => theme.spacing?.xl || '24px'};\n  text-align: center;\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-style: italic;\n  colspan: 9;\n`;\n\n/**\n * TradesTableBody Component\n * \n * PATTERN: F1 Component Pattern\n * - Optimized rendering with React.memo\n * - Consistent F1 racing theme\n * - Performance-focused row rendering\n * - Clean prop interface\n * - Accessible table structure\n */\nexport const TradesTableBody: React.FC<TradesTableBodyProps> = React.memo(({\n  trades,\n  selectedTradeId,\n  onRowClick,\n  formatters,\n  handlers,\n}) => {\n  // Handle empty state\n  if (!trades || trades.length === 0) {\n    return (\n      <TableBody>\n        <EmptyRow>\n          <EmptyCell colSpan={9}>\n            No trades found for the selected filters.\n          </EmptyCell>\n        </EmptyRow>\n      </TableBody>\n    );\n  }\n\n  return (\n    <TableBody>\n      {trades.map((trade) => (\n        <TradesTableRow\n          key={trade.id}\n          trade={trade}\n          isSelected={trade.id === selectedTradeId}\n          onClick={() => onRowClick(trade.id)}\n          formatters={formatters}\n          handlers={handlers}\n        />\n      ))}\n    </TableBody>\n  );\n});\n\n// Display name for debugging\nTradesTableBody.displayName = 'TradesTableBody';\n\nexport default TradesTableBody;\n", "/**\n * useTradesTableData Hook\n *\n * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)\n * Custom hook for managing trades table data, sorting, and formatting.\n *\n * BENEFITS:\n * - Focused responsibility (data management only)\n * - Reusable across different table implementations\n * - Optimized with useMemo for performance\n * - Clean separation of business logic\n * - Easy to test and maintain\n */\n\nimport { useState, useMemo, useCallback } from 'react';\nimport { Trade } from '../types';\nimport { SortField, SortDirection } from '../components/TradesTableHeader';\n\nexport interface UseTradesTableDataReturn {\n  /** Sorted trades array */\n  sortedTrades: Trade[];\n  /** Current sort field */\n  sortField: SortField;\n  /** Current sort direction */\n  sortDirection: SortDirection;\n  /** Sort handler function */\n  handleSort: (field: SortField) => void;\n  /** Formatting utilities */\n  formatters: {\n    formatDate: (dateString: string) => string;\n    formatCurrency: (value: number) => string;\n    formatPercent: (value: number) => string;\n  };\n  /** Event handlers */\n  handlers: {\n    getDirectionVariant: (direction: string) => string;\n    getStatusVariant: (status: string) => string;\n  };\n}\n\n/**\n * useTradesTableData Hook\n *\n * Manages sorting, filtering, and formatting for the trades table.\n * Optimized for performance with memoization.\n */\nexport const useTradesTableData = (trades: Trade[]): UseTradesTableDataReturn => {\n  const [sortField, setSortField] = useState<SortField>('entryTime');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n\n  /**\n   * Handle sorting logic\n   */\n  const handleSort = useCallback(\n    (field: SortField) => {\n      if (sortField === field) {\n        // Toggle direction if same field\n        setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));\n      } else {\n        // Set new field and default direction\n        setSortField(field);\n        setSortDirection('desc');\n      }\n    },\n    [sortField]\n  );\n\n  /**\n   * Sort trades based on current sort field and direction\n   */\n  const sortedTrades = useMemo(() => {\n    if (!trades || trades.length === 0) return [];\n\n    return [...trades].sort((a, b) => {\n      let comparison = 0;\n\n      switch (sortField) {\n        case 'entryTime':\n          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();\n          break;\n        case 'symbol':\n          comparison = a.symbol.localeCompare(b.symbol);\n          break;\n        case 'direction':\n          comparison = a.direction.localeCompare(b.direction);\n          break;\n        case 'profitLoss':\n          comparison = a.profitLoss - b.profitLoss;\n          break;\n        case 'profitLossPercent':\n          // Calculate percentage from profitLoss since profitLossPercent doesn't exist on Trade\n          comparison = a.profitLoss - b.profitLoss;\n          break;\n        case 'status':\n          // Derive status from profitLoss since status doesn't exist on Trade\n          const statusA = a.profitLoss > 0 ? 'win' : a.profitLoss < 0 ? 'loss' : 'breakeven';\n          const statusB = b.profitLoss > 0 ? 'win' : b.profitLoss < 0 ? 'loss' : 'breakeven';\n          comparison = statusA.localeCompare(statusB);\n          break;\n        default:\n          comparison = 0;\n      }\n\n      return sortDirection === 'asc' ? comparison : -comparison;\n    });\n  }, [trades, sortField, sortDirection]);\n\n  /**\n   * Formatting utilities\n   */\n  const formatters = useMemo(\n    () => ({\n      /**\n       * Format date string for display\n       */\n      formatDate: (dateString: string): string => {\n        try {\n          const date = new Date(dateString);\n          if (isNaN(date.getTime())) {\n            return 'Invalid Date';\n          }\n\n          return (\n            date.toLocaleDateString('en-US', {\n              month: 'short',\n              day: 'numeric',\n              year: '2-digit',\n            }) +\n            ' ' +\n            date.toLocaleTimeString('en-US', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false,\n            })\n          );\n        } catch (error) {\n          console.warn('Error formatting date:', dateString, error);\n          return 'Invalid Date';\n        }\n      },\n\n      /**\n       * Format currency value for display\n       */\n      formatCurrency: (value: number): string => {\n        try {\n          return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2,\n          }).format(value);\n        } catch (error) {\n          console.warn('Error formatting currency:', value, error);\n          return `$${value.toFixed(2)}`;\n        }\n      },\n\n      /**\n       * Format percentage value for display\n       */\n      formatPercent: (value: number): string => {\n        try {\n          const sign = value > 0 ? '+' : '';\n          return `${sign}${value.toFixed(2)}%`;\n        } catch (error) {\n          console.warn('Error formatting percent:', value, error);\n          return `${value}%`;\n        }\n      },\n    }),\n    []\n  );\n\n  /**\n   * Event handlers and utilities\n   */\n  const handlers = useMemo(\n    () => ({\n      /**\n       * Get badge variant for trade direction\n       */\n      getDirectionVariant: (direction: string): string => {\n        switch (direction.toLowerCase()) {\n          case 'long':\n            return 'success';\n          case 'short':\n            return 'error';\n          default:\n            return 'default';\n        }\n      },\n\n      /**\n       * Get badge variant for trade status\n       */\n      getStatusVariant: (status: string): string => {\n        switch (status.toLowerCase()) {\n          case 'win':\n            return 'success';\n          case 'loss':\n            return 'error';\n          case 'breakeven':\n            return 'warning';\n          case 'open':\n            return 'info';\n          default:\n            return 'default';\n        }\n      },\n    }),\n    []\n  );\n\n  return {\n    sortedTrades,\n    sortField,\n    sortDirection,\n    handleSort,\n    formatters,\n    handlers,\n  };\n};\n\nexport default useTradesTableData;\n", "/**\n * TradesTableContainer Component\n *\n * REFACTORED FROM: TradesTable.tsx (330 lines → focused components)\n * Main orchestrator for the trades table with F1 container pattern.\n *\n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and loading states\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense } from 'react';\nimport styled from 'styled-components';\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\nimport { TradesTableHeader } from './TradesTableHeader';\nimport { TradesTableBody } from './TradesTableBody';\nimport { useTradesTableData } from '../hooks/useTradesTableData';\n\nexport interface TradesTableContainerProps {\n  /** Custom className */\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  overflow: hidden;\n`;\n\nconst TableWrapper = styled.div`\n  flex: 1;\n  overflow: auto;\n  position: relative;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n`;\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '24px'};\n  text-align: center;\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  min-height: 200px;\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.5;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst EmptyDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 300px;\n`;\n\nconst LoadingFallback: React.FC = () => (\n  <EmptyState>\n    <EmptyIcon>⏳</EmptyIcon>\n    <EmptyTitle>Loading Trades</EmptyTitle>\n    <EmptyDescription>Please wait while we load your trading data...</EmptyDescription>\n  </EmptyState>\n);\n\n// ErrorFallback component removed as unused\n\n/**\n * TradesTableContent Component\n *\n * Renders the table content with proper error handling.\n */\nconst TradesTableContent: React.FC = () => {\n  const { data, selectedTradeId, selectTrade } = useTradeAnalysis();\n  const { sortedTrades, sortField, sortDirection, handleSort, formatters, handlers } =\n    useTradesTableData(data?.trades || []);\n\n  // Handle empty state\n  if (!data || !data.trades || data.trades.length === 0) {\n    return (\n      <EmptyState>\n        <EmptyIcon>📊</EmptyIcon>\n        <EmptyTitle>No Trades Found</EmptyTitle>\n        <EmptyDescription>\n          No trades match your current filters. Try adjusting your search criteria or add some\n          trades to get started.\n        </EmptyDescription>\n      </EmptyState>\n    );\n  }\n\n  // Handle row selection\n  const handleRowClick = (tradeId: string) => {\n    selectTrade(tradeId === selectedTradeId ? null : tradeId);\n  };\n\n  return (\n    <TableWrapper>\n      <Table>\n        <TradesTableHeader\n          sortField={sortField}\n          sortDirection={sortDirection}\n          onSort={handleSort}\n        />\n        <TradesTableBody\n          trades={sortedTrades}\n          selectedTradeId={selectedTradeId}\n          onRowClick={handleRowClick}\n          formatters={formatters}\n          handlers={handlers}\n        />\n      </Table>\n    </TableWrapper>\n  );\n};\n\n/**\n * TradesTableContainer Component\n *\n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const TradesTableContainer: React.FC<TradesTableContainerProps> = ({ className }) => {\n  return (\n    <Container className={className}>\n      <Suspense fallback={<LoadingFallback />}>\n        <TradesTableContent />\n      </Suspense>\n    </Container>\n  );\n};\n\nexport default TradesTableContainer;\n", "/**\n * Trades Table Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 330 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 95% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { TradesTableContainer } from './TradesTableContainer';\n\ninterface TradesTableProps {\n  className?: string;\n}\n\n/**\n * TradesTable Component\n *\n * Simple wrapper that renders the container.\n * Follows the proven architecture pattern.\n */\nexport const TradesTable: React.FC<TradesTableProps> = (props) => {\n  return <TradesTableContainer {...props} />;\n};\n\nexport default TradesTable;\n", "/**\n * Category Performance Chart Component\n *\n * Displays performance metrics by category (symbol, strategy, timeframe, session)\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { CategoryPerformance } from '../types';\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\n\ninterface CategoryPerformanceChartProps {\n  className?: string;\n  category: 'symbol' | 'strategy' | 'timeframe' | 'session';\n  title: string;\n}\n\ntype SortField = 'value' | 'trades' | 'winRate' | 'profitLoss' | 'averageProfitLoss';\ntype SortDirection = 'asc' | 'desc';\n\nconst Container = styled.div`\n  overflow-x: auto;\n`;\n\nconst Table = styled.table`\n  width: 100%;\n  border-collapse: collapse;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n`;\n\nconst TableHead = styled.thead`\n  background-color: ${({ theme }) => theme.colors.background};\n`;\n\nconst TableBody = styled.tbody``;\n\nconst TableRow = styled.tr`\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst TableHeaderCell = styled.th<{ sortable?: boolean; active?: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm};\n  text-align: left;\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, active }) => (active ? theme.colors.primary : theme.colors.textPrimary)};\n  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};\n\n  &:hover {\n    ${({ sortable, theme }) =>\n      sortable &&\n      `\n      color: ${theme.colors.primary};\n    `}\n  }\n`;\n\nconst TableCell = styled.td`\n  padding: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst SortIcon = styled.span<{ direction: SortDirection }>`\n  display: inline-block;\n  margin-left: ${({ theme }) => theme.spacing.xs};\n\n  &::after {\n    content: '${({ direction }) => (direction === 'asc' ? '↑' : '↓')}';\n  }\n`;\n\nconst BarContainer = styled.div`\n  height: 8px;\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.pill};\n  overflow: hidden;\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Bar = styled.div<{ width: number; positive: boolean }>`\n  height: 100%;\n  width: ${({ width }) => `${width}%`};\n  background-color: ${({ theme, positive }) =>\n    positive ? theme.colors.profit : theme.colors.loss};\n`;\n\nconst ProfitLoss = styled.span<{ value: number }>`\n  color: ${({ theme, value }) =>\n    value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};\n  font-weight: ${({ theme, value }) =>\n    value !== 0 ? theme.fontWeights.medium : theme.fontWeights.regular};\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const CategoryPerformanceChart: React.FC<CategoryPerformanceChartProps> = ({\n  className,\n  category,\n  title,\n}) => {\n  const { data } = useTradeAnalysis();\n  const [sortField, setSortField] = useState<SortField>('profitLoss');\n  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');\n\n  if (!data) {\n    return null;\n  }\n\n  let performanceData: CategoryPerformance[] = [];\n\n  switch (category) {\n    case 'symbol':\n      performanceData = data.symbolPerformance;\n      break;\n    case 'strategy':\n      performanceData = data.strategyPerformance;\n      break;\n    case 'timeframe':\n      performanceData = data.timeframePerformance;\n      break;\n    case 'session':\n      performanceData = data.sessionPerformance;\n      break;\n  }\n\n  if (!performanceData || performanceData.length === 0) {\n    return <EmptyState>No {category} performance data available.</EmptyState>;\n  }\n\n  const handleSort = (field: SortField) => {\n    if (sortField === field) {\n      // Toggle direction if same field\n      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');\n    } else {\n      // Set new field and default direction\n      setSortField(field);\n      setSortDirection('desc');\n    }\n  };\n\n  // Sort data\n  const sortedData = [...performanceData].sort((a, b) => {\n    let comparison = 0;\n\n    switch (sortField) {\n      case 'value':\n        comparison = a.value.localeCompare(b.value);\n        break;\n      case 'trades':\n        comparison = a.trades - b.trades;\n        break;\n      case 'winRate':\n        comparison = a.winRate - b.winRate;\n        break;\n      case 'profitLoss':\n        comparison = a.profitLoss - b.profitLoss;\n        break;\n      case 'averageProfitLoss':\n        comparison = a.averageProfitLoss - b.averageProfitLoss;\n        break;\n      default:\n        comparison = 0;\n    }\n\n    return sortDirection === 'asc' ? comparison : -comparison;\n  });\n\n  // Find max profit/loss for bar scaling\n  const maxProfitLoss = Math.max(...performanceData.map((item) => Math.abs(item.profitLoss)));\n\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n\n  return (\n    <Container className={className}>\n      <Table>\n        <TableHead>\n          <TableRow>\n            <TableHeaderCell\n              sortable\n              active={sortField === 'value'}\n              onClick={() => handleSort('value')}\n            >\n              {title}\n              {sortField === 'value' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n\n            <TableHeaderCell\n              sortable\n              active={sortField === 'trades'}\n              onClick={() => handleSort('trades')}\n            >\n              Trades\n              {sortField === 'trades' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n\n            <TableHeaderCell\n              sortable\n              active={sortField === 'winRate'}\n              onClick={() => handleSort('winRate')}\n            >\n              Win Rate\n              {sortField === 'winRate' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n\n            <TableHeaderCell\n              sortable\n              active={sortField === 'profitLoss'}\n              onClick={() => handleSort('profitLoss')}\n            >\n              P&L\n              {sortField === 'profitLoss' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n\n            <TableHeaderCell\n              sortable\n              active={sortField === 'averageProfitLoss'}\n              onClick={() => handleSort('averageProfitLoss')}\n            >\n              Avg P&L\n              {sortField === 'averageProfitLoss' && <SortIcon direction={sortDirection} />}\n            </TableHeaderCell>\n          </TableRow>\n        </TableHead>\n\n        <TableBody>\n          {sortedData.map((item, index) => (\n            <TableRow key={index}>\n              <TableCell>{item.value}</TableCell>\n              <TableCell>{item.trades}</TableCell>\n              <TableCell>{formatPercent(item.winRate)}</TableCell>\n              <TableCell>\n                <ProfitLoss value={item.profitLoss}>{formatCurrency(item.profitLoss)}</ProfitLoss>\n                <BarContainer>\n                  <Bar\n                    width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)}\n                    positive={item.profitLoss >= 0}\n                  />\n                </BarContainer>\n              </TableCell>\n              <TableCell>\n                <ProfitLoss value={item.averageProfitLoss}>\n                  {formatCurrency(item.averageProfitLoss)}\n                </ProfitLoss>\n              </TableCell>\n            </TableRow>\n          ))}\n        </TableBody>\n      </Table>\n    </Container>\n  );\n};\n", "/**\n * Time Performance Chart Component\n *\n * Displays performance metrics by time (time of day, day of week)\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n// TimePerformance import removed as unused\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\n\ninterface TimePerformanceChartProps {\n  className?: string;\n  timeType: 'timeOfDay' | 'dayOfWeek';\n  title: string;\n}\n\nconst Container = styled.div``;\n\nconst ChartContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst TimeSlot = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst TimeSlotHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst TimeSlotLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst TimeSlotMetrics = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.md};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst MetricValue = styled.span<{ positive?: boolean; negative?: boolean }>`\n  color: ${({ theme, positive, negative }) => {\n    if (positive) return theme.colors.profit;\n    if (negative) return theme.colors.loss;\n    return theme.colors.textSecondary;\n  }};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n`;\n\nconst BarContainer = styled.div`\n  height: 24px;\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  overflow: hidden;\n  position: relative;\n`;\n\nconst Bar = styled.div<{ width: number; positive: boolean }>`\n  height: 100%;\n  width: ${({ width }) => `${width}%`};\n  background-color: ${({ theme, positive }) =>\n    positive ? theme.colors.profit : theme.colors.loss};\n  transition: width 0.3s ease;\n`;\n\nconst BarLabel = styled.div`\n  position: absolute;\n  top: 0;\n  left: ${({ theme }) => theme.spacing.sm};\n  height: 100%;\n  display: flex;\n  align-items: center;\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textInverse};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const TimePerformanceChart: React.FC<TimePerformanceChartProps> = ({\n  className,\n  timeType,\n}) => {\n  const { data } = useTradeAnalysis();\n\n  if (!data) {\n    return null;\n  }\n\n  const performanceData =\n    timeType === 'timeOfDay' ? data.timeOfDayPerformance : data.dayOfWeekPerformance;\n\n  if (!performanceData || performanceData.length === 0) {\n    return <EmptyState>No {timeType} performance data available.</EmptyState>;\n  }\n\n  // Find max profit/loss for bar scaling\n  const maxProfitLoss = Math.max(...performanceData.map(item => Math.abs(item.profitLoss)));\n\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  const formatPercent = (value: number): string => {\n    return `${value.toFixed(2)}%`;\n  };\n\n  return (\n    <Container className={className}>\n      <ChartContainer>\n        {performanceData.map((item, index) => (\n          <TimeSlot key={index}>\n            <TimeSlotHeader>\n              <TimeSlotLabel>{item.timeSlot}</TimeSlotLabel>\n              <TimeSlotMetrics>\n                <div>\n                  Trades: <MetricValue>{item.trades}</MetricValue>\n                </div>\n                <div>\n                  Win Rate:{' '}\n                  <MetricValue positive={item.winRate > 50} negative={item.winRate < 50}>\n                    {formatPercent(item.winRate)}\n                  </MetricValue>\n                </div>\n                <div>\n                  P&L:{' '}\n                  <MetricValue positive={item.profitLoss > 0} negative={item.profitLoss < 0}>\n                    {formatCurrency(item.profitLoss)}\n                  </MetricValue>\n                </div>\n              </TimeSlotMetrics>\n            </TimeSlotHeader>\n            <BarContainer>\n              <Bar\n                width={Math.min(100, (Math.abs(item.profitLoss) / maxProfitLoss) * 100)}\n                positive={item.profitLoss >= 0}\n              >\n                {item.profitLoss !== 0 && <BarLabel>{formatCurrency(item.profitLoss)}</BarLabel>}\n              </Bar>\n            </BarContainer>\n          </TimeSlot>\n        ))}\n      </ChartContainer>\n    </Container>\n  );\n};\n", "/**\n * Trade Detail Component\n *\n * Displays detailed information about a selected trade\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Trade import removed as unused\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\nimport { Badge, Card, Tag } from '@adhd-trading-dashboard/shared';\n\ninterface TradeDetailProps {\n  className?: string;\n}\n\nconst Container = styled(Card)`\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xxs};\n`;\n\nconst DetailValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ProfitLoss = styled.div<{ value: number }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: ${({ theme }) => theme.fontWeights.semibold};\n  color: ${({ theme, value }) =>\n    value > 0 ? theme.colors.profit : value < 0 ? theme.colors.loss : theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst TagsContainer = styled.div`\n  display: flex;\n  flex-wrap: wrap;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Notes = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  padding-top: ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  white-space: pre-wrap;\n`;\n\nconst EmptyState = styled.div`\n  padding: ${({ theme }) => theme.spacing.lg};\n  text-align: center;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-style: italic;\n`;\n\nexport const TradeDetail: React.FC<TradeDetailProps> = ({ className }) => {\n  const { data, selectedTradeId } = useTradeAnalysis();\n\n  if (!data || !selectedTradeId) {\n    return null;\n  }\n\n  const selectedTrade = data.trades.find(trade => trade.id === selectedTradeId);\n\n  if (!selectedTrade) {\n    return <EmptyState>Trade not found.</EmptyState>;\n  }\n\n  const formatDate = (dateString: string): string => {\n    const date = new Date(dateString);\n    return (\n      date.toLocaleDateString() +\n      ' ' +\n      date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    );\n  };\n\n  const formatCurrency = (value: number): string => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2,\n    }).format(value);\n  };\n\n  // formatPercent function removed as unused\n\n  const getDirectionVariant = (direction: string): string => {\n    return direction === 'long' ? 'success' : 'error';\n  };\n\n  // getStatusVariant and calculateDuration functions removed as unused\n\n  return (\n    <Container className={className} title='Trade Details' variant='default' padding='medium'>\n      <ProfitLoss value={selectedTrade.profitLoss || 0}>\n        {formatCurrency(selectedTrade.profitLoss || 0)}\n      </ProfitLoss>\n\n      <DetailGrid>\n        <DetailSection>\n          <DetailLabel>Direction</DetailLabel>\n          <DetailValue>\n            <Badge variant={getDirectionVariant(selectedTrade.direction) as any} size='small'>\n              {selectedTrade.direction}\n            </Badge>\n          </DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Symbol</DetailLabel>\n          <DetailValue>{selectedTrade.symbol}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Entry Time</DetailLabel>\n          <DetailValue>{formatDate(selectedTrade.date)}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Entry Price</DetailLabel>\n          <DetailValue>${selectedTrade.entry}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Exit Price</DetailLabel>\n          <DetailValue>${selectedTrade.exit}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Size</DetailLabel>\n          <DetailValue>{selectedTrade.size}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Strategy</DetailLabel>\n          <DetailValue>{selectedTrade.strategy}</DetailValue>\n        </DetailSection>\n\n        <DetailSection>\n          <DetailLabel>Strategy</DetailLabel>\n          <DetailValue>{selectedTrade.strategy}</DetailValue>\n        </DetailSection>\n      </DetailGrid>\n\n      {selectedTrade.tags && selectedTrade.tags.length > 0 && (\n        <DetailSection>\n          <DetailLabel>Tags</DetailLabel>\n          <TagsContainer>\n            {selectedTrade.tags.map((tag, index) => (\n              <Tag key={index} variant='info' size='small'>\n                {tag}\n              </Tag>\n            ))}\n          </TagsContainer>\n        </DetailSection>\n      )}\n\n      {selectedTrade.notes && (\n        <Notes>\n          <DetailLabel>Notes</DetailLabel>\n          <DetailValue>{selectedTrade.notes}</DetailValue>\n        </Notes>\n      )}\n    </Container>\n  );\n};\n", "/**\n * TabContentRenderer Component\n * \n * Renders the appropriate content for each analysis tab.\n * Extracted from the original TradeAnalysis component for better separation of concerns.\n */\n\nimport React from 'react';\nimport { DataCard } from '@adhd-trading-dashboard/shared';\nimport { PerformanceSummary } from './PerformanceSummary';\nimport { TradesTable } from './TradesTable';\nimport { CategoryPerformanceChart } from './CategoryPerformanceChart';\nimport { TimePerformanceChart } from './TimePerformanceChart';\nimport { TradeDetail } from './TradeDetail';\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\n\nexport interface TabContentRendererProps {\n  /** Currently active tab */\n  activeTab: string;\n  /** Analysis data */\n  data: any;\n  /** Loading state */\n  isLoading: boolean;\n  /** Error message */\n  error: string | null;\n}\n\n/**\n * TabContentRenderer Component\n * \n * Handles rendering of content for each analysis tab with proper\n * error handling and loading states.\n */\nexport const TabContentRenderer: React.FC<TabContentRendererProps> = ({\n  activeTab,\n  data,\n  isLoading,\n  error,\n}) => {\n  const { selectedTradeId } = useTradeAnalysis();\n\n  const renderContent = () => {\n    switch (activeTab) {\n      case 'summary':\n        return (\n          <>\n            <DataCard\n              title=\"Performance Summary\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.metrics}\n              emptyMessage=\"No performance data available for the selected filters.\"\n            >\n              <PerformanceSummary />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Time of Day\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}\n              emptyMessage=\"No time of day performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"timeOfDay\" title=\"Time of Day\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Day of Week\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}\n              emptyMessage=\"No day of week performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"dayOfWeek\" title=\"Day of Week\" />\n            </DataCard>\n          </>\n        );\n\n      case 'trades':\n        return (\n          <>\n            <DataCard\n              title=\"Trades\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.trades || data.trades.length === 0}\n              emptyMessage=\"No trades available for the selected filters.\"\n            >\n              <TradesTable />\n            </DataCard>\n\n            {selectedTradeId && <TradeDetail />}\n          </>\n        );\n\n      case 'symbols':\n        return (\n          <DataCard\n            title=\"Performance by Symbol\"\n            isLoading={isLoading}\n            hasError={!!error}\n            errorMessage={error || ''}\n            isEmpty={!data?.symbolPerformance || data.symbolPerformance.length === 0}\n            emptyMessage=\"No symbol performance data available for the selected filters.\"\n          >\n            <CategoryPerformanceChart category=\"symbol\" title=\"Symbol\" />\n          </DataCard>\n        );\n\n      case 'strategies':\n        return (\n          <DataCard\n            title=\"Performance by Strategy\"\n            isLoading={isLoading}\n            hasError={!!error}\n            errorMessage={error || ''}\n            isEmpty={!data?.strategyPerformance || data.strategyPerformance.length === 0}\n            emptyMessage=\"No strategy performance data available for the selected filters.\"\n          >\n            <CategoryPerformanceChart category=\"strategy\" title=\"Strategy\" />\n          </DataCard>\n        );\n\n      case 'timeframes':\n        return (\n          <>\n            <DataCard\n              title=\"Performance by Timeframe\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeframePerformance || data.timeframePerformance.length === 0}\n              emptyMessage=\"No timeframe performance data available for the selected filters.\"\n            >\n              <CategoryPerformanceChart category=\"timeframe\" title=\"Timeframe\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Session\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.sessionPerformance || data.sessionPerformance.length === 0}\n              emptyMessage=\"No session performance data available for the selected filters.\"\n            >\n              <CategoryPerformanceChart category=\"session\" title=\"Session\" />\n            </DataCard>\n          </>\n        );\n\n      case 'time':\n        return (\n          <>\n            <DataCard\n              title=\"Performance by Time of Day\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.timeOfDayPerformance || data.timeOfDayPerformance.length === 0}\n              emptyMessage=\"No time of day performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"timeOfDay\" title=\"Time of Day\" />\n            </DataCard>\n\n            <DataCard\n              title=\"Performance by Day of Week\"\n              isLoading={isLoading}\n              hasError={!!error}\n              errorMessage={error || ''}\n              isEmpty={!data?.dayOfWeekPerformance || data.dayOfWeekPerformance.length === 0}\n              emptyMessage=\"No day of week performance data available for the selected filters.\"\n            >\n              <TimePerformanceChart timeType=\"dayOfWeek\" title=\"Day of Week\" />\n            </DataCard>\n          </>\n        );\n\n      default:\n        return (\n          <DataCard\n            title=\"Unknown Tab\"\n            hasError={true}\n            errorMessage={`Unknown tab: ${activeTab}`}\n          >\n            <div style={{ textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }}>\n              Tab content not found: {activeTab}\n            </div>\n          </DataCard>\n        );\n    }\n  };\n\n  return <>{renderContent()}</>;\n};\n\nexport default TabContentRenderer;\n", "/**\n * TradeAnalysisContainer Component\n *\n * REFACTORED: Main orchestrator for the trade analysis feature\n * Follows the proven TradingDashboard architecture pattern for consistency.\n *\n * BENEFITS:\n * - Clean separation of concerns\n * - Comprehensive error boundaries\n * - F1 racing theme consistency\n * - Performance optimized with memoization\n * - Reusable tab navigation pattern\n */\n\nimport React, { Suspense } from 'react';\nimport styled from 'styled-components';\nimport { LoadingSpinner } from '@adhd-trading-dashboard/shared';\nimport { useTradeAnalysis } from '../hooks/TradeAnalysisContext';\nimport { AnalysisHeader } from './AnalysisHeader';\nimport { AnalysisTabs } from './AnalysisTabs';\nimport { FilterPanel } from './FilterPanel';\nimport { TabContentRenderer } from './TabContentRenderer';\n\nexport interface TradeAnalysisContainerProps {\n  /** Custom className */\n  className?: string;\n}\n\nconst AnalysisLayout = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '16px'};\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: ${({ theme }) => theme.spacing?.lg || '16px'};\n  min-height: 100vh;\n`;\n\nconst ContentArea = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '16px'};\n`;\n\nconst TabContentContainer = styled.div`\n  animation: fadeIn 0.3s ease-in-out;\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(10px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst ErrorFallback = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '24px'};\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  text-align: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst RetryButton = styled.button`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'}\n    ${({ theme }) => theme.spacing?.lg || '16px'};\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  color: white;\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};\n  cursor: pointer;\n  font-weight: ${({ theme }) => theme.fontWeights?.medium || '500'};\n  transition: ${({ theme }) => theme.transitions?.fast || 'all 0.2s ease'};\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n  }\n`;\n\n/**\n * Loading Fallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <div\n    style={{\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      height: '400px',\n      gap: '16px',\n    }}\n  >\n    <LoadingSpinner size='lg' />\n    <div style={{ color: 'var(--text-secondary)' }}>Loading Trade Analysis...</div>\n  </div>\n);\n\n/**\n * Analysis Content Component (uses context)\n */\nconst AnalysisContent: React.FC = () => {\n  const { data, isLoading, error, preferences, updatePreferences, fetchData } = useTradeAnalysis();\n\n  // Handle tab changes with preference persistence\n  const handleTabChange = (tab: string) => {\n    updatePreferences({ defaultView: tab as 'summary' | 'trades' | 'charts' });\n  };\n\n  if (error) {\n    return (\n      <ErrorFallback>\n        <div>❌ Analysis Loading Error</div>\n        <div>{error}</div>\n        <RetryButton onClick={fetchData}>Retry Analysis</RetryButton>\n      </ErrorFallback>\n    );\n  }\n\n  return (\n    <AnalysisLayout>\n      {/* F1 Racing Header */}\n      <AnalysisHeader onRefresh={fetchData} isRefreshing={isLoading} />\n\n      {/* Filter Panel */}\n      <FilterPanel />\n\n      {/* Tab Navigation */}\n      <AnalysisTabs\n        activeTab={preferences.defaultView || 'summary'}\n        onTabChange={handleTabChange}\n      />\n\n      {/* Tab Content */}\n      <ContentArea>\n        <Suspense fallback={<LoadingFallback />}>\n          <TabContentContainer>\n            <TabContentRenderer\n              activeTab={preferences.defaultView || 'summary'}\n              data={data}\n              isLoading={isLoading}\n              error={error}\n            />\n          </TabContentContainer>\n        </Suspense>\n      </ContentArea>\n    </AnalysisLayout>\n  );\n};\n\n/**\n * TradeAnalysisContainer Component\n *\n * Main container that provides layout management and error boundaries\n * for the refactored trade analysis. Follows TradingDashboard patterns.\n */\nexport const TradeAnalysisContainer: React.FC<TradeAnalysisContainerProps> = ({ className }) => {\n  return (\n    <div className={className}>\n      <Suspense fallback={<LoadingFallback />}>\n        <AnalysisContent />\n      </Suspense>\n    </div>\n  );\n};\n\nexport default TradeAnalysisContainer;\n", "/**\n * Trade Analysis Page\n *\n * REFACTORED: Now uses the proven TradingDashboard container pattern\n * for better performance, maintainability, and consistency.\n */\n\nimport React from 'react';\nimport { TradeAnalysisProvider } from './hooks/TradeAnalysisContext';\nimport { TradeAnalysisContainer } from './components/TradeAnalysisContainer';\n\n/**\n * Main Trade Analysis Component\n *\n * Simple wrapper that provides context and renders the container.\n * Follows the proven TradingDashboard architecture pattern.\n */\nconst TradeAnalysis: React.FC = () => {\n  return (\n    <TradeAnalysisProvider>\n      <TradeAnalysisContainer />\n    </TradeAnalysisProvider>\n  );\n};\n\nexport default TradeAnalysis;\n"], "names": ["sizeStyles", "small", "css", "theme", "spacing", "xxs", "xs", "fontSizes", "dot", "medium", "sm", "large", "md", "getVariantStyles", "variant", "solid", "outlined", "bgColor", "textColor", "borderColor", "colors", "primary", "textInverse", "secondary", "success", "warning", "error", "info", "textSecondary", "IconContainer", "span", "withConfig", "displayName", "componentId", "StartIcon", "styled", "EndIcon", "StyledBadge", "inline", "rounded", "borderRadius", "fontWeights", "size", "counter", "clickable", "transitions", "fast", "Badge", "children", "className", "style", "onClick", "startIcon", "endIcon", "max", "content", "undefined", "jsx", "spin", "keyframes", "LoadingSpinner", "div", "lg", "variantStyles", "textPrimary", "primaryDark", "secondaryDark", "outline", "text", "danger", "StyledButton", "button", "fullWidth", "$hasStartIcon", "$hasEndIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "disabled", "loading", "type", "rest", "jsxs", "custom", "props", "customHeight", "customWidth", "default", "background", "card", "surface", "shadows", "list", "Container", "Spinner", "Text", "LoadingPlaceholder", "height", "width", "showSpinner", "StyledTag", "pill", "RemoveButton", "sizeMap", "Tag", "removable", "onRemove", "handleRemoveClick", "e", "stopPropagation", "racingStripes", "StyledSpinner", "$size", "$variant", "$speed", "$showStripes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "speed", "showStripes", "LoadingSpinner$1", "Title", "h3", "semibold", "xl", "Description", "p", "compact", "ActionContainer", "ChildrenC<PERSON>r", "EmptyState", "title", "description", "icon", "actionText", "onAction", "HeaderActions", "DataCard", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "showRetry", "onRetry", "isEmpty", "emptyMessage", "emptyActionText", "onEmptyAction", "actionButton", "cardProps", "headerActions", "Card", "useLocalStorage", "key", "initialValue", "readValue", "window", "item", "localStorage", "getItem", "JSON", "parse", "warn", "storedValue", "setStoredValue", "useState", "setValue", "value", "valueToStore", "Function", "setItem", "stringify", "useEffect", "handleStorageChange", "event", "newValue", "addEventListener", "removeEventListener", "calculateRealPerformanceMetrics", "trades", "length", "getEmptyMetrics", "tradeRecords", "map", "t", "trade", "winningTrades", "filter", "achieved_pl", "losingTrades", "breakEvenTrades", "totalProfitLoss", "reduce", "sum", "totalWinAmount", "totalLossAmount", "Math", "abs", "averageWin", "averageLoss", "largestWin", "largestLoss", "min", "durations", "calculateTradeDurations", "averageDuration", "duration", "winRate", "profitFactor", "Infinity", "expectancy", "totalTrades", "breakeven", "round", "calculateCategoryPerformance", "category", "categories", "Map", "for<PERSON>ach", "tradeData", "categoryValue", "market", "model_type", "session", "setup", "direction", "has", "set", "get", "push", "performance", "categoryTrades", "averageProfitLoss", "profitLoss", "sort", "a", "b", "calculateTimePerformance", "timeType", "timeSlots", "timePerformance", "timeSlot", "tradeDate", "Date", "date", "slotIndex", "entry_time", "getTimeOfDaySlot", "dayOfWeek", "getDay", "getMonth", "slot", "index", "slotTrades", "winningSlotTrades", "generateEquityCurve", "sortedTrades", "getTime", "equityPoints", "runningBalance", "equity", "baseline", "balance", "tradeNumber", "generateDistributionData", "ranges", "label", "distribution", "range", "count", "percentage", "totalPnL", "isWin", "pnl", "rangeIndex", "findIndex", "bar", "exit_time", "entryTime", "exitTime", "timeString", "time", "hour", "getHours", "minute", "getMinutes", "timeValue", "PerformanceCacheManager", "cache", "config", "maxAge", "maxSize", "generate<PERSON>ache<PERSON>ey", "operation", "params", "tradeIds", "id", "join", "paramsStr", "getCached", "entry", "now", "timestamp", "delete", "data", "setCached", "oldest<PERSON>ey", "keys", "next", "hash", "clearCache", "clear", "clearExpiredCache", "entries", "getCacheStats", "oldestTimestamp", "values", "hitRate", "oldestEntry", "getCachedPerformanceMetrics", "calculator", "cache<PERSON>ey", "cached", "console", "log", "result", "getCachedCategoryPerformance", "getCachedTimePerformance", "getCachedEquityCurve", "getCachedDistributionData", "performanceCache", "batchCalculateAnalytics", "calculators", "metrics", "symbolPerformance", "strategyPerformance", "sessionPerformance", "setupPerformance", "directionPerformance", "timeOfDayPerformance", "dayOfWeekPerformance", "monthlyPerformance", "equityCurve", "distributionData", "Promise", "all", "performanceMetrics", "categoryPerformance", "performanceMonitor", "measureTime", "name", "fn", "start", "end", "toFixed", "logMemoryUsage", "context", "memory", "used", "usedJSHeapSize", "total", "totalJSHeapSize", "limit", "jsHeapSizeLimit", "fetchRealTradeAnalysisData", "filters", "tradeStorageService", "getAllTrades", "filteredTrades", "applyClientSideFilters", "analysisData", "generateAnalysisData", "Error", "message", "analyticsResults", "cacheStats", "convertToAnalysisTradeFormat", "timeframePerformance", "date<PERSON><PERSON><PERSON>", "lastUpdated", "toISOString", "symbols", "some", "toLowerCase", "includes", "symbol", "directions", "tradeDirection", "sessions", "tradeSession", "strategies", "statuses", "tradeStatus", "getTradeStatus", "tags", "toString", "entryPrice", "entry_price", "exitPrice", "exit_price", "quantity", "no_of_contracts", "status", "profitLossPercent", "calculateProfitLossPercent", "timeframe", "strategy", "rMultiple", "r_multiple", "notes", "patternQuality", "pattern_quality_rating", "do<PERSON><PERSON><PERSON><PERSON>", "dol_target", "rdType", "rd_type", "drawOnLiquidity", "draw_on_liquidity", "entryValue", "USE_REAL_DATA", "fetchTradeAnalysisData", "generateMockData", "generateMockTrades", "calculateMetrics", "startDate", "endDate", "daysDiff", "floor", "numTrades", "random", "timeframes", "i", "entryHour", "entryMinute", "setHours", "durationMinutes", "isBreakeven", "winPercent", "lossPercent", "numTags", "tradeTags", "j", "tag", "tEntryTime", "tHour", "tMinute", "tTimeValue", "getDefaultDateRange", "today", "setMonth", "split", "initialState", "preferences", "defaultDateRange", "defaultView", "chartTypes", "timeAnalysis", "tableColumns", "favoriteStrategies", "favoriteTags", "selectedTradeId", "tradeAnalysisReducer", "state", "action", "payload", "TradeAnalysisContext", "createContext", "TradeAnalysisProvider", "savedPreferences", "mergedInitialState", "dispatch", "useReducer", "fetchData", "useCallback", "updateFilters", "updatePreferences", "selectTrade", "tradeId", "resetFilters", "useTradeAnalysis", "useContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TitleSection", "h1", "xxl", "Subtitle", "ActionsSection", "RefreshButton", "isRefreshing", "StatusIndicator", "StatusDot", "AnalysisHeader", "onRefresh", "TabsContainer", "Tab", "active", "TabBadge", "TAB_CONFIG", "summary", "AnalysisTabs", "activeTab", "onTabChange", "handleTabClick", "tab", "Object", "tabKey", "badge", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveIndicator", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>lter<PERSON><PERSON>", "FilterGroup", "Filter<PERSON>abel", "DateRangeContainer", "DateInput", "input", "TagsContainer", "FilterTag", "selected", "ActionBar", "FilterStats", "ActionButtons", "ActionButton", "FilterPanel", "localFilters", "setLocalFilters", "availableSymbols", "Set", "availableStrategies", "availableTags", "flatMap", "directionOptions", "statusOptions", "timeframeOptions", "sessionOptions", "activeFilterCount", "Array", "isArray", "v", "handleDateChange", "field", "prev", "handleToggleFilter", "currentV<PERSON>ues", "newValues", "applyFilters", "handleResetFilters", "isSelected", "target", "MetricCard", "MetricLabel", "MetricValue", "positive", "negative", "profit", "loss", "MetricChange", "PerformanceSummary", "formatCurrency", "Intl", "NumberFormat", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "formatPercent", "TableHead", "thead", "TableRow", "tr", "TableHeaderCell", "th", "$active", "$sortable", "border", "SortIcon", "$direction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderText", "COLUMNS", "sortable", "TradesTableHeader", "sortField", "sortDirection", "onSort", "handleHeaderClick", "column<PERSON>ey", "handleKeyDown", "preventDefault", "column", "$isSelected", "TableCell", "td", "DirectionBadge", "StatusBadge", "ProfitLoss", "$value", "PriceRange", "SymbolCell", "DateCell", "TradesTableRow", "React", "memo", "formatters", "handlers", "formatDate", "getDirectionVariant", "getStatusVariant", "exit", "slice", "TableBody", "tbody", "EmptyRow", "EmptyCell", "TradesTableBody", "onRowClick", "useTradesTableData", "setSortField", "setSortDirection", "handleSort", "useMemo", "comparison", "localeCompare", "statusA", "statusB", "dateString", "isNaN", "toLocaleDateString", "month", "day", "year", "toLocaleTimeString", "hour12", "TableWrapper", "Table", "table", "EmptyIcon", "EmptyTitle", "EmptyDescription", "LoadingFallback", "TradesTableContent", "handleRowClick", "TradesTableContainer", "Suspense", "TradesTable", "BarC<PERSON>r", "Bar", "regular", "CategoryPerformanceChart", "performanceData", "sortedData", "maxProfitLoss", "ChartContainer", "TimeSlot", "TimeSlotHeader", "TimeSlotLabel", "TimeSlotMetrics", "BarLabel", "TimePerformanceChart", "DetailGrid", "DetailSection", "DetailLabel", "DetailValue", "Notes", "TradeDetail", "selected<PERSON><PERSON>", "find", "<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderContent", "Fragment", "textAlign", "padding", "color", "AnalysisLayout", "ContentArea", "TabContentContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RetryButton", "display", "flexDirection", "alignItems", "justifyContent", "gap", "AnalysisContent", "handleTabChange", "TradeAnalysisContainer", "TradeAnalysis"], "mappings": "iaAqDA,MAAMA,GAAa,CACjBC,MAAOC,EAAG,CAAA,WAAA,cAAA,8BAAA,GAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQC,OAAOF,EAAMC,QAAQE,KACnD,CAAC,CAAEH,MAAAA,CAAAA,IAAYA,EAAMI,UAAUD,GAE/B,CAAC,CAAEE,IAAAA,CAAAA,IAAWA,EAAM,MAAQ,MAAO,EAElDC,OAAQP,EAAG,CAAA,WAAA,cAAA,8BAAA,GAAA,EACE,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQE,MAAMH,EAAMC,QAAQM,KAClD,CAAC,CAAEP,MAAAA,CAAAA,IAAYA,EAAMI,UAAUG,GAE/B,CAAC,CAAEF,IAAAA,CAAAA,IAAWA,EAAM,OAAS,MAAO,EAEnDG,MAAOT,EAAG,CAAA,WAAA,cAAA,8BAAA,GAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQM,MAAMP,EAAMC,QAAQQ,KAClD,CAAC,CAAET,MAAAA,CAAAA,IAAYA,EAAMI,UAAUK,GAE/B,CAAC,CAAEJ,IAAAA,CAAAA,IAAWA,EAAM,OAAS,MAAO,CAErD,EAGMK,GAAmBA,CAACC,EAAuBC,EAAgBC,EAAoB,KAC5Ed,UACH,CAAC,CAAEC,MAAAA,CAAAA,IAAY,CAEf,IAAIc,EAASC,EAAWC,EAExB,OAAQL,EAAO,CACb,IAAK,UACHG,EAAUF,EAAQZ,EAAMiB,OAAOC,QAAU,GAAGlB,EAAMiB,OAAOC,YACzDH,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOC,QAC5DF,EAAchB,EAAMiB,OAAOC,QAC3B,MACF,IAAK,YACHJ,EAAUF,EAAQZ,EAAMiB,OAAOG,UAAY,GAAGpB,EAAMiB,OAAOG,cAC3DL,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOG,UAC5DJ,EAAchB,EAAMiB,OAAOG,UAC3B,MACF,IAAK,UACHN,EAAUF,EAAQZ,EAAMiB,OAAOI,QAAU,GAAGrB,EAAMiB,OAAOI,YACzDN,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOI,QAC5DL,EAAchB,EAAMiB,OAAOI,QAC3B,MACF,IAAK,UACHP,EAAUF,EAAQZ,EAAMiB,OAAOK,QAAU,GAAGtB,EAAMiB,OAAOK,YACzDP,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOK,QAC5DN,EAAchB,EAAMiB,OAAOK,QAC3B,MACF,IAAK,QACHR,EAAUF,EAAQZ,EAAMiB,OAAOM,MAAQ,GAAGvB,EAAMiB,OAAOM,UACvDR,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOM,MAC5DP,EAAchB,EAAMiB,OAAOM,MAC3B,MACF,IAAK,OACHT,EAAUF,EAAQZ,EAAMiB,OAAOO,KAAO,GAAGxB,EAAMiB,OAAOO,SACtDT,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOO,KAC5DR,EAAchB,EAAMiB,OAAOO,KAC3B,MACF,IAAK,UACHV,EAAUF,EAAQZ,EAAMiB,OAAOQ,cAAgB,GAAGzB,EAAMiB,OAAOQ,kBAC/DV,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOQ,cAC5DT,EAAchB,EAAMiB,OAAOQ,cAC3B,MACF,QACEX,EAAUF,EAAQZ,EAAMiB,OAAOQ,cAAgB,GAAGzB,EAAMiB,OAAOQ,kBAC/DV,EAAYH,EAAQZ,EAAMiB,OAAOE,YAAcnB,EAAMiB,OAAOQ,cAC5DT,EAAchB,EAAMiB,OAAOQ,aAC/B,CAEA,OAAIZ,EACK;AAAA;AAAA,mBAEIG;AAAAA,8BACWA;AAAAA,UAIjB;AAAA,4BACeF;AAAAA,iBACXC;AAAAA;AAAAA,OAAAA,CAGZ,EAICW,GAAuBC,EAAAA,KAAIC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAIhC,EAAA,CAAA,yDAAA,CAAA,EAEKC,GAAYC,EAAON,EAAa,EAACE,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gBAAA,GAAA,EACrB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAAG,EAG5C+B,GAAUD,EAAON,EAAa,EAACE,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,eAAA,GAAA,EACpB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAAG,EAG3CgC,GAAqBP,EAAAA,KAAIC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4DAAA,gBAAA,uBAAA,IAAA,IAAA,IAAA,IAAA,EAAA,EAWlB,CAAC,CAAEK,OAAAA,CAAO,IAAOA,EAAS,cAAgB,OAGpC,CAAC,CAAEnC,MAAAA,EAAOoC,QAAAA,EAAS/B,IAAAA,CAAI,IACtCA,EAAM,MAAQ+B,EAAU,SAAWpC,EAAMqC,aAAa9B,GACzC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,OAI9C,CAAC,CAAEiC,KAAAA,CAAK,IAAM1C,GAAW0C,CAAI,EAG7B,CAAC,CAAE5B,QAAAA,EAASC,MAAAA,EAAOC,SAAAA,CAAS,IAAMH,GAAiBC,EAASC,EAAOC,GAAY,EAAK,EAGpF,CAAC,CAAER,IAAAA,CAAI,IACPA,GACAN,EAAG,CAAA,iCAAA,CAAA,EAOH,CAAC,CAAEyC,QAAAA,CAAQ,IACXA,GACAzC,EAAG,CAAA,iEAAA,CAAA,EAQH,CAAC,CAAE0C,UAAAA,CAAU,IACbA,GACA1C,wFAEwB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM0C,YAAYC,IAAI,CAS5D,EAQQC,GAA8BA,CAAC,CAC1CC,SAAAA,EACAlC,QAAAA,EAAU,UACV4B,KAAAA,EAAO,SACP3B,MAAAA,EAAQ,GACRkC,UAAAA,EAAY,GACZC,MAAAA,EACAC,QAAAA,EACAZ,QAAAA,EAAU,GACV/B,IAAAA,EAAM,GACNmC,QAAAA,EAAU,GACV3B,SAAAA,EAAW,GACXoC,UAAAA,EACAC,QAAAA,EACAC,IAAAA,EACAhB,OAAAA,EAAS,EACX,IAAM,CAEJ,IAAIiB,EAAUP,EACd,OAAIL,GAAW,OAAOK,GAAa,UAAYM,IAAQE,QAAaR,EAAWM,IAC7EC,EAAU,GAAGD,YAIZjB,GACC,CAAA,QAAAvB,EACA,KAAA4B,EACA,MAAA3B,EACA,UAAW,CAAC,CAACoC,EACb,UAAAF,EACA,MAAAC,EACA,QAAAC,EACA,QAAAZ,EACA,IAAA/B,EACA,QAAAmC,EACA,SAAA3B,EACA,OAAAsB,EAEC,SAAA,CAAC9B,GAEG4C,EAAAA,KAAAA,EAAAA,SAAAA,CAAAA,SAAAA,CAAaA,GAAAK,EAAAA,IAACvB,IAAWkB,SAAUA,CAAA,CAAA,EACnCG,EACAF,GAAYI,EAAAA,IAAArB,GAAA,CAASiB,SAAQA,CAAA,CAAA,CAAA,CAChC,CAAA,CAEJ,CAAA,CAEJ,ECtOMK,GAAOC,GAGZ,CAAA,4DAAA,CAAA,EAEKC,GAAwBC,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAMlByB,EAAAA,CAAAA,mHAAAA,sCAAAA,GAAAA,EAAAA,GACG,CAAC,CAAEvD,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAI3CN,GAAa,CACjBC,MAAOC,EAAG,CAAA,WAAA,cAAA,mBAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQC,OAAOF,EAAMC,QAAQM,KACnD,CAAC,CAAEP,MAAAA,CAAAA,IAAYA,EAAMI,UAAUD,EAAE,EAGhDG,OAAQP,EAAG,CAAA,WAAA,cAAA,mBAAA,EACE,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQE,MAAMH,EAAMC,QAAQQ,KAClD,CAAC,CAAET,MAAAA,CAAAA,IAAYA,EAAMI,UAAUG,EAAE,EAGhDC,MAAOT,EAAG,CAAA,WAAA,cAAA,mBAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQM,MAAMP,EAAMC,QAAQ0D,KAClD,CAAC,CAAE3D,MAAAA,CAAAA,IAAYA,EAAMI,UAAUK,EAAE,CAGlD,EAGMmD,GAAgB,CACpB1C,QAASnB,EAAG,CAAA,oBAAA,UAAA,wDAAA,8GAAA,4CAAA,EACU,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QACvC,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO4C,aAAe7D,EAAMiB,OAAOE,aAAe,OAI1D,CAAC,CAAEnB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO6C,YAM5B,CAAC,CAAE9D,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO6C,WAAW,EAK/D1C,UAAWrB,EAAG,CAAA,oBAAA,UAAA,wDAAA,8GAAA,4CAAA,EACQ,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOG,UACvC,CAAC,CAAEpB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO4C,aAAe7D,EAAMiB,OAAOE,aAAe,OAI1D,CAAC,CAAEnB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO8C,cAM5B,CAAC,CAAE/D,MAAAA,CAAAA,IAAYA,EAAMiB,OAAO8C,aAAa,EAKjEC,QAASjE,EAAG,CAAA,sCAAA,qBAAA,4CAAA,2EAAA,8BAAA,EAED,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QACjB,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QAG1B,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QAK5B,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,OAAO,EAI3D+C,KAAMlE,EAAG,CAAA,sCAAA,6BAAA,kBAAA,4CAAA,gDAAA,MAAA,EAEE,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QAErB,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMC,QAAQE,GAC5B,CAAC,CAAEH,MAAAA,CAAAA,IAAYA,EAAMC,QAAQE,GAGxB,CAAC,CAAEH,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,QAI5B,CAAC,CAAElB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOC,OAAO,EAG3DG,QAAStB,EAAG,CAAA,oBAAA,UAAA,wDAAA,uIAAA,EACU,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOI,QACvC,CAAC,CAAErB,MAAAA,CAAYA,IAAAA,EAAMiB,OAAOE,aAAe,OAI9B,CAAC,CAAEnB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOI,OAAO,EAU3D6C,OAAQnE,EAAG,CAAA,oBAAA,UAAA,wDAAA,uIAAA,EACW,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOM,MACvC,CAAC,CAAEvB,MAAAA,CAAYA,IAAAA,EAAMiB,OAAOE,aAAe,OAI9B,CAAC,CAAEnB,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOM,KAAK,CAU3D,EAYM4C,GAAsBC,EAAAA,OAAMxC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,gBAAA,kCAAA,sCAAA,IAAA,IAAA,uFAAA,IAAA,EAAA,EAIf,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMqC,aAAa9B,GACpC,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMsC,cAANtC,YAAAA,EAAmBM,SAAU,KAEzC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM0C,cAAN1C,YAAAA,EAAmB2C,OAAQ,aAK1D,CAAC,CAAEJ,KAAAA,EAAO,QAAS,IAAM1C,GAAW0C,CAAI,EAGxC,CAAC,CAAE5B,QAAAA,EAAU,SAAU,IAAMiD,GAAcjD,CAAO,EAGlD,CAAC,CAAE0D,UAAAA,CAAU,IACbA,GACAtE,EAAG,CAAA,aAAA,CAAA,EAaH,CAAC,CAAEuE,cAAAA,CAAc,IACjBA,GACAvE,2CAEoB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAInD,CAAC,CAAEoE,YAAAA,CAAY,IACfA,GACAxE,yCAEmB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,CAEjD,EAGCqE,GAAuBd,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAI/B,EAAA,CAAA,yDAAA,CAAA,EAOY2C,GAAgCA,CAAC,CAC5C5B,SAAAA,EACAlC,QAAAA,EAAU,UACV+D,SAAAA,EAAW,GACXC,QAAAA,EAAU,GACVpC,KAAAA,EAAO,SACP8B,UAAAA,EAAY,GACZpB,UAAAA,EACAC,QAAAA,EACAF,QAAAA,EACAF,UAAAA,EAAY,GACZ8B,KAAAA,EAAO,SACP,GAAGC,CACL,IAEIvB,EAAA,IAACa,GACC,CAAA,QAAAxD,EACA,SAAU+D,GAAYC,EACtB,KAAApC,EACA,UAAA8B,EACA,QAAArB,EACA,UAAAF,EACA,KAAA8B,EACA,cAAe,CAAC,CAAC3B,GAAa,CAAC0B,EAC/B,YAAa,CAAC,CAACzB,GAAW,CAACyB,EAC3B,GAAIE,EAEJ,SAAAC,EAAA,KAACN,GACEG,CAAAA,SAAAA,CAAAA,SAAYlB,GAAiB,EAAA,EAC7B,CAACkB,GAAW1B,EACZJ,EACA,CAAC8B,GAAWzB,CAAAA,CACf,CAAA,CACF,CAAA,ECtPErD,GAAa,CACjBC,MAAOC,EAEN,CAAA,eAAA,CAAA,EACDO,OAAQP,EAEP,CAAA,eAAA,CAAA,EACDS,MAAOT,EAEN,CAAA,eAAA,CAAA,EACDgF,OAASC,GAA0DjF,EAAG,CAAA,UAAA,UAAA,GAAA,EAC1DiF,EAAMC,aACPD,EAAME,aAAe,MAAM,CAExC,EAGMtB,GAAgB,CACpBuB,QAASpF,EAAG,CAAA,oBAAA,kBAAA,GAAA,EACU,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOmE,WAC/B,CAAC,CAAEpF,MAAAA,CAAAA,IAAYA,EAAMqC,aAAa5B,EAAE,EAEvD4E,KAAMtF,EAAG,CAAA,oBAAA,kBAAA,eAAA,GAAA,EACa,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOqE,QAC/B,CAAC,CAAEtF,MAAAA,CAAAA,IAAYA,EAAMqC,aAAa5B,GACrC,CAAC,CAAET,MAAAA,CAAAA,IAAYA,EAAMuF,QAAQhF,EAAE,EAE/C0D,KAAMlE,EAIL,CAAA,uEAAA,CAAA,EACDyF,KAAMzF,EAAG,CAAA,oBAAA,kBAAA,kBAAA,GAAA,EACa,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOmE,WAC/B,CAAC,CAAEpF,MAAAA,CAAAA,IAAYA,EAAMqC,aAAa9B,GAClC,CAAC,CAAEP,MAAAA,CAAAA,IAAYA,EAAMC,QAAQM,EAAE,CAEpD,EAGMgD,GAAOC,GAOZ,CAAA,4DAAA,CAAA,EAEKiC,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gFAAA,IAAA,EAAA,EAYxB,CAAC,CAAES,KAAAA,EAAM0C,aAAAA,EAAcC,YAAAA,CAAY,IAC/B3C,IAAS,SACJ1C,GAAWkF,OAAO,CAAEE,aAAAA,EAAcC,YAAAA,CAAAA,CAAa,EAEjDrF,GAAW0C,CAAI,EAItB,CAAC,CAAE5B,QAAAA,CAAQ,IAAMiD,GAAcjD,CAAO,CAAC,EAGrC+E,GAAiBhC,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2CAAA,yBAAA,gCAAA,qCAAA,GAAA,EAGJ,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,WACxB,CAAC,CAAEpF,MAAAA,CAAM,IAAMA,EAAMiB,OAAOC,QAEvCqC,GACI,CAAC,CAAEvD,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAG5CoF,GAAcjC,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,OAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,cAAA,GAAA,EACZ,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,cACxB,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMI,UAAUG,EAAE,EAQnCqF,GAAwDA,CAAC,CACpEjF,QAAAA,EAAU,UACV4B,KAAAA,EAAO,SACPsD,OAAAA,EAAS,QACTC,MAAAA,EAAQ,GACR7B,KAAAA,EAAO,aACP8B,YAAAA,EAAc,GACdjD,UAAAA,EAAY,EACd,IAEIgC,OAACW,IACC,QAAA9E,EACA,KAAA4B,EACA,aAAcsD,EACd,YAAaC,EACb,UAAAhD,EAECiD,SAAAA,CAAAA,SAAgBL,GAAU,EAAA,EAC1BzB,GAASX,EAAAA,IAAAqC,GAAA,CAAM1B,SAAKA,CAAA,CAAA,CACvB,CAAA,CAAA,ECxGEpE,GAAa,CACjBC,MAAOC,EAAG,CAAA,WAAA,cAAA,GAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQC,OAAOF,EAAMC,QAAQE,KACnD,CAAC,CAAEH,MAAAA,CAAAA,IAAYA,EAAMI,UAAUD,EAAE,EAEhDG,OAAQP,EAAG,CAAA,WAAA,cAAA,GAAA,EACE,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQE,MAAMH,EAAMC,QAAQM,KAClD,CAAC,CAAEP,MAAAA,CAAAA,IAAYA,EAAMI,UAAUG,EAAE,EAEhDC,MAAOT,EAAG,CAAA,WAAA,cAAA,GAAA,EACG,CAAC,CAAEC,MAAAA,CAAAA,IAAY,GAAGA,EAAMC,QAAQM,MAAMP,EAAMC,QAAQQ,KAClD,CAAC,CAAET,MAAAA,CAAAA,IAAYA,EAAMI,UAAUK,EAAE,CAElD,EAGMC,GAAoBC,GACjBZ,UACH,CAAC,CAAEC,MAAAA,CAAAA,IAAY,CAEf,IAAIc,EAASC,EAAWC,EAExB,OAAQL,EAAO,CACb,IAAK,UACOG,EAAA,GAAGd,EAAMiB,OAAOC,YAC1BH,EAAYf,EAAMiB,OAAOC,QACXF,EAAA,GAAGhB,EAAMiB,OAAOC,YAC9B,MACF,IAAK,YACOJ,EAAA,GAAGd,EAAMiB,OAAOG,cAC1BL,EAAYf,EAAMiB,OAAOG,UACXJ,EAAA,GAAGhB,EAAMiB,OAAOG,cAC9B,MACF,IAAK,UACON,EAAA,GAAGd,EAAMiB,OAAOI,YAC1BN,EAAYf,EAAMiB,OAAOI,QACXL,EAAA,GAAGhB,EAAMiB,OAAOI,YAC9B,MACF,IAAK,UACOP,EAAA,GAAGd,EAAMiB,OAAOK,YAC1BP,EAAYf,EAAMiB,OAAOK,QACXN,EAAA,GAAGhB,EAAMiB,OAAOK,YAC9B,MACF,IAAK,QACOR,EAAA,GAAGd,EAAMiB,OAAOM,UAC1BR,EAAYf,EAAMiB,OAAOM,MACXP,EAAA,GAAGhB,EAAMiB,OAAOM,UAC9B,MACF,IAAK,OACOT,EAAA,GAAGd,EAAMiB,OAAOO,SAC1BT,EAAYf,EAAMiB,OAAOO,KACXR,EAAA,GAAGhB,EAAMiB,OAAOO,SAC9B,MACF,QACYV,EAAA,GAAGd,EAAMiB,OAAOQ,kBAC1BV,EAAYf,EAAMiB,OAAOQ,cACXT,EAAA,GAAGhB,EAAMiB,OAAOQ,iBAClC,CAEO,MAAA;AAAA,4BACeX;AAAAA,iBACXC;AAAAA,4BACWC;AAAAA,OAAAA,CAEvB,EAICgF,GAAmBrE,EAAAA,KAAIC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wDAAA,gBAAA,IAAA,IAAA,IAAA,EAAA,EAOV,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMqC,aAAa4D,KACpC,CAAC,CAAEjG,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,OAG9C,CAAC,CAAEiC,KAAAA,CAAK,IAAM1C,GAAW0C,CAAI,EAG7B,CAAC,CAAE5B,QAAAA,CAAQ,IAAMD,GAAiBC,CAAO,EAGzC,CAAC,CAAE8B,UAAAA,CAAU,IACbA,GACA1C,wFAEwB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM0C,YAAYC,IAAI,CAS5D,EAGCuD,GAAsB9B,EAAAA,OAAMxC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kJAAA,cAAA,sBAAA,EASjB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,GAI1C,CAAC,CAAEoC,KAAAA,EAAMvC,MAAAA,CAAM,IAAM,CACrB,MAAMmG,EAAU,CACdrG,MAAO,OACPQ,OAAQ,OACRE,MAAO,MAAA,EAGF,MAAA;AAAA,eACI2F,EAAQ5D,CAAI;AAAA,gBACX4D,EAAQ5D,CAAI;AAAA,mBACTvC,EAAMI,UAAUD;AAAAA,KAEjC,CAAC,EAYUiG,GAA0BA,CAAC,CACtCvD,SAAAA,EACAlC,QAAAA,EAAU,UACV4B,KAAAA,EAAO,SACP8D,UAAAA,EAAY,GACZC,SAAAA,EACAxD,UAAAA,EAAY,GACZE,QAAAA,CACF,IAAM,CACEuD,MAAAA,EAAqBC,GAAwB,CACjDA,EAAEC,gBAAgB,EACPH,GAAA,MAAAA,GAAA,EAIX,OAAAxB,OAACkB,IACC,QAAArF,EACA,KAAA4B,EACA,UAAW,CAAC,CAACS,EACb,UAAAF,EACA,QAAAE,EAECH,SAAAA,CAAAA,EACAwD,GACE/C,EAAAA,IAAA4C,GAAA,CAAa,KAAA3D,EAAY,QAASgE,EAAkB,SAErD,IAAA,CAEJ,CAAA,CAAA,CAEJ,ECxKMhD,GAAOC,GAOZ,CAAA,4DAAA,CAAA,EAGKkD,GAAgBlD,GAOrB,CAAA,oEAAA,CAAA,EASKmD,GAAuBjD,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,wHAAA,cAAA,IAAA,sBAAA,EAAA,EAI5B,CAAC,CAAE8E,MAAAA,CAAM,IAAM,CACf,OAAQA,EAAK,CACX,IAAK,KACI7G,OAAAA,EAAG,CAAA,yBAAA,CAAA,EAIZ,IAAK,KACIA,OAAAA,EAAG,CAAA,yBAAA,CAAA,EAIZ,IAAK,KACIA,OAAAA,EAAG,CAAA,yBAAA,CAAA,EAIZ,IAAK,KACIA,OAAAA,EAAG,CAAA,yBAAA,CAAA,EAIZ,IAAK,KACIA,OAAAA,EAAG,CAAA,yBAAA,CAAA,EAIZ,QACSA,OAAAA,EAAG,CAAA,yBAAA,CAAA,CAId,CACF,EAYI,CAAC,CAAE8G,SAAAA,EAAU7G,MAAAA,CAAM,IAAM,iBACzB,OAAQ6G,EAAQ,CACd,IAAK,UACH,OAAO9G,EACeC,CAAAA,oBAAAA,uBAAAA,GAAAA,IAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,YACvBlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,SAAS,EAE5D,IAAK,YACH,OAAOnB,EACeC,CAAAA,oBAAAA,uBAAAA,GAAAA,IAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,YAC7BzB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,SAAS,EAElE,IAAK,QACI1B,OAAAA,EAAG,CAAA,sDAAA,CAAA,EAIZ,IAAK,MACIA,OAAAA,EAAG,CAAA,sDAAA,CAAA,EAIZ,QACE,OAAOA,EACeC,CAAAA,oBAAAA,uBAAAA,GAAAA,IAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,YACvBlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,SAAS,CAE9D,CACF,EAEaqC,GAAQ,CAAC,CAAEuD,OAAAA,CAAO,IAAM,EAAIA,EAGzC,CAAC,CAAEC,aAAAA,EAAcF,SAAAA,CAAS,IAC1BE,GACAhH,EASkB8G,CAAAA,4GAAAA,sCAAAA,IAAAA,qBAAAA,EAAAA,IAAa,OAASA,IAAa,UAC7C,gKACA,sKAESH,GAAkB1B,GAAe,EAAIA,EAAM8B,MAAM,CAEjE,EAGCE,GAA0BtD,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAIlC,EAAA,CAAA,gEAAA,CAAA,EAyBY2B,GAA2DuB,GAAA,CAChE,KAAA,CACJzC,KAAAA,EAAO,KACP5B,QAAAA,EAAU,UACVmC,UAAAA,EACA,aAAcmE,EACdC,MAAAA,EAAQ,EACRC,YAAAA,EAAc,EACZnC,EAAAA,EAEF,OAAA1B,MAAC0D,IAAiB,UAAAlE,EAChB,SAAAQ,EAAAA,IAACqD,IACC,MAAOpE,EACP,SAAU5B,EACV,OAAQuG,EACR,aAAcC,EACd,KAAK,SACL,aAAYF,GAAa,UACzB,YAAU,QAAQ,CAAA,CAEtB,CAAA,CAEJ,EAEAG,GAAe3D,GCvLT4D,GAAeC,EAAAA,GAAE1F,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,gBAAA,IAAA,EAAA,EACP,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAClC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMiB,OAAO4C,YACtB,CAAC,CAAE7D,MAAAA,CAAM,IAAMA,EAAMsC,YAAYiF,SAE9C,CAAC,CAAEhF,KAAAA,EAAMvC,MAAAA,CAAM,IAAM,CACrB,MAAMmG,EAAU,CACdrG,MAAOE,EAAMI,UAAUK,GACvBH,OAAQN,EAAMI,UAAUuD,GACxBnD,MAAOR,EAAMI,UAAUoH,EAAAA,EAGzB,OAAOzH,EAAG,CAAA,aAAA,GAAA,EACKoG,EAAQ5D,CAAI,CAAC,CAE9B,CAAC,EAGGkF,GAAqBC,EAAAA,EAAC9F,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,EAAA,EACZ,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQ0D,GAClC,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,cAEnC,CAAC,CAAEc,KAAAA,EAAMvC,MAAAA,CAAM,IAAM,CACrB,MAAMmG,EAAU,CACdrG,MAAOE,EAAMI,UAAUG,GACvBD,OAAQN,EAAMI,UAAUK,GACxBD,MAAOR,EAAMI,UAAUuD,EAAAA,EAGzB,OAAO5D,EAAG,CAAA,aAAA,GAAA,EACKoG,EAAQ5D,CAAI,CAAC,CAE9B,CAAC,EAMGqB,GAAgB,CACpBuB,QAASpF,EAER,CAAA,+BAAA,CAAA,EACD4H,QAAS5H,EAIR,CAAA,sEAAA,CAAA,EACDsF,KAAMtF,EAAG,CAAA,oBAAA,kBAAA,eAAA,GAAA,EACa,CAAC,CAAEC,MAAAA,CAAAA,IAAYA,EAAMiB,OAAOqE,QAC/B,CAAC,CAAEtF,MAAAA,CAAAA,IAAYA,EAAMqC,aAAa5B,GACrC,CAAC,CAAET,MAAAA,CAAAA,IAAYA,EAAMuF,QAAQhF,EAAE,CAEjD,EAEMkF,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6GAAA,IAAA,EAAA,EAYxB,CAAC,CAAEnB,QAAAA,CAAQ,IAAMiD,GAAcjD,CAAO,EAGtC,CAAC,CAAE4B,KAAAA,EAAMvC,MAAAA,CAAM,IAAM,CACrB,OAAQuC,EAAI,CACV,IAAK,QACH,OAAOxC,EACMC,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMC,QAAQQ,EAAE,EAG/B,IAAK,QACH,OAAOV,EACMC,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMC,QAAQuH,EAAE,EAG/B,QACE,OAAOzH,EACMC,CAAAA,WAAAA,oBAAAA,EAAAA,EAAMC,QAAQ0D,EAAE,CAGjC,CACF,CAAC,EAGGjC,GAAuBgC,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,IAAA,EAAA,EACb,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GAE5C,CAAC,CAAE8B,KAAAA,EAAMvC,MAAAA,CAAM,IAAM,CACrB,MAAMmG,EAAU,CACdrG,MAAO,OACPQ,OAAQ,OACRE,MAAO,MAAA,EAGT,OAAOT,yDACQoG,EAAQ5D,CAAI,EAGd4D,EAAQ5D,CAAI,EACX4D,EAAQ5D,CAAI,EACbvC,EAAMiB,OAAOQ,aAAa,CAGzC,CAAC,EAKGmG,GAAyBlE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EAClB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAGzCoH,GAA2BnE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,cAAA,EACpB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQ0D,EAAE,EASlCmE,GAAwCA,CAAC,CACpDC,MAAAA,EAAQ,GACRC,YAAAA,EAAc,GACdC,KAAAA,EACAC,WAAAA,EAAa,GACbC,SAAAA,EACAxH,QAAAA,EAAU,UACV4B,KAAAA,EAAO,SACPO,UAAAA,EAAY,GACZD,SAAAA,CACF,IAEKiC,EAAAA,KAAAW,GAAA,CAAU,QAAA9E,EAAkB,KAAA4B,EAAY,UAAAO,EACtCmF,SAAAA,CAAQA,GAAA3E,EAAA,IAAC5B,GAAc,CAAA,KAAAa,EAAa0F,SAAKA,EAAA,EAEzCF,GAASzE,EAAA,IAAC+D,GAAM,CAAA,KAAA9E,EAAawF,SAAMA,EAAA,EACnCC,GAAe1E,EAAA,IAACmE,GAAY,CAAA,KAAAlF,EAAayF,SAAYA,EAAA,EAErDE,GAAcC,GACb7E,EAAA,IAACsE,GACC,CAAA,SAAAtE,MAACmB,IAAO,QAAQ,UAAU,KAAMlC,IAAS,QAAU,QAAU,SAAU,QAAS4F,EAC7ED,UACH,CAAA,EACF,EAGDrF,GAAaS,EAAA,IAAAuE,GAAA,CAAmBhF,SAAAA,CAAS,CAAA,CAC5C,CAAA,CAAA,ECpJEuF,GAAuB1E,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAEvB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAQ3B8H,EAAoCA,CAAC,CAChDN,MAAAA,EACAlF,SAAAA,EACAyF,UAAAA,EAAY,GACZC,SAAAA,EAAW,GACXC,aAAAA,EAAe,uCACfC,UAAAA,EAAY,GACZC,QAAAA,EACAC,QAAAA,EAAU,GACVC,aAAAA,EAAe,oBACfC,gBAAAA,EACAC,cAAAA,EACAC,aAAAA,EACAjG,UAAAA,EACA,GAAGkG,CACL,IAAM,CAEEC,MAAAA,EAAiB3F,EAAAA,IAAA8E,GAAA,CAAeW,SAAaA,CAAA,CAAA,EAG/C3F,IAAAA,EAEJ,OAAIkF,EACFlF,EAAWE,EAAAA,IAAAsC,GAAA,CAAmB,QAAQ,OAAO,KAAK,iBAAoB,CAAA,EAC7D2C,EACTnF,EACGE,EAAAA,IAAAwE,GAAA,CACC,MAAM,QACN,YAAaU,EACb,QAAQ,UACR,WAAYC,EAAY,QAAUpF,OAClC,SAAUoF,EAAYC,EAAUrF,MAEnC,CAAA,EACQsF,EAEPvF,EAAAE,EAAA,IAACwE,GACC,CAAA,MAAM,UACN,YAAac,EACb,QAAQ,UACR,WAAYC,EACZ,SAAUC,CAEb,CAAA,EAESjG,EAAAA,EAIVS,MAAC4F,IAAK,MAAAnB,EAAc,QAASkB,EAAe,UAAAnG,EAA0BkG,GAAAA,EACnE5F,SACHA,CAAA,CAAA,CAEJ,EC1FgB+F,SAAAA,GACdC,EACAC,EAC2C,CAG3C,MAAMC,EAAYA,IAAS,CAErB,GAAA,OAAOC,OAAW,IACbF,OAAAA,EAGL,GAAA,CACF,MAAMG,EAAOD,OAAOE,aAAaC,QAAQN,CAAG,EAC5C,OAAOI,EAAQG,KAAKC,MAAMJ,CAAI,EAAUH,QACjC9H,GACCsI,eAAAA,KAAK,mCAAmCT,MAAS7H,CAAK,EACvD8H,CACT,CAAA,EAKI,CAACS,EAAaC,CAAc,EAAIC,WAAYV,CAAS,EAIrDW,EAAYC,GAA+B,CAC3C,GAAA,CAEF,MAAMC,EACJD,aAAiBE,SAAWF,EAAMJ,CAAW,EAAII,EAGnDH,EAAeI,CAAY,EAGvB,OAAOZ,OAAW,KACpBA,OAAOE,aAAaY,QAAQjB,EAAKO,KAAKW,UAAUH,CAAY,CAAC,QAExD5I,GACCsI,QAAAA,KAAK,mCAAmCT,MAAS7H,CAAK,CAChE,CAAA,EAIFgJ,OAAAA,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAuBC,GAAwB,CAC/CA,EAAMrB,MAAQA,GAAOqB,EAAMC,UAC7BX,EAAeJ,KAAKC,MAAMa,EAAMC,QAAQ,CAAM,CAChD,EAIKC,cAAAA,iBAAiB,UAAWH,CAAmB,EAG/C,IAAMjB,OAAOqB,oBAAoB,UAAWJ,CAAmB,CAAA,EACrE,CAACpB,CAAG,CAAC,EAED,CAACU,EAAaG,CAAQ,CAC/B,CCxDaY,MAAAA,GACXC,GACuB,CACnBA,GAAAA,EAAOC,SAAW,EACpB,OAAOC,GAAgB,EAIzB,MAAMC,EAAeH,EAAOI,IAAIC,GAAKA,EAAEC,KAAK,EAGtCC,EAAgBJ,EAAaK,WAAiBF,EAAMG,aAAe,GAAK,CAAC,EACzEC,EAAeP,EAAaK,WAAiBF,EAAMG,aAAe,GAAK,CAAC,EACxEE,EAAkBR,EAAaK,WAAiBF,EAAMG,aAAe,KAAO,CAAC,EAG7EG,EAAkBT,EAAaU,OAAO,CAACC,EAAKR,IAAUQ,GAAOR,EAAMG,aAAe,GAAI,CAAC,EACvFM,EAAiBR,EAAcM,OAAO,CAACC,EAAKR,IAAUQ,GAAOR,EAAMG,aAAe,GAAI,CAAC,EACvFO,EAAkBC,KAAKC,IAC3BR,EAAaG,OAAO,CAACC,EAAKR,IAAUQ,GAAOR,EAAMG,aAAe,GAAI,CAAC,CACvE,EAGMU,EAAaZ,EAAcN,OAAS,EAAIc,EAAiBR,EAAcN,OAAS,EAChFmB,EAAcV,EAAaT,OAAS,EAAIe,EAAkBN,EAAaT,OAAS,EAGhFoB,EACJd,EAAcN,OAAS,EAAIgB,KAAK5I,IAAI,GAAGkI,EAAcH,IAAaE,GAAAA,EAAMG,aAAe,CAAC,CAAC,EAAI,EACzFa,EACJZ,EAAaT,OAAS,EAAIgB,KAAKM,IAAI,GAAGb,EAAaN,IAAaE,GAAAA,EAAMG,aAAe,CAAC,CAAC,EAAI,EAGvFe,EAAYC,GAAwBtB,CAAY,EAChDuB,EACJF,EAAUvB,OAAS,EACfuB,EAAUX,OAAO,CAACC,EAAKa,IAAab,EAAMa,EAAU,CAAC,EAAIH,EAAUvB,OACnE,EAGA2B,EAAU5B,EAAOC,OAAS,EAAIM,EAAcN,OAASD,EAAOC,OAAS,EACrE4B,EACJb,EAAkB,EAAID,EAAiBC,EAAkBD,EAAiB,EAAIe,IAAW,EACrFC,EAAaH,EAAUT,GAAc,EAAIS,GAAWR,EAanD,MAAA,CACLY,YAAahC,EAAOC,OACpBM,cAAeA,EAAcN,OAC7BS,aAAcA,EAAaT,OAC3BgC,UAAWtB,EAAgBV,OAC3B2B,QAASX,KAAKiB,MAAMN,EAAU,GAAK,EAAI,IACvCT,WAAYF,KAAKiB,MAAMf,EAAa,GAAG,EAAI,IAC3CC,YAAaH,KAAKiB,MAAMd,EAAc,GAAG,EAAI,IAC7CS,aAAcZ,KAAKiB,MAAML,EAAe,GAAG,EAAI,IAC/CjB,gBAAiBK,KAAKiB,MAAMtB,EAAkB,GAAG,EAAI,IACrDS,WAAYJ,KAAKiB,MAAMb,EAAa,GAAG,EAAI,IAC3CC,YAAaL,KAAKiB,MAAMZ,EAAc,GAAG,EAAI,IAC7CI,gBAAiBT,KAAKiB,MAAMR,EAAkB,GAAG,EAAI,IACrDK,WAAYd,KAAKiB,MAAMH,EAAa,GAAG,EAAI,GAAA,CAO/C,EAKaI,GAA+BA,CAC1CnC,EACAoC,IAC0B,CAC1B,GAAIpC,EAAOC,SAAW,EAAG,MAAO,GAG1BoC,MAAAA,MAAiBC,IAEvBtC,EAAOuC,QAAqBC,GAAA,CAC1B,MAAMlC,EAAQkC,EAAUlC,MACpBmC,IAAAA,EAEJ,OAAQL,EAAQ,CACd,IAAK,SACHK,EAAgBnC,EAAMoC,QAAU,UAChC,MACF,IAAK,aACHD,EAAgBnC,EAAMqC,YAAc,UACpC,MACF,IAAK,UACHF,EAAgBnC,EAAMsC,SAAW,UACjC,MACF,IAAK,QACHH,EAAgBnC,EAAMuC,OAAS,UAC/B,MACF,IAAK,YACHJ,EAAgBnC,EAAMwC,WAAa,UACnC,MACF,QACkBL,EAAA,SACpB,CAEKJ,EAAWU,IAAIN,CAAa,GACpBO,EAAAA,IAAIP,EAAe,CAAA,CAAE,EAElCJ,EAAWY,IAAIR,CAAa,EAAGS,KAAKV,CAAS,CAAA,CAC9C,EAGD,MAAMW,EAAqC,CAAA,EAEhCZ,OAAAA,EAAAA,QAAQ,CAACa,EAAgBX,IAAkB,CACpD,MAAMtC,EAAeiD,EAAehD,IAAIC,GAAKA,EAAEC,KAAK,EAC9CC,EAAgBJ,EAAaK,WAAiBF,EAAMG,aAAe,GAAK,CAAC,EACzEG,EAAkBT,EAAaU,OAAO,CAACC,EAAKR,IAAUQ,GAAOR,EAAMG,aAAe,GAAI,CAAC,EACvFmB,EAAUwB,EAAenD,OAAS,EAAIM,EAAcN,OAASmD,EAAenD,OAAS,EACrFoD,EACJD,EAAenD,OAAS,EAAIW,EAAkBwC,EAAenD,OAAS,EAExEkD,EAAYD,KAAK,CACfd,SAAAA,EACAhD,MAAOqD,EACPzC,OAAQoD,EAAenD,OACvB2B,QAASX,KAAKiB,MAAMN,EAAU,GAAK,EAAI,IACvC0B,WAAYrC,KAAKiB,MAAMtB,EAAkB,GAAG,EAAI,IAChDyC,kBAAmBpC,KAAKiB,MAAMmB,EAAoB,GAAG,EAAI,GAAA,CAC1D,CAAA,CACF,EAGMF,EAAYI,KAAK,CAACC,EAAGC,IAAMA,EAAEH,WAAaE,EAAEF,UAAU,CAC/D,EAKaI,GAA2BA,CACtC1D,EACA2D,IACsB,CACtB,GAAI3D,EAAOC,SAAW,EAAG,MAAO,GAE5B2D,IAAAA,EAEJ,OAAQD,EAAQ,CACd,IAAK,YACHC,EAAY,CACV,aACA,cACA,cACA,cACA,cACA,cACA,aAAa,EAEf,MACF,IAAK,YACHA,EAAY,CAAC,SAAU,UAAW,YAAa,WAAY,QAAQ,EACnE,MACF,IAAK,UACHA,EAAY,CACV,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,UAAU,EAEZ,MACF,QACE,MAAO,EACX,CAGMC,MAAAA,EAAqCD,EAAUxD,IAAiB0D,IAAA,CACpEA,SAAAA,EACA9D,OAAQ,EACR4B,QAAS,EACT0B,WAAY,CACZ,EAAA,EAGFtD,OAAAA,EAAOuC,QAAqBC,GAAA,CAC1B,MAAMlC,EAAQkC,EAAUlC,MAClByD,EAAY,IAAIC,KAAK1D,EAAM2D,IAAI,EACrC,IAAIC,EAAoB,GAEpBP,GAAAA,IAAa,aAAerD,EAAM6D,WACxBC,EAAAA,GAAiB9D,EAAM6D,UAAU,UACpCR,IAAa,YAAa,CAC7BU,MAAAA,EAAYN,EAAUO,SACxBD,GAAa,GAAKA,GAAa,IAEjCH,EAAYG,EAAY,QAEjBV,IAAa,YACtBO,EAAYH,EAAUQ,YAGxB,GAAIL,GAAa,GAAKA,EAAYL,EAAgB5D,OAAQ,CAClDuE,MAAAA,EAAOX,EAAgBK,CAAS,EACjClE,EAAAA,SACAsD,EAAAA,YAAchD,EAAMG,aAAe,EAC1C,CACD,EAGe8B,EAAAA,QAAQ,CAACiC,EAAMC,IAAU,CACnCD,GAAAA,EAAKxE,OAAS,EAAG,CACb0E,MAAAA,EAAa1E,EAAOQ,OAAoBgC,GAAA,CAC5C,MAAMlC,EAAQkC,EAAUlC,MAClByD,EAAY,IAAIC,KAAK1D,EAAM2D,IAAI,EAEjCN,OAAAA,IAAa,aAAerD,EAAM6D,WAC7BC,GAAiB9D,EAAM6D,UAAU,IAAMM,EACrCd,IAAa,YACJI,EAAUO,WACPG,EAAQ,EACpBd,IAAa,UACfI,EAAUQ,SAAeE,IAAAA,EAE3B,EAAA,CACR,EAEKE,EAAoBD,EAAWlE,OACnCgC,IAAcA,EAAUlC,MAAMG,aAAe,GAAK,CACpD,EACKmB,EAAAA,QACH8C,EAAWzE,OAAS,EAAK0E,EAAkB1E,OAASyE,EAAWzE,OAAU,IAAM,EACnF,CACD,EAGM4D,EACJrD,OAAegE,GAAAA,EAAKxE,OAAS,CAAC,EAC9BI,IAAaoE,IAAA,CACZ,GAAGA,EACH5C,QAASX,KAAKiB,MAAMsC,EAAK5C,QAAU,GAAG,EAAI,IAC1C0B,WAAYrC,KAAKiB,MAAMsC,EAAKlB,WAAa,GAAG,EAAI,GAChD,EAAA,CACN,EAKasB,GAAuB5E,GAA+C,CACjF,GAAIA,EAAOC,SAAW,EAAG,MAAO,GAG1B4E,MAAAA,EAAe,CAAC,GAAG7E,CAAM,EAAEuD,KAC/B,CAACC,EAAGC,IAAM,IAAIO,KAAKR,EAAElD,MAAM2D,IAAI,EAAEa,QAAQ,EAAI,IAAId,KAAKP,EAAEnD,MAAM2D,IAAI,EAAEa,QAAAA,CACtE,EAEMC,EAA8B,CAAA,EACpC,IAAIC,EAAiB,EAERzC,OAAAA,EAAAA,QAAQ,CAACC,EAAWiC,IAAU,CACzC,MAAMnE,EAAQkC,EAAUlC,MACxB0E,GAAkB1E,EAAMG,aAAe,EAEvCsE,EAAa7B,KAAK,CAChBe,KAAM3D,EAAM2D,KACZgB,OAAQhE,KAAKiB,MAAM8C,EAAiB,GAAG,EAAI,IAC3CE,SAAU,EACVC,QAASlE,KAAKiB,MAAM8C,EAAiB,GAAG,EAAI,IAC5CI,YAAaX,EAAQ,EACrBnB,WAAYhD,EAAMG,aAAe,CAAA,CAClC,CAAA,CACF,EAEMsE,CACT,EAKaM,GAA4BrF,GAAmD,CAC1F,GAAIA,EAAOC,SAAW,EAAG,MAAO,GAGhC,MAAMqF,EAAS,CACb,CAAE/D,IAAK,KAAWlJ,IAAK,KAAOkN,MAAO,UAAA,EACrC,CAAEhE,IAAK,KAAOlJ,IAAK,KAAMkN,MAAO,iBAAA,EAChC,CAAEhE,IAAK,KAAMlJ,IAAK,KAAMkN,MAAO,gBAAA,EAC/B,CAAEhE,IAAK,KAAMlJ,IAAK,EAAGkN,MAAO,aAAA,EAC5B,CAAEhE,IAAK,EAAGlJ,IAAK,IAAKkN,MAAO,YAAA,EAC3B,CAAEhE,IAAK,IAAKlJ,IAAK,IAAKkN,MAAO,cAAA,EAC7B,CAAEhE,IAAK,IAAKlJ,IAAK,IAAMkN,MAAO,eAAA,EAC9B,CAAEhE,IAAK,IAAMlJ,IAAKyJ,IAAUyD,MAAO,SAAA,CAAW,EAG1CC,EAAkCF,EAAOlF,IAAcqF,IAAA,CAC3DA,MAAOA,EAAMF,MACbG,MAAO,EACPC,WAAY,EACZC,SAAU,EACVC,MAAOJ,EAAMlE,KAAO,CACpB,EAAA,EAGFvB,OAAAA,EAAOuC,QAAqBC,GAAA,CACpBsD,MAAAA,EAAMtD,EAAUlC,MAAMG,aAAe,EACrCsF,EAAaT,EAAOU,UAAUP,GAASK,EAAML,EAAMlE,KAAOuE,GAAOL,EAAMpN,GAAG,EAE5E0N,GAAc,IAChBP,EAAaO,CAAU,EAAEL,QACZK,EAAAA,CAAU,EAAEH,UAAYE,EACvC,CACD,EAGDN,EAAajD,QAAe0D,GAAA,CACtBN,EAAAA,WAAa3F,EAAOC,OAAS,EAAKgG,EAAIP,MAAQ1F,EAAOC,OAAU,IAAM,EACzEgG,EAAIN,WAAa1E,KAAKiB,MAAM+D,EAAIN,WAAa,GAAG,EAAI,IACpDM,EAAIL,SAAW3E,KAAKiB,MAAM+D,EAAIL,SAAW,GAAG,EAAI,GAAA,CACjD,EAEMJ,EAAahF,OAAcyF,GAAAA,EAAIP,MAAQ,CAAC,CACjD,EAGMxF,GAAkBA,KAA2B,CACjD8B,YAAa,EACbzB,cAAe,EACfG,aAAc,EACduB,UAAW,EACXL,QAAS,EACTT,WAAY,EACZC,YAAa,EACbS,aAAc,EACdjB,gBAAiB,EACjBS,WAAY,EACZC,YAAa,EACbI,gBAAiB,EACjBK,WAAY,CACd,GAEMN,GAA2BzB,GACxBA,EACJQ,OAAgBF,GAAAA,EAAM6D,YAAc7D,EAAM4F,SAAS,EACnD9F,IAAaE,GAAA,CACZ,MAAM6F,EAAY,IAAInC,KAAK1D,EAAM6D,UAAW,EAAEW,UAEtCsB,OADS,IAAIpC,KAAK1D,EAAM4F,SAAU,EAAEpB,UACzBqB,IAAc,IAAO,GAAA,CACzC,EAGC/B,GAAoBiC,GAA+B,CACjDC,MAAAA,EAAO,IAAItC,KAAKqC,CAAU,EAC1BE,EAAOD,EAAKE,WACZC,EAASH,EAAKI,aACdC,EAAYJ,EAAOE,EAAS,GAE9BE,OAAAA,EAAY,KAAOA,GAAa,GAAW,GAC3CA,EAAY,KAAa,EACzBA,EAAY,KAAa,EACzBA,EAAY,KAAa,EACzBA,EAAY,KAAa,EACzBA,EAAY,KAAa,EACzBA,EAAY,KAAa,EACtB,CACT,EChXA,MAAMC,EAAwB,CAA9B,cACUC,GAAAA,iBAAYvE,KACZwE,GAAAA,cAAsB,CAC5BC,OAAQ,EAAI,GAAK,IACjBC,QAAS,GAAA,GAMHC,iBAAiBjH,EAA6BkH,EAAmBC,EAAsB,CAEvFC,MAAAA,EAAWpH,EACdI,IAASC,GAAAA,EAAEC,MAAM+G,EAAE,EACnB9D,KAAAA,EACA+D,KAAK,GAAG,EACLC,EAAYJ,EAAStI,KAAKW,UAAU2H,CAAM,EAAI,GAC7C,MAAA,GAAGD,KAAaE,KAAYG,GACrC,CAKQC,UAAalJ,EAAuB,CAC1C,MAAMmJ,EAAQ,KAAKZ,MAAM5D,IAAI3E,CAAG,EAEhC,OAAKmJ,EAKDzD,KAAK0D,MAAQD,EAAME,UAAY,KAAKb,OAAOC,QACxCF,KAAAA,MAAMe,OAAOtJ,CAAG,EACd,MAGFmJ,EAAMI,KATJ,IAUX,CAKQC,UAAaxJ,EAAauJ,EAAe,CAE/C,GAAI,KAAKhB,MAAMpP,MAAQ,KAAKqP,OAAOE,QAAS,CAC1C,MAAMe,EAAY,KAAKlB,MAAMmB,KAAK,EAAEC,KAAO7I,EAAAA,MACvC2I,GACGlB,KAAAA,MAAMe,OAAOG,CAAS,EAI1BlB,KAAAA,MAAM7D,IAAI1E,EAAK,CAClBuJ,KAAAA,EACAF,UAAW3D,KAAK0D,IAAI,EACpBQ,KAAM5J,CAAAA,CACP,CACH,CAKA6J,YAAmB,CACjB,KAAKtB,MAAMuB,OACb,CAKAC,mBAA0B,CAClBX,MAAAA,EAAM1D,KAAK0D,MACjB,SAAW,CAACpJ,EAAKmJ,CAAK,IAAK,KAAKZ,MAAMyB,UAChCZ,EAAMD,EAAME,UAAY,KAAKb,OAAOC,QACjCF,KAAAA,MAAMe,OAAOtJ,CAAG,CAG3B,CAKAiK,eAKE,CACA,IAAIC,EAAiC,KAErC,UAAWf,KAAS,KAAKZ,MAAM4B,OAAAA,GACzBD,IAAoB,MAAQf,EAAME,UAAYa,KAChDA,EAAkBf,EAAME,WAIrB,MAAA,CACLlQ,KAAM,KAAKoP,MAAMpP,KACjBuP,QAAS,KAAKF,OAAOE,QACrB0B,QAAS,EACTC,YAAaH,CAAAA,CAEjB,CAKA,MAAMI,4BACJ5I,EACA6I,EAC6B,CAC7B,MAAMC,EAAW,KAAK7B,iBAAiBjH,EAAQ,qBAAqB,EAG9D+I,EAAS,KAAKvB,UAA8BsB,CAAQ,EAC1D,GAAIC,EACFC,eAAQC,IAAI,6CAA6C,EAClDF,EAITC,QAAQC,IAAI,uCAAuC,EAC7CC,MAAAA,EAASL,EAAW7I,CAAM,EAC3B8H,YAAAA,UAAUgB,EAAUI,CAAM,EAExBA,CACT,CAKA,MAAMC,6BACJnJ,EACAoC,EACAyG,EACgC,CAChC,MAAMC,EAAW,KAAK7B,iBAAiBjH,EAAQ,uBAAwB,CAAEoC,SAAAA,CAAAA,CAAU,EAG7E2G,EAAS,KAAKvB,UAAiCsB,CAAQ,EAC7D,GAAIC,EACME,eAAAA,IAAI,4BAA4B7G,yBAAgC,EACjE2G,EAIDE,QAAAA,IAAI,2CAA2C7G,MAAa,EAC9D8G,MAAAA,EAASL,EAAW7I,EAAQoC,CAAe,EAC5C0F,YAAAA,UAAUgB,EAAUI,CAAM,EAExBA,CACT,CAKA,MAAME,yBACJpJ,EACA2D,EACAkF,EAC4B,CAC5B,MAAMC,EAAW,KAAK7B,iBAAiBjH,EAAQ,mBAAoB,CAAE2D,SAAAA,CAAAA,CAAU,EAGzEoF,EAAS,KAAKvB,UAA6BsB,CAAQ,EACzD,GAAIC,EACME,eAAAA,IAAI,uBAAuBtF,yBAAgC,EAC5DoF,EAIDE,QAAAA,IAAI,sCAAsCtF,MAAa,EACzDuF,MAAAA,EAASL,EAAW7I,EAAQ2D,CAAe,EAC5CmE,YAAAA,UAAUgB,EAAUI,CAAM,EAExBA,CACT,CAKA,MAAMG,qBACJrJ,EACA6I,EACwB,CACxB,MAAMC,EAAW,KAAK7B,iBAAiBjH,EAAQ,cAAc,EAGvD+I,EAAS,KAAKvB,UAAyBsB,CAAQ,EACrD,GAAIC,EACFC,eAAQC,IAAI,sCAAsC,EAC3CF,EAITC,QAAQC,IAAI,gCAAgC,EACtCC,MAAAA,EAASL,EAAW7I,CAAM,EAC3B8H,YAAAA,UAAUgB,EAAUI,CAAM,EAExBA,CACT,CAKA,MAAMI,0BACJtJ,EACA6I,EAC4B,CAC5B,MAAMC,EAAW,KAAK7B,iBAAiBjH,EAAQ,mBAAmB,EAG5D+I,EAAS,KAAKvB,UAA6BsB,CAAQ,EACzD,GAAIC,EACFC,eAAQC,IAAI,2CAA2C,EAChDF,EAITC,QAAQC,IAAI,qCAAqC,EAC3CC,MAAAA,EAASL,EAAW7I,CAAM,EAC3B8H,YAAAA,UAAUgB,EAAUI,CAAM,EAExBA,CACT,CACF,CAGaK,MAAAA,EAAmB,IAAI3C,GAQvB4C,GAA0B,MACrCxJ,EACAyJ,IAmBI,CACJT,QAAQC,IAAI,4CAA4C,EAGxD,KAAM,CACJS,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CAAgB,EACd,MAAMC,QAAQC,IAAI,CACpBf,EAAiBX,4BAA4B5I,EAAQyJ,EAAYc,kBAAkB,EACnFhB,EAAiBJ,6BACfnJ,EACA,SACAyJ,EAAYe,mBACd,EACAjB,EAAiBJ,6BACfnJ,EACA,aACAyJ,EAAYe,mBACd,EACAjB,EAAiBJ,6BACfnJ,EACA,UACAyJ,EAAYe,mBACd,EACAjB,EAAiBJ,6BAA6BnJ,EAAQ,QAASyJ,EAAYe,mBAAmB,EAC9FjB,EAAiBJ,6BACfnJ,EACA,YACAyJ,EAAYe,mBACd,EACAjB,EAAiBH,yBAAyBpJ,EAAQ,YAAayJ,EAAY5F,eAAe,EAC1F0F,EAAiBH,yBAAyBpJ,EAAQ,YAAayJ,EAAY5F,eAAe,EAC1F0F,EAAiBH,yBAAyBpJ,EAAQ,UAAWyJ,EAAY5F,eAAe,EACxF0F,EAAiBF,qBAAqBrJ,EAAQyJ,EAAYU,WAAW,EACrEZ,EAAiBD,0BAA0BtJ,EAAQyJ,EAAYW,gBAAgB,CAAC,CACjF,EAEDpB,eAAQC,IAAI,yCAAyC,EAE9C,CACLS,QAAAA,EACAC,kBAAAA,EACAC,oBAAAA,EACAC,mBAAAA,EACAC,iBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,mBAAAA,EACAC,YAAAA,EACAC,iBAAAA,CAAAA,CAEJ,EAKaK,EAAqB,CAIhC,MAAMC,YAAeC,EAAcC,EAAsC,CACjEC,MAAAA,EAAQ1H,YAAYuE,MACpBwB,EAAS,MAAM0B,IACfE,EAAM3H,YAAYuE,MACxBsB,eAAQC,IAAI,MAAM0B,WAAcG,EAAMD,GAAOE,QAAQ,CAAC,KAAK,EACpD7B,CACT,EAKA8B,eAAeC,EAAuB,CACpC,GAAI,WAAY9H,YAAa,CAC3B,MAAM+H,EAAU/H,YAAoB+H,OAC5BjC,QAAAA,IAAI,oBAAoBgC,MAAa,CAC3CE,KAAM,IAAID,EAAOE,eAAiB,KAAO,MAAML,QAAQ,CAAC,OACxDM,MAAO,IAAIH,EAAOI,gBAAkB,KAAO,MAAMP,QAAQ,CAAC,OAC1DQ,MAAO,IAAIL,EAAOM,gBAAkB,KAAO,MAAMT,QAAQ,CAAC,MAAA,CAC3D,EAEL,EAKAxC,eAAgB,CACd,OAAOgB,EAAiBhB,eAC1B,CACF,ECtWakD,GAA6B,MACxCC,GAC+B,CAC3B,GAAA,CACMzC,QAAAA,IAAI,qDAAsDyC,CAAO,EAMnE1L,MAAAA,EAAS,MAAM2L,GAAoBC,eAEjC3C,QAAAA,IAAI,gBAAgBjJ,EAAOC,4BAA4B,EAGzD4L,MAAAA,EAAiBC,GAAuB9L,EAAQ0L,CAAO,EAErDzC,QAAAA,IAAI,mCAAmC4C,EAAe5L,eAAe,EAGvE8L,MAAAA,EAAe,MAAMC,GAAqBH,CAAc,EAE9D7C,eAAQC,IAAI,8CAA8C,EACnD8C,QACAtV,GACCA,cAAAA,MAAM,6CAA8CA,CAAK,EAC3D,IAAIwV,MACR,wCACExV,aAAiBwV,MAAQxV,EAAMyV,QAAU,iBAE7C,CACF,CACF,EAKMF,GAAuB,MAAOhM,GAA4D,CAC9FgJ,QAAQC,IAAI,kDAAkD,EAG9DwB,EAAmBO,eAAe,iBAAiB,EAGnD,MAAMmB,EAAmB,MAAM1B,EAAmBC,YAAY,8BAA+B,IAC3FlB,GAAwBxJ,EAAQ,CAC9BuK,mBAAoBxK,GACpByK,oBAAqBrI,GACrB0B,gBAAiBH,GACjByG,YAAavF,GACbwF,iBAAkB/E,EACnB,CAAA,CACH,EAGM,CACJqE,QAAAA,EACAC,kBAAAA,EACAC,oBAAAA,EACAC,mBAAAA,EACAC,iBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,mBAAAA,EACAC,YAAAA,EACAC,iBAAAA,CACE+B,EAAAA,EAGJ1B,EAAmBO,eAAe,gBAAgB,EAG5CoB,MAAAA,EAAa3B,EAAmBlC,gBAC9BU,eAAAA,IAAI,uBAAwBmD,CAAU,EAEvC,CACLpM,OAAQA,EAAOI,IAAIiM,EAA4B,EAC/C3C,QAAAA,EACAC,kBAAAA,EACAC,oBAAAA,EACA0C,qBAAsB,CAAE,EACxBzC,mBAAAA,EACAC,iBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,qBAAAA,EACAC,mBAAAA,EACAC,YAAAA,EACAC,iBAAAA,EAEApI,YAAahC,EAAOC,OACpBsM,UAAW,CACT1B,MACE7K,EAAOC,OAAS,EACZgB,KAAKM,IAAI,GAAGvB,EAAOI,IAAIC,GAAK,IAAI2D,KAAK3D,EAAEC,MAAM2D,IAAI,EAAEa,SAAS,CAAC,EAC7Dd,KAAK0D,IAAI,EACfoD,IACE9K,EAAOC,OAAS,EACZgB,KAAK5I,IAAI,GAAG2H,EAAOI,IAAIC,GAAK,IAAI2D,KAAK3D,EAAEC,MAAM2D,IAAI,EAAEa,SAAS,CAAC,EAC7Dd,KAAK0D,IAAI,CACjB,EACA8E,YAAa,IAAIxI,KAAK,EAAEyI,YAAY,CAAA,CAExC,EAuDMX,GAAyBA,CAC7B9L,EACA0L,IAEO1L,EAAOQ,OAAoBgC,GAAA,OAChC,MAAMlC,EAAQkC,EAAUlC,MAGxB,GAAIoL,EAAQgB,SAAWhB,EAAQgB,QAAQzM,OAAS,GAI1C,CAHsByL,EAAQgB,QAAQC,KACxCrM,GAAAA,OAAAA,OAAAA,EAAAA,EAAMoC,SAANpC,YAAAA,EAAcsM,cAAcC,SAASC,EAAOF,YAAAA,GAC9C,EAC+B,MAAA,GAIjC,GAAIlB,EAAQqB,YAAcrB,EAAQqB,WAAW9M,OAAS,EAAG,CACjD+M,MAAAA,GAAiB1M,EAAAA,EAAMwC,YAANxC,YAAAA,EAAiBsM,cAIxC,GAAI,CAHyBlB,EAAQqB,WAAWJ,KAC9C7J,GAAaA,IAAckK,CAC7B,EACkC,MAAA,GAIpC,GAAItB,EAAQuB,UAAYvB,EAAQuB,SAAShN,OAAS,EAAG,CAC7CiN,MAAAA,EAAe5M,EAAMsC,SAAW,GACtC,GAAI,CAAC8I,EAAQuB,SAASJ,SAASK,CAAmB,EAAU,MAAA,GAI9D,GAAIxB,EAAQyB,YAAczB,EAAQyB,WAAWlN,OAAS,GAChD,CAACyL,EAAQyB,WAAWN,SAASvM,EAAMqC,YAAc,EAAE,EAAU,MAAA,GAInE,GAAI+I,EAAQ0B,UAAY1B,EAAQ0B,SAASnN,OAAS,EAAG,CACnD,MAAMoN,EAAcC,GAAehN,EAAMG,aAAe,CAAC,EACzD,GAAI,CAACiL,EAAQ0B,SAASP,SAASQ,CAAW,EAAU,MAAA,GAItD,OAAI3B,EAAQ6B,MAAQ7B,EAAQ6B,KAAKtN,OAAS,EAInC,EAAA,CACR,EAMGoM,GAAgC7J,GAAsC,SAC1E,MAAMlC,EAAQkC,EAAUlC,MAEjB,MAAA,CACL+G,KAAI/G,EAAAA,EAAM+G,KAAN/G,YAAAA,EAAUkN,aAAc,GAAGlN,EAAM2D,QAAQ3D,EAAMoC,SACnDoK,OAAQxM,EAAMoC,QAAU,UACxBI,YAAWxC,EAAAA,EAAMwC,YAANxC,YAAAA,EAAiBsM,gBAAiB,UAC7Ca,WAAYnN,EAAMoN,aAAe,EACjCC,UAAWrN,EAAMsN,YAAc,EAC/BC,SAAUvN,EAAMwN,iBAAmB,EACnC3H,UAAW7F,EAAM6D,YAAc7D,EAAM2D,KACrCmC,SAAU9F,EAAM4F,WAAa5F,EAAM2D,KACnC8J,OAAQT,GAAehN,EAAMG,aAAe,CAAC,EAC7C6C,WAAYhD,EAAMG,aAAe,EACjCuN,kBAAmBC,GAA2B3N,CAAK,EACnD4N,UAAW,MACXtL,QAAStC,EAAMsC,SAAW,UAC1BuL,SAAU7N,EAAMqC,YAAc,UAC9BE,MAAOvC,EAAMuC,OAAS,UACtBuL,UAAW9N,EAAM+N,YAAc,EAC/Bd,KAAM,CAAE,EACRe,MAAOhO,EAAMgO,OAAS,GAEtBC,eAAgBjO,EAAMkO,wBAA0B,EAChDC,UAAWnO,EAAMoO,YAAc,GAC/BC,OAAQrO,EAAMsO,SAAW,GACzBC,gBAAiBvO,EAAMwO,mBAAqB,EAAA,CAEhD,EAKMxB,GAAkBhK,GAClBA,EAAa,EAAU,MACvBA,EAAa,EAAU,OACpB,YAMH2K,GAA8B3N,GAAuB,CACzD,GAAI,CAACA,EAAMoN,aAAepN,EAAMoN,cAAgB,EAAU,MAAA,GAEpDpK,MAAAA,EAAahD,EAAMG,aAAe,EAClCsO,EAAazO,EAAMoN,aAAepN,EAAMwN,iBAAmB,GAEjE,OAAOiB,EAAa,EAAKzL,EAAayL,EAAc,IAAM,CAC5D,EC9PMC,GAAgB,GAQTC,GAAyB,MAAOvD,GAAsD,CACzFzC,QAAAA,IAAI,+CAA+C+F,KAAgB,EAGrE,GAAA,CACK,OAAA,MAAMvD,GAA2BC,CAAO,QACxCjV,GACCA,eAAAA,MAAM,+DAAgEA,CAAK,EAE5EyY,GAAiBxD,CAAO,CACjC,CAaJ,EA+CMwD,GAAoBxD,GAA6C,CAE/D1L,MAAAA,EAASmP,GAAmBzD,CAAO,EAGnChC,EAAU0F,GAAiBpP,CAAM,EAGjC2J,EAAoBxH,EAA6BnC,EAAQ,QAAQ,EACjE4J,EAAsBzH,EAA6BnC,EAAQ,UAAU,EACrEsM,EAAuBnK,EAA6BnC,EAAQ,WAAW,EACvE6J,EAAqB1H,EAA6BnC,EAAQ,SAAS,EAGnEgK,EAAuBtG,GAAyB1D,EAAQ,WAAW,EACnEiK,EAAuBvG,GAAyB1D,EAAQ,WAAW,EAElE,MAAA,CACLA,OAAAA,EACA0J,QAAAA,EACAC,kBAAAA,EACAC,oBAAAA,EACA0C,qBAAAA,EACAzC,mBAAAA,EACAG,qBAAAA,EACAC,qBAAAA,CAAAA,CAEJ,EAKMkF,GAAsBzD,GAAmC,CACvD,KAAA,CAAEa,UAAAA,CAAcb,EAAAA,EAChB2D,EAAY,IAAIrL,KAAKuI,EAAU8C,SAAS,EACxCC,EAAU,IAAItL,KAAKuI,EAAU+C,OAAO,EAGpCC,EAAWtO,KAAKuO,OAAOF,EAAQxK,QAAQ,EAAIuK,EAAUvK,QAAAA,IAAc,IAAO,GAAK,GAAK,GAAG,EAGvF2K,EAAYxO,KAAK5I,IAAI,EAAGkX,CAAQ,GAAK,EAAItO,KAAKuO,MAAMvO,KAAKyO,OAAO,EAAI,CAAC,GAErE1P,EAAkB,CAAA,EAClB0M,EAAU,CAAC,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,OAAQ,OAAQ,MAAM,EAC1ES,EAAa,CACjB,WACA,WACA,kBACA,aACA,cACA,oBAAoB,EAEhBwC,EAA+B,CAAC,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,OAAO,EAC7E1C,EAA6B,CAAC,aAAc,UAAW,aAAa,EACpEM,EAAO,CACX,cACA,YACA,WACA,OACA,YACA,WACA,WACA,YAAY,EAGd,QAASqC,EAAI,EAAGA,EAAIH,EAAWG,IAAK,CAElC,MAAM7L,EAAY,IAAIC,KACpBqL,EAAUvK,QAAY7D,EAAAA,KAAKyO,OAAO,GAAKJ,EAAQxK,QAAYuK,EAAAA,EAAUvK,UACvE,EAGM+K,EAAY,EAAI5O,KAAKuO,MAAMvO,KAAKyO,OAAAA,EAAW,CAAC,EAC5CI,EAAc7O,KAAKuO,MAAMvO,KAAKyO,OAAAA,EAAW,EAAE,EAC3CvJ,EAAY,IAAInC,KAAKD,CAAS,EACpCoC,EAAU4J,SAASF,EAAWC,EAAa,EAAG,CAAC,EAG/C,MAAME,EAAkB,EAAI/O,KAAKuO,MAAMvO,KAAKyO,OAAAA,EAAW,GAAG,EACpDtJ,EAAW,IAAIpC,KAAKmC,EAAUrB,UAAYkL,EAAkB,GAAK,GAAI,EAGrElD,EAASJ,EAAQzL,KAAKuO,MAAMvO,KAAKyO,SAAWhD,EAAQzM,MAAM,CAAC,EAC3D6C,EAA4B7B,KAAKyO,OAAO,EAAI,GAAM,OAAS,QAC3DjC,EAAa,IAAMxM,KAAKyO,OAAAA,EAAW,IAGnC7J,EAAQ5E,KAAKyO,OAAAA,EAAW,GACxBO,EAAc,CAACpK,GAAS5E,KAAKyO,SAAW,GAG1C/B,IAAAA,EAAWrK,EAAY0K,EAAmBD,EAE9C,GAAIkC,EACFtC,EAAYF,GAAcxM,KAAKyO,OAAO,EAAI,GAAM,IACvC3B,EAAA,oBACAlI,EAAO,CAChB,MAAMqK,EAAa,GAAMjP,KAAKyO,OAAAA,EAAW,IAEvC5M,EAAAA,IAAc,OACV2K,GAAc,EAAIyC,EAAa,KAC/BzC,GAAc,EAAIyC,EAAa,KAC5BnC,EAAA,UACJ,CACL,MAAMoC,EAAc,GAAMlP,KAAKyO,OAAAA,EAAW,IAExC5M,EAAAA,IAAc,OACV2K,GAAc,EAAI0C,EAAc,KAChC1C,GAAc,EAAI0C,EAAc,KAC7BpC,EAAA,OAIX,MAAMF,GAAW,GAAK5M,KAAKuO,MAAMvO,KAAKyO,OAAAA,EAAW,EAAE,EAE/C5M,IAAc,QAChBQ,GAAcqK,EAAYF,GAAcI,GACnBF,GAAAA,EAAYF,EAAa,GAAK,MAEnDnK,GAAcmK,EAAaE,GAAaE,GACnBJ,GAAAA,EAAaE,EAAY,GAAK,KAIrDrK,EAAarC,KAAKiB,MAAMoB,EAAa,GAAG,EAAI,IAC5C0K,EAAoB/M,KAAKiB,MAAM8L,EAAoB,GAAG,EAAI,IAGpDE,MAAAA,GAAYyB,EAAW1O,KAAKuO,MAAMvO,KAAKyO,SAAWC,EAAW1P,MAAM,CAAC,EACpE2C,GAAUqK,EAAShM,KAAKuO,MAAMvO,KAAKyO,SAAWzC,EAAShN,MAAM,CAAC,EAC9DkO,GAAWhB,EAAWlM,KAAKuO,MAAMvO,KAAKyO,SAAWvC,EAAWlN,MAAM,CAAC,EAGnEmQ,GAAUnP,KAAKuO,MAAMvO,KAAKyO,OAAAA,EAAW,CAAC,EACtCW,GAAsB,CAAA,EAC5B,QAASC,EAAI,EAAGA,EAAIF,GAASE,IAAK,CAC1BC,MAAAA,GAAMhD,EAAKtM,KAAKuO,MAAMvO,KAAKyO,SAAWnC,EAAKtN,MAAM,CAAC,EACnDoQ,GAAUxD,SAAS0D,EAAG,GACzBF,GAAUnN,KAAKqN,EAAG,EAKtB,MAAMjQ,GAAa,CACjB+G,GAAI,SAASuI,IACb9C,OAAAA,EACAhK,UAAAA,EACA2K,WAAAA,EACAE,UAAAA,EACAE,SAAAA,GACA1H,UAAWA,EAAUsG,YAAY,EACjCrG,SAAUA,EAASqG,YAAY,EAC/BsB,OAAAA,EACAzK,WAAAA,EACA0K,kBAAAA,EACAE,UAAAA,GACAtL,QAAAA,GACAuL,SAAAA,GACAZ,KAAM8C,GACN/B,MAAOrN,KAAKyO,SAAW,GAAM,mBAAmB5C,KAAUhK,UAAoBvK,MAAAA,EAGhFyH,EAAOkD,KAAK5C,EAAK,EAInB,OAAON,EAAOuD,KACZ,CAACC,EAAQC,IAAW,IAAIO,KAAKP,EAAE0C,SAAS,EAAErB,QAAAA,EAAY,IAAId,KAAKR,EAAE2C,SAAS,EAAErB,SAC9E,CACF,EAKMsK,GAAoBpP,GAAsC,CAC9D,MAAMO,EAAgBP,EAAOQ,OAAgBF,GAAAA,EAAMyN,SAAW,KAAK,EAC7DrN,EAAeV,EAAOQ,OAAgBF,GAAAA,EAAMyN,SAAW,MAAM,EAC7D9L,EAAYjC,EAAOQ,OAAgBF,GAAAA,EAAMyN,SAAW,WAAW,EAE/DnN,EAAkBZ,EAAOa,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAMgD,WAAY,CAAC,EACzEvC,EAAiBR,EAAcM,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAMgD,WAAY,CAAC,EAC/EtC,EAAkBC,KAAKC,IAAIR,EAAaG,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAMgD,WAAY,CAAC,CAAC,EAEzFnC,EAAaZ,EAAcN,OAAS,EAAIc,EAAiBR,EAAcN,OAAS,EAEhFmB,EAAcV,EAAaT,OAAS,EAAIe,EAAkBN,EAAaT,OAAS,EAEhFoB,EACJd,EAAcN,OAAS,EAAIgB,KAAK5I,IAAI,GAAGkI,EAAcH,IAAIE,GAASA,EAAMgD,UAAU,CAAC,EAAI,EAEnFhC,EACJZ,EAAaT,OAAS,EAAIgB,KAAKM,IAAI,GAAGb,EAAaN,IAAIE,GAASA,EAAMgD,UAAU,CAAC,EAAI,EAGjF9B,EAAYxB,EAAOI,IAAaE,GAAA,CACpC,MAAM6F,EAAY,IAAInC,KAAK1D,EAAM6F,SAAS,EAAErB,UAEpCsB,OADS,IAAIpC,KAAK1D,EAAM8F,QAAQ,EAAEtB,UACvBqB,IAAc,IAAO,GAAA,CACzC,EAEKzE,EACJF,EAAUvB,OAAS,EACfuB,EAAUX,OAAO,CAACC,EAAKa,IAAab,EAAMa,EAAU,CAAC,EAAIH,EAAUvB,OACnE,EAGA4B,EACJb,EAAkB,EAAID,EAAiBC,EAAkBD,EAAiB,EAAIe,IAAW,EAErFF,EAAU5B,EAAOC,OAAS,EAAIM,EAAcN,OAASD,EAAOC,OAAS,EAErE8B,EAAaH,EAAUT,GAAc,EAAIS,GAAWR,EAEnD,MAAA,CACLY,YAAahC,EAAOC,OACpBM,cAAeA,EAAcN,OAC7BS,aAAcA,EAAaT,OAC3BgC,UAAWA,EAAUhC,OACrB2B,QAASX,KAAKiB,MAAMN,EAAU,GAAK,EAAI,IACvCT,WAAYF,KAAKiB,MAAMf,EAAa,GAAG,EAAI,IAC3CC,YAAaH,KAAKiB,MAAMd,EAAc,GAAG,EAAI,IAC7CS,aAAcZ,KAAKiB,MAAML,EAAe,GAAG,EAAI,IAC/CjB,gBAAiBK,KAAKiB,MAAMtB,EAAkB,GAAG,EAAI,IACrDS,WAAYJ,KAAKiB,MAAMb,EAAa,GAAG,EAAI,IAC3CC,YAAaL,KAAKiB,MAAMZ,EAAc,GAAG,EAAI,IAC7CI,gBAAiBT,KAAKiB,MAAMR,EAAkB,GAAG,EAAI,IACrDK,WAAYd,KAAKiB,MAAMH,EAAa,GAAG,EAAI,GAAA,CAE/C,EAKMI,EAA+BA,CACnCnC,EACAoC,IAC0B,CAEpBC,MAAAA,MAAiBC,IAEvBtC,EAAOuC,QAAiBjC,GAAA,CAChBmC,MAAAA,EAAgBnC,EAAM8B,CAAQ,EAC/BC,EAAWU,IAAIN,CAAa,GACpBO,EAAAA,IAAIP,EAAe,CAAA,CAAE,EAElCJ,EAAWY,IAAIR,CAAa,EAAGS,KAAK5C,CAAK,CAAA,CAC1C,EAGD,MAAM6C,EAAqC,CAAA,EAEhCZ,OAAAA,EAAAA,QAAQ,CAACa,EAAgBX,IAAkB,CACpD,MAAMlC,EAAgB6C,EAAe5C,OAAgBF,GAAAA,EAAMyN,SAAW,KAAK,EACrEnN,EAAkBwC,EAAevC,OAAO,CAACC,EAAKR,IAAUQ,EAAMR,EAAMgD,WAAY,CAAC,EACjF1B,EAAUwB,EAAenD,OAAS,EAAIM,EAAcN,OAASmD,EAAenD,OAAS,EACrFoD,EACJD,EAAenD,OAAS,EAAIW,EAAkBwC,EAAenD,OAAS,EAExEkD,EAAYD,KAAK,CACfd,SAAAA,EACAhD,MAAOqD,EACPzC,OAAQoD,EAAenD,OACvB2B,QAASX,KAAKiB,MAAMN,EAAU,GAAK,EAAI,IACvC0B,WAAYrC,KAAKiB,MAAMtB,EAAkB,GAAG,EAAI,IAChDyC,kBAAmBpC,KAAKiB,MAAMmB,EAAoB,GAAG,EAAI,GAAA,CAC1D,CAAA,CACF,EAGMF,EAAYI,KAAK,CAACC,EAAGC,IAAMA,EAAEH,WAAaE,EAAEF,UAAU,CAC/D,EAKMI,GAA2BA,CAC/B1D,EACA2D,IACsB,CAElBC,IAAAA,EAEAD,IAAa,YACfC,EAAY,CACV,aACA,cACA,cACA,cACA,cACA,cACA,aAAa,EAGfA,EAAY,CAAC,SAAU,UAAW,YAAa,WAAY,QAAQ,EAI/DC,MAAAA,EAAqCD,EAAUxD,IAAiB0D,IAAA,CACpEA,SAAAA,EACA9D,OAAQ,EACR4B,QAAS,EACT0B,WAAY,CACZ,EAAA,EAEFtD,OAAAA,EAAOuC,QAAiBjC,GAAA,CACtB,MAAM6F,EAAY,IAAInC,KAAK1D,EAAM6F,SAAS,EACtCjC,IAAAA,EAEJ,GAAIP,IAAa,YAAa,CAEtB4C,MAAAA,EAAOJ,EAAUK,WACjBC,EAASN,EAAUO,aACnBC,EAAYJ,EAAOE,EAAS,GAE9BE,GAAAA,EAAY,KAAOA,GAAa,GAClC,OAGEA,EAAY,KAAkBzC,EAAA,EACzByC,EAAY,KAAkBzC,EAAA,EAC9ByC,EAAY,KAAkBzC,EAAA,EAC9ByC,EAAY,KAAkBzC,EAAA,EAC9ByC,EAAY,KAAkBzC,EAAA,EAC9ByC,EAAY,KAAkBzC,EAAA,EACtBA,EAAA,MACZ,CAECG,MAAAA,EAAY8B,EAAU7B,SACxBD,GAAAA,IAAc,GAAKA,IAAc,EACnC,OAEFH,EAAYG,EAAY,EAIpBG,MAAAA,EAAOX,EAAgBK,CAAS,EACjClE,EAAAA,SACLwE,EAAKlB,YAAchD,EAAMgD,WAGnBoB,MAAAA,EAAa1E,EAAOQ,OAAYH,GAAA,CACpC,MAAMmQ,EAAa,IAAIxM,KAAK3D,EAAE8F,SAAS,EACvC,GAAIxC,IAAa,YAAa,CACtB8M,MAAAA,EAAQD,EAAWhK,WACnBkK,EAAUF,EAAW9J,aACrBiK,EAAaF,EAAQC,EAAU,GAErC,OAAIxM,IAAc,EAAUyM,GAAc,KAAOA,EAAa,KAC1DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,KAC3DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,KAC3DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,KAC3DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,KAC3DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,KAC3DzM,IAAc,EAAUyM,GAAc,MAAQA,EAAa,GACxD,OAEAH,QAAAA,EAAWlM,WAAaJ,EAAY,CAC7C,CACD,EAEKS,EAAoBD,EAAWlE,OAAYH,GAAAA,EAAE0N,SAAW,KAAK,EAC9DnM,EAAAA,QAAU8C,EAAWzE,OAAS,EAAK0E,EAAkB1E,OAASyE,EAAWzE,OAAU,IAAM,CAAA,CAC/F,EAGM4D,EACJrD,OAAegE,GAAAA,EAAKxE,OAAS,CAAC,EAC9BI,IAAaoE,IAAA,CACZ,GAAGA,EACH5C,QAASX,KAAKiB,MAAMsC,EAAK5C,QAAU,GAAG,EAAI,IAC1C0B,WAAYrC,KAAKiB,MAAMsC,EAAKlB,WAAa,GAAG,EAAI,GAChD,EAAA,CACN,EChdMsN,GAAsBA,IAAM,CAC1BC,MAAAA,MAAY7M,KACZqL,MAAgBrL,KACtBqL,OAAAA,EAAUyB,SAASD,EAAMtM,SAAS,EAAI,CAAC,EAEhC,CACL8K,UAAWA,EAAU5C,YAAAA,EAAcsE,MAAM,GAAG,EAAE,CAAC,EAC/CzB,QAASuB,EAAMpE,YAAAA,EAAcsE,MAAM,GAAG,EAAE,CAAC,CAAA,CAE7C,EAGMC,GAAmC,CACvCnJ,KAAM,KACN6D,QAAS,CACPa,UAAWqE,GAAoB,CACjC,EACAK,YAAa,CACXC,iBAAkB,QAClBC,YAAa,UACbC,WAAY,CACVjO,YAAa,MACbqC,aAAc,MACd6L,aAAc,KAChB,EACAC,aAAc,CACZ,SACA,YACA,YACA,WACA,aACA,SACA,UAAU,EAEZC,mBAAoB,CAAE,EACtBC,aAAc,CAAA,CAChB,EACAhU,UAAW,GACX/G,MAAO,KACPgb,gBAAiB,IACnB,EAGMC,GAAuBA,CAC3BC,EACAC,IACuB,CACvB,OAAQA,EAAO9X,KAAI,CACjB,IAAK,mBACI,MAAA,CACL,GAAG6X,EACHnU,UAAW,GACX/G,MAAO,IAAA,EAEX,IAAK,qBACI,MAAA,CACL,GAAGkb,EACH9J,KAAM+J,EAAOC,QACbrU,UAAW,GACX/G,MAAO,IAAA,EAEX,IAAK,mBACI,MAAA,CACL,GAAGkb,EACHnU,UAAW,GACX/G,MAAOmb,EAAOC,OAAAA,EAElB,IAAK,iBACI,MAAA,CACL,GAAGF,EACHjG,QAAS,CACP,GAAGiG,EAAMjG,QACT,GAAGkG,EAAOC,OACZ,CAAA,EAEJ,IAAK,qBACI,MAAA,CACL,GAAGF,EACHV,YAAa,CACX,GAAGU,EAAMV,YACT,GAAGW,EAAOC,OACZ,CAAA,EAEJ,IAAK,eACI,MAAA,CACL,GAAGF,EACHF,gBAAiBG,EAAOC,OAAAA,EAE5B,IAAK,gBACI,MAAA,CACL,GAAGF,EACHjG,QAAS,CACPa,UAAWqE,GAAoB,CACjC,CAAA,EAEJ,QACSe,OAAAA,CACX,CACF,EAWMG,GAAuBC,EAAAA,cAAoDxZ,MAAS,EAO7EyZ,GAA8DA,CAAC,CAAEja,SAAAA,CAAS,IAAM,CAE3F,KAAM,CAACka,CAAgB,EAAI5T,GACzB,6BACA,CACF,CAAA,EAGM6T,EAAqB,CACzB,GAAGlB,GACHC,YAAa,CACX,GAAGD,GAAaC,YAChB,GAAGgB,CACL,CAAA,EAGI,CAACN,EAAOQ,CAAQ,EAAIC,EAAAA,WAAWV,GAAsBQ,CAAkB,EAG7EzS,EAAAA,UAAU,IAAM,CACdd,aAAaY,QAAQ,6BAA8BV,KAAKW,UAAUmS,EAAMV,WAAW,CAAC,CAAA,EACnF,CAACU,EAAMV,WAAW,CAAC,EAEhBoB,MAAAA,EAAYC,EAAAA,YAAY,SAAY,CAC/BH,EAAA,CAAErY,KAAM,kBAAA,CAAoB,EACjC,GAAA,CACF,MAAM+N,EAAO,MAAMoH,GAAuB0C,EAAMjG,OAAO,EAC9CyG,EAAA,CAAErY,KAAM,qBAAsB+X,QAAShK,CAAAA,CAAM,QAC/CpR,GACE0b,EAAA,CACPrY,KAAM,mBACN+X,QAASpb,aAAiBwV,MAAQxV,EAAMyV,QAAU,2BAAA,CACnD,CACH,CAAA,EACC,CAACyF,EAAMjG,OAAO,CAAC,EAEZ6G,EAAgBD,cAAa5G,GAAmC,CAC3DyG,EAAA,CAAErY,KAAM,iBAAkB+X,QAASnG,CAAAA,CAAS,CACvD,EAAG,CAAE,CAAA,EAEC8G,EAAoBF,cAAarB,GAA0C,CACtEkB,EAAA,CAAErY,KAAM,qBAAsB+X,QAASZ,CAAAA,CAAa,CAC/D,EAAG,CAAE,CAAA,EAECwB,EAAcH,cAAaI,GAA2B,CACjDP,EAAA,CAAErY,KAAM,eAAgB+X,QAASa,CAAAA,CAAS,CACrD,EAAG,CAAE,CAAA,EAECC,EAAeL,EAAAA,YAAY,IAAM,CAC5BH,EAAA,CAAErY,KAAM,eAAA,CAAiB,CACpC,EAAG,CAAE,CAAA,EAGL2F,EAAAA,UAAU,IAAM,CACJ4S,GACT,EAAA,CAACA,EAAWV,EAAMjG,OAAO,CAAC,EAE7B,MAAMtM,EAAQ,CACZ,GAAGuS,EACHU,UAAAA,EACAE,cAAAA,EACAC,kBAAAA,EACAC,YAAAA,EACAE,aAAAA,CAAAA,EAGF,OAAQna,EAAAA,IAAAsZ,GAAqB,SAArB,CAA8B,MAAA1S,EAAerH,SAAAA,CAAS,CAAA,CAChE,EAGa6a,EAAmBA,IAAgC,CACxD3H,MAAAA,EAAU4H,aAAWf,EAAoB,EAC/C,GAAI7G,IAAY1S,OACR,MAAA,IAAI0T,MAAM,8DAA8D,EAEzEhB,OAAAA,CACT,EC/LM6H,GAAyBla,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,yEAAA,kEAAA,GAAA,EAIrB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,QAE9B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,OAAM,EAGvDka,GAAsBna,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGtB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,MAAK,EAG5CkH,GAAeyW,EAAAA,GAAElc,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,6HAAA,EACR,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiB+d,MAAO,QAE3C,CAAC,CAAE/d,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc6D,cAAe,UAAS,EAS1Dma,GAAkBta,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,8FAAA,EACZ,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,WAAU,EAOzD0d,GAAwBva,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGxB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,OAAM,EAG7Cyd,GAAgBlc,EAAOyC,EAAM,EAAC7C,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qCAAA,EAAA,EAIhC,CAAC,CAAEqc,aAAAA,CAAa,IAChBA,GACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAmBD,EAGGC,GAAyB1a,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,YAAA,IAAA,4FAAA,cAAA,gGAAA,EAGzB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAChC,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAAS,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeO,KAAM,OAG3E,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoBO,KAAM,OAC7C,CAAC,CAAEP,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBG,KAAM,UAAS,EAOxDke,GAAmB3a,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAgB3B,EAAA,CAAA,2JAAA,CAAA,EAQYwc,GAAgDA,CAAC,CAC5DC,UAAAA,EACAJ,aAAAA,EAAe,GACfrb,UAAAA,CACF,IAEIgC,OAAC8Y,IAAgB,UAAA9a,EACf,SAAA,CAAAgC,OAAC+Y,GACC,CAAA,SAAA,CAAAva,EAAAA,IAAC+D,IAAM,SAAc,gBAAA,CAAA,EACrB/D,EAAAA,IAAC0a,IAAS,SAA8B,gCAAA,CAAA,CAAA,EAC1C,SAECC,GACC,CAAA,SAAA,CAAAnZ,OAACsZ,GACC,CAAA,SAAA,CAAA9a,EAAA,IAAC+a,GAAS,EAAA,EAAA,WAAA,EAEZ,EAECE,GACCjb,EAAA,IAAC4a,GACC,CAAA,QAAQ,UACR,KAAK,QACL,QAASK,EACT,SAAUJ,EACV,aAAAA,EAECA,SAAAA,EAAe,gBAAkB,UACpC,CAAA,EAEJ,CACF,CAAA,CAAA,ECzIEK,GAAuB9a,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,iEAAA,mGAAA,EAEvB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAC1B,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,QAEnC,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,MAAK,EAWvDse,GAAara,EAAAA,OAAMxC,WAAA,CAAAC,YAAA,MAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,IAAA,cAAA,gBAAA,UAAA,2CAAA,mBAAA,uKAAA,iHAAA,KAAA,EAAA,EAGZ,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeO,KAAM,OAAS,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,QAC/E,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,YACpC,CAAC,CAAEP,MAAAA,EAAO0e,OAAAA,CAAO,aAC9BA,OAAAA,IAAS1e,EAAAA,EAAMsC,cAANtC,YAAAA,EAAmBuH,WAAY,QAAQvH,EAAAA,EAAMsC,cAANtC,YAAAA,EAAmBM,SAAU,OACtE,CAAC,CAAEN,MAAAA,EAAO0e,OAAAA,CAAO,IAAOA,OAAAA,OAAAA,IAAS1e,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,uBAAyB,yBAEjE,CAAC,CAAElB,MAAAA,EAAO0e,OAAAA,CAAO,IACzCA,OAAAA,OAAAA,IAAS1e,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,uBAAyB,eAC5C,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM0C,cAAN1C,YAAAA,EAAmB2C,OAAQ,aAUjD,CAAC,CAAE3C,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,wBAOhC,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoBG,KAAM,OAI1D,CAAC,CAAEue,OAAAA,EAAQ1e,MAAAA,CAAM,IAAA,OAAM0e,OAAAA,GAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAQjB1e,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAY1C,EAGGyd,GAAkBhd,EAAAA,KAAIC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,4FAAA,cAAA,iCAAA,EAEX,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAIlC,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoBG,KAAM,OAC7C,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBG,KAAM,UAAS,EAQxDye,GAAyE,CAC7EC,QAAS,CAAExO,MAAO,SAAU,EAC5BvF,OAAQ,CAAEuF,MAAO,QAAS,EAC1BmH,QAAS,CAAEnH,MAAO,SAAU,EAC5B4H,WAAY,CAAE5H,MAAO,YAAa,EAClCoK,WAAY,CAAEpK,MAAO,YAAa,EAClCe,KAAM,CAAEf,MAAO,eAAgB,CACjC,EAQayO,GAA4CA,CAAC,CACxDC,UAAAA,EACAC,YAAAA,EACAlc,UAAAA,CACF,IAAM,CACEmc,MAAAA,EAAkBC,GAAyB,CAC/CF,EAAYE,CAAG,CAAA,EAIf,OAAA5b,EAAA,IAACkb,GAAc,CAAA,UAAA1b,EACXqc,SAAO/L,OAAAA,QAAQwL,EAAU,EAA8D1T,IACvF,CAAC,CAACkU,EAAQxN,CAAM,IACb9M,EAAA,KAAA2Z,GAAA,CAEC,OAAQM,IAAcK,EACtB,QAAS,IAAMH,EAAeG,CAAM,EACpC,gBAAeL,IAAcK,EAC7B,KAAK,MAEJxN,SAAAA,CAAOvB,EAAAA,MACPuB,EAAOyN,OAAU/b,MAAAqb,GAAA,CAAU/M,WAAOyN,MAAM,CAAA,GAPpCD,CAQP,CAEJ,CACF,CAAA,CAEJ,EC7HM3Z,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAM3B,EAAA,CAAA,2HAAA,CAAA,EAGKwd,GAAsB5b,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAO9B,EAAA,CAAA,qMAAA,CAAA,EAEKyd,GAAqB7b,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAI7B,EAAA,CAAA,2CAAA,CAAA,EAEKuF,GAAeC,EAAAA,GAAE1F,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAMtB,EAAA,CAAA,6EAAA,CAAA,EAEK0d,GAAuB9b,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CA4B/B,EAAA,CAAA,2TAAA,CAAA,EAEKsG,GAAuB1E,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAG/B,EAAA,CAAA,wBAAA,CAAA,EAGK2d,GAAuB/b,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAE/B,EAAA,CAAA,eAAA,CAAA,EAEK4d,GAAoBhc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAU5B,EAAA,CAAA,iKAAA,CAAA,EAEK6d,EAAqBjc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAU7B,EAAA,CAAA,mLAAA,CAAA,EAEK8d,EAAqBlc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAkB7B,EAAA,CAAA,gQAAA,CAAA,EAEK+d,GAA4Bnc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAIpC,EAAA,CAAA,sDAAA,CAAA,EAEKge,GAAmBC,EAAAA,MAAKne,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAoB7B,EAAA,CAAA,oWAAA,CAAA,EAEKke,EAAuBtc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAI/B,EAAA,CAAA,sCAAA,CAAA,EAEKme,EAAYje,EAAOoE,EAAG,EAACxE,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0BAAA,eAAA,UAAA,qBAAA,4EAAA,EAEhB,CAAC,CAAEoe,SAAAA,CAAS,IAAOA,EAAW,EAAI,GAC/B,CAAC,CAAEA,SAAAA,EAAUvf,QAAAA,CAAQ,IAAM,CACvC,GAAI,CAACuf,EAAiB,MAAA,wBACtB,OAAQvf,EAAO,CACb,IAAK,UACI,MAAA,uBACT,IAAK,QACI,MAAA,qBACT,IAAK,OACI,MAAA,oBACT,IAAK,UACI,MAAA,UACT,IAAK,YACI,MAAA,uBACT,QACS,MAAA,uBACX,CACF,EACS,CAAC,CAAEuf,SAAAA,CAAS,IAAOA,EAAW,UAAY,wBAE/C,CAAC,CAAEA,SAAAA,EAAUvf,QAAAA,CAAQ,IAAM,CAC3B,GAAI,CAACuf,EAAiB,MAAA,wBACtB,OAAQvf,EAAO,CACb,IAAK,UACI,MAAA,uBACT,IAAK,QACI,MAAA,qBACT,IAAK,OACI,MAAA,oBACT,IAAK,UACI,MAAA,UACT,IAAK,YACI,MAAA,uBACT,QACS,MAAA,uBACX,CACF,CAAC,EASCwf,GAAmBzc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAO3B,EAAA,CAAA,0JAAA,CAAA,EAEKse,GAAqB1c,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAI7B,EAAA,CAAA,6DAAA,CAAA,EAEKue,GAAuB3c,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAG/B,EAAA,CAAA,wBAAA,CAAA,EAEKwe,GAAsBlc,EAAAA,OAAMxC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,qBAAA,4LAAA,iEAAA,EAClB,CAAC,CAAEnB,QAAAA,CAAQ,IAAOA,IAAY,UAAY,qBAAuB,cACtE,CAAC,CAAEA,QAAAA,CAAQ,IAAOA,IAAY,UAAY,UAAY,wBAE3D,CAAC,CAAEA,QAAAA,CAAQ,IAAOA,IAAY,UAAY,qBAAuB,wBAYrD,CAAC,CAAEA,QAAAA,CAAQ,IACvBA,IAAY,UAAY,uBAAyB,uBAAuB,EASjE4f,GAA0CA,CAAC,CAAEzd,UAAAA,CAAU,IAAM,OAClE,KAAA,CAAE0T,QAAAA,EAAS6G,cAAAA,EAAeI,aAAAA,EAAc9K,KAAAA,GAAS+K,EAAiB,EAGlE,CAAC8C,EAAcC,CAAe,EAAIzW,WAAuBwM,CAAO,EAGhEkK,EAAmB/N,GAAAA,MAAAA,EAAM7H,OAAS,CAAC,GAAG,IAAI6V,IAAIhO,EAAK7H,OAAOI,OAAaE,EAAMwM,MAAM,CAAC,CAAC,EAAI,GAEzFgJ,EAAsBjO,GAAAA,MAAAA,EAAM7H,OAC9B,CAAC,GAAG,IAAI6V,IAAIhO,EAAK7H,OAAOI,OAAaE,EAAM6N,QAAQ,CAAC,CAAC,EACrD,GAEE4H,EAAgBlO,GAAAA,MAAAA,EAAM7H,OACxB,CAAC,GAAG,IAAI6V,IAAIhO,EAAK7H,OAAOgW,QAAQ1V,GAASA,EAAMiN,MAAQ,CAAA,CAAE,CAAC,CAAC,EAC3D,GAGE0I,EAAqC,CAAC,OAAQ,OAAO,EAGrDC,EAA+B,CAAC,MAAO,OAAQ,WAAW,EAG1DC,EAAqC,CAAC,KAAM,KAAM,MAAO,MAAO,KAAM,KAAM,OAAO,EAGnFC,EAAmC,CAAC,aAAc,UAAW,aAAa,EAG1EC,EAAoBhC,OAAO5L,OAAOiN,CAAY,EAAElV,OAAgBpB,GAChEkX,MAAMC,QAAQnX,CAAK,EAAUA,EAAMa,OAAS,EAC5C,OAAOb,GAAU,UAAYA,IAAU,KAAaiV,OAAO5L,OAAOrJ,CAAK,EAAEuN,QAAU6J,CAAC,EAC1DpX,GAAU,MAAQA,IAAU,EAC3D,EAAEa,OAEG+B,IAAc6F,EAAAA,GAAAA,YAAAA,EAAM7H,SAAN6H,YAAAA,EAAc5H,SAAU,EAGtCwW,EAAmBA,CAACC,EAAgCtX,IAAkB,CAC1EuW,EAAyBgB,IAAA,CACvB,GAAGA,EACHpK,UAAW,CACT,GAAGoK,EAAKpK,UACR,CAACmK,CAAK,EAAGtX,CACX,CACA,EAAA,CAAA,EAIEwX,EAAqB,CACzBF,EAIAtX,IACG,CACHuW,EAAwBgB,GAAA,CACtB,MAAME,EAAiBF,EAAKD,CAAK,GAAa,CAAA,EACxCI,EAAYD,EAAchK,SAASzN,CAAK,EAC1CyX,EAAcrW,OAAYgW,GAAAA,IAAMpX,CAAK,EACrC,CAAC,GAAGyX,EAAezX,CAAK,EAErB,MAAA,CACL,GAAGuX,EACH,CAACD,CAAK,EAAGI,EAAU7W,OAAS,EAAI6W,EAAYve,MAAAA,CAC9C,CACD,CAAA,EAIGwe,EAAeA,IAAM,CACzBxE,EAAcmD,CAAY,CAAA,EAItBsB,EAAqBA,IAAM,CAClBrE,IACbgD,EAAgBjK,CAAO,CAAA,EAInBuL,EAAa,CACjBP,EAIAtX,IACY,CACNqJ,MAAAA,EAASiN,EAAagB,CAAK,EACjC,OAAOjO,EAASA,EAAOoE,SAASzN,CAAK,EAAI,EAAA,EAIzC,OAAApF,OAACW,IAAU,UAAA3C,EACT,SAAA,CAAAgC,OAACwa,GACC,CAAA,SAAA,CAAAxa,OAACya,GACC,CAAA,SAAA,CAAAjc,EAAAA,IAAC+D,IAAM,SAAc,gBAAA,CAAA,EACrB/D,EAAAA,IAACkc,IAAc,SAAI,MAAA,CAAA,CAAA,EACrB,EACAlc,EAAAA,IAAC8E,IACC,SAAC9E,EAAA,IAAAgd,GAAA,CAAa,QAAQ,YAAY,QAASwB,EAAmB,SAAA,OAAA,CAE9D,CACF,CAAA,CAAA,EACF,EAEAxe,EAAA,IAACmc,GACC,CAAA,SAAA3a,EAAAA,KAAC4a,GACC,CAAA,SAAA,CAAA5a,OAAC6a,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAU,YAAA,CAAA,SACtBC,GACC,CAAA,SAAA,CAAAvc,MAACwc,IACC,KAAK,OACL,MAAOU,EAAanJ,UAAU8C,UAC9B,SAAeoH,GAAAA,EAAiB,YAAa/a,EAAEwb,OAAO9X,KAAK,EAC3D,YAAY,aAAY,QAEzB4V,GACC,CAAA,KAAK,OACL,MAAOU,EAAanJ,UAAU+C,QAC9B,SAAU5T,GAAK+a,EAAiB,UAAW/a,EAAEwb,OAAO9X,KAAK,EACzD,YAAY,WAAU,CAAA,EAE1B,CAAA,EACF,SAECyV,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAS,WAAA,CAAA,EACtBtc,EAAAA,IAAC0c,EACEe,CAAAA,SAAAA,EAAiB7V,IAAI0C,GACnBtK,EAAA,IAAA2c,EAAA,CAEC,QAASrS,IAAc,OAAS,UAAY,QAC5C,SAAUmU,EAAW,aAAcnU,CAAS,EAC5C,QAAS,IAAM8T,EAAmB,aAAc9T,CAAS,EAExDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAEC+R,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAM,QAAA,CAAA,EAClBtc,EAAA,IAAA0c,EAAA,CACEgB,SAAc9V,EAAAA,IACb2N,GAAAvV,EAAA,IAAC2c,EAEC,CAAA,QAASpH,IAAW,MAAQ,UAAYA,IAAW,OAAS,QAAU,OACtE,SAAUkJ,EAAW,WAAYlJ,CAAM,EACvC,QAAS,IAAM6I,EAAmB,WAAY7I,CAAM,EAEnDA,SALIA,CAAAA,EAAAA,CAMP,CACD,EACH,CAAA,EACF,EAEC6H,EAAiB3V,OAAS,GACzBjG,EAAA,KAAC6a,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAO,SAAA,CAAA,EACpBtc,EAAAA,IAAC0c,GACEU,SAAiBxV,EAAAA,OACf5H,EAAA,IAAA2c,EAAA,CAEC,QAAQ,UACR,SAAU8B,EAAW,UAAWnK,CAAM,EACtC,QAAS,IAAM8J,EAAmB,UAAW9J,CAAM,EAElDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,EAGDgJ,EAAoB7V,OAAS,GAC5BjG,EAAA,KAAC6a,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAU,YAAA,CAAA,EACvBtc,EAAAA,IAAC0c,GACEY,SAAoB1V,EAAAA,OAClB5H,EAAA,IAAA2c,EAAA,CAEC,QAAQ,YACR,SAAU8B,EAAW,aAAc9I,CAAQ,EAC3C,QAAS,IAAMyI,EAAmB,aAAczI,CAAQ,EAEvDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAGD0G,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAS,WAAA,CAAA,EACtBtc,EAAAA,IAAC0c,GACEiB,SAAiB/V,EAAAA,OACf5H,EAAA,IAAA2c,EAAA,CAEC,QAAQ,UACR,SAAU8B,EAAW,aAAc/I,CAAS,EAC5C,QAAS,IAAM0I,EAAmB,aAAc1I,CAAS,EAExDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,SAEC2G,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAO,SAAA,CAAA,EACpBtc,EAAAA,IAAC0c,GACEkB,SAAehW,EAAAA,OACb5H,EAAA,IAAA2c,EAAA,CAEC,QAAQ,UACR,SAAU8B,EAAW,WAAYrU,CAAO,EACxC,QAAS,IAAMgU,EAAmB,WAAYhU,CAAO,EAEpDA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,EAECmT,EAAc9V,OAAS,GACtBjG,EAAA,KAAC6a,EACC,CAAA,SAAA,CAAArc,EAAAA,IAACsc,GAAY,SAAI,MAAA,CAAA,EACjBtc,EAAAA,IAAC0c,GACEa,SAAc3V,EAAAA,OACZ5H,EAAA,IAAA2c,EAAA,CAEC,QAAQ,OACR,SAAU8B,EAAW,OAAQ1G,CAAG,EAChC,QAAS,IAAMqG,EAAmB,OAAQrG,CAAG,EAE5CA,SAAAA,CAAAA,EALIA,CAMP,CACD,CACH,CAAA,CAAA,EACF,CAAA,CAAA,CAEJ,CACF,CAAA,SAEC8E,GACC,CAAA,SAAA,CAAC7c,MAAA8c,GAAA,CACEe,SAAoBA,EAAA,EACjB,GAAGA,WACDA,EAAoB,EAAI,IAAM,eACnBrU,WACb,GAAGA,gCACT,EACAxJ,EAAAA,IAAC+c,IACC,SAAC/c,EAAA,IAAAgd,GAAA,CAAa,QAAQ,UAAU,QAASuB,EAAa,SAAA,eAAA,CAEtD,CACF,CAAA,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EClgBMpc,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGnB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAGlCwhB,EAAoBve,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,sCAAA,EACP,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,WAC/B,CAAC,CAAEpF,MAAAA,CAAM,IAAMA,EAAMqC,aAAa9B,GACxC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAKtCyhB,EAAqBxe,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUG,GACnC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,cACpB,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAG5CgiB,EAAqBze,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACf,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUuD,GAC7B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMsC,YAAYiF,SACvC,CAAC,CAAEvH,MAAAA,EAAOoiB,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiBpiB,EAAMiB,OAAOqhB,OAC9BD,EAAiBriB,EAAMiB,OAAOshB,KAC3BviB,EAAMiB,OAAO4C,WACrB,EAGG2e,GAAsB9e,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,eAAA,UAAA,GAAA,EAChB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUD,GAC9B,CAAC,CAAEH,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,GAClC,CAAC,CAAEH,MAAAA,EAAOoiB,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiBpiB,EAAMiB,OAAOqhB,OAC9BD,EAAiBriB,EAAMiB,OAAOshB,KAC3BviB,EAAMiB,OAAOQ,aACrB,EAGUghB,GAAwDA,CAAC,CAAE3f,UAAAA,CAAU,IAAM,CAChF,KAAA,CAAE6P,KAAAA,GAAS+K,EAAiB,EAElC,GAAI,CAAC/K,EACI,OAAA,KAGH,KAAA,CAAE6B,QAAAA,CAAY7B,EAAAA,EAEd+P,EAAkBxY,GACf,IAAIyY,KAAKC,aAAa,QAAS,CACpC7f,MAAO,WACP8f,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAO9Y,CAAK,EAGX+Y,EAAiB/Y,GACd,GAAGA,EAAM2L,QAAQ,CAAC,KAIzB,OAAA/Q,OAACW,IAAU,UAAA3C,EACT,SAAA,CAAAgC,OAACmd,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAS,WAAA,CAAA,EACrB5e,EAAA,IAAA6e,EAAA,CAAY,SAAU3N,EAAQ9I,gBAAkB,EAAG,SAAU8I,EAAQ9I,gBAAkB,EACrFgX,SAAelO,EAAAA,EAAQ9I,eAAe,EACzC,CAAA,EACF,SAECuW,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAQ,UAAA,CAAA,EACpB5e,EAAA,IAAA6e,EAAA,CAAY,SAAU3N,EAAQ9H,QAAU,GAAI,SAAU8H,EAAQ9H,QAAU,GACtEuW,SAAczO,EAAAA,EAAQ9H,OAAO,EAChC,SACC8V,GACEhO,CAAAA,SAAAA,CAAQnJ,EAAAA,cAAc,MAAImJ,EAAQ1H,YAAY,SAAA,EACjD,CAAA,EACF,SAECmV,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAa,eAAA,CAAA,EACzB5e,EAAA,IAAA6e,EAAA,CAAY,SAAU3N,EAAQ7H,aAAe,EAAG,SAAU6H,EAAQ7H,aAAe,EAC/E6H,SAAAA,EAAQ7H,aAAakJ,QAAQ,CAAC,EACjC,CAAA,EACF,SAECoM,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAU,YAAA,CAAA,EACtB5e,EAAA,IAAA6e,EAAA,CAAY,SAAU3N,EAAQ3H,WAAa,EAAG,SAAU2H,EAAQ3H,WAAa,EAC3E6V,SAAelO,EAAAA,EAAQ3H,UAAU,EACpC,CAAA,EACF,SAECoV,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAY,CAAA,SAAU,GAAOO,SAAelO,EAAAA,EAAQvI,UAAU,EAAE,CAAA,EACnE,SAECgW,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAY,cAAA,CAAA,EACzB5e,EAAAA,IAAC6e,EAAY,CAAA,SAAU,GAAOO,SAAAA,EAAe,CAAC3W,KAAKC,IAAIwI,EAAQtI,WAAW,CAAC,CAAE,CAAA,CAAA,EAC/E,SAEC+V,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAY,CAAA,SAAU,GAAOO,SAAelO,EAAAA,EAAQrI,UAAU,EAAE,CAAA,EACnE,SAEC8V,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAY,cAAA,CAAA,QACxBC,EAAY,CAAA,SAAU,GAAOO,SAAelO,EAAAA,EAAQpI,WAAW,EAAE,CAAA,EACpE,SAEC6V,EACC,CAAA,SAAA,CAAA3e,EAAAA,IAAC4e,GAAY,SAAY,cAAA,CAAA,EACzB5e,EAAAA,IAAC6e,EAAa3N,CAAAA,SAAAA,EAAQ1H,WAAY,CAAA,SACjC0V,GAAa,CAAA,SAAA,CAAA,iBAAehO,EAAQhI,gBAAgBqJ,QAAQ,CAAC,EAAE,MAAA,EAAI,CAAA,EACtE,CACF,CAAA,CAAA,CAEJ,ECtGMqN,GAAmBC,EAAAA,MAAKvhB,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,6DAAA,GAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcoF,aAAc,qBAI9B,CAAC,CAAEpF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,uBAAsB,EAGrFkiB,GAAkBC,EAAAA,GAAEzhB,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACV,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcoF,aAAc,oBAAmB,EAGxEke,GAAyBC,EAAAA,GAAE3hB,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,8CAAA,UAAA,WAAA,sHAAA,YAAA,aAAA,GAAA,EACpB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,QAC3C,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeO,KAAM,OAG3B,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,YAC1C,CAAC,CAAEP,MAAAA,EAAOwjB,QAAAA,CAAQ,aACzBA,OAAAA,IACIxjB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,yBACzBlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc6D,cAAe,WACzB,CAAC,CAAE4f,UAAAA,CAAU,IAAOA,EAAY,UAAY,UAK3B,CAAC,CAAEzjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc0jB,SAAU,yBAI9D,CAAC,CAAED,UAAAA,EAAWzjB,MAAAA,CAAM,IAAA,SACpByjB,OAAAA,GACA;AAAA,iBACSzjB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW;AAAA,sBACpBlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW;AAAA;AAAA,OAMvC,CAAC,CAAEuiB,UAAAA,CAAU,IACbA,GACA;AAAA;AAAA,KAED,EAICE,GAAkBhiB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,2BAAA,mCAAA,sBAAA,KAAA,EAEX,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAExC,CAAC,CAAEqjB,QAAAA,CAAQ,IAAOA,EAAU,EAAI,GAElC,CAAC,CAAExjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,wBAGnC,CAAC,CAAE0iB,WAAAA,CAAW,IAAOA,IAAe,MAAQ,IAAM,GAAI,EAIhEC,GAAuBngB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAK/B,EAAA,CAAA,gFAAA,CAAA,EAEKgiB,GAAoBniB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAE7B,EAAA,CAAA,SAAA,CAAA,EAOKiiB,GAAU,CACd,CACE3a,IAAK,YACLiH,MAAO,YACP2T,SAAU,GACVle,MAAO,OACT,EACA,CACEsD,IAAK,SACLiH,MAAO,SACP2T,SAAU,GACVle,MAAO,MACT,EACA,CACEsD,IAAK,YACLiH,MAAO,YACP2T,SAAU,GACVle,MAAO,MACT,EACA,CACEsD,IAAK,KACLiH,MAAO,aACP2T,SAAU,GACVle,MAAO,OACT,EACA,CACEsD,IAAK,aACLiH,MAAO,MACP2T,SAAU,GACVle,MAAO,OACT,EACA,CACEsD,IAAK,oBACLiH,MAAO,QACP2T,SAAU,GACVle,MAAO,MACT,EACA,CACEsD,IAAK,SACLiH,MAAO,SACP2T,SAAU,GACVle,MAAO,MACT,EACA,CACEsD,IAAK,KACLiH,MAAO,WACP2T,SAAU,GACVle,MAAO,OACT,EACA,CACEsD,IAAK,KACLiH,MAAO,OACP2T,SAAU,GACVle,MAAO,OACT,CAAC,EAaUme,GAAsDA,CAAC,CAClEC,UAAAA,EACAC,cAAAA,EACAC,OAAAA,CACF,IAAM,CACEC,MAAAA,EAAqBC,GAAgC,CACrDA,GAAaF,GACfA,EAAOE,CAAS,CAClB,EAGIC,EAAgBA,CAAC9Z,EAA4B6Z,IAAgC,EAC5E7Z,EAAMrB,MAAQ,SAAWqB,EAAMrB,MAAQ,MAAQkb,IAClD7Z,EAAM+Z,eAAe,EACrBJ,EAAOE,CAAS,EAClB,EAGF,aACGpB,GACC,CAAA,SAAA5f,EAAA,IAAC8f,IACEW,SAAQ7Y,GAAAA,IAAI,CAACuZ,EAAQlV,IACnBjM,EAAAA,IAAAggB,GAAA,CAEC,UAAWmB,EAAOT,SAClB,QAASS,EAAOrb,MAAQ8a,EACxB,MAAO,CAAEpe,MAAO2e,EAAO3e,KAAM,EAC7B,QAAS,IAAMue,EAAkBI,EAAOrb,GAAG,EAC3C,UAAY5C,GAAM+d,EAAc/d,EAAGie,EAAOrb,GAAG,EAC7C,SAAUqb,EAAOT,SAAW,EAAI,GAChC,KAAMS,EAAOT,SAAW,SAAW3gB,OACnC,YACEohB,EAAOrb,MAAQ8a,EACXC,IAAkB,MAChB,YACA,aACF9gB,OAEN,MACEohB,EAAOT,SACH,WAAWS,EAAOpU,SAChBoU,EAAOrb,MAAQ8a,EACXC,IAAkB,MAChB,eACA,cACF,KAEN9gB,OAGN,SAAAyB,EAAA,KAAC+e,GACC,CAAA,SAAA,CAACvgB,EAAAA,IAAAwgB,GAAA,CAAYW,WAAOpU,KAAM,CAAA,EACzBoU,EAAOT,UACL1gB,MAAAqgB,GAAA,CAAS,WAAYQ,EAAe,QAASM,EAAOrb,MAAQ8a,CAC9D,CAAA,CAAA,CAAA,CACH,CAhCK3U,EAAAA,CAiCP,CACD,CACH,CAAA,CACF,CAAA,CAEJ,EC3MM6T,GAAkBC,EAAAA,GAAEzhB,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,eAAA,+DAAA,oDAAA,wCAAA,EACG,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc0jB,SAAU,yBACpD,CAAC,CAAE1jB,MAAAA,EAAO0kB,YAAAA,CAAY,WAClCA,OAAAA,EAAc,KAAG1kB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,2BAA6B,eAMzD,CAAC,CAAElB,MAAAA,EAAO0kB,YAAAA,CAAY,IAClCA,SAAAA,OAAAA,EACI,KAAG1kB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,2BAC5B,KAAGlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,4BAEV,CAAC,CAAElB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,uBAAsB,EAQpFyjB,EAAmBC,EAAAA,GAAEhjB,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,oCAAA,UAAA,2BAAA,qCAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeO,KAAM,OAElC,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,YAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc6D,cAAe,WAC3B,CAAC,CAAE7D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc0jB,SAAU,wBAAuB,EAOpFmB,GAAiB7iB,EAAOY,EAAK,EAAChB,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAKnC,EAAA,CAAA,kFAAA,CAAA,EAEKgjB,GAAc9iB,EAAOY,EAAK,EAAChB,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAKhC,EAAA,CAAA,kFAAA,CAAA,EAEKijB,GAAoBpjB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,cAAA,GAAA,EACnB,CAAC,CAAE9B,MAAAA,EAAOglB,OAAAA,CAAO,IACxBA,WAAAA,OAAAA,EAAS,IACLhlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcqB,UAAW,uBACzB2jB,EAAS,IACThlB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcuB,QAAS,uBACvBvB,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,yBACtB,CAAC,CAAEujB,OAAAA,CAAO,IAAOA,IAAW,EAAI,IAAM,IACxC,CAAC,CAAEhlB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,WAAU,EAGzD0kB,GAAoBtjB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kEAAA,UAAA,GAAA,EAEf,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBG,KAAM,WAC1C,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,wBAAuB,EAG1Eue,GAAuBtc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,mCAAA,mBAAA,EAGvB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,MAAK,EAI5C+kB,GAAaljB,EAAO2iB,CAAS,EAAC/iB,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGzB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,uBAAsB,EAGnEikB,GAAWnjB,EAAO2iB,CAAS,EAAC/iB,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kEAAA,UAAA,mBAAA,EAEnB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBG,KAAM,WAC1C,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,wBAAuB,EAcnE2jB,GAAgDC,GAAMC,KACjE,CAAC,CAAEla,MAAAA,EAAO2W,WAAAA,EAAY/e,QAAAA,EAASuiB,WAAAA,EAAYC,SAAAA,CAAS,IAAM,SAClD,KAAA,CAAEC,WAAAA,EAAY/C,eAAAA,CAAmB6C,EAAAA,EACjC,CAAEG,oBAAAA,EAAqBC,iBAAAA,CAAqBH,EAAAA,EAE5CpN,EAAkBhK,GAClBA,EAAa,EAAU,MACvBA,EAAa,EAAU,OACpB,YAIP,OAAAtJ,EAAA,KAACse,GACC,CAAA,YAAarB,EACb,QAAA/e,EACA,KAAK,SACL,SAAU,EACV,UAAgBwD,GAAA,EACVA,EAAE4C,MAAQ,SAAW5C,EAAE4C,MAAQ,OACjC5C,EAAEge,eAAe,EACTxhB,IAEZ,EACA,gBAAe+e,EACf,MAAO,SAAS3W,EAAMwM,qBAAqBmK,EAAa,WAAa,WAGrE,SAAA,CAAAze,EAAA,IAAC6hB,GAAUM,CAAAA,SAAAA,EAAWra,EAAM2D,IAAI,EAAE,EAGlCzL,EAAAA,IAAC4hB,GAAY9Z,CAAAA,SAAAA,EAAMwM,MAAO,CAAA,QAGzB+M,EACC,CAAA,SAAArhB,EAAA,IAACuhB,IACC,WAAYzZ,EAAMwC,UAAU8J,YAAY,EACxC,QAASgO,EAAoBta,EAAMwC,SAAS,EAC5C,KAAK,QAEJxC,SAAAA,EAAMwC,SACT,CAAA,EACF,EAGAtK,EAAA,IAACqhB,EACC,CAAA,SAAA7f,EAAAA,KAACmgB,GACE7Z,CAAAA,SAAAA,CAAMmH,EAAAA,MAAM,MAAInH,EAAMwa,IAAAA,CAAAA,CACzB,CACF,CAAA,EAGAtiB,EAAA,IAACqhB,EACC,CAAA,SAAArhB,EAAAA,IAACyhB,GAAW,CAAA,OAAQ3Z,EAAMgD,WAAasU,SAAetX,EAAAA,EAAMgD,UAAU,CAAE,CAAA,EAC1E,EAGC9K,EAAA,IAAAqhB,EAAA,CACC,SAACrhB,EAAA,IAAAyhB,GAAA,CAAW,OAAQ3Z,EAAMgD,YAAc,EACrCsU,SAAetX,EAAAA,EAAMgD,YAAc,CAAC,CACvC,CAAA,EACF,EAGA9K,EAAAA,IAACqhB,GACC,SAACrhB,EAAA,IAAAwhB,GAAA,CACC,QAAS1M,EAAehN,EAAMgD,UAAU,EACxC,QAASuX,EAAiBvN,EAAehN,EAAMgD,UAAU,CAAC,EAC1D,KAAK,QAEJgK,SAAehN,EAAAA,EAAMgD,UAAU,CAAA,CAClC,CACF,CAAA,EAGC9K,EAAA,IAAAqhB,EAAA,CAAWvZ,SAAM6N,EAAAA,UAAY,IAAI,EAGlC3V,EAAA,IAACqhB,EACC,CAAA,SAAA7f,EAAAA,KAACkb,GACE5U,CAAAA,SAAAA,EAAAA,EAAAA,EAAMiN,OAANjN,YAAAA,EAAYya,MAAM,EAAG,GAAG3a,IAAI,CAACmQ,EAAK9L,IACjCjM,EAAAA,IAAC8C,IAAgB,KAAK,QAAQ,QAAQ,UACnCiV,SAAAA,GADO9L,CAEV,KAEDnE,EAAAA,EAAMiN,OAANjN,YAAAA,EAAYL,QAAS,UACnB3E,GAAI,CAAA,KAAK,QAAQ,QAAQ,YAAW,SAAA,CAAA,IACjCgF,EAAMiN,KAAKtN,OAAS,CAAA,EACxB,CAAA,CAAA,CAEJ,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,CACF,EAGAqa,GAAevjB,YAAc,iBChM7B,MAAMikB,GAAmBC,EAAAA,MAAKnkB,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcsF,UAAW,sBAAqB,EAGvE0gB,GAAkB3C,EAAAA,GAAEzhB,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAEzB,EAAA,CAAA,yBAAA,CAAA,EAEKmkB,GAAmBrB,EAAAA,GAAEhjB,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,+BAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAewH,KAAM,QAEtC,CAAC,CAAExH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,wBAAuB,EAenEykB,GAAkDb,GAAMC,KAAK,CAAC,CACzExa,OAAAA,EACAyR,gBAAAA,EACA4J,WAAAA,EACAZ,WAAAA,EACAC,SAAAA,CACF,IAEM,CAAC1a,GAAUA,EAAOC,SAAW,EAE7BzH,EAAAA,IAACwiB,GACC,CAAA,SAAAxiB,EAAAA,IAAC0iB,GACC,CAAA,SAAA1iB,EAAAA,IAAC2iB,IAAU,QAAS,EAAE,SAEtB,4CAAA,CAAA,CACF,CACF,CAAA,EAKF3iB,EAAA,IAACwiB,IACEhb,SAAOI,EAAAA,OACL5H,EAAA,IAAA8hB,GAAA,CAEC,MAAAha,EACA,WAAYA,EAAM+G,KAAOoK,EACzB,QAAS,IAAM4J,EAAW/a,EAAM+G,EAAE,EAClC,WAAAoT,EACA,SAAAC,CALKpa,EAAAA,EAAM+G,EAKQ,CAEtB,CACH,CAAA,CAEH,EAGD+T,GAAgBrkB,YAAc,kBCxDjBukB,MAAAA,GAAsBtb,GAA8C,CAC/E,KAAM,CAACoZ,EAAWmC,CAAY,EAAIrc,WAAoB,WAAW,EAC3D,CAACma,EAAemC,CAAgB,EAAItc,WAAwB,MAAM,EAKlEuc,EAAanJ,cAChBoE,GAAqB,CAChB0C,IAAc1C,EAEhB8E,EAA0B7E,GAAAA,IAAS,MAAQ,OAAS,KAAM,GAG1D4E,EAAa7E,CAAK,EAClB8E,EAAiB,MAAM,EACzB,EAEF,CAACpC,CAAS,CACZ,EAKMvU,EAAe6W,EAAAA,QAAQ,IACvB,CAAC1b,GAAUA,EAAOC,SAAW,EAAU,GAEpC,CAAC,GAAGD,CAAM,EAAEuD,KAAK,CAACC,EAAGC,IAAM,CAChC,IAAIkY,EAAa,EAEjB,OAAQvC,EAAS,CACf,IAAK,YACHuC,EAAa,IAAI3X,KAAKR,EAAES,IAAI,EAAEa,UAAY,IAAId,KAAKP,EAAEQ,IAAI,EAAEa,QAAQ,EACnE,MACF,IAAK,SACH6W,EAAanY,EAAEsJ,OAAO8O,cAAcnY,EAAEqJ,MAAM,EAC5C,MACF,IAAK,YACH6O,EAAanY,EAAEV,UAAU8Y,cAAcnY,EAAEX,SAAS,EAClD,MACF,IAAK,aACUU,EAAAA,EAAEF,WAAaG,EAAEH,WAC9B,MACF,IAAK,oBAEUE,EAAAA,EAAEF,WAAaG,EAAEH,WAC9B,MACF,IAAK,SAEGuY,MAAAA,EAAUrY,EAAEF,WAAa,EAAI,MAAQE,EAAEF,WAAa,EAAI,OAAS,YACjEwY,EAAUrY,EAAEH,WAAa,EAAI,MAAQG,EAAEH,WAAa,EAAI,OAAS,YAC1DuY,EAAAA,EAAQD,cAAcE,CAAO,EAC1C,MACF,QACeH,EAAA,CACjB,CAEOtC,OAAAA,IAAkB,MAAQsC,EAAa,CAACA,CAAAA,CAChD,EACA,CAAC3b,EAAQoZ,EAAWC,CAAa,CAAC,EAK/BoB,EAAaiB,EAAAA,QACjB,KAAO,CAILf,WAAaoB,GAA+B,CACtC,GAAA,CACI9X,MAAAA,EAAO,IAAID,KAAK+X,CAAU,EAChC,OAAIC,MAAM/X,EAAKa,QAAQ,CAAC,EACf,eAIPb,EAAKgY,mBAAmB,QAAS,CAC/BC,MAAO,QACPC,IAAK,UACLC,KAAM,SACP,CAAA,EACD,IACAnY,EAAKoY,mBAAmB,QAAS,CAC/B9V,KAAM,UACNE,OAAQ,UACR6V,OAAQ,EAAA,CACT,QAEI7lB,GACCsI,eAAAA,KAAK,yBAA0Bgd,EAAYtlB,CAAK,EACjD,cACT,CACF,EAKAmhB,eAAiBxY,GAA0B,CACrC,GAAA,CACK,OAAA,IAAIyY,KAAKC,aAAa,QAAS,CACpC7f,MAAO,WACP8f,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAO9Y,CAAK,QACR3I,GACCsI,eAAAA,KAAK,6BAA8BK,EAAO3I,CAAK,EAChD,IAAI2I,EAAM2L,QAAQ,CAAC,GAC5B,CACF,EAKAoN,cAAgB/Y,GAA0B,CACpC,GAAA,CAEF,MAAO,GADMA,EAAQ,EAAI,IAAM,KACdA,EAAM2L,QAAQ,CAAC,WACzBtU,GACCsI,eAAAA,KAAK,4BAA6BK,EAAO3I,CAAK,EAC/C,GAAG2I,IACZ,CACF,CAAA,GAEF,CACF,CAAA,EAKMsb,EAAWgB,EAAAA,QACf,KAAO,CAILd,oBAAsB9X,GAA8B,CAC1CA,OAAAA,EAAU8J,YAAa,EAAA,CAC7B,IAAK,OACI,MAAA,UACT,IAAK,QACI,MAAA,QACT,QACS,MAAA,SACX,CACF,EAKAiO,iBAAmB9M,GAA2B,CACpCA,OAAAA,EAAOnB,YAAa,EAAA,CAC1B,IAAK,MACI,MAAA,UACT,IAAK,OACI,MAAA,QACT,IAAK,YACI,MAAA,UACT,IAAK,OACI,MAAA,OACT,QACS,MAAA,SACX,CACF,CAAA,GAEF,CACF,CAAA,EAEO,MAAA,CACL/H,aAAAA,EACAuU,UAAAA,EACAC,cAAAA,EACAoC,WAAAA,EACAhB,WAAAA,EACAC,SAAAA,CAAAA,CAEJ,ECpMM/f,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6DAAA,kBAAA,qBAAA,mBAAA,EAIZ,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcsF,UAAW,uBACrC,CAAC,CAAEtF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoB2D,KAAM,OACtC,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc0jB,SAAU,wBAAuB,EAI9E2D,GAAsB3jB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAI9B,EAAA,CAAA,yCAAA,CAAA,EAEKwlB,GAAeC,EAAAA,MAAK3lB,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iDAAA,eAAA,GAAA,EAGX,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,YACrC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcsF,UAAW,sBAAqB,EAGvEwC,GAAoBpE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,4BAAA,oBAAA,EAKhB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAewH,KAAM,QAEtC,CAAC,CAAExH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,wBAAuB,EAI1E+lB,GAAmB9jB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,eAAA,EAET,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,OAAM,EAIvDgnB,GAAoBngB,EAAAA,GAAE1F,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiB2D,KAAM,YAE1C,CAAC,CAAE3D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc6D,cAAe,WACvC,CAAC,CAAE7D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeO,KAAM,MAAK,EAGnDmnB,GAA0BhgB,EAAAA,EAAC9F,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EAClB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMI,YAANJ,YAAAA,EAAiBO,KAAM,YAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcyB,gBAAiB,wBAAuB,EAK1EkmB,GAA4BA,IAChC7iB,EAAAA,KAACgD,GACC,CAAA,SAAA,CAAAxE,EAAAA,IAACkkB,IAAU,SAAC,GAAA,CAAA,EACZlkB,EAAAA,IAACmkB,IAAW,SAAc,gBAAA,CAAA,EAC1BnkB,EAAAA,IAACokB,IAAiB,SAA8C,gDAAA,CAAA,CAAA,CAClE,CAAA,EAUIE,GAA+BA,IAAM,CACnC,KAAA,CAAEjV,KAAAA,EAAM4J,gBAAAA,EAAiBgB,YAAAA,GAAgBG,EAAiB,EAC1D,CAAE/N,aAAAA,EAAcuU,UAAAA,EAAWC,cAAAA,EAAeoC,WAAAA,EAAYhB,WAAAA,EAAYC,SAAAA,CACtEY,EAAAA,IAAmBzT,GAAAA,YAAAA,EAAM7H,SAAU,CAAE,CAAA,EAGnC,GAAA,CAAC6H,GAAQ,CAACA,EAAK7H,QAAU6H,EAAK7H,OAAOC,SAAW,EAClD,cACGjD,GACC,CAAA,SAAA,CAAAxE,EAAAA,IAACkkB,IAAU,SAAE,IAAA,CAAA,EACblkB,EAAAA,IAACmkB,IAAW,SAAe,iBAAA,CAAA,EAC3BnkB,EAAAA,IAACokB,IAAgB,SAGjB,6GAAA,CAAA,CACF,CAAA,CAAA,EAKEG,MAAAA,EAAkBrK,GAAoB,CAC9BA,EAAAA,IAAYjB,EAAkB,KAAOiB,CAAO,CAAA,EAIxD,OAAAla,EAAAA,IAAC+jB,GACC,CAAA,SAAAviB,EAAAA,KAACwiB,GACC,CAAA,SAAA,CAAAhkB,EAAA,IAAC2gB,GACC,CAAA,UAAAC,EACA,cAAAC,EACA,OAAQoC,EAAW,EAErBjjB,MAAC4iB,IACC,OAAQvW,EACR,gBAAA4M,EACA,WAAYsL,EACZ,WAAAtC,EACA,SAAAC,EAAmB,CAAA,CAEvB,CAAA,CACF,CAAA,CAEJ,EAWasC,GAA4DA,CAAC,CAAEhlB,UAAAA,CAAU,IAEjFQ,EAAA,IAAAmC,GAAA,CAAU,UAAA3C,EACT,SAAAQ,MAACykB,EAAAA,SAAS,CAAA,SAAWzkB,EAAAA,IAAAqkB,GAAA,CAAe,CAAA,EAClC,SAAArkB,EAAA,IAACskB,GAAkB,EAAA,CAAA,CACrB,CACF,CAAA,EC/HSI,GAAqDhjB,GACzD1B,EAAA,IAACwkB,GAAyB9iB,CAAAA,GAAAA,CAAS,CAAA,ECPtCS,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAE3B,EAAA,CAAA,kBAAA,CAAA,EAEKwlB,GAAeC,EAAAA,MAAK3lB,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iDAAA,GAAA,EAGX,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUG,EAAE,EAG1C2iB,GAAmBC,EAAAA,MAAKvhB,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EACR,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,UAAU,EAGtD0gB,GAAmBC,EAAAA,MAAKnkB,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAE1BshB,GAAkBC,EAAAA,GAAEzhB,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2BAAA,6BAAA,IAAA,EACG,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOyiB,OAGjC,CAAC,CAAE1jB,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,UAAU,EAIxDke,EAAyBC,EAAAA,GAAE3hB,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,gCAAA,UAAA,WAAA,YAAA,GAAA,EACpB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAEzB,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMsC,YAAYiF,SACvC,CAAC,CAAEvH,MAAAA,EAAO0e,OAAAA,CAAO,IAAOA,EAAS1e,EAAMiB,OAAOC,QAAUlB,EAAMiB,OAAO4C,YACpE,CAAC,CAAEmgB,SAAAA,CAAS,IAAOA,EAAW,UAAY,UAGhD,CAAC,CAAEA,SAAAA,EAAUhkB,MAAAA,CAAM,IACnBgkB,GACA;AAAA,eACShkB,EAAMiB,OAAOC;AAAAA,KACvB,EAICyjB,EAAmBC,EAAAA,GAAEhjB,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAGtCojB,EAAkBhiB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,sBAAA,KAAA,EAEX,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,GAG9B,CAAC,CAAEyN,UAAAA,CAAU,IAAOA,IAAc,MAAQ,IAAM,GAAI,EAI9Dqa,GAAsBvkB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+BAAA,kBAAA,+BAAA,GAAA,EAET,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,WAC/B,CAAC,CAAEpF,MAAAA,CAAM,IAAMA,EAAMqC,aAAa4D,KAErC,CAAC,CAAEjG,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAGzC+nB,GAAaxkB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,MAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,qBAAA,qBAAA,GAAA,EAEX,CAAC,CAAEgE,MAAAA,CAAM,IAAM,GAAGA,KACP,CAAC,CAAE9F,MAAAA,EAAOoiB,SAAAA,CAAS,IACrCA,EAAWpiB,EAAMiB,OAAOqhB,OAAStiB,EAAMiB,OAAOshB,IAAI,EAGhDwC,GAAoBpjB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,GAAA,EACnB,CAAC,CAAE9B,MAAAA,EAAOkK,MAAAA,CAAM,IACvBA,EAAQ,EAAIlK,EAAMiB,OAAOqhB,OAASpY,EAAQ,EAAIlK,EAAMiB,OAAOshB,KAAOviB,EAAMiB,OAAOQ,cAClE,CAAC,CAAEzB,MAAAA,EAAOkK,MAAAA,CAAM,IAC7BA,IAAU,EAAIlK,EAAMsC,YAAYhC,OAASN,EAAMsC,YAAY6lB,OAAO,EAGhErgB,GAAoBpE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQ0D,GAE/B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,aAAa,EAIvC2mB,EAAoEA,CAAC,CAChFtlB,UAAAA,EACAoK,SAAAA,EACAnF,MAAAA,CACF,IAAM,CACE,KAAA,CAAE4K,KAAAA,GAAS+K,EAAiB,EAC5B,CAACwG,EAAWmC,CAAY,EAAIrc,WAAoB,YAAY,EAC5D,CAACma,EAAemC,CAAgB,EAAItc,WAAwB,MAAM,EAExE,GAAI,CAAC2I,EACI,OAAA,KAGT,IAAI0V,EAAyC,CAAA,EAE7C,OAAQnb,EAAQ,CACd,IAAK,SACHmb,EAAkB1V,EAAK8B,kBACvB,MACF,IAAK,WACH4T,EAAkB1V,EAAK+B,oBACvB,MACF,IAAK,YACH2T,EAAkB1V,EAAKyE,qBACvB,MACF,IAAK,UACHiR,EAAkB1V,EAAKgC,mBACvB,KACJ,CAEA,GAAI,CAAC0T,GAAmBA,EAAgBtd,SAAW,EACjD,cAAQjD,GAAW,CAAA,SAAA,CAAA,MAAIoF,EAAS,8BAA4B,CAAA,CAAA,EAGxDqZ,MAAAA,EAAc/E,GAAqB,CACnC0C,IAAc1C,EAEC2C,EAAAA,IAAkB,MAAQ,OAAS,KAAK,GAGzDkC,EAAa7E,CAAK,EAClB8E,EAAiB,MAAM,EACzB,EAIIgC,EAAa,CAAC,GAAGD,CAAe,EAAEha,KAAK,CAACC,EAAGC,IAAM,CACrD,IAAIkY,EAAa,EAEjB,OAAQvC,EAAS,CACf,IAAK,QACHuC,EAAanY,EAAEpE,MAAMwc,cAAcnY,EAAErE,KAAK,EAC1C,MACF,IAAK,SACUoE,EAAAA,EAAExD,OAASyD,EAAEzD,OAC1B,MACF,IAAK,UACUwD,EAAAA,EAAE5B,QAAU6B,EAAE7B,QAC3B,MACF,IAAK,aACU4B,EAAAA,EAAEF,WAAaG,EAAEH,WAC9B,MACF,IAAK,oBACUE,EAAAA,EAAEH,kBAAoBI,EAAEJ,kBACrC,MACF,QACesY,EAAA,CACjB,CAEOtC,OAAAA,IAAkB,MAAQsC,EAAa,CAACA,CAAAA,CAChD,EAGK8B,EAAgBxc,KAAK5I,IAAI,GAAGklB,EAAgBnd,IAAca,GAAAA,KAAKC,IAAIxC,EAAK4E,UAAU,CAAC,CAAC,EAEpFsU,EAAkBxY,GACf,IAAIyY,KAAKC,aAAa,QAAS,CACpC7f,MAAO,WACP8f,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAO9Y,CAAK,EAGX+Y,EAAiB/Y,GACd,GAAGA,EAAM2L,QAAQ,CAAC,KAG3B,OACGvS,EAAA,IAAAmC,GAAA,CAAU,UAAA3C,EACT,SAAAgC,OAACwiB,GACC,CAAA,SAAA,CAAChkB,EAAA,IAAA4f,GAAA,CACC,gBAACE,GACC,CAAA,SAAA,CAACte,EAAAA,KAAAwe,EAAA,CACC,SAAQ,GACR,OAAQY,IAAc,QACtB,QAAS,IAAMqC,EAAW,OAAO,EAEhCxe,SAAAA,CAAAA,EACAmc,IAAc,SAAY5gB,EAAA,IAAAqgB,EAAA,CAAS,UAAWQ,EAAiB,CAAA,EAClE,EAEArf,EAAAA,KAACwe,EACC,CAAA,SAAQ,GACR,OAAQY,IAAc,SACtB,QAAS,IAAMqC,EAAW,QAAQ,EAAE,SAAA,CAAA,SAGnCrC,IAAc,UAAa5gB,EAAA,IAAAqgB,EAAA,CAAS,UAAWQ,EAAiB,CAAA,EACnE,EAEArf,EAAAA,KAACwe,EACC,CAAA,SAAQ,GACR,OAAQY,IAAc,UACtB,QAAS,IAAMqC,EAAW,SAAS,EAAE,SAAA,CAAA,WAGpCrC,IAAc,WAAc5gB,EAAA,IAAAqgB,EAAA,CAAS,UAAWQ,EAAiB,CAAA,EACpE,EAEArf,EAAAA,KAACwe,EACC,CAAA,SAAQ,GACR,OAAQY,IAAc,aACtB,QAAS,IAAMqC,EAAW,YAAY,EAAE,SAAA,CAAA,MAGvCrC,IAAc,cAAiB5gB,EAAA,IAAAqgB,EAAA,CAAS,UAAWQ,EAAiB,CAAA,EACvE,EAEArf,EAAAA,KAACwe,EACC,CAAA,SAAQ,GACR,OAAQY,IAAc,oBACtB,QAAS,IAAMqC,EAAW,mBAAmB,EAAE,SAAA,CAAA,UAG9CrC,IAAc,qBAAwB5gB,EAAA,IAAAqgB,EAAA,CAAS,UAAWQ,EAAiB,CAAA,EAC9E,CAAA,CAAA,CACF,CACF,CAAA,EAEA7gB,EAAAA,IAACwiB,IACEwC,SAAWpd,EAAAA,IAAI,CAAC1B,EAAM+F,WACpB6T,GACC,CAAA,SAAA,CAAC9f,EAAAA,IAAAqhB,EAAA,CAAWnb,WAAKU,KAAM,CAAA,EACvB5G,EAAAA,IAACqhB,EAAWnb,CAAAA,SAAAA,EAAKsB,MAAO,CAAA,EACvBxH,EAAA,IAAAqhB,EAAA,CAAW1B,SAAczZ,EAAAA,EAAKkD,OAAO,EAAE,SACvCiY,EACC,CAAA,SAAA,CAAArhB,EAAAA,IAACyhB,IAAW,MAAOvb,EAAK4E,WAAasU,SAAelZ,EAAAA,EAAK4E,UAAU,EAAE,EACrE9K,EAAAA,IAAC2kB,IACC,SAAC3kB,MAAA4kB,GAAA,CACC,MAAOnc,KAAKM,IAAI,IAAMN,KAAKC,IAAIxC,EAAK4E,UAAU,EAAIma,EAAiB,GAAG,EACtE,SAAU/e,EAAK4E,YAAc,CAAE,CAAA,EAEnC,CAAA,EACF,EACA9K,EAAA,IAACqhB,EACC,CAAA,SAAArhB,EAAAA,IAACyhB,GAAW,CAAA,MAAOvb,EAAK2E,kBACrBuU,SAAelZ,EAAAA,EAAK2E,iBAAiB,CACxC,CAAA,EACF,CAAA,GAjBaoB,CAkBf,CACD,EACH,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,EC3PM9J,GAAmB/B,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAE,EAAA,CAAA,EAAA,CAAA,EAExB0mB,GAAwB9kB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGxB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GACtB,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAGzCgoB,GAAkB/kB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGlB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAGlCuoB,GAAwBhlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAIhC,EAAA,CAAA,gEAAA,CAAA,EAEK6mB,GAAuBjlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACjB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUG,GAC7B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,OACvC,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMiB,OAAO4C,WAAW,EAG5C+kB,GAAyBllB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,cAAA,UAAA,GAAA,EAEzB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GACvB,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMI,UAAUD,GACnC,CAAC,CAAEH,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,aAAa,EAG9C0gB,GAAqBxgB,EAAAA,KAAIC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,gBAAA,GAAA,EACpB,CAAC,CAAE9B,MAAAA,EAAOoiB,SAAAA,EAAUC,SAAAA,CAAS,IAChCD,EAAiBpiB,EAAMiB,OAAOqhB,OAC9BD,EAAiBriB,EAAMiB,OAAOshB,KAC3BviB,EAAMiB,OAAOQ,cAEP,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,MAAM,EAGlD2nB,GAAsBvkB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,kBAAA,qCAAA,EAET,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMiB,OAAOmE,WAC/B,CAAC,CAAEpF,MAAAA,CAAM,IAAMA,EAAMqC,aAAa9B,EAAE,EAKjD2nB,GAAaxkB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,MAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qBAAA,qBAAA,8BAAA,EAEX,CAAC,CAAEgE,MAAAA,CAAM,IAAM,GAAGA,KACP,CAAC,CAAE9F,MAAAA,EAAOoiB,SAAAA,CAAS,IACrCA,EAAWpiB,EAAMiB,OAAOqhB,OAAStiB,EAAMiB,OAAOshB,IAAI,EAIhDsG,GAAkBnlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,0DAAA,UAAA,gBAAA,uCAAA,EAGjB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,GAIxB,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMI,UAAUD,GACnC,CAAC,CAAEH,MAAAA,CAAM,IAAMA,EAAMiB,OAAOE,YACtB,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,MAAM,EAIlDwH,GAAoBpE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQ0D,GAE/B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,aAAa,EAIvCqnB,EAA4DA,CAAC,CACxEhmB,UAAAA,EACA2L,SAAAA,CACF,IAAM,CACE,KAAA,CAAEkE,KAAAA,GAAS+K,EAAiB,EAElC,GAAI,CAAC/K,EACI,OAAA,KAGT,MAAM0V,EACJ5Z,IAAa,YAAckE,EAAKmC,qBAAuBnC,EAAKoC,qBAE9D,GAAI,CAACsT,GAAmBA,EAAgBtd,SAAW,EACjD,cAAQjD,GAAW,CAAA,SAAA,CAAA,MAAI2G,EAAS,8BAA4B,CAAA,CAAA,EAI9D,MAAM8Z,EAAgBxc,KAAK5I,IAAI,GAAGklB,EAAgBnd,IAAYa,GAAAA,KAAKC,IAAIxC,EAAK4E,UAAU,CAAC,CAAC,EAElFsU,EAAkBxY,GACf,IAAIyY,KAAKC,aAAa,QAAS,CACpC7f,MAAO,WACP8f,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAO9Y,CAAK,EAGX+Y,EAAiB/Y,GACd,GAAGA,EAAM2L,QAAQ,CAAC,KAIzB,OAAAvS,EAAAA,IAACmC,GAAU,CAAA,UAAA3C,EACT,SAACQ,EAAAA,IAAAklB,GAAA,CACEH,SAAgBnd,EAAAA,IAAI,CAAC1B,EAAM+F,IAC1BzK,EAAAA,KAAC2jB,GACC,CAAA,SAAA,CAAA3jB,OAAC4jB,GACC,CAAA,SAAA,CAACplB,EAAAA,IAAAqlB,GAAA,CAAenf,WAAKoF,QAAS,CAAA,SAC7Bga,GACC,CAAA,SAAA,CAAA9jB,OAAC,MAAG,CAAA,SAAA,CAAA,WACMxB,EAAAA,IAAC6e,GAAa3Y,CAAAA,SAAAA,EAAKsB,MAAO,CAAA,CAAA,EACpC,SACC,MAAG,CAAA,SAAA,CAAA,YACQ,IACTxH,EAAA,IAAA6e,GAAA,CAAY,SAAU3Y,EAAKkD,QAAU,GAAI,SAAUlD,EAAKkD,QAAU,GAChEuW,SAAczZ,EAAAA,EAAKkD,OAAO,EAC7B,CAAA,EACF,SACC,MAAG,CAAA,SAAA,CAAA,OACG,IACJpJ,EAAA,IAAA6e,GAAA,CAAY,SAAU3Y,EAAK4E,WAAa,EAAG,SAAU5E,EAAK4E,WAAa,EACrEsU,SAAelZ,EAAAA,EAAK4E,UAAU,EACjC,CAAA,EACF,CAAA,EACF,CAAA,EACF,EACC9K,EAAA,IAAA2kB,GAAA,CACC,SAAC3kB,EAAA,IAAA4kB,GAAA,CACC,MAAOnc,KAAKM,IAAI,IAAMN,KAAKC,IAAIxC,EAAK4E,UAAU,EAAIma,EAAiB,GAAG,EACtE,SAAU/e,EAAK4E,YAAc,EAE5B5E,SAAAA,EAAK4E,aAAe,GAAM9K,EAAA,IAAAulB,GAAA,CAAUnG,SAAelZ,EAAAA,EAAK4E,UAAU,CAAE,CAAA,CACvE,CAAA,EACF,CA5BamB,CAAAA,EAAAA,CA6Bf,CACD,CACH,CAAA,CACF,CAAA,CAEJ,ECxJM9J,GAAYzD,EAAOkH,EAAI,EAACtH,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAGzCsoB,GAAoBrlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAGlCuoB,EAAuBtlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACb,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,EAAE,EAG5CwoB,EAAqBvlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUG,GACnC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,cACpB,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,GAAG,EAG7CgpB,EAAqBxlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACf,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUK,GAC7B,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMsC,YAAYhC,OACvC,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMiB,OAAO4C,WAAW,EAG5CkhB,GAAoBrhB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,kBAAA,GAAA,EACd,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMI,UAAUuD,GAC7B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMsC,YAAYiF,SACvC,CAAC,CAAEvH,MAAAA,EAAOkK,MAAAA,CAAM,IACvBA,EAAQ,EAAIlK,EAAMiB,OAAOqhB,OAASpY,EAAQ,EAAIlK,EAAMiB,OAAOshB,KAAOviB,EAAMiB,OAAOQ,cAChE,CAAC,CAAEzB,MAAAA,CAAM,IAAMA,EAAMC,QAAQM,EAAE,EAG5Cyf,GAAuBtc,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,mCAAA,eAAA,GAAA,EAGvB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,GACtB,CAAC,CAAEH,MAAAA,CAAM,IAAMA,EAAMC,QAAQE,EAAE,EAGzCgpB,GAAezlB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,gBAAA,yBAAA,wBAAA,EACR,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GAC5B,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMC,QAAQQ,GACpB,CAAC,CAAET,MAAAA,CAAM,IAAMA,EAAMiB,OAAOyiB,MAAM,EAItD5b,GAAoBpE,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,qBAAA,EAChB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMC,QAAQ0D,GAE/B,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,EAAMiB,OAAOQ,aAAa,EAIvC2nB,GAA0CA,CAAC,CAAEtmB,UAAAA,CAAU,IAAM,CAClE,KAAA,CAAE6P,KAAAA,EAAM4J,gBAAAA,GAAoBmB,EAAiB,EAE/C,GAAA,CAAC/K,GAAQ,CAAC4J,EACL,OAAA,KAGT,MAAM8M,EAAgB1W,EAAK7H,OAAOwe,KAAcle,GAAAA,EAAM+G,KAAOoK,CAAe,EAE5E,GAAI,CAAC8M,EACI,OAAA/lB,EAAA,IAACwE,IAAW,SAAgB,kBAAA,CAAA,EAG/B2d,MAAAA,EAAcoB,GAA+B,CAC3C9X,MAAAA,EAAO,IAAID,KAAK+X,CAAU,EAChC,OACE9X,EAAKgY,mBAAmB,EACxB,IACAhY,EAAKoY,mBAAmB,GAAI,CAAE9V,KAAM,UAAWE,OAAQ,SAAA,CAAW,CAAA,EAIhEmR,EAAkBxY,GACf,IAAIyY,KAAKC,aAAa,QAAS,CACpC7f,MAAO,WACP8f,SAAU,MACVC,sBAAuB,EACvBC,sBAAuB,CAAA,CACxB,EAAEC,OAAO9Y,CAAK,EAKXwb,EAAuB9X,GACpBA,IAAc,OAAS,UAAY,QAM1C,OAAA9I,OAACW,IAAU,UAAA3C,EAAsB,MAAM,gBAAgB,QAAQ,UAAU,QAAQ,SAC/E,SAAA,CAACQ,EAAAA,IAAAyhB,GAAA,CAAW,MAAOsE,EAAcjb,YAAc,EAC5CsU,SAAe2G,EAAAA,EAAcjb,YAAc,CAAC,CAC/C,CAAA,SAEC2a,GACC,CAAA,SAAA,CAAAjkB,OAACkkB,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAS,WAAA,CAAA,EACrB3lB,EAAA,IAAA4lB,EAAA,CACC,SAAC5lB,EAAA,IAAAV,GAAA,CAAM,QAAS8iB,EAAoB2D,EAAczb,SAAS,EAAU,KAAK,QACvEyb,SAAAA,EAAczb,SACjB,CAAA,EACF,CAAA,EACF,SAECob,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAM,QAAA,CAAA,EACnB3lB,EAAAA,IAAC4lB,EAAaG,CAAAA,SAAAA,EAAczR,MAAO,CAAA,CAAA,EACrC,SAECoR,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAU,YAAA,CAAA,EACtB3lB,EAAA,IAAA4lB,EAAA,CAAazD,SAAW4D,EAAAA,EAActa,IAAI,EAAE,CAAA,EAC/C,SAECia,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAW,aAAA,CAAA,SACvBC,EAAY,CAAA,SAAA,CAAA,IAAEG,EAAc9W,KAAAA,EAAM,CAAA,EACrC,SAECyW,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAU,YAAA,CAAA,SACtBC,EAAY,CAAA,SAAA,CAAA,IAAEG,EAAczD,IAAAA,EAAK,CAAA,EACpC,SAECoD,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAI,MAAA,CAAA,EACjB3lB,EAAAA,IAAC4lB,EAAaG,CAAAA,SAAAA,EAAc9mB,IAAK,CAAA,CAAA,EACnC,SAECymB,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAQ,UAAA,CAAA,EACrB3lB,EAAAA,IAAC4lB,EAAaG,CAAAA,SAAAA,EAAcpQ,QAAS,CAAA,CAAA,EACvC,SAEC+P,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAQ,UAAA,CAAA,EACrB3lB,EAAAA,IAAC4lB,EAAaG,CAAAA,SAAAA,EAAcpQ,QAAS,CAAA,CAAA,EACvC,CAAA,EACF,EAECoQ,EAAchR,MAAQgR,EAAchR,KAAKtN,OAAS,UAChDie,EACC,CAAA,SAAA,CAAA1lB,EAAAA,IAAC2lB,GAAY,SAAI,MAAA,CAAA,QAChBjJ,GACEqJ,CAAAA,SAAAA,EAAchR,KAAKnN,IAAI,CAACmQ,EAAK9L,IAC3BjM,EAAA,IAAA8C,GAAA,CAAgB,QAAQ,OAAO,KAAK,QAClCiV,SADO9L,CAAAA,EAAAA,CAEV,CACD,EACH,CAAA,EACF,EAGD8Z,EAAcjQ,OACbtU,EAAAA,KAACqkB,GACC,CAAA,SAAA,CAAA7lB,EAAAA,IAAC2lB,GAAY,SAAK,OAAA,CAAA,EAClB3lB,EAAAA,IAAC4lB,EAAaG,CAAAA,SAAAA,EAAcjQ,KAAM,CAAA,CAAA,EACpC,CAEJ,CAAA,CAAA,CAEJ,ECtJamQ,GAAwDA,CAAC,CACpExK,UAAAA,EACApM,KAAAA,EACArK,UAAAA,EACA/G,MAAAA,CACF,IAAM,CACE,KAAA,CAAEgb,gBAAAA,GAAoBmB,EAAiB,EAEvC8L,EAAgBA,IAAM,CAC1B,OAAQzK,EAAS,CACf,IAAK,UACH,OAEIja,EAAA,KAAA2kB,WAAA,CAAA,SAAA,CAAAnmB,MAAC+E,GACC,MAAM,sBACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAM6B,SAChB,aAAa,0DAEb,SAAAlR,MAACmf,IAAkB,CAAA,EACrB,EAECnf,EAAAA,IAAA+E,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMmC,uBAAwBnC,EAAKmC,qBAAqB/J,SAAW,EAC7E,aAAa,sEAEb,SAACzH,EAAAA,IAAAwlB,EAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,EAECxlB,EAAAA,IAAA+E,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMoC,uBAAwBpC,EAAKoC,qBAAqBhK,SAAW,EAC7E,aAAa,sEAEb,SAACzH,EAAAA,IAAAwlB,EAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,CACF,CAAA,CAAA,EAGJ,IAAK,SACH,OAEIhkB,EAAA,KAAA2kB,WAAA,CAAA,SAAA,CAACnmB,EAAAA,IAAA+E,EAAA,CACC,MAAM,SACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAM7H,SAAU6H,EAAK7H,OAAOC,SAAW,EACjD,aAAa,gDAEb,SAACzH,EAAA,IAAA0kB,GAAA,CAAA,CAAW,CACd,CAAA,EAECzL,SAAoB6M,GAAc,EAAA,CACrC,CAAA,CAAA,EAGJ,IAAK,UACH,OACG9lB,EAAAA,IAAA+E,EAAA,CACC,MAAM,wBACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAM8B,oBAAqB9B,EAAK8B,kBAAkB1J,SAAW,EACvE,aAAa,iEAEb,SAACzH,EAAAA,IAAA8kB,EAAA,CAAyB,SAAS,SAAS,MAAM,QAAA,CAAQ,CAC5D,CAAA,EAGJ,IAAK,aACH,OACG9kB,EAAAA,IAAA+E,EAAA,CACC,MAAM,0BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAM+B,sBAAuB/B,EAAK+B,oBAAoB3J,SAAW,EAC3E,aAAa,mEAEb,SAACzH,EAAAA,IAAA8kB,EAAA,CAAyB,SAAS,WAAW,MAAM,UAAA,CAAU,CAChE,CAAA,EAGJ,IAAK,aACH,OAEItjB,EAAA,KAAA2kB,WAAA,CAAA,SAAA,CAACnmB,EAAAA,IAAA+E,EAAA,CACC,MAAM,2BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMyE,uBAAwBzE,EAAKyE,qBAAqBrM,SAAW,EAC7E,aAAa,oEAEb,SAACzH,EAAAA,IAAA8kB,EAAA,CAAyB,SAAS,YAAY,MAAM,WAAA,CAAW,CAClE,CAAA,EAEC9kB,EAAAA,IAAA+E,EAAA,CACC,MAAM,yBACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMgC,qBAAsBhC,EAAKgC,mBAAmB5J,SAAW,EACzE,aAAa,kEAEb,SAACzH,EAAAA,IAAA8kB,EAAA,CAAyB,SAAS,UAAU,MAAM,SAAA,CAAS,CAC9D,CAAA,CACF,CAAA,CAAA,EAGJ,IAAK,OACH,OAEItjB,EAAA,KAAA2kB,WAAA,CAAA,SAAA,CAACnmB,EAAAA,IAAA+E,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMmC,uBAAwBnC,EAAKmC,qBAAqB/J,SAAW,EAC7E,aAAa,sEAEb,SAACzH,EAAAA,IAAAwlB,EAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,EAECxlB,EAAAA,IAAA+E,EAAA,CACC,MAAM,6BACN,UAAAC,EACA,SAAU,CAAC,CAAC/G,EACZ,aAAcA,GAAS,GACvB,QAAS,EAACoR,GAAAA,MAAAA,EAAMoC,uBAAwBpC,EAAKoC,qBAAqBhK,SAAW,EAC7E,aAAa,sEAEb,SAACzH,EAAAA,IAAAwlB,EAAA,CAAqB,SAAS,YAAY,MAAM,aAAA,CAAa,CAChE,CAAA,CACF,CAAA,CAAA,EAGJ,QAEI,OAAAxlB,EAAAA,IAAC+E,EACC,CAAA,MAAM,cACN,SAAU,GACV,aAAc,gBAAgB0W,IAE9B,SAACja,EAAAA,KAAA,MAAA,CAAI,MAAO,CAAE4kB,UAAW,SAAUC,QAAS,OAAQC,MAAO,uBAA0B,EAAA,SAAA,CAAA,0BAC3D7K,CAAAA,CAC1B,CAAA,CACF,CAAA,CAEN,CAAA,EAGK,OAAAzb,EAAAA,IAAAmmB,EAAAA,SAAA,CAAGD,YAAgB,CAAA,CAC5B,ECzKMK,GAAwBnmB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,2CAAA,oBAAA,EAGxB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,QAGhC,CAAC,CAAE3D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,OAAM,EAIjDmmB,GAAqBpmB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGrB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,OAAM,EAG7ComB,GAA6BrmB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAarC,EAAA,CAAA,wIAAA,CAAA,EAEKkoB,GAAuBtmB,EAAAA,IAAG9B,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,eAAA,kBAAA,qBAAA,UAAA,0BAAA,GAAA,EAKnB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAewH,KAAM,QACjC,CAAC,CAAExH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcsF,UAAW,uBACrC,CAAC,CAAEtF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoB2D,KAAM,OACtC,CAAC,CAAE3D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcuB,QAAS,sBACjD,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAcuB,QAAS,sBAExC,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,OAAM,EAG7CwpB,GAAqB7lB,EAAAA,OAAMxC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,0CAAA,+BAAA,eAAA,uBAAA,+BAAA,EACpB,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeS,KAAM,QAC3C,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe2D,KAAM,QAC1B,CAAC,CAAE3D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAckB,UAAW,wBAGrC,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMqC,eAANrC,YAAAA,EAAoBO,KAAM,OAE3C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMsC,cAANtC,YAAAA,EAAmBM,SAAU,OAC7C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM0C,cAAN1C,YAAAA,EAAmB2C,OAAQ,iBAGxC,CAAC,CAAE3C,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMiB,SAANjB,YAAAA,EAAc8D,cAAe,sBAAqB,EAQ7E6jB,GAA4BA,IAC/B7iB,EAAAA,KAAA,MAAA,CACC,MAAO,CACLolB,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBxkB,OAAQ,QACRykB,IAAK,MACP,EAEA,SAAA,CAAChnB,EAAAA,IAAAG,GAAA,CAAe,KAAK,IAAI,CAAA,EACzBH,MAAC,OAAI,MAAO,CAAEsmB,MAAO,uBAAA,EAA2B,SAAyB,4BAAA,CAAA,CAC3E,CAAA,EAMIW,GAA4BA,IAAM,CAChC,KAAA,CAAE5X,KAAAA,EAAMrK,UAAAA,EAAW/G,MAAAA,EAAOwa,YAAAA,EAAauB,kBAAAA,EAAmBH,UAAAA,GAAcO,EAAiB,EAGzF8M,EAAmBtL,GAAgB,CACrB5B,EAAA,CAAErB,YAAaiD,CAAAA,CAAwC,CAAA,EAG3E,OAAI3d,SAECyoB,GACC,CAAA,SAAA,CAAA1mB,EAAAA,IAAC,OAAI,SAAwB,0BAAA,CAAA,EAC7BA,EAAAA,IAAC,OAAK/B,SAAMA,CAAA,CAAA,EACX+B,EAAA,IAAA2mB,GAAA,CAAY,QAAS9M,EAAW,SAAc,iBAAA,CACjD,CAAA,CAAA,SAKD0M,GAEC,CAAA,SAAA,CAAAvmB,EAAA,IAACgb,GAAe,CAAA,UAAWnB,EAAW,aAAc7U,EAAU,QAG7DiY,GAAW,EAAA,QAGXzB,GACC,CAAA,UAAW/C,EAAYE,aAAe,UACtC,YAAauO,EAAgB,EAI/BlnB,EAAAA,IAACwmB,IACC,SAACxmB,EAAAA,IAAAykB,EAAAA,SAAA,CAAS,SAAWzkB,EAAAA,IAAAqkB,GAAA,CAAA,CAAe,EAClC,SAACrkB,EAAA,IAAAymB,GAAA,CACC,eAACR,GACC,CAAA,UAAWxN,EAAYE,aAAe,UACtC,KAAAtJ,EACA,UAAArK,EACA,MAAA/G,CAAA,CAAa,CAEjB,CAAA,CAAA,CACF,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,EAQakpB,GAAgEA,CAAC,CAAE3nB,UAAAA,CAAU,IAErFQ,EAAA,IAAA,MAAA,CAAI,UAAAR,EACH,SAAAQ,MAACykB,EAAAA,SAAS,CAAA,SAAWzkB,EAAAA,IAAAqkB,GAAA,CAAe,CAAA,EAClC,SAAArkB,EAAA,IAACinB,GAAe,EAAA,CAAA,CAClB,CACF,CAAA,EC5JEG,GAA0BA,IAE3BpnB,EAAA,IAAAwZ,GAAA,CACC,SAACxZ,EAAA,IAAAmnB,GAAA,CAAA,CAAsB,CACzB,CAAA"}