import{r as s}from"./react-25c2faed.js";import{_ as F}from"./main-681bb6a1.js";import{c as m}from"./router-2c168ac3.js";import{t as x}from"./tradeStorage-a5c0ed9a.js";function f(e,n){return s.useEffect(()=>{if(e.riskPoints&&e.profit){const r=parseFloat(e.riskPoints),l=parseFloat(e.profit);if(r>0){const a=(l/r).toFixed(2);n(t=>({...t,rMultiple:a}))}}},[e.riskPoints,e.profit,n]),s.useEffect(()=>{},[e.entryTime,e.exitTime,e.rdTime]),{calculateProfitLoss:()=>{if(e.entryPrice&&e.exitPrice&&e.quantity){const r=parseFloat(e.entryPrice),l=parseFloat(e.exitPrice),a=parseFloat(e.quantity);if(!isNaN(r)&&!isNaN(l)&&!isNaN(a)){let t;e.direction==="long"?t=(l-r)*a:t=(r-l)*a,n(o=>({...o,profit:t.toFixed(2),result:t>0?"win":t<0?"loss":"breakeven"}))}}}}}function A(e,n,d,r,l,a,t,o,i,p){const y=m(),[v,T]=s.useState(!1);return{handleSubmit:s.useCallback(async D=>{var b,g,P,_,S;if(D.preventDefault(),!l()){i("Please complete the required fields in the Basic Info section."),o&&o("basic");return}if(t&&t!=="basic"&&a&&!a()){i("Please fix the validation errors in the current section before submitting.");return}T(!0),i(null),p(null);try{const c={clarity:e.patternQualityClarity||"",confluence:e.patternQualityConfluence||"",context:e.patternQualityContext||"",risk:e.patternQualityRisk||"",reward:e.patternQualityReward||"",timeframe:e.patternQualityTimeframe||"",volume:e.patternQualityVolume||""},w=Object.values(c).every(u=>u!==""),h={...e};if(w){const{calculateTotalScore:u,convertScoreToRating:R}=await F(()=>import("./patternQuality-31c523c9.js"),[]),k=u(c),I=R(k);h.patternQuality={total:k,rating:I,criteria:c,notes:e.patternQualityNotes||""}}e.dolType&&(h.dolAnalysis={dolType:e.dolType,dolStrength:e.dolStrength||"",dolReaction:e.dolReaction||"",dolContext:e.dolContext||[],priceAction:e.dolPriceAction||"",volumeProfile:e.dolVolumeProfile||"",timeOfDay:e.dolTimeOfDay||"",marketStructure:e.dolMarketStructure||"",effectiveness:e.dolEffectiveness?parseInt(e.dolEffectiveness):5,notes:e.dolNotes||""}),console.log("Form submitted:",h);const O={date:e.date,model_type:e.modelType||"Unknown",session:e.session,direction:e.direction==="long"?"Long":"Short",market:e.market,entry_price:parseFloat(e.entryPrice)||0,exit_price:parseFloat(e.exitPrice)||0,r_multiple:e.rMultiple?parseFloat(e.rMultiple):void 0,achieved_pl:parseFloat(e.profit)||0,win_loss:e.result==="win"?"Win":e.result==="loss"?"Loss":void 0,pattern_quality_rating:e.patternQuality?parseFloat(e.patternQuality):void 0,entry_time:e.entryTime,exit_time:e.exitTime,rd_time:e.rdTime,risk_points:e.riskPoints?parseFloat(e.riskPoints):void 0,no_of_contracts:parseInt(e.quantity)||0,notes:e.notes,setup_constant:(b=e.setupComponents)==null?void 0:b.constant,setup_action:(g=e.setupComponents)==null?void 0:g.action,setup_variable:(P=e.setupComponents)==null?void 0:P.variable,setup_entry:(_=e.setupComponents)==null?void 0:_.entry,setupComponents:e.setupComponents},L=e.entryVersion?{trade_id:0,rd_type:e.modelType,entry_version:e.entryVersion,draw_on_liquidity:e.specificDOLType}:void 0,N=e.primarySetupType||e.liquidityTaken?{trade_id:0,primary_setup:e.primarySetupType,secondary_setup:e.secondarySetupType,liquidity_taken:e.liquidityTaken,additional_fvgs:(S=e.additionalFVGs)==null?void 0:S.join(", "),dol:e.dolTargetType}:void 0,q=e.dolType||e.dolNotes?{trade_id:0,dol_target_type:e.dolTargetType,path_quality:e.dolPriceAction,clustering:e.dolVolumeProfile,dol_notes:e.dolNotes}:void 0,E={trade:O,fvg_details:L,setup:N,analysis:q};if(n&&r&&r.id){const u=typeof r.id=="string"?parseInt(r.id):r.id;await x.updateTradeWithDetails(u,E),console.log("Trade updated in IndexedDB successfully",u)}else{const u=await x.saveTradeWithDetails(E);console.log("New trade saved to IndexedDB successfully",u)}p(n?`Trade for ${e.symbol} on ${e.date} updated successfully!`:`New trade for ${e.symbol} on ${e.date} created successfully!`),setTimeout(()=>{console.log("Navigating back to journal page after successful submission"),y("/journal")},1500)}catch(c){i("Failed to save trade. Please try again."),console.error("Error submitting form:",c)}finally{T(!1)}},[e,l,a,t,o,i,p,n,r,y]),isSubmitting:v}}function W(){const[e,n]=s.useState({}),d=s.useCallback((t,o)=>{const i={};switch(o){case"basic":t.date||(i.date="Date is required"),t.symbol||(i.symbol="Symbol is required"),t.entryPrice||(i.entryPrice="Entry price is required"),t.exitPrice||(i.exitPrice="Exit price is required"),t.quantity||(i.quantity="Quantity is required");break;case"timing":t.entryTime&&t.exitTime&&t.exitTime<t.entryTime&&(i.exitTime="Exit time must be after entry time"),t.rdTime&&t.entryTime&&t.entryTime<t.rdTime&&(i.entryTime="Entry time must be after RD formation time");break;case"strategy":t.primarySetupCategory&&!t.primarySetupType&&(i.primarySetupType="Please select a primary setup type"),t.secondarySetupCategory&&!t.secondarySetupType&&(i.secondarySetupType="Please select a secondary setup type"),t.primarySetupType&&t.secondarySetupType&&t.primarySetupType===t.secondarySetupType&&(i.secondarySetupType="Primary and secondary setup types must be different");break;case"dol-analysis":t.dolType&&(t.dolStrength||(i.dolStrength="Please select a DOL strength"),t.dolReaction||(i.dolReaction="Please select a DOL reaction"),(!t.dolContext||t.dolContext.length===0)&&(i.dolContext="Please select at least one DOL context")),t.dolTargetType&&t.dolTargetType!=="RD Target"&&!t.specificDOLType&&(i.specificDOLType="Please select a specific DOL type");break}return n(p=>{const y={...p};return Object.keys(i).forEach(v=>{y[v]=i[v]}),y}),Object.keys(i).length===0},[]),r=s.useCallback(t=>{const o={};return t.date||(o.date="Date is required"),t.symbol||(o.symbol="Symbol is required"),t.entryPrice||(o.entryPrice="Entry price is required"),t.exitPrice||(o.exitPrice="Exit price is required"),t.quantity||(o.quantity="Quantity is required"),n(i=>({...i,...o})),Object.keys(o).length===0},[]),l=s.useCallback(t=>{n(o=>{const i={...o};return delete i[t],i})},[]),a=s.useCallback(()=>{n({})},[]);return{validationErrors:e,validateCurrentTab:d,validateBasicInfoTab:r,clearFieldError:l,clearAllErrors:a,setValidationErrors:n}}const B=[{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"Combined",label:"Combined"}],G=[{value:"Stocks",label:"Stocks"},{value:"Options",label:"Options"},{value:"Futures",label:"Futures"},{value:"Forex",label:"Forex"},{value:"Crypto",label:"Crypto"},{value:"Other",label:"Other"}];Array.from({length:10},(e,n)=>({value:String(n+1),label:String(n+1)}));const U=e=>{console.log(`useTradeForm hook initialized with tradeId: "${e}"`);const n=window.location.hash.substring(1);console.log(`Current hash path in useTradeForm: ${n}`);let d=e;if(!d&&n.includes("/trade/edit/")){const c=n.match(/\/trade\/edit\/([^\/]+)/);c&&c[1]&&(d=c[1],console.log(`Extracted trade ID from URL: ${d}`))}const r=d==="new"||n.includes("/trade/new"),l=d&&d!=="new"||n.includes("/trade/edit/")&&!n.includes("/trade/edit/new");console.log(`useTradeForm - isNewTrade: ${r}, isEditMode: ${l}`);const[a,t]=s.useState({}),[o]=s.useState(!1),[i,p]=s.useState(null),[y,v]=s.useState(null),T=null,C=s.useCallback(c=>{const{name:w,value:h}=c.target;t(O=>({...O,[w]:h}))},[]),{validationErrors:D,validateBasicInfoTab:b}=W(),{calculateProfitLoss:g}=f(a,t),P=()=>b(a),{handleSubmit:_,isSubmitting:S}=A(a,l,r,T,P,null,null,null,p,v);return{formValues:a,setFormValues:t,handleChange:C,handleSubmit:_,isSubmitting:S,isLoading:o,error:i,success:y,validationErrors:D,isNewTrade:r,calculateProfitLoss:g}},Y=[{value:"Sweep",label:"Sweep - Price moves through the liquidity level"},{value:"Tap",label:"Tap - Price touches the liquidity level exactly"},{value:"Approach",label:"Approach - Price approaches but doesn't quite reach the level"},{value:"Rejection",label:"Rejection - Price rejects from the liquidity level"}],K=[{value:"Strong",label:"Strong - Significant price movement with high volume"},{value:"Moderate",label:"Moderate - Noticeable price movement with average volume"},{value:"Weak",label:"Weak - Minimal price movement with low volume"}],z=[{value:"Immediate Reversal",label:"Immediate Reversal - Price reverses direction immediately"},{value:"Delayed Reversal",label:"Delayed Reversal - Price reverses after some consolidation"},{value:"Consolidation",label:"Consolidation - Price consolidates at the level"},{value:"Continuation",label:"Continuation - Price continues in the same direction"}],X=[{value:"High Volume Node",label:"High Volume Node"},{value:"Low Volume Node",label:"Low Volume Node"},{value:"VPOC",label:"Volume Point of Control (VPOC)"},{value:"VAH/VAL",label:"Value Area High/Low"},{value:"Previous Day High/Low",label:"Previous Day High/Low"},{value:"Previous Week High/Low",label:"Previous Week High/Low"},{value:"Previous Month High/Low",label:"Previous Month High/Low"},{value:"Round Number",label:"Round Number (00, 50, etc.)"},{value:"Technical Level",label:"Technical Level (Support/Resistance)"},{value:"News Event",label:"News Event"},{value:"Other",label:"Other"}];Array.from({length:10},(e,n)=>({value:String(n+1),label:String(n+1)}));const J={Sweep:"Describe how price moved through the liquidity level. Was it a clean sweep or did it struggle?",Tap:"Describe how price interacted with the liquidity level. Was it a precise tap or did it linger?",Approach:"Describe how price approached the liquidity level. How close did it get and why did it stop?",Rejection:"Describe how price rejected from the liquidity level. Was it a sharp rejection or gradual?"},Z={Strong:"Describe the volume profile during the liquidity interaction. Was there a volume spike?",Moderate:"Describe the volume profile during the liquidity interaction. Was volume consistent?",Weak:"Describe the volume profile during the liquidity interaction. Why was volume low?"},V="Describe the significance of the time of day for this liquidity interaction. Was it during a key market session or near a session transition?",ee="Describe the market structure context for this liquidity interaction. Was price in an uptrend, downtrend, or range? Were there any key levels nearby?",te=e=>e>=8?"var(--success-color)":e>=6?"#8BC34A":e>=5?"#FFC107":e>=3?"#FF9800":"var(--error-color)",ie=e=>e>=9?"Exceptional":e>=8?"Excellent":e>=7?"Very Good":e>=6?"Good":e>=5?"Average":e>=4?"Below Average":e>=3?"Poor":e>=2?"Very Poor":"Ineffective";export{Y as D,G as M,B as a,K as b,z as c,X as d,V as e,ee as f,J as g,Z as h,te as i,ie as j,U as u};
//# sourceMappingURL=dolAnalysis-cc48a373.js.map
