{"version": 3, "file": "TradeJournal-fde9d003.js", "sources": ["../../src/features/trade-journal/hooks/useTradeJournal.ts", "../../src/features/trade-journal/hooks/useTradeFilters.ts", "../../src/features/trade-journal/hooks/useTradeList.ts", "../../src/features/trade-journal/components/trade-list/TradeListHeader.tsx", "../../src/features/trade-journal/components/trade-list/TradeListRow.tsx", "../../src/features/trade-journal/components/trade-list/TradeListExpandedRow.tsx", "../../src/features/trade-journal/components/trade-list/TradeListEmpty.tsx", "../../src/features/trade-journal/components/trade-list/TradeListLoading.tsx", "../../src/features/trade-journal/components/TradeList.tsx", "../../src/features/trade-journal/components/LegacyDataImport.jsx", "../../src/features/trade-journal/components/F1FilterField.tsx", "../../src/features/trade-journal/components/useFilterState.ts", "../../src/features/trade-journal/components/filterFieldConfig.tsx", "../../src/features/trade-journal/components/F1FilterPanel.tsx", "../../src/features/trade-journal/components/trade-journal/TradeJournalFilters.tsx", "../../src/features/trade-journal/components/trade-journal/TradeJournalContent.tsx", "../../src/features/trade-journal/components/F1JournalHeader.tsx", "../../src/features/trade-journal/components/F1JournalTabs.tsx", "../../src/features/trade-journal/components/useJournalNavigation.ts", "../../src/features/trade-journal/components/journalTabConfig.tsx", "../../src/features/trade-journal/components/F1JournalContainer.tsx", "../../src/features/trade-journal/TradeJournal.tsx"], "sourcesContent": ["/**\n * useTradeJournal Hook\n *\n * Custom hook for managing trade journal data and operations.\n */\n\nimport { useState, useEffect } from 'react';\nimport { TradeJournalState } from '../types';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared/services';\n\n/**\n * useTradeJournal Hook\n *\n * Manages the state and operations for the trade journal feature.\n */\nexport const useTradeJournal = (): TradeJournalState => {\n  const [state, setState] = useState<TradeJournalState>({\n    trades: [],\n    isLoading: true,\n    error: null,\n  });\n\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        // Set loading state\n        setState(prev => ({\n          ...prev,\n          isLoading: true,\n          error: null,\n        }));\n\n        // Fetch trades from IndexedDB\n        const trades = await tradeStorageService.getAllTrades();\n\n        // Sort trades by date (newest first)\n        const sortedTrades = [...trades].sort(\n          (a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()\n        );\n\n        setState({\n          trades: sortedTrades as any, // Type assertion for interface compatibility\n          isLoading: false,\n          error: null,\n        });\n      } catch (error) {\n        console.error('Error fetching trades:', error);\n        setState(prev => ({\n          ...prev,\n          isLoading: false,\n          error: 'Failed to load trades. Please try again.',\n        }));\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Add function to refresh trades\n  const refreshTrades = async () => {\n    try {\n      setState(prev => ({\n        ...prev,\n        isLoading: true,\n        error: null,\n      }));\n\n      const trades = await tradeStorageService.getAllTrades();\n\n      // Sort trades by date (newest first)\n      const sortedTrades = [...trades].sort(\n        (a, b) => new Date(b.trade.date).getTime() - new Date(a.trade.date).getTime()\n      );\n\n      setState({\n        trades: sortedTrades as any, // Type assertion for interface compatibility\n        isLoading: false,\n        error: null,\n      });\n    } catch (error) {\n      console.error('Error refreshing trades:', error);\n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: 'Failed to refresh trades. Please try again.',\n      }));\n    }\n  };\n\n  return {\n    ...state,\n    refreshTrades,\n  };\n};\n\nexport default useTradeJournal;\n", "/**\n * useTradeFilters Hook\n *\n * Custom hook for managing trade filters\n */\n\nimport { useState, useMemo } from 'react';\n// Removed unused imports - will be added back when needed for real data integration\nimport { FilterState, CompleteTradeData } from '../types';\n\n/**\n * Hook for managing trade filters\n * @param trades The trades to filter\n */\nexport function useTradeFilters(trades: CompleteTradeData[]) {\n  // Filter state\n  const [filters, setFilters] = useState<FilterState>({\n    symbol: '',\n    direction: '',\n    setup: '',\n    modelType: '',\n    result: '',\n    dateFrom: '',\n    dateTo: '',\n    primarySetupType: '',\n    secondarySetupType: '',\n    liquidityTaken: '',\n    patternQualityMin: '',\n    patternQualityMax: '',\n    dolType: '',\n    dolEffectivenessMin: '',\n    dolEffectivenessMax: '',\n  });\n\n  // Handle filter changes\n  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFilters((prev) => ({\n      ...prev,\n      [name]: value,\n    }));\n  };\n\n  // Reset filters\n  const resetFilters = () => {\n    setFilters({\n      symbol: '',\n      direction: '',\n      setup: '',\n      modelType: '',\n      result: '',\n      dateFrom: '',\n      dateTo: '',\n      primarySetupType: '',\n      secondarySetupType: '',\n      liquidityTaken: '',\n      patternQualityMin: '',\n      patternQualityMax: '',\n      dolType: '',\n      dolEffectivenessMin: '',\n      dolEffectivenessMax: '',\n    });\n  };\n\n  // Apply filters to trades\n  const filteredTrades = useMemo(() => {\n    if (!trades) return [];\n\n    return trades.filter((tradeData) => {\n      const { trade, setup, analysis } = tradeData;\n\n      // Symbol filter (using market as symbol)\n      if (\n        filters.symbol &&\n        trade.market &&\n        !trade.market.toLowerCase().includes(filters.symbol.toLowerCase())\n      ) {\n        return false;\n      }\n\n      // Direction filter\n      if (filters.direction && trade.direction !== filters.direction) {\n        return false;\n      }\n\n      // Setup filter\n      if (filters.setup && setup?.primary_setup !== filters.setup) {\n        return false;\n      }\n\n      // Model type filter\n      if (filters.modelType && trade.model_type !== filters.modelType) {\n        return false;\n      }\n\n      // Result filter (win/loss)\n      if (filters.result) {\n        const isWin = (trade.achieved_pl || 0) > 0;\n        if ((filters.result === 'win' && !isWin) || (filters.result === 'loss' && isWin)) {\n          return false;\n        }\n      }\n\n      // Date range filter\n      if (filters.dateFrom) {\n        const tradeDate = new Date(trade.date);\n        const fromDate = new Date(filters.dateFrom);\n        if (tradeDate < fromDate) {\n          return false;\n        }\n      }\n\n      if (filters.dateTo) {\n        const tradeDate = new Date(trade.date);\n        const toDate = new Date(filters.dateTo);\n        // Set time to end of day\n        toDate.setHours(23, 59, 59, 999);\n        if (tradeDate > toDate) {\n          return false;\n        }\n      }\n\n      // Primary Setup Type filter\n      if (filters.primarySetupType && setup?.primary_setup !== filters.primarySetupType) {\n        return false;\n      }\n\n      // Secondary Setup Type filter\n      if (filters.secondarySetupType && setup?.secondary_setup !== filters.secondarySetupType) {\n        return false;\n      }\n\n      // Liquidity Taken filter\n      if (filters.liquidityTaken && setup?.liquidity_taken !== filters.liquidityTaken) {\n        return false;\n      }\n\n      // Pattern Quality Min filter\n      if (filters.patternQualityMin && trade.pattern_quality_rating) {\n        const minQuality = parseInt(filters.patternQualityMin);\n        if (!isNaN(minQuality) && trade.pattern_quality_rating < minQuality) {\n          return false;\n        }\n      }\n\n      // Pattern Quality Max filter\n      if (filters.patternQualityMax && trade.pattern_quality_rating) {\n        const maxQuality = parseInt(filters.patternQualityMax);\n        if (!isNaN(maxQuality) && trade.pattern_quality_rating > maxQuality) {\n          return false;\n        }\n      }\n\n      // DOL Type filter\n      if (filters.dolType && analysis?.dol_target_type !== filters.dolType) {\n        return false;\n      }\n\n      // DOL Effectiveness Min filter (not available in new schema, skip)\n      if (filters.dolEffectivenessMin) {\n        // This field doesn't exist in the new schema, so we'll skip this filter\n      }\n\n      // DOL Effectiveness Max filter (not available in new schema, skip)\n      if (filters.dolEffectivenessMax) {\n        // This field doesn't exist in the new schema, so we'll skip this filter\n      }\n\n      return true;\n    });\n  }, [trades, filters]);\n\n  // Get unique values for filter dropdowns\n  const uniqueSetups = useMemo(() => {\n    if (!trades) return [];\n    const setups = trades\n      .map((tradeData) => tradeData.setup?.primary_setup)\n      .filter((setup): setup is string => !!setup);\n    return Array.from(new Set(setups));\n  }, [trades]);\n\n  const uniqueModelTypes = useMemo(() => {\n    if (!trades) return [];\n    const modelTypes = trades\n      .map((tradeData) => tradeData.trade.model_type)\n      .filter((modelType): modelType is string => !!modelType);\n    return Array.from(new Set(modelTypes));\n  }, [trades]);\n\n  const uniquePrimarySetupTypes = useMemo(() => {\n    if (!trades) return [];\n    const setupTypes = trades\n      .map((tradeData) => tradeData.setup?.primary_setup)\n      .filter((setupType): setupType is string => !!setupType);\n    return Array.from(new Set(setupTypes));\n  }, [trades]);\n\n  const uniqueSecondarySetupTypes = useMemo(() => {\n    if (!trades) return [];\n    const setupTypes = trades\n      .map((tradeData) => tradeData.setup?.secondary_setup)\n      .filter((setupType): setupType is string => !!setupType);\n    return Array.from(new Set(setupTypes));\n  }, [trades]);\n\n  const uniqueLiquidityTypes = useMemo(() => {\n    if (!trades) return [];\n    const liquidityTypes = trades\n      .map((tradeData) => tradeData.setup?.liquidity_taken)\n      .filter((liquidityType): liquidityType is string => !!liquidityType);\n    return Array.from(new Set(liquidityTypes));\n  }, [trades]);\n\n  const uniqueDOLTypes = useMemo(() => {\n    if (!trades) return [];\n    const dolTypes = trades\n      .filter((tradeData) => tradeData.analysis)\n      .map((tradeData) => tradeData.analysis?.dol_target_type)\n      .filter((dolType): dolType is string => !!dolType);\n    return Array.from(new Set(dolTypes));\n  }, [trades]);\n\n  return {\n    filters,\n    setFilters,\n    handleFilterChange,\n    resetFilters,\n    filteredTrades,\n    uniqueSetups,\n    uniqueModelTypes,\n    uniquePrimarySetupTypes,\n    uniqueSecondarySetupTypes,\n    uniqueLiquidityTypes,\n    uniqueDOLTypes,\n  };\n}\n\nexport type TradeFiltersHook = ReturnType<typeof useTradeFilters>;\n", "/**\n * useTradeList Hook\n *\n * Custom hook for managing trade list state and behavior\n */\n\nimport { useState, useMemo } from 'react';\n// Removed unused imports - will be added back when needed for real data integration\nimport { CompleteTradeData } from '../types';\n\n/**\n * Hook for managing trade list state and behavior\n * @param trades The trades to display\n * @param expandable Whether the rows can be expanded\n */\nexport function useTradeList(trades: CompleteTradeData[], expandable: boolean = false) {\n  // Track expanded rows\n  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({});\n\n  // Toggle row expansion\n  const toggleRowExpansion = (tradeId: number) => {\n    if (!expandable) return;\n\n    setExpandedRows((prev) => ({\n      ...prev,\n      [tradeId]: !prev[tradeId],\n    }));\n  };\n\n  // Check if a row is expanded\n  const isRowExpanded = (tradeId: number) => {\n    return expandable && expandedRows[tradeId];\n  };\n\n  // Sort trades by date (newest first)\n  const sortedTrades = useMemo(() => {\n    if (!trades) return [];\n\n    return [...trades].sort((a, b) => {\n      const dateA = new Date(a.trade.date).getTime();\n      const dateB = new Date(b.trade.date).getTime();\n      return dateB - dateA; // Newest first\n    });\n  }, [trades]);\n\n  return {\n    sortedTrades,\n    expandedRows,\n    toggleRowExpansion,\n    isRowExpanded,\n  };\n}\n\nexport type TradeListHook = ReturnType<typeof useTradeList>;\n", "/**\n * Trade List Header Component\n *\n * Displays the header for the trade list\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeColumn } from '../TradeList';\n\nconst TradeHeader = styled.div`\n  display: grid;\n  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));\n  align-items: center;\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  background-color: transparent;\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n  position: sticky;\n  top: 0;\n  z-index: 1;\n  backdrop-filter: blur(8px);\n`;\n\nconst TradeDetail = styled.div`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n`;\n\ninterface TradeListHeaderProps {\n  visibleColumns: TradeColumn[];\n}\n\n/**\n * Trade List Header Component\n */\nconst TradeListHeader: React.FC<TradeListHeaderProps> = ({ visibleColumns }) => {\n  return (\n    <TradeHeader>\n      {visibleColumns.map((column) => (\n        <TradeDetail key={column.id}>{column.label}</TradeDetail>\n      ))}\n    </TradeHeader>\n  );\n};\n\nexport default TradeListHeader;\n", "/**\n * Trade List Row Component\n *\n * Displays a row in the trade list\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { CompleteTradeData } from '@adhd-trading-dashboard/shared';\nimport { TradeColumn } from '../TradeList';\n\nconst TradeItem = styled.div<{ expanded?: boolean }>`\n  display: grid;\n  grid-template-columns: var(--grid-template-columns, repeat(auto-fit, minmax(100px, 1fr)));\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.cardBackground};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: all ${({ theme }) => theme.transitions.fast};\n  cursor: ${({ expanded }) => (expanded !== undefined ? 'pointer' : 'default')};\n  position: relative;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n  }\n`;\n\ninterface TradeListRowProps {\n  trade: CompleteTradeData;\n  visibleColumns: TradeColumn[];\n  expanded?: boolean;\n  toggleRowExpansion: (tradeId: number) => void;\n}\n\n/**\n * Trade List Row Component\n */\nconst TradeListRow: React.FC<TradeListRowProps> = ({\n  trade,\n  visibleColumns,\n  expanded,\n  toggleRowExpansion,\n}) => {\n  return (\n    <TradeItem expanded={expanded} onClick={() => toggleRowExpansion(trade.trade.id!)}>\n      {visibleColumns.map((column) => (\n        <React.Fragment key={column.id}>{column.accessor(trade)}</React.Fragment>\n      ))}\n    </TradeItem>\n  );\n};\n\nexport default TradeListRow;\n", "/**\n * Trade List Expanded Row Component\n *\n * Displays expanded details for a trade row\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nconst ExpandedContent = styled.div`\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ExpandedSection = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst DetailGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst DetailItem = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst DetailLabel = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst DetailValue = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ActionButtons = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ActionButton = styled(Link)`\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  text-decoration: none;\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeListExpandedRowProps {\n  trade: CompleteTradeData;\n}\n\n/**\n * Trade List Expanded Row Component\n */\nconst TradeListExpandedRow: React.FC<TradeListExpandedRowProps> = ({ trade }) => {\n  return (\n    <ExpandedContent>\n      <ExpandedSection>\n        <SectionTitle>Trade Details</SectionTitle>\n        <DetailGrid>\n          <DetailItem>\n            <DetailLabel>Market</DetailLabel>\n            <DetailValue>{trade.trade.market || 'N/A'}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Date</DetailLabel>\n            <DetailValue>{trade.trade.date}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Direction</DetailLabel>\n            <DetailValue>{trade.trade.direction}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Entry Price</DetailLabel>\n            <DetailValue>${(trade.trade.entry_price || 0).toFixed(2)}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Exit Price</DetailLabel>\n            <DetailValue>${(trade.trade.exit_price || 0).toFixed(2)}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Contracts</DetailLabel>\n            <DetailValue>{trade.trade.no_of_contracts || 0}</DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>Profit/Loss</DetailLabel>\n            <DetailValue\n              style={{\n                color:\n                  (trade.trade.achieved_pl || 0) > 0\n                    ? 'green'\n                    : (trade.trade.achieved_pl || 0) < 0\n                    ? 'red'\n                    : 'inherit',\n              }}\n            >\n              ${(trade.trade.achieved_pl || 0).toFixed(2)}\n            </DetailValue>\n          </DetailItem>\n          <DetailItem>\n            <DetailLabel>R-Multiple</DetailLabel>\n            <DetailValue>{trade.trade.r_multiple?.toFixed(2) || 'N/A'}</DetailValue>\n          </DetailItem>\n        </DetailGrid>\n      </ExpandedSection>\n\n      {trade.setup && (\n        <ExpandedSection>\n          <SectionTitle>Strategy</SectionTitle>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>Model Type</DetailLabel>\n              <DetailValue>{trade.trade.model_type || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Session</DetailLabel>\n              <DetailValue>{trade.trade.session || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Primary Setup</DetailLabel>\n              <DetailValue>{trade.setup.primary_setup || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Secondary Setup</DetailLabel>\n              <DetailValue>{trade.setup.secondary_setup || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Liquidity Taken</DetailLabel>\n              <DetailValue>{trade.setup.liquidity_taken || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Pattern Quality</DetailLabel>\n              <DetailValue>{trade.trade.pattern_quality_rating || 'N/A'}</DetailValue>\n            </DetailItem>\n          </DetailGrid>\n        </ExpandedSection>\n      )}\n\n      {trade.analysis && (\n        <ExpandedSection>\n          <SectionTitle>Analysis</SectionTitle>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>Execution Quality</DetailLabel>\n              <DetailValue>{(trade.analysis as any)?.execution_quality || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Lessons Learned</DetailLabel>\n              <DetailValue>{(trade.analysis as any)?.lessons_learned || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Emotional State</DetailLabel>\n              <DetailValue>{(trade.analysis as any)?.emotional_state || 'N/A'}</DetailValue>\n            </DetailItem>\n            <DetailItem>\n              <DetailLabel>Market Conditions</DetailLabel>\n              <DetailValue>{(trade.analysis as any)?.market_conditions || 'N/A'}</DetailValue>\n            </DetailItem>\n          </DetailGrid>\n        </ExpandedSection>\n      )}\n\n      {trade.trade.notes && (\n        <ExpandedSection>\n          <SectionTitle>Notes</SectionTitle>\n          <DetailItem>\n            <DetailValue>{trade.trade.notes}</DetailValue>\n          </DetailItem>\n        </ExpandedSection>\n      )}\n\n      <ActionButtons>\n        <ActionButton to={`/trade-journal/edit/${trade.trade.id}`}>Edit Trade</ActionButton>\n      </ActionButtons>\n    </ExpandedContent>\n  );\n};\n\nexport default TradeListExpandedRow;\n", "/**\n * Trade List Empty Component\n *\n * Displays a message when there are no trades to show\n */\n\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n\nconst EmptyContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.xl};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  text-align: center;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst EmptyDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0 0 ${({ theme }) => theme.spacing.lg} 0;\n  max-width: 500px;\n`;\n\nconst AddTradeButton = styled(Link)`\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  font-weight: 500;\n  text-decoration: none;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeListEmptyProps {\n  filtered?: boolean;\n}\n\n/**\n * Trade List Empty Component\n */\nconst TradeListEmpty: React.FC<TradeListEmptyProps> = ({ filtered }) => {\n  return (\n    <EmptyContainer>\n      <EmptyTitle>{filtered ? 'No matching trades found' : 'No trades yet'}</EmptyTitle>\n      <EmptyDescription>\n        {filtered\n          ? 'Try adjusting your filters to see more results.'\n          : 'Start tracking your trades to gain insights into your trading performance.'}\n      </EmptyDescription>\n      {!filtered && <AddTradeButton to=\"/trade/new\">+ Add Your First Trade</AddTradeButton>}\n    </EmptyContainer>\n  );\n};\n\nexport default TradeListEmpty;\n", "/**\n * Trade List Loading Component\n *\n * Displays a loading state for the trade list\n */\n\nimport React from 'react';\nimport styled, { keyframes } from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n\nconst shimmer = keyframes`\n  0% {\n    background-position: -1000px 0;\n  }\n  100% {\n    background-position: 1000px 0;\n  }\n`;\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  padding: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst LoadingRow = styled.div`\n  height: 60px;\n  background: linear-gradient(\n    to right,\n    ${({ theme }) => theme.colors.background} 8%,\n    ${({ theme }) => theme.colors.cardBackground} 18%,\n    ${({ theme }) => theme.colors.background} 33%\n  );\n  background-size: 2000px 100%;\n  animation: ${shimmer} 1.5s infinite linear;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\ninterface TradeListLoadingProps {\n  rowCount?: number;\n}\n\n/**\n * Trade List Loading Component\n */\nconst TradeListLoading: React.FC<TradeListLoadingProps> = ({ rowCount = 5 }) => {\n  return (\n    <LoadingContainer>\n      {Array.from({ length: rowCount }).map((_, index) => (\n        <LoadingRow key={index} />\n      ))}\n    </LoadingContainer>\n  );\n};\n\nexport default TradeListLoading;\n", "/**\n * Trade List Component\n *\n * Displays a list of trades with expandable rows\n */\n\nimport React, { useMemo } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { CompleteTradeData } from '../types';\nimport { useTradeList } from '../hooks/useTradeList';\nimport { SetupTransformer } from '../../../services/transformers/setupTransformer';\nimport {\n  TradeListHeader,\n  TradeListRow,\n  TradeListExpandedRow,\n  TradeListEmpty,\n  TradeListLoading,\n} from './trade-list';\n\nconst TradeListContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst TradeDetail = styled.div<{ profit?: boolean; loss?: boolean }>`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme, profit, loss }) =>\n    profit ? theme.colors.success : loss ? theme.colors.danger : theme.colors.textPrimary};\n  font-weight: ${({ profit, loss }) => (profit || loss ? 600 : 'normal')};\n`;\n\nconst Badge = styled.span<{ type?: 'long' | 'short' }>`\n  display: inline-block;\n  padding: ${({ theme }) => `${theme.spacing.xxs} ${theme.spacing.xs}`};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  font-weight: 600;\n  text-transform: uppercase;\n  background-color: ${({ theme, type }) =>\n    type === 'long'\n      ? theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'\n      : type === 'short'\n      ? theme.colors.dangerLight || 'rgba(244, 67, 54, 0.1)'\n      : theme.colors.background};\n  color: ${({ theme, type }) =>\n    type === 'long'\n      ? theme.colors.success\n      : type === 'short'\n      ? theme.colors.danger\n      : theme.colors.textPrimary};\n`;\n\nexport interface TradeColumn {\n  id: string;\n  label: string;\n  accessor: (trade: CompleteTradeData) => React.ReactNode;\n}\n\ninterface TradeListProps {\n  trades: CompleteTradeData[];\n  isLoading?: boolean;\n  expandable?: boolean;\n  onEditTrade?: (tradeId: number) => void;\n}\n\n/**\n * Trade List Component\n */\nconst TradeList: React.FC<TradeListProps> = ({\n  trades,\n  isLoading = false,\n  expandable = false,\n  onEditTrade,\n}) => {\n  const navigate = useNavigate();\n  const { sortedTrades, toggleRowExpansion, isRowExpanded } = useTradeList(trades, expandable);\n\n  // Handle edit trade\n  const handleEditTrade = (tradeId: number) => {\n    if (onEditTrade) {\n      onEditTrade(tradeId);\n    } else {\n      // Default navigation to edit page\n      navigate(`/trade/edit/${tradeId}`);\n    }\n  };\n\n  // Define columns for the trade list\n  const columns: TradeColumn[] = useMemo(\n    () => [\n      {\n        id: 'date',\n        label: 'Date',\n        accessor: trade => <TradeDetail>{trade.trade.date}</TradeDetail>,\n      },\n      {\n        id: 'symbol',\n        label: 'Symbol',\n        accessor: trade => <TradeDetail>{trade.trade.market || 'MNQ'}</TradeDetail>,\n      },\n      {\n        id: 'direction',\n        label: 'Direction',\n        accessor: trade => (\n          <TradeDetail>\n            <Badge type={trade.trade.direction.toLowerCase() as 'long' | 'short'}>\n              {trade.trade.direction}\n            </Badge>\n          </TradeDetail>\n        ),\n      },\n      {\n        id: 'setup',\n        label: 'Setup',\n        accessor: trade => {\n          // Try to get setup from setupComponents first\n          if (trade.trade.setupComponents) {\n            const setupDisplay = SetupTransformer.getShortDisplayString(\n              trade.trade.setupComponents\n            );\n            return <TradeDetail>{setupDisplay}</TradeDetail>;\n          }\n\n          // Use setupComponents if available (this is the correct field)\n          if (\n            (trade.trade as any).setupComponents?.constant &&\n            (trade.trade as any).setupComponents?.entry\n          ) {\n            const setupDisplay = SetupTransformer.getShortDisplayString(\n              (trade.trade as any).setupComponents\n            );\n            return <TradeDetail>{setupDisplay}</TradeDetail>;\n          }\n\n          // Fallback to old setup field or N/A\n          return <TradeDetail>{trade.setup?.primary_setup || 'N/A'}</TradeDetail>;\n        },\n      },\n      {\n        id: 'entry',\n        label: 'Entry',\n        accessor: trade => <TradeDetail>${(trade.trade.entry_price || 0).toFixed(2)}</TradeDetail>,\n      },\n      {\n        id: 'exit',\n        label: 'Exit',\n        accessor: trade => <TradeDetail>${(trade.trade.exit_price || 0).toFixed(2)}</TradeDetail>,\n      },\n      {\n        id: 'size',\n        label: 'Size',\n        accessor: trade => <TradeDetail>{trade.trade.no_of_contracts || 0}</TradeDetail>,\n      },\n      {\n        id: 'profitLoss',\n        label: 'P/L',\n        accessor: trade => (\n          <TradeDetail\n            profit={(trade.trade.achieved_pl || 0) > 0}\n            loss={(trade.trade.achieved_pl || 0) < 0}\n          >\n            ${(trade.trade.achieved_pl || 0).toFixed(2)}\n          </TradeDetail>\n        ),\n      },\n      {\n        id: 'actions',\n        label: 'Actions',\n        accessor: trade => (\n          <TradeDetail>\n            <button\n              onClick={() => handleEditTrade(trade.trade.id!)}\n              style={{\n                padding: '4px 8px',\n                fontSize: '12px',\n                border: '1px solid #ccc',\n                borderRadius: '4px',\n                background: 'white',\n                cursor: 'pointer',\n              }}\n            >\n              Edit\n            </button>\n          </TradeDetail>\n        ),\n      },\n    ],\n    [handleEditTrade]\n  );\n\n  // Set grid template columns based on number of columns\n  const gridTemplateColumns = `repeat(${columns.length}, 1fr)`;\n\n  // If loading, show loading state\n  if (isLoading) {\n    return <TradeListLoading rowCount={5} />;\n  }\n\n  // If no trades, show empty state\n  if (!sortedTrades || sortedTrades.length === 0) {\n    return <TradeListEmpty filtered={trades && trades.length > 0} />;\n  }\n\n  return (\n    <TradeListContainer style={{ '--grid-template-columns': gridTemplateColumns } as any}>\n      <TradeListHeader visibleColumns={columns} />\n\n      {sortedTrades.map(trade => (\n        <React.Fragment key={trade.trade.id}>\n          <TradeListRow\n            trade={trade}\n            visibleColumns={columns}\n            expanded={isRowExpanded(trade.trade.id!)}\n            toggleRowExpansion={toggleRowExpansion}\n          />\n          {isRowExpanded(trade.trade.id!) && <TradeListExpandedRow trade={trade} />}\n        </React.Fragment>\n      ))}\n    </TradeListContainer>\n  );\n};\n\nexport default TradeList;\n", "import React, { useState, useCallback } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared/services';\n\n// Simple icon components to replace lucide-react\nconst Upload = () => <span>📤</span>;\nconst FileText = () => <span>📄</span>;\nconst CheckCircle = () => <span>✅</span>;\nconst XCircle = () => <span>❌</span>;\nconst AlertCircle = () => <span>⚠️</span>;\nconst Download = () => <span>💾</span>;\n\nconst CSVImportTool = ({ onImportComplete }) => {\n  const [file, setFile] = useState(null);\n  const [csvData, setCsvData] = useState(null);\n  const [mappedData, setMappedData] = useState(null);\n  const [importStatus, setImportStatus] = useState('idle'); // idle, processing, preview, imported\n  const [stats, setStats] = useState(null);\n  const [importMethod, setImportMethod] = useState('csv'); // csv, json, manual, paste\n\n  // Updated column mappings for your specific CSV structure\n  const COLUMN_MAPPINGS = {\n    // Direct mappings for your exact column names\n    date: ['date'],\n    model_type: ['model type'],\n    direction: ['direction'],\n    market: ['market'],\n    entry_price: ['entry price'],\n    exit_price: ['exit price'],\n    achieved_pl: ['achieved p/l'],\n    r_multiple: ['r-multiple'],\n    risk_points: ['risk (points)'],\n    no_of_contracts: ['no. of contracts'],\n    win_loss: ['win/loss'],\n    pattern_quality_rating: ['pattern quality rating (1-5)'],\n    session: ['session (time block)'],\n    entry_time: ['entry time'],\n    exit_time: ['exit time'],\n    setup: ['setup'],\n    primary_setup: ['primary setup'],\n    secondary_setup: ['secondary setup'],\n    notes: ['notes'],\n    rd_type: ['rd type'],\n    draw_on_liquidity: ['draw on liquidity'],\n    fvg_date: ['fvg date'],\n    entry_version: ['entry version'],\n    liquidity_taken: ['liquidity taken'],\n    additional_fvgs: ['additional fvgs'],\n    dol: ['dol'],\n    tradingview_link: ['tradingview link'],\n    dol_target_type: ['dol target type'],\n    beyond_target: ['beyond target'],\n    clustering: ['clustering'],\n    path_quality: ['path quality'],\n    idr_context: ['idr context'],\n    sequential_fvg_rd: ['sequential-fvg-rd'],\n    dol_notes: ['dol notes'],\n  };\n\n  // Valid trading models from your schema\n  const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];\n\n  // Valid sessions from your application\n  const VALID_SESSIONS = [\n    'NY Open',\n    'Lunch Macro',\n    'MOC',\n    'London Open',\n    'Asian Session',\n    'Pre-Market',\n    'After Hours',\n    'NY AM',\n    'NY PM',\n  ];\n\n  // Valid markets\n  const VALID_MARKETS = ['MNQ', 'NQ', 'ES', 'MES', 'YM', 'MYM', 'RTY', 'M2K'];\n\n  const handleFileUpload = useCallback((event) => {\n    const uploadedFile = event.target.files[0];\n    if (uploadedFile && uploadedFile.type === 'text/csv') {\n      setFile(uploadedFile);\n      setImportStatus('processing');\n      parseCSV(uploadedFile);\n    }\n  }, []);\n\n  // Improved CSV parser that handles quoted fields properly\n  const parseCSVLine = (line) => {\n    const result = [];\n    let current = '';\n    let inQuotes = false;\n\n    for (let i = 0; i < line.length; i++) {\n      const char = line[i];\n\n      if (char === '\"') {\n        inQuotes = !inQuotes;\n      } else if (char === ',' && !inQuotes) {\n        result.push(current.trim());\n        current = '';\n      } else {\n        current += char;\n      }\n    }\n\n    result.push(current.trim());\n    return result;\n  };\n\n  const parseCSV = async (file) => {\n    try {\n      const text = await file.text();\n      const lines = text.split('\\n').filter((line) => line.trim());\n\n      if (lines.length === 0) {\n        console.error('CSV file is empty');\n        setImportStatus('idle');\n        return;\n      }\n\n      console.log('Total lines found:', lines.length);\n\n      // FIXED: Skip the first row (category descriptions) and use the second row as headers\n      const headerLine = lines[1]; // Row 2 contains actual headers\n      const headers = parseCSVLine(headerLine).map((h) => h.trim().toLowerCase().replace(/\"/g, ''));\n\n      console.log('Parsed headers:', headers);\n\n      // Parse data rows starting from row 3 (index 2)\n      const rows = lines\n        .slice(2) // Skip first 2 rows (categories + headers)\n        .filter((line) => line.trim())\n        .map((line) => {\n          const values = parseCSVLine(line);\n          const row = {};\n          headers.forEach((header, index) => {\n            row[header] = values[index]?.trim().replace(/\"/g, '') || '';\n          });\n          return row;\n        });\n\n      console.log('Parsed rows:', rows.length);\n      console.log('Sample row:', rows[0]);\n\n      setCsvData({ headers, rows });\n      mapColumns(headers, rows);\n    } catch (error) {\n      console.error('CSV parsing error:', error);\n      setImportStatus('idle');\n    }\n  };\n\n  const mapColumns = (headers, rows) => {\n    console.log('Starting column mapping...');\n    console.log('Available headers:', headers);\n\n    // Debug: Show which columns we can map\n    const mappingResults = {};\n    Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {\n      const matchedHeader = headers.find((h) =>\n        possibleHeaders.some((ph) => h.toLowerCase().includes(ph.toLowerCase()))\n      );\n      mappingResults[dbField] = matchedHeader || 'NOT FOUND';\n    });\n    console.log('Column mapping results:', mappingResults);\n\n    const mapped = rows\n      .map((row, rowIndex) => {\n        const tradeRecord = {};\n\n        // Smart mapping logic - map to TradeRecord schema\n        Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {\n          const matchedHeader = headers.find((h) =>\n            possibleHeaders.some((ph) => h.toLowerCase().includes(ph.toLowerCase()))\n          );\n\n          if (matchedHeader && row[matchedHeader]) {\n            let value = row[matchedHeader].trim();\n\n            // Field-specific transformations to match TradeRecord schema\n            if (dbField === 'model_type') {\n              // Map to valid trading models - handle \"RD-Cont.\" format\n              let model = value.replace('.', ''); // Remove trailing period\n              const validModel = VALID_TRADING_MODELS.find(\n                (vm) =>\n                  model.toLowerCase().includes(vm.toLowerCase()) ||\n                  model\n                    .toLowerCase()\n                    .replace(/[-\\s]/g, '')\n                    .includes(vm.toLowerCase().replace(/[-\\s]/g, ''))\n              );\n              tradeRecord[dbField] = validModel || 'Combined';\n            } else if (dbField === 'direction') {\n              // Standardize direction to 'Long' | 'Short'\n              const lower = value.toLowerCase();\n              if (lower.includes('long') || lower.includes('buy') || lower === 'l') {\n                tradeRecord[dbField] = 'Long';\n              } else if (lower.includes('short') || lower.includes('sell') || lower === 's') {\n                tradeRecord[dbField] = 'Short';\n              } else {\n                tradeRecord[dbField] = value; // Keep original if already correct\n              }\n            } else if (dbField === 'win_loss') {\n              // Standardize win/loss to 'Win' | 'Loss'\n              const lower = value.toLowerCase();\n              if (lower.includes('win') || lower.includes('profit') || lower === 'w') {\n                tradeRecord[dbField] = 'Win';\n              } else if (lower.includes('loss') || lower.includes('lose') || lower === 'l') {\n                tradeRecord[dbField] = 'Loss';\n              } else {\n                tradeRecord[dbField] = value; // Keep original if already correct\n              }\n            } else if (dbField === 'session') {\n              // Map to valid sessions\n              const session = VALID_SESSIONS.find(\n                (vs) =>\n                  value.toLowerCase().includes(vs.toLowerCase()) ||\n                  vs.toLowerCase().includes(value.toLowerCase())\n              );\n              tradeRecord[dbField] = session || value; // Keep original if no match\n            } else if (dbField === 'market') {\n              // Map to valid markets\n              const market = VALID_MARKETS.find((vm) =>\n                value.toLowerCase().includes(vm.toLowerCase())\n              );\n              tradeRecord[dbField] = market || value; // Keep original if no exact match\n            } else if (\n              ['entry_price', 'exit_price', 'r_multiple', 'risk_points'].includes(dbField)\n            ) {\n              // FIXED: Clean up numeric fields - remove commas and other non-numeric chars\n              const numericValue = parseFloat(value.replace(/[^-0-9.]/g, ''));\n              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;\n            } else if (dbField === 'achieved_pl') {\n              // FIXED: Handle P&L with dollar signs and commas\n              const numericValue = parseFloat(value.replace(/[\\$,]/g, ''));\n              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;\n            } else if (dbField === 'pattern_quality_rating') {\n              // Handle decimal quality ratings\n              const rating = parseFloat(value) || 3;\n              tradeRecord[dbField] = Math.max(1, Math.min(5, rating));\n            } else if (dbField === 'no_of_contracts') {\n              // Ensure contracts is a positive number\n              const contracts = parseFloat(value) || 1;\n              tradeRecord[dbField] = Math.max(0.1, contracts);\n            } else if (['entry_time', 'exit_time'].includes(dbField)) {\n              // Handle time fields - ensure proper format\n              tradeRecord[dbField] = value.includes(':') ? value : null;\n            } else {\n              // String fields - just clean and assign\n              tradeRecord[dbField] = value;\n            }\n          }\n        });\n\n        // Set required defaults for TradeRecord\n        if (!tradeRecord.model_type) tradeRecord.model_type = 'Combined';\n        if (!tradeRecord.direction) tradeRecord.direction = 'Long';\n        if (!tradeRecord.market) tradeRecord.market = 'MNQ';\n        if (!tradeRecord.pattern_quality_rating) tradeRecord.pattern_quality_rating = 3;\n        if (!tradeRecord.no_of_contracts) tradeRecord.no_of_contracts = 1;\n\n        // FIXED: Handle date format (MM/DD/YYYY to YYYY-MM-DD)\n        if (tradeRecord.date) {\n          try {\n            const dateParts = tradeRecord.date.split('/');\n            if (dateParts.length === 3) {\n              const [month, day, year] = dateParts;\n              tradeRecord.date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;\n            }\n          } catch (e) {\n            tradeRecord.date = new Date().toISOString().split('T')[0];\n          }\n        } else {\n          tradeRecord.date = new Date().toISOString().split('T')[0];\n        }\n\n        // Debug logging for first few rows\n        if (rowIndex < 3) {\n          console.log(`Row ${rowIndex} mapped:`, tradeRecord);\n          console.log(\n            `Has date: ${!!tradeRecord.date}, Has entry_price: ${!!tradeRecord.entry_price}, Has exit_price: ${!!tradeRecord.exit_price}`\n          );\n        }\n\n        // More flexible validation - just need some price data\n        const hasValidData =\n          tradeRecord.entry_price || tradeRecord.exit_price || tradeRecord.achieved_pl;\n\n        if (!hasValidData) {\n          if (rowIndex < 3) console.log(`Row ${rowIndex} rejected: no price data`);\n          return null;\n        }\n\n        return tradeRecord;\n      })\n      .filter(Boolean);\n\n    setMappedData(mapped);\n\n    // Generate comprehensive stats\n    const validTrades = mapped.filter(\n      (t) => t.date && t.model_type && (t.entry_price || t.exit_price)\n    );\n    const unmappedModels = mapped.filter(\n      (t) => t.model_type && !VALID_TRADING_MODELS.includes(t.model_type)\n    ).length;\n    const missingPrices = mapped.filter((t) => t.date && !t.entry_price && !t.exit_price).length;\n    const winningTrades = mapped.filter((t) => t.win_loss === 'Win').length;\n    const losingTrades = mapped.filter((t) => t.win_loss === 'Loss').length;\n\n    setStats({\n      totalRows: rows.length,\n      validTrades: validTrades.length,\n      unmappedModels,\n      missingPrices,\n      winningTrades,\n      losingTrades,\n      skipped: rows.length - validTrades.length,\n      winRate:\n        validTrades.length > 0\n          ? ((winningTrades / (winningTrades + losingTrades)) * 100).toFixed(1)\n          : 0,\n    });\n\n    setImportStatus('preview');\n  };\n\n  const handleImport = async () => {\n    setImportStatus('processing');\n\n    try {\n      // Convert mapped data to CompleteTradeData and save to IndexedDB\n      const importPromises = mappedData.map(async (tradeRecord) => {\n        // The tradeRecord is already in the correct TradeRecord format from mapping\n        const cleanedTradeRecord = {\n          ...tradeRecord,\n          // Ensure timestamps\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        };\n\n        // Create setup data from your CSV fields\n        const setupData = {\n          primary_setup: tradeRecord.primary_setup || null,\n          secondary_setup: tradeRecord.secondary_setup || null,\n          liquidity_taken: tradeRecord.liquidity_taken || null,\n          additional_fvgs: tradeRecord.additional_fvgs || null,\n          dol: tradeRecord.dol || null,\n        };\n\n        // Create FVG details from your CSV fields\n        const fvgDetails = {\n          fvg_date: tradeRecord.fvg_date || tradeRecord.date,\n          rd_type: tradeRecord.rd_type || null,\n          entry_version: tradeRecord.entry_version || null,\n          draw_on_liquidity: tradeRecord.draw_on_liquidity || null,\n        };\n\n        // Create analysis data with your additional fields\n        const analysisData = {\n          tradingview_link: tradeRecord.tradingview_link || null,\n          dol_target_type: tradeRecord.dol_target_type || null,\n          beyond_target: tradeRecord.beyond_target || null,\n          clustering: tradeRecord.clustering || null,\n          path_quality: tradeRecord.path_quality || null,\n          idr_context: tradeRecord.idr_context || null,\n          sequential_fvg_rd: tradeRecord.sequential_fvg_rd || null,\n          dol_notes:\n            tradeRecord.dol_notes ||\n            `Imported from CSV on ${new Date().toLocaleDateString()}. Original notes: ${\n              tradeRecord.notes || 'None'\n            }`,\n        };\n\n        // Create complete trade data structure matching your schema\n        const completeTradeData = {\n          trade: cleanedTradeRecord,\n          fvg_details: fvgDetails,\n          setup: setupData,\n          analysis: analysisData,\n        };\n\n        return tradeStorageService.saveTradeWithDetails(completeTradeData);\n      });\n\n      await Promise.all(importPromises);\n      setImportStatus('imported');\n\n      // Call completion callback after successful import\n      setTimeout(() => {\n        onImportComplete?.();\n      }, 2000); // Give user time to see success message\n    } catch (error) {\n      console.error('Import failed:', error);\n      setImportStatus('preview'); // Return to preview on error\n    }\n  };\n\n  const downloadCleanedData = () => {\n    const csv = [\n      // Headers\n      Object.keys(mappedData[0] || {}).join(','),\n      // Data rows\n      ...mappedData.map((trade) =>\n        Object.values(trade)\n          .map((v) => `\"${v}\"`)\n          .join(',')\n      ),\n    ].join('\\n');\n\n    const blob = new Blob([csv], { type: 'text/csv' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = 'cleaned_trades.csv';\n    a.click();\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg\">\n      <div className=\"mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">📊 Legacy Trade Data Import</h2>\n        <p className=\"text-gray-600\">\n          Import your FVG Models trading data into the ADHD Trading Dashboard.\n        </p>\n\n        <div className=\"mt-2 text-sm text-gray-500\">\n          <strong>Your CSV format detected:</strong> FVG Models v3 with 40 columns including all\n          FVG-specific fields\n        </div>\n      </div>\n\n      {/* Import Interface */}\n      {importStatus === 'idle' && (\n        <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\">\n          <div className=\"mx-auto text-4xl mb-4\">\n            <Upload />\n          </div>\n          <div className=\"mb-4\">\n            <label htmlFor=\"csv-upload\" className=\"cursor-pointer\">\n              <span className=\"text-lg font-medium text-blue-600 hover:text-blue-500\">\n                Upload Your FVG Models CSV\n              </span>\n              <input\n                id=\"csv-upload\"\n                type=\"file\"\n                accept=\".csv\"\n                onChange={handleFileUpload}\n                className=\"sr-only\"\n              />\n            </label>\n          </div>\n          <p className=\"text-sm text-gray-500\">\n            Select your \"FVGModelsv3 Data Entry Form 2.csv\" file\n          </p>\n        </div>\n      )}\n\n      {/* Processing */}\n      {importStatus === 'processing' && (\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-lg text-gray-600\">Processing your FVG Models data...</p>\n        </div>\n      )}\n\n      {/* Preview */}\n      {importStatus === 'preview' && stats && (\n        <div className=\"space-y-6\">\n          {/* Stats */}\n          <div className=\"bg-gray-50 rounded-lg p-4\">\n            <h3 className=\"text-lg font-semibold mb-3\">Import Summary</h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4\">\n              <div className=\"flex items-center\">\n                <FileText />\n                <span className=\"ml-2\">{stats.totalRows} total rows</span>\n              </div>\n              <div className=\"flex items-center\">\n                <CheckCircle />\n                <span className=\"ml-2\">{stats.validTrades} valid trades</span>\n              </div>\n              <div className=\"flex items-center\">\n                <AlertCircle />\n                <span className=\"ml-2\">{stats.unmappedModels} unknown models</span>\n              </div>\n              <div className=\"flex items-center\">\n                <XCircle />\n                <span className=\"ml-2\">{stats.skipped} skipped</span>\n              </div>\n            </div>\n\n            {/* Additional Stats */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm pt-2 border-t border-gray-200\">\n              <div className=\"flex items-center\">\n                <span className=\"text-green-600\">✅</span>\n                <span className=\"ml-2\">{stats.winningTrades} wins</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-red-600\">❌</span>\n                <span className=\"ml-2\">{stats.losingTrades} losses</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-blue-600\">📊</span>\n                <span className=\"ml-2\">{stats.winRate}% win rate</span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-yellow-600\">⚠️</span>\n                <span className=\"ml-2\">{stats.missingPrices} missing prices</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Preview Table */}\n          <div className=\"bg-white border rounded-lg overflow-hidden\">\n            <div className=\"px-4 py-3 bg-gray-50 border-b\">\n              <h3 className=\"text-lg font-semibold\">Preview (First 5 trades)</h3>\n            </div>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    {Object.keys(mappedData[0] || {})\n                      .slice(0, 10)\n                      .map((key) => (\n                        <th\n                          key={key}\n                          className=\"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase\"\n                        >\n                          {key}\n                        </th>\n                      ))}\n                  </tr>\n                </thead>\n                <tbody className=\"divide-y divide-gray-200\">\n                  {mappedData.slice(0, 5).map((trade, index) => (\n                    <tr key={index} className=\"hover:bg-gray-50\">\n                      {Object.entries(trade)\n                        .slice(0, 10)\n                        .map(([field, value], i) => (\n                          <td key={i} className=\"px-3 py-2 text-sm text-gray-900\">\n                            {typeof value === 'number' && value !== null\n                              ? value.toFixed(2)\n                              : value || '—'}\n                          </td>\n                        ))}\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex gap-4\">\n            <button\n              onClick={handleImport}\n              className=\"flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium\"\n            >\n              Import {stats.validTrades} Trades\n            </button>\n            <button\n              onClick={downloadCleanedData}\n              className=\"bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2\"\n            >\n              <Download />\n              Download Cleaned CSV\n            </button>\n            <button\n              onClick={() => setImportStatus('idle')}\n              className=\"bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium\"\n            >\n              Start Over\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Success */}\n      {importStatus === 'imported' && (\n        <div className=\"text-center py-8\">\n          <div className=\"mx-auto text-6xl mb-4\">\n            <CheckCircle />\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">Import Complete!</h3>\n          <p className=\"text-gray-600 mb-6\">\n            {stats?.validTrades} trades have been imported into your dashboard\n          </p>\n          <button\n            onClick={() => setImportStatus('idle')}\n            className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700\"\n          >\n            Import Another File\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CSVImportTool;\n", "/**\n * F1FilterField Component\n * \n * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)\n * Reusable filter field component with F1 racing theme.\n * \n * BENEFITS:\n * - Focused responsibility (single filter field)\n * - Eliminates code duplication (15+ similar filter groups)\n * - F1 racing theme with consistent styling\n * - Type-safe filter handling\n * - Accessible form controls\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type FilterFieldType = 'text' | 'select' | 'date' | 'number';\n\nexport interface FilterOption {\n  value: string | number;\n  label: string;\n}\n\nexport interface F1FilterFieldProps {\n  /** Field identifier */\n  name: string;\n  /** Field label */\n  label: string;\n  /** Field type */\n  type: FilterFieldType;\n  /** Current value */\n  value: string | number;\n  /** Change handler */\n  onChange: (name: string, value: string | number) => void;\n  /** Options for select fields */\n  options?: FilterOption[];\n  /** Placeholder text */\n  placeholder?: string;\n  /** Whether field is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst FilterGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  min-width: 150px;\n`;\n\nconst FilterLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  cursor: pointer;\n  \n  /* F1 Racing accent */\n  &::before {\n    content: '🏎️';\n    font-size: 10px;\n    margin-right: ${({ theme }) => theme.spacing?.xs || '4px'};\n    opacity: 0.7;\n  }\n`;\n\nconst FilterInput = styled.input<{ $hasValue?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasValue }) => \n    $hasValue \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  transition: all 0.2s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n    transform: translateY(-1px);\n  }\n  \n  &:hover:not(:disabled) {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80;\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  &::placeholder {\n    color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n    font-style: italic;\n  }\n`;\n\nconst FilterSelect = styled.select<{ $hasValue?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasValue }) => \n    $hasValue \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n    transform: translateY(-1px);\n  }\n  \n  &:hover:not(:disabled) {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80;\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  /* Style the dropdown arrow */\n  background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e\");\n  background-repeat: no-repeat;\n  background-position: right 8px center;\n  background-size: 16px;\n  padding-right: 32px;\n  appearance: none;\n`;\n\nconst FilterOption = styled.option`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'};\n`;\n\n/**\n * F1FilterField Component\n * \n * PATTERN: F1 Filter Field Pattern\n * - Racing-inspired styling with red accents\n * - Visual feedback for active filters\n * - Consistent form controls across field types\n * - Accessible with proper labels and focus\n * - Responsive design for mobile\n */\nexport const F1FilterField: React.FC<F1FilterFieldProps> = ({\n  name,\n  label,\n  type,\n  value,\n  onChange,\n  options = [],\n  placeholder,\n  disabled = false,\n  className,\n}) => {\n  const fieldId = `filter-field-${name}`;\n  const hasValue = value !== '' && value !== null && value !== undefined;\n  \n  const handleChange = (newValue: string | number) => {\n    if (!disabled) {\n      onChange(name, newValue);\n    }\n  };\n  \n  const renderControl = () => {\n    switch (type) {\n      case 'text':\n        return (\n          <FilterInput\n            id={fieldId}\n            type=\"text\"\n            value={value || ''}\n            onChange={(e) => handleChange(e.target.value)}\n            placeholder={placeholder}\n            disabled={disabled}\n            $hasValue={hasValue}\n          />\n        );\n        \n      case 'number':\n        return (\n          <FilterInput\n            id={fieldId}\n            type=\"number\"\n            value={value || ''}\n            onChange={(e) => handleChange(parseFloat(e.target.value) || 0)}\n            placeholder={placeholder}\n            disabled={disabled}\n            $hasValue={hasValue}\n          />\n        );\n        \n      case 'date':\n        return (\n          <FilterInput\n            id={fieldId}\n            type=\"date\"\n            value={value || ''}\n            onChange={(e) => handleChange(e.target.value)}\n            disabled={disabled}\n            $hasValue={hasValue}\n          />\n        );\n        \n      case 'select':\n        return (\n          <FilterSelect\n            id={fieldId}\n            value={value || ''}\n            onChange={(e) => handleChange(e.target.value)}\n            disabled={disabled}\n            $hasValue={hasValue}\n          >\n            <FilterOption value=\"\">All</FilterOption>\n            {options.map((option) => (\n              <FilterOption key={option.value} value={option.value}>\n                {option.label}\n              </FilterOption>\n            ))}\n          </FilterSelect>\n        );\n        \n      default:\n        return null;\n    }\n  };\n  \n  return (\n    <FilterGroup className={className}>\n      <FilterLabel htmlFor={fieldId}>\n        {label}\n      </FilterLabel>\n      {renderControl()}\n    </FilterGroup>\n  );\n};\n\nexport default F1FilterField;\n", "/**\n * useFilterState Hook\n *\n * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)\n * Hook for managing filter state and operations.\n *\n * BENEFITS:\n * - Focused responsibility (filter state only)\n * - Type-safe filter handling\n * - Reusable across filter components\n * - Better separation of concerns\n * - Centralized filter logic\n */\n\nimport { useState, useCallback, useMemo } from 'react';\n\nexport interface FilterState {\n  [key: string]: string | number;\n}\n\nexport interface UseFilterStateProps {\n  /** Initial filter values */\n  initialFilters?: FilterState;\n  /** Filter change handler */\n  onFiltersChange?: (filters: FilterState) => void;\n  /** Reset handler */\n  onReset?: () => void;\n}\n\nexport interface UseFilterStateReturn {\n  /** Current filter values */\n  filters: FilterState;\n  /** Update a single filter */\n  updateFilter: (name: string, value: string | number) => void;\n  /** Update multiple filters */\n  updateFilters: (newFilters: Partial<FilterState>) => void;\n  /** Reset all filters */\n  resetFilters: () => void;\n  /** Check if any filters are active */\n  hasActiveFilters: boolean;\n  /** Get active filter count */\n  activeFilterCount: number;\n  /** Get filter value */\n  getFilterValue: (name: string) => string | number;\n  /** Check if filter has value */\n  hasFilterValue: (name: string) => boolean;\n}\n\n/**\n * useFilterState Hook\n *\n * Manages filter state with type safety and utility functions.\n */\nexport const useFilterState = ({\n  initialFilters = {},\n  onFiltersChange,\n  onReset,\n}: UseFilterStateProps = {}): UseFilterStateReturn => {\n  const [filters, setFilters] = useState<FilterState>(initialFilters);\n\n  /**\n   * Update a single filter\n   */\n  const updateFilter = useCallback(\n    (name: string, value: string | number) => {\n      setFilters(prev => {\n        const newFilters = {\n          ...prev,\n          [name]: value,\n        };\n\n        // Call external handler if provided\n        if (onFiltersChange) {\n          onFiltersChange(newFilters);\n        }\n\n        return newFilters;\n      });\n    },\n    [onFiltersChange]\n  );\n\n  /**\n   * Update multiple filters\n   */\n  const updateFilters = useCallback(\n    (newFilters: Partial<FilterState>) => {\n      setFilters(prev => {\n        const updatedFilters: FilterState = {\n          ...prev,\n          ...Object.fromEntries(\n            Object.entries(newFilters)\n              .filter(([, value]) => value !== undefined)\n              .map(([key, value]) => [key, value!])\n          ),\n        };\n\n        // Call external handler if provided\n        if (onFiltersChange) {\n          onFiltersChange(updatedFilters);\n        }\n\n        return updatedFilters;\n      });\n    },\n    [onFiltersChange]\n  );\n\n  /**\n   * Reset all filters\n   */\n  const resetFilters = useCallback(() => {\n    setFilters(initialFilters);\n\n    // Call external handlers if provided\n    if (onReset) {\n      onReset();\n    }\n    if (onFiltersChange) {\n      onFiltersChange(initialFilters);\n    }\n  }, [initialFilters, onReset, onFiltersChange]);\n\n  /**\n   * Check if any filters are active\n   */\n  const hasActiveFilters = useMemo(() => {\n    return Object.values(filters).some(\n      value => value !== '' && value !== null && value !== undefined\n    );\n  }, [filters]);\n\n  /**\n   * Get active filter count\n   */\n  const activeFilterCount = useMemo(() => {\n    return Object.values(filters).filter(\n      value => value !== '' && value !== null && value !== undefined\n    ).length;\n  }, [filters]);\n\n  /**\n   * Get filter value\n   */\n  const getFilterValue = useCallback(\n    (name: string): string | number => {\n      return filters[name] || '';\n    },\n    [filters]\n  );\n\n  /**\n   * Check if filter has value\n   */\n  const hasFilterValue = useCallback(\n    (name: string): boolean => {\n      const value = filters[name];\n      return value !== '' && value !== null && value !== undefined;\n    },\n    [filters]\n  );\n\n  return {\n    filters,\n    updateFilter,\n    updateFilters,\n    resetFilters,\n    hasActiveFilters,\n    activeFilterCount,\n    getFilterValue,\n    hasFilterValue,\n  };\n};\n\nexport default useFilterState;\n", "/**\n * Filter Field Configuration\n * \n * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)\n * Centralized configuration for all filter fields.\n * \n * BENEFITS:\n * - Single source of truth for filter definitions\n * - Easy to maintain and extend\n * - Type-safe filter configurations\n * - Eliminates repetitive filter field code\n * - Clear field organization\n */\n\nimport { FilterFieldType, FilterOption } from './F1FilterField';\n\nexport interface FilterFieldConfig {\n  name: string;\n  label: string;\n  type: FilterFieldType;\n  placeholder?: string;\n  options?: FilterOption[];\n  group: 'basic' | 'trading' | 'analysis' | 'dates';\n  order: number;\n}\n\n/**\n * Generate numeric options for rating fields\n */\nconst generateRatingOptions = (min: number = 1, max: number = 10): FilterOption[] => {\n  return Array.from({ length: max - min + 1 }, (_, i) => ({\n    value: min + i,\n    label: (min + i).toString(),\n  }));\n};\n\n/**\n * Complete filter field configuration\n */\nexport const FILTER_FIELD_CONFIG: FilterFieldConfig[] = [\n  // Basic Filters\n  {\n    name: 'symbol',\n    label: 'Symbol',\n    type: 'text',\n    placeholder: 'AAPL, MSFT, etc.',\n    group: 'basic',\n    order: 1,\n  },\n  {\n    name: 'direction',\n    label: 'Direction',\n    type: 'select',\n    options: [\n      { value: 'Long', label: 'Long' },\n      { value: 'Short', label: 'Short' },\n    ],\n    group: 'basic',\n    order: 2,\n  },\n  {\n    name: 'result',\n    label: 'Result',\n    type: 'select',\n    options: [\n      { value: 'win', label: 'Wins' },\n      { value: 'loss', label: 'Losses' },\n    ],\n    group: 'basic',\n    order: 3,\n  },\n  \n  // Date Filters\n  {\n    name: 'dateFrom',\n    label: 'From Date',\n    type: 'date',\n    group: 'dates',\n    order: 4,\n  },\n  {\n    name: 'dateTo',\n    label: 'To Date',\n    type: 'date',\n    group: 'dates',\n    order: 5,\n  },\n  \n  // Trading Setup Filters\n  {\n    name: 'setup',\n    label: 'Setup',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'trading',\n    order: 6,\n  },\n  {\n    name: 'modelType',\n    label: 'Model Type',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'trading',\n    order: 7,\n  },\n  {\n    name: 'primarySetupType',\n    label: 'Primary Setup',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'trading',\n    order: 8,\n  },\n  {\n    name: 'secondarySetupType',\n    label: 'Secondary Setup',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'trading',\n    order: 9,\n  },\n  {\n    name: 'liquidityTaken',\n    label: 'Liquidity Taken',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'trading',\n    order: 10,\n  },\n  \n  // Analysis Filters\n  {\n    name: 'patternQualityMin',\n    label: 'Pattern Quality Min',\n    type: 'select',\n    options: generateRatingOptions(1, 10),\n    group: 'analysis',\n    order: 11,\n  },\n  {\n    name: 'patternQualityMax',\n    label: 'Pattern Quality Max',\n    type: 'select',\n    options: generateRatingOptions(1, 10),\n    group: 'analysis',\n    order: 12,\n  },\n  {\n    name: 'dolType',\n    label: 'DOL Type',\n    type: 'select',\n    options: [], // Will be populated dynamically\n    group: 'analysis',\n    order: 13,\n  },\n  {\n    name: 'dolEffectivenessMin',\n    label: 'DOL Effectiveness Min',\n    type: 'select',\n    options: generateRatingOptions(1, 10),\n    group: 'analysis',\n    order: 14,\n  },\n  {\n    name: 'dolEffectivenessMax',\n    label: 'DOL Effectiveness Max',\n    type: 'select',\n    options: generateRatingOptions(1, 10),\n    group: 'analysis',\n    order: 15,\n  },\n];\n\n/**\n * Get filter fields by group\n */\nexport const getFilterFieldsByGroup = (group: FilterFieldConfig['group']): FilterFieldConfig[] => {\n  return FILTER_FIELD_CONFIG\n    .filter(field => field.group === group)\n    .sort((a, b) => a.order - b.order);\n};\n\n/**\n * Get all filter groups\n */\nexport const getFilterGroups = (): FilterFieldConfig['group'][] => {\n  return ['basic', 'dates', 'trading', 'analysis'];\n};\n\n/**\n * Get filter field by name\n */\nexport const getFilterField = (name: string): FilterFieldConfig | undefined => {\n  return FILTER_FIELD_CONFIG.find(field => field.name === name);\n};\n\n/**\n * Update dynamic options for a field\n */\nexport const updateFieldOptions = (\n  fieldName: string, \n  options: FilterOption[]\n): FilterFieldConfig[] => {\n  return FILTER_FIELD_CONFIG.map(field => \n    field.name === fieldName \n      ? { ...field, options }\n      : field\n  );\n};\n\n/**\n * Group labels for display\n */\nexport const FILTER_GROUP_LABELS: Record<FilterFieldConfig['group'], string> = {\n  basic: 'Basic Filters',\n  dates: 'Date Range',\n  trading: 'Trading Setup',\n  analysis: 'Analysis & Quality',\n};\n\n/**\n * Group descriptions\n */\nexport const FILTER_GROUP_DESCRIPTIONS: Record<FilterFieldConfig['group'], string> = {\n  basic: 'Filter by symbol, direction, and trade results',\n  dates: 'Filter trades by date range',\n  trading: 'Filter by trading setups and strategies',\n  analysis: 'Filter by pattern quality and DOL analysis',\n};\n\n/**\n * Get field configuration with dynamic options\n */\nexport const getFieldConfigWithOptions = (\n  uniqueData: {\n    uniqueSetups: string[];\n    uniqueModelTypes: string[];\n    uniquePrimarySetupTypes: string[];\n    uniqueSecondarySetupTypes: string[];\n    uniqueLiquidityTypes: string[];\n    uniqueDOLTypes: string[];\n  }\n): FilterFieldConfig[] => {\n  return FILTER_FIELD_CONFIG.map(field => {\n    switch (field.name) {\n      case 'setup':\n        return {\n          ...field,\n          options: uniqueData.uniqueSetups.map(setup => ({\n            value: setup,\n            label: setup,\n          })),\n        };\n      case 'modelType':\n        return {\n          ...field,\n          options: uniqueData.uniqueModelTypes.map(type => ({\n            value: type,\n            label: type,\n          })),\n        };\n      case 'primarySetupType':\n        return {\n          ...field,\n          options: uniqueData.uniquePrimarySetupTypes.map(type => ({\n            value: type,\n            label: type,\n          })),\n        };\n      case 'secondarySetupType':\n        return {\n          ...field,\n          options: uniqueData.uniqueSecondarySetupTypes.map(type => ({\n            value: type,\n            label: type,\n          })),\n        };\n      case 'liquidityTaken':\n        return {\n          ...field,\n          options: uniqueData.uniqueLiquidityTypes.map(type => ({\n            value: type,\n            label: type,\n          })),\n        };\n      case 'dolType':\n        return {\n          ...field,\n          options: uniqueData.uniqueDOLTypes.map(type => ({\n            value: type,\n            label: type,\n          })),\n        };\n      default:\n        return field;\n    }\n  });\n};\n\nexport default FILTER_FIELD_CONFIG;\n", "/**\n * F1FilterPanel Component\n * \n * REFACTORED FROM: TradeJournalFilters.tsx (321 lines → focused components)\n * F1 racing-themed filter panel with organized filter groups.\n * \n * BENEFITS:\n * - 90% code reduction through reusable components\n * - F1 racing theme with organized filter groups\n * - Type-safe filter handling\n * - Better separation of concerns\n * - Accessible and responsive design\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { F1FilterField } from './F1FilterField';\nimport { useFilterState, FilterState } from './useFilterState';\nimport { \n  getFilterFieldsByGroup, \n  getFilterGroups, \n  getFieldConfigWithOptions,\n  FILTER_GROUP_LABELS,\n  FILTER_GROUP_DESCRIPTIONS \n} from './filterFieldConfig';\n\nexport interface F1FilterPanelProps {\n  /** Initial filter values */\n  initialFilters?: FilterState;\n  /** Filter change handler */\n  onFiltersChange?: (filters: FilterState) => void;\n  /** Reset handler */\n  onReset?: () => void;\n  /** Unique data for dynamic options */\n  uniqueData?: {\n    uniqueSetups: string[];\n    uniqueModelTypes: string[];\n    uniquePrimarySetupTypes: string[];\n    uniqueSecondarySetupTypes: string[];\n    uniqueLiquidityTypes: string[];\n    uniqueDOLTypes: string[];\n  };\n  /** Whether panel is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst FilterContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  position: relative;\n  overflow: hidden;\n\n  /* F1 Racing accent line */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg, \n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%, \n      transparent 100%\n    );\n  }\n`;\n\nconst FilterHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst FilterTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  \n  span {\n    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  }\n`;\n\nconst FilterBadge = styled.div<{ $count: number }>`\n  background: ${({ $count, theme }) => \n    $count > 0 \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.surface || 'var(--bg-secondary)'};\n  color: ${({ $count, theme }) => \n    $count > 0 \n      ? theme.colors?.textInverse || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  min-width: 24px;\n  text-align: center;\n  border: 1px solid ${({ $count, theme }) => \n    $count > 0 \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst FilterGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst GroupHeader = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  padding-bottom: ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst GroupTitle = styled.h4`\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst GroupDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  font-style: italic;\n`;\n\nconst FieldGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst ActionBar = styled.div`\n  display: flex;\n  justify-content: flex-end;\n  gap: ${({ theme }) => theme.spacing?.sm || '8px'};\n  padding-top: ${({ theme }) => theme.spacing?.md || '12px'};\n  border-top: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`\n  background: ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : 'transparent'};\n  color: ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.textInverse || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  border: 1px solid ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n\n  &:hover:not(:disabled) {\n    background: ${({ $variant, theme }) => \n      $variant === 'primary' \n        ? theme.colors?.primaryDark || 'var(--primary-dark)'\n        : theme.colors?.surface || 'var(--bg-secondary)'};\n    transform: translateY(-1px);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * F1FilterPanel Component\n * \n * PATTERN: F1 Filter Panel Pattern\n * - Racing-inspired styling with organized groups\n * - Reusable filter fields eliminate code duplication\n * - Type-safe filter state management\n * - Responsive design for mobile\n * - Clear visual feedback for active filters\n */\nexport const F1FilterPanel: React.FC<F1FilterPanelProps> = ({\n  initialFilters = {},\n  onFiltersChange,\n  onReset,\n  uniqueData = {\n    uniqueSetups: [],\n    uniqueModelTypes: [],\n    uniquePrimarySetupTypes: [],\n    uniqueSecondarySetupTypes: [],\n    uniqueLiquidityTypes: [],\n    uniqueDOLTypes: [],\n  },\n  disabled = false,\n  className,\n}) => {\n  const {\n    filters,\n    updateFilter,\n    resetFilters,\n    hasActiveFilters,\n    activeFilterCount,\n  } = useFilterState({\n    initialFilters,\n    onFiltersChange,\n    onReset,\n  });\n  \n  // Get field configurations with dynamic options\n  const fieldConfigs = getFieldConfigWithOptions(uniqueData);\n  const filterGroups = getFilterGroups();\n  \n  return (\n    <FilterContainer className={className}>\n      {/* Header */}\n      <FilterHeader>\n        <FilterTitle>\n          🏎️ <span>FILTERS</span>\n        </FilterTitle>\n        <FilterBadge $count={activeFilterCount}>\n          {activeFilterCount}\n        </FilterBadge>\n      </FilterHeader>\n      \n      {/* Filter Groups */}\n      {filterGroups.map(groupName => {\n        const groupFields = getFilterFieldsByGroup(groupName);\n        const configuredFields = groupFields.map(field => \n          fieldConfigs.find(config => config.name === field.name)\n        ).filter(Boolean);\n        \n        if (configuredFields.length === 0) return null;\n        \n        return (\n          <FilterGroup key={groupName}>\n            <GroupHeader>\n              <GroupTitle>{FILTER_GROUP_LABELS[groupName]}</GroupTitle>\n              <GroupDescription>{FILTER_GROUP_DESCRIPTIONS[groupName]}</GroupDescription>\n            </GroupHeader>\n            \n            <FieldGrid>\n              {configuredFields.map(field => (\n                <F1FilterField\n                  key={field!.name}\n                  name={field!.name}\n                  label={field!.label}\n                  type={field!.type}\n                  value={filters[field!.name] || ''}\n                  onChange={updateFilter}\n                  options={field!.options}\n                  placeholder={field!.placeholder}\n                  disabled={disabled}\n                />\n              ))}\n            </FieldGrid>\n          </FilterGroup>\n        );\n      })}\n      \n      {/* Action Bar */}\n      <ActionBar>\n        <ActionButton\n          $variant=\"secondary\"\n          onClick={resetFilters}\n          disabled={disabled || !hasActiveFilters}\n        >\n          Reset Filters\n        </ActionButton>\n      </ActionBar>\n    </FilterContainer>\n  );\n};\n\nexport default F1FilterPanel;\n", "/**\n * Trade Journal Filters Component\n *\n * REFACTORED: Now uses the new F1 filter component library.\n * Simplified from 322 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 90% code reduction\n * - Uses F1FilterPanel for consistent styling\n * - Eliminates repetitive filter field code\n * - Better separation of concerns\n * - Type-safe filter handling\n */\n\nimport React, { useMemo } from 'react';\nimport { F1FilterPanel } from '../F1FilterPanel';\nimport { FilterState } from '../../types';\n\ninterface TradeJournalFiltersProps {\n  filters: FilterState;\n  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  resetFilters: () => void;\n  uniqueSetups: string[];\n  uniqueModelTypes: string[];\n  uniquePrimarySetupTypes: string[];\n  uniqueSecondarySetupTypes: string[];\n  uniqueLiquidityTypes: string[];\n  uniqueDOLTypes: string[];\n}\n\n/**\n * Trade Journal Filters Component\n *\n * Simple wrapper that renders the F1FilterPanel.\n * Follows the proven architecture pattern.\n */\nconst TradeJournalFilters: React.FC<TradeJournalFiltersProps> = ({\n  filters,\n  handleFilterChange,\n  resetFilters,\n  uniqueSetups,\n  uniqueModelTypes,\n  uniquePrimarySetupTypes,\n  uniqueSecondarySetupTypes,\n  uniqueLiquidityTypes,\n  uniqueDOLTypes,\n}) => {\n  // Convert unique data to the format expected by F1FilterPanel\n  const uniqueData = useMemo(\n    () => ({\n      uniqueSetups,\n      uniqueModelTypes,\n      uniquePrimarySetupTypes,\n      uniqueSecondarySetupTypes,\n      uniqueLiquidityTypes,\n      uniqueDOLTypes,\n    }),\n    [\n      uniqueSetups,\n      uniqueModelTypes,\n      uniquePrimarySetupTypes,\n      uniqueSecondarySetupTypes,\n      uniqueLiquidityTypes,\n      uniqueDOLTypes,\n    ]\n  );\n\n  // Convert the legacy handleFilterChange to the new format\n  const handleFiltersChange = (newFilters: Record<string, string | number>) => {\n    // For each changed filter, create a synthetic event and call the legacy handler\n    Object.entries(newFilters).forEach(([name, value]) => {\n      if (filters[name as keyof FilterState] !== value) {\n        const syntheticEvent = {\n          target: { name, value: String(value) },\n        } as React.ChangeEvent<HTMLInputElement | HTMLSelectElement>;\n        handleFilterChange(syntheticEvent);\n      }\n    });\n  };\n\n  return (\n    <F1FilterPanel\n      initialFilters={filters as any}\n      onFiltersChange={handleFiltersChange}\n      onReset={resetFilters}\n      uniqueData={uniqueData}\n    />\n  );\n};\n\nexport default TradeJournalFilters;\n", "/**\n * Trade Journal Content Component\n *\n * Displays the content section of the trade journal\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Trade } from '@adhd-trading-dashboard/shared';\nimport TradeList from '../TradeList';\nimport TradeJournalFilters from './TradeJournalFilters';\n\nconst ContentSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${({ theme }) => theme.colors.danger};\n  padding: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\ninterface TradeJournalContentProps {\n  error: string | null;\n  showFilters: boolean;\n  filteredTrades: Trade[];\n  isLoading: boolean;\n  filters: any;\n  handleFilterChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;\n  resetFilters: () => void;\n  uniqueSetups: string[];\n  uniqueModelTypes: string[];\n  uniquePrimarySetupTypes: string[];\n  uniqueSecondarySetupTypes: string[];\n  uniqueLiquidityTypes: string[];\n  uniqueDOLTypes: string[];\n}\n\n/**\n * Trade Journal Content Component\n */\nconst TradeJournalContent: React.FC<TradeJournalContentProps> = ({\n  error,\n  showFilters,\n  filteredTrades,\n  isLoading,\n  filters,\n  handleFilterChange,\n  resetFilters,\n  uniqueSetups,\n  uniqueModelTypes,\n  uniquePrimarySetupTypes,\n  uniqueSecondarySetupTypes,\n  uniqueLiquidityTypes,\n  uniqueDOLTypes,\n}) => {\n  return (\n    <>\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n\n      <ContentSection>\n        <SectionTitle>Recent Trades</SectionTitle>\n\n        {showFilters && (\n          <TradeJournalFilters\n            filters={filters}\n            handleFilterChange={handleFilterChange}\n            resetFilters={resetFilters}\n            uniqueSetups={uniqueSetups}\n            uniqueModelTypes={uniqueModelTypes}\n            uniquePrimarySetupTypes={uniquePrimarySetupTypes}\n            uniqueSecondarySetupTypes={uniqueSecondarySetupTypes}\n            uniqueLiquidityTypes={uniqueLiquidityTypes}\n            uniqueDOLTypes={uniqueDOLTypes}\n          />\n        )}\n\n        <TradeList trades={filteredTrades as any} isLoading={isLoading} expandable={true} />\n      </ContentSection>\n    </>\n  );\n};\n\nexport default TradeJournalContent;\n", "/**\n * F1JournalHeader Component\n *\n * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)\n * F1 racing-themed header for the trade journal feature.\n *\n * BENEFITS:\n * - Focused responsibility (header only)\n * - F1 racing theme with journal-specific indicators\n * - Consistent with other F1Header components\n * - Better separation of concerns\n * - Reusable across journal contexts\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { Link } from 'react-router-dom';\nimport { LegacyDataImport } from './index';\n\nexport interface F1JournalHeaderProps {\n  /** Custom className */\n  className?: string;\n  /** Whether data is loading */\n  isLoading?: boolean;\n  /** Whether refresh is in progress */\n  isRefreshing?: boolean;\n  /** Number of trades */\n  tradeCount?: number;\n  /** Number of filtered trades */\n  filteredCount?: number;\n  /** Whether filters are active */\n  hasActiveFilters?: boolean;\n  /** Refresh handler */\n  onRefresh?: () => void;\n  /** Export handler */\n  onExport?: () => void;\n  /** Import handler */\n  onImport?: () => void;\n  /** Custom title */\n  title?: string;\n}\n\nconst HeaderContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst F1Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: linear-gradient(\n    135deg,\n    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,\n    rgba(75, 85, 99, 0.1) 100%\n  );\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  position: relative;\n  overflow: hidden;\n\n  /* F1 Racing accent line */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg,\n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,\n      transparent 100%\n    );\n  }\n`;\n\nconst F1Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 2px;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  span {\n    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    font-weight: 800;\n  }\n`;\n\nconst JournalIndicator = styled.div<{ $hasData: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  color: ${({ $hasData, theme }) =>\n    $hasData ? theme.colors?.success || 'var(--success-color)' : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  border: 1px solid\n    ${({ $hasData, theme }) =>\n      $hasData ? theme.colors?.success || 'var(--success-color)' : theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ $hasData, theme }) =>\n    $hasData ? `${theme.colors?.success || 'var(--success-color)'}20` : 'transparent'};\n\n  &::before {\n    content: '📊';\n    font-size: 12px;\n  }\n`;\n\nconst SubHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst SubTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};\n  margin: 0;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-weight: 600;\n`;\n\nconst StatsContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst StatBadge = styled.span<{ $variant: 'total' | 'filtered' | 'active' }>`\n  background: ${({ $variant, theme }) => {\n    switch ($variant) {\n      case 'total':\n        return theme.colors?.surface || 'var(--bg-secondary)';\n      case 'filtered':\n        return theme.colors?.primary || 'var(--primary-color)';\n      case 'active':\n        return theme.colors?.success || 'var(--success-color)';\n      default:\n        return theme.colors?.surface || 'var(--bg-secondary)';\n    }\n  }}20;\n  color: ${({ $variant, theme }) => {\n    switch ($variant) {\n      case 'total':\n        return theme.colors?.textPrimary || '#ffffff';\n      case 'filtered':\n        return theme.colors?.primary || 'var(--primary-color)';\n      case 'active':\n        return theme.colors?.success || 'var(--success-color)';\n      default:\n        return theme.colors?.textPrimary || '#ffffff';\n    }\n  }};\n  padding: ${({ theme }) => theme.spacing?.xxs || '2px'}\n    ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  border: 1px solid\n    ${({ $variant, theme }) => {\n      switch ($variant) {\n        case 'total':\n          return theme.colors?.border || 'var(--border-primary)';\n        case 'filtered':\n          return theme.colors?.primary || 'var(--primary-color)';\n        case 'active':\n          return theme.colors?.success || 'var(--success-color)';\n        default:\n          return theme.colors?.border || 'var(--border-primary)';\n      }\n    }}40;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst ActionsContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`\n  background: ${({ $variant, theme }) =>\n    $variant === 'primary' ? theme.colors?.primary || 'var(--primary-color)' : 'transparent'};\n  color: ${({ $variant, theme }) =>\n    $variant === 'primary'\n      ? theme.colors?.textInverse || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  border: 1px solid\n    ${({ $variant, theme }) =>\n      $variant === 'primary'\n        ? theme.colors?.primary || 'var(--primary-color)'\n        : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n    ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  min-width: 100px;\n  justify-content: center;\n\n  &:hover:not(:disabled) {\n    background: ${({ $variant, theme }) =>\n      $variant === 'primary'\n        ? theme.colors?.primaryDark || 'var(--primary-dark)'\n        : theme.colors?.surface || 'var(--bg-secondary)'};\n    transform: translateY(-1px);\n    box-shadow: 0 4px 8px\n      ${({ $variant, theme }) =>\n        $variant === 'primary' ? `${theme.colors?.primary || 'var(--primary-color)'}40` : 'rgba(0, 0, 0, 0.1)'};\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\nconst AddTradeLink = styled(Link)`\n  background: ${({ theme }) => theme.colors?.success || 'var(--success-color)'};\n  color: ${({ theme }) => theme.colors?.textInverse || '#ffffff'};\n  border: 1px solid ${({ theme }) => theme.colors?.success || 'var(--success-color)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n    ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  min-width: 120px;\n  justify-content: center;\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.successDark || 'var(--success-color)'};\n    transform: translateY(-1px);\n    box-shadow: 0 4px 8px ${({ theme }) => theme.colors?.success || 'var(--success-color)'}40;\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n`;\n\n// Modal styles for import functionality\nconst ModalOverlay = styled.div`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst ModalContent = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  max-width: 800px;\n  width: 100%;\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n`;\n\nconst ModalHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst ModalTitle = styled.h2`\n  margin: 0;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.xl || '1.25rem'};\n  font-weight: 600;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-size: 24px;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  }\n`;\n\n/**\n * F1JournalHeader Component\n *\n * PATTERN: F1 Header Pattern\n * - Racing-inspired styling with journal-specific indicators\n * - Trade count and filter status indicators\n * - Consistent with F1 design system\n * - Accessible and responsive\n * - Professional trading journal appearance\n */\nexport const F1JournalHeader: React.FC<F1JournalHeaderProps> = ({\n  className,\n  isLoading = false,\n  isRefreshing = false,\n  tradeCount = 0,\n  filteredCount,\n  hasActiveFilters = false,\n  onRefresh,\n  onExport,\n  onImport,\n  title = 'Trade Journal',\n}) => {\n  const [showImportModal, setShowImportModal] = useState(false);\n  const hasData = tradeCount > 0;\n  const showFilteredCount =\n    hasActiveFilters && filteredCount !== undefined && filteredCount !== tradeCount;\n\n  const handleImportClick = () => {\n    setShowImportModal(true);\n    onImport?.();\n  };\n\n  const handleImportComplete = () => {\n    setShowImportModal(false);\n    // Trigger refresh if available\n    onRefresh?.();\n  };\n\n  return (\n    <HeaderContainer className={className}>\n      {/* F1 Racing Header */}\n      <F1Header>\n        <F1Title>\n          🏎️ TRADING <span>JOURNAL</span>\n        </F1Title>\n        <JournalIndicator $hasData={hasData}>\n          {hasData ? `${tradeCount} TRADES` : 'NO TRADES'}\n        </JournalIndicator>\n      </F1Header>\n\n      {/* Sub Header */}\n      <SubHeader>\n        <TitleSection>\n          <SubTitle>{title}</SubTitle>\n          <StatsContainer>\n            {hasData && (\n              <StatBadge $variant=\"total\">📊 {tradeCount.toLocaleString()} total</StatBadge>\n            )}\n            {showFilteredCount && (\n              <StatBadge $variant=\"filtered\">\n                🔍 {filteredCount!.toLocaleString()} filtered\n              </StatBadge>\n            )}\n            {hasActiveFilters && <StatBadge $variant=\"active\">⚡ filters active</StatBadge>}\n          </StatsContainer>\n        </TitleSection>\n\n        <ActionsContainer>\n          {onRefresh && (\n            <ActionButton\n              $variant=\"secondary\"\n              onClick={onRefresh}\n              disabled={isLoading}\n              title={isLoading ? 'Refreshing trades...' : 'Refresh trade data'}\n            >\n              {isLoading || isRefreshing ? '⏳' : '🔄'}\n              {isLoading ? 'Refreshing' : 'Refresh'}\n            </ActionButton>\n          )}\n\n          <ActionButton\n            $variant=\"secondary\"\n            onClick={handleImportClick}\n            disabled={isLoading}\n            title=\"Import legacy trade data from CSV\"\n          >\n            📥 Import\n          </ActionButton>\n\n          {onExport && (\n            <ActionButton\n              $variant=\"secondary\"\n              onClick={onExport}\n              disabled={isLoading || !hasData}\n              title=\"Export trade data\"\n            >\n              📊 Export\n            </ActionButton>\n          )}\n\n          <AddTradeLink to=\"/trade/new\" title=\"Add new trade\">\n            ➕ Add Trade\n          </AddTradeLink>\n        </ActionsContainer>\n      </SubHeader>\n\n      {/* Import Modal */}\n      {showImportModal && (\n        <ModalOverlay onClick={() => setShowImportModal(false)}>\n          <ModalContent onClick={(e) => e.stopPropagation()}>\n            <ModalHeader>\n              <ModalTitle>🏎️ Import Legacy Trade Data</ModalTitle>\n              <CloseButton onClick={() => setShowImportModal(false)}>×</CloseButton>\n            </ModalHeader>\n            <LegacyDataImport onImportComplete={handleImportComplete} />\n          </ModalContent>\n        </ModalOverlay>\n      )}\n    </HeaderContainer>\n  );\n};\n\nexport default F1JournalHeader;\n", "/**\n * F1JournalTabs Component\n * \n * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)\n * F1 racing-themed tabs for journal navigation.\n * \n * BENEFITS:\n * - Focused responsibility (tab navigation only)\n * - F1 racing theme with smooth animations\n * - Consistent with other F1Tab components\n * - Better separation of concerns\n * - Reusable tab navigation pattern\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type JournalTab = 'all' | 'recent' | 'filters' | 'stats';\n\nexport interface F1JournalTabsProps {\n  /** Currently active tab */\n  activeTab: JournalTab;\n  /** Tab change handler */\n  onTabChange: (tab: JournalTab) => void;\n  /** Whether tabs are disabled */\n  disabled?: boolean;\n  /** Trade counts for badges */\n  tradeCounts?: {\n    total: number;\n    recent: number;\n    filtered: number;\n  };\n  /** Whether filters are active */\n  hasActiveFilters?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst TabsContainer = styled.div`\n  display: flex;\n  gap: 0;\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0 ${({ theme }) => theme.spacing?.xl || '32px'} 0;\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  position: relative;\n`;\n\nconst Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};\n  border: none;\n  background: transparent;\n  color: ${({ $isActive, theme }) => \n    $isActive \n      ? theme.colors?.textPrimary || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  cursor: ${({ $disabled }) => $disabled ? 'not-allowed' : 'pointer'};\n  transition: all 0.2s ease;\n  font-weight: ${({ $isActive }) => $isActive ? '600' : '400'};\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  position: relative;\n  border-bottom: 2px solid transparent;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n\n  /* F1 Racing active indicator */\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    transform: scaleX(${({ $isActive }) => $isActive ? 1 : 0});\n    transition: transform 0.2s ease;\n    transform-origin: center;\n  }\n\n  /* F1 Racing hover effect */\n  &:hover:not(:disabled) {\n    color: ${({ $isActive, theme }) => \n      $isActive \n        ? theme.colors?.textPrimary || '#ffffff'\n        : theme.colors?.textPrimary || '#ffffff'};\n    transform: translateY(-1px);\n\n    &::after {\n      transform: scaleX(1);\n      background: ${({ $isActive, theme }) => \n        $isActive \n          ? theme.colors?.primary || 'var(--primary-color)'\n          : theme.colors?.textSecondary || 'var(--text-secondary)'};\n    }\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  /* Disabled styling */\n  ${({ $disabled }) =>\n    $disabled &&\n    `\n    opacity: 0.5;\n    cursor: not-allowed;\n  `}\n\n  /* Mobile responsive */\n  @media (max-width: 768px) {\n    padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  }\n`;\n\nconst TabIcon = styled.span`\n  font-size: 16px;\n  \n  @media (max-width: 768px) {\n    font-size: 14px;\n  }\n`;\n\nconst TabLabel = styled.span`\n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\nconst TabBadge = styled.span<{ $variant: 'count' | 'active' | 'new' }>`\n  background: ${({ $variant, theme }) => {\n    switch ($variant) {\n      case 'count':\n        return theme.colors?.surface || 'var(--bg-secondary)';\n      case 'active':\n        return theme.colors?.primary || 'var(--primary-color)';\n      case 'new':\n        return theme.colors?.success || 'var(--success-color)';\n      default:\n        return theme.colors?.surface || 'var(--bg-secondary)';\n    }\n  }};\n  color: ${({ $variant, theme }) => {\n    switch ($variant) {\n      case 'count':\n        return theme.colors?.textSecondary || 'var(--text-secondary)';\n      case 'active':\n        return theme.colors?.textInverse || '#ffffff';\n      case 'new':\n        return theme.colors?.textInverse || '#ffffff';\n      default:\n        return theme.colors?.textSecondary || 'var(--text-secondary)';\n    }\n  }};\n  padding: 2px 6px;\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  min-width: 20px;\n  text-align: center;\n  line-height: 1;\n  margin-left: ${({ theme }) => theme.spacing?.xs || '4px'};\n\n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\n/**\n * Tab configuration with icons and labels\n */\nconst TAB_CONFIG: Record<JournalTab, { icon: string; label: string; description: string }> = {\n  all: {\n    icon: '📋',\n    label: 'All Trades',\n    description: 'Complete trade history and journal entries',\n  },\n  recent: {\n    icon: '⚡',\n    label: 'Recent',\n    description: 'Latest trades and recent activity',\n  },\n  filters: {\n    icon: '🔍',\n    label: 'Filters',\n    description: 'Advanced filtering and search options',\n  },\n  stats: {\n    icon: '📊',\n    label: 'Statistics',\n    description: 'Performance metrics and analytics',\n  },\n};\n\n/**\n * F1JournalTabs Component\n * \n * PATTERN: F1 Tabs Pattern\n * - Racing-inspired styling with red accents\n * - Smooth hover animations and transitions\n * - Clear visual feedback for active state\n * - Accessible keyboard navigation\n * - Responsive design for mobile\n * - Trade count badges for context\n */\nexport const F1JournalTabs: React.FC<F1JournalTabsProps> = ({\n  activeTab,\n  onTabChange,\n  disabled = false,\n  tradeCounts,\n  hasActiveFilters = false,\n  className,\n}) => {\n  const handleTabClick = (tab: JournalTab) => {\n    if (!disabled) {\n      onTabChange(tab);\n    }\n  };\n  \n  const handleKeyDown = (event: React.KeyboardEvent, tab: JournalTab) => {\n    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {\n      event.preventDefault();\n      onTabChange(tab);\n    }\n  };\n\n  const getBadgeForTab = (tab: JournalTab) => {\n    if (!tradeCounts) return null;\n\n    switch (tab) {\n      case 'all':\n        return tradeCounts.total > 0 ? (\n          <TabBadge $variant=\"count\">{tradeCounts.total}</TabBadge>\n        ) : null;\n      case 'recent':\n        return tradeCounts.recent > 0 ? (\n          <TabBadge $variant=\"new\">{tradeCounts.recent}</TabBadge>\n        ) : null;\n      case 'filters':\n        return hasActiveFilters ? (\n          <TabBadge $variant=\"active\">ON</TabBadge>\n        ) : null;\n      case 'stats':\n        return tradeCounts.total > 0 ? (\n          <TabBadge $variant=\"count\">📈</TabBadge>\n        ) : null;\n      default:\n        return null;\n    }\n  };\n  \n  return (\n    <TabsContainer className={className} role=\"tablist\">\n      {(Object.keys(TAB_CONFIG) as JournalTab[]).map((tab) => {\n        const config = TAB_CONFIG[tab];\n        const isActive = activeTab === tab;\n        const badge = getBadgeForTab(tab);\n        \n        return (\n          <Tab\n            key={tab}\n            $isActive={isActive}\n            $disabled={disabled}\n            onClick={() => handleTabClick(tab)}\n            onKeyDown={(e) => handleKeyDown(e, tab)}\n            disabled={disabled}\n            role=\"tab\"\n            aria-selected={isActive}\n            aria-controls={`journal-panel-${tab}`}\n            tabIndex={disabled ? -1 : 0}\n            title={config.description}\n          >\n            <TabIcon>{config.icon}</TabIcon>\n            <TabLabel>{config.label}</TabLabel>\n            {badge}\n          </Tab>\n        );\n      })}\n    </TabsContainer>\n  );\n};\n\nexport default F1JournalTabs;\n", "/**\n * useJournalNavigation Hook\n * \n * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)\n * Hook for managing journal navigation and tab state.\n * \n * BENEFITS:\n * - Focused responsibility (navigation only)\n * - Persistent tab state with localStorage\n * - Type-safe navigation handling\n * - Reusable across journal components\n * - Better separation of concerns\n */\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { JournalTab } from './F1JournalTabs';\n\nexport interface UseJournalNavigationProps {\n  /** Default tab to show */\n  defaultTab?: JournalTab;\n  /** Storage key for persistence */\n  storageKey?: string;\n}\n\nexport interface UseJournalNavigationReturn {\n  /** Current active tab */\n  activeTab: JournalTab;\n  /** Change active tab */\n  setActiveTab: (tab: JournalTab) => void;\n  /** Navigate to next tab */\n  nextTab: () => void;\n  /** Navigate to previous tab */\n  previousTab: () => void;\n  /** Check if tab is active */\n  isTabActive: (tab: JournalTab) => boolean;\n  /** Get tab index */\n  getTabIndex: (tab: JournalTab) => number;\n  /** Get all available tabs */\n  availableTabs: JournalTab[];\n  /** Show filters (for backward compatibility) */\n  showFilters: boolean;\n  /** Set show filters */\n  setShowFilters: (show: boolean) => void;\n}\n\n/**\n * Available tabs in order\n */\nconst AVAILABLE_TABS: JournalTab[] = ['all', 'recent', 'filters', 'stats'];\n\n/**\n * Default storage key\n */\nconst DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:journal:active-tab';\n\n/**\n * Load tab from localStorage\n */\nconst loadTabFromStorage = (storageKey: string, defaultTab: JournalTab): JournalTab => {\n  try {\n    const stored = localStorage.getItem(storageKey);\n    if (stored && AVAILABLE_TABS.includes(stored as JournalTab)) {\n      return stored as JournalTab;\n    }\n  } catch (error) {\n    console.warn('Failed to load journal tab from localStorage:', error);\n  }\n  return defaultTab;\n};\n\n/**\n * Save tab to localStorage\n */\nconst saveTabToStorage = (storageKey: string, tab: JournalTab): void => {\n  try {\n    localStorage.setItem(storageKey, tab);\n  } catch (error) {\n    console.warn('Failed to save journal tab to localStorage:', error);\n  }\n};\n\n/**\n * useJournalNavigation Hook\n * \n * Manages tab navigation state with persistence and keyboard navigation.\n */\nexport const useJournalNavigation = ({\n  defaultTab = 'all',\n  storageKey = DEFAULT_STORAGE_KEY,\n}: UseJournalNavigationProps = {}): UseJournalNavigationReturn => {\n  \n  // Initialize active tab from storage or default\n  const [activeTab, setActiveTabState] = useState<JournalTab>(() =>\n    loadTabFromStorage(storageKey, defaultTab)\n  );\n\n  // Backward compatibility: show filters state\n  const [showFilters, setShowFilters] = useState<boolean>(false);\n  \n  /**\n   * Set active tab with persistence\n   */\n  const setActiveTab = useCallback((tab: JournalTab) => {\n    if (AVAILABLE_TABS.includes(tab)) {\n      setActiveTabState(tab);\n      saveTabToStorage(storageKey, tab);\n      \n      // Auto-manage filters visibility based on tab\n      if (tab === 'filters') {\n        setShowFilters(true);\n      }\n    }\n  }, [storageKey]);\n  \n  /**\n   * Navigate to next tab\n   */\n  const nextTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;\n    setActiveTab(AVAILABLE_TABS[nextIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Navigate to previous tab\n   */\n  const previousTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;\n    setActiveTab(AVAILABLE_TABS[previousIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Check if tab is active\n   */\n  const isTabActive = useCallback((tab: JournalTab): boolean => {\n    return activeTab === tab;\n  }, [activeTab]);\n  \n  /**\n   * Get tab index\n   */\n  const getTabIndex = useCallback((tab: JournalTab): number => {\n    return AVAILABLE_TABS.indexOf(tab);\n  }, []);\n  \n  // Handle keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Only handle if no input is focused\n      if (document.activeElement?.tagName === 'INPUT' || \n          document.activeElement?.tagName === 'TEXTAREA' ||\n          document.activeElement?.tagName === 'SELECT') {\n        return;\n      }\n      \n      // Handle Ctrl/Cmd + Arrow keys for tab navigation\n      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault();\n            previousTab();\n            break;\n          case 'ArrowRight':\n            event.preventDefault();\n            nextTab();\n            break;\n        }\n      }\n      \n      // Handle number keys for direct tab navigation\n      if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {\n        const tabIndex = parseInt(event.key) - 1;\n        if (tabIndex < AVAILABLE_TABS.length) {\n          event.preventDefault();\n          setActiveTab(AVAILABLE_TABS[tabIndex]);\n        }\n      }\n      \n      // Handle Alt + Tab keys for journal-specific navigation\n      if (event.altKey && !event.ctrlKey && !event.metaKey) {\n        switch (event.key.toLowerCase()) {\n          case 'a':\n            event.preventDefault();\n            setActiveTab('all');\n            break;\n          case 'r':\n            event.preventDefault();\n            setActiveTab('recent');\n            break;\n          case 'f':\n            event.preventDefault();\n            setActiveTab('filters');\n            break;\n          case 's':\n            event.preventDefault();\n            setActiveTab('stats');\n            break;\n        }\n      }\n\n      // Handle 'f' key to toggle filters (common UX pattern)\n      if (event.key.toLowerCase() === 'f' && !event.ctrlKey && !event.metaKey && !event.altKey) {\n        // Only if not in an input field\n        if (document.activeElement?.tagName !== 'INPUT' && \n            document.activeElement?.tagName !== 'TEXTAREA') {\n          event.preventDefault();\n          if (activeTab === 'filters') {\n            setShowFilters(!showFilters);\n          } else {\n            setActiveTab('filters');\n          }\n        }\n      }\n    };\n    \n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [nextTab, previousTab, setActiveTab, activeTab, showFilters]);\n  \n  return {\n    activeTab,\n    setActiveTab,\n    nextTab,\n    previousTab,\n    isTabActive,\n    getTabIndex,\n    availableTabs: AVAILABLE_TABS,\n    showFilters,\n    setShowFilters,\n  };\n};\n\nexport default useJournalNavigation;\n", "/**\n * Journal Tab Configuration\n *\n * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)\n * Centralized configuration for journal tabs and their content.\n *\n * BENEFITS:\n * - Single source of truth for tab definitions\n * - Easy to maintain and extend\n * - Type-safe tab configurations\n * - Reusable across different journal views\n * - Clear content mapping\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { JournalTab } from './F1JournalTabs';\nimport { TradeJournalContent } from './trade-journal';\nimport TradeList from './TradeList';\nimport { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nexport interface JournalTabConfig {\n  id: JournalTab;\n  title: string;\n  description: string;\n  icon: string;\n  component: React.ComponentType<any>;\n  showInMobile: boolean;\n  requiresData: boolean;\n}\n\nexport interface JournalTabContentProps {\n  /** Current active tab */\n  activeTab: JournalTab;\n  /** Journal data */\n  data: {\n    trades: CompleteTradeData[];\n    filteredTrades: CompleteTradeData[];\n    recentTrades: CompleteTradeData[];\n    filters: any;\n    uniqueSetups: string[];\n    uniqueModelTypes: string[];\n    uniquePrimarySetupTypes: string[];\n    uniqueSecondarySetupTypes: string[];\n    uniqueLiquidityTypes: string[];\n    uniqueDOLTypes: string[];\n  };\n  /** Loading state */\n  isLoading: boolean;\n  /** Error state */\n  error: string | null;\n  /** Show filters state */\n  showFilters: boolean;\n  /** Action handlers */\n  handlers: {\n    handleFilterChange: (filters: any) => void;\n    resetFilters: () => void;\n    refreshTrades: () => void;\n  };\n}\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 300px;\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst EmptyMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst StatsContainer = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst StatCard = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};\n`;\n\nconst StatLabel = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\n/**\n * All Trades Tab Content\n */\nconst AllTradesTabContent: React.FC<JournalTabContentProps> = ({\n  data,\n  isLoading,\n  error,\n  showFilters,\n  handlers,\n}) => {\n  if (error) {\n    return (\n      <EmptyState>\n        <EmptyIcon>⚠️</EmptyIcon>\n        <EmptyTitle>Error Loading Trades</EmptyTitle>\n        <EmptyMessage>{error}</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  if (!isLoading && data.trades.length === 0) {\n    return (\n      <EmptyState>\n        <EmptyIcon>📋</EmptyIcon>\n        <EmptyTitle>No Trades Found</EmptyTitle>\n        <EmptyMessage>Start building your trading journal by adding your first trade.</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  return (\n    <TradeJournalContent\n      error={error}\n      showFilters={showFilters}\n      filteredTrades={data.filteredTrades as any}\n      isLoading={isLoading}\n      filters={data.filters}\n      handleFilterChange={handlers.handleFilterChange}\n      resetFilters={handlers.resetFilters}\n      uniqueSetups={data.uniqueSetups}\n      uniqueModelTypes={data.uniqueModelTypes}\n      uniquePrimarySetupTypes={data.uniquePrimarySetupTypes}\n      uniqueSecondarySetupTypes={data.uniqueSecondarySetupTypes}\n      uniqueLiquidityTypes={data.uniqueLiquidityTypes}\n      uniqueDOLTypes={data.uniqueDOLTypes}\n    />\n  );\n};\n\n/**\n * Recent Trades Tab Content\n */\nconst RecentTradesTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading }) => {\n  if (!isLoading && data.recentTrades.length === 0) {\n    return (\n      <EmptyState>\n        <EmptyIcon>⚡</EmptyIcon>\n        <EmptyTitle>No Recent Trades</EmptyTitle>\n        <EmptyMessage>Recent trades from the last 7 days will appear here.</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  return <TradeList trades={data.recentTrades as any} isLoading={isLoading} />;\n};\n\n/**\n * Filters Tab Content\n */\nconst FiltersTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading, handlers }) => {\n  return (\n    <TradeJournalContent\n      error={null}\n      showFilters={true}\n      filteredTrades={data.filteredTrades as any}\n      isLoading={isLoading}\n      filters={data.filters}\n      handleFilterChange={handlers.handleFilterChange}\n      resetFilters={handlers.resetFilters}\n      uniqueSetups={data.uniqueSetups}\n      uniqueModelTypes={data.uniqueModelTypes}\n      uniquePrimarySetupTypes={data.uniquePrimarySetupTypes}\n      uniqueSecondarySetupTypes={data.uniqueSecondarySetupTypes}\n      uniqueLiquidityTypes={data.uniqueLiquidityTypes}\n      uniqueDOLTypes={data.uniqueDOLTypes}\n    />\n  );\n};\n\n/**\n * Statistics Tab Content\n */\nconst StatsTabContent: React.FC<JournalTabContentProps> = ({ data, isLoading }) => {\n  if (!isLoading && data.trades.length === 0) {\n    return (\n      <EmptyState>\n        <EmptyIcon>📊</EmptyIcon>\n        <EmptyTitle>No Statistics Available</EmptyTitle>\n        <EmptyMessage>\n          Trade statistics will be calculated once you have recorded trades.\n        </EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  // Calculate basic statistics using correct field names (matching main dashboard)\n  const totalTrades = data.trades.length;\n  const winningTrades = data.trades.filter(t => t.trade.win_loss === 'Win').length;\n  const winRate = totalTrades > 0 ? ((winningTrades / totalTrades) * 100).toFixed(1) : '0';\n  const totalPnL = data.trades.reduce((sum, t) => sum + (t.trade.achieved_pl || 0), 0);\n\n  return (\n    <div>\n      <StatsContainer>\n        <StatCard>\n          <StatValue>{totalTrades}</StatValue>\n          <StatLabel>Total Trades</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue>{winRate}%</StatValue>\n          <StatLabel>Win Rate</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue>${totalPnL.toFixed(2)}</StatValue>\n          <StatLabel>Total P&L</StatLabel>\n        </StatCard>\n        <StatCard>\n          <StatValue>{data.uniqueSetups.length}</StatValue>\n          <StatLabel>Unique Setups</StatLabel>\n        </StatCard>\n      </StatsContainer>\n\n      <TradeList trades={data.trades as any} isLoading={isLoading} />\n    </div>\n  );\n};\n\n/**\n * Tab configuration with components and metadata\n */\nexport const JOURNAL_TAB_CONFIG: Record<JournalTab, JournalTabConfig> = {\n  all: {\n    id: 'all',\n    title: 'All Trades',\n    description: 'Complete trade history and journal entries',\n    icon: '📋',\n    component: AllTradesTabContent,\n    showInMobile: true,\n    requiresData: false,\n  },\n  recent: {\n    id: 'recent',\n    title: 'Recent Trades',\n    description: 'Latest trades and recent activity',\n    icon: '⚡',\n    component: RecentTradesTabContent,\n    showInMobile: true,\n    requiresData: false,\n  },\n  filters: {\n    id: 'filters',\n    title: 'Advanced Filters',\n    description: 'Advanced filtering and search options',\n    icon: '🔍',\n    component: FiltersTabContent,\n    showInMobile: false,\n    requiresData: false,\n  },\n  stats: {\n    id: 'stats',\n    title: 'Statistics',\n    description: 'Performance metrics and analytics',\n    icon: '📊',\n    component: StatsTabContent,\n    showInMobile: true,\n    requiresData: true,\n  },\n};\n\n/**\n * Get tab configuration by ID\n */\nexport const getTabConfig = (tabId: JournalTab): JournalTabConfig => {\n  return JOURNAL_TAB_CONFIG[tabId];\n};\n\n/**\n * Get all tab configurations\n */\nexport const getAllTabConfigs = (): JournalTabConfig[] => {\n  return Object.values(JOURNAL_TAB_CONFIG);\n};\n\n/**\n * Get mobile-friendly tabs\n */\nexport const getMobileTabConfigs = (): JournalTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.showInMobile);\n};\n\n/**\n * Get tabs that require data\n */\nexport const getDataRequiredTabConfigs = (): JournalTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.requiresData);\n};\n\n/**\n * Tab Content Renderer Component\n */\nexport const JournalTabContentRenderer: React.FC<JournalTabContentProps> = props => {\n  const { activeTab } = props;\n  const config = getTabConfig(activeTab);\n\n  if (!config) {\n    return (\n      <EmptyState>\n        <EmptyIcon>❌</EmptyIcon>\n        <EmptyTitle>Unknown Tab</EmptyTitle>\n        <EmptyMessage>Tab \"{activeTab}\" not found.</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  const TabComponent = config.component;\n\n  return (\n    <div\n      id={`journal-panel-${activeTab}`}\n      role='tabpanel'\n      aria-labelledby={`journal-tab-${activeTab}`}\n    >\n      <TabComponent {...props} />\n    </div>\n  );\n};\n\nexport default JournalTabContentRenderer;\n", "/**\n * F1JournalContainer Component\n *\n * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)\n * Main orchestrator for trade journal with F1 container pattern.\n *\n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and loading states\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense, useMemo } from 'react';\nimport styled from 'styled-components';\nimport { useTradeJournal } from '../hooks/useTradeJournal';\nimport { useTradeFilters } from '../hooks/useTradeFilters';\nimport { F1JournalHeader } from './F1JournalHeader';\nimport { F1JournalTabs } from './F1JournalTabs';\nimport { useJournalNavigation } from './useJournalNavigation';\nimport { JournalTabContentRenderer } from './journalTabConfig';\n\nexport interface F1JournalContainerProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'all' | 'recent' | 'filters' | 'stats';\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  min-height: 100vh;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  max-width: 1400px;\n  margin: 0 auto;\n`;\n\nconst ContentArea = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  flex: 1;\n`;\n\nconst TabContentContainer = styled.div`\n  animation: fadeIn 0.3s ease-in-out;\n\n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(10px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n`;\n\nconst LoadingIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 0.7;\n    }\n    50% {\n      opacity: 0.3;\n    }\n  }\n`;\n\nconst LoadingText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;\n  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n`;\n\nconst ErrorTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst ErrorMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst RetryButton = styled.button`\n  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n    ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  color: white;\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n  }\n`;\n\n/**\n * LoadingFallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <LoadingState>\n    <LoadingIcon>📋</LoadingIcon>\n    <LoadingText>Loading Trade Journal...</LoadingText>\n  </LoadingState>\n);\n\n/**\n * ErrorFallback Component\n */\nconst ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (\n  <ErrorState>\n    <ErrorIcon>⚠️</ErrorIcon>\n    <ErrorTitle>Journal Error</ErrorTitle>\n    <ErrorMessage>{error}</ErrorMessage>\n    <RetryButton onClick={onRetry}>Try Again</RetryButton>\n  </ErrorState>\n);\n\n/**\n * JournalContent Component\n */\nconst JournalContent: React.FC<F1JournalContainerProps> = ({ initialTab }) => {\n  const { trades, isLoading, error, refreshTrades } = useTradeJournal();\n\n  const {\n    filters,\n    handleFilterChange,\n    resetFilters,\n    filteredTrades,\n    uniqueSetups,\n    uniqueModelTypes,\n    uniquePrimarySetupTypes,\n    uniqueSecondarySetupTypes,\n    uniqueLiquidityTypes,\n    uniqueDOLTypes,\n  } = useTradeFilters(trades as any); // Type assertion for interface compatibility\n\n  const { activeTab, setActiveTab, showFilters } = useJournalNavigation({\n    defaultTab: initialTab || 'all',\n  });\n\n  // Calculate recent trades (last 7 days)\n  const recentTrades = useMemo(() => {\n    const sevenDaysAgo = new Date();\n    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n    return trades.filter(trade => {\n      const tradeDate = new Date(trade.date);\n      return tradeDate >= sevenDaysAgo;\n    });\n  }, [trades]);\n\n  // Calculate trade counts for badges\n  const tradeCounts = useMemo(\n    () => ({\n      total: trades.length,\n      recent: recentTrades.length,\n      filtered: filteredTrades.length,\n    }),\n    [trades.length, recentTrades.length, filteredTrades.length]\n  );\n\n  // Check if filters are active\n  const hasActiveFilters = useMemo(() => {\n    return Object.values(filters).some(\n      value => value !== '' && value !== null && value !== undefined\n    );\n  }, [filters]);\n\n  // Prepare data and handlers for tab content\n  const tabContentProps = {\n    activeTab,\n    data: {\n      trades: trades as any, // Type assertion for interface compatibility\n      filteredTrades,\n      recentTrades: recentTrades as any, // Type assertion for interface compatibility\n      filters,\n      uniqueSetups,\n      uniqueModelTypes,\n      uniquePrimarySetupTypes,\n      uniqueSecondarySetupTypes,\n      uniqueLiquidityTypes,\n      uniqueDOLTypes,\n    },\n    isLoading,\n    error,\n    showFilters,\n    handlers: {\n      handleFilterChange,\n      resetFilters,\n      refreshTrades: () => refreshTrades && refreshTrades(),\n    },\n  };\n\n  // Export handler\n  const handleExport = () => {\n    // TODO: Implement export functionality\n    console.log('Export trades:', filteredTrades);\n  };\n\n  if (error) {\n    return <ErrorFallback error={error} onRetry={() => refreshTrades && refreshTrades()} />;\n  }\n\n  return (\n    <Container>\n      {/* F1 Racing Header */}\n      <F1JournalHeader\n        isLoading={isLoading}\n        tradeCount={trades.length}\n        filteredCount={filteredTrades.length}\n        hasActiveFilters={hasActiveFilters}\n        onRefresh={refreshTrades}\n        onExport={handleExport}\n      />\n\n      {/* F1 Racing Tabs */}\n      <F1JournalTabs\n        activeTab={activeTab}\n        onTabChange={setActiveTab}\n        disabled={isLoading}\n        tradeCounts={tradeCounts}\n        hasActiveFilters={hasActiveFilters}\n      />\n\n      {/* Tab Content */}\n      <ContentArea>\n        <TabContentContainer>\n          <Suspense fallback={<LoadingFallback />}>\n            <JournalTabContentRenderer {...tabContentProps} />\n          </Suspense>\n        </TabContentContainer>\n      </ContentArea>\n    </Container>\n  );\n};\n\n/**\n * F1JournalContainer Component\n *\n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const F1JournalContainer: React.FC<F1JournalContainerProps> = props => {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <JournalContent {...props} />\n    </Suspense>\n  );\n};\n\nexport default F1JournalContainer;\n", "/**\n * Trade Journal Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 72 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 90% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { F1JournalContainer } from './components/F1JournalContainer';\n\nexport interface TradeJournalProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'all' | 'recent' | 'filters' | 'stats';\n}\n\n/**\n * Trade Journal Component\n *\n * Simple wrapper that renders the container.\n * Follows the proven architecture pattern.\n */\nconst TradeJournal: React.FC<TradeJournalProps> = ({ className, initialTab }) => {\n  return <F1JournalContainer className={className} initialTab={initialTab} />;\n};\n\nexport default TradeJournal;\n"], "names": ["useTradeJournal", "state", "setState", "useState", "trades", "isLoading", "error", "useEffect", "prev", "sortedTrades", "tradeStorageService", "getAllTrades", "sort", "a", "b", "Date", "trade", "date", "getTime", "refreshTrades", "useTradeFilters", "filters", "setFilters", "symbol", "direction", "setup", "modelType", "result", "dateFrom", "dateTo", "primarySetupType", "secondarySetupType", "liquidityTaken", "patternQualityMin", "patternQualityMax", "dolType", "dolEffectivenessMin", "dolEffectivenessMax", "handleFilterChange", "e", "name", "value", "target", "resetFilters", "filteredTrades", "useMemo", "filter", "tradeData", "analysis", "market", "toLowerCase", "includes", "primary_setup", "model_type", "isWin", "achieved_pl", "tradeDate", "fromDate", "toDate", "setHours", "secondary_setup", "liquidity_taken", "pattern_quality_rating", "minQuality", "parseInt", "isNaN", "maxQuality", "dol_target_type", "uniqueSetups", "setups", "map", "Array", "from", "Set", "uniqueModelTypes", "modelTypes", "uniquePrimarySetupTypes", "setupTypes", "setupType", "uniqueSecondarySetupTypes", "uniqueLiquidityTypes", "liquidityTypes", "liquidityType", "uniqueDOLTypes", "dolTypes", "useTradeList", "expandable", "expandedRows", "setExpandedRows", "toggleRowExpansion", "tradeId", "isRowExpanded", "dateA", "TradeHeader", "div", "withConfig", "displayName", "componentId", "theme", "colors", "textSecondary", "spacing", "sm", "md", "TradeDetail", "xs", "TradeListHeader", "visibleColumns", "jsx", "column", "label", "id", "TradeItem", "cardBackground", "borderRadius", "transitions", "fast", "expanded", "undefined", "chartGrid", "TradeListRow", "React", "accessor", "ExpandedContent", "background", "ExpandedSection", "SectionTitle", "h3", "fontSizes", "textPrimary", "DetailGrid", "DetailItem", "DetailLabel", "span", "DetailValue", "ActionButtons", "ActionButton", "styled", "Link", "primary", "primaryDark", "TradeListExpandedRow", "jsxs", "entry_price", "toFixed", "exit_price", "no_of_contracts", "color", "r_multiple", "session", "execution_quality", "lessons_learned", "emotional_state", "market_conditions", "notes", "EmptyContainer", "xl", "EmptyTitle", "lg", "EmptyDescription", "p", "AddTradeButton", "TradeListEmpty", "filtered", "shimmer", "keyframes", "LoadingContainer", "LoadingRow", "TradeListLoading", "rowCount", "length", "_", "index", "TradeListContainer", "profit", "loss", "success", "danger", "Badge", "xxs", "type", "successLight", "dangerLight", "TradeList", "onEditTrade", "navigate", "useNavigate", "handleEditTrade", "columns", "setupComponents", "setupDisplay", "SetupTransformer", "getShortDisplayString", "constant", "entry", "padding", "fontSize", "border", "cursor", "gridTemplateColumns", "Upload", "FileText", "CheckCircle", "XCircle", "AlertCircle", "Download", "CSVImportTool", "onImportComplete", "file", "setFile", "csvData", "setCsvData", "mappedData", "setMappedData", "importStatus", "setImportStatus", "stats", "setStats", "COLUMN_MAPPINGS", "risk_points", "win_loss", "entry_time", "exit_time", "rd_type", "draw_on_liquidity", "fvg_date", "entry_version", "additional_fvgs", "dol", "tradingview_link", "beyond_target", "clustering", "path_quality", "idr_context", "sequential_fvg_rd", "dol_notes", "VALID_TRADING_MODELS", "VALID_SESSIONS", "VALID_MARKETS", "handleFileUpload", "useCallback", "event", "uploadedFile", "files", "parseCSV", "parseCSVLine", "line", "current", "inQuotes", "i", "char", "push", "trim", "lines", "text", "split", "console", "log", "headerLine", "headers", "h", "replace", "rows", "slice", "values", "row", "for<PERSON>ach", "header", "mapColumns", "mappingResults", "entries", "db<PERSON><PERSON>", "possibleHeaders", "<PERSON><PERSON><PERSON><PERSON>", "find", "some", "ph", "mapped", "rowIndex", "tradeRecord", "model", "validModel", "vm", "lower", "vs", "numericValue", "parseFloat", "rating", "Math", "max", "min", "contracts", "dateParts", "month", "day", "year", "padStart", "toISOString", "Boolean", "validTrades", "t", "unmappedModels", "missingPrices", "winningTrades", "losingTrades", "totalRows", "skipped", "winRate", "handleImport", "importPromises", "cleanedTradeRecord", "created_at", "updated_at", "setupData", "fvgDetails", "analysisData", "toLocaleDateString", "completeTradeData", "fvg_details", "saveTradeWithDetails", "Promise", "all", "setTimeout", "downloadCleanedData", "csv", "Object", "keys", "join", "v", "blob", "Blob", "url", "URL", "createObjectURL", "document", "createElement", "href", "download", "click", "key", "field", "FilterGroup", "Filter<PERSON>abel", "FilterInput", "input", "$hasValue", "FilterSelect", "select", "FilterOption", "option", "surface", "F1FilterField", "onChange", "options", "placeholder", "disabled", "className", "fieldId", "hasValue", "handleChange", "newValue", "renderControl", "useFilterState", "initialFilters", "onFiltersChange", "onReset", "updateFilter", "newFilters", "updateFilters", "updatedFilters", "fromEntries", "hasActiveFilters", "activeFilterCount", "getFilterValue", "hasFilterValue", "generateRatingOptions", "toString", "FILTER_FIELD_CONFIG", "group", "order", "getFilterFieldsByGroup", "getFilterGroups", "FILTER_GROUP_LABELS", "basic", "dates", "trading", "FILTER_GROUP_DESCRIPTIONS", "getFieldConfigWithOptions", "uniqueData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FilterTitle", "FilterBadge", "$count", "textInverse", "full", "GroupHeader", "GroupTitle", "h4", "GroupDescription", "FieldGrid", "ActionBar", "button", "$variant", "F1FilterPanel", "fieldConfigs", "filterGroups", "groupName", "configuredFields", "config", "TradeJournalFilters", "handleFiltersChange", "syntheticEvent", "String", "ContentSection", "shadows", "h2", "ErrorMessage", "TradeJournalContent", "showFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "F1Title", "h1", "JournalIndicator", "$hasData", "SubHeader", "TitleSection", "SubTitle", "xxl", "StatsContainer", "StatBadge", "ActionsContainer", "AddTradeLink", "successDark", "ModalOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ModalTitle", "CloseButton", "F1JournalHeader", "isRefreshing", "tradeCount", "filteredCount", "onRefresh", "onExport", "onImport", "title", "showImportModal", "setShowImportModal", "hasData", "showFilteredCount", "handleImportClick", "handleImportComplete", "toLocaleString", "stopPropagation", "LegacyDataImport", "TabsContainer", "Tab", "$isActive", "$disabled", "TabIcon", "TabLabel", "TabBadge", "TAB_CONFIG", "icon", "description", "recent", "F1JournalTabs", "activeTab", "onTabChange", "tradeCounts", "handleTabClick", "tab", "handleKeyDown", "preventDefault", "getBadgeForTab", "total", "isActive", "badge", "AVAILABLE_TABS", "DEFAULT_STORAGE_KEY", "loadTabFromStorage", "storageKey", "defaultTab", "stored", "localStorage", "getItem", "warn", "saveTabToStorage", "setItem", "useJournalNavigation", "setActiveTabState", "setShowFilters", "setActiveTab", "nextTab", "nextIndex", "indexOf", "previousTab", "currentIndex", "previousIndex", "isTabActive", "getTabIndex", "activeElement", "tagName", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "tabIndex", "altKey", "addEventListener", "window", "removeEventListener", "availableTabs", "EmptyState", "EmptyIcon", "EmptyMessage", "StatCard", "StatValue", "StatLabel", "AllTradesTabContent", "data", "handlers", "RecentTradesTabContent", "recentTrades", "Filters<PERSON>ab<PERSON><PERSON>nt", "StatsTabContent", "totalTrades", "totalPnL", "reduce", "sum", "JOURNAL_TAB_CONFIG", "component", "showInMobile", "requiresData", "getTabConfig", "tabId", "JournalTabContent<PERSON><PERSON><PERSON>", "props", "TabComponent", "Container", "ContentArea", "TabContentContainer", "LoadingState", "LoadingIcon", "LoadingText", "ErrorState", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RetryButton", "LoadingFallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRetry", "JournalContent", "initialTab", "sevenDaysAgo", "setDate", "getDate", "tabContentProps", "handleExport", "Suspense", "F1JournalContainer", "TradeJournal"], "mappings": "wWAeO,MAAMA,GAAkBA,IAAyB,CACtD,KAAM,CAACC,EAAOC,CAAQ,EAAIC,WAA4B,CACpDC,OAAQ,CAAE,EACVC,UAAW,GACXC,MAAO,IAAA,CACR,EAEDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CAEFL,EAAkBM,IAAA,CAChB,GAAGA,EACHH,UAAW,GACXC,MAAO,IACP,EAAA,EAMIG,MAAAA,EAAe,CAAC,GAHP,MAAMC,GAAoBC,cAGV,EAAEC,KAC/B,CAACC,EAAGC,IAAM,IAAIC,KAAKD,EAAEE,MAAMC,IAAI,EAAEC,QAAQ,EAAI,IAAIH,KAAKF,EAAEG,MAAMC,IAAI,EAAEC,QAAAA,CACtE,EAEShB,EAAA,CACPE,OAAQK,EACRJ,UAAW,GACXC,MAAO,IAAA,CACR,QACMA,GACCA,QAAAA,MAAM,yBAA0BA,CAAK,EAC7CJ,EAAkBM,IAAA,CAChB,GAAGA,EACHH,UAAW,GACXC,MAAO,0CACP,EAAA,CACJ,CAAA,IAIJ,EAAG,CAAE,CAAA,EAiCE,CACL,GAAGL,EACHkB,cAhCoB,SAAY,CAC5B,GAAA,CACFjB,EAAkBM,IAAA,CAChB,GAAGA,EACHH,UAAW,GACXC,MAAO,IACP,EAAA,EAKIG,MAAAA,EAAe,CAAC,GAHP,MAAMC,GAAoBC,cAGV,EAAEC,KAC/B,CAACC,EAAGC,IAAM,IAAIC,KAAKD,EAAEE,MAAMC,IAAI,EAAEC,QAAQ,EAAI,IAAIH,KAAKF,EAAEG,MAAMC,IAAI,EAAEC,QAAAA,CACtE,EAEShB,EAAA,CACPE,OAAQK,EACRJ,UAAW,GACXC,MAAO,IAAA,CACR,QACMA,GACCA,QAAAA,MAAM,2BAA4BA,CAAK,EAC/CJ,EAAkBM,IAAA,CAChB,GAAGA,EACHH,UAAW,GACXC,MAAO,6CACP,EAAA,CACJ,CAAA,CAKAa,CAEJ,EC/EO,SAASC,GAAgBhB,EAA6B,CAE3D,KAAM,CAACiB,EAASC,CAAU,EAAInB,WAAsB,CAClDoB,OAAQ,GACRC,UAAW,GACXC,MAAO,GACPC,UAAW,GACXC,OAAQ,GACRC,SAAU,GACVC,OAAQ,GACRC,iBAAkB,GAClBC,mBAAoB,GACpBC,eAAgB,GAChBC,kBAAmB,GACnBC,kBAAmB,GACnBC,QAAS,GACTC,oBAAqB,GACrBC,oBAAqB,EAAA,CACtB,EAGKC,EAAsBC,GAA+D,CACnF,KAAA,CAAEC,KAAAA,EAAMC,MAAAA,CAAAA,EAAUF,EAAEG,OAC1BpB,EAAsBd,IAAA,CACpB,GAAGA,EACH,CAACgC,CAAI,EAAGC,CACR,EAAA,CAAA,EAIEE,EAAeA,IAAM,CACdrB,EAAA,CACTC,OAAQ,GACRC,UAAW,GACXC,MAAO,GACPC,UAAW,GACXC,OAAQ,GACRC,SAAU,GACVC,OAAQ,GACRC,iBAAkB,GAClBC,mBAAoB,GACpBC,eAAgB,GAChBC,kBAAmB,GACnBC,kBAAmB,GACnBC,QAAS,GACTC,oBAAqB,GACrBC,oBAAqB,EAAA,CACtB,CAAA,EAIGO,EAAiBC,EAAAA,QAAQ,IACxBzC,EAEEA,EAAO0C,OAAsBC,GAAA,CAC5B,KAAA,CAAE/B,MAAAA,EAAOS,MAAAA,EAAOuB,SAAAA,CAAaD,EAAAA,EAsBnC,GAlBE1B,EAAQE,QACRP,EAAMiC,QACN,CAACjC,EAAMiC,OAAOC,YAAY,EAAEC,SAAS9B,EAAQE,OAAO2B,YAAa,CAAA,GAM/D7B,EAAQG,WAAaR,EAAMQ,YAAcH,EAAQG,WAKjDH,EAAQI,QAASA,GAAAA,YAAAA,EAAO2B,iBAAkB/B,EAAQI,OAKlDJ,EAAQK,WAAaV,EAAMqC,aAAehC,EAAQK,UAC7C,MAAA,GAIT,GAAIL,EAAQM,OAAQ,CACZ2B,MAAAA,GAAStC,EAAMuC,aAAe,GAAK,EACpClC,GAAAA,EAAQM,SAAW,OAAS,CAAC2B,GAAWjC,EAAQM,SAAW,QAAU2B,EACjE,MAAA,GAKX,GAAIjC,EAAQO,SAAU,CACpB,MAAM4B,EAAY,IAAIzC,KAAKC,EAAMC,IAAI,EAC/BwC,EAAW,IAAI1C,KAAKM,EAAQO,QAAQ,EAC1C,GAAI4B,EAAYC,EACP,MAAA,GAIX,GAAIpC,EAAQQ,OAAQ,CAClB,MAAM2B,EAAY,IAAIzC,KAAKC,EAAMC,IAAI,EAC/ByC,EAAS,IAAI3C,KAAKM,EAAQQ,MAAM,EAGtC,GADA6B,EAAOC,SAAS,GAAI,GAAI,GAAI,GAAG,EAC3BH,EAAYE,EACP,MAAA,GAeX,GAVIrC,EAAQS,mBAAoBL,GAAAA,YAAAA,EAAO2B,iBAAkB/B,EAAQS,kBAK7DT,EAAQU,qBAAsBN,GAAAA,YAAAA,EAAOmC,mBAAoBvC,EAAQU,oBAKjEV,EAAQW,iBAAkBP,GAAAA,YAAAA,EAAOoC,mBAAoBxC,EAAQW,eACxD,MAAA,GAILX,GAAAA,EAAQY,mBAAqBjB,EAAM8C,uBAAwB,CACvDC,MAAAA,EAAaC,SAAS3C,EAAQY,iBAAiB,EACrD,GAAI,CAACgC,MAAMF,CAAU,GAAK/C,EAAM8C,uBAAyBC,EAChD,MAAA,GAKP1C,GAAAA,EAAQa,mBAAqBlB,EAAM8C,uBAAwB,CACvDI,MAAAA,EAAaF,SAAS3C,EAAQa,iBAAiB,EACrD,GAAI,CAAC+B,MAAMC,CAAU,GAAKlD,EAAM8C,uBAAyBI,EAChD,MAAA,GAKX,OAAI7C,EAAQc,UAAWa,GAAAA,YAAAA,EAAUmB,mBAAoB9C,EAAQc,QACpD,IAILd,EAAQe,oBAKRf,EAAQgB,oBAIL,GAAA,CACR,EAvGmB,GAwGnB,CAACjC,EAAQiB,CAAO,CAAC,EAGd+C,EAAevB,EAAAA,QAAQ,IAAM,CACjC,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAMiE,EAASjE,EACZkE,IAAKvB,GAAcA,OAAAA,OAAAA,EAAAA,EAAUtB,QAAVsB,YAAAA,EAAiBK,cAAa,EACjDN,OAAQrB,GAA2B,CAAC,CAACA,CAAK,EAC7C,OAAO8C,MAAMC,KAAK,IAAIC,IAAIJ,CAAM,CAAC,CAAA,EAChC,CAACjE,CAAM,CAAC,EAELsE,EAAmB7B,EAAAA,QAAQ,IAAM,CACrC,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAMuE,EAAavE,EAChBkE,IAAKvB,GAAcA,EAAU/B,MAAMqC,UAAU,EAC7CP,OAAQpB,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAO6C,MAAMC,KAAK,IAAIC,IAAIE,CAAU,CAAC,CAAA,EACpC,CAACvE,CAAM,CAAC,EAELwE,EAA0B/B,EAAAA,QAAQ,IAAM,CAC5C,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAMyE,EAAazE,EAChBkE,IAAKvB,GAAcA,OAAAA,OAAAA,EAAAA,EAAUtB,QAAVsB,YAAAA,EAAiBK,cAAa,EACjDN,OAAQgC,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAOP,MAAMC,KAAK,IAAIC,IAAII,CAAU,CAAC,CAAA,EACpC,CAACzE,CAAM,CAAC,EAEL2E,EAA4BlC,EAAAA,QAAQ,IAAM,CAC9C,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAMyE,EAAazE,EAChBkE,IAAKvB,GAAcA,OAAAA,OAAAA,EAAAA,EAAUtB,QAAVsB,YAAAA,EAAiBa,gBAAe,EACnDd,OAAQgC,GAAmC,CAAC,CAACA,CAAS,EACzD,OAAOP,MAAMC,KAAK,IAAIC,IAAII,CAAU,CAAC,CAAA,EACpC,CAACzE,CAAM,CAAC,EAEL4E,EAAuBnC,EAAAA,QAAQ,IAAM,CACzC,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAM6E,EAAiB7E,EACpBkE,IAAKvB,GAAcA,OAAAA,OAAAA,EAAAA,EAAUtB,QAAVsB,YAAAA,EAAiBc,gBAAe,EACnDf,OAAQoC,GAA2C,CAAC,CAACA,CAAa,EACrE,OAAOX,MAAMC,KAAK,IAAIC,IAAIQ,CAAc,CAAC,CAAA,EACxC,CAAC7E,CAAM,CAAC,EAEL+E,EAAiBtC,EAAAA,QAAQ,IAAM,CACnC,GAAI,CAACzC,EAAQ,MAAO,GACpB,MAAMgF,EAAWhF,EACd0C,UAAsBC,EAAUC,QAAQ,EACxCsB,IAAKvB,GAAAA,OAAcA,OAAAA,EAAAA,EAAUC,WAAVD,YAAAA,EAAoBoB,gBAAe,EACtDrB,OAAQX,GAA+B,CAAC,CAACA,CAAO,EACnD,OAAOoC,MAAMC,KAAK,IAAIC,IAAIW,CAAQ,CAAC,CAAA,EAClC,CAAChF,CAAM,CAAC,EAEJ,MAAA,CACLiB,QAAAA,EACAC,WAAAA,EACAgB,mBAAAA,EACAK,aAAAA,EACAC,eAAAA,EACAwB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CAAAA,CAEJ,CC5NgBE,SAAAA,GAAajF,EAA6BkF,EAAsB,GAAO,CAErF,KAAM,CAACC,EAAcC,CAAe,EAAIrF,EAAAA,SAAkC,CAAE,CAAA,EAGtEsF,EAAsBC,GAAoB,CACzCJ,GAELE,EAA2BhF,IAAA,CACzB,GAAGA,EACH,CAACkF,CAAO,EAAG,CAAClF,EAAKkF,CAAO,CACxB,EAAA,CAAA,EAIEC,EAAiBD,GACdJ,GAAcC,EAAaG,CAAO,EAcpC,MAAA,CACLjF,aAXmBoC,EAAAA,QAAQ,IACtBzC,EAEE,CAAC,GAAGA,CAAM,EAAEQ,KAAK,CAACC,EAAGC,IAAM,CAChC,MAAM8E,EAAQ,IAAI7E,KAAKF,EAAEG,MAAMC,IAAI,EAAEC,UAErC,OADc,IAAIH,KAAKD,EAAEE,MAAMC,IAAI,EAAEC,UACtB0E,CAAAA,CAChB,EANmB,GAOnB,CAACxF,CAAM,CAAC,EAITmF,aAAAA,EACAE,mBAAAA,EACAE,cAAAA,CAAAA,CAEJ,CCxCA,MAAME,GAAqBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+IAAA,yCAAA,IAAA,6DAAA,EAKnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,cAE1B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,QAAQC,GAAM,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAOzEC,GAAqBV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uEAAA,GAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQI,EAAE,EAUxCC,GAAkDA,CAAC,CAAEC,eAAAA,CAAe,IAErEC,EAAAA,IAAAf,GAAA,CACEc,SAAerC,EAAAA,IACduC,GAAAD,EAAA,IAACJ,GAA6BK,CAAAA,SAAAA,EAAOC,KAAnBD,EAAAA,EAAOE,EAAkB,CAC5C,CACH,CAAA,EClCEC,GAAmBlB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iIAAA,qBAAA,kBAAA,mBAAA,WAAA,+CAAA,IAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACpB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAOc,eAC/B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,GACjC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMiB,YAAYC,KACzC,CAAC,CAAEC,SAAAA,CAAS,IAAOA,IAAaC,OAAY,UAAY,UAI5C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMC,OAAOoB,SAAS,EAcvDC,GAA4CA,CAAC,CACjDxG,MAAAA,EACA2F,eAAAA,EACAU,SAAAA,EACA5B,mBAAAA,CACF,IAEImB,EAAA,IAACI,GAAU,CAAA,SAAAK,EAAoB,QAAS,IAAM5B,EAAmBzE,EAAMA,MAAM+F,EAAG,EAC7EJ,SAAerC,EAAAA,OACbsC,EAAA,IAAAa,GAAM,SAAN,CAAgCZ,SAAOa,EAAAA,SAAS1G,CAAK,CAAjC6F,EAAAA,EAAOE,EAA4B,CACzD,CACH,CAAA,ECrCEY,GAAyB7B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,eAAA,kBAAA,GAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACpB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAOyB,WAC/B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,GACrC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMG,QAAQI,GAC1B,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAG5CsB,EAAyB/B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAG5CuB,EAAsBC,EAAAA,GAAEhC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAUzB,GAEnC,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAO8B,YACvB,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGzC4B,GAAoBpC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8EAAA,GAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAGlC4B,EAAoBrC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAG5B,EAAA,CAAA,qCAAA,CAAA,EAEKmC,EAAqBC,EAAAA,KAAItC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAU1B,GACnC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,aAAa,EAG9CkC,EAAqBD,EAAAA,KAAItC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAUzB,GACnC,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAO8B,WAAW,EAG5CM,GAAuBzC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,eAAA,GAAA,EAEvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACtB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAGzCiC,GAAeC,EAAOC,EAAI,EAAC3C,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,qBAAA,8BAAA,mCAAA,gDAAA,6BAAA,IAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQI,GAAM,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACvD,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAOwC,QAE/B,CAAC,CAAEzC,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,GAEtC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAM8B,UAAU1B,GAEb,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMiB,YAAYC,KAG1C,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOyC,WAAW,EAWzDC,GAA4DA,CAAC,CAAE7H,MAAAA,CAAM,IAAM,eAC/E,cACG2G,GACC,CAAA,SAAA,CAAAmB,OAACjB,EACC,CAAA,SAAA,CAAAjB,EAAAA,IAACkB,GAAa,SAAa,eAAA,CAAA,SAC1BI,GACC,CAAA,SAAA,CAAAY,OAACX,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAM,QAAA,CAAA,EAClBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMiC,QAAU,MAAM,CAAA,EAC5C,SACCkF,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAI,MAAA,CAAA,EAChBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMC,KAAK,CAAA,EACjC,SACCkH,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAS,WAAA,CAAA,EACrBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMQ,UAAU,CAAA,EACtC,SACC2G,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAW,aAAA,CAAA,SACvBE,EAAY,CAAA,SAAA,CAAA,KAAGtH,EAAMA,MAAM+H,aAAe,GAAGC,QAAQ,CAAC,CAAA,EAAE,CAAA,EAC3D,SACCb,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAU,YAAA,CAAA,SACtBE,EAAY,CAAA,SAAA,CAAA,KAAGtH,EAAMA,MAAMiI,YAAc,GAAGD,QAAQ,CAAC,CAAA,EAAE,CAAA,EAC1D,SACCb,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAS,WAAA,CAAA,EACrBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMkI,iBAAmB,EAAE,CAAA,EACjD,SACCf,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAW,aAAA,CAAA,EACxBU,OAACR,GACC,MAAO,CACLa,OACGnI,EAAMA,MAAMuC,aAAe,GAAK,EAC7B,SACCvC,EAAMA,MAAMuC,aAAe,GAAK,EACjC,MACA,SACN,EAAA,SAAA,CAAA,KAECvC,EAAMA,MAAMuC,aAAe,GAAGyF,QAAQ,CAAC,CAAA,EAC5C,CAAA,EACF,SACCb,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAU,YAAA,CAAA,EACvBxB,EAAAA,IAAC0B,GAAatH,WAAMA,EAAAA,EAAAA,MAAMoI,aAANpI,YAAAA,EAAkBgI,QAAQ,KAAM,MAAM,CAAA,EAC5D,CAAA,EACF,CAAA,EACF,EAEChI,EAAMS,OACLqH,EAAAA,KAACjB,EACC,CAAA,SAAA,CAAAjB,EAAAA,IAACkB,GAAa,SAAQ,UAAA,CAAA,SACrBI,GACC,CAAA,SAAA,CAAAY,OAACX,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAU,YAAA,CAAA,EACtBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMqC,YAAc,MAAM,CAAA,EAChD,SACC8E,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAO,SAAA,CAAA,EACnBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAMqI,SAAW,MAAM,CAAA,EAC7C,SACClB,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAa,eAAA,CAAA,EACzBxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMS,EAAAA,MAAM2B,eAAiB,MAAM,CAAA,EACnD,SACC+E,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAe,iBAAA,CAAA,EAC3BxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMS,EAAAA,MAAMmC,iBAAmB,MAAM,CAAA,EACrD,SACCuE,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAe,iBAAA,CAAA,EAC3BxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMS,EAAAA,MAAMoC,iBAAmB,MAAM,CAAA,EACrD,SACCsE,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAe,iBAAA,CAAA,EAC3BxB,EAAA,IAAA0B,EAAA,CAAatH,SAAMA,EAAAA,MAAM8C,wBAA0B,MAAM,CAAA,EAC5D,CAAA,EACF,CAAA,EACF,EAGD9C,EAAMgC,UACL8F,EAAAA,KAACjB,EACC,CAAA,SAAA,CAAAjB,EAAAA,IAACkB,GAAa,SAAQ,UAAA,CAAA,SACrBI,GACC,CAAA,SAAA,CAAAY,OAACX,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAiB,mBAAA,CAAA,EAC7BxB,EAAA,IAAA0B,EAAA,CAActH,WAAMgC,EAAAA,EAAAA,WAAAA,YAAAA,EAAkBsG,oBAAqB,MAAM,CAAA,EACpE,SACCnB,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAe,iBAAA,CAAA,EAC3BxB,EAAA,IAAA0B,EAAA,CAActH,WAAMgC,EAAAA,EAAAA,WAAAA,YAAAA,EAAkBuG,kBAAmB,MAAM,CAAA,EAClE,SACCpB,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAe,iBAAA,CAAA,EAC3BxB,EAAA,IAAA0B,EAAA,CAActH,WAAMgC,EAAAA,EAAAA,WAAAA,YAAAA,EAAkBwG,kBAAmB,MAAM,CAAA,EAClE,SACCrB,EACC,CAAA,SAAA,CAAAvB,EAAAA,IAACwB,GAAY,SAAiB,mBAAA,CAAA,EAC7BxB,EAAA,IAAA0B,EAAA,CAActH,WAAMgC,EAAAA,EAAAA,WAAAA,YAAAA,EAAkByG,oBAAqB,MAAM,CAAA,EACpE,CAAA,EACF,CAAA,EACF,EAGDzI,EAAMA,MAAM0I,OACXZ,EAAA,KAACjB,EACC,CAAA,SAAA,CAAAjB,EAAAA,IAACkB,GAAa,SAAK,OAAA,CAAA,QAClBK,EACC,CAAA,SAAAvB,EAAA,IAAC0B,GAAatH,SAAMA,EAAAA,MAAM0I,MAAM,CAClC,CAAA,CAAA,EACF,EAGF9C,EAAA,IAAC2B,GACC,CAAA,SAAA3B,EAAAA,IAAC4B,GAAa,CAAA,GAAI,uBAAuBxH,EAAMA,MAAM+F,KAAM,SAAA,YAAU,CAAA,EACvE,CACF,CAAA,CAAA,CAEJ,EC9LM4C,GAAwB7D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,qBAAA,kBAAA,qBAAA,EAKpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQuD,GACpB,CAAC,CAAE1D,MAAAA,CAAM,IAAMA,EAAMC,OAAOyB,WAC/B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMgB,aAAaX,EAAE,EAIjDsD,GAAoB9B,EAAAA,GAAEhC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAU8B,GAEnC,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAMC,OAAO8B,YACvB,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,EAAMG,QAAQC,EAAE,EAGzCyD,GAA0BC,EAAAA,EAACjE,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,qBAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAUzB,GACnC,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAOC,cACvB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,QAAQyD,EAAE,EAIzCG,GAAiBxB,EAAOC,EAAI,EAAC3C,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,IAAA,qBAAA,8BAAA,qEAAA,6BAAA,IAAA,EAItB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQC,GAAM,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMG,QAAQyD,GACvD,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAMC,OAAOwC,QAE/B,CAAC,CAAEzC,MAAAA,CAAM,IAAMA,EAAMgB,aAAaX,GAGpB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMiB,YAAYC,KAG1C,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMC,OAAOyC,WAAW,EAWzDsB,GAAgDA,CAAC,CAAEC,SAAAA,CAAS,WAE7DR,GACC,CAAA,SAAA,CAAC/C,EAAA,IAAAiD,GAAA,CAAYM,SAAWA,EAAA,2BAA6B,gBAAgB,EACpEvD,EAAA,IAAAmD,GAAA,CACEI,SACGA,EAAA,kDACA,6EACN,EACC,CAACA,GAAYvD,EAAA,IAACqD,GAAe,CAAA,GAAG,aAAa,SAAsB,yBAAA,CACtE,CAAA,CAAA,EC5DEG,GAAUC,GAOf,CAAA,uEAAA,CAAA,EAEKC,GAA0BxE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,YAAA,KAAA,EAG1B,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACzB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAGtCgE,GAAoBzE,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,OAAA,QAAA,gDAAA,uCAAA,GAAA,EAIvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,OAAOyB,WAC5B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMC,OAAOc,eAC5B,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMC,OAAOyB,WAGnBwC,GACI,CAAC,CAAElE,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,EAAE,EAUjDkE,GAAoDA,CAAC,CAAEC,SAAAA,EAAW,CAAE,IAEtE7D,EAAA,IAAC0D,GACE/F,CAAAA,SAAAA,MAAMC,KAAK,CAAEkG,OAAQD,CAAU,CAAA,EAAEnG,IAAI,CAACqG,EAAGC,IACvChE,EAAAA,IAAA2D,GAAA,GAAgBK,EAClB,CACH,CAAA,EC/BEC,GAA4B/E,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAG5B,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAGlCC,EAAqBV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uEAAA,UAAA,gBAAA,GAAA,EAIf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMG,QAAQI,GACjC,CAAC,CAAEP,MAAAA,EAAO4E,OAAAA,EAAQC,KAAAA,CAAK,IAC9BD,EAAS5E,EAAMC,OAAO6E,QAAUD,EAAO7E,EAAMC,OAAO8E,OAAS/E,EAAMC,OAAO8B,YAC7D,CAAC,CAAE6C,OAAAA,EAAQC,KAAAA,CAAK,IAAOD,GAAUC,EAAO,IAAM,QAAS,EAGlEG,GAAe7C,EAAAA,KAAItC,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,kBAAA,cAAA,8DAAA,UAAA,GAAA,EAEZ,CAAC,CAAEC,MAAAA,CAAM,IAAM,GAAGA,EAAMG,QAAQ8E,OAAOjF,EAAMG,QAAQI,KAC/C,CAAC,CAAEP,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,GACtC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAM8B,UAAUvB,GAGxB,CAAC,CAAEP,MAAAA,EAAOkF,KAAAA,CAAK,IACjCA,IAAS,OACLlF,EAAMC,OAAOkF,cAAgB,yBAC7BD,IAAS,QACTlF,EAAMC,OAAOmF,aAAe,yBAC5BpF,EAAMC,OAAOyB,WACV,CAAC,CAAE1B,MAAAA,EAAOkF,KAAAA,CAAK,IACtBA,IAAS,OACLlF,EAAMC,OAAO6E,QACbI,IAAS,QACTlF,EAAMC,OAAO8E,OACb/E,EAAMC,OAAO8B,WAAW,EAmB1BsD,GAAsCA,CAAC,CAC3CnL,OAAAA,EACAC,UAAAA,EAAY,GACZiF,WAAAA,EAAa,GACbkG,YAAAA,CACF,IAAM,CACJ,MAAMC,EAAWC,KACX,CAAEjL,aAAAA,EAAcgF,mBAAAA,EAAoBE,cAAAA,CAAAA,EAAkBN,GAAajF,EAAQkF,CAAU,EAGrFqG,EAAmBjG,GAAoB,CACvC8F,EACFA,EAAY9F,CAAO,EAGnB+F,EAAS,eAAe/F,GAAS,CACnC,EAIIkG,EAAyB/I,EAC7B,QAAA,IAAM,CACJ,CACEkE,GAAI,OACJD,MAAO,OACPY,SAAmB1G,GAAA4F,EAAAA,IAACJ,EAAaxF,CAAAA,SAAAA,EAAMA,MAAMC,KAAK,CAAA,EAEpD,CACE8F,GAAI,SACJD,MAAO,SACPY,SAAmB1G,GAAA4F,EAAA,IAACJ,GAAaxF,SAAMA,EAAAA,MAAMiC,QAAU,MAAM,CAAA,EAE/D,CACE8D,GAAI,YACJD,MAAO,YACPY,SAAU1G,GACP4F,MAAAJ,EAAA,CACC,eAAC0E,GAAM,CAAA,KAAMlK,EAAMA,MAAMQ,UAAU0B,cAChClC,SAAMA,EAAAA,MAAMQ,SACf,CAAA,EACF,CAAA,EAGJ,CACEuF,GAAI,QACJD,MAAO,QACPY,SAAmB1G,GAAA,WAEbA,GAAAA,EAAMA,MAAM6K,gBAAiB,CAC/B,MAAMC,EAAeC,GAAiBC,sBACpChL,EAAMA,MAAM6K,eACd,EACO,OAAAjF,EAAA,IAACJ,GAAasF,SAAaA,CAAA,CAAA,EAIpC,IACG9K,EAAAA,EAAMA,MAAc6K,kBAApB7K,MAAAA,EAAqCiL,YACrCjL,EAAAA,EAAMA,MAAc6K,kBAApB7K,MAAAA,EAAqCkL,OACtC,CACA,MAAMJ,EAAeC,GAAiBC,sBACnChL,EAAMA,MAAc6K,eACvB,EACO,OAAAjF,EAAA,IAACJ,GAAasF,SAAaA,CAAA,CAAA,EAIpC,OAAQlF,EAAA,IAAAJ,EAAA,CAAaxF,WAAMS,EAAAA,EAAAA,QAAAA,YAAAA,EAAO2B,gBAAiB,KAAM,CAAA,CAC3D,CAAA,EAEF,CACE2D,GAAI,QACJD,MAAO,QACPY,SAAmB1G,GAAA8H,EAAA,KAACtC,EAAY,CAAA,SAAA,CAAA,KAAGxF,EAAMA,MAAM+H,aAAe,GAAGC,QAAQ,CAAC,CAAA,EAAE,CAAA,EAE9E,CACEjC,GAAI,OACJD,MAAO,OACPY,SAAmB1G,GAAA8H,EAAA,KAACtC,EAAY,CAAA,SAAA,CAAA,KAAGxF,EAAMA,MAAMiI,YAAc,GAAGD,QAAQ,CAAC,CAAA,EAAE,CAAA,EAE7E,CACEjC,GAAI,OACJD,MAAO,OACPY,SAAmB1G,GAAA4F,EAAA,IAACJ,GAAaxF,SAAMA,EAAAA,MAAMkI,iBAAmB,EAAE,CAAA,EAEpE,CACEnC,GAAI,aACJD,MAAO,MACPY,SAAU1G,GACP8H,OAAAtC,EAAA,CACC,QAASxF,EAAMA,MAAMuC,aAAe,GAAK,EACzC,MAAOvC,EAAMA,MAAMuC,aAAe,GAAK,EAAE,SAAA,CAAA,KAEtCvC,EAAMA,MAAMuC,aAAe,GAAGyF,QAAQ,CAAC,CAAA,EAC5C,CAAA,EAGJ,CACEjC,GAAI,UACJD,MAAO,UACPY,SAAU1G,GACP4F,EAAAA,IAAAJ,EAAA,CACC,SAACI,MAAA,SAAA,CACC,QAAS,IAAM+E,EAAgB3K,EAAMA,MAAM+F,EAAG,EAC9C,MAAO,CACLoF,QAAS,UACTC,SAAU,OACVC,OAAQ,iBACRnF,aAAc,MACdU,WAAY,QACZ0E,OAAQ,SAAA,EACR,eAGJ,CAAA,EACF,CAAA,CAEH,EAEH,CAACX,CAAe,CAClB,EAGMY,EAAsB,UAAUX,EAAQlB,eAG9C,OAAIrK,EACKuG,EAAA,IAAC4D,GAAiB,CAAA,SAAU,CAAK,CAAA,EAItC,CAAC/J,GAAgBA,EAAaiK,SAAW,QACnCR,GAAe,CAAA,SAAU9J,GAAUA,EAAOsK,OAAS,CAAK,CAAA,EAIhE5B,EAAA,KAAC+B,IAAmB,MAAO,CAAE,0BAA2B0B,CACtD,EAAA,SAAA,CAAC3F,EAAAA,IAAAF,GAAA,CAAgB,eAAgBkF,CAAQ,CAAA,EAExCnL,EAAa6D,IAAItD,GACf8H,EAAA,KAAArB,GAAM,SAAN,CACC,SAAA,CAACb,EAAAA,IAAAY,GAAA,CACC,MAAAxG,EACA,eAAgB4K,EAChB,SAAUjG,EAAc3E,EAAMA,MAAM+F,EAAG,EACvC,mBAAAtB,CAAuC,CAAA,EAExCE,EAAc3E,EAAMA,MAAM+F,EAAG,GAAKH,EAAAA,IAACiC,IAAqB,MAAA7H,EAAgB,CAPtDA,CAAAA,EAAAA,EAAMA,MAAM+F,EAQjC,CACD,CACH,CAAA,CAAA,CAEJ,EC9NMyF,GAASA,IAAO5F,EAAAA,IAAA,OAAA,CAAK,SAAE,IAAA,CAAA,EACvB6F,GAAWA,IAAO7F,EAAAA,IAAA,OAAA,CAAK,SAAE,IAAA,CAAA,EACzB8F,GAAcA,IAAO9F,EAAAA,IAAA,OAAA,CAAK,SAAC,GAAA,CAAA,EAC3B+F,GAAUA,IAAO/F,EAAAA,IAAA,OAAA,CAAK,SAAC,GAAA,CAAA,EACvBgG,GAAcA,IAAOhG,EAAAA,IAAA,OAAA,CAAK,SAAE,IAAA,CAAA,EAC5BiG,GAAWA,IAAOjG,EAAAA,IAAA,OAAA,CAAK,SAAE,IAAA,CAAA,EAEzBkG,GAAgBA,CAAC,CAAEC,iBAAAA,CAAiB,IAAM,CAC9C,KAAM,CAACC,EAAMC,CAAO,EAAI9M,WAAS,IAAI,EAC/B,CAAC+M,EAASC,CAAU,EAAIhN,WAAS,IAAI,EACrC,CAACiN,EAAYC,CAAa,EAAIlN,WAAS,IAAI,EAC3C,CAACmN,EAAcC,CAAe,EAAIpN,WAAS,MAAM,EACjD,CAACqN,EAAOC,CAAQ,EAAItN,WAAS,IAAI,EACCA,EAAAA,SAAS,KAAK,EAGtD,MAAMuN,EAAkB,CAEtBzM,KAAM,CAAC,MAAM,EACboC,WAAY,CAAC,YAAY,EACzB7B,UAAW,CAAC,WAAW,EACvByB,OAAQ,CAAC,QAAQ,EACjB8F,YAAa,CAAC,aAAa,EAC3BE,WAAY,CAAC,YAAY,EACzB1F,YAAa,CAAC,cAAc,EAC5B6F,WAAY,CAAC,YAAY,EACzBuE,YAAa,CAAC,eAAe,EAC7BzE,gBAAiB,CAAC,kBAAkB,EACpC0E,SAAU,CAAC,UAAU,EACrB9J,uBAAwB,CAAC,8BAA8B,EACvDuF,QAAS,CAAC,sBAAsB,EAChCwE,WAAY,CAAC,YAAY,EACzBC,UAAW,CAAC,WAAW,EACvBrM,MAAO,CAAC,OAAO,EACf2B,cAAe,CAAC,eAAe,EAC/BQ,gBAAiB,CAAC,iBAAiB,EACnC8F,MAAO,CAAC,OAAO,EACfqE,QAAS,CAAC,SAAS,EACnBC,kBAAmB,CAAC,mBAAmB,EACvCC,SAAU,CAAC,UAAU,EACrBC,cAAe,CAAC,eAAe,EAC/BrK,gBAAiB,CAAC,iBAAiB,EACnCsK,gBAAiB,CAAC,iBAAiB,EACnCC,IAAK,CAAC,KAAK,EACXC,iBAAkB,CAAC,kBAAkB,EACrClK,gBAAiB,CAAC,iBAAiB,EACnCmK,cAAe,CAAC,eAAe,EAC/BC,WAAY,CAAC,YAAY,EACzBC,aAAc,CAAC,cAAc,EAC7BC,YAAa,CAAC,aAAa,EAC3BC,kBAAmB,CAAC,mBAAmB,EACvCC,UAAW,CAAC,WAAW,CAAA,EAInBC,EAAuB,CAAC,UAAW,SAAU,UAAU,EAGvDC,EAAiB,CACrB,UACA,cACA,MACA,cACA,gBACA,aACA,cACA,QACA,OAAO,EAIHC,EAAgB,CAAC,MAAO,KAAM,KAAM,MAAO,KAAM,MAAO,MAAO,KAAK,EAEpEC,EAAmBC,cAAuBC,GAAA,CAC9C,MAAMC,EAAeD,EAAMvM,OAAOyM,MAAM,CAAC,EACrCD,GAAgBA,EAAa9D,OAAS,aACxC6B,EAAQiC,CAAY,EACpB3B,EAAgB,YAAY,EAC5B6B,EAASF,CAAY,EAEzB,EAAG,CAAE,CAAA,EAGCG,EAAyBC,GAAA,CAC7B,MAAM3N,EAAS,CAAA,EACf,IAAI4N,EAAU,GACVC,EAAW,GAEf,QAASC,EAAI,EAAGA,EAAIH,EAAK5E,OAAQ+E,IAAK,CAC9BC,MAAAA,EAAOJ,EAAKG,CAAC,EAEfC,IAAS,IACXF,EAAW,CAACA,EACHE,IAAS,KAAO,CAACF,GACnBG,EAAAA,KAAKJ,EAAQK,KAAM,CAAA,EAChBL,EAAA,IAECG,GAAAA,EAIRC,OAAAA,EAAAA,KAAKJ,EAAQK,KAAM,CAAA,EACnBjO,CAAAA,EAGHyN,EAAW,MAAOpC,GAAS,CAC3B,GAAA,CAEI6C,MAAAA,GADO,MAAM7C,EAAK8C,QACLC,MAAM;AAAA,CAAI,EAAEjN,OAAQwM,GAASA,EAAKM,KAAAA,CAAM,EAEvDC,GAAAA,EAAMnF,SAAW,EAAG,CACtBsF,QAAQ1P,MAAM,mBAAmB,EACjCiN,EAAgB,MAAM,EACtB,OAGM0C,QAAAA,IAAI,qBAAsBJ,EAAMnF,MAAM,EAGxCwF,MAAAA,EAAaL,EAAM,CAAC,EACpBM,EAAUd,EAAaa,CAAU,EAAE5L,IAAW8L,GAAAA,EAAER,KAAK,EAAE1M,YAAY,EAAEmN,QAAQ,KAAM,EAAE,CAAC,EAEpFJ,QAAAA,IAAI,kBAAmBE,CAAO,EAGtC,MAAMG,EAAOT,EACVU,MAAM,CAAC,EACPzN,OAAiBwM,GAAAA,EAAKM,KAAK,CAAC,EAC5BtL,IAAcgL,GAAA,CACPkB,MAAAA,EAASnB,EAAaC,CAAI,EAC1BmB,EAAM,CAAA,EACJC,OAAAA,EAAAA,QAAQ,CAACC,EAAQ/F,IAAU,OAC7B+F,EAAAA,CAAM,IAAIH,EAAAA,EAAO5F,CAAK,IAAZ4F,YAAAA,EAAeZ,OAAOS,QAAQ,KAAM,MAAO,EAAA,CAC1D,EACMI,CAAAA,CACR,EAEKR,QAAAA,IAAI,eAAgBK,EAAK5F,MAAM,EACvCsF,QAAQC,IAAI,cAAeK,EAAK,CAAC,CAAC,EAEvBnD,EAAA,CAAEgD,QAAAA,EAASG,KAAAA,CAAAA,CAAM,EAC5BM,EAAWT,EAASG,CAAI,QACjBhQ,GACCA,QAAAA,MAAM,qBAAsBA,CAAK,EACzCiN,EAAgB,MAAM,CACxB,CAAA,EAGIqD,EAAaA,CAACT,EAASG,IAAS,CACpCN,QAAQC,IAAI,4BAA4B,EAChCA,QAAAA,IAAI,qBAAsBE,CAAO,EAGzC,MAAMU,EAAiB,CAAA,EAChBC,OAAAA,QAAQpD,CAAe,EAAEgD,QAAQ,CAAC,CAACK,EAASC,CAAe,IAAM,CACtE,MAAMC,EAAgBd,EAAQe,KAAMd,IAClCY,EAAgBG,KAAMC,GAAOhB,GAAElN,YAAAA,EAAcC,SAASiO,EAAGlO,YAAY,CAAC,CAAC,CACzE,EACe6N,EAAAA,CAAO,EAAIE,GAAiB,WAAA,CAC5C,EACOhB,QAAAA,IAAI,0BAA2BY,CAAc,EAErD,MAAMQ,EAASf,EACZhM,IAAI,CAACmM,EAAKa,IAAa,CACtB,MAAMC,EAAc,CAAA,EA8FpB,GA3FOT,OAAAA,QAAQpD,CAAe,EAAEgD,QAAQ,CAAC,CAACK,EAASC,EAAe,IAAM,CACtE,MAAMC,EAAgBd,EAAQe,KAAMd,GAClCY,GAAgBG,KAAMC,GAAOhB,EAAElN,YAAAA,EAAcC,SAASiO,EAAGlO,YAAY,CAAC,CAAC,CACzE,EAEI+N,GAAAA,GAAiBR,EAAIQ,CAAa,EAAG,CACvC,IAAIxO,EAAQgO,EAAIQ,CAAa,EAAErB,KAAK,EAGpC,GAAImB,IAAY,aAAc,CAE5B,IAAIS,EAAQ/O,EAAM4N,QAAQ,IAAK,EAAE,EAC3BoB,MAAAA,EAAa7C,EAAqBsC,KAEpCM,IAAAA,EAAMtO,YAAY,EAAEC,SAASuO,GAAGxO,YAAY,CAAC,GAC7CsO,EACGtO,YAAY,EACZmN,QAAQ,SAAU,EAAE,EACpBlN,SAASuO,GAAGxO,YAAY,EAAEmN,QAAQ,SAAU,EAAE,CAAC,CACtD,EACYU,EAAAA,CAAO,EAAIU,GAAc,mBAC5BV,IAAY,YAAa,CAE5BY,MAAAA,EAAQlP,EAAMS,cAChByO,EAAMxO,SAAS,MAAM,GAAKwO,EAAMxO,SAAS,KAAK,GAAKwO,IAAU,IAC/DJ,EAAYR,CAAO,EAAI,OACdY,EAAMxO,SAAS,OAAO,GAAKwO,EAAMxO,SAAS,MAAM,GAAKwO,IAAU,IACxEJ,EAAYR,CAAO,EAAI,QAEvBQ,EAAYR,CAAO,EAAItO,UAEhBsO,IAAY,WAAY,CAE3BY,MAAAA,EAAQlP,EAAMS,cAChByO,EAAMxO,SAAS,KAAK,GAAKwO,EAAMxO,SAAS,QAAQ,GAAKwO,IAAU,IACjEJ,EAAYR,CAAO,EAAI,MACdY,EAAMxO,SAAS,MAAM,GAAKwO,EAAMxO,SAAS,MAAM,GAAKwO,IAAU,IACvEJ,EAAYR,CAAO,EAAI,OAEvBQ,EAAYR,CAAO,EAAItO,UAEhBsO,IAAY,UAAW,CAEhC,MAAM1H,EAAUwF,EAAeqC,QAE3BzO,EAAMS,cAAcC,SAASyO,EAAG1O,aAAa,GAC7C0O,EAAG1O,YAAY,EAAEC,SAASV,EAAMS,YAAa,CAAA,CACjD,EACY6N,EAAAA,CAAO,EAAI1H,GAAW5G,UACzBsO,IAAY,SAAU,CAEzB9N,MAAAA,EAAS6L,EAAcoC,KAC3BzO,GAAAA,EAAMS,YAAY,EAAEC,SAASuO,EAAGxO,YAAY,CAAC,CAC/C,EACY6N,EAAAA,CAAO,EAAI9N,GAAUR,UAEjC,CAAC,cAAe,aAAc,aAAc,aAAa,EAAEU,SAAS4N,CAAO,EAC3E,CAEA,MAAMc,EAAeC,WAAWrP,EAAM4N,QAAQ,YAAa,EAAE,CAAC,EAC9DkB,EAAYR,CAAO,EAAI9M,MAAM4N,CAAY,EAAI,KAAOA,UAC3Cd,IAAY,cAAe,CAEpC,MAAMc,EAAeC,WAAWrP,EAAM4N,QAAQ,SAAU,EAAE,CAAC,EAC3DkB,EAAYR,CAAO,EAAI9M,MAAM4N,CAAY,EAAI,KAAOA,UAC3Cd,IAAY,yBAA0B,CAEzCgB,MAAAA,EAASD,WAAWrP,CAAK,GAAK,EACxBsO,EAAAA,CAAO,EAAIiB,KAAKC,IAAI,EAAGD,KAAKE,IAAI,EAAGH,CAAM,CAAC,UAC7ChB,IAAY,kBAAmB,CAElCoB,MAAAA,EAAYL,WAAWrP,CAAK,GAAK,EACvC8O,EAAYR,CAAO,EAAIiB,KAAKC,IAAI,GAAKE,CAAS,MACrC,CAAC,aAAc,WAAW,EAAEhP,SAAS4N,CAAO,EAErDQ,EAAYR,CAAO,EAAItO,EAAMU,SAAS,GAAG,EAAIV,EAAQ,KAGrD8O,EAAYR,CAAO,EAAItO,EAE3B,CACD,EAGI8O,EAAYlO,aAAYkO,EAAYlO,WAAa,YACjDkO,EAAY/P,YAAW+P,EAAY/P,UAAY,QAC/C+P,EAAYtO,SAAQsO,EAAYtO,OAAS,OACzCsO,EAAYzN,yBAAwByN,EAAYzN,uBAAyB,GACzEyN,EAAYrI,kBAAiBqI,EAAYrI,gBAAkB,GAG5DqI,EAAYtQ,KACV,GAAA,CACF,MAAMmR,EAAYb,EAAYtQ,KAAK8O,MAAM,GAAG,EACxCqC,GAAAA,EAAU1H,SAAW,EAAG,CAC1B,KAAM,CAAC2H,GAAOC,EAAKC,CAAI,EAAIH,EACfnR,EAAAA,KAAO,GAAGsR,KAAQF,GAAMG,SAAS,EAAG,GAAG,KAAKF,EAAIE,SAAS,EAAG,GAAG,WAGjEvR,EAAAA,SAAWF,KAAK,EAAE0R,YAAc1C,EAAAA,MAAM,GAAG,EAAE,CAAC,CAC1D,MAEY9O,EAAAA,SAAWF,KAAK,EAAE0R,YAAc1C,EAAAA,MAAM,GAAG,EAAE,CAAC,EAe1D,OAXIuB,EAAW,IACLrB,QAAAA,IAAI,OAAOqB,YAAoBC,CAAW,EAClDvB,QAAQC,IACN,aAAa,CAAC,CAACsB,EAAYtQ,0BAA0B,CAAC,CAACsQ,EAAYxI,gCAAgC,CAAC,CAACwI,EAAYtI,YACnH,GAKAsI,EAAYxI,aAAewI,EAAYtI,YAAcsI,EAAYhO,YAO5DgO,GAJDD,EAAW,GAAWrB,QAAAA,IAAI,OAAOqB,2BAAkC,EAChE,KAGFC,CACR,EACAzO,OAAO4P,OAAO,EAEjBrF,EAAcgE,CAAM,EAGdsB,MAAAA,EAActB,EAAOvO,OAClB8P,GAAAA,EAAE3R,MAAQ2R,EAAEvP,aAAeuP,EAAE7J,aAAe6J,EAAE3J,WACvD,EACM4J,EAAiBxB,EAAOvO,OAC3B8P,GAAMA,EAAEvP,YAAc,CAACuL,EAAqBzL,SAASyP,EAAEvP,UAAU,CACpE,EAAEqH,OACIoI,EAAgBzB,EAAOvO,OAAQ8P,GAAMA,EAAE3R,MAAQ,CAAC2R,EAAE7J,aAAe,CAAC6J,EAAE3J,UAAU,EAAEyB,OAChFqI,EAAgB1B,EAAOvO,UAAc8P,EAAEhF,WAAa,KAAK,EAAElD,OAC3DsI,EAAe3B,EAAOvO,UAAc8P,EAAEhF,WAAa,MAAM,EAAElD,OAExD+C,EAAA,CACPwF,UAAW3C,EAAK5F,OAChBiI,YAAaA,EAAYjI,OACzBmI,eAAAA,EACAC,cAAAA,EACAC,cAAAA,EACAC,aAAAA,EACAE,QAAS5C,EAAK5F,OAASiI,EAAYjI,OACnCyI,QACER,EAAYjI,OAAS,GACfqI,GAAiBA,EAAgBC,GAAiB,KAAKhK,QAAQ,CAAC,EAClE,CAAA,CACP,EAEDuE,EAAgB,SAAS,CAAA,EAGrB6F,GAAe,SAAY,CAC/B7F,EAAgB,YAAY,EAExB,GAAA,CAEF,MAAM8F,EAAiBjG,EAAW9I,IAAI,MAAOiN,GAAgB,CAE3D,MAAM+B,EAAqB,CACzB,GAAG/B,EAEHgC,WAAY,IAAIxS,KAAK,EAAE0R,YAAY,EACnCe,WAAY,IAAIzS,KAAK,EAAE0R,YAAY,CAAA,EAI/BgB,EAAY,CAChBrQ,cAAemO,EAAYnO,eAAiB,KAC5CQ,gBAAiB2N,EAAY3N,iBAAmB,KAChDC,gBAAiB0N,EAAY1N,iBAAmB,KAChDsK,gBAAiBoD,EAAYpD,iBAAmB,KAChDC,IAAKmD,EAAYnD,KAAO,IAAA,EAIpBsF,EAAa,CACjBzF,SAAUsD,EAAYtD,UAAYsD,EAAYtQ,KAC9C8M,QAASwD,EAAYxD,SAAW,KAChCG,cAAeqD,EAAYrD,eAAiB,KAC5CF,kBAAmBuD,EAAYvD,mBAAqB,IAAA,EAIhD2F,EAAe,CACnBtF,iBAAkBkD,EAAYlD,kBAAoB,KAClDlK,gBAAiBoN,EAAYpN,iBAAmB,KAChDmK,cAAeiD,EAAYjD,eAAiB,KAC5CC,WAAYgD,EAAYhD,YAAc,KACtCC,aAAc+C,EAAY/C,cAAgB,KAC1CC,YAAa8C,EAAY9C,aAAe,KACxCC,kBAAmB6C,EAAY7C,mBAAqB,KACpDC,UACE4C,EAAY5C,WACZ,wBAAwB,IAAI5N,OAAO6S,mBACjCrC,sBAAAA,EAAY7H,OAAS,QAAA,EAKrBmK,EAAoB,CACxB7S,MAAOsS,EACPQ,YAAaJ,EACbjS,MAAOgS,EACPzQ,SAAU2Q,CAAAA,EAGLjT,OAAAA,GAAoBqT,qBAAqBF,CAAiB,CAAA,CAClE,EAEKG,MAAAA,QAAQC,IAAIZ,CAAc,EAChC9F,EAAgB,UAAU,EAG1B2G,WAAW,IAAM,CACInH,GAAA,MAAAA,KAClB,GAAI,QACAzM,GACCA,QAAAA,MAAM,iBAAkBA,CAAK,EACrCiN,EAAgB,SAAS,CAC3B,CAAA,EAGI4G,EAAsBA,IAAM,CAChC,MAAMC,EAAM,CAEVC,OAAOC,KAAKlH,EAAW,CAAC,GAAK,EAAE,EAAEmH,KAAK,GAAG,EAEzC,GAAGnH,EAAW9I,IAAKtD,GACjBqT,OAAO7D,OAAOxP,CAAK,EAChBsD,OAAW,IAAIkQ,IAAI,EACnBD,KAAK,GAAG,CACb,CAAA,EACAA,KAAK;AAAA,CAAI,EAELE,EAAO,IAAIC,KAAK,CAACN,CAAG,EAAG,CAAEhJ,KAAM,UAAA,CAAY,EAC3CuJ,EAAMC,IAAIC,gBAAgBJ,CAAI,EAC9B5T,EAAIiU,SAASC,cAAc,GAAG,EACpClU,EAAEmU,KAAOL,EACT9T,EAAEoU,SAAW,qBACbpU,EAAEqU,MAAM,CAAA,EAIR,OAAApM,EAAA,KAAC,MAAI,CAAA,UAAU,sDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,OACb,SAAA,CAAClC,EAAA,IAAA,KAAA,CAAG,UAAU,wCAAwC,SAA2B,8BAAA,EAChFA,EAAA,IAAA,IAAA,CAAE,UAAU,gBAAe,SAE5B,uEAAA,EAEAkC,EAAAA,KAAC,MAAI,CAAA,UAAU,6BACb,SAAA,CAAAlC,EAAAA,IAAC,UAAO,SAAyB,2BAAA,CAAA,EAAS,kEAAA,EAE5C,CAAA,EACF,EAGC0G,IAAiB,QACfxE,OAAA,MAAA,CAAI,UAAU,oEACb,SAAA,CAAAlC,MAAC,MAAI,CAAA,UAAU,wBACb,SAAAA,MAAC4F,IAAM,CAAA,EACT,EACA5F,EAAAA,IAAC,OAAI,UAAU,OACb,gBAAC,QAAM,CAAA,QAAQ,aAAa,UAAU,iBACpC,SAAA,CAACA,EAAA,IAAA,OAAA,CAAK,UAAU,wDAAuD,SAEvE,6BAAA,EACAA,EAAAA,IAAC,QACC,CAAA,GAAG,aACH,KAAK,OACL,OAAO,OACP,SAAUmI,EACV,UAAU,SAAS,CAAA,CAAA,CAAA,CAEvB,CACF,CAAA,EACCnI,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAuB,SAEpC,uDAAA,CAAA,EACF,EAID0G,IAAiB,cACfxE,OAAA,MAAA,CAAI,UAAU,mBACb,SAAA,CAAClC,EAAAA,IAAA,MAAA,CAAI,UAAU,6EAA8E,CAAA,EAC5FA,EAAA,IAAA,IAAA,CAAE,UAAU,wBAAwB,SAAkC,qCAAA,CAAA,EACzE,EAID0G,IAAiB,WAAaE,GAC5B1E,EAAA,KAAA,MAAA,CAAI,UAAU,YAEb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,4BACb,SAAA,CAAClC,EAAA,IAAA,KAAA,CAAG,UAAU,6BAA6B,SAAc,iBAAA,EACzDkC,EAAAA,KAAC,MAAI,CAAA,UAAU,qDACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAAlC,EAAA,IAAC6F,GAAQ,EAAA,EACT3D,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMyF,EAAAA,UAAU,aAAA,EAAW,CAAA,EACrD,EACAnK,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAlC,EAAA,IAAC8F,GAAW,EAAA,EACZ5D,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMmF,EAAAA,YAAY,eAAA,EAAa,CAAA,EACzD,EACA7J,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAlC,EAAA,IAACgG,GAAW,EAAA,EACZ9D,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMqF,EAAAA,eAAe,iBAAA,EAAe,CAAA,EAC9D,EACA/J,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAAlC,EAAA,IAAC+F,GAAO,EAAA,EACR7D,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAM0F,EAAAA,QAAQ,UAAA,EAAQ,CAAA,EAChD,CAAA,EACF,EAGApK,EAAAA,KAAC,MAAI,CAAA,UAAU,8EACb,SAAA,CAACA,EAAAA,KAAA,MAAA,CAAI,UAAU,oBACb,SAAA,CAAClC,EAAA,IAAA,OAAA,CAAK,UAAU,iBAAiB,SAAC,IAAA,EAClCkC,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMuF,EAAAA,cAAc,OAAA,EAAK,CAAA,EACnD,EACAjK,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAClC,EAAA,IAAA,OAAA,CAAK,UAAU,eAAe,SAAC,IAAA,EAChCkC,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMwF,EAAAA,aAAa,SAAA,EAAO,CAAA,EACpD,EACAlK,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAClC,EAAA,IAAA,OAAA,CAAK,UAAU,gBAAgB,SAAE,KAAA,EAClCkC,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAM2F,EAAAA,QAAQ,YAAA,EAAU,CAAA,EAClD,EACArK,EAAAA,KAAC,MAAI,CAAA,UAAU,oBACb,SAAA,CAAClC,EAAA,IAAA,OAAA,CAAK,UAAU,kBAAkB,SAAE,KAAA,EACpCkC,EAAAA,KAAC,OAAK,CAAA,UAAU,OAAQ0E,SAAAA,CAAMsF,EAAAA,cAAc,iBAAA,EAAe,CAAA,EAC7D,CAAA,EACF,CAAA,EACF,EAGAhK,EAAAA,KAAC,MAAI,CAAA,UAAU,6CACb,SAAA,CAAClC,EAAAA,IAAA,MAAA,CAAI,UAAU,gCACb,SAAAA,EAAA,IAAC,MAAG,UAAU,wBAAwB,oCAAwB,CAChE,CAAA,QACC,MAAI,CAAA,UAAU,kBACb,SAACkC,EAAA,KAAA,QAAA,CAAM,UAAU,sCACf,SAAA,CAAClC,EAAA,IAAA,QAAA,CAAM,UAAU,aACf,SAACA,EAAAA,IAAA,KAAA,CACEyN,SAAOC,OAAAA,KAAKlH,EAAW,CAAC,GAAK,CAAA,CAAE,EAC7BmD,MAAM,EAAG,EAAE,EACXjM,IAAK6Q,GACHvO,EAAAA,IAAA,KAAA,CAEC,UAAU,kEAETuO,SAHIA,CAAAA,EAAAA,CAIP,CACD,CACL,CAAA,EACF,EACAvO,EAAAA,IAAC,QAAM,CAAA,UAAU,2BACdwG,SAAAA,EAAWmD,MAAM,EAAG,CAAC,EAAEjM,IAAI,CAACtD,EAAO4J,IACjChE,EAAAA,IAAA,KAAA,CAAe,UAAU,mBACvByN,SAAOvD,OAAAA,QAAQ9P,CAAK,EAClBuP,MAAM,EAAG,EAAE,EACXjM,IAAI,CAAC,CAAC8Q,EAAO3S,CAAK,EAAGgN,IACpB7I,EAAA,IAAC,MAAW,UAAU,kCACnB,SAAOnE,OAAAA,GAAU,UAAYA,IAAU,KACpCA,EAAMuG,QAAQ,CAAC,EACfvG,GAAS,GAAA,EAHNgN,CAIT,CACD,CAAA,EATI7E,CAUT,CACD,CACH,CAAA,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,EAGA9B,EAAAA,KAAC,MAAI,CAAA,UAAU,aACb,SAAA,CAAAA,EAAA,KAAC,SACC,CAAA,QAASsK,GACT,UAAU,mFAAkF,SAAA,CAAA,UAEpF5F,EAAMmF,YAAY,SAAA,EAC5B,EACC7J,EAAA,KAAA,SAAA,CACC,QAASqL,EACT,UAAU,oGAEV,SAAA,CAAAvN,EAAA,IAACiG,GAAQ,EAAA,EAAA,sBAAA,EAEX,EACAjG,EAAAA,IAAC,UACC,QAAS,IAAM2G,EAAgB,MAAM,EACrC,UAAU,+EAA8E,SAG1F,YAAA,CAAA,CAAA,EACF,CAAA,EACF,EAIDD,IAAiB,YACfxE,OAAA,MAAA,CAAI,UAAU,mBACb,SAAA,CAAAlC,MAAC,MAAI,CAAA,UAAU,wBACb,SAAAA,MAAC8F,IAAW,CAAA,EACd,EACC9F,EAAA,IAAA,KAAA,CAAG,UAAU,2CAA2C,SAAgB,mBAAA,EACzEkC,EAAAA,KAAC,IAAE,CAAA,UAAU,qBACV0E,SAAAA,CAAOmF,GAAAA,YAAAA,EAAAA,YAAY,gDAAA,EACtB,EACA/L,EAAAA,IAAC,UACC,QAAS,IAAM2G,EAAgB,MAAM,EACrC,UAAU,gEAA+D,SAG3E,qBAAA,CAAA,CAAA,EACF,CAEJ,CAAA,CAAA,CAEJ,ECxiBM8H,GAAqBvP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,mBAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,MAAK,EAI5C6O,GAAqBxO,EAAAA,MAAKf,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,uHAAA,gBAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAE1C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WASnC,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,MAAK,EAKvD8O,GAAqBC,EAAAA,MAAKzP,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,+DAAA,yBAAA,sEAAA,uEAAA,sBAAA,EACnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAAS,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC9E,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc0B,aAAc,qBACrC,CAAC,CAAE1B,MAAAA,EAAOuP,UAAAA,CAAU,aACtCA,OAAAA,IACIvP,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBACb,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBI,KAAM,OACjD,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WACxC,CAAC,CAAE/B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAKjC,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAChC,CAAC,CAAEzC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAKhD,CAAC,CAAEzC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAS/C,CAAC,CAAEzC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAK5EsP,GAAsBC,EAAAA,OAAM5P,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,8EAAA,yBAAA,sEAAA,kcAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAAS,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC9E,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc0B,aAAc,qBACrC,CAAC,CAAE1B,MAAAA,EAAOuP,UAAAA,CAAU,aACtCA,OAAAA,IACIvP,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBACb,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBI,KAAM,OACjD,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WACxC,CAAC,CAAE/B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAMjC,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAChC,CAAC,CAAEzC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAKhD,CAAC,CAAEzC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAsB,EAiB5EiN,GAAsBC,EAAAA,OAAM9P,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,UAAA,YAAA,GAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAC7C,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WAC1C,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,MAAK,EAazCsP,GAA8CA,CAAC,CAC1DvT,KAAAA,EACAsE,MAAAA,EACAsE,KAAAA,EACA3I,MAAAA,EACAuT,SAAAA,EACAC,QAAAA,EAAU,CAAE,EACZC,YAAAA,EACAC,SAAAA,EAAW,GACXC,UAAAA,CACF,IAAM,CACJ,MAAMC,EAAU,gBAAgB7T,IAC1B8T,EAAW7T,IAAU,IAAMA,IAAU,MAAQA,IAAU6E,OAEvDiP,EAAgBC,GAA8B,CAC7CL,GACHH,EAASxT,EAAMgU,CAAQ,CACzB,EAGIC,EAAgBA,IAAM,CAC1B,OAAQrL,EAAI,CACV,IAAK,OACH,aACGmK,GACC,CAAA,GAAIc,EACJ,KAAK,OACL,MAAO5T,GAAS,GAChB,SAAiB8T,GAAAA,EAAahU,EAAEG,OAAOD,KAAK,EAC5C,YAAAyT,EACA,SAAAC,EACA,UAAWG,CACX,CAAA,EAGN,IAAK,SAED,OAAA1P,MAAC2O,IACC,GAAIc,EACJ,KAAK,SACL,MAAO5T,GAAS,GAChB,SAAWF,GAAMgU,EAAazE,WAAWvP,EAAEG,OAAOD,KAAK,GAAK,CAAC,EAC7D,YAAAyT,EACA,SAAAC,EACA,UAAWG,CACX,CAAA,EAGN,IAAK,OACH,aACGf,GACC,CAAA,GAAIc,EACJ,KAAK,OACL,MAAO5T,GAAS,GAChB,SAAWF,GAAMgU,EAAahU,EAAEG,OAAOD,KAAK,EAC5C,SAAA0T,EACA,UAAWG,CACX,CAAA,EAGN,IAAK,SACH,cACGZ,GACC,CAAA,GAAIW,EACJ,MAAO5T,GAAS,GAChB,SAAiB8T,GAAAA,EAAahU,EAAEG,OAAOD,KAAK,EAC5C,SAAA0T,EACA,UAAWG,EAEX,SAAA,CAAC1P,EAAA,IAAAgP,GAAA,CAAa,MAAM,GAAG,SAAG,MAAA,EACzBK,EAAQ3R,IACPuR,GAAAjP,EAAAA,IAACgP,GAAgC,CAAA,MAAOC,EAAOpT,MAC5CoT,SAAO/O,EAAAA,KAAAA,EADS+O,EAAOpT,KAE1B,CACD,CACH,CAAA,CAAA,EAGJ,QACS,OAAA,IACX,CAAA,EAIA,OAAAqG,OAACuM,IAAY,UAAAe,EACX,SAAA,CAACxP,EAAA,IAAA0O,GAAA,CAAY,QAASe,EACnBvP,SACHA,EAAA,EACC2P,EAAc,CACjB,CAAA,CAAA,CAEJ,ECnMaC,GAAiBA,CAAC,CAC7BC,eAAAA,EAAiB,CAAC,EAClBC,gBAAAA,EACAC,QAAAA,CACmB,EAAI,KAA6B,CACpD,KAAM,CAACxV,EAASC,CAAU,EAAInB,WAAsBwW,CAAc,EAK5DG,EAAe9H,EAAAA,YACnB,CAACxM,EAAcC,IAA2B,CACxCnB,EAAmBd,GAAA,CACjB,MAAMuW,EAAa,CACjB,GAAGvW,EACH,CAACgC,CAAI,EAAGC,CAAAA,EAIV,OAAImU,GACFA,EAAgBG,CAAU,EAGrBA,CAAAA,CACR,CAAA,EAEH,CAACH,CAAe,CAClB,EAKMI,EAAgBhI,cACnB+H,GAAqC,CACpCzV,EAAmBd,GAAA,CACjB,MAAMyW,EAA8B,CAClC,GAAGzW,EACH,GAAG6T,OAAO6C,YACR7C,OAAOvD,QAAQiG,CAAU,EACtBjU,OAAO,CAAC,CAAGL,CAAAA,CAAK,IAAMA,IAAU6E,MAAS,EACzChD,IAAI,CAAC,CAAC6Q,EAAK1S,CAAK,IAAM,CAAC0S,EAAK1S,CAAM,CAAC,CACxC,CAAA,EAIF,OAAImU,GACFA,EAAgBK,CAAc,EAGzBA,CAAAA,CACR,CAAA,EAEH,CAACL,CAAe,CAClB,EAKMjU,EAAeqM,EAAAA,YAAY,IAAM,CACrC1N,EAAWqV,CAAc,EAGrBE,GACMA,IAEND,GACFA,EAAgBD,CAAc,CAE/B,EAAA,CAACA,EAAgBE,EAASD,CAAe,CAAC,EAKvCO,EAAmBtU,EAAAA,QAAQ,IACxBwR,OAAO7D,OAAOnP,CAAO,EAAE8P,KACnB1O,GAAAA,IAAU,IAAMA,IAAU,MAAQA,IAAU6E,MACvD,EACC,CAACjG,CAAO,CAAC,EAKN+V,EAAoBvU,EAAAA,QAAQ,IACzBwR,OAAO7D,OAAOnP,CAAO,EAAEyB,OAC5BL,GAASA,IAAU,IAAMA,IAAU,MAAQA,IAAU6E,MACvD,EAAEoD,OACD,CAACrJ,CAAO,CAAC,EAKNgW,EAAiBrI,cACpBxM,GACQnB,EAAQmB,CAAI,GAAK,GAE1B,CAACnB,CAAO,CACV,EAKMiW,EAAiBtI,cACpBxM,GAA0B,CACnBC,MAAAA,EAAQpB,EAAQmB,CAAI,EAC1B,OAAOC,IAAU,IAAMA,IAAU,MAAQA,IAAU6E,MAAAA,EAErD,CAACjG,CAAO,CACV,EAEO,MAAA,CACLA,QAAAA,EACAyV,aAAAA,EACAE,cAAAA,EACArU,aAAAA,EACAwU,iBAAAA,EACAC,kBAAAA,EACAC,eAAAA,EACAC,eAAAA,CAAAA,CAEJ,EC/IMC,EAAwBA,CAACrF,EAAc,EAAGD,EAAc,KACrD1N,MAAMC,KAAK,CAAEkG,OAAQuH,EAAMC,EAAM,CAAA,EAAK,CAACvH,EAAG8E,KAAO,CACtDhN,MAAOyP,EAAMzC,EACb3I,OAAQoL,EAAMzC,GAAG+H,SAAS,CAC1B,EAAA,EAMSC,GAA2C,CAEtD,CACEjV,KAAM,SACNsE,MAAO,SACPsE,KAAM,OACN8K,YAAa,mBACbwB,MAAO,QACPC,MAAO,CACT,EACA,CACEnV,KAAM,YACNsE,MAAO,YACPsE,KAAM,SACN6K,QAAS,CACP,CAAExT,MAAO,OAAQqE,MAAO,MAAA,EACxB,CAAErE,MAAO,QAASqE,MAAO,OAAA,CAAS,EAEpC4Q,MAAO,QACPC,MAAO,CACT,EACA,CACEnV,KAAM,SACNsE,MAAO,SACPsE,KAAM,SACN6K,QAAS,CACP,CAAExT,MAAO,MAAOqE,MAAO,MAAA,EACvB,CAAErE,MAAO,OAAQqE,MAAO,QAAA,CAAU,EAEpC4Q,MAAO,QACPC,MAAO,CACT,EAGA,CACEnV,KAAM,WACNsE,MAAO,YACPsE,KAAM,OACNsM,MAAO,QACPC,MAAO,CACT,EACA,CACEnV,KAAM,SACNsE,MAAO,UACPsE,KAAM,OACNsM,MAAO,QACPC,MAAO,CACT,EAGA,CACEnV,KAAM,QACNsE,MAAO,QACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,UACPC,MAAO,CACT,EACA,CACEnV,KAAM,YACNsE,MAAO,aACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,UACPC,MAAO,CACT,EACA,CACEnV,KAAM,mBACNsE,MAAO,gBACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,UACPC,MAAO,CACT,EACA,CACEnV,KAAM,qBACNsE,MAAO,kBACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,UACPC,MAAO,CACT,EACA,CACEnV,KAAM,iBACNsE,MAAO,kBACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,UACPC,MAAO,EACT,EAGA,CACEnV,KAAM,oBACNsE,MAAO,sBACPsE,KAAM,SACN6K,QAASsB,EAAsB,EAAG,EAAE,EACpCG,MAAO,WACPC,MAAO,EACT,EACA,CACEnV,KAAM,oBACNsE,MAAO,sBACPsE,KAAM,SACN6K,QAASsB,EAAsB,EAAG,EAAE,EACpCG,MAAO,WACPC,MAAO,EACT,EACA,CACEnV,KAAM,UACNsE,MAAO,WACPsE,KAAM,SACN6K,QAAS,CAAE,EACXyB,MAAO,WACPC,MAAO,EACT,EACA,CACEnV,KAAM,sBACNsE,MAAO,wBACPsE,KAAM,SACN6K,QAASsB,EAAsB,EAAG,EAAE,EACpCG,MAAO,WACPC,MAAO,EACT,EACA,CACEnV,KAAM,sBACNsE,MAAO,wBACPsE,KAAM,SACN6K,QAASsB,EAAsB,EAAG,EAAE,EACpCG,MAAO,WACPC,MAAO,EACT,CAAC,EAMUC,GAA0BF,GAC9BD,GACJ3U,OAAgBsS,GAAAA,EAAMsC,QAAUA,CAAK,EACrC9W,KAAK,CAACC,EAAGC,IAAMD,EAAE8W,MAAQ7W,EAAE6W,KAAK,EAMxBE,GAAkBA,IACtB,CAAC,QAAS,QAAS,UAAW,UAAU,EA2BpCC,GAAkE,CAC7EC,MAAO,gBACPC,MAAO,aACPC,QAAS,gBACTjV,SAAU,oBACZ,EAKakV,GAAwE,CACnFH,MAAO,iDACPC,MAAO,8BACPC,QAAS,0CACTjV,SAAU,4CACZ,EAKamV,GACXC,GASOX,GAAoBnT,IAAa8Q,GAAA,CACtC,OAAQA,EAAM5S,KAAI,CAChB,IAAK,QACI,MAAA,CACL,GAAG4S,EACHa,QAASmC,EAAWhU,aAAaE,IAAc7C,IAAA,CAC7CgB,MAAOhB,EACPqF,MAAOrF,CAAAA,EACP,CAAA,EAEN,IAAK,YACI,MAAA,CACL,GAAG2T,EACHa,QAASmC,EAAW1T,iBAAiBJ,IAAa8G,IAAA,CAChD3I,MAAO2I,EACPtE,MAAOsE,CAAAA,EACP,CAAA,EAEN,IAAK,mBACI,MAAA,CACL,GAAGgK,EACHa,QAASmC,EAAWxT,wBAAwBN,IAAa8G,IAAA,CACvD3I,MAAO2I,EACPtE,MAAOsE,CAAAA,EACP,CAAA,EAEN,IAAK,qBACI,MAAA,CACL,GAAGgK,EACHa,QAASmC,EAAWrT,0BAA0BT,IAAa8G,IAAA,CACzD3I,MAAO2I,EACPtE,MAAOsE,CAAAA,EACP,CAAA,EAEN,IAAK,iBACI,MAAA,CACL,GAAGgK,EACHa,QAASmC,EAAWpT,qBAAqBV,IAAa8G,IAAA,CACpD3I,MAAO2I,EACPtE,MAAOsE,CAAAA,EACP,CAAA,EAEN,IAAK,UACI,MAAA,CACL,GAAGgK,EACHa,QAASmC,EAAWjT,eAAeb,IAAa8G,IAAA,CAC9C3I,MAAO2I,EACPtE,MAAOsE,CAAAA,EACP,CAAA,EAEN,QACSgK,OAAAA,CACX,CAAA,CACD,ECxPGiD,GAAyBvS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,qBAAA,kBAAA,YAAA,+IAAA,0BAAA,EAGzB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAC7B,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAClC,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBAC1C,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,OAC/C,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAczC,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAsB,EAMhE2P,GAAsBxS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIZ,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAGvDgS,GAAqBxQ,EAAAA,GAAEhC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,oEAAA,IAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB4D,KAAM,YAE1C,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WAM1C,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAsB,EAIrE6P,GAAqB1S,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,UAAA,YAAA,IAAA,kBAAA,cAAA,sEAAA,GAAA,EACd,CAAC,CAAEwS,OAAAA,EAAQvS,MAAAA,CAAM,IAC7BuS,SAAAA,OAAAA,EAAS,IACLvS,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBACtB,CAAC,CAAE2C,OAAAA,EAAQvS,MAAAA,CAAM,IACxBuS,SAAAA,OAAAA,EAAS,IACLvS,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,YAC7BxS,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBAC1B,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAAS,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3E,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoByS,OAAQ,UAC/C,CAAC,CAAEzS,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBO,KAAM,WAI/B,CAAC,CAAEgS,OAAAA,EAAQvS,MAAAA,CAAM,IACnCuS,SAAAA,OAAAA,EAAS,IACLvS,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAGjDgJ,GAAqBvP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAG7CqS,GAAqB9S,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,mBAAA,4BAAA,GAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OACzB,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAGrFwM,GAAoBC,EAAAA,GAAE/S,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,0DAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBK,KAAM,QAE1C,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,UAAS,EAM1D8Q,GAA0B/O,EAAAA,EAACjE,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,8BAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBO,KAAM,WAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAK1E4S,GAAmBlT,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6EAAA,uDAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAO7C0S,GAAmBnT,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6CAAA,gBAAA,yBAAA,GAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC5B,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC3B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAGlF7D,GAAsB0Q,EAAAA,OAAMnT,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,qBAAA,kBAAA,YAAA,IAAA,cAAA,8IAAA,0EAAA,EAClB,CAAC,CAAEkT,SAAAA,EAAUjT,MAAAA,CAAM,IAC/BiT,OAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBACzB,eACG,CAAC,CAAEwQ,SAAAA,EAAUjT,MAAAA,CAAM,IAC1BiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,YAC7BxS,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBACjB,CAAC,CAAE+S,SAAAA,EAAUjT,MAAAA,CAAM,IACrCiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBACb,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBK,KAAM,OAC/C,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAAS,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC/E,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAQnC,CAAC,CAAE6S,SAAAA,EAAUjT,MAAAA,CAAM,IAC/BiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAc0C,cAAe,wBAC7B1C,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,sBAAqB,EAoB3CsD,GAA8CA,CAAC,CAC1DzC,eAAAA,EAAiB,CAAC,EAClBC,gBAAAA,EACAC,QAAAA,EACAuB,WAAAA,EAAa,CACXhU,aAAc,CAAE,EAChBM,iBAAkB,CAAE,EACpBE,wBAAyB,CAAE,EAC3BG,0BAA2B,CAAE,EAC7BC,qBAAsB,CAAE,EACxBG,eAAgB,CAAA,CAClB,EACAgR,SAAAA,EAAW,GACXC,UAAAA,CACF,IAAM,CACE,KAAA,CACJ/U,QAAAA,EACAyV,aAAAA,EACAnU,aAAAA,EACAwU,iBAAAA,EACAC,kBAAAA,GACEV,GAAe,CACjBC,eAAAA,EACAC,gBAAAA,EACAC,QAAAA,CAAAA,CACD,EAGKwC,EAAelB,GAA0BC,CAAU,EACnDkB,EAAezB,KAGnB,OAAA/O,OAACuP,IAAgB,UAAAjC,EAEf,SAAA,CAAAtN,OAACwP,GACC,CAAA,SAAA,CAAAxP,OAACyP,GAAW,CAAA,SAAA,CAAA,OACN3R,EAAAA,IAAC,QAAK,SAAO,SAAA,CAAA,CAAA,EACnB,EACCA,EAAA,IAAA4R,GAAA,CAAY,OAAQpB,EAClBA,SACHA,EAAA,CAAA,EACF,EAGCkC,EAAahV,IAAiBiV,GAAA,CAE7B,MAAMC,EADc5B,GAAuB2B,CAAS,EACfjV,IAAI8Q,GACvCiE,EAAanI,KAAKuI,GAAUA,EAAOjX,OAAS4S,EAAM5S,IAAI,CACxD,EAAEM,OAAO4P,OAAO,EAEhB,OAAI8G,EAAiB9O,SAAW,EAAU,YAGvC2K,GACC,CAAA,SAAA,CAAAvM,OAAC8P,GACC,CAAA,SAAA,CAAChS,EAAA,IAAAiS,GAAA,CAAYf,SAAoByB,GAAAA,CAAS,CAAE,CAAA,EAC3C3S,EAAA,IAAAmS,GAAA,CAAkBb,SAA0BqB,GAAAA,CAAS,CAAE,CAAA,CAAA,EAC1D,EAEC3S,EAAA,IAAAoS,GAAA,CACEQ,SAAiBlV,EAAAA,OACfsC,MAAAmP,GAAA,CAEC,KAAMX,EAAO5S,KACb,MAAO4S,EAAOtO,MACd,KAAMsO,EAAOhK,KACb,MAAO/J,EAAQ+T,EAAO5S,IAAI,GAAK,GAC/B,SAAUsU,EACV,QAAS1B,EAAOa,QAChB,YAAab,EAAOc,YACpB,SAAAC,CAAA,EARKf,EAAO5S,KAUf,EACH,CAAA,CAAA,EApBgB+W,CAqBlB,CAAA,CAEH,EAGA3S,EAAA,IAAAqS,GAAA,CACC,SAACrS,EAAA,IAAA4B,GAAA,CACC,SAAS,YACT,QAAS7F,EACT,SAAUwT,GAAY,CAACgB,EAAiB,wBAG1C,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,ECxQMuC,GAA0DA,CAAC,CAC/DrY,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAyB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CACF,IAAM,CAEEiT,MAAAA,EAAavV,EAAAA,QACjB,KAAO,CACLuB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CAAAA,GAEF,CACEf,EACAM,EACAE,EACAG,EACAC,EACAG,CAAc,CAElB,EAGMwU,EAAuB5C,GAAgD,CAEpEjG,OAAAA,QAAQiG,CAAU,EAAErG,QAAQ,CAAC,CAAClO,EAAMC,CAAK,IAAM,CAChDpB,GAAAA,EAAQmB,CAAyB,IAAMC,EAAO,CAChD,MAAMmX,EAAiB,CACrBlX,OAAQ,CAAEF,KAAAA,EAAMC,MAAOoX,OAAOpX,CAAK,CAAE,CAAA,EAEvCH,EAAmBsX,CAAc,EACnC,CACD,CAAA,EAID,OAAAhT,MAACwS,IACC,eAAgB/X,EAChB,gBAAiBsY,EACjB,QAAShX,EACT,WAAAyV,CACA,CAAA,CAEN,EC5EM0B,GAAwBhU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,GAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,OAAO2P,QAC/B,CAAC,CAAE5P,MAAAA,CAAM,IAAMA,EAAMgB,aAAaX,GACxC,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMG,QAAQyD,GAC1B,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAM6T,QAAQzT,EAAE,EAGzCwB,GAAsBkS,EAAAA,GAAEjU,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAM8B,UAAU8B,GAEnC,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,EAAMC,OAAO8B,YACvB,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAGzC0T,GAAsBnU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,SAAA,YAAA,qBAAA,kBAAA,kBAAA,GAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,OAAO8E,OAC1B,CAAC,CAAE/E,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,GACpB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,OAAOyB,WAC/B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMgB,aAAaZ,GAClC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMG,QAAQE,EAAE,EAsB5C2T,GAA0DA,CAAC,CAC/D5Z,MAAAA,EACA6Z,YAAAA,EACAvX,eAAAA,EACAvC,UAAAA,EACAgB,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAyB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CACF,IAGO7E,EAAAA,KAAAA,WAAAA,CAAAA,SAAAA,CAASA,GAAAsG,EAAAA,IAACqT,IAAc3Z,SAAMA,CAAA,CAAA,SAE9BwZ,GACC,CAAA,SAAA,CAAAlT,EAAAA,IAACkB,IAAa,SAAa,eAAA,CAAA,EAE1BqS,GACEvT,EAAAA,IAAA8S,GAAA,CACC,QAAArY,EACA,mBAAAiB,EACA,aAAAK,EACA,aAAAyB,EACA,iBAAAM,EACA,wBAAAE,EACA,0BAAAG,EACA,qBAAAC,EACA,eAAAG,CAEH,CAAA,QAEAoG,GAAU,CAAA,OAAQ3I,EAAuB,UAAAvC,EAAsB,WAAY,GAAK,CAAA,EACnF,CACF,CAAA,CAAA,ECjDE+Z,GAAyBtU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAGzB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAC1B,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,OAAM,EAGvDyQ,GAAkBvU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,uCAAA,kDAAA,kBAAA,+IAAA,0BAAA,EAId,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAG3C,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAGxB,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBAC1C,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,OAcpD,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAsB,EAMhE2R,GAAiBC,EAAAA,GAAExU,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,oIAAA,oBAAA,EACV,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB8T,KAAM,UAE1C,CAAC,CAAE9T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WAO1C,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAsB,EAKrE6R,GAA0B1U,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,UAAA,0EAAA,YAAA,IAAA,kBAAA,qBAAA,eAAA,0CAAA,EAG1B,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAClC,CAAC,CAAEgU,SAAAA,EAAUvU,MAAAA,CAAM,aAC1BuU,OAAAA,IAAWvU,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,yBAAyB9E,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBAIjF,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YACxC,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAAS,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3E,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoByS,OAAQ,UAExD,CAAC,CAAE8B,SAAAA,EAAUvU,MAAAA,CAAM,aACnBuU,OAAAA,IAAWvU,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,yBAAyB9E,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBAC3E,CAAC,CAAEoO,SAAAA,EAAUvU,MAAAA,CAAM,WAC/BuU,OAAAA,EAAW,KAAGvU,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,2BAA6B,cAAa,EAQ/E0P,GAAmB5U,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gFAAA,4BAAA,GAAA,EAIR,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC3B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAGrFsO,GAAsB7U,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGtB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAG7CqU,GAAkBZ,EAAAA,GAAEjU,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,mBAAA,mBAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB2U,MAAO,YAE3C,CAAC,CAAE3U,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,UAAS,EAI1D6S,GAAwBhV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAG7CwU,GAAmB1S,EAAAA,KAAItC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,YAAA,YAAA,IAAA,kBAAA,cAAA,qCAAA,mDAAA,EACb,CAAC,CAAEkT,SAAAA,EAAUjT,MAAAA,CAAM,IAAM,aACrC,OAAQiT,EAAQ,CACd,IAAK,QACIjT,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,sBAClC,IAAK,WACI5P,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAClC,IAAK,SACIzC,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,uBAClC,QACS9E,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,qBACpC,CACF,EACS,CAAC,CAAEqD,SAAAA,EAAUjT,MAAAA,CAAM,IAAM,aAChC,OAAQiT,EAAQ,CACd,IAAK,QACIjT,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,UACtC,IAAK,WACI/B,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAClC,IAAK,SACIzC,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,uBAClC,QACS9E,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,SACxC,CACF,EACW,CAAC,CAAE/B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeiF,MAAO,OAC5C,CAAC,CAAEjF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OACvB,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoByS,OAAQ,UAC/C,CAAC,CAAEzS,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBO,KAAM,WAG/C,CAAC,CAAE0S,SAAAA,EAAUjT,MAAAA,CAAM,IAAM,aACzB,OAAQiT,EAAQ,CACd,IAAK,QACIjT,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBACjC,IAAK,WACInG,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAClC,IAAK,SACIzC,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,uBAClC,QACS9E,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,uBACnC,CACF,CAAC,EAKC2O,GAA0BlV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAG1B,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAG7CiC,GAAsB0Q,EAAAA,OAAMnT,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,qBAAA,kBAAA,YAAA,IAAA,cAAA,uEAAA,sJAAA,oDAAA,gGAAA,EAClB,CAAC,CAAEkT,SAAAA,EAAUjT,MAAAA,CAAM,IAC/BiT,OAAAA,OAAAA,IAAa,YAAYjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAAyB,eACpE,CAAC,CAAEwQ,SAAAA,EAAUjT,MAAAA,CAAM,IAC1BiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,YAC7BxS,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBAEjC,CAAC,CAAE+S,SAAAA,EAAUjT,MAAAA,CAAM,IACnBiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBACf,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBK,KAAM,OAC/C,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC3B,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAK5C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAQ3B,CAAC,CAAE0S,SAAAA,EAAUjT,MAAAA,CAAM,IAC/BiT,SAAAA,OAAAA,IAAa,YACTjT,EAAAA,EAAMC,SAAND,YAAAA,EAAc0C,cAAe,wBAC7B1C,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAG3B,CAAC,CAAEqD,SAAAA,EAAUjT,MAAAA,CAAM,IAAA,OACnBiT,OAAAA,IAAa,UAAY,KAAGjT,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,2BAA6B,qBAAoB,EAaxGsS,GAAexS,EAAOC,EAAI,EAAC3C,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,qBAAA,kBAAA,YAAA,IAAA,cAAA,6EAAA,uIAAA,oDAAA,wCAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,wBAC7C,CAAC,CAAE9E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,WACjC,CAAC,CAAExS,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,wBAC3C,CAAC,CAAE9E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBK,KAAM,OAC/C,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC3B,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAK5C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAQ3B,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcgV,cAAe,wBAElC,CAAC,CAAEhV,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,uBAAsB,EASpFmQ,GAAsBrV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uJAAA,GAAA,EAWlB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,OAAM,EAGjDsR,GAAsBtV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,gFAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAClC,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBAC1C,CAAC,CAAEnG,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,MAAK,EAQ3DuR,GAAqBvV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,yEAAA,4BAAA,GAAA,EAIjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QACpB,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAGrFiP,GAAoBtB,EAAAA,GAAEjU,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kBAAA,cAAA,mBAAA,EAEjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WACxC,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB0D,KAAM,UAAS,EAIxD2R,GAAqBrC,EAAAA,OAAMnT,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qCAAA,mFAAA,UAAA,IAAA,EAGtB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBAOvC,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAC7C,CAAC,CAAE5P,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,UAAS,EAcrDuT,GAAkDA,CAAC,CAC9DpF,UAAAA,EACA/V,UAAAA,EAAY,GACZob,aAAAA,EAAe,GACfC,WAAAA,EAAa,EACbC,cAAAA,EACAxE,iBAAAA,EAAmB,GACnByE,UAAAA,EACAC,SAAAA,EACAC,SAAAA,EACAC,MAAAA,EAAQ,eACV,IAAM,CACJ,KAAM,CAACC,EAAiBC,CAAkB,EAAI9b,WAAS,EAAK,EACtD+b,EAAUR,EAAa,EACvBS,EACJhF,GAAoBwE,IAAkBrU,QAAaqU,IAAkBD,EAEjEU,EAAoBA,IAAM,CAC9BH,EAAmB,EAAI,EACZH,GAAA,MAAAA,GAAA,EAGPO,EAAuBA,IAAM,CACjCJ,EAAmB,EAAK,EAEZL,GAAA,MAAAA,GAAA,EAIZ,OAAA9S,OAACsR,IAAgB,UAAAhE,EAEf,SAAA,CAAAtN,OAACuR,GACC,CAAA,SAAA,CAAAvR,OAACwR,GAAO,CAAA,SAAA,CAAA,eACM1T,EAAAA,IAAC,QAAK,SAAO,SAAA,CAAA,CAAA,EAC3B,QACC4T,GAAiB,CAAA,SAAU0B,EACzBA,SAAUA,EAAA,GAAGR,WAAsB,WACtC,CAAA,CAAA,EACF,SAGChB,GACC,CAAA,SAAA,CAAA5R,OAAC6R,GACC,CAAA,SAAA,CAAA/T,EAAAA,IAACgU,IAAUmB,SAAMA,CAAA,CAAA,SAChBjB,GACEoB,CAAAA,SAAAA,CACCA,GAAApT,EAAA,KAACiS,GAAU,CAAA,SAAS,QAAQ,SAAA,CAAA,MAAIW,EAAWY,eAAe,EAAE,QAAA,EAAM,EAEnEH,GACCrT,EAAA,KAACiS,GAAU,CAAA,SAAS,WAAU,SAAA,CAAA,MACxBY,EAAeW,eAAe,EAAE,WAAA,EACtC,EAEDnF,GAAoBvQ,EAAA,IAACmU,GAAU,CAAA,SAAS,SAAS,SAAgB,mBAAA,CAAA,EACpE,CAAA,EACF,SAECC,GACEY,CAAAA,SAAAA,CACCA,GAAA9S,EAAA,KAACN,GACC,CAAA,SAAS,YACT,QAASoT,EACT,SAAUvb,EACV,MAAOA,EAAY,uBAAyB,qBAE3CA,SAAAA,CAAAA,GAAaob,EAAe,IAAM,KAClCpb,EAAY,aAAe,SAAA,EAC9B,EAGFuG,EAAAA,IAAC4B,GACC,CAAA,SAAS,YACT,QAAS4T,EACT,SAAU/b,EACV,MAAM,oCAAmC,SAG3C,WAAA,CAAA,EAECwb,GACCjV,EAAA,IAAC4B,GACC,CAAA,SAAS,YACT,QAASqT,EACT,SAAUxb,GAAa,CAAC6b,EACxB,MAAM,oBAAmB,SAG3B,YAAA,QAGDjB,GAAa,CAAA,GAAG,aAAa,MAAM,gBAAe,SAEnD,cAAA,CAAA,EACF,CAAA,EACF,EAGCe,GACCpV,EAAA,IAACuU,GAAa,CAAA,QAAS,IAAMc,EAAmB,EAAK,EACnD,SAAAnT,EAAAA,KAACsS,GAAa,CAAA,QAAgB7Y,GAAAA,EAAEga,gBAC9B,EAAA,SAAA,CAAAzT,OAACuS,GACC,CAAA,SAAA,CAAAzU,EAAAA,IAAC0U,IAAW,SAA4B,8BAAA,CAAA,QACvCC,GAAY,CAAA,QAAS,IAAMU,EAAmB,EAAK,EAAG,SAAC,IAAA,CAAA,EAC1D,EACArV,EAAAA,IAAC4V,GAAiB,CAAA,iBAAkBH,CAAqB,CAAA,CAAA,CAAA,CAC3D,CACF,CAAA,CAEJ,CAAA,CAAA,CAEJ,EC/ZMI,GAAuB3W,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6BAAA,MAAA,8BAAA,qBAAA,EAGpB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAAY,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,QACnE,CAAC,CAAE1D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAIrFqQ,GAAaxD,EAAAA,OAAMnT,WAAA,CAAAC,YAAA,MAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,6CAAA,WAAA,yCAAA,cAAA,8MAAA,2FAAA,qBAAA,0FAAA,uEAAA,uDAAA,qCAAA,IAAA,cAAA,IAAA,EACZ,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAAU,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAGpF,CAAC,CAAE6S,UAAAA,EAAWzW,MAAAA,CAAM,aAC3ByW,OAAAA,IACIzW,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,YAC7B/B,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBAC3B,CAAC,CAAEwW,UAAAA,CAAU,IAAMA,EAAY,cAAgB,UAE1C,CAAC,CAAED,UAAAA,CAAU,IAAMA,EAAY,MAAQ,MACzC,CAAC,CAAEzW,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBK,KAAM,QAQ5C,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,OAU3B,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAClC,CAAC,CAAEgU,UAAAA,CAAU,IAAMA,EAAY,EAAI,EAO9C,CAAC,CAAEA,UAAAA,EAAWzW,MAAAA,CAAM,aAC3ByW,OAAAA,IACIzW,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,YAC7B/B,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WAKnB,CAAC,CAAE0U,UAAAA,EAAWzW,MAAAA,CAAM,aAChCyW,OAAAA,IACIzW,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,yBACzBzC,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,yBASvC,CAAC,CAAEwW,UAAAA,CAAU,IACbA,GACA;AAAA;AAAA;AAAA,IAOW,CAAC,CAAE1W,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAAS,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC/E,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,WAAU,EAI3DuW,GAAiBxU,EAAAA,KAAItC,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAM1B,EAAA,CAAA,0DAAA,CAAA,EAEK6W,GAAkBzU,EAAAA,KAAItC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAI3B,EAAA,CAAA,yCAAA,CAAA,EAEK8W,EAAkB1U,EAAAA,KAAItC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,kCAAA,cAAA,+EAAA,0CAAA,EACZ,CAAC,CAAEkT,SAAAA,EAAUjT,MAAAA,CAAM,IAAM,aACrC,OAAQiT,EAAQ,CACd,IAAK,QACIjT,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,sBAClC,IAAK,SACI5P,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,uBAClC,IAAK,MACIzC,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc8E,UAAW,uBAClC,QACS9E,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,qBACpC,CACF,EACS,CAAC,CAAEqD,SAAAA,EAAUjT,MAAAA,CAAM,IAAM,aAChC,OAAQiT,EAAQ,CACd,IAAK,QACIjT,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBACxC,IAAK,SACIF,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,UACtC,IAAK,MACIxS,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcwS,cAAe,UACtC,QACSxS,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,uBAC1C,CACF,EAEiB,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoByS,OAAQ,UAC/C,CAAC,CAAEzS,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBO,KAAM,WAKpC,CAAC,CAAEP,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,MAAK,EAUpDuW,GAAuF,CAC3F/I,IAAK,CACHgJ,KAAM,KACNnW,MAAO,aACPoW,YAAa,4CACf,EACAC,OAAQ,CACNF,KAAM,IACNnW,MAAO,SACPoW,YAAa,mCACf,EACA7b,QAAS,CACP4b,KAAM,KACNnW,MAAO,UACPoW,YAAa,uCACf,EACA1P,MAAO,CACLyP,KAAM,KACNnW,MAAO,aACPoW,YAAa,mCACf,CACF,EAaaE,GAA8CA,CAAC,CAC1DC,UAAAA,EACAC,YAAAA,EACAnH,SAAAA,EAAW,GACXoH,YAAAA,EACApG,iBAAAA,EAAmB,GACnBf,UAAAA,CACF,IAAM,CACEoH,MAAAA,EAAkBC,GAAoB,CACrCtH,GACHmH,EAAYG,CAAG,CACjB,EAGIC,EAAgBA,CAACzO,EAA4BwO,IAAoB,EAChExO,EAAMkG,MAAQ,SAAWlG,EAAMkG,MAAQ,MAAQ,CAACgB,IACnDlH,EAAM0O,eAAe,EACrBL,EAAYG,CAAG,EACjB,EAGIG,EAAkBH,GAAoB,CAC1C,GAAI,CAACF,EAAoB,OAAA,KAEzB,OAAQE,EAAG,CACT,IAAK,MACIF,OAAAA,EAAYM,MAAQ,EACzBjX,EAAAA,IAACmW,GAAS,SAAS,QAASQ,SAAYM,EAAAA,KAAM,CAAA,EAC5C,KACN,IAAK,SACIN,OAAAA,EAAYJ,OAAS,EAC1BvW,EAAAA,IAACmW,GAAS,SAAS,MAAOQ,SAAYJ,EAAAA,MAAO,CAAA,EAC3C,KACN,IAAK,UACH,OAAOhG,EACJvQ,MAAAmW,EAAA,CAAS,SAAS,SAAS,cAAE,EAC5B,KACN,IAAK,QACIQ,OAAAA,EAAYM,MAAQ,EACzBjX,EAAA,IAACmW,GAAS,SAAS,QAAQ,aAAE,CAAA,EAC3B,KACN,QACS,OAAA,IACX,CAAA,EAIA,OAAAnW,EAAA,IAAC6V,GAAc,CAAA,UAAArG,EAAsB,KAAK,UACtC/B,gBAAOC,KAAK0I,EAAU,EAAmB1Y,IAAamZ,GAAA,CAChDhE,MAAAA,EAASuD,GAAWS,CAAG,EACvBK,EAAWT,IAAcI,EACzBM,EAAQH,EAAeH,CAAG,EAEhC,OACG3U,EAAAA,KAAA4T,GAAA,CAEC,UAAWoB,EACX,UAAW3H,EACX,QAAS,IAAMqH,EAAeC,CAAG,EACjC,aAAkBC,EAAcnb,EAAGkb,CAAG,EACtC,SAAAtH,EACA,KAAK,MACL,gBAAe2H,EACf,gBAAe,iBAAiBL,IAChC,SAAUtH,EAAW,GAAK,EAC1B,MAAOsD,EAAOyD,YAEd,SAAA,CAACtW,EAAAA,IAAAiW,GAAA,CAASpD,WAAOwD,IAAK,CAAA,EACtBrW,EAAAA,IAACkW,GAAUrD,CAAAA,SAAAA,EAAO3S,KAAM,CAAA,EACvBiX,CAAAA,CAAAA,EAdIN,CAeP,CAEH,CAAA,CACH,CAAA,CAEJ,EC1OMO,EAA+B,CAAC,MAAO,SAAU,UAAW,OAAO,EAKnEC,GAAsB,4CAKtBC,GAAqBA,CAACC,EAAoBC,IAAuC,CACjF,GAAA,CACIC,MAAAA,EAASC,aAAaC,QAAQJ,CAAU,EAC9C,GAAIE,GAAUL,EAAe7a,SAASkb,CAAoB,EACjDA,OAAAA,QAEF/d,GACCke,QAAAA,KAAK,gDAAiDle,CAAK,CACrE,CACO8d,OAAAA,CACT,EAKMK,GAAmBA,CAACN,EAAoBV,IAA0B,CAClE,GAAA,CACWiB,aAAAA,QAAQP,EAAYV,CAAG,QAC7Bnd,GACCke,QAAAA,KAAK,8CAA+Cle,CAAK,CACnE,CACF,EAOaqe,GAAuBA,CAAC,CACnCP,WAAAA,EAAa,MACbD,WAAAA,EAAaF,EACY,EAAI,KAAmC,CAG1D,KAAA,CAACZ,EAAWuB,CAAiB,EAAIze,EAAAA,SAAqB,IAC1D+d,GAAmBC,EAAYC,CAAU,CAC3C,EAGM,CAACjE,EAAa0E,CAAc,EAAI1e,WAAkB,EAAK,EAKvD2e,EAAe9P,cAAayO,GAAoB,CAChDO,EAAe7a,SAASsa,CAAG,IAC7BmB,EAAkBnB,CAAG,EACrBgB,GAAiBN,EAAYV,CAAG,EAG5BA,IAAQ,WACVoB,EAAe,EAAI,EAEvB,EACC,CAACV,CAAU,CAAC,EAKTY,EAAU/P,EAAAA,YAAY,IAAM,CAE1BgQ,MAAAA,GADehB,EAAeiB,QAAQ5B,CAAS,EACnB,GAAKW,EAAetT,OACzCsT,EAAAA,EAAegB,CAAS,CAAC,CAAA,EACrC,CAAC3B,EAAWyB,CAAY,CAAC,EAKtBI,EAAclQ,EAAAA,YAAY,IAAM,CAC9BmQ,MAAAA,EAAenB,EAAeiB,QAAQ5B,CAAS,EAC/C+B,EAAgBD,IAAiB,EAAInB,EAAetT,OAAS,EAAIyU,EAAe,EACzEnB,EAAAA,EAAeoB,CAAa,CAAC,CAAA,EACzC,CAAC/B,EAAWyB,CAAY,CAAC,EAKtBO,EAAcrQ,cAAayO,GACxBJ,IAAcI,EACpB,CAACJ,CAAS,CAAC,EAKRiC,EAActQ,cAAayO,GACxBO,EAAeiB,QAAQxB,CAAG,EAChC,CAAE,CAAA,EAGLld,OAAAA,EAAAA,UAAU,IAAM,CACRmd,MAAAA,EAAiBzO,GAAyB,eAE1C6F,GAAAA,IAAAA,EAAAA,SAASyK,gBAATzK,YAAAA,EAAwB0K,WAAY,WACpC1K,EAAAA,SAASyK,gBAATzK,YAAAA,EAAwB0K,WAAY,cACpC1K,EAAAA,SAASyK,gBAATzK,YAAAA,EAAwB0K,WAAY,UAKxC,KAAKvQ,EAAMwQ,SAAWxQ,EAAMyQ,UAAY,CAACzQ,EAAM0Q,SAC7C,OAAQ1Q,EAAMkG,IAAG,CACf,IAAK,YACHlG,EAAM0O,eAAe,EACTuB,IACZ,MACF,IAAK,aACHjQ,EAAM0O,eAAe,EACboB,IACR,KACJ,CAIE9P,GAAAA,EAAMkG,KAAO,KAAOlG,EAAMkG,KAAO,KAAO,CAAClG,EAAMwQ,SAAW,CAACxQ,EAAMyQ,QAAS,CAC5E,MAAME,EAAW5b,SAASiL,EAAMkG,GAAG,EAAI,EACnCyK,EAAW5B,EAAetT,SAC5BuE,EAAM0O,eAAe,EACRK,EAAAA,EAAe4B,CAAQ,CAAC,GAKzC,GAAI3Q,EAAM4Q,QAAU,CAAC5Q,EAAMwQ,SAAW,CAACxQ,EAAMyQ,QACnCzQ,OAAAA,EAAMkG,IAAIjS,YAAa,EAAA,CAC7B,IAAK,IACH+L,EAAM0O,eAAe,EACrBmB,EAAa,KAAK,EAClB,MACF,IAAK,IACH7P,EAAM0O,eAAe,EACrBmB,EAAa,QAAQ,EACrB,MACF,IAAK,IACH7P,EAAM0O,eAAe,EACrBmB,EAAa,SAAS,EACtB,MACF,IAAK,IACH7P,EAAM0O,eAAe,EACrBmB,EAAa,OAAO,EACpB,KACJ,CAIE7P,EAAMkG,IAAIjS,YAAY,IAAM,KAAO,CAAC+L,EAAMwQ,SAAW,CAACxQ,EAAMyQ,SAAW,CAACzQ,EAAM4Q,UAE5E/K,EAAAA,SAASyK,gBAATzK,YAAAA,EAAwB0K,WAAY,WACpC1K,EAAAA,SAASyK,gBAATzK,YAAAA,EAAwB0K,WAAY,aACtCvQ,EAAM0O,eAAe,EACjBN,IAAc,UAChBwB,EAAe,CAAC1E,CAAW,EAE3B2E,EAAa,SAAS,GAG5B,EAGKgB,cAAAA,iBAAiB,UAAWpC,CAAa,EACzC,IAAMqC,OAAOC,oBAAoB,UAAWtC,CAAa,CAAA,EAC/D,CAACqB,EAASG,EAAaJ,EAAczB,EAAWlD,CAAW,CAAC,EAExD,CACLkD,UAAAA,EACAyB,aAAAA,EACAC,QAAAA,EACAG,YAAAA,EACAG,YAAAA,EACAC,YAAAA,EACAW,cAAejC,EACf7D,YAAAA,EACA0E,eAAAA,CAAAA,CAEJ,EC1KMqB,EAAoBpa,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,kBAAA,qBAAA,GAAA,EAKhB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,QAGjC,CAAC,CAAE1D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBACrC,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,OACtC,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,wBAAuB,EAG9E8T,EAAmBra,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,eAAA,EAET,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAIvDsD,EAAoB9B,EAAAA,GAAEhC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB4D,KAAM,YAE1C,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WACvC,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,MAAK,EAGnD8Z,EAAsBpW,EAAAA,EAACjE,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAC1C,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAK1E0U,GAAwBhV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,kBAAA,GAAA,EAGxB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAC1B,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,OAAM,EAGvDyW,EAAkBva,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,YAAA,qBAAA,EACX,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc4P,UAAW,uBAClC,CAAC,CAAE5P,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcmG,SAAU,yBAC1C,CAAC,CAAEnG,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,OAC/C,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,OAAM,EAIjDwW,GAAmBxa,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,kBAAA,GAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB2U,MAAO,QAE3C,CAAC,CAAE3U,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAChC,CAAC,CAAEzC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeO,KAAM,MAAK,EAGtD8Z,GAAmBza,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,mDAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAC1C,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAQ1Eoa,GAAwDA,CAAC,CAC7DC,KAAAA,EACApgB,UAAAA,EACAC,MAAAA,EACA6Z,YAAAA,EACAuG,SAAAA,CACF,IACMpgB,SAEC4f,EACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,GAAU,SAAE,IAAA,CAAA,EACbvZ,EAAAA,IAACiD,GAAW,SAAoB,sBAAA,CAAA,EAChCjD,EAAAA,IAACwZ,GAAc9f,SAAMA,CAAA,CAAA,CACvB,CAAA,CAAA,EAIA,CAACD,GAAaogB,EAAKrgB,OAAOsK,SAAW,SAEpCwV,EACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,GAAU,SAAE,IAAA,CAAA,EACbvZ,EAAAA,IAACiD,GAAW,SAAe,iBAAA,CAAA,EAC3BjD,EAAAA,IAACwZ,GAAa,SAA+D,iEAAA,CAAA,CAC/E,CAAA,CAAA,EAKDxZ,EAAAA,IAAAsT,GAAA,CACC,MAAA5Z,EACA,YAAA6Z,EACA,eAAgBsG,EAAK7d,eACrB,UAAAvC,EACA,QAASogB,EAAKpf,QACd,mBAAoBqf,EAASpe,mBAC7B,aAAcoe,EAAS/d,aACvB,aAAc8d,EAAKrc,aACnB,iBAAkBqc,EAAK/b,iBACvB,wBAAyB+b,EAAK7b,wBAC9B,0BAA2B6b,EAAK1b,0BAChC,qBAAsB0b,EAAKzb,qBAC3B,eAAgByb,EAAKtb,cACrB,CAAA,EAOAwb,GAA2DA,CAAC,CAAEF,KAAAA,EAAMpgB,UAAAA,CAAU,IAC9E,CAACA,GAAaogB,EAAKG,aAAalW,SAAW,SAE1CwV,EACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,GAAU,SAAC,GAAA,CAAA,EACZvZ,EAAAA,IAACiD,GAAW,SAAgB,kBAAA,CAAA,EAC5BjD,EAAAA,IAACwZ,GAAa,SAAoD,sDAAA,CAAA,CACpE,CAAA,CAAA,EAIIxZ,EAAAA,IAAA2E,GAAA,CAAU,OAAQkV,EAAKG,aAAqB,UAAAvgB,CAAwB,CAAA,EAMxEwgB,GAAsDA,CAAC,CAAEJ,KAAAA,EAAMpgB,UAAAA,EAAWqgB,SAAAA,CAAS,UAEpFxG,GACC,CAAA,MAAO,KACP,YAAa,GACb,eAAgBuG,EAAK7d,eACrB,UAAAvC,EACA,QAASogB,EAAKpf,QACd,mBAAoBqf,EAASpe,mBAC7B,aAAcoe,EAAS/d,aACvB,aAAc8d,EAAKrc,aACnB,iBAAkBqc,EAAK/b,iBACvB,wBAAyB+b,EAAK7b,wBAC9B,0BAA2B6b,EAAK1b,0BAChC,qBAAsB0b,EAAKzb,qBAC3B,eAAgByb,EAAKtb,cACrB,CAAA,EAOA2b,GAAoDA,CAAC,CAAEL,KAAAA,EAAMpgB,UAAAA,CAAU,IAAM,CACjF,GAAI,CAACA,GAAaogB,EAAKrgB,OAAOsK,SAAW,EACvC,cACGwV,EACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,GAAU,SAAE,IAAA,CAAA,EACbvZ,EAAAA,IAACiD,GAAW,SAAuB,yBAAA,CAAA,EACnCjD,EAAAA,IAACwZ,GAAY,SAEb,oEAAA,CAAA,CACF,CAAA,CAAA,EAKEW,MAAAA,EAAcN,EAAKrgB,OAAOsK,OAC1BqI,EAAgB0N,EAAKrgB,OAAO0C,UAAY8P,EAAE5R,MAAM4M,WAAa,KAAK,EAAElD,OACpEyI,EAAU4N,EAAc,GAAMhO,EAAgBgO,EAAe,KAAK/X,QAAQ,CAAC,EAAI,IAC/EgY,EAAWP,EAAKrgB,OAAO6gB,OAAO,CAACC,EAAKtO,IAAMsO,GAAOtO,EAAE5R,MAAMuC,aAAe,GAAI,CAAC,EAEnF,cACG,MACC,CAAA,SAAA,CAAAuF,OAACgS,GACC,CAAA,SAAA,CAAAhS,OAACuX,EACC,CAAA,SAAA,CAAAzZ,EAAAA,IAAC0Z,IAAWS,SAAYA,CAAA,CAAA,EACxBna,EAAAA,IAAC2Z,IAAU,SAAY,cAAA,CAAA,CAAA,EACzB,SACCF,EACC,CAAA,SAAA,CAAAvX,OAACwX,GAAWnN,CAAAA,SAAAA,CAAAA,EAAQ,GAAA,EAAC,EACrBvM,EAAAA,IAAC2Z,IAAU,SAAQ,UAAA,CAAA,CAAA,EACrB,SACCF,EACC,CAAA,SAAA,CAAAvX,OAACwX,GAAU,CAAA,SAAA,CAAA,IAAEU,EAAShY,QAAQ,CAAC,CAAA,EAAE,EACjCpC,EAAAA,IAAC2Z,IAAU,SAAS,WAAA,CAAA,CAAA,EACtB,SACCF,EACC,CAAA,SAAA,CAACzZ,EAAA,IAAA0Z,GAAA,CAAWG,SAAKrc,EAAAA,aAAasG,OAAO,EACrC9D,EAAAA,IAAC2Z,IAAU,SAAa,eAAA,CAAA,CAAA,EAC1B,CAAA,EACF,EAEC3Z,EAAA,IAAA2E,GAAA,CAAU,OAAQkV,EAAKrgB,OAAe,UAAAC,EAAqB,CAC9D,CAAA,CAAA,CAEJ,EAKa8gB,GAA2D,CACtElN,IAAK,CACHlN,GAAI,MACJgV,MAAO,aACPmB,YAAa,6CACbD,KAAM,KACNmE,UAAWZ,GACXa,aAAc,GACdC,aAAc,EAChB,EACAnE,OAAQ,CACNpW,GAAI,SACJgV,MAAO,gBACPmB,YAAa,oCACbD,KAAM,IACNmE,UAAWT,GACXU,aAAc,GACdC,aAAc,EAChB,EACAjgB,QAAS,CACP0F,GAAI,UACJgV,MAAO,mBACPmB,YAAa,wCACbD,KAAM,KACNmE,UAAWP,GACXQ,aAAc,GACdC,aAAc,EAChB,EACA9T,MAAO,CACLzG,GAAI,QACJgV,MAAO,aACPmB,YAAa,oCACbD,KAAM,KACNmE,UAAWN,GACXO,aAAc,GACdC,aAAc,EAChB,CACF,EAKaC,GAAgBC,GACpBL,GAAmBK,CAAK,EA2BpBC,GAAuEC,GAAA,CAC5E,KAAA,CAAErE,UAAAA,CAAcqE,EAAAA,EAChBjI,EAAS8H,GAAalE,CAAS,EAErC,GAAI,CAAC5D,EACH,cACGyG,EACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,GAAU,SAAC,GAAA,CAAA,EACZvZ,EAAAA,IAACiD,GAAW,SAAW,aAAA,CAAA,SACtBuW,EAAa,CAAA,SAAA,CAAA,QAAM/C,EAAU,cAAA,EAAY,CAC5C,CAAA,CAAA,EAIJ,MAAMsE,EAAelI,EAAO2H,UAE5B,OACGxa,EAAA,IAAA,MAAA,CACC,GAAI,iBAAiByW,IACrB,KAAK,WACL,kBAAiB,eAAeA,IAEhC,SAAAzW,EAAAA,IAAC+a,EAAa,CAAA,GAAID,EAAM,CAC1B,CAAA,CAEJ,ECxUME,GAAmB9b,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,UAAA,6BAAA,kCAAA,EAGnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,QAC7B,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc0B,aAAc,qBAChD,CAAC,CAAE1B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc+B,cAAe,WAE1C,CAAC,CAAE/B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,OAAM,EAKjD+X,GAAqB/b,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,UAAA,EAGrB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,OAAM,EAI7CgY,GAA6Bhc,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAarC,EAAA,CAAA,wIAAA,CAAA,EAEK8b,GAAsBjc,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,sCAAA,EAKlB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,OAAM,EAKjDoY,GAAqBlc,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,mGAAA,EAEX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,OAAM,EAevD0b,GAAqBjY,EAAAA,EAACjE,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,YAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB4D,KAAM,YAC1C,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAI1E8b,GAAoBpc,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,uBAAA,oBAAA,WAAA,KAAA,EAKhB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe0D,KAAM,QAGjC,CAAC,CAAE1D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc5F,QAAS,sBAChC,CAAC,CAAE4F,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc5F,QAAS,sBACzC,CAAC,CAAE4F,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoB4D,KAAM,OAChD,CAAC,CAAE5D,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAe4D,KAAM,OAAM,EAGhDqY,GAAmBrc,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,UAAA,GAAA,EAET,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC5C,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc5F,QAAS,qBAAoB,EAG/D8hB,GAAoBra,EAAAA,GAAEhC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiB4D,KAAM,YAE1C,CAAC,CAAE5D,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc5F,QAAS,sBACjC,CAAC,CAAE4F,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,MAAK,EAGnD2T,GAAsBjQ,EAAAA,EAACjE,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAM8B,YAAN9B,YAAAA,EAAiBI,KAAM,YAC1C,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcE,gBAAiB,wBAAuB,EAK1Eic,GAAqBnJ,EAAAA,OAAMnT,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,eAAA,0CAAA,+EAAA,+BAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QACvC,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeI,KAAM,OAC3C,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,UAANH,YAAAA,EAAeK,KAAM,QAC1B,CAAC,CAAEL,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAcyC,UAAW,wBAGrC,CAAC,CAAEzC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMgB,eAANhB,YAAAA,EAAoBK,KAAM,OAM1C,CAAC,CAAEL,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,SAAND,YAAAA,EAAc0C,cAAe,sBAAqB,EAQ7E0Z,GAA4BA,IAChCxZ,EAAAA,KAACiZ,GACC,CAAA,SAAA,CAAAnb,EAAAA,IAACob,IAAY,SAAE,IAAA,CAAA,EACfpb,EAAAA,IAACqb,IAAY,SAAwB,0BAAA,CAAA,CAAA,CACvC,CAAA,EAMIM,GAAkEA,CAAC,CAAEjiB,MAAAA,EAAOkiB,QAAAA,CAAQ,WACvFN,GACC,CAAA,SAAA,CAAAtb,EAAAA,IAACub,IAAU,SAAE,IAAA,CAAA,EACbvb,EAAAA,IAACwb,IAAW,SAAa,eAAA,CAAA,EACzBxb,EAAAA,IAACqT,IAAc3Z,SAAMA,CAAA,CAAA,EACpBsG,EAAA,IAAAyb,GAAA,CAAY,QAASG,EAAS,SAAS,YAAA,CAAA,CAC1C,CAAA,EAMIC,GAAoDA,CAAC,CAAEC,WAAAA,CAAW,IAAM,CACtE,KAAA,CAAEtiB,OAAAA,EAAQC,UAAAA,EAAWC,MAAAA,EAAOa,cAAAA,GAAkBnB,GAAgB,EAE9D,CACJqB,QAAAA,EACAiB,mBAAAA,EACAK,aAAAA,EACAC,eAAAA,EACAwB,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CAAAA,EACE/D,GAAgBhB,CAAa,EAE3B,CAAEid,UAAAA,EAAWyB,aAAAA,EAAc3E,YAAAA,GAAgBwE,GAAqB,CACpEP,WAAYsE,GAAc,KAAA,CAC3B,EAGK9B,EAAe/d,EAAAA,QAAQ,IAAM,CAC3B8f,MAAAA,MAAmB5hB,KACzB4hB,OAAAA,EAAaC,QAAQD,EAAaE,QAAQ,EAAI,CAAC,EAExCziB,EAAO0C,OAAgB9B,GACV,IAAID,KAAKC,EAAMC,IAAI,GACjB0hB,CACrB,CAAA,EACA,CAACviB,CAAM,CAAC,EAGLmd,GAAc1a,EAAAA,QAClB,KAAO,CACLgb,MAAOzd,EAAOsK,OACdyS,OAAQyD,EAAalW,OACrBP,SAAUvH,EAAe8H,MAAAA,GAE3B,CAACtK,EAAOsK,OAAQkW,EAAalW,OAAQ9H,EAAe8H,MAAM,CAC5D,EAGMyM,EAAmBtU,EAAAA,QAAQ,IACxBwR,OAAO7D,OAAOnP,CAAO,EAAE8P,KACnB1O,GAAAA,IAAU,IAAMA,IAAU,MAAQA,IAAU6E,MACvD,EACC,CAACjG,CAAO,CAAC,EAGNyhB,EAAkB,CACtBzF,UAAAA,EACAoD,KAAM,CACJrgB,OAAAA,EACAwC,eAAAA,EACAge,aAAAA,EACAvf,QAAAA,EACA+C,aAAAA,EACAM,iBAAAA,EACAE,wBAAAA,EACAG,0BAAAA,EACAC,qBAAAA,EACAG,eAAAA,CACF,EACA9E,UAAAA,EACAC,MAAAA,EACA6Z,YAAAA,EACAuG,SAAU,CACRpe,mBAAAA,EACAK,aAAAA,EACAxB,cAAeA,IAAMA,GAAiBA,EAAc,CACtD,CAAA,EAII4hB,EAAeA,IAAM,CAEjB9S,QAAAA,IAAI,iBAAkBrN,CAAc,CAAA,EAG9C,OAAItC,QACMiiB,GAAc,CAAA,MAAAjiB,EAAc,QAAS,IAAMa,GAAiBA,EAAmB,CAAA,CAAA,SAItFygB,GAEC,CAAA,SAAA,CAAAhb,EAAA,IAAC4U,GACC,CAAA,UAAAnb,EACA,WAAYD,EAAOsK,OACnB,cAAe9H,EAAe8H,OAC9B,iBAAAyM,EACA,UAAWhW,EACX,SAAU4hB,EAAa,EAIzBnc,MAACwW,IACC,UAAAC,EACA,YAAayB,EACb,SAAUze,EACV,YAAAkd,GACA,iBAAApG,EAAmC,QAIpC0K,GACC,CAAA,SAAAjb,MAACkb,GACC,CAAA,SAAAlb,EAAA,IAACoc,YAAS,SAAUpc,EAAAA,IAAC0b,GAAe,EAAA,EAClC,eAACb,GAA0B,CAAA,GAAIqB,EAAgB,CAAA,CACjD,CACF,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EAWaG,GAAiEvB,GAE1E9a,EAAAA,IAACoc,EAAAA,SAAS,CAAA,SAAWpc,EAAA,IAAA0b,GAAA,CAAe,CAAA,EAClC,SAAC1b,EAAAA,IAAA6b,GAAA,CAAe,GAAIf,CAAM,CAAA,CAC5B,CAAA,ECjREwB,GAA4CA,CAAC,CAAE9M,UAAAA,EAAWsM,WAAAA,CAAW,IAClE9b,EAAA,IAACqc,GAAmB,CAAA,UAAA7M,EAAsB,WAAAsM,CAA0B,CAAA"}