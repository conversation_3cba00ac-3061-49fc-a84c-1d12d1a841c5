import{c as ti,r as U,R as P}from"./react-25c2faed.js";import{r as go}from"./styled-components-00fe3932.js";function Ih(e){var t,r,n="";if(typeof e=="string"||typeof e=="number")n+=e;else if(typeof e=="object")if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(r=Ih(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function ne(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=Ih(e))&&(n&&(n+=" "),n+=t);return n}var Av=Array.isArray,je=Av,_v=typeof ti=="object"&&ti&&ti.Object===Object&&ti,Dh=_v,Pv=Dh,$v=typeof self=="object"&&self&&self.Object===Object&&self,Tv=Pv||$v||Function("return this")(),ot=Tv,Ev=ot,jv=Ev.Symbol,Wn=jv,Bc=Wn,Nh=Object.prototype,Mv=Nh.hasOwnProperty,Cv=Nh.toString,Nr=Bc?Bc.toStringTag:void 0;function kv(e){var t=Mv.call(e,Nr),r=e[Nr];try{e[Nr]=void 0;var n=!0}catch{}var i=Cv.call(e);return n&&(t?e[Nr]=r:delete e[Nr]),i}var Iv=kv,Dv=Object.prototype,Nv=Dv.toString;function Rv(e){return Nv.call(e)}var Lv=Rv,Fc=Wn,Bv=Iv,Fv=Lv,Wv="[object Null]",Uv="[object Undefined]",Wc=Fc?Fc.toStringTag:void 0;function zv(e){return e==null?e===void 0?Uv:Wv:Wc&&Wc in Object(e)?Bv(e):Fv(e)}var gt=zv;function qv(e){return e!=null&&typeof e=="object"}var mt=qv,Gv=gt,Hv=mt,Kv="[object Symbol]";function Xv(e){return typeof e=="symbol"||Hv(e)&&Gv(e)==Kv}var Pr=Xv,Vv=je,Yv=Pr,Zv=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Jv=/^\w*$/;function Qv(e,t){if(Vv(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Yv(e)?!0:Jv.test(e)||!Zv.test(e)||t!=null&&e in Object(t)}var Du=Qv;function ey(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var Le=ey,ty=gt,ry=Le,ny="[object AsyncFunction]",iy="[object Function]",ay="[object GeneratorFunction]",oy="[object Proxy]";function uy(e){if(!ry(e))return!1;var t=ty(e);return t==iy||t==ay||t==ny||t==oy}var Y=uy,cy=ot,sy=cy["__core-js_shared__"],ly=sy,Fa=ly,Uc=function(){var e=/[^.]+$/.exec(Fa&&Fa.keys&&Fa.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function fy(e){return!!Uc&&Uc in e}var hy=fy,dy=Function.prototype,py=dy.toString;function vy(e){if(e!=null){try{return py.call(e)}catch{}try{return e+""}catch{}}return""}var Rh=vy,yy=Y,gy=hy,my=Le,by=Rh,xy=/[\\^$.*+?()[\]{}|]/g,wy=/^\[object .+?Constructor\]$/,Oy=Function.prototype,Sy=Object.prototype,Ay=Oy.toString,_y=Sy.hasOwnProperty,Py=RegExp("^"+Ay.call(_y).replace(xy,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function $y(e){if(!my(e)||gy(e))return!1;var t=yy(e)?Py:wy;return t.test(by(e))}var Ty=$y;function Ey(e,t){return e==null?void 0:e[t]}var jy=Ey,My=Ty,Cy=jy;function ky(e,t){var r=Cy(e,t);return My(r)?r:void 0}var Xt=ky,Iy=Xt,Dy=Iy(Object,"create"),la=Dy,zc=la;function Ny(){this.__data__=zc?zc(null):{},this.size=0}var Ry=Ny;function Ly(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var By=Ly,Fy=la,Wy="__lodash_hash_undefined__",Uy=Object.prototype,zy=Uy.hasOwnProperty;function qy(e){var t=this.__data__;if(Fy){var r=t[e];return r===Wy?void 0:r}return zy.call(t,e)?t[e]:void 0}var Gy=qy,Hy=la,Ky=Object.prototype,Xy=Ky.hasOwnProperty;function Vy(e){var t=this.__data__;return Hy?t[e]!==void 0:Xy.call(t,e)}var Yy=Vy,Zy=la,Jy="__lodash_hash_undefined__";function Qy(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Zy&&t===void 0?Jy:t,this}var eg=Qy,tg=Ry,rg=By,ng=Gy,ig=Yy,ag=eg;function $r(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}$r.prototype.clear=tg;$r.prototype.delete=rg;$r.prototype.get=ng;$r.prototype.has=ig;$r.prototype.set=ag;var og=$r;function ug(){this.__data__=[],this.size=0}var cg=ug;function sg(e,t){return e===t||e!==e&&t!==t}var Nu=sg,lg=Nu;function fg(e,t){for(var r=e.length;r--;)if(lg(e[r][0],t))return r;return-1}var fa=fg,hg=fa,dg=Array.prototype,pg=dg.splice;function vg(e){var t=this.__data__,r=hg(t,e);if(r<0)return!1;var n=t.length-1;return r==n?t.pop():pg.call(t,r,1),--this.size,!0}var yg=vg,gg=fa;function mg(e){var t=this.__data__,r=gg(t,e);return r<0?void 0:t[r][1]}var bg=mg,xg=fa;function wg(e){return xg(this.__data__,e)>-1}var Og=wg,Sg=fa;function Ag(e,t){var r=this.__data__,n=Sg(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this}var _g=Ag,Pg=cg,$g=yg,Tg=bg,Eg=Og,jg=_g;function Tr(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Tr.prototype.clear=Pg;Tr.prototype.delete=$g;Tr.prototype.get=Tg;Tr.prototype.has=Eg;Tr.prototype.set=jg;var ha=Tr,Mg=Xt,Cg=ot,kg=Mg(Cg,"Map"),Ru=kg,qc=og,Ig=ha,Dg=Ru;function Ng(){this.size=0,this.__data__={hash:new qc,map:new(Dg||Ig),string:new qc}}var Rg=Ng;function Lg(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}var Bg=Lg,Fg=Bg;function Wg(e,t){var r=e.__data__;return Fg(t)?r[typeof t=="string"?"string":"hash"]:r.map}var da=Wg,Ug=da;function zg(e){var t=Ug(this,e).delete(e);return this.size-=t?1:0,t}var qg=zg,Gg=da;function Hg(e){return Gg(this,e).get(e)}var Kg=Hg,Xg=da;function Vg(e){return Xg(this,e).has(e)}var Yg=Vg,Zg=da;function Jg(e,t){var r=Zg(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this}var Qg=Jg,em=Rg,tm=qg,rm=Kg,nm=Yg,im=Qg;function Er(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}Er.prototype.clear=em;Er.prototype.delete=tm;Er.prototype.get=rm;Er.prototype.has=nm;Er.prototype.set=im;var Lu=Er,Lh=Lu,am="Expected a function";function Bu(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(am);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],a=r.cache;if(a.has(i))return a.get(i);var o=e.apply(this,n);return r.cache=a.set(i,o)||a,o};return r.cache=new(Bu.Cache||Lh),r}Bu.Cache=Lh;var om=Bu,um=om,cm=500;function sm(e){var t=um(e,function(n){return r.size===cm&&r.clear(),n}),r=t.cache;return t}var lm=sm,fm=lm,hm=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,dm=/\\(\\)?/g,pm=fm(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(hm,function(r,n,i,a){t.push(i?a.replace(dm,"$1"):n||r)}),t}),vm=pm;function ym(e,t){for(var r=-1,n=e==null?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}var Fu=ym,Gc=Wn,gm=Fu,mm=je,bm=Pr,xm=1/0,Hc=Gc?Gc.prototype:void 0,Kc=Hc?Hc.toString:void 0;function Bh(e){if(typeof e=="string")return e;if(mm(e))return gm(e,Bh)+"";if(bm(e))return Kc?Kc.call(e):"";var t=e+"";return t=="0"&&1/e==-xm?"-0":t}var wm=Bh,Om=wm;function Sm(e){return e==null?"":Om(e)}var Fh=Sm,Am=je,_m=Du,Pm=vm,$m=Fh;function Tm(e,t){return Am(e)?e:_m(e,t)?[e]:Pm($m(e))}var Wh=Tm,Em=Pr,jm=1/0;function Mm(e){if(typeof e=="string"||Em(e))return e;var t=e+"";return t=="0"&&1/e==-jm?"-0":t}var pa=Mm,Cm=Wh,km=pa;function Im(e,t){t=Cm(t,e);for(var r=0,n=t.length;e!=null&&r<n;)e=e[km(t[r++])];return r&&r==n?e:void 0}var Wu=Im,Dm=Wu;function Nm(e,t,r){var n=e==null?void 0:Dm(e,t);return n===void 0?r:n}var Ne=Nm;function Rm(e){return e==null}var J=Rm,Lm=gt,Bm=je,Fm=mt,Wm="[object String]";function Um(e){return typeof e=="string"||!Bm(e)&&Fm(e)&&Lm(e)==Wm}var zm=Um;const Un=zm;var qm=gt,Gm=mt,Hm="[object Number]";function Km(e){return typeof e=="number"||Gm(e)&&qm(e)==Hm}var Uh=Km,Xm=Uh;function Vm(e){return Xm(e)&&e!=+e}var zn=Vm,Je=function(t){return t===0?0:t>0?1:-1},Wt=function(t){return Un(t)&&t.indexOf("%")===t.length-1},L=function(t){return Uh(t)&&!zn(t)},ye=function(t){return L(t)||Un(t)},Ym=0,qn=function(t){var r=++Ym;return"".concat(t||"").concat(r)},cr=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!L(t)&&!Un(t))return n;var a;if(Wt(t)){var o=t.indexOf("%");a=r*parseFloat(t.slice(0,o))/100}else a=+t;return zn(a)&&(a=n),i&&a>r&&(a=r),a},St=function(t){if(!t)return null;var r=Object.keys(t);return r&&r.length?t[r[0]]:null},Zm=function(t){if(!Array.isArray(t))return!1;for(var r=t.length,n={},i=0;i<r;i++)if(!n[t[i]])n[t[i]]=!0;else return!0;return!1},Ye=function(t,r){return L(t)&&L(r)?function(n){return t+n*(r-t)}:function(){return r}};function yi(e,t,r){return!e||!e.length?null:e.find(function(n){return n&&(typeof t=="function"?t(n):Ne(n,t))===r})}function ar(e,t){for(var r in e)if({}.hasOwnProperty.call(e,r)&&(!{}.hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if({}.hasOwnProperty.call(t,n)&&!{}.hasOwnProperty.call(e,n))return!1;return!0}function mo(e){"@babel/helpers - typeof";return mo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mo(e)}var Jm=["viewBox","children"],Qm=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Xc=["points","pathLength"],Wa={svg:Jm,polygon:Xc,polyline:Xc},Uu=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],gi=function(t,r){if(!t||typeof t=="function"||typeof t=="boolean")return null;var n=t;if(U.isValidElement(t)&&(n=t.props),!Le(n))return null;var i={};return Object.keys(n).forEach(function(a){Uu.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},eb=function(t,r,n){return function(i){return t(r,n,i),null}},mi=function(t,r,n){if(!Le(t)||mo(t)!=="object")return null;var i=null;return Object.keys(t).forEach(function(a){var o=t[a];Uu.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=eb(o,r,n))}),i},tb=["children"],rb=["children"];function Vc(e,t){if(e==null)return{};var r=nb(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function nb(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function bo(e){"@babel/helpers - typeof";return bo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bo(e)}var Yc={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},lt=function(t){return typeof t=="string"?t:t?t.displayName||t.name||"Component":""},Zc=null,Ua=null,zu=function e(t){if(t===Zc&&Array.isArray(Ua))return Ua;var r=[];return U.Children.forEach(t,function(n){J(n)||(go.isFragment(n)?r=r.concat(e(n.props.children)):r.push(n))}),Ua=r,Zc=t,r};function qe(e,t){var r=[],n=[];return Array.isArray(t)?n=t.map(function(i){return lt(i)}):n=[lt(t)],zu(e).forEach(function(i){var a=Ne(i,"type.displayName")||Ne(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function ut(e,t){var r=qe(e,t);return r&&r[0]}var Jc=function(t){if(!t||!t.props)return!1;var r=t.props,n=r.width,i=r.height;return!(!L(n)||n<=0||!L(i)||i<=0)},ib=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ab=function(t){return t&&t.type&&Un(t.type)&&ib.indexOf(t.type)>=0},ob=function(t){return t&&bo(t)==="object"&&"cx"in t&&"cy"in t&&"r"in t},ub=function(t,r,n,i){var a,o=(a=Wa==null?void 0:Wa[i])!==null&&a!==void 0?a:[];return!Y(t)&&(i&&o.includes(r)||Qm.includes(r))||n&&Uu.includes(r)},Z=function(t,r,n){if(!t||typeof t=="function"||typeof t=="boolean")return null;var i=t;if(U.isValidElement(t)&&(i=t.props),!Le(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;ub((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},zh=function e(t,r){if(t===r)return!0;var n=U.Children.count(t);if(n!==U.Children.count(r))return!1;if(n===0)return!0;if(n===1)return Qc(Array.isArray(t)?t[0]:t,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=t[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!e(a,o))return!1}else if(!Qc(a,o))return!1}return!0},Qc=function(t,r){if(J(t)&&J(r))return!0;if(!J(t)&&!J(r)){var n=t.props||{},i=n.children,a=Vc(n,tb),o=r.props||{},u=o.children,c=Vc(o,rb);return i&&u?ar(a,c)&&zh(i,u):!i&&!u?ar(a,c):!1}return!1},es=function(t,r){var n=[],i={};return zu(t).forEach(function(a,o){if(ab(a))n.push(a);else if(a){var u=lt(a.type),c=r[u]||{},s=c.handler,f=c.once;if(s&&(!f||!i[u])){var l=s(a,u,o);n.push(l),i[u]=!0}}}),n},cb=function(t){var r=t&&t.type;return r&&Yc[r]?Yc[r]:null},sb=function(t,r){return zu(r).indexOf(t)},lb=["children","width","height","viewBox","className","style","title","desc"];function xo(){return xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},xo.apply(this,arguments)}function fb(e,t){if(e==null)return{};var r=hb(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function hb(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function wo(e){var t=e.children,r=e.width,n=e.height,i=e.viewBox,a=e.className,o=e.style,u=e.title,c=e.desc,s=fb(e,lb),f=i||{width:r,height:n,x:0,y:0},l=ne("recharts-surface",a);return P.createElement("svg",xo({},Z(s,!0,"svg"),{className:l,width:r,height:n,style:o,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),P.createElement("title",null,u),P.createElement("desc",null,c),t)}var db=["children","className"];function Oo(){return Oo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Oo.apply(this,arguments)}function pb(e,t){if(e==null)return{};var r=vb(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function vb(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}var he=P.forwardRef(function(e,t){var r=e.children,n=e.className,i=pb(e,db),a=ne("recharts-layer",n);return P.createElement("g",Oo({className:a},Z(i,!0),{ref:t}),r)}),qt=function(t,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]};function yb(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),r=r>i?i:r,r<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}var gb=yb,mb=gb;function bb(e,t,r){var n=e.length;return r=r===void 0?n:r,!t&&r>=n?e:mb(e,t,r)}var xb=bb,wb="\\ud800-\\udfff",Ob="\\u0300-\\u036f",Sb="\\ufe20-\\ufe2f",Ab="\\u20d0-\\u20ff",_b=Ob+Sb+Ab,Pb="\\ufe0e\\ufe0f",$b="\\u200d",Tb=RegExp("["+$b+wb+_b+Pb+"]");function Eb(e){return Tb.test(e)}var qh=Eb;function jb(e){return e.split("")}var Mb=jb,Gh="\\ud800-\\udfff",Cb="\\u0300-\\u036f",kb="\\ufe20-\\ufe2f",Ib="\\u20d0-\\u20ff",Db=Cb+kb+Ib,Nb="\\ufe0e\\ufe0f",Rb="["+Gh+"]",So="["+Db+"]",Ao="\\ud83c[\\udffb-\\udfff]",Lb="(?:"+So+"|"+Ao+")",Hh="[^"+Gh+"]",Kh="(?:\\ud83c[\\udde6-\\uddff]){2}",Xh="[\\ud800-\\udbff][\\udc00-\\udfff]",Bb="\\u200d",Vh=Lb+"?",Yh="["+Nb+"]?",Fb="(?:"+Bb+"(?:"+[Hh,Kh,Xh].join("|")+")"+Yh+Vh+")*",Wb=Yh+Vh+Fb,Ub="(?:"+[Hh+So+"?",So,Kh,Xh,Rb].join("|")+")",zb=RegExp(Ao+"(?="+Ao+")|"+Ub+Wb,"g");function qb(e){return e.match(zb)||[]}var Gb=qb,Hb=Mb,Kb=qh,Xb=Gb;function Vb(e){return Kb(e)?Xb(e):Hb(e)}var Yb=Vb,Zb=xb,Jb=qh,Qb=Yb,e0=Fh;function t0(e){return function(t){t=e0(t);var r=Jb(t)?Qb(t):void 0,n=r?r[0]:t.charAt(0),i=r?Zb(r,1).join(""):t.slice(1);return n[e]()+i}}var r0=t0,n0=r0,i0=n0("toUpperCase"),va=i0;function oe(e){return function(){return e}}const Zh=Math.cos,bi=Math.sin,Qe=Math.sqrt,xi=Math.PI,ya=2*xi,_o=Math.PI,Po=2*_o,Lt=1e-6,a0=Po-Lt;function Jh(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}function o0(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return Jh;const r=10**t;return function(n){this._+=n[0];for(let i=1,a=n.length;i<a;++i)this._+=Math.round(arguments[i]*r)/r+n[i]}}class u0{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?Jh:o0(t)}moveTo(t,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,r){this._append`L${this._x1=+t},${this._y1=+r}`}quadraticCurveTo(t,r,n,i){this._append`Q${+t},${+r},${this._x1=+n},${this._y1=+i}`}bezierCurveTo(t,r,n,i,a,o){this._append`C${+t},${+r},${+n},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,r,n,i,a){if(t=+t,r=+r,n=+n,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,u=this._y1,c=n-t,s=i-r,f=o-t,l=u-r,h=f*f+l*l;if(this._x1===null)this._append`M${this._x1=t},${this._y1=r}`;else if(h>Lt)if(!(Math.abs(l*c-s*f)>Lt)||!a)this._append`L${this._x1=t},${this._y1=r}`;else{let d=n-o,p=i-u,v=c*c+s*s,y=d*d+p*p,b=Math.sqrt(v),x=Math.sqrt(h),w=a*Math.tan((_o-Math.acos((v+h-y)/(2*b*x)))/2),O=w/x,g=w/b;Math.abs(O-1)>Lt&&this._append`L${t+O*f},${r+O*l}`,this._append`A${a},${a},0,0,${+(l*d>f*p)},${this._x1=t+g*c},${this._y1=r+g*s}`}}arc(t,r,n,i,a,o){if(t=+t,r=+r,n=+n,o=!!o,n<0)throw new Error(`negative radius: ${n}`);let u=n*Math.cos(i),c=n*Math.sin(i),s=t+u,f=r+c,l=1^o,h=o?i-a:a-i;this._x1===null?this._append`M${s},${f}`:(Math.abs(this._x1-s)>Lt||Math.abs(this._y1-f)>Lt)&&this._append`L${s},${f}`,n&&(h<0&&(h=h%Po+Po),h>a0?this._append`A${n},${n},0,1,${l},${t-u},${r-c}A${n},${n},0,1,${l},${this._x1=s},${this._y1=f}`:h>Lt&&this._append`A${n},${n},0,${+(h>=_o)},${l},${this._x1=t+n*Math.cos(a)},${this._y1=r+n*Math.sin(a)}`)}rect(t,r,n,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+r}h${n=+n}v${+i}h${-n}Z`}toString(){return this._}}function qu(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(r==null)t=null;else{const n=Math.floor(r);if(!(n>=0))throw new RangeError(`invalid digits: ${r}`);t=n}return e},()=>new u0(t)}function Gu(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Qh(e){this._context=e}Qh.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function ga(e){return new Qh(e)}function ed(e){return e[0]}function td(e){return e[1]}function rd(e,t){var r=oe(!0),n=null,i=ga,a=null,o=qu(u);e=typeof e=="function"?e:e===void 0?ed:oe(e),t=typeof t=="function"?t:t===void 0?td:oe(t);function u(c){var s,f=(c=Gu(c)).length,l,h=!1,d;for(n==null&&(a=i(d=o())),s=0;s<=f;++s)!(s<f&&r(l=c[s],s,c))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(l,s,c),+t(l,s,c));if(d)return a=null,d+""||null}return u.x=function(c){return arguments.length?(e=typeof c=="function"?c:oe(+c),u):e},u.y=function(c){return arguments.length?(t=typeof c=="function"?c:oe(+c),u):t},u.defined=function(c){return arguments.length?(r=typeof c=="function"?c:oe(!!c),u):r},u.curve=function(c){return arguments.length?(i=c,n!=null&&(a=i(n)),u):i},u.context=function(c){return arguments.length?(c==null?n=a=null:a=i(n=c),u):n},u}function ri(e,t,r){var n=null,i=oe(!0),a=null,o=ga,u=null,c=qu(s);e=typeof e=="function"?e:e===void 0?ed:oe(+e),t=typeof t=="function"?t:oe(t===void 0?0:+t),r=typeof r=="function"?r:r===void 0?td:oe(+r);function s(l){var h,d,p,v=(l=Gu(l)).length,y,b=!1,x,w=new Array(v),O=new Array(v);for(a==null&&(u=o(x=c())),h=0;h<=v;++h){if(!(h<v&&i(y=l[h],h,l))===b)if(b=!b)d=h,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=h-1;p>=d;--p)u.point(w[p],O[p]);u.lineEnd(),u.areaEnd()}b&&(w[h]=+e(y,h,l),O[h]=+t(y,h,l),u.point(n?+n(y,h,l):w[h],r?+r(y,h,l):O[h]))}if(x)return u=null,x+""||null}function f(){return rd().defined(i).curve(o).context(a)}return s.x=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),n=null,s):e},s.x0=function(l){return arguments.length?(e=typeof l=="function"?l:oe(+l),s):e},s.x1=function(l){return arguments.length?(n=l==null?null:typeof l=="function"?l:oe(+l),s):n},s.y=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),r=null,s):t},s.y0=function(l){return arguments.length?(t=typeof l=="function"?l:oe(+l),s):t},s.y1=function(l){return arguments.length?(r=l==null?null:typeof l=="function"?l:oe(+l),s):r},s.lineX0=s.lineY0=function(){return f().x(e).y(t)},s.lineY1=function(){return f().x(e).y(r)},s.lineX1=function(){return f().x(n).y(t)},s.defined=function(l){return arguments.length?(i=typeof l=="function"?l:oe(!!l),s):i},s.curve=function(l){return arguments.length?(o=l,a!=null&&(u=o(a)),s):o},s.context=function(l){return arguments.length?(l==null?a=u=null:u=o(a=l),s):a},s}class nd{constructor(t,r){this._context=t,this._x=r}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,r){switch(t=+t,r=+r,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,r):this._context.moveTo(t,r);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,r,t,r):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+r)/2,t,this._y0,t,r);break}}this._x0=t,this._y0=r}}function c0(e){return new nd(e,!0)}function s0(e){return new nd(e,!1)}const Hu={draw(e,t){const r=Qe(t/xi);e.moveTo(r,0),e.arc(0,0,r,0,ya)}},l0={draw(e,t){const r=Qe(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},id=Qe(1/3),f0=id*2,h0={draw(e,t){const r=Qe(t/f0),n=r*id;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},d0={draw(e,t){const r=Qe(t),n=-r/2;e.rect(n,n,r,r)}},p0=.8908130915292852,ad=bi(xi/10)/bi(7*xi/10),v0=bi(ya/10)*ad,y0=-Zh(ya/10)*ad,g0={draw(e,t){const r=Qe(t*p0),n=v0*r,i=y0*r;e.moveTo(0,-r),e.lineTo(n,i);for(let a=1;a<5;++a){const o=ya*a/5,u=Zh(o),c=bi(o);e.lineTo(c*r,-u*r),e.lineTo(u*n-c*i,c*n+u*i)}e.closePath()}},za=Qe(3),m0={draw(e,t){const r=-Qe(t/(za*3));e.moveTo(0,r*2),e.lineTo(-za*r,-r),e.lineTo(za*r,-r),e.closePath()}},Be=-.5,Fe=Qe(3)/2,$o=1/Qe(12),b0=($o/2+1)*3,x0={draw(e,t){const r=Qe(t/b0),n=r/2,i=r*$o,a=n,o=r*$o+r,u=-a,c=o;e.moveTo(n,i),e.lineTo(a,o),e.lineTo(u,c),e.lineTo(Be*n-Fe*i,Fe*n+Be*i),e.lineTo(Be*a-Fe*o,Fe*a+Be*o),e.lineTo(Be*u-Fe*c,Fe*u+Be*c),e.lineTo(Be*n+Fe*i,Be*i-Fe*n),e.lineTo(Be*a+Fe*o,Be*o-Fe*a),e.lineTo(Be*u+Fe*c,Be*c-Fe*u),e.closePath()}};function w0(e,t){let r=null,n=qu(i);e=typeof e=="function"?e:oe(e||Hu),t=typeof t=="function"?t:oe(t===void 0?64:+t);function i(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:oe(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:oe(+a),i):t},i.context=function(a){return arguments.length?(r=a??null,i):r},i}function wi(){}function Oi(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function od(e){this._context=e}od.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Oi(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Oi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function O0(e){return new od(e)}function ud(e){this._context=e}ud.prototype={areaStart:wi,areaEnd:wi,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Oi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function S0(e){return new ud(e)}function cd(e){this._context=e}cd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:Oi(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function A0(e){return new cd(e)}function sd(e){this._context=e}sd.prototype={areaStart:wi,areaEnd:wi,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function _0(e){return new sd(e)}function ts(e){return e<0?-1:1}function rs(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0),u=(a*i+o*n)/(n+i);return(ts(a)+ts(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(u))||0}function ns(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function qa(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,u=(a-n)/3;e._context.bezierCurveTo(n+u,i+u*t,a-u,o-u*r,a,o)}function Si(e){this._context=e}Si.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:qa(this,this._t0,ns(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,qa(this,ns(this,r=rs(this,e,t)),r);break;default:qa(this,this._t0,r=rs(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}};function ld(e){this._context=new fd(e)}(ld.prototype=Object.create(Si.prototype)).point=function(e,t){Si.prototype.point.call(this,t,e)};function fd(e){this._context=e}fd.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}};function P0(e){return new Si(e)}function $0(e){return new ld(e)}function hd(e){this._context=e}hd.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),r===2)this._context.lineTo(e[1],t[1]);else for(var n=is(e),i=is(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&r===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function is(e){var t,r=e.length-1,n,i=new Array(r),a=new Array(r),o=new Array(r);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,a[r-1]=7,o[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/a[t-1],a[t]-=n,o[t]-=n*o[t-1];for(i[r-1]=o[r-1]/a[r-1],t=r-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function T0(e){return new hd(e)}function ma(e,t){this._context=e,this._t=t}ma.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}break}}this._x=e,this._y=t}};function E0(e){return new ma(e,.5)}function j0(e){return new ma(e,0)}function M0(e){return new ma(e,1)}function sr(e,t){if((o=e.length)>1)for(var r=1,n,i,a=e[t[0]],o,u=a.length;r<o;++r)for(i=a,a=e[t[r]],n=0;n<u;++n)a[n][1]+=a[n][0]=isNaN(i[n][1])?i[n][0]:i[n][1]}function To(e){for(var t=e.length,r=new Array(t);--t>=0;)r[t]=t;return r}function C0(e,t){return e[t]}function k0(e){const t=[];return t.key=e,t}function I0(){var e=oe([]),t=To,r=sr,n=C0;function i(a){var o=Array.from(e.apply(this,arguments),k0),u,c=o.length,s=-1,f;for(const l of a)for(u=0,++s;u<c;++u)(o[u][s]=[0,+n(l,o[u].key,s,a)]).data=l;for(u=0,f=Gu(t(o));u<c;++u)o[f[u]].index=u;return r(o,f),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:oe(Array.from(a)),i):e},i.value=function(a){return arguments.length?(n=typeof a=="function"?a:oe(+a),i):n},i.order=function(a){return arguments.length?(t=a==null?To:typeof a=="function"?a:oe(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(r=a??sr,i):r},i}function D0(e,t){if((n=e.length)>0){for(var r,n,i=0,a=e[0].length,o;i<a;++i){for(o=r=0;r<n;++r)o+=e[r][i][1]||0;if(o)for(r=0;r<n;++r)e[r][i][1]/=o}sr(e,t)}}function N0(e,t){if((i=e.length)>0){for(var r=0,n=e[t[0]],i,a=n.length;r<a;++r){for(var o=0,u=0;o<i;++o)u+=e[o][r][1]||0;n[r][1]+=n[r][0]=-u/2}sr(e,t)}}function R0(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var r=0,n=1,i,a,o;n<a;++n){for(var u=0,c=0,s=0;u<o;++u){for(var f=e[t[u]],l=f[n][1]||0,h=f[n-1][1]||0,d=(l-h)/2,p=0;p<u;++p){var v=e[t[p]],y=v[n][1]||0,b=v[n-1][1]||0;d+=y-b}c+=l,s+=d*l}i[n-1][1]+=i[n-1][0]=r,c&&(r-=s/c)}i[n-1][1]+=i[n-1][0]=r,sr(e,t)}}function en(e){"@babel/helpers - typeof";return en=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},en(e)}var L0=["type","size","sizeType"];function Eo(){return Eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Eo.apply(this,arguments)}function as(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function os(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?as(Object(r),!0).forEach(function(n){B0(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):as(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function B0(e,t,r){return t=F0(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F0(e){var t=W0(e,"string");return en(t)==="symbol"?t:String(t)}function W0(e,t){if(en(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(en(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function U0(e,t){if(e==null)return{};var r=z0(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function z0(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}var dd={symbolCircle:Hu,symbolCross:l0,symbolDiamond:h0,symbolSquare:d0,symbolStar:g0,symbolTriangle:m0,symbolWye:x0},q0=Math.PI/180,G0=function(t){var r="symbol".concat(va(t));return dd[r]||Hu},H0=function(t,r,n){if(r==="area")return t;switch(n){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":{var i=18*q0;return 1.25*t*t*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},K0=function(t,r){dd["symbol".concat(va(t))]=r},Ku=function(t){var r=t.type,n=r===void 0?"circle":r,i=t.size,a=i===void 0?64:i,o=t.sizeType,u=o===void 0?"area":o,c=U0(t,L0),s=os(os({},c),{},{type:n,size:a,sizeType:u}),f=function(){var y=G0(n),b=w0().type(y).size(H0(a,u,n));return b()},l=s.className,h=s.cx,d=s.cy,p=Z(s,!0);return h===+h&&d===+d&&a===+a?P.createElement("path",Eo({},p,{className:ne("recharts-symbols",l),transform:"translate(".concat(h,", ").concat(d,")"),d:f()})):null};Ku.registerSymbol=K0;function lr(e){"@babel/helpers - typeof";return lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},lr(e)}function jo(){return jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},jo.apply(this,arguments)}function us(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function X0(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?us(Object(r),!0).forEach(function(n){tn(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):us(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function V0(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function cs(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pd(n.key),n)}}function Y0(e,t,r){return t&&cs(e.prototype,t),r&&cs(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Z0(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Mo(e,t)}function Mo(e,t){return Mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mo(e,t)}function J0(e){var t=tx();return function(){var n=Ai(e),i;if(t){var a=Ai(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Q0(this,i)}}function Q0(e,t){if(t&&(lr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ex(e)}function ex(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function tx(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Ai(e){return Ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ai(e)}function tn(e,t,r){return t=pd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pd(e){var t=rx(e,"string");return lr(t)==="symbol"?t:String(t)}function rx(e,t){if(lr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(lr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var We=32,Xu=function(e){Z0(r,e);var t=J0(r);function r(){return V0(this,r),t.apply(this,arguments)}return Y0(r,[{key:"renderIcon",value:function(i){var a=this.props.inactiveColor,o=We/2,u=We/6,c=We/3,s=i.inactive?a:i.color;if(i.type==="plainline")return P.createElement("line",{strokeWidth:4,fill:"none",stroke:s,strokeDasharray:i.payload.strokeDasharray,x1:0,y1:o,x2:We,y2:o,className:"recharts-legend-icon"});if(i.type==="line")return P.createElement("path",{strokeWidth:4,fill:"none",stroke:s,d:"M0,".concat(o,"h").concat(c,`
            A`).concat(u,",").concat(u,",0,1,1,").concat(2*c,",").concat(o,`
            H`).concat(We,"M").concat(2*c,",").concat(o,`
            A`).concat(u,",").concat(u,",0,1,1,").concat(c,",").concat(o),className:"recharts-legend-icon"});if(i.type==="rect")return P.createElement("path",{stroke:"none",fill:s,d:"M0,".concat(We/8,"h").concat(We,"v").concat(We*3/4,"h").concat(-We,"z"),className:"recharts-legend-icon"});if(P.isValidElement(i.legendIcon)){var f=X0({},i);return delete f.legendIcon,P.cloneElement(i.legendIcon,f)}return P.createElement(Ku,{fill:s,cx:o,cy:o,size:We,sizeType:"diameter",type:i.type})}},{key:"renderItems",value:function(){var i=this,a=this.props,o=a.payload,u=a.iconSize,c=a.layout,s=a.formatter,f=a.inactiveColor,l={x:0,y:0,width:We,height:We},h={display:c==="horizontal"?"inline-block":"block",marginRight:10},d={display:"inline-block",verticalAlign:"middle",marginRight:4};return o.map(function(p,v){var y,b=p.formatter||s,x=ne((y={"recharts-legend-item":!0},tn(y,"legend-item-".concat(v),!0),tn(y,"inactive",p.inactive),y));if(p.type==="none")return null;var w=Y(p.value)?null:p.value;qt(!Y(p.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var O=p.inactive?f:p.color;return P.createElement("li",jo({className:x,style:h,key:"legend-item-".concat(v)},mi(i.props,p,v)),P.createElement(wo,{width:u,height:u,viewBox:l,style:d},i.renderIcon(p)),P.createElement("span",{className:"recharts-legend-item-text",style:{color:O}},b?b(w,p,v):w))})}},{key:"render",value:function(){var i=this.props,a=i.payload,o=i.layout,u=i.align;if(!a||!a.length)return null;var c={padding:0,margin:0,textAlign:o==="horizontal"?u:"left"};return P.createElement("ul",{className:"recharts-default-legend",style:c},this.renderItems())}}]),r}(U.PureComponent);tn(Xu,"displayName","Legend");tn(Xu,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var nx=ha;function ix(){this.__data__=new nx,this.size=0}var ax=ix;function ox(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}var ux=ox;function cx(e){return this.__data__.get(e)}var sx=cx;function lx(e){return this.__data__.has(e)}var fx=lx,hx=ha,dx=Ru,px=Lu,vx=200;function yx(e,t){var r=this.__data__;if(r instanceof hx){var n=r.__data__;if(!dx||n.length<vx-1)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new px(n)}return r.set(e,t),this.size=r.size,this}var gx=yx,mx=ha,bx=ax,xx=ux,wx=sx,Ox=fx,Sx=gx;function jr(e){var t=this.__data__=new mx(e);this.size=t.size}jr.prototype.clear=bx;jr.prototype.delete=xx;jr.prototype.get=wx;jr.prototype.has=Ox;jr.prototype.set=Sx;var vd=jr,Ax="__lodash_hash_undefined__";function _x(e){return this.__data__.set(e,Ax),this}var Px=_x;function $x(e){return this.__data__.has(e)}var Tx=$x,Ex=Lu,jx=Px,Mx=Tx;function _i(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new Ex;++t<r;)this.add(e[t])}_i.prototype.add=_i.prototype.push=jx;_i.prototype.has=Mx;var yd=_i;function Cx(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var gd=Cx;function kx(e,t){return e.has(t)}var md=kx,Ix=yd,Dx=gd,Nx=md,Rx=1,Lx=2;function Bx(e,t,r,n,i,a){var o=r&Rx,u=e.length,c=t.length;if(u!=c&&!(o&&c>u))return!1;var s=a.get(e),f=a.get(t);if(s&&f)return s==t&&f==e;var l=-1,h=!0,d=r&Lx?new Ix:void 0;for(a.set(e,t),a.set(t,e);++l<u;){var p=e[l],v=t[l];if(n)var y=o?n(v,p,l,t,e,a):n(p,v,l,e,t,a);if(y!==void 0){if(y)continue;h=!1;break}if(d){if(!Dx(t,function(b,x){if(!Nx(d,x)&&(p===b||i(p,b,r,n,a)))return d.push(x)})){h=!1;break}}else if(!(p===v||i(p,v,r,n,a))){h=!1;break}}return a.delete(e),a.delete(t),h}var bd=Bx,Fx=ot,Wx=Fx.Uint8Array,Ux=Wx;function zx(e){var t=-1,r=Array(e.size);return e.forEach(function(n,i){r[++t]=[i,n]}),r}var qx=zx;function Gx(e){var t=-1,r=Array(e.size);return e.forEach(function(n){r[++t]=n}),r}var Vu=Gx,ss=Wn,ls=Ux,Hx=Nu,Kx=bd,Xx=qx,Vx=Vu,Yx=1,Zx=2,Jx="[object Boolean]",Qx="[object Date]",ew="[object Error]",tw="[object Map]",rw="[object Number]",nw="[object RegExp]",iw="[object Set]",aw="[object String]",ow="[object Symbol]",uw="[object ArrayBuffer]",cw="[object DataView]",fs=ss?ss.prototype:void 0,Ga=fs?fs.valueOf:void 0;function sw(e,t,r,n,i,a,o){switch(r){case cw:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case uw:return!(e.byteLength!=t.byteLength||!a(new ls(e),new ls(t)));case Jx:case Qx:case rw:return Hx(+e,+t);case ew:return e.name==t.name&&e.message==t.message;case nw:case aw:return e==t+"";case tw:var u=Xx;case iw:var c=n&Yx;if(u||(u=Vx),e.size!=t.size&&!c)return!1;var s=o.get(e);if(s)return s==t;n|=Zx,o.set(e,t);var f=Kx(u(e),u(t),n,i,a,o);return o.delete(e),f;case ow:if(Ga)return Ga.call(e)==Ga.call(t)}return!1}var lw=sw;function fw(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}var xd=fw,hw=xd,dw=je;function pw(e,t,r){var n=t(e);return dw(e)?n:hw(n,r(e))}var vw=pw;function yw(e,t){for(var r=-1,n=e==null?0:e.length,i=0,a=[];++r<n;){var o=e[r];t(o,r,e)&&(a[i++]=o)}return a}var gw=yw;function mw(){return[]}var bw=mw,xw=gw,ww=bw,Ow=Object.prototype,Sw=Ow.propertyIsEnumerable,hs=Object.getOwnPropertySymbols,Aw=hs?function(e){return e==null?[]:(e=Object(e),xw(hs(e),function(t){return Sw.call(e,t)}))}:ww,_w=Aw;function Pw(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}var $w=Pw,Tw=gt,Ew=mt,jw="[object Arguments]";function Mw(e){return Ew(e)&&Tw(e)==jw}var Cw=Mw,ds=Cw,kw=mt,wd=Object.prototype,Iw=wd.hasOwnProperty,Dw=wd.propertyIsEnumerable,Nw=ds(function(){return arguments}())?ds:function(e){return kw(e)&&Iw.call(e,"callee")&&!Dw.call(e,"callee")},Yu=Nw,rn={},Rw={get exports(){return rn},set exports(e){rn=e}};function Lw(){return!1}var Bw=Lw;(function(e,t){var r=ot,n=Bw,i=t&&!t.nodeType&&t,a=i&&!0&&e&&!e.nodeType&&e,o=a&&a.exports===i,u=o?r.Buffer:void 0,c=u?u.isBuffer:void 0,s=c||n;e.exports=s})(Rw,rn);var Fw=9007199254740991,Ww=/^(?:0|[1-9]\d*)$/;function Uw(e,t){var r=typeof e;return t=t??Fw,!!t&&(r=="number"||r!="symbol"&&Ww.test(e))&&e>-1&&e%1==0&&e<t}var Zu=Uw,zw=9007199254740991;function qw(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=zw}var Ju=qw,Gw=gt,Hw=Ju,Kw=mt,Xw="[object Arguments]",Vw="[object Array]",Yw="[object Boolean]",Zw="[object Date]",Jw="[object Error]",Qw="[object Function]",e1="[object Map]",t1="[object Number]",r1="[object Object]",n1="[object RegExp]",i1="[object Set]",a1="[object String]",o1="[object WeakMap]",u1="[object ArrayBuffer]",c1="[object DataView]",s1="[object Float32Array]",l1="[object Float64Array]",f1="[object Int8Array]",h1="[object Int16Array]",d1="[object Int32Array]",p1="[object Uint8Array]",v1="[object Uint8ClampedArray]",y1="[object Uint16Array]",g1="[object Uint32Array]",ue={};ue[s1]=ue[l1]=ue[f1]=ue[h1]=ue[d1]=ue[p1]=ue[v1]=ue[y1]=ue[g1]=!0;ue[Xw]=ue[Vw]=ue[u1]=ue[Yw]=ue[c1]=ue[Zw]=ue[Jw]=ue[Qw]=ue[e1]=ue[t1]=ue[r1]=ue[n1]=ue[i1]=ue[a1]=ue[o1]=!1;function m1(e){return Kw(e)&&Hw(e.length)&&!!ue[Gw(e)]}var b1=m1;function x1(e){return function(t){return e(t)}}var Od=x1,Pi={},w1={get exports(){return Pi},set exports(e){Pi=e}};(function(e,t){var r=Dh,n=t&&!t.nodeType&&t,i=n&&!0&&e&&!e.nodeType&&e,a=i&&i.exports===n,o=a&&r.process,u=function(){try{var c=i&&i.require&&i.require("util").types;return c||o&&o.binding&&o.binding("util")}catch{}}();e.exports=u})(w1,Pi);var O1=b1,S1=Od,ps=Pi,vs=ps&&ps.isTypedArray,A1=vs?S1(vs):O1,Sd=A1,_1=$w,P1=Yu,$1=je,T1=rn,E1=Zu,j1=Sd,M1=Object.prototype,C1=M1.hasOwnProperty;function k1(e,t){var r=$1(e),n=!r&&P1(e),i=!r&&!n&&T1(e),a=!r&&!n&&!i&&j1(e),o=r||n||i||a,u=o?_1(e.length,String):[],c=u.length;for(var s in e)(t||C1.call(e,s))&&!(o&&(s=="length"||i&&(s=="offset"||s=="parent")||a&&(s=="buffer"||s=="byteLength"||s=="byteOffset")||E1(s,c)))&&u.push(s);return u}var I1=k1,D1=Object.prototype;function N1(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||D1;return e===r}var R1=N1;function L1(e,t){return function(r){return e(t(r))}}var Ad=L1,B1=Ad,F1=B1(Object.keys,Object),W1=F1,U1=R1,z1=W1,q1=Object.prototype,G1=q1.hasOwnProperty;function H1(e){if(!U1(e))return z1(e);var t=[];for(var r in Object(e))G1.call(e,r)&&r!="constructor"&&t.push(r);return t}var K1=H1,X1=Y,V1=Ju;function Y1(e){return e!=null&&V1(e.length)&&!X1(e)}var Gn=Y1,Z1=I1,J1=K1,Q1=Gn;function eO(e){return Q1(e)?Z1(e):J1(e)}var ba=eO,tO=vw,rO=_w,nO=ba;function iO(e){return tO(e,nO,rO)}var aO=iO,ys=aO,oO=1,uO=Object.prototype,cO=uO.hasOwnProperty;function sO(e,t,r,n,i,a){var o=r&oO,u=ys(e),c=u.length,s=ys(t),f=s.length;if(c!=f&&!o)return!1;for(var l=c;l--;){var h=u[l];if(!(o?h in t:cO.call(t,h)))return!1}var d=a.get(e),p=a.get(t);if(d&&p)return d==t&&p==e;var v=!0;a.set(e,t),a.set(t,e);for(var y=o;++l<c;){h=u[l];var b=e[h],x=t[h];if(n)var w=o?n(x,b,h,t,e,a):n(b,x,h,e,t,a);if(!(w===void 0?b===x||i(b,x,r,n,a):w)){v=!1;break}y||(y=h=="constructor")}if(v&&!y){var O=e.constructor,g=t.constructor;O!=g&&"constructor"in e&&"constructor"in t&&!(typeof O=="function"&&O instanceof O&&typeof g=="function"&&g instanceof g)&&(v=!1)}return a.delete(e),a.delete(t),v}var lO=sO,fO=Xt,hO=ot,dO=fO(hO,"DataView"),pO=dO,vO=Xt,yO=ot,gO=vO(yO,"Promise"),mO=gO,bO=Xt,xO=ot,wO=bO(xO,"Set"),_d=wO,OO=Xt,SO=ot,AO=OO(SO,"WeakMap"),_O=AO,Co=pO,ko=Ru,Io=mO,Do=_d,No=_O,Pd=gt,Mr=Rh,gs="[object Map]",PO="[object Object]",ms="[object Promise]",bs="[object Set]",xs="[object WeakMap]",ws="[object DataView]",$O=Mr(Co),TO=Mr(ko),EO=Mr(Io),jO=Mr(Do),MO=Mr(No),Bt=Pd;(Co&&Bt(new Co(new ArrayBuffer(1)))!=ws||ko&&Bt(new ko)!=gs||Io&&Bt(Io.resolve())!=ms||Do&&Bt(new Do)!=bs||No&&Bt(new No)!=xs)&&(Bt=function(e){var t=Pd(e),r=t==PO?e.constructor:void 0,n=r?Mr(r):"";if(n)switch(n){case $O:return ws;case TO:return gs;case EO:return ms;case jO:return bs;case MO:return xs}return t});var CO=Bt,Ha=vd,kO=bd,IO=lw,DO=lO,Os=CO,Ss=je,As=rn,NO=Sd,RO=1,_s="[object Arguments]",Ps="[object Array]",ni="[object Object]",LO=Object.prototype,$s=LO.hasOwnProperty;function BO(e,t,r,n,i,a){var o=Ss(e),u=Ss(t),c=o?Ps:Os(e),s=u?Ps:Os(t);c=c==_s?ni:c,s=s==_s?ni:s;var f=c==ni,l=s==ni,h=c==s;if(h&&As(e)){if(!As(t))return!1;o=!0,f=!1}if(h&&!f)return a||(a=new Ha),o||NO(e)?kO(e,t,r,n,i,a):IO(e,t,c,r,n,i,a);if(!(r&RO)){var d=f&&$s.call(e,"__wrapped__"),p=l&&$s.call(t,"__wrapped__");if(d||p){var v=d?e.value():e,y=p?t.value():t;return a||(a=new Ha),i(v,y,r,n,a)}}return h?(a||(a=new Ha),DO(e,t,r,n,i,a)):!1}var FO=BO,WO=FO,Ts=mt;function $d(e,t,r,n,i){return e===t?!0:e==null||t==null||!Ts(e)&&!Ts(t)?e!==e&&t!==t:WO(e,t,r,n,$d,i)}var Qu=$d,UO=vd,zO=Qu,qO=1,GO=2;function HO(e,t,r,n){var i=r.length,a=i,o=!n;if(e==null)return!a;for(e=Object(e);i--;){var u=r[i];if(o&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){u=r[i];var c=u[0],s=e[c],f=u[1];if(o&&u[2]){if(s===void 0&&!(c in e))return!1}else{var l=new UO;if(n)var h=n(s,f,c,e,t,l);if(!(h===void 0?zO(f,s,qO|GO,n,l):h))return!1}}return!0}var KO=HO,XO=Le;function VO(e){return e===e&&!XO(e)}var Td=VO,YO=Td,ZO=ba;function JO(e){for(var t=ZO(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,YO(i)]}return t}var QO=JO;function eS(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var Ed=eS,tS=KO,rS=QO,nS=Ed;function iS(e){var t=rS(e);return t.length==1&&t[0][2]?nS(t[0][0],t[0][1]):function(r){return r===e||tS(r,e,t)}}var aS=iS;function oS(e,t){return e!=null&&t in Object(e)}var uS=oS,cS=Wh,sS=Yu,lS=je,fS=Zu,hS=Ju,dS=pa;function pS(e,t,r){t=cS(t,e);for(var n=-1,i=t.length,a=!1;++n<i;){var o=dS(t[n]);if(!(a=e!=null&&r(e,o)))break;e=e[o]}return a||++n!=i?a:(i=e==null?0:e.length,!!i&&hS(i)&&fS(o,i)&&(lS(e)||sS(e)))}var vS=pS,yS=uS,gS=vS;function mS(e,t){return e!=null&&gS(e,t,yS)}var bS=mS,xS=Qu,wS=Ne,OS=bS,SS=Du,AS=Td,_S=Ed,PS=pa,$S=1,TS=2;function ES(e,t){return SS(e)&&AS(t)?_S(PS(e),t):function(r){var n=wS(r,e);return n===void 0&&n===t?OS(r,e):xS(t,n,$S|TS)}}var jS=ES;function MS(e){return e}var Cr=MS;function CS(e){return function(t){return t==null?void 0:t[e]}}var kS=CS,IS=Wu;function DS(e){return function(t){return IS(t,e)}}var NS=DS,RS=kS,LS=NS,BS=Du,FS=pa;function WS(e){return BS(e)?RS(FS(e)):LS(e)}var US=WS,zS=aS,qS=jS,GS=Cr,HS=je,KS=US;function XS(e){return typeof e=="function"?e:e==null?GS:typeof e=="object"?HS(e)?qS(e[0],e[1]):zS(e):KS(e)}var jt=XS;function VS(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var jd=VS;function YS(e){return e!==e}var ZS=YS;function JS(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return-1}var QS=JS,eA=jd,tA=ZS,rA=QS;function nA(e,t,r){return t===t?rA(e,t,r):eA(e,tA,r)}var iA=nA,aA=iA;function oA(e,t){var r=e==null?0:e.length;return!!r&&aA(e,t,0)>-1}var uA=oA;function cA(e,t,r){for(var n=-1,i=e==null?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}var sA=cA;function lA(){}var fA=lA,Ka=_d,hA=fA,dA=Vu,pA=1/0,vA=Ka&&1/dA(new Ka([,-0]))[1]==pA?function(e){return new Ka(e)}:hA,yA=vA,gA=yd,mA=uA,bA=sA,xA=md,wA=yA,OA=Vu,SA=200;function AA(e,t,r){var n=-1,i=mA,a=e.length,o=!0,u=[],c=u;if(r)o=!1,i=bA;else if(a>=SA){var s=t?null:wA(e);if(s)return OA(s);o=!1,i=xA,c=new gA}else c=t?[]:u;e:for(;++n<a;){var f=e[n],l=t?t(f):f;if(f=r||f!==0?f:0,o&&l===l){for(var h=c.length;h--;)if(c[h]===l)continue e;t&&c.push(l),u.push(f)}else i(c,l,r)||(c!==u&&c.push(l),u.push(f))}return u}var _A=AA,PA=jt,$A=_A;function TA(e,t){return e&&e.length?$A(e,PA(t)):[]}var EA=TA;const Es=EA;function Md(e,t,r){return t===!0?Es(e,r):Y(t)?Es(e,t):e}function fr(e){"@babel/helpers - typeof";return fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fr(e)}var jA=["ref"];function js(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Rt(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?js(Object(r),!0).forEach(function(n){xa(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):js(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function MA(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ms(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,kd(n.key),n)}}function CA(e,t,r){return t&&Ms(e.prototype,t),r&&Ms(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function kA(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ro(e,t)}function Ro(e,t){return Ro=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ro(e,t)}function IA(e){var t=NA();return function(){var n=$i(e),i;if(t){var a=$i(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return DA(this,i)}}function DA(e,t){if(t&&(fr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Cd(e)}function Cd(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function NA(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function $i(e){return $i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$i(e)}function xa(e,t,r){return t=kd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kd(e){var t=RA(e,"string");return fr(t)==="symbol"?t:String(t)}function RA(e,t){if(fr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function LA(e,t){if(e==null)return{};var r=BA(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function BA(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function FA(e){return e.value}function WA(e,t){if(P.isValidElement(e))return P.cloneElement(e,t);if(typeof e=="function")return P.createElement(e,t);t.ref;var r=LA(t,jA);return P.createElement(Xu,r)}var Cs=1,nn=function(e){kA(r,e);var t=IA(r);function r(){var n;MA(this,r);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=t.call.apply(t,[this].concat(a)),xa(Cd(n),"lastBoundingBox",{width:-1,height:-1}),n}return CA(r,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){return this.wrapperNode&&this.wrapperNode.getBoundingClientRect?this.wrapperNode.getBoundingClientRect():null}},{key:"updateBBox",value:function(){var i=this.props.onBBoxUpdate;if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var a=this.wrapperNode.getBoundingClientRect();(Math.abs(a.width-this.lastBoundingBox.width)>Cs||Math.abs(a.height-this.lastBoundingBox.height)>Cs)&&(this.lastBoundingBox.width=a.width,this.lastBoundingBox.height=a.height,i&&i(a))}else(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,i&&i(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Rt({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(i){var a=this.props,o=a.layout,u=a.align,c=a.verticalAlign,s=a.margin,f=a.chartWidth,l=a.chartHeight,h,d;if(!i||(i.left===void 0||i.left===null)&&(i.right===void 0||i.right===null))if(u==="center"&&o==="vertical"){var p=this.getBBoxSnapshot();h={left:((f||0)-p.width)/2}}else h=u==="right"?{right:s&&s.right||0}:{left:s&&s.left||0};if(!i||(i.top===void 0||i.top===null)&&(i.bottom===void 0||i.bottom===null))if(c==="middle"){var v=this.getBBoxSnapshot();d={top:((l||0)-v.height)/2}}else d=c==="bottom"?{bottom:s&&s.bottom||0}:{top:s&&s.top||0};return Rt(Rt({},h),d)}},{key:"render",value:function(){var i=this,a=this.props,o=a.content,u=a.width,c=a.height,s=a.wrapperStyle,f=a.payloadUniqBy,l=a.payload,h=Rt(Rt({position:"absolute",width:u||"auto",height:c||"auto"},this.getDefaultPosition(s)),s);return P.createElement("div",{className:"recharts-legend-wrapper",style:h,ref:function(p){i.wrapperNode=p}},WA(o,Rt(Rt({},this.props),{},{payload:Md(l,f,FA)})))}}],[{key:"getWithHeight",value:function(i,a){var o=i.props.layout;return o==="vertical"&&L(i.props.height)?{height:i.props.height}:o==="horizontal"?{width:i.props.width||a}:null}}]),r}(U.PureComponent);xa(nn,"displayName","Legend");xa(nn,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ks=Wn,UA=Yu,zA=je,Is=ks?ks.isConcatSpreadable:void 0;function qA(e){return zA(e)||UA(e)||!!(Is&&e&&e[Is])}var GA=qA,HA=xd,KA=GA;function Id(e,t,r,n,i){var a=-1,o=e.length;for(r||(r=KA),i||(i=[]);++a<o;){var u=e[a];t>0&&r(u)?t>1?Id(u,t-1,r,n,i):HA(i,u):n||(i[i.length]=u)}return i}var Dd=Id;function XA(e){return function(t,r,n){for(var i=-1,a=Object(t),o=n(t),u=o.length;u--;){var c=o[e?u:++i];if(r(a[c],c,a)===!1)break}return t}}var VA=XA,YA=VA,ZA=YA(),JA=ZA,QA=JA,e_=ba;function t_(e,t){return e&&QA(e,t,e_)}var Nd=t_,r_=Gn;function n_(e,t){return function(r,n){if(r==null)return r;if(!r_(r))return e(r,n);for(var i=r.length,a=t?i:-1,o=Object(r);(t?a--:++a<i)&&n(o[a],a,o)!==!1;);return r}}var i_=n_,a_=Nd,o_=i_,u_=o_(a_),ec=u_,c_=ec,s_=Gn;function l_(e,t){var r=-1,n=s_(e)?Array(e.length):[];return c_(e,function(i,a,o){n[++r]=t(i,a,o)}),n}var Rd=l_;function f_(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}var h_=f_,Ds=Pr;function d_(e,t){if(e!==t){var r=e!==void 0,n=e===null,i=e===e,a=Ds(e),o=t!==void 0,u=t===null,c=t===t,s=Ds(t);if(!u&&!s&&!a&&e>t||a&&o&&c&&!u&&!s||n&&o&&c||!r&&c||!i)return 1;if(!n&&!a&&!s&&e<t||s&&r&&i&&!n&&!a||u&&r&&i||!o&&i||!c)return-1}return 0}var p_=d_,v_=p_;function y_(e,t,r){for(var n=-1,i=e.criteria,a=t.criteria,o=i.length,u=r.length;++n<o;){var c=v_(i[n],a[n]);if(c){if(n>=u)return c;var s=r[n];return c*(s=="desc"?-1:1)}}return e.index-t.index}var g_=y_,Xa=Fu,m_=Wu,b_=jt,x_=Rd,w_=h_,O_=Od,S_=g_,A_=Cr,__=je;function P_(e,t,r){t.length?t=Xa(t,function(a){return __(a)?function(o){return m_(o,a.length===1?a[0]:a)}:a}):t=[A_];var n=-1;t=Xa(t,O_(b_));var i=x_(e,function(a,o,u){var c=Xa(t,function(s){return s(a)});return{criteria:c,index:++n,value:a}});return w_(i,function(a,o){return S_(a,o,r)})}var $_=P_;function T_(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}var E_=T_,j_=E_,Ns=Math.max;function M_(e,t,r){return t=Ns(t===void 0?e.length-1:t,0),function(){for(var n=arguments,i=-1,a=Ns(n.length-t,0),o=Array(a);++i<a;)o[i]=n[t+i];i=-1;for(var u=Array(t+1);++i<t;)u[i]=n[i];return u[t]=r(o),j_(e,this,u)}}var C_=M_;function k_(e){return function(){return e}}var I_=k_,D_=Xt,N_=function(){try{var e=D_(Object,"defineProperty");return e({},"",{}),e}catch{}}(),Ld=N_,R_=I_,Rs=Ld,L_=Cr,B_=Rs?function(e,t){return Rs(e,"toString",{configurable:!0,enumerable:!1,value:R_(t),writable:!0})}:L_,F_=B_,W_=800,U_=16,z_=Date.now;function q_(e){var t=0,r=0;return function(){var n=z_(),i=U_-(n-r);if(r=n,i>0){if(++t>=W_)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var G_=q_,H_=F_,K_=G_,X_=K_(H_),V_=X_,Y_=Cr,Z_=C_,J_=V_;function Q_(e,t){return J_(Z_(e,t,Y_),e+"")}var eP=Q_,tP=Nu,rP=Gn,nP=Zu,iP=Le;function aP(e,t,r){if(!iP(r))return!1;var n=typeof t;return(n=="number"?rP(r)&&nP(t,r.length):n=="string"&&t in r)?tP(r[t],e):!1}var wa=aP,oP=Dd,uP=$_,cP=eP,Ls=wa,sP=cP(function(e,t){if(e==null)return[];var r=t.length;return r>1&&Ls(e,t[0],t[1])?t=[]:r>2&&Ls(t[0],t[1],t[2])&&(t=[t[0]]),uP(e,oP(t,1),[])}),tc=sP;function an(e){"@babel/helpers - typeof";return an=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},an(e)}function lP(e,t){return pP(e)||dP(e,t)||hP(e,t)||fP()}function fP(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hP(e,t){if(e){if(typeof e=="string")return Bs(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bs(e,t)}}function Bs(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function dP(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function pP(e){if(Array.isArray(e))return e}function Fs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Va(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Fs(Object(r),!0).forEach(function(n){vP(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Fs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vP(e,t,r){return t=yP(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yP(e){var t=gP(e,"string");return an(t)==="symbol"?t:String(t)}function gP(e,t){if(an(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(an(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function mP(e){return Array.isArray(e)&&ye(e[0])&&ye(e[1])?e.join(" ~ "):e}var bP=function(t){var r=t.separator,n=r===void 0?" : ":r,i=t.contentStyle,a=i===void 0?{}:i,o=t.itemStyle,u=o===void 0?{}:o,c=t.labelStyle,s=c===void 0?{}:c,f=t.payload,l=t.formatter,h=t.itemSorter,d=t.wrapperClassName,p=t.labelClassName,v=t.label,y=t.labelFormatter,b=function(){if(f&&f.length){var _={padding:0,margin:0},E=(h?tc(f,h):f).map(function($,T){if($.type==="none")return null;var M=Va({display:"block",paddingTop:4,paddingBottom:4,color:$.color||"#000"},u),k=$.formatter||l||mP,C=$.value,N=$.name,I=C,R=N;if(k&&I!=null&&R!=null){var B=k(C,N,$,T,f);if(Array.isArray(B)){var q=lP(B,2);I=q[0],R=q[1]}else I=B}return P.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(T),style:M},ye(R)?P.createElement("span",{className:"recharts-tooltip-item-name"},R):null,ye(R)?P.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,P.createElement("span",{className:"recharts-tooltip-item-value"},I),P.createElement("span",{className:"recharts-tooltip-item-unit"},$.unit||""))});return P.createElement("ul",{className:"recharts-tooltip-item-list",style:_},E)}return null},x=Va({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),w=Va({margin:0},s),O=!J(v),g=O?v:"",m=ne("recharts-default-tooltip",d),S=ne("recharts-tooltip-label",p);return O&&y&&f!==void 0&&f!==null&&(g=y(v,f)),P.createElement("div",{className:m,style:x},P.createElement("p",{className:S,style:w},P.isValidElement(g)?g:"".concat(g)),b())},H={},xP={get exports(){return H},set exports(e){H=e}},wP="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",OP=wP,SP=OP;function Bd(){}function Fd(){}Fd.resetWarningCache=Bd;var AP=function(){function e(n,i,a,o,u,c){if(c!==SP){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Fd,resetWarningCache:Bd};return r.PropTypes=r,r};xP.exports=AP();var _P=Object.getOwnPropertyNames,PP=Object.getOwnPropertySymbols,$P=Object.prototype.hasOwnProperty;function Ws(e,t){return function(n,i,a){return e(n,i,a)&&t(n,i,a)}}function ii(e){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return e(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=e(r,n,i);return a.delete(r),a.delete(n),c}}function Us(e){return _P(e).concat(PP(e))}var TP=Object.hasOwn||function(e,t){return $P.call(e,t)};function Vt(e,t){return e===t||!e&&!t&&e!==e&&t!==t}var EP="__v",jP="__o",MP="_owner",zs=Object.getOwnPropertyDescriptor,qs=Object.keys;function CP(e,t,r){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(!r.equals(e[n],t[n],n,n,e,t,r))return!1;return!0}function kP(e,t){return Vt(e.getTime(),t.getTime())}function IP(e,t){return e.name===t.name&&e.message===t.message&&e.cause===t.cause&&e.stack===t.stack}function DP(e,t){return e===t}function Gs(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.entries(),o,u,c=0;(o=a.next())&&!o.done;){for(var s=t.entries(),f=!1,l=0;(u=s.next())&&!u.done;){if(i[l]){l++;continue}var h=o.value,d=u.value;if(r.equals(h[0],d[0],c,l,e,t,r)&&r.equals(h[1],d[1],h[0],d[0],e,t,r)){f=i[l]=!0;break}l++}if(!f)return!1;c++}return!0}var NP=Vt;function RP(e,t,r){var n=qs(e),i=n.length;if(qs(t).length!==i)return!1;for(;i-- >0;)if(!Wd(e,t,r,n[i]))return!1;return!0}function Rr(e,t,r){var n=Us(e),i=n.length;if(Us(t).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],!Wd(e,t,r,a)||(o=zs(e,a),u=zs(t,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function LP(e,t){return Vt(e.valueOf(),t.valueOf())}function BP(e,t){return e.source===t.source&&e.flags===t.flags}function Hs(e,t,r){var n=e.size;if(n!==t.size)return!1;if(!n)return!0;for(var i=new Array(n),a=e.values(),o,u;(o=a.next())&&!o.done;){for(var c=t.values(),s=!1,f=0;(u=c.next())&&!u.done;){if(!i[f]&&r.equals(o.value,u.value,o.value,u.value,e,t,r)){s=i[f]=!0;break}f++}if(!s)return!1}return!0}function FP(e,t){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(e[r]!==t[r])return!1;return!0}function WP(e,t){return e.hostname===t.hostname&&e.pathname===t.pathname&&e.protocol===t.protocol&&e.port===t.port&&e.hash===t.hash&&e.username===t.username&&e.password===t.password}function Wd(e,t,r,n){return(n===MP||n===jP||n===EP)&&(e.$$typeof||t.$$typeof)?!0:TP(t,n)&&r.equals(e[n],t[n],n,n,e,t,r)}var UP="[object Arguments]",zP="[object Boolean]",qP="[object Date]",GP="[object Error]",HP="[object Map]",KP="[object Number]",XP="[object Object]",VP="[object RegExp]",YP="[object Set]",ZP="[object String]",JP="[object URL]",QP=Array.isArray,Ks=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Xs=Object.assign,e$=Object.prototype.toString.call.bind(Object.prototype.toString);function t$(e){var t=e.areArraysEqual,r=e.areDatesEqual,n=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,o=e.areNumbersEqual,u=e.areObjectsEqual,c=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,l=e.areTypedArraysEqual,h=e.areUrlsEqual;return function(p,v,y){if(p===v)return!0;if(p==null||v==null)return!1;var b=typeof p;if(b!==typeof v)return!1;if(b!=="object")return b==="number"?o(p,v,y):b==="function"?i(p,v,y):!1;var x=p.constructor;if(x!==v.constructor)return!1;if(x===Object)return u(p,v,y);if(QP(p))return t(p,v,y);if(Ks!=null&&Ks(p))return l(p,v,y);if(x===Date)return r(p,v,y);if(x===RegExp)return s(p,v,y);if(x===Map)return a(p,v,y);if(x===Set)return f(p,v,y);var w=e$(p);return w===qP?r(p,v,y):w===VP?s(p,v,y):w===HP?a(p,v,y):w===YP?f(p,v,y):w===XP?typeof p.then!="function"&&typeof v.then!="function"&&u(p,v,y):w===JP?h(p,v,y):w===GP?n(p,v,y):w===UP?u(p,v,y):w===zP||w===KP||w===ZP?c(p,v,y):!1}}function r$(e){var t=e.circular,r=e.createCustomConfig,n=e.strict,i={areArraysEqual:n?Rr:CP,areDatesEqual:kP,areErrorsEqual:IP,areFunctionsEqual:DP,areMapsEqual:n?Ws(Gs,Rr):Gs,areNumbersEqual:NP,areObjectsEqual:n?Rr:RP,arePrimitiveWrappersEqual:LP,areRegExpsEqual:BP,areSetsEqual:n?Ws(Hs,Rr):Hs,areTypedArraysEqual:n?Rr:FP,areUrlsEqual:WP};if(r&&(i=Xs({},i,r(i))),t){var a=ii(i.areArraysEqual),o=ii(i.areMapsEqual),u=ii(i.areObjectsEqual),c=ii(i.areSetsEqual);i=Xs({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function n$(e){return function(t,r,n,i,a,o,u){return e(t,r,u)}}function i$(e){var t=e.circular,r=e.comparator,n=e.createState,i=e.equals,a=e.strict;if(n)return function(c,s){var f=n(),l=f.cache,h=l===void 0?t?new WeakMap:void 0:l,d=f.meta;return r(c,s,{cache:h,equals:i,meta:d,strict:a})};if(t)return function(c,s){return r(c,s,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,s){return r(c,s,o)}}var a$=Mt();Mt({strict:!0});Mt({circular:!0});Mt({circular:!0,strict:!0});Mt({createInternalComparator:function(){return Vt}});Mt({strict:!0,createInternalComparator:function(){return Vt}});Mt({circular:!0,createInternalComparator:function(){return Vt}});Mt({circular:!0,createInternalComparator:function(){return Vt},strict:!0});function Mt(e){e===void 0&&(e={});var t=e.circular,r=t===void 0?!1:t,n=e.createInternalComparator,i=e.createState,a=e.strict,o=a===void 0?!1:a,u=r$(e),c=t$(u),s=n?n(c):n$(c);return i$({circular:r,comparator:c,createState:i,equals:s,strict:o})}function o$(e){typeof requestAnimationFrame<"u"&&requestAnimationFrame(e)}function Vs(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>t?(e(a),r=-1):o$(i)};requestAnimationFrame(n)}function Lo(e){"@babel/helpers - typeof";return Lo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lo(e)}function u$(e){return f$(e)||l$(e)||s$(e)||c$()}function c$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function s$(e,t){if(e){if(typeof e=="string")return Ys(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ys(e,t)}}function Ys(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function l$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function f$(e){if(Array.isArray(e))return e}function h$(){var e={},t=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=u$(o),c=u[0],s=u.slice(1);if(typeof c=="number"){Vs(i.bind(null,s),c);return}i(c),Vs(i.bind(null,s));return}Lo(a)==="object"&&(e=a,t(e)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return t=a,function(){t=function(){return null}}}}}function on(e){"@babel/helpers - typeof";return on=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},on(e)}function Zs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function hr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Zs(Object(r),!0).forEach(function(n){Ti(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ti(e,t,r){return t=d$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d$(e){var t=p$(e,"string");return on(t)==="symbol"?t:String(t)}function p$(e,t){if(on(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(on(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var v$=["Webkit","Moz","O","ms"],y$=["-webkit-","-moz-","-o-","-ms-"],g$=["transform","transformOrigin","transition"],m$=function(t,r){return[Object.keys(t),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},b$=function(t){return t},x$=function(t){return t.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},w$=function(t,r){if(g$.indexOf(t)===-1)return Ti({},t,Number.isNaN(r)?0:r);var n=t==="transition",i=t.replace(/(\w)/,function(o){return o.toUpperCase()}),a=r;return v$.reduce(function(o,u,c){return n&&(a=r.replace(/(transform|transform-origin)/gim,"".concat(y$[c],"$1"))),hr(hr({},o),{},Ti({},u+i,a))},{})},Xr=function(t,r){return Object.keys(r).reduce(function(n,i){return hr(hr({},n),{},Ti({},i,t(i,r[i])))},{})},rc=function(t){return Object.keys(t).reduce(function(r,n){return hr(hr({},r),w$(n,r[n]))},t)},Js=function(t,r,n){return t.map(function(i){return"".concat(x$(i)," ").concat(r,"ms ").concat(n)}).join(",")};function O$(e,t){return _$(e)||A$(e,t)||Ud(e,t)||S$()}function S$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function A$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function _$(e){if(Array.isArray(e))return e}function P$(e){return E$(e)||T$(e)||Ud(e)||$$()}function $$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ud(e,t){if(e){if(typeof e=="string")return Bo(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bo(e,t)}}function T$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function E$(e){if(Array.isArray(e))return Bo(e)}function Bo(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Ei=1e-4,zd=function(t,r){return[0,3*t,3*r-6*t,3*t-3*r+1]},qd=function(t,r){return t.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Qs=function(t,r){return function(n){var i=zd(t,r);return qd(i,n)}},j$=function(t,r){return function(n){var i=zd(t,r),a=[].concat(P$(i.map(function(o,u){return o*u}).slice(1)),[0]);return qd(a,n)}},el=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var s=c[1].split(")")[0].split(",").map(function(y){return parseFloat(y)}),f=O$(s,4);i=f[0],a=f[1],o=f[2],u=f[3]}}}var l=Qs(i,o),h=Qs(a,u),d=j$(i,o),p=function(b){return b>1?1:b<0?0:b},v=function(b){for(var x=b>1?1:b,w=x,O=0;O<8;++O){var g=l(w)-x,m=d(w);if(Math.abs(g-x)<Ei||m<Ei)return h(w);w=p(w-g/m)}return h(w)};return v.isStepper=!1,v},M$=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=t.stiff,n=r===void 0?100:r,i=t.damping,a=i===void 0?8:i,o=t.dt,u=o===void 0?17:o,c=function(f,l,h){var d=-(f-l)*n,p=h*a,v=h+(d-p)*u/1e3,y=h*u/1e3+f;return Math.abs(y-l)<Ei&&Math.abs(v)<Ei?[l,0]:[y,v]};return c.isStepper=!0,c.dt=u,c},C$=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return el(i);case"spring":return M$();default:if(i.split("(")[0]==="cubic-bezier")return el(i)}return typeof i=="function"?i:null};function un(e){"@babel/helpers - typeof";return un=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},un(e)}function tl(e){return D$(e)||I$(e)||Gd(e)||k$()}function k$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function I$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function D$(e){if(Array.isArray(e))return Wo(e)}function rl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Oe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?rl(Object(r),!0).forEach(function(n){Fo(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Fo(e,t,r){return t=N$(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function N$(e){var t=R$(e,"string");return un(t)==="symbol"?t:String(t)}function R$(e,t){if(un(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(un(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function L$(e,t){return W$(e)||F$(e,t)||Gd(e,t)||B$()}function B$(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gd(e,t){if(e){if(typeof e=="string")return Wo(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wo(e,t)}}function Wo(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function F$(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function W$(e){if(Array.isArray(e))return e}var ji=function(t,r,n){return t+(r-t)*n},Uo=function(t){var r=t.from,n=t.to;return r!==n},U$=function e(t,r,n){var i=Xr(function(a,o){if(Uo(o)){var u=t(o.from,o.to,o.velocity),c=L$(u,2),s=c[0],f=c[1];return Oe(Oe({},o),{},{from:s,velocity:f})}return o},r);return n<1?Xr(function(a,o){return Uo(o)?Oe(Oe({},o),{},{velocity:ji(o.velocity,i[a].velocity,n),from:ji(o.from,i[a].from,n)}):o},r):e(t,i,n-1)};const z$=function(e,t,r,n,i){var a=m$(e,t),o=a.reduce(function(y,b){return Oe(Oe({},y),{},Fo({},b,[e[b],t[b]]))},{}),u=a.reduce(function(y,b){return Oe(Oe({},y),{},Fo({},b,{from:e[b],velocity:0,to:t[b]}))},{}),c=-1,s,f,l=function(){return null},h=function(){return Xr(function(b,x){return x.from},u)},d=function(){return!Object.values(u).filter(Uo).length},p=function(b){s||(s=b);var x=b-s,w=x/r.dt;u=U$(r,u,w),i(Oe(Oe(Oe({},e),t),h())),s=b,d()||(c=requestAnimationFrame(l))},v=function(b){f||(f=b);var x=(b-f)/n,w=Xr(function(g,m){return ji.apply(void 0,tl(m).concat([r(x)]))},o);if(i(Oe(Oe(Oe({},e),t),w)),x<1)c=requestAnimationFrame(l);else{var O=Xr(function(g,m){return ji.apply(void 0,tl(m).concat([r(1)]))},o);i(Oe(Oe(Oe({},e),t),O))}};return l=r.isStepper?p:v,function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(c)}}};function dr(e){"@babel/helpers - typeof";return dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dr(e)}var q$=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function G$(e,t){if(e==null)return{};var r=H$(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function H$(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Ya(e){return Y$(e)||V$(e)||X$(e)||K$()}function K$(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function X$(e,t){if(e){if(typeof e=="string")return zo(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zo(e,t)}}function V$(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Y$(e){if(Array.isArray(e))return zo(e)}function zo(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function nl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?nl(Object(r),!0).forEach(function(n){Hr(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Hr(e,t,r){return t=Hd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z$(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function il(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Hd(n.key),n)}}function J$(e,t,r){return t&&il(e.prototype,t),r&&il(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function Hd(e){var t=Q$(e,"string");return dr(t)==="symbol"?t:String(t)}function Q$(e,t){if(dr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(dr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&qo(e,t)}function qo(e,t){return qo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},qo(e,t)}function tT(e){var t=rT();return function(){var n=Mi(e),i;if(t){var a=Mi(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Go(this,i)}}function Go(e,t){if(t&&(dr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Ho(e)}function Ho(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function rT(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Mi(e){return Mi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Mi(e)}var dt=function(e){eT(r,e);var t=tT(r);function r(n,i){var a;Z$(this,r),a=t.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,s=o.from,f=o.to,l=o.steps,h=o.children,d=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Ho(a)),a.changeStyle=a.changeStyle.bind(Ho(a)),!u||d<=0)return a.state={style:{}},typeof h=="function"&&(a.state={style:f}),Go(a);if(l&&l.length)a.state={style:l[0].style};else if(s){if(typeof h=="function")return a.state={style:s},Go(a);a.state={style:c?Hr({},c,s):s}}else a.state={style:{}};return a}return J$(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,s=a.shouldReAnimate,f=a.to,l=a.from,h=this.state.style;if(u){if(!o){var d={style:c?Hr({},c,f):f};this.state&&h&&(c&&h[c]!==f||!c&&h!==f)&&this.setState(d);return}if(!(a$(i.to,f)&&i.canBegin&&i.isActive)){var p=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var v=p||s?l:i.to;if(this.state&&h){var y={style:c?Hr({},c,v):v};(c&&[c]!==v||!c&&h!==v)&&this.setState(y)}this.runAnimation(Ke(Ke({},this.props),{},{from:v,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,s=i.easing,f=i.begin,l=i.onAnimationEnd,h=i.onAnimationStart,d=z$(o,u,C$(s),c,this.changeStyle),p=function(){a.stopJSAnimation=d()};this.manager.start([h,f,p,c,l])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,s=o[0],f=s.style,l=s.duration,h=l===void 0?0:l,d=function(v,y,b){if(b===0)return v;var x=y.duration,w=y.easing,O=w===void 0?"ease":w,g=y.style,m=y.properties,S=y.onAnimationEnd,A=b>0?o[b-1]:y,_=m||Object.keys(g);if(typeof O=="function"||O==="spring")return[].concat(Ya(v),[a.runJSAnimation.bind(a,{from:A.style,to:g,duration:x,easing:O}),x]);var E=Js(_,x,O),$=Ke(Ke(Ke({},A.style),g),{},{transition:E});return[].concat(Ya(v),[$,x,S]).filter(b$)};return this.manager.start([c].concat(Ya(o.reduce(d,[f,Math.max(h,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=h$());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,s=i.easing,f=i.onAnimationStart,l=i.onAnimationEnd,h=i.steps,d=i.children,p=this.manager;if(this.unSubscribe=p.subscribe(this.handleStyleChange),typeof s=="function"||typeof d=="function"||s==="spring"){this.runJSAnimation(i);return}if(h.length>1){this.runStepAnimation(i);return}var v=u?Hr({},u,c):c,y=Js(Object.keys(v),o,s);p.start([f,a,Ke(Ke({},v),{},{transition:y}),o,l])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=G$(i,q$),s=U.Children.count(a),f=rc(this.state.style);if(typeof a=="function")return a(f);if(!u||s===0||o<=0)return a;var l=function(d){var p=d.props,v=p.style,y=v===void 0?{}:v,b=p.className,x=U.cloneElement(d,Ke(Ke({},c),{},{style:Ke(Ke({},y),f),className:b}));return x};return s===1?l(U.Children.only(a)):P.createElement("div",null,U.Children.map(a,function(h){return l(h)}))}}]),r}(U.PureComponent);dt.displayName="Animate";dt.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};dt.propTypes={from:H.oneOfType([H.object,H.string]),to:H.oneOfType([H.object,H.string]),attributeName:H.string,duration:H.number,begin:H.number,easing:H.oneOfType([H.string,H.func]),steps:H.arrayOf(H.shape({duration:H.number.isRequired,style:H.object.isRequired,easing:H.oneOfType([H.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),H.func]),properties:H.arrayOf("string"),onAnimationEnd:H.func})),children:H.oneOfType([H.node,H.func]),isActive:H.bool,canBegin:H.bool,onAnimationEnd:H.func,shouldReAnimate:H.bool,onAnimationStart:H.func,onAnimationReStart:H.func};Number.isFinite===void 0&&(Number.isFinite=function(e){return typeof e=="number"&&isFinite(e)});H.object,H.object,H.object,H.element;H.object,H.object,H.object,H.oneOfType([H.array,H.element]),H.any;function cn(e){"@babel/helpers - typeof";return cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},cn(e)}function ai(e,t,r){return t=nT(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function nT(e){var t=iT(e,"string");return cn(t)==="symbol"?t:String(t)}function iT(e,t){if(cn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(cn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Lr="recharts-tooltip-wrapper",aT={visibility:"hidden"};function oT(e){var t,r=e.coordinate,n=e.translateX,i=e.translateY;return ne(Lr,(t={},ai(t,"".concat(Lr,"-right"),L(n)&&r&&L(r.x)&&n>=r.x),ai(t,"".concat(Lr,"-left"),L(n)&&r&&L(r.x)&&n<r.x),ai(t,"".concat(Lr,"-bottom"),L(i)&&r&&L(r.y)&&i>=r.y),ai(t,"".concat(Lr,"-top"),L(i)&&r&&L(r.y)&&i<r.y),t))}function al(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.key,i=e.offsetTopLeft,a=e.position,o=e.reverseDirection,u=e.tooltipDimension,c=e.viewBox,s=e.viewBoxDimension;if(a&&L(a[n]))return a[n];var f=r[n]-u-i,l=r[n]+i;if(t[n])return o[n]?f:l;if(o[n]){var h=f,d=c[n];return h<d?Math.max(l,c[n]):Math.max(f,c[n])}var p=l+u,v=c[n]+s;return p>v?Math.max(f,c[n]):Math.max(l,c[n])}function uT(e){var t=e.translateX,r=e.translateY,n=e.useTranslate3d;return rc({transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")})}function cT(e){var t=e.allowEscapeViewBox,r=e.coordinate,n=e.offsetTopLeft,i=e.position,a=e.reverseDirection,o=e.tooltipBox,u=e.useTranslate3d,c=e.viewBox,s,f,l;return o.height>0&&o.width>0&&r?(f=al({allowEscapeViewBox:t,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),l=al({allowEscapeViewBox:t,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),s=uT({translateX:f,translateY:l,useTranslate3d:u})):s=aT,{cssProperties:s,cssClasses:oT({translateX:f,translateY:l,coordinate:r})}}function pr(e){"@babel/helpers - typeof";return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pr(e)}function ol(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Za(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ol(Object(r),!0).forEach(function(n){vi(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ol(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ul(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Kd(n.key),n)}}function lT(e,t,r){return t&&ul(e.prototype,t),r&&ul(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function fT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ko(e,t)}function Ko(e,t){return Ko=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ko(e,t)}function hT(e){var t=pT();return function(){var n=Ci(e),i;if(t){var a=Ci(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return dT(this,i)}}function dT(e,t){if(t&&(pr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return pi(e)}function pi(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pT(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Ci(e){return Ci=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ci(e)}function vi(e,t,r){return t=Kd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kd(e){var t=vT(e,"string");return pr(t)==="symbol"?t:String(t)}function vT(e,t){if(pr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(pr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var cl=1,yT=function(e){fT(r,e);var t=hT(r);function r(){var n;sT(this,r);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=t.call.apply(t,[this].concat(a)),vi(pi(n),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),vi(pi(n),"lastBoundingBox",{width:-1,height:-1}),vi(pi(n),"handleKeyDown",function(u){if(u.key==="Escape"){var c,s,f,l;n.setState({dismissed:!0,dismissedAtCoordinate:{x:(c=(s=n.props.coordinate)===null||s===void 0?void 0:s.x)!==null&&c!==void 0?c:0,y:(f=(l=n.props.coordinate)===null||l===void 0?void 0:l.y)!==null&&f!==void 0?f:0}})}}),n}return lT(r,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var i=this.wrapperNode.getBoundingClientRect();(Math.abs(i.width-this.lastBoundingBox.width)>cl||Math.abs(i.height-this.lastBoundingBox.height)>cl)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height)}else(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1)}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var i,a;this.props.active&&this.updateBBox(),this.state.dismissed&&(((i=this.props.coordinate)===null||i===void 0?void 0:i.x)!==this.state.dismissedAtCoordinate.x||((a=this.props.coordinate)===null||a===void 0?void 0:a.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var i=this,a=this.props,o=a.active,u=a.allowEscapeViewBox,c=a.animationDuration,s=a.animationEasing,f=a.children,l=a.coordinate,h=a.hasPayload,d=a.isAnimationActive,p=a.offset,v=a.position,y=a.reverseDirection,b=a.useTranslate3d,x=a.viewBox,w=a.wrapperStyle,O=cT({allowEscapeViewBox:u,coordinate:l,offsetTopLeft:p,position:v,reverseDirection:y,tooltipBox:{height:this.lastBoundingBox.height,width:this.lastBoundingBox.width},useTranslate3d:b,viewBox:x}),g=O.cssClasses,m=O.cssProperties,S=Za(Za(Za({},d&&o&&rc({transition:"transform ".concat(c,"ms ").concat(s)})),m),{},{pointerEvents:"none",visibility:!this.state.dismissed&&o&&h?"visible":"hidden",position:"absolute",top:0,left:0},w);return P.createElement("div",{tabIndex:-1,role:"dialog",className:g,style:S,ref:function(_){i.wrapperNode=_}},f)}}]),r}(U.PureComponent),gT=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},ft={isSsr:gT(),get:function(t){return ft[t]},set:function(t,r){if(typeof t=="string")ft[t]=r;else{var n=Object.keys(t);n&&n.length&&n.forEach(function(i){ft[i]=t[i]})}}};function vr(e){"@babel/helpers - typeof";return vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vr(e)}function sl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ll(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sl(Object(r),!0).forEach(function(n){nc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function mT(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function fl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,Xd(n.key),n)}}function bT(e,t,r){return t&&fl(e.prototype,t),r&&fl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function xT(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xo(e,t)}function Xo(e,t){return Xo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xo(e,t)}function wT(e){var t=AT();return function(){var n=ki(e),i;if(t){var a=ki(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return OT(this,i)}}function OT(e,t){if(t&&(vr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ST(e)}function ST(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function AT(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ki(e){return ki=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ki(e)}function nc(e,t,r){return t=Xd(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Xd(e){var t=_T(e,"string");return vr(t)==="symbol"?t:String(t)}function _T(e,t){if(vr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(vr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function PT(e){return e.dataKey}function $T(e,t){return P.isValidElement(e)?P.cloneElement(e,t):typeof e=="function"?P.createElement(e,t):P.createElement(bP,t)}var tr=function(e){xT(r,e);var t=wT(r);function r(){return mT(this,r),t.apply(this,arguments)}return bT(r,[{key:"render",value:function(){var i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,s=i.content,f=i.coordinate,l=i.filterNull,h=i.isAnimationActive,d=i.offset,p=i.payload,v=i.payloadUniqBy,y=i.position,b=i.reverseDirection,x=i.useTranslate3d,w=i.viewBox,O=i.wrapperStyle,g=p??[];l&&g.length&&(g=Md(p.filter(function(S){return S.value!=null}),v,PT));var m=g.length>0;return P.createElement(yT,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:h,active:a,coordinate:f,hasPayload:m,offset:d,position:y,reverseDirection:b,useTranslate3d:x,viewBox:w,wrapperStyle:O},$T(s,ll(ll({},this.props),{},{payload:g})))}}]),r}(U.PureComponent);nc(tr,"displayName","Tooltip");nc(tr,"defaultProps",{allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ft.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var TT=ot,ET=function(){return TT.Date.now()},jT=ET,MT=/\s/;function CT(e){for(var t=e.length;t--&&MT.test(e.charAt(t)););return t}var kT=CT,IT=kT,DT=/^\s+/;function NT(e){return e&&e.slice(0,IT(e)+1).replace(DT,"")}var RT=NT,LT=RT,hl=Le,BT=Pr,dl=0/0,FT=/^[-+]0x[0-9a-f]+$/i,WT=/^0b[01]+$/i,UT=/^0o[0-7]+$/i,zT=parseInt;function qT(e){if(typeof e=="number")return e;if(BT(e))return dl;if(hl(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=hl(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=LT(e);var r=WT.test(e);return r||UT.test(e)?zT(e.slice(2),r?2:8):FT.test(e)?dl:+e}var Vd=qT,GT=Le,Ja=jT,pl=Vd,HT="Expected a function",KT=Math.max,XT=Math.min;function VT(e,t,r){var n,i,a,o,u,c,s=0,f=!1,l=!1,h=!0;if(typeof e!="function")throw new TypeError(HT);t=pl(t)||0,GT(r)&&(f=!!r.leading,l="maxWait"in r,a=l?KT(pl(r.maxWait)||0,t):a,h="trailing"in r?!!r.trailing:h);function d(m){var S=n,A=i;return n=i=void 0,s=m,o=e.apply(A,S),o}function p(m){return s=m,u=setTimeout(b,t),f?d(m):o}function v(m){var S=m-c,A=m-s,_=t-S;return l?XT(_,a-A):_}function y(m){var S=m-c,A=m-s;return c===void 0||S>=t||S<0||l&&A>=a}function b(){var m=Ja();if(y(m))return x(m);u=setTimeout(b,v(m))}function x(m){return u=void 0,h&&n?d(m):(n=i=void 0,o)}function w(){u!==void 0&&clearTimeout(u),s=0,n=c=i=u=void 0}function O(){return u===void 0?o:x(Ja())}function g(){var m=Ja(),S=y(m);if(n=arguments,i=this,c=m,S){if(u===void 0)return p(c);if(l)return clearTimeout(u),u=setTimeout(b,t),d(c)}return u===void 0&&(u=setTimeout(b,t)),o}return g.cancel=w,g.flush=O,g}var YT=VT,ZT=YT,JT=Le,QT="Expected a function";function eE(e,t,r){var n=!0,i=!0;if(typeof e!="function")throw new TypeError(QT);return JT(r)&&(n="leading"in r?!!r.leading:n,i="trailing"in r?!!r.trailing:i),ZT(e,t,{leading:n,maxWait:t,trailing:i})}var Yd=eE;function sn(e){"@babel/helpers - typeof";return sn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},sn(e)}function vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function oi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vl(Object(r),!0).forEach(function(n){tE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function tE(e,t,r){return t=rE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function rE(e){var t=nE(e,"string");return sn(t)==="symbol"?t:String(t)}function nE(e,t){if(sn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(sn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function iE(e,t){return cE(e)||uE(e,t)||oE(e,t)||aE()}function aE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oE(e,t){if(e){if(typeof e=="string")return yl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yl(e,t)}}function yl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function cE(e){if(Array.isArray(e))return e}var KL=U.forwardRef(function(e,t){var r=e.aspect,n=e.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=e.width,o=a===void 0?"100%":a,u=e.height,c=u===void 0?"100%":u,s=e.minWidth,f=s===void 0?0:s,l=e.minHeight,h=e.maxHeight,d=e.children,p=e.debounce,v=p===void 0?0:p,y=e.id,b=e.className,x=e.onResize,w=e.style,O=w===void 0?{}:w,g=U.useRef(null),m=U.useRef();m.current=x,U.useImperativeHandle(t,function(){return g});var S=U.useState({containerWidth:i.width,containerHeight:i.height}),A=iE(S,2),_=A[0],E=A[1],$=U.useCallback(function(M,k){E(function(C){var N=Math.round(M),I=Math.round(k);return C.containerWidth===N&&C.containerHeight===I?C:{containerWidth:N,containerHeight:I}})},[]);U.useEffect(function(){var M=function(B){var q,re=B[0].contentRect,G=re.width,ee=re.height;$(G,ee),(q=m.current)===null||q===void 0||q.call(m,G,ee)};v>0&&(M=Yd(M,v,{trailing:!0,leading:!1}));var k=new ResizeObserver(M),C=g.current.getBoundingClientRect(),N=C.width,I=C.height;return $(N,I),k.observe(g.current),function(){k.disconnect()}},[$,v]);var T=U.useMemo(function(){var M=_.containerWidth,k=_.containerHeight;if(M<0||k<0)return null;qt(Wt(o)||Wt(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),qt(!r||r>0,"The aspect(%s) must be greater than zero.",r);var C=Wt(o)?M:o,N=Wt(c)?k:c;r&&r>0&&(C?N=C/r:N&&(C=N*r),h&&N>h&&(N=h)),qt(C>0||N>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,C,N,o,c,f,l,r);var I=!Array.isArray(d)&&go.isElement(d)&&lt(d.type).endsWith("Chart");return P.Children.map(d,function(R){return go.isElement(R)?U.cloneElement(R,oi({width:C,height:N},I?{style:oi({height:"100%",width:"100%",maxHeight:N,maxWidth:C},R.props.style)}:{})):R})},[r,d,c,h,l,f,_,o]);return P.createElement("div",{id:y?"".concat(y):void 0,className:ne("recharts-responsive-container",b),style:oi(oi({},O),{},{width:o,height:c,minWidth:f,minHeight:l,maxHeight:h}),ref:g},T)}),Zd=function(t){return null};Zd.displayName="Cell";function ln(e){"@babel/helpers - typeof";return ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ln(e)}function gl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Vo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gl(Object(r),!0).forEach(function(n){sE(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gl(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function sE(e,t,r){return t=lE(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lE(e){var t=fE(e,"string");return ln(t)==="symbol"?t:String(t)}function fE(e,t){if(ln(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(ln(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jt={widthCache:{},cacheCount:0},hE=2e3,dE={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},ml="recharts_measurement_span";function pE(e){var t=Vo({},e);return Object.keys(t).forEach(function(r){t[r]||delete t[r]}),t}var Vr=function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t==null||ft.isSsr)return{width:0,height:0};var n=pE(r),i=JSON.stringify({text:t,copyStyle:n});if(Jt.widthCache[i])return Jt.widthCache[i];try{var a=document.getElementById(ml);a||(a=document.createElement("span"),a.setAttribute("id",ml),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=Vo(Vo({},dE),n);Object.assign(a.style,o),a.textContent="".concat(t);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return Jt.widthCache[i]=c,++Jt.cacheCount>hE&&(Jt.cacheCount=0,Jt.widthCache={}),c}catch{return{width:0,height:0}}},vE=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}};function fn(e){"@babel/helpers - typeof";return fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fn(e)}function Ii(e,t){return bE(e)||mE(e,t)||gE(e,t)||yE()}function yE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gE(e,t){if(e){if(typeof e=="string")return bl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bl(e,t)}}function bl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function mE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function bE(e){if(Array.isArray(e))return e}function xE(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function xl(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,OE(n.key),n)}}function wE(e,t,r){return t&&xl(e.prototype,t),r&&xl(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function OE(e){var t=SE(e,"string");return fn(t)==="symbol"?t:String(t)}function SE(e,t){if(fn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(fn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wl=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Ol=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,AE=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,_E=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Jd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},PE=Object.keys(Jd),rr="NaN";function $E(e,t){return e*Jd[t]}var ui=function(){function e(t,r){xE(this,e),this.num=t,this.unit=r,this.num=t,this.unit=r,Number.isNaN(t)&&(this.unit=""),r!==""&&!AE.test(r)&&(this.num=NaN,this.unit=""),PE.includes(r)&&(this.num=$E(t,r),this.unit="px")}return wE(e,[{key:"add",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new e(NaN,""):new e(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new e(NaN,""):new e(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=_E.exec(r))!==null&&n!==void 0?n:[],a=Ii(i,3),o=a[1],u=a[2];return new e(parseFloat(o),u??"")}}]),e}();function Qd(e){if(e.includes(rr))return rr;for(var t=e;t.includes("*")||t.includes("/");){var r,n=(r=wl.exec(t))!==null&&r!==void 0?r:[],i=Ii(n,4),a=i[1],o=i[2],u=i[3],c=ui.parse(a??""),s=ui.parse(u??""),f=o==="*"?c.multiply(s):c.divide(s);if(f.isNaN())return rr;t=t.replace(wl,f.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var l,h=(l=Ol.exec(t))!==null&&l!==void 0?l:[],d=Ii(h,4),p=d[1],v=d[2],y=d[3],b=ui.parse(p??""),x=ui.parse(y??""),w=v==="+"?b.add(x):b.subtract(x);if(w.isNaN())return rr;t=t.replace(Ol,w.toString())}return t}var Sl=/\(([^()]*)\)/;function TE(e){for(var t=e;t.includes("(");){var r=Sl.exec(t),n=Ii(r,2),i=n[1];t=t.replace(Sl,Qd(i))}return t}function EE(e){var t=e.replace(/\s+/g,"");return t=TE(t),t=Qd(t),t}function jE(e){try{return EE(e)}catch{return rr}}function Qa(e){var t=jE(e.slice(5,-1));return t===rr?"":t}var ME=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],CE=["dx","dy","angle","className","breakAll"];function Yo(){return Yo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yo.apply(this,arguments)}function Al(e,t){if(e==null)return{};var r=kE(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function kE(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function _l(e,t){return RE(e)||NE(e,t)||DE(e,t)||IE()}function IE(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DE(e,t){if(e){if(typeof e=="string")return Pl(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pl(e,t)}}function Pl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function NE(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function RE(e){if(Array.isArray(e))return e}var ep=/[ \f\n\r\t\v\u2028\u2029]+/,tp=function(t){var r=t.children,n=t.breakAll,i=t.style;try{var a=[];J(r)||(n?a=r.toString().split(""):a=r.toString().split(ep));var o=a.map(function(c){return{word:c,width:Vr(c,i).width}}),u=n?0:Vr(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},LE=function(t,r,n,i,a){var o=t.maxLines,u=t.children,c=t.style,s=t.breakAll,f=L(o),l=u,h=function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return C.reduce(function(N,I){var R=I.word,B=I.width,q=N[N.length-1];if(q&&(i==null||a||q.width+B+n<Number(i)))q.words.push(R),q.width+=B+n;else{var re={words:[R],width:B};N.push(re)}return N},[])},d=h(r),p=function(C){return C.reduce(function(N,I){return N.width>I.width?N:I})};if(!f)return d;for(var v="…",y=function(C){var N=l.slice(0,C),I=tp({breakAll:s,style:c,children:N+v}).wordsWithComputedWidth,R=h(I),B=R.length>o||p(R).width>Number(i);return[B,R]},b=0,x=l.length-1,w=0,O;b<=x&&w<=l.length-1;){var g=Math.floor((b+x)/2),m=g-1,S=y(m),A=_l(S,2),_=A[0],E=A[1],$=y(g),T=_l($,1),M=T[0];if(!_&&!M&&(b=g+1),_&&M&&(x=g-1),!_&&M){O=E;break}w++}return O||d},$l=function(t){var r=J(t)?[]:t.toString().split(ep);return[{words:r}]},BE=function(t){var r=t.width,n=t.scaleToFit,i=t.children,a=t.style,o=t.breakAll,u=t.maxLines;if((r||n)&&!ft.isSsr){var c,s,f=tp({breakAll:o,children:i,style:a});if(f){var l=f.wordsWithComputedWidth,h=f.spaceWidth;c=l,s=h}else return $l(i);return LE({breakAll:o,children:i,maxLines:u,style:a},c,s,r,n)}return $l(i)},Tl="#808080",Di=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.lineHeight,u=o===void 0?"1em":o,c=t.capHeight,s=c===void 0?"0.71em":c,f=t.scaleToFit,l=f===void 0?!1:f,h=t.textAnchor,d=h===void 0?"start":h,p=t.verticalAnchor,v=p===void 0?"end":p,y=t.fill,b=y===void 0?Tl:y,x=Al(t,ME),w=U.useMemo(function(){return BE({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:l,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,l,x.style,x.width]),O=x.dx,g=x.dy,m=x.angle,S=x.className,A=x.breakAll,_=Al(x,CE);if(!ye(n)||!ye(a))return null;var E=n+(L(O)?O:0),$=a+(L(g)?g:0),T;switch(v){case"start":T=Qa("calc(".concat(s,")"));break;case"middle":T=Qa("calc(".concat((w.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:T=Qa("calc(".concat(w.length-1," * -").concat(u,")"));break}var M=[];if(l){var k=w[0].width,C=x.width;M.push("scale(".concat((L(C)?C/k:1)/k,")"))}return m&&M.push("rotate(".concat(m,", ").concat(E,", ").concat($,")")),M.length&&(_.transform=M.join(" ")),P.createElement("text",Yo({},Z(_,!0),{x:E,y:$,className:ne("recharts-text",S),textAnchor:d,fill:b.includes("url")?Tl:b}),w.map(function(N,I){var R=N.words.join(A?"":" ");return P.createElement("tspan",{x:E,dy:I===0?T:u,key:R},R)}))};function Tt(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function FE(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ic(e){let t,r,n;e.length!==2?(t=Tt,r=(u,c)=>Tt(e(u),c),n=(u,c)=>e(u)-c):(t=e===Tt||e===FE?e:WE,r=e,n=e);function i(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<0?s=l+1:f=l}while(s<f)}return s}function a(u,c,s=0,f=u.length){if(s<f){if(t(c,c)!==0)return f;do{const l=s+f>>>1;r(u[l],c)<=0?s=l+1:f=l}while(s<f)}return s}function o(u,c,s=0,f=u.length){const l=i(u,c,s,f-1);return l>s&&n(u[l-1],c)>-n(u[l],c)?l-1:l}return{left:i,center:o,right:a}}function WE(){return 0}function rp(e){return e===null?NaN:+e}function*UE(e,t){if(t===void 0)for(let r of e)r!=null&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of e)(n=t(n,++r,e))!=null&&(n=+n)>=n&&(yield n)}}const zE=ic(Tt),qE=zE.right;ic(rp).center;const Hn=qE;class El extends Map{constructor(t,r=KE){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:r}}),t!=null)for(const[n,i]of t)this.set(n,i)}get(t){return super.get(jl(this,t))}has(t){return super.has(jl(this,t))}set(t,r){return super.set(GE(this,t),r)}delete(t){return super.delete(HE(this,t))}}function jl({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):r}function GE({_intern:e,_key:t},r){const n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}function HE({_intern:e,_key:t},r){const n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}function KE(e){return e!==null&&typeof e=="object"?e.valueOf():e}function XE(e=Tt){if(e===Tt)return np;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,r)=>{const n=e(t,r);return n||n===0?n:(e(r,r)===0)-(e(t,t)===0)}}function np(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const VE=Math.sqrt(50),YE=Math.sqrt(10),ZE=Math.sqrt(2);function Ni(e,t,r){const n=(t-e)/Math.max(0,r),i=Math.floor(Math.log10(n)),a=n/Math.pow(10,i),o=a>=VE?10:a>=YE?5:a>=ZE?2:1;let u,c,s;return i<0?(s=Math.pow(10,-i)/o,u=Math.round(e*s),c=Math.round(t*s),u/s<e&&++u,c/s>t&&--c,s=-s):(s=Math.pow(10,i)*o,u=Math.round(e/s),c=Math.round(t/s),u*s<e&&++u,c*s>t&&--c),c<u&&.5<=r&&r<2?Ni(e,t,r*2):[u,c,s]}function Zo(e,t,r){if(t=+t,e=+e,r=+r,!(r>0))return[];if(e===t)return[e];const n=t<e,[i,a,o]=n?Ni(t,e,r):Ni(e,t,r);if(!(a>=i))return[];const u=a-i+1,c=new Array(u);if(n)if(o<0)for(let s=0;s<u;++s)c[s]=(a-s)/-o;else for(let s=0;s<u;++s)c[s]=(a-s)*o;else if(o<0)for(let s=0;s<u;++s)c[s]=(i+s)/-o;else for(let s=0;s<u;++s)c[s]=(i+s)*o;return c}function Jo(e,t,r){return t=+t,e=+e,r=+r,Ni(e,t,r)[2]}function Qo(e,t,r){t=+t,e=+e,r=+r;const n=t<e,i=n?Jo(t,e,r):Jo(e,t,r);return(n?-1:1)*(i<0?1/-i:i)}function Ml(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r<n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r<i||r===void 0&&i>=i)&&(r=i)}return r}function Cl(e,t){let r;if(t===void 0)for(const n of e)n!=null&&(r>n||r===void 0&&n>=n)&&(r=n);else{let n=-1;for(let i of e)(i=t(i,++n,e))!=null&&(r>i||r===void 0&&i>=i)&&(r=i)}return r}function ip(e,t,r=0,n=1/0,i){if(t=Math.floor(t),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(e.length-1,n)),!(r<=t&&t<=n))return e;for(i=i===void 0?np:XE(i);n>r;){if(n-r>600){const c=n-r+1,s=t-r+1,f=Math.log(c),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(c-l)/c)*(s-c/2<0?-1:1),d=Math.max(r,Math.floor(t-s*l/c+h)),p=Math.min(n,Math.floor(t+(c-s)*l/c+h));ip(e,t,d,p,i)}const a=e[t];let o=r,u=n;for(Br(e,r,t),i(e[n],a)>0&&Br(e,r,n);o<u;){for(Br(e,o,u),++o,--u;i(e[o],a)<0;)++o;for(;i(e[u],a)>0;)--u}i(e[r],a)===0?Br(e,r,u):(++u,Br(e,u,n)),u<=t&&(r=u+1),t<=u&&(n=u-1)}return e}function Br(e,t,r){const n=e[t];e[t]=e[r],e[r]=n}function JE(e,t,r){if(e=Float64Array.from(UE(e,r)),!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return Cl(e);if(t>=1)return Ml(e);var n,i=(n-1)*t,a=Math.floor(i),o=Ml(ip(e,a).subarray(0,a+1)),u=Cl(e.subarray(a+1));return o+(u-o)*(i-a)}}function QE(e,t,r=rp){if(!(!(n=e.length)||isNaN(t=+t))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e),u=+r(e[a+1],a+1,e);return o+(u-o)*(i-a)}}function ej(e,t,r){e=+e,t=+t,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=Math.max(0,Math.ceil((t-e)/r))|0,a=new Array(i);++n<i;)a[n]=e+n*r;return a}function He(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function bt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const eu=Symbol("implicit");function ac(){var e=new El,t=[],r=[],n=eu;function i(a){let o=e.get(a);if(o===void 0){if(n!==eu)return n;e.set(a,o=t.push(a)-1)}return r[o%r.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new El;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(r=Array.from(a),i):r.slice()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return ac(t,r).unknown(n)},He.apply(i,arguments),i}function hn(){var e=ac().unknown(void 0),t=e.domain,r=e.range,n=0,i=1,a,o,u=!1,c=0,s=0,f=.5;delete e.unknown;function l(){var h=t().length,d=i<n,p=d?i:n,v=d?n:i;a=(v-p)/Math.max(1,h-c+s*2),u&&(a=Math.floor(a)),p+=(v-p-a*(h-c))*f,o=a*(1-c),u&&(p=Math.round(p),o=Math.round(o));var y=ej(h).map(function(b){return p+a*b});return r(d?y.reverse():y)}return e.domain=function(h){return arguments.length?(t(h),l()):t()},e.range=function(h){return arguments.length?([n,i]=h,n=+n,i=+i,l()):[n,i]},e.rangeRound=function(h){return[n,i]=h,n=+n,i=+i,u=!0,l()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(h){return arguments.length?(u=!!h,l()):u},e.padding=function(h){return arguments.length?(c=Math.min(1,s=+h),l()):c},e.paddingInner=function(h){return arguments.length?(c=Math.min(1,h),l()):c},e.paddingOuter=function(h){return arguments.length?(s=+h,l()):s},e.align=function(h){return arguments.length?(f=Math.max(0,Math.min(1,h)),l()):f},e.copy=function(){return hn(t(),[n,i]).round(u).paddingInner(c).paddingOuter(s).align(f)},He.apply(l(),arguments)}function ap(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return ap(t())},e}function Yr(){return ap(hn.apply(null,arguments).paddingInner(1))}function oc(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function op(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function Kn(){}var dn=.7,Ri=1/dn,or="\\s*([+-]?\\d+)\\s*",pn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",nt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",tj=/^#([0-9a-f]{3,8})$/,rj=new RegExp(`^rgb\\(${or},${or},${or}\\)$`),nj=new RegExp(`^rgb\\(${nt},${nt},${nt}\\)$`),ij=new RegExp(`^rgba\\(${or},${or},${or},${pn}\\)$`),aj=new RegExp(`^rgba\\(${nt},${nt},${nt},${pn}\\)$`),oj=new RegExp(`^hsl\\(${pn},${nt},${nt}\\)$`),uj=new RegExp(`^hsla\\(${pn},${nt},${nt},${pn}\\)$`),kl={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};oc(Kn,vn,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Il,formatHex:Il,formatHex8:cj,formatHsl:sj,formatRgb:Dl,toString:Dl});function Il(){return this.rgb().formatHex()}function cj(){return this.rgb().formatHex8()}function sj(){return up(this).formatHsl()}function Dl(){return this.rgb().formatRgb()}function vn(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=tj.exec(e))?(r=t[1].length,t=parseInt(t[1],16),r===6?Nl(t):r===3?new Ee(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):r===8?ci(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):r===4?ci(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=rj.exec(e))?new Ee(t[1],t[2],t[3],1):(t=nj.exec(e))?new Ee(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=ij.exec(e))?ci(t[1],t[2],t[3],t[4]):(t=aj.exec(e))?ci(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=oj.exec(e))?Bl(t[1],t[2]/100,t[3]/100,1):(t=uj.exec(e))?Bl(t[1],t[2]/100,t[3]/100,t[4]):kl.hasOwnProperty(e)?Nl(kl[e]):e==="transparent"?new Ee(NaN,NaN,NaN,0):null}function Nl(e){return new Ee(e>>16&255,e>>8&255,e&255,1)}function ci(e,t,r,n){return n<=0&&(e=t=r=NaN),new Ee(e,t,r,n)}function lj(e){return e instanceof Kn||(e=vn(e)),e?(e=e.rgb(),new Ee(e.r,e.g,e.b,e.opacity)):new Ee}function tu(e,t,r,n){return arguments.length===1?lj(e):new Ee(e,t,r,n??1)}function Ee(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}oc(Ee,tu,op(Kn,{brighter(e){return e=e==null?Ri:Math.pow(Ri,e),new Ee(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?dn:Math.pow(dn,e),new Ee(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ee(Gt(this.r),Gt(this.g),Gt(this.b),Li(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Rl,formatHex:Rl,formatHex8:fj,formatRgb:Ll,toString:Ll}));function Rl(){return`#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}`}function fj(){return`#${Ut(this.r)}${Ut(this.g)}${Ut(this.b)}${Ut((isNaN(this.opacity)?1:this.opacity)*255)}`}function Ll(){const e=Li(this.opacity);return`${e===1?"rgb(":"rgba("}${Gt(this.r)}, ${Gt(this.g)}, ${Gt(this.b)}${e===1?")":`, ${e})`}`}function Li(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Gt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Ut(e){return e=Gt(e),(e<16?"0":"")+e.toString(16)}function Bl(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new Ze(e,t,r,n)}function up(e){if(e instanceof Ze)return new Ze(e.h,e.s,e.l,e.opacity);if(e instanceof Kn||(e=vn(e)),!e)return new Ze;if(e instanceof Ze)return e;e=e.rgb();var t=e.r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,u=a-i,c=(a+i)/2;return u?(t===a?o=(r-n)/u+(r<n)*6:r===a?o=(n-t)/u+2:o=(t-r)/u+4,u/=c<.5?a+i:2-a-i,o*=60):u=c>0&&c<1?0:o,new Ze(o,u,c,e.opacity)}function hj(e,t,r,n){return arguments.length===1?up(e):new Ze(e,t,r,n??1)}function Ze(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}oc(Ze,hj,op(Kn,{brighter(e){return e=e==null?Ri:Math.pow(Ri,e),new Ze(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?dn:Math.pow(dn,e),new Ze(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Ee(eo(e>=240?e-240:e+120,i,n),eo(e,i,n),eo(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new Ze(Fl(this.h),si(this.s),si(this.l),Li(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Li(this.opacity);return`${e===1?"hsl(":"hsla("}${Fl(this.h)}, ${si(this.s)*100}%, ${si(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Fl(e){return e=(e||0)%360,e<0?e+360:e}function si(e){return Math.max(0,Math.min(1,e||0))}function eo(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}const uc=e=>()=>e;function dj(e,t){return function(r){return e+r*t}}function pj(e,t,r){return e=Math.pow(e,r),t=Math.pow(t,r)-e,r=1/r,function(n){return Math.pow(e+n*t,r)}}function vj(e){return(e=+e)==1?cp:function(t,r){return r-t?pj(t,r,e):uc(isNaN(t)?r:t)}}function cp(e,t){var r=t-e;return r?dj(e,r):uc(isNaN(e)?t:e)}const Wl=function e(t){var r=vj(t);function n(i,a){var o=r((i=tu(i)).r,(a=tu(a)).r),u=r(i.g,a.g),c=r(i.b,a.b),s=cp(i.opacity,a.opacity);return function(f){return i.r=o(f),i.g=u(f),i.b=c(f),i.opacity=s(f),i+""}}return n.gamma=e,n}(1);function yj(e,t){t||(t=[]);var r=e?Math.min(t.length,e.length):0,n=t.slice(),i;return function(a){for(i=0;i<r;++i)n[i]=e[i]*(1-a)+t[i]*a;return n}}function gj(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function mj(e,t){var r=t?t.length:0,n=e?Math.min(r,e.length):0,i=new Array(n),a=new Array(r),o;for(o=0;o<n;++o)i[o]=kr(e[o],t[o]);for(;o<r;++o)a[o]=t[o];return function(u){for(o=0;o<n;++o)a[o]=i[o](u);return a}}function bj(e,t){var r=new Date;return e=+e,t=+t,function(n){return r.setTime(e*(1-n)+t*n),r}}function Bi(e,t){return e=+e,t=+t,function(r){return e*(1-r)+t*r}}function xj(e,t){var r={},n={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?r[i]=kr(e[i],t[i]):n[i]=t[i];return function(a){for(i in r)n[i]=r[i](a);return n}}var ru=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,to=new RegExp(ru.source,"g");function wj(e){return function(){return e}}function Oj(e){return function(t){return e(t)+""}}function Sj(e,t){var r=ru.lastIndex=to.lastIndex=0,n,i,a,o=-1,u=[],c=[];for(e=e+"",t=t+"";(n=ru.exec(e))&&(i=to.exec(t));)(a=i.index)>r&&(a=t.slice(r,a),u[o]?u[o]+=a:u[++o]=a),(n=n[0])===(i=i[0])?u[o]?u[o]+=i:u[++o]=i:(u[++o]=null,c.push({i:o,x:Bi(n,i)})),r=to.lastIndex;return r<t.length&&(a=t.slice(r),u[o]?u[o]+=a:u[++o]=a),u.length<2?c[0]?Oj(c[0].x):wj(t):(t=c.length,function(s){for(var f=0,l;f<t;++f)u[(l=c[f]).i]=l.x(s);return u.join("")})}function kr(e,t){var r=typeof t,n;return t==null||r==="boolean"?uc(t):(r==="number"?Bi:r==="string"?(n=vn(t))?(t=n,Wl):Sj:t instanceof vn?Wl:t instanceof Date?bj:gj(t)?yj:Array.isArray(t)?mj:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?xj:Bi)(e,t)}function cc(e,t){return e=+e,t=+t,function(r){return Math.round(e*(1-r)+t*r)}}function Aj(e,t){t===void 0&&(t=e,e=kr);for(var r=0,n=t.length-1,i=t[0],a=new Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(o){var u=Math.max(0,Math.min(n-1,Math.floor(o*=n)));return a[u](o-u)}}function _j(e){return function(){return e}}function Fi(e){return+e}var Ul=[0,1];function $e(e){return e}function nu(e,t){return(t-=e=+e)?function(r){return(r-e)/t}:_j(isNaN(t)?NaN:.5)}function Pj(e,t){var r;return e>t&&(r=e,e=t,t=r),function(n){return Math.max(e,Math.min(t,n))}}function $j(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=nu(i,n),a=r(o,a)):(n=nu(n,i),a=r(a,o)),function(u){return a(n(u))}}function Tj(e,t,r){var n=Math.min(e.length,t.length)-1,i=new Array(n),a=new Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=nu(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(u){var c=Hn(e,u,1,n)-1;return a[c](i[c](u))}}function Xn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function Oa(){var e=Ul,t=Ul,r=kr,n,i,a,o=$e,u,c,s;function f(){var h=Math.min(e.length,t.length);return o!==$e&&(o=Pj(e[0],e[h-1])),u=h>2?Tj:$j,c=s=null,l}function l(h){return h==null||isNaN(h=+h)?a:(c||(c=u(e.map(n),t,r)))(n(o(h)))}return l.invert=function(h){return o(i((s||(s=u(t,e.map(n),Bi)))(h)))},l.domain=function(h){return arguments.length?(e=Array.from(h,Fi),f()):e.slice()},l.range=function(h){return arguments.length?(t=Array.from(h),f()):t.slice()},l.rangeRound=function(h){return t=Array.from(h),r=cc,f()},l.clamp=function(h){return arguments.length?(o=h?!0:$e,f()):o!==$e},l.interpolate=function(h){return arguments.length?(r=h,f()):r},l.unknown=function(h){return arguments.length?(a=h,l):a},function(h,d){return n=h,i=d,f()}}function sc(){return Oa()($e,$e)}function Ej(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Wi(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function yr(e){return e=Wi(Math.abs(e)),e?e[1]:NaN}function jj(e,t){return function(r,n){for(var i=r.length,a=[],o=0,u=e[0],c=0;i>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),a.push(r.substring(i-=u,i+u)),!((c+=u+1)>n));)u=e[o=(o+1)%e.length];return a.reverse().join(t)}}function Mj(e){return function(t){return t.replace(/[0-9]/g,function(r){return e[+r]})}}var Cj=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function yn(e){if(!(t=Cj.exec(e)))throw new Error("invalid format: "+e);var t;return new lc({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}yn.prototype=lc.prototype;function lc(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}lc.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function kj(e){e:for(var t=e.length,r=1,n=-1,i;r<t;++r)switch(e[r]){case".":n=i=r;break;case"0":n===0&&(n=r),i=r;break;default:if(!+e[r])break e;n>0&&(n=0);break}return n>0?e.slice(0,n)+e.slice(i+1):e}var sp;function Ij(e,t){var r=Wi(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(sp=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=n.length;return a===o?n:a>o?n+new Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+new Array(1-a).join("0")+Wi(e,Math.max(0,t+a-1))[0]}function zl(e,t){var r=Wi(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+new Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+new Array(i-n.length+2).join("0")}const ql={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:Ej,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>zl(e*100,t),r:zl,s:Ij,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Gl(e){return e}var Hl=Array.prototype.map,Kl=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Dj(e){var t=e.grouping===void 0||e.thousands===void 0?Gl:jj(Hl.call(e.grouping,Number),e.thousands+""),r=e.currency===void 0?"":e.currency[0]+"",n=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?Gl:Mj(Hl.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",u=e.minus===void 0?"−":e.minus+"",c=e.nan===void 0?"NaN":e.nan+"";function s(l){l=yn(l);var h=l.fill,d=l.align,p=l.sign,v=l.symbol,y=l.zero,b=l.width,x=l.comma,w=l.precision,O=l.trim,g=l.type;g==="n"?(x=!0,g="g"):ql[g]||(w===void 0&&(w=12),O=!0,g="g"),(y||h==="0"&&d==="=")&&(y=!0,h="0",d="=");var m=v==="$"?r:v==="#"&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",S=v==="$"?n:/[%p]/.test(g)?o:"",A=ql[g],_=/[defgprs%]/.test(g);w=w===void 0?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,w)):Math.max(0,Math.min(20,w));function E($){var T=m,M=S,k,C,N;if(g==="c")M=A($)+M,$="";else{$=+$;var I=$<0||1/$<0;if($=isNaN($)?c:A(Math.abs($),w),O&&($=kj($)),I&&+$==0&&p!=="+"&&(I=!1),T=(I?p==="("?p:u:p==="-"||p==="("?"":p)+T,M=(g==="s"?Kl[8+sp/3]:"")+M+(I&&p==="("?")":""),_){for(k=-1,C=$.length;++k<C;)if(N=$.charCodeAt(k),48>N||N>57){M=(N===46?i+$.slice(k+1):$.slice(k))+M,$=$.slice(0,k);break}}}x&&!y&&($=t($,1/0));var R=T.length+$.length+M.length,B=R<b?new Array(b-R+1).join(h):"";switch(x&&y&&($=t(B+$,B.length?b-M.length:1/0),B=""),d){case"<":$=T+$+M+B;break;case"=":$=T+B+$+M;break;case"^":$=B.slice(0,R=B.length>>1)+T+$+M+B.slice(R);break;default:$=B+T+$+M;break}return a($)}return E.toString=function(){return l+""},E}function f(l,h){var d=s((l=yn(l),l.type="f",l)),p=Math.max(-8,Math.min(8,Math.floor(yr(h)/3)))*3,v=Math.pow(10,-p),y=Kl[8+p/3];return function(b){return d(v*b)+y}}return{format:s,formatPrefix:f}}var li,fc,lp;Nj({thousands:",",grouping:[3],currency:["$",""]});function Nj(e){return li=Dj(e),fc=li.format,lp=li.formatPrefix,li}function Rj(e){return Math.max(0,-yr(Math.abs(e)))}function Lj(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(yr(t)/3)))*3-yr(Math.abs(e)))}function Bj(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,yr(t)-yr(e))+1}function fp(e,t,r,n){var i=Qo(e,t,r),a;switch(n=yn(n??",f"),n.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return n.precision==null&&!isNaN(a=Lj(i,o))&&(n.precision=a),lp(n,o)}case"":case"e":case"g":case"p":case"r":{n.precision==null&&!isNaN(a=Bj(i,Math.max(Math.abs(e),Math.abs(t))))&&(n.precision=a-(n.type==="e"));break}case"f":case"%":{n.precision==null&&!isNaN(a=Rj(i))&&(n.precision=a-(n.type==="%")*2);break}}return fc(n)}function Ct(e){var t=e.domain;return e.ticks=function(r){var n=t();return Zo(n[0],n[n.length-1],r??10)},e.tickFormat=function(r,n){var i=t();return fp(i[0],i[i.length-1],r??10,n)},e.nice=function(r){r==null&&(r=10);var n=t(),i=0,a=n.length-1,o=n[i],u=n[a],c,s,f=10;for(u<o&&(s=o,o=u,u=s,s=i,i=a,a=s);f-- >0;){if(s=Jo(o,u,r),s===c)return n[i]=o,n[a]=u,t(n);if(s>0)o=Math.floor(o/s)*s,u=Math.ceil(u/s)*s;else if(s<0)o=Math.ceil(o*s)/s,u=Math.floor(u*s)/s;else break;c=s}return e},e}function Ui(){var e=sc();return e.copy=function(){return Xn(e,Ui())},He.apply(e,arguments),Ct(e)}function hp(e){var t;function r(n){return n==null||isNaN(n=+n)?t:n}return r.invert=r,r.domain=r.range=function(n){return arguments.length?(e=Array.from(n,Fi),r):e.slice()},r.unknown=function(n){return arguments.length?(t=n,r):t},r.copy=function(){return hp(e).unknown(t)},e=arguments.length?Array.from(e,Fi):[0,1],Ct(r)}function dp(e,t){e=e.slice();var r=0,n=e.length-1,i=e[r],a=e[n],o;return a<i&&(o=r,r=n,n=o,o=i,i=a,a=o),e[r]=t.floor(i),e[n]=t.ceil(a),e}function Xl(e){return Math.log(e)}function Vl(e){return Math.exp(e)}function Fj(e){return-Math.log(-e)}function Wj(e){return-Math.exp(-e)}function Uj(e){return isFinite(e)?+("1e"+e):e<0?0:e}function zj(e){return e===10?Uj:e===Math.E?Math.exp:t=>Math.pow(e,t)}function qj(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Yl(e){return(t,r)=>-e(-t,r)}function hc(e){const t=e(Xl,Vl),r=t.domain;let n=10,i,a;function o(){return i=qj(n),a=zj(n),r()[0]<0?(i=Yl(i),a=Yl(a),e(Fj,Wj)):e(Xl,Vl),t}return t.base=function(u){return arguments.length?(n=+u,o()):n},t.domain=function(u){return arguments.length?(r(u),o()):r()},t.ticks=u=>{const c=r();let s=c[0],f=c[c.length-1];const l=f<s;l&&([s,f]=[f,s]);let h=i(s),d=i(f),p,v;const y=u==null?10:+u;let b=[];if(!(n%1)&&d-h<y){if(h=Math.floor(h),d=Math.ceil(d),s>0){for(;h<=d;++h)for(p=1;p<n;++p)if(v=h<0?p/a(-h):p*a(h),!(v<s)){if(v>f)break;b.push(v)}}else for(;h<=d;++h)for(p=n-1;p>=1;--p)if(v=h>0?p/a(-h):p*a(h),!(v<s)){if(v>f)break;b.push(v)}b.length*2<y&&(b=Zo(s,f,y))}else b=Zo(h,d,Math.min(d-h,y)).map(a);return l?b.reverse():b},t.tickFormat=(u,c)=>{if(u==null&&(u=10),c==null&&(c=n===10?"s":","),typeof c!="function"&&(!(n%1)&&(c=yn(c)).precision==null&&(c.trim=!0),c=fc(c)),u===1/0)return c;const s=Math.max(1,n*u/t.ticks().length);return f=>{let l=f/a(Math.round(i(f)));return l*n<n-.5&&(l*=n),l<=s?c(f):""}},t.nice=()=>r(dp(r(),{floor:u=>a(Math.floor(i(u))),ceil:u=>a(Math.ceil(i(u)))})),t}function pp(){const e=hc(Oa()).domain([1,10]);return e.copy=()=>Xn(e,pp()).base(e.base()),He.apply(e,arguments),e}function Zl(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Jl(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function dc(e){var t=1,r=e(Zl(t),Jl(t));return r.constant=function(n){return arguments.length?e(Zl(t=+n),Jl(t)):t},Ct(r)}function vp(){var e=dc(Oa());return e.copy=function(){return Xn(e,vp()).constant(e.constant())},He.apply(e,arguments)}function Ql(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function Gj(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function Hj(e){return e<0?-e*e:e*e}function pc(e){var t=e($e,$e),r=1;function n(){return r===1?e($e,$e):r===.5?e(Gj,Hj):e(Ql(r),Ql(1/r))}return t.exponent=function(i){return arguments.length?(r=+i,n()):r},Ct(t)}function vc(){var e=pc(Oa());return e.copy=function(){return Xn(e,vc()).exponent(e.exponent())},He.apply(e,arguments),e}function Kj(){return vc.apply(null,arguments).exponent(.5)}function ef(e){return Math.sign(e)*e*e}function Xj(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function yp(){var e=sc(),t=[0,1],r=!1,n;function i(a){var o=Xj(e(a));return isNaN(o)?n:r?Math.round(o):o}return i.invert=function(a){return e.invert(ef(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Fi)).map(ef)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(r=!!a,i):r},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return yp(e.domain(),t).round(r).clamp(e.clamp()).unknown(n)},He.apply(i,arguments),Ct(i)}function gp(){var e=[],t=[],r=[],n;function i(){var o=0,u=Math.max(1,t.length);for(r=new Array(u-1);++o<u;)r[o-1]=QE(e,o/u);return a}function a(o){return o==null||isNaN(o=+o)?n:t[Hn(r,o)]}return a.invertExtent=function(o){var u=t.indexOf(o);return u<0?[NaN,NaN]:[u>0?r[u-1]:e[0],u<r.length?r[u]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let u of o)u!=null&&!isNaN(u=+u)&&e.push(u);return e.sort(Tt),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(n=o,a):n},a.quantiles=function(){return r.slice()},a.copy=function(){return gp().domain(e).range(t).unknown(n)},He.apply(a,arguments)}function mp(){var e=0,t=1,r=1,n=[.5],i=[0,1],a;function o(c){return c!=null&&c<=c?i[Hn(n,c,0,r)]:a}function u(){var c=-1;for(n=new Array(r);++c<r;)n[c]=((c+1)*t-(c-r)*e)/(r+1);return o}return o.domain=function(c){return arguments.length?([e,t]=c,e=+e,t=+t,u()):[e,t]},o.range=function(c){return arguments.length?(r=(i=Array.from(c)).length-1,u()):i.slice()},o.invertExtent=function(c){var s=i.indexOf(c);return s<0?[NaN,NaN]:s<1?[e,n[0]]:s>=r?[n[r-1],t]:[n[s-1],n[s]]},o.unknown=function(c){return arguments.length&&(a=c),o},o.thresholds=function(){return n.slice()},o.copy=function(){return mp().domain([e,t]).range(i).unknown(a)},He.apply(Ct(o),arguments)}function bp(){var e=[.5],t=[0,1],r,n=1;function i(a){return a!=null&&a<=a?t[Hn(e,a,0,n)]:r}return i.domain=function(a){return arguments.length?(e=Array.from(a),n=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),n=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return bp().domain(e).range(t).unknown(r)},He.apply(i,arguments)}const ro=new Date,no=new Date;function ge(e,t,r,n){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),u=i.ceil(a);return a-o<u-a?o:u},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,u)=>{const c=[];if(a=i.ceil(a),u=u==null?1:Math.floor(u),!(a<o)||!(u>0))return c;let s;do c.push(s=new Date(+a)),t(a,u),e(a);while(s<a&&a<o);return c},i.filter=a=>ge(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,u)=>{if(o>=o)if(u<0)for(;++u<=0;)for(;t(o,-1),!a(o););else for(;--u>=0;)for(;t(o,1),!a(o););}),r&&(i.count=(a,o)=>(ro.setTime(+a),no.setTime(+o),e(ro),e(no),Math.floor(r(ro,no))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(n?o=>n(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const zi=ge(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);zi.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?ge(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):zi);zi.range;const ct=1e3,ze=ct*60,st=ze*60,pt=st*24,yc=pt*7,tf=pt*30,io=pt*365,zt=ge(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*ct)},(e,t)=>(t-e)/ct,e=>e.getUTCSeconds());zt.range;const gc=ge(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ct)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getMinutes());gc.range;const mc=ge(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*ze)},(e,t)=>(t-e)/ze,e=>e.getUTCMinutes());mc.range;const bc=ge(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*ct-e.getMinutes()*ze)},(e,t)=>{e.setTime(+e+t*st)},(e,t)=>(t-e)/st,e=>e.getHours());bc.range;const xc=ge(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*st)},(e,t)=>(t-e)/st,e=>e.getUTCHours());xc.range;const Vn=ge(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*ze)/pt,e=>e.getDate()-1);Vn.range;const Sa=ge(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/pt,e=>e.getUTCDate()-1);Sa.range;const xp=ge(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/pt,e=>Math.floor(e/pt));xp.range;function Yt(e){return ge(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,r)=>{t.setDate(t.getDate()+r*7)},(t,r)=>(r-t-(r.getTimezoneOffset()-t.getTimezoneOffset())*ze)/yc)}const Aa=Yt(0),qi=Yt(1),Vj=Yt(2),Yj=Yt(3),gr=Yt(4),Zj=Yt(5),Jj=Yt(6);Aa.range;qi.range;Vj.range;Yj.range;gr.range;Zj.range;Jj.range;function Zt(e){return ge(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCDate(t.getUTCDate()+r*7)},(t,r)=>(r-t)/yc)}const _a=Zt(0),Gi=Zt(1),Qj=Zt(2),eM=Zt(3),mr=Zt(4),tM=Zt(5),rM=Zt(6);_a.range;Gi.range;Qj.range;eM.range;mr.range;tM.range;rM.range;const wc=ge(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());wc.range;const Oc=ge(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());Oc.range;const vt=ge(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());vt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:ge(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)});vt.range;const yt=ge(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());yt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:ge(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)});yt.range;function wp(e,t,r,n,i,a){const o=[[zt,1,ct],[zt,5,5*ct],[zt,15,15*ct],[zt,30,30*ct],[a,1,ze],[a,5,5*ze],[a,15,15*ze],[a,30,30*ze],[i,1,st],[i,3,3*st],[i,6,6*st],[i,12,12*st],[n,1,pt],[n,2,2*pt],[r,1,yc],[t,1,tf],[t,3,3*tf],[e,1,io]];function u(s,f,l){const h=f<s;h&&([s,f]=[f,s]);const d=l&&typeof l.range=="function"?l:c(s,f,l),p=d?d.range(s,+f+1):[];return h?p.reverse():p}function c(s,f,l){const h=Math.abs(f-s)/l,d=ic(([,,y])=>y).right(o,h);if(d===o.length)return e.every(Qo(s/io,f/io,l));if(d===0)return zi.every(Math.max(Qo(s,f,l),1));const[p,v]=o[h/o[d-1][2]<o[d][2]/h?d-1:d];return p.every(v)}return[u,c]}const[nM,iM]=wp(yt,Oc,_a,xp,xc,mc),[aM,oM]=wp(vt,wc,Aa,Vn,bc,gc);function ao(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function oo(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Fr(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}function uM(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,u=e.months,c=e.shortMonths,s=Wr(i),f=Ur(i),l=Wr(a),h=Ur(a),d=Wr(o),p=Ur(o),v=Wr(u),y=Ur(u),b=Wr(c),x=Ur(c),w={a:I,A:R,b:B,B:q,c:null,d:cf,e:cf,f:jM,g:FM,G:UM,H:$M,I:TM,j:EM,L:Op,m:MM,M:CM,p:re,q:G,Q:ff,s:hf,S:kM,u:IM,U:DM,V:NM,w:RM,W:LM,x:null,X:null,y:BM,Y:WM,Z:zM,"%":lf},O={a:ee,A:Te,b:we,B:xt,c:null,d:sf,e:sf,f:KM,g:nC,G:aC,H:qM,I:GM,j:HM,L:Ap,m:XM,M:VM,p:wt,q:Me,Q:ff,s:hf,S:YM,u:ZM,U:JM,V:QM,w:eC,W:tC,x:null,X:null,y:rC,Y:iC,Z:oC,"%":lf},g={a:E,A:$,b:T,B:M,c:k,d:of,e:of,f:SM,g:af,G:nf,H:uf,I:uf,j:bM,L:OM,m:mM,M:xM,p:_,q:gM,Q:_M,s:PM,S:wM,u:hM,U:dM,V:pM,w:fM,W:vM,x:C,X:N,y:af,Y:nf,Z:yM,"%":AM};w.x=m(r,w),w.X=m(n,w),w.c=m(t,w),O.x=m(r,O),O.X=m(n,O),O.c=m(t,O);function m(F,K){return function(X){var D=[],le=-1,Q=0,fe=F.length,pe,Ce,et;for(X instanceof Date||(X=new Date(+X));++le<fe;)F.charCodeAt(le)===37&&(D.push(F.slice(Q,le)),(Ce=rf[pe=F.charAt(++le)])!=null?pe=F.charAt(++le):Ce=pe==="e"?" ":"0",(et=K[pe])&&(pe=et(X,Ce)),D.push(pe),Q=le+1);return D.push(F.slice(Q,le)),D.join("")}}function S(F,K){return function(X){var D=Fr(1900,void 0,1),le=A(D,F,X+="",0),Q,fe;if(le!=X.length)return null;if("Q"in D)return new Date(D.Q);if("s"in D)return new Date(D.s*1e3+("L"in D?D.L:0));if(K&&!("Z"in D)&&(D.Z=0),"p"in D&&(D.H=D.H%12+D.p*12),D.m===void 0&&(D.m="q"in D?D.q:0),"V"in D){if(D.V<1||D.V>53)return null;"w"in D||(D.w=1),"Z"in D?(Q=oo(Fr(D.y,0,1)),fe=Q.getUTCDay(),Q=fe>4||fe===0?Gi.ceil(Q):Gi(Q),Q=Sa.offset(Q,(D.V-1)*7),D.y=Q.getUTCFullYear(),D.m=Q.getUTCMonth(),D.d=Q.getUTCDate()+(D.w+6)%7):(Q=ao(Fr(D.y,0,1)),fe=Q.getDay(),Q=fe>4||fe===0?qi.ceil(Q):qi(Q),Q=Vn.offset(Q,(D.V-1)*7),D.y=Q.getFullYear(),D.m=Q.getMonth(),D.d=Q.getDate()+(D.w+6)%7)}else("W"in D||"U"in D)&&("w"in D||(D.w="u"in D?D.u%7:"W"in D?1:0),fe="Z"in D?oo(Fr(D.y,0,1)).getUTCDay():ao(Fr(D.y,0,1)).getDay(),D.m=0,D.d="W"in D?(D.w+6)%7+D.W*7-(fe+5)%7:D.w+D.U*7-(fe+6)%7);return"Z"in D?(D.H+=D.Z/100|0,D.M+=D.Z%100,oo(D)):ao(D)}}function A(F,K,X,D){for(var le=0,Q=K.length,fe=X.length,pe,Ce;le<Q;){if(D>=fe)return-1;if(pe=K.charCodeAt(le++),pe===37){if(pe=K.charAt(le++),Ce=g[pe in rf?K.charAt(le++):pe],!Ce||(D=Ce(F,X,D))<0)return-1}else if(pe!=X.charCodeAt(D++))return-1}return D}function _(F,K,X){var D=s.exec(K.slice(X));return D?(F.p=f.get(D[0].toLowerCase()),X+D[0].length):-1}function E(F,K,X){var D=d.exec(K.slice(X));return D?(F.w=p.get(D[0].toLowerCase()),X+D[0].length):-1}function $(F,K,X){var D=l.exec(K.slice(X));return D?(F.w=h.get(D[0].toLowerCase()),X+D[0].length):-1}function T(F,K,X){var D=b.exec(K.slice(X));return D?(F.m=x.get(D[0].toLowerCase()),X+D[0].length):-1}function M(F,K,X){var D=v.exec(K.slice(X));return D?(F.m=y.get(D[0].toLowerCase()),X+D[0].length):-1}function k(F,K,X){return A(F,t,K,X)}function C(F,K,X){return A(F,r,K,X)}function N(F,K,X){return A(F,n,K,X)}function I(F){return o[F.getDay()]}function R(F){return a[F.getDay()]}function B(F){return c[F.getMonth()]}function q(F){return u[F.getMonth()]}function re(F){return i[+(F.getHours()>=12)]}function G(F){return 1+~~(F.getMonth()/3)}function ee(F){return o[F.getUTCDay()]}function Te(F){return a[F.getUTCDay()]}function we(F){return c[F.getUTCMonth()]}function xt(F){return u[F.getUTCMonth()]}function wt(F){return i[+(F.getUTCHours()>=12)]}function Me(F){return 1+~~(F.getUTCMonth()/3)}return{format:function(F){var K=m(F+="",w);return K.toString=function(){return F},K},parse:function(F){var K=S(F+="",!1);return K.toString=function(){return F},K},utcFormat:function(F){var K=m(F+="",O);return K.toString=function(){return F},K},utcParse:function(F){var K=S(F+="",!0);return K.toString=function(){return F},K}}}var rf={"-":"",_:" ",0:"0"},xe=/^\s*\d+/,cM=/^%/,sM=/[\\^$*+?|[\]().{}]/g;function te(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?new Array(r-a+1).join(t)+i:i)}function lM(e){return e.replace(sM,"\\$&")}function Wr(e){return new RegExp("^(?:"+e.map(lM).join("|")+")","i")}function Ur(e){return new Map(e.map((t,r)=>[t.toLowerCase(),r]))}function fM(e,t,r){var n=xe.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function hM(e,t,r){var n=xe.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function dM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function pM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function vM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function nf(e,t,r){var n=xe.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function af(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function yM(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function gM(e,t,r){var n=xe.exec(t.slice(r,r+1));return n?(e.q=n[0]*3-3,r+n[0].length):-1}function mM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function of(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function bM(e,t,r){var n=xe.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function uf(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function xM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function wM(e,t,r){var n=xe.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function OM(e,t,r){var n=xe.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function SM(e,t,r){var n=xe.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function AM(e,t,r){var n=cM.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function _M(e,t,r){var n=xe.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function PM(e,t,r){var n=xe.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function cf(e,t){return te(e.getDate(),t,2)}function $M(e,t){return te(e.getHours(),t,2)}function TM(e,t){return te(e.getHours()%12||12,t,2)}function EM(e,t){return te(1+Vn.count(vt(e),e),t,3)}function Op(e,t){return te(e.getMilliseconds(),t,3)}function jM(e,t){return Op(e,t)+"000"}function MM(e,t){return te(e.getMonth()+1,t,2)}function CM(e,t){return te(e.getMinutes(),t,2)}function kM(e,t){return te(e.getSeconds(),t,2)}function IM(e){var t=e.getDay();return t===0?7:t}function DM(e,t){return te(Aa.count(vt(e)-1,e),t,2)}function Sp(e){var t=e.getDay();return t>=4||t===0?gr(e):gr.ceil(e)}function NM(e,t){return e=Sp(e),te(gr.count(vt(e),e)+(vt(e).getDay()===4),t,2)}function RM(e){return e.getDay()}function LM(e,t){return te(qi.count(vt(e)-1,e),t,2)}function BM(e,t){return te(e.getFullYear()%100,t,2)}function FM(e,t){return e=Sp(e),te(e.getFullYear()%100,t,2)}function WM(e,t){return te(e.getFullYear()%1e4,t,4)}function UM(e,t){var r=e.getDay();return e=r>=4||r===0?gr(e):gr.ceil(e),te(e.getFullYear()%1e4,t,4)}function zM(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+te(t/60|0,"0",2)+te(t%60,"0",2)}function sf(e,t){return te(e.getUTCDate(),t,2)}function qM(e,t){return te(e.getUTCHours(),t,2)}function GM(e,t){return te(e.getUTCHours()%12||12,t,2)}function HM(e,t){return te(1+Sa.count(yt(e),e),t,3)}function Ap(e,t){return te(e.getUTCMilliseconds(),t,3)}function KM(e,t){return Ap(e,t)+"000"}function XM(e,t){return te(e.getUTCMonth()+1,t,2)}function VM(e,t){return te(e.getUTCMinutes(),t,2)}function YM(e,t){return te(e.getUTCSeconds(),t,2)}function ZM(e){var t=e.getUTCDay();return t===0?7:t}function JM(e,t){return te(_a.count(yt(e)-1,e),t,2)}function _p(e){var t=e.getUTCDay();return t>=4||t===0?mr(e):mr.ceil(e)}function QM(e,t){return e=_p(e),te(mr.count(yt(e),e)+(yt(e).getUTCDay()===4),t,2)}function eC(e){return e.getUTCDay()}function tC(e,t){return te(Gi.count(yt(e)-1,e),t,2)}function rC(e,t){return te(e.getUTCFullYear()%100,t,2)}function nC(e,t){return e=_p(e),te(e.getUTCFullYear()%100,t,2)}function iC(e,t){return te(e.getUTCFullYear()%1e4,t,4)}function aC(e,t){var r=e.getUTCDay();return e=r>=4||r===0?mr(e):mr.ceil(e),te(e.getUTCFullYear()%1e4,t,4)}function oC(){return"+0000"}function lf(){return"%"}function ff(e){return+e}function hf(e){return Math.floor(+e/1e3)}var Qt,Pp,$p;uC({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function uC(e){return Qt=uM(e),Pp=Qt.format,Qt.parse,$p=Qt.utcFormat,Qt.utcParse,Qt}function cC(e){return new Date(e)}function sC(e){return e instanceof Date?+e:+new Date(+e)}function Sc(e,t,r,n,i,a,o,u,c,s){var f=sc(),l=f.invert,h=f.domain,d=s(".%L"),p=s(":%S"),v=s("%I:%M"),y=s("%I %p"),b=s("%a %d"),x=s("%b %d"),w=s("%B"),O=s("%Y");function g(m){return(c(m)<m?d:u(m)<m?p:o(m)<m?v:a(m)<m?y:n(m)<m?i(m)<m?b:x:r(m)<m?w:O)(m)}return f.invert=function(m){return new Date(l(m))},f.domain=function(m){return arguments.length?h(Array.from(m,sC)):h().map(cC)},f.ticks=function(m){var S=h();return e(S[0],S[S.length-1],m??10)},f.tickFormat=function(m,S){return S==null?g:s(S)},f.nice=function(m){var S=h();return(!m||typeof m.range!="function")&&(m=t(S[0],S[S.length-1],m??10)),m?h(dp(S,m)):f},f.copy=function(){return Xn(f,Sc(e,t,r,n,i,a,o,u,c,s))},f}function lC(){return He.apply(Sc(aM,oM,vt,wc,Aa,Vn,bc,gc,zt,Pp).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function fC(){return He.apply(Sc(nM,iM,yt,Oc,_a,Sa,xc,mc,zt,$p).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Pa(){var e=0,t=1,r,n,i,a,o=$e,u=!1,c;function s(l){return l==null||isNaN(l=+l)?c:o(i===0?.5:(l=(a(l)-r)*i,u?Math.max(0,Math.min(1,l)):l))}s.domain=function(l){return arguments.length?([e,t]=l,r=a(e=+e),n=a(t=+t),i=r===n?0:1/(n-r),s):[e,t]},s.clamp=function(l){return arguments.length?(u=!!l,s):u},s.interpolator=function(l){return arguments.length?(o=l,s):o};function f(l){return function(h){var d,p;return arguments.length?([d,p]=h,o=l(d,p),s):[o(0),o(1)]}}return s.range=f(kr),s.rangeRound=f(cc),s.unknown=function(l){return arguments.length?(c=l,s):c},function(l){return a=l,r=l(e),n=l(t),i=r===n?0:1/(n-r),s}}function kt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function Tp(){var e=Ct(Pa()($e));return e.copy=function(){return kt(e,Tp())},bt.apply(e,arguments)}function Ep(){var e=hc(Pa()).domain([1,10]);return e.copy=function(){return kt(e,Ep()).base(e.base())},bt.apply(e,arguments)}function jp(){var e=dc(Pa());return e.copy=function(){return kt(e,jp()).constant(e.constant())},bt.apply(e,arguments)}function Ac(){var e=pc(Pa());return e.copy=function(){return kt(e,Ac()).exponent(e.exponent())},bt.apply(e,arguments)}function hC(){return Ac.apply(null,arguments).exponent(.5)}function Mp(){var e=[],t=$e;function r(n){if(n!=null&&!isNaN(n=+n))return t((Hn(e,n,1)-1)/(e.length-1))}return r.domain=function(n){if(!arguments.length)return e.slice();e=[];for(let i of n)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(Tt),r},r.interpolator=function(n){return arguments.length?(t=n,r):t},r.range=function(){return e.map((n,i)=>t(i/(e.length-1)))},r.quantiles=function(n){return Array.from({length:n+1},(i,a)=>JE(e,a/n))},r.copy=function(){return Mp(t).domain(e)},bt.apply(r,arguments)}function $a(){var e=0,t=.5,r=1,n=1,i,a,o,u,c,s=$e,f,l=!1,h;function d(v){return isNaN(v=+v)?h:(v=.5+((v=+f(v))-a)*(n*v<n*a?u:c),s(l?Math.max(0,Math.min(1,v)):v))}d.domain=function(v){return arguments.length?([e,t,r]=v,i=f(e=+e),a=f(t=+t),o=f(r=+r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d):[e,t,r]},d.clamp=function(v){return arguments.length?(l=!!v,d):l},d.interpolator=function(v){return arguments.length?(s=v,d):s};function p(v){return function(y){var b,x,w;return arguments.length?([b,x,w]=y,s=Aj(v,[b,x,w]),d):[s(0),s(.5),s(1)]}}return d.range=p(kr),d.rangeRound=p(cc),d.unknown=function(v){return arguments.length?(h=v,d):h},function(v){return f=v,i=v(e),a=v(t),o=v(r),u=i===a?0:.5/(a-i),c=a===o?0:.5/(o-a),n=a<i?-1:1,d}}function Cp(){var e=Ct($a()($e));return e.copy=function(){return kt(e,Cp())},bt.apply(e,arguments)}function kp(){var e=hc($a()).domain([.1,1,10]);return e.copy=function(){return kt(e,kp()).base(e.base())},bt.apply(e,arguments)}function Ip(){var e=dc($a());return e.copy=function(){return kt(e,Ip()).constant(e.constant())},bt.apply(e,arguments)}function _c(){var e=pc($a());return e.copy=function(){return kt(e,_c()).exponent(e.exponent())},bt.apply(e,arguments)}function dC(){return _c.apply(null,arguments).exponent(.5)}const df=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:hn,scaleDiverging:Cp,scaleDivergingLog:kp,scaleDivergingPow:_c,scaleDivergingSqrt:dC,scaleDivergingSymlog:Ip,scaleIdentity:hp,scaleImplicit:eu,scaleLinear:Ui,scaleLog:pp,scaleOrdinal:ac,scalePoint:Yr,scalePow:vc,scaleQuantile:gp,scaleQuantize:mp,scaleRadial:yp,scaleSequential:Tp,scaleSequentialLog:Ep,scaleSequentialPow:Ac,scaleSequentialQuantile:Mp,scaleSequentialSqrt:hC,scaleSequentialSymlog:jp,scaleSqrt:Kj,scaleSymlog:vp,scaleThreshold:bp,scaleTime:lC,scaleUtc:fC,tickFormat:fp},Symbol.toStringTag,{value:"Module"}));var pC=Pr;function vC(e,t,r){for(var n=-1,i=e.length;++n<i;){var a=e[n],o=t(a);if(o!=null&&(u===void 0?o===o&&!pC(o):r(o,u)))var u=o,c=a}return c}var Dp=vC;function yC(e,t){return e>t}var gC=yC,mC=Dp,bC=gC,xC=Cr;function wC(e){return e&&e.length?mC(e,xC,bC):void 0}var Ta=wC;function OC(e,t){return e<t}var SC=OC,AC=Dp,_C=SC,PC=Cr;function $C(e){return e&&e.length?AC(e,PC,_C):void 0}var Ea=$C,TC=Fu,EC=jt,jC=Rd,MC=je;function CC(e,t){var r=MC(e)?TC:jC;return r(e,EC(t))}var kC=CC,IC=Dd,DC=kC;function NC(e,t){return IC(DC(e,t),1)}var RC=NC,LC=Qu;function BC(e,t){return LC(e,t)}var ja=BC,Ir=1e9,FC={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},$c,se=!0,Ge="[DecimalError] ",Ht=Ge+"Invalid argument: ",Pc=Ge+"Exponent out of range: ",Dr=Math.floor,Ft=Math.pow,WC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,De,be=1e7,ce=7,Np=9007199254740991,Hi=Dr(Np/ce),W={};W.absoluteValue=W.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};W.comparedTo=W.cmp=function(e){var t,r,n,i,a=this;if(e=new a.constructor(e),a.s!==e.s)return a.s||-e.s;if(a.e!==e.e)return a.e>e.e^a.s<0?1:-1;for(n=a.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(a.d[t]!==e.d[t])return a.d[t]>e.d[t]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};W.decimalPlaces=W.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*ce;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};W.dividedBy=W.div=function(e){return ht(this,new this.constructor(e))};W.dividedToIntegerBy=W.idiv=function(e){var t=this,r=t.constructor;return ae(ht(t,new r(e),0,1),r.precision)};W.equals=W.eq=function(e){return!this.cmp(e)};W.exponent=function(){return de(this)};W.greaterThan=W.gt=function(e){return this.cmp(e)>0};W.greaterThanOrEqualTo=W.gte=function(e){return this.cmp(e)>=0};W.isInteger=W.isint=function(){return this.e>this.d.length-2};W.isNegative=W.isneg=function(){return this.s<0};W.isPositive=W.ispos=function(){return this.s>0};W.isZero=function(){return this.s===0};W.lessThan=W.lt=function(e){return this.cmp(e)<0};W.lessThanOrEqualTo=W.lte=function(e){return this.cmp(e)<1};W.logarithm=W.log=function(e){var t,r=this,n=r.constructor,i=n.precision,a=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(De))throw Error(Ge+"NaN");if(r.s<1)throw Error(Ge+(r.s?"NaN":"-Infinity"));return r.eq(De)?new n(0):(se=!1,t=ht(gn(r,a),gn(e,a),a),se=!0,ae(t,i))};W.minus=W.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Bp(t,e):Rp(t,(e.s=-e.s,e))};W.modulo=W.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(Ge+"NaN");return r.s?(se=!1,t=ht(r,e,0,1).times(e),se=!0,r.minus(t)):ae(new n(r),i)};W.naturalExponential=W.exp=function(){return Lp(this)};W.naturalLogarithm=W.ln=function(){return gn(this)};W.negated=W.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};W.plus=W.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Rp(t,e):Bp(t,(e.s=-e.s,e))};W.precision=W.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ht+e);if(t=de(i)+1,n=i.d.length-1,r=n*ce+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};W.squareRoot=W.sqrt=function(){var e,t,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Ge+"NaN")}for(e=de(u),se=!1,i=Math.sqrt(+u),i==0||i==1/0?(t=rt(u.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=Dr((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new c(t)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(ht(u,a,o+2)).times(.5),rt(a.d).slice(0,o)===(t=rt(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&t=="4999"){if(ae(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(t!="9999")break;o+=4}return se=!0,ae(n,r)};W.times=W.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this,l=f.constructor,h=f.d,d=(e=new l(e)).d;if(!f.s||!e.s)return new l(0);for(e.s*=f.s,r=f.e+e.e,c=h.length,s=d.length,c<s&&(a=h,h=d,d=a,o=c,c=s,s=o),a=[],o=c+s,n=o;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+d[n]*h[i-n-1]+t,a[i--]=u%be|0,t=u/be|0;a[i]=(a[i]+t)%be|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,se?ae(e,l.precision):e};W.toDecimalPlaces=W.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(at(e,0,Ir),t===void 0?t=n.rounding:at(t,0,8),ae(r,e+de(r)+1,t))};W.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Kt(n,!0):(at(e,0,Ir),t===void 0?t=i.rounding:at(t,0,8),n=ae(new i(n),e+1,t),r=Kt(n,!0,e+1)),r};W.toFixed=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?Kt(i):(at(e,0,Ir),t===void 0?t=a.rounding:at(t,0,8),n=ae(new a(i),e+de(i)+1,t),r=Kt(n.abs(),!1,e+de(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};W.toInteger=W.toint=function(){var e=this,t=e.constructor;return ae(new t(e),de(e)+1,t.rounding)};W.toNumber=function(){return+this};W.toPower=W.pow=function(e){var t,r,n,i,a,o,u=this,c=u.constructor,s=12,f=+(e=new c(e));if(!e.s)return new c(De);if(u=new c(u),!u.s){if(e.s<1)throw Error(Ge+"Infinity");return u}if(u.eq(De))return u;if(n=c.precision,e.eq(De))return ae(u,n);if(t=e.e,r=e.d.length-1,o=t>=r,a=u.s,o){if((r=f<0?-f:f)<=Np){for(i=new c(De),t=Math.ceil(n/ce+4),se=!1;r%2&&(i=i.times(u),vf(i.d,t)),r=Dr(r/2),r!==0;)u=u.times(u),vf(u.d,t);return se=!0,e.s<0?new c(De).div(i):ae(i,n)}}else if(a<0)throw Error(Ge+"NaN");return a=a<0&&e.d[Math.max(t,r)]&1?-1:1,u.s=1,se=!1,i=e.times(gn(u,n+s)),se=!0,i=Lp(i),i.s=a,i};W.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return e===void 0?(r=de(i),n=Kt(i,r<=a.toExpNeg||r>=a.toExpPos)):(at(e,1,Ir),t===void 0?t=a.rounding:at(t,0,8),i=ae(new a(i),e,t),r=de(i),n=Kt(i,e<=r||r<=a.toExpNeg,e)),n};W.toSignificantDigits=W.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(at(e,1,Ir),t===void 0?t=n.rounding:at(t,0,8)),ae(new n(r),e,t)};W.toString=W.valueOf=W.val=W.toJSON=W[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=de(e),r=e.constructor;return Kt(e,t<=r.toExpNeg||t>=r.toExpPos)};function Rp(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,l=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),se?ae(t,l):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),o=Math.ceil(l/ce),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=s.length,u-a<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/be|0,c[a]%=be;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return t.d=c,t.e=i,se?ae(t,l):t}function at(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Ht+e)}function rt(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)n=e[t]+"",r=ce-n.length,r&&(a+=Pt(r)),a+=n;o=e[t],n=o+"",r=ce-n.length,r&&(a+=Pt(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var ht=function(){function e(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%be|0,o=a/be|0;return o&&n.unshift(o),n}function t(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*be+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,s,f,l,h,d,p,v,y,b,x,w,O,g,m,S,A,_=n.constructor,E=n.s==i.s?1:-1,$=n.d,T=i.d;if(!n.s)return new _(n);if(!i.s)throw Error(Ge+"Division by zero");for(c=n.e-i.e,S=T.length,g=$.length,d=new _(E),p=d.d=[],s=0;T[s]==($[s]||0);)++s;if(T[s]>($[s]||0)&&--c,a==null?x=a=_.precision:o?x=a+(de(n)-de(i))+1:x=a,x<0)return new _(0);if(x=x/ce+2|0,s=0,S==1)for(f=0,T=T[0],x++;(s<g||f)&&x--;s++)w=f*be+($[s]||0),p[s]=w/T|0,f=w%T|0;else{for(f=be/(T[0]+1)|0,f>1&&(T=e(T,f),$=e($,f),S=T.length,g=$.length),O=S,v=$.slice(0,S),y=v.length;y<S;)v[y++]=0;A=T.slice(),A.unshift(0),m=T[0],T[1]>=be/2&&++m;do f=0,u=t(T,v,S,y),u<0?(b=v[0],S!=y&&(b=b*be+(v[1]||0)),f=b/m|0,f>1?(f>=be&&(f=be-1),l=e(T,f),h=l.length,y=v.length,u=t(l,v,h,y),u==1&&(f--,r(l,S<h?A:T,h))):(f==0&&(u=f=1),l=T.slice()),h=l.length,h<y&&l.unshift(0),r(v,l,y),u==-1&&(y=v.length,u=t(T,v,S,y),u<1&&(f++,r(v,S<y?A:T,y))),y=v.length):u===0&&(f++,v=[0]),p[s++]=f,u&&v[0]?v[y++]=$[O]||0:(v=[$[O]],y=1);while((O++<g||v[0]!==void 0)&&x--)}return p[0]||p.shift(),d.e=c,ae(d,o?a+de(d)+1:a)}}();function Lp(e,t){var r,n,i,a,o,u,c=0,s=0,f=e.constructor,l=f.precision;if(de(e)>16)throw Error(Pc+de(e));if(!e.s)return new f(De);for(t==null?(se=!1,u=l):u=t,o=new f(.03125);e.abs().gte(.1);)e=e.times(o),s+=5;for(n=Math.log(Ft(2,s))/Math.LN10*2+5|0,u+=n,r=i=a=new f(De),f.precision=u;;){if(i=ae(i.times(e),u),r=r.times(++c),o=a.plus(ht(i,r,u)),rt(o.d).slice(0,u)===rt(a.d).slice(0,u)){for(;s--;)a=ae(a.times(a),u);return f.precision=l,t==null?(se=!0,ae(a,l)):a}a=o}}function de(e){for(var t=e.e*ce,r=e.d[0];r>=10;r/=10)t++;return t}function uo(e,t,r){if(t>e.LN10.sd())throw se=!0,r&&(e.precision=r),Error(Ge+"LN10 precision limit exceeded");return ae(new e(e.LN10),t)}function Pt(e){for(var t="";e--;)t+="0";return t}function gn(e,t){var r,n,i,a,o,u,c,s,f,l=1,h=10,d=e,p=d.d,v=d.constructor,y=v.precision;if(d.s<1)throw Error(Ge+(d.s?"NaN":"-Infinity"));if(d.eq(De))return new v(0);if(t==null?(se=!1,s=y):s=t,d.eq(10))return t==null&&(se=!0),uo(v,s);if(s+=h,v.precision=s,r=rt(p),n=r.charAt(0),a=de(d),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(e),r=rt(d.d),n=r.charAt(0),l++;a=de(d),n>1?(d=new v("0."+r),a++):d=new v(n+"."+r.slice(1))}else return c=uo(v,s+2,y).times(a+""),d=gn(new v(n+"."+r.slice(1)),s-h).plus(c),v.precision=y,t==null?(se=!0,ae(d,y)):d;for(u=o=d=ht(d.minus(De),d.plus(De),s),f=ae(d.times(d),s),i=3;;){if(o=ae(o.times(f),s),c=u.plus(ht(o,new v(i),s)),rt(c.d).slice(0,s)===rt(u.d).slice(0,s))return u=u.times(2),a!==0&&(u=u.plus(uo(v,s+2,y).times(a+""))),u=ht(u,new v(l),s),v.precision=y,t==null?(se=!0,ae(u,y)):u;u=c,i+=2}}function pf(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=Dr(r/ce),e.d=[],n=(r+1)%ce,r<0&&(n+=ce),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=ce;n<i;)e.d.push(+t.slice(n,n+=ce));t=t.slice(n),n=ce-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),se&&(e.e>Hi||e.e<-Hi))throw Error(Pc+r)}else e.s=0,e.e=0,e.d=[0];return e}function ae(e,t,r){var n,i,a,o,u,c,s,f,l=e.d;for(o=1,a=l[0];a>=10;a/=10)o++;if(n=t-o,n<0)n+=ce,i=t,s=l[f=0];else{if(f=Math.ceil((n+1)/ce),a=l.length,f>=a)return e;for(s=a=l[f],o=1;a>=10;a/=10)o++;n%=ce,i=n-ce+o}if(r!==void 0&&(a=Ft(10,o-i-1),u=s/a%10|0,c=t<0||l[f+1]!==void 0||s%a,c=r<4?(u||c)&&(r==0||r==(e.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?s/Ft(10,o-i):0:l[f-1])%10&1||r==(e.s<0?8:7))),t<1||!l[0])return c?(a=de(e),l.length=1,t=t-a-1,l[0]=Ft(10,(ce-t%ce)%ce),e.e=Dr(-t/ce)||0):(l.length=1,l[0]=e.e=e.s=0),e;if(n==0?(l.length=f,a=1,f--):(l.length=f+1,a=Ft(10,ce-n),l[f]=i>0?(s/Ft(10,o-i)%Ft(10,i)|0)*a:0),c)for(;;)if(f==0){(l[0]+=a)==be&&(l[0]=1,++e.e);break}else{if(l[f]+=a,l[f]!=be)break;l[f--]=0,a=1}for(n=l.length;l[--n]===0;)l.pop();if(se&&(e.e>Hi||e.e<-Hi))throw Error(Pc+de(e));return e}function Bp(e,t){var r,n,i,a,o,u,c,s,f,l,h=e.constructor,d=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),se?ae(t,d):t;if(c=e.d,l=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n,o){for(f=o<0,f?(r=c,o=-o,u=l.length):(r=l,n=s,u=c.length),i=Math.max(Math.ceil(d/ce),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=l.length,f=i<u,f&&(u=i),i=0;i<u;i++)if(c[i]!=l[i]){f=c[i]<l[i];break}o=0}for(f&&(r=c,c=l,l=r,t.s=-t.s),u=c.length,i=l.length-u;i>0;--i)c[u++]=0;for(i=l.length;i>o;){if(c[--i]<l[i]){for(a=i;a&&c[--a]===0;)c[a]=be-1;--c[a],c[i]+=be}c[i]-=l[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(t.d=c,t.e=n,se?ae(t,d):t):new h(0)}function Kt(e,t,r){var n,i=de(e),a=rt(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+Pt(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+Pt(-i-1)+a,r&&(n=r-o)>0&&(a+=Pt(n))):i>=o?(a+=Pt(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+Pt(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=Pt(n))),e.s<0?"-"+a:a}function vf(e,t){if(e.length>t)return e.length=t,!0}function Fp(e){var t,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(Ht+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return pf(o,a.toString())}else if(typeof a!="string")throw Error(Ht+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,WC.test(a))pf(o,a);else throw Error(Ht+a)}if(i.prototype=W,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Fp,i.config=i.set=UC,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function UC(e){if(!e||typeof e!="object")throw Error(Ge+"Object expected");var t,r,n,i=["precision",1,Ir,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(Dr(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Ht+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Ht+r+": "+n);return this}var $c=Fp(FC);De=new $c(1);const ie=$c;function zC(e){return KC(e)||HC(e)||GC(e)||qC()}function qC(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function GC(e,t){if(e){if(typeof e=="string")return iu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iu(e,t)}}function HC(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function KC(e){if(Array.isArray(e))return iu(e)}function iu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var XC=function(t){return t},Wp={"@@functional/placeholder":!0},Up=function(t){return t===Wp},yf=function(t){return function r(){return arguments.length===0||arguments.length===1&&Up(arguments.length<=0?void 0:arguments[0])?r:t.apply(void 0,arguments)}},VC=function e(t,r){return t===1?r:yf(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Wp}).length;return o>=t?r.apply(void 0,i):e(t-o,yf(function(){for(var u=arguments.length,c=new Array(u),s=0;s<u;s++)c[s]=arguments[s];var f=i.map(function(l){return Up(l)?c.shift():l});return r.apply(void 0,zC(f).concat(c))}))})},Ma=function(t){return VC(t.length,t)},au=function(t,r){for(var n=[],i=t;i<r;++i)n[i-t]=i;return n},YC=Ma(function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map(function(r){return t[r]}).map(e)}),ZC=function(){for(var t=arguments.length,r=new Array(t),n=0;n<t;n++)r[n]=arguments[n];if(!r.length)return XC;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},ou=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},zp=function(t){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=t.apply(void 0,a)),n}};function JC(e){var t;return e===0?t=1:t=Math.floor(new ie(e).abs().log(10).toNumber())+1,t}function QC(e,t,r){for(var n=new ie(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var ek=Ma(function(e,t,r){var n=+e,i=+t;return n+r*(i-n)}),tk=Ma(function(e,t,r){var n=t-+e;return n=n||1/0,(r-e)/n}),rk=Ma(function(e,t,r){var n=t-+e;return n=n||1/0,Math.max(0,Math.min(1,(r-e)/n))});const Ca={rangeStep:QC,getDigitCount:JC,interpolateNumber:ek,uninterpolateNumber:tk,uninterpolateTruncation:rk};function uu(e){return ak(e)||ik(e)||qp(e)||nk()}function nk(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ik(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function ak(e){if(Array.isArray(e))return cu(e)}function mn(e,t){return ck(e)||uk(e,t)||qp(e,t)||ok()}function ok(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qp(e,t){if(e){if(typeof e=="string")return cu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cu(e,t)}}function cu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function uk(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=e[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(t&&r.length===t));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function ck(e){if(Array.isArray(e))return e}function Gp(e){var t=mn(e,2),r=t[0],n=t[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Hp(e,t,r){if(e.lte(0))return new ie(0);var n=Ca.getDigitCount(e.toNumber()),i=new ie(10).pow(n),a=e.div(i),o=n!==1?.05:.1,u=new ie(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return t?c:new ie(Math.ceil(c))}function sk(e,t,r){var n=1,i=new ie(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new ie(10).pow(Ca.getDigitCount(e)-1),i=new ie(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new ie(Math.floor(e)))}else e===0?i=new ie(Math.floor((t-1)/2)):r||(i=new ie(Math.floor(e)));var o=Math.floor((t-1)/2),u=ZC(YC(function(c){return i.add(new ie(c-o).mul(n)).toNumber()}),au);return u(0,t)}function Kp(e,t,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new ie(0),tickMin:new ie(0),tickMax:new ie(0)};var a=Hp(new ie(t).sub(e).div(r-1),n,i),o;e<=0&&t>=0?o=new ie(0):(o=new ie(e).add(t).div(2),o=o.sub(new ie(o).mod(a)));var u=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new ie(t).sub(o).div(a).toNumber()),s=u+c+1;return s>r?Kp(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,u=t>0?u:u+(r-s)),{step:a,tickMin:o.sub(new ie(u).mul(a)),tickMax:o.add(new ie(c).mul(a))})}function lk(e){var t=mn(e,2),r=t[0],n=t[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=Gp([r,n]),c=mn(u,2),s=c[0],f=c[1];if(s===-1/0||f===1/0){var l=f===1/0?[s].concat(uu(au(0,i-1).map(function(){return 1/0}))):[].concat(uu(au(0,i-1).map(function(){return-1/0})),[f]);return r>n?ou(l):l}if(s===f)return sk(s,i,a);var h=Kp(s,f,o,a),d=h.step,p=h.tickMin,v=h.tickMax,y=Ca.rangeStep(p,v.add(new ie(.1).mul(d)),d);return r>n?ou(y):y}function fk(e,t){var r=mn(e,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Gp([n,i]),u=mn(o,2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[n,i];if(c===s)return[c];var f=Math.max(t,2),l=Hp(new ie(s).sub(c).div(f-1),a,0),h=[].concat(uu(Ca.rangeStep(new ie(c),new ie(s).sub(new ie(.99).mul(l)),l)),[s]);return n>i?ou(h):h}var hk=zp(lk),dk=zp(fk),pk=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Ki(){return Ki=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ki.apply(this,arguments)}function vk(e,t){return bk(e)||mk(e,t)||gk(e,t)||yk()}function yk(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function gk(e,t){if(e){if(typeof e=="string")return gf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return gf(e,t)}}function gf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function mk(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function bk(e){if(Array.isArray(e))return e}function xk(e,t){if(e==null)return{};var r=wk(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function wk(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Yn(e){var t=e.offset,r=e.layout,n=e.width,i=e.dataKey,a=e.data,o=e.dataPointFormatter,u=e.xAxis,c=e.yAxis,s=xk(e,pk),f=Z(s),l=a.map(function(h){var d=o(h,i),p=d.x,v=d.y,y=d.value,b=d.errorVal;if(!b)return null;var x=[],w,O;if(Array.isArray(b)){var g=vk(b,2);w=g[0],O=g[1]}else w=O=b;if(r==="vertical"){var m=u.scale,S=v+t,A=S+n,_=S-n,E=m(y-w),$=m(y+O);x.push({x1:$,y1:A,x2:$,y2:_}),x.push({x1:E,y1:S,x2:$,y2:S}),x.push({x1:E,y1:A,x2:E,y2:_})}else if(r==="horizontal"){var T=c.scale,M=p+t,k=M-n,C=M+n,N=T(y-w),I=T(y+O);x.push({x1:k,y1:I,x2:C,y2:I}),x.push({x1:M,y1:N,x2:M,y2:I}),x.push({x1:k,y1:N,x2:C,y2:N})}return P.createElement(he,Ki({className:"recharts-errorBar",key:"bar-".concat(x.map(function(R){return"".concat(R.x1,"-").concat(R.x2,"-").concat(R.y1,"-").concat(R.y2)}))},f),x.map(function(R){return P.createElement("line",Ki({},R,{key:"line-".concat(R.x1,"-").concat(R.x2,"-").concat(R.y1,"-").concat(R.y2)}))}))});return P.createElement(he,{className:"recharts-errorBars"},l)}Yn.defaultProps={stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"};Yn.displayName="ErrorBar";function bn(e){"@babel/helpers - typeof";return bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bn(e)}function mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function co(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mf(Object(r),!0).forEach(function(n){Ok(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Ok(e,t,r){return t=Sk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Sk(e){var t=Ak(e,"string");return bn(t)==="symbol"?t:String(t)}function Ak(e,t){if(bn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(bn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Xp=function(t){var r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,a=t.legendContent,o=ut(r,nn);if(!o)return null;var u;return o.props&&o.props.payload?u=o.props&&o.props.payload:a==="children"?u=(n||[]).reduce(function(c,s){var f=s.item,l=s.props,h=l.sectors||l.data||[];return c.concat(h.map(function(d){return{type:o.props.iconType||f.props.legendType,value:d.name,color:d.fill,payload:d}}))},[]):u=(n||[]).map(function(c){var s=c.item,f=s.props,l=f.dataKey,h=f.name,d=f.legendType,p=f.hide;return{inactive:p,dataKey:l,type:o.props.iconType||d||"square",color:Tc(s),value:h||l,payload:s.props}}),co(co(co({},o.props),nn.getWithHeight(o,i)),{},{payload:u,item:o})};function xn(e){"@babel/helpers - typeof";return xn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xn(e)}function bf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ue(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?bf(Object(r),!0).forEach(function(n){ur(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ur(e,t,r){return t=_k(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _k(e){var t=Pk(e,"string");return xn(t)==="symbol"?t:String(t)}function Pk(e,t){if(xn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(xn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function xf(e){return jk(e)||Ek(e)||Tk(e)||$k()}function $k(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tk(e,t){if(e){if(typeof e=="string")return su(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return su(e,t)}}function Ek(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function jk(e){if(Array.isArray(e))return su(e)}function su(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Re(e,t,r){return J(e)||J(t)?r:ye(t)?Ne(e,t,r):Y(t)?t(e):r}function Zr(e,t,r,n){var i=RC(e,function(u){return Re(u,t)});if(r==="number"){var a=i.filter(function(u){return L(u)||parseFloat(u)});return a.length?[Ea(a),Ta(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!J(u)}):i;return o.map(function(u){return ye(u)||u instanceof Date?u:""})}var Mk=function(t){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,s=0;s<u;s++){var f=s>0?i[s-1].coordinate:i[u-1].coordinate,l=i[s].coordinate,h=s>=u-1?i[0].coordinate:i[s+1].coordinate,d=void 0;if(Je(l-f)!==Je(h-l)){var p=[];if(Je(h-l)===Je(c[1]-c[0])){d=h;var v=l+c[1]-c[0];p[0]=Math.min(v,(v+f)/2),p[1]=Math.max(v,(v+f)/2)}else{d=f;var y=h+c[1]-c[0];p[0]=Math.min(l,(y+l)/2),p[1]=Math.max(l,(y+l)/2)}var b=[Math.min(l,(d+l)/2),Math.max(l,(d+l)/2)];if(t>b[0]&&t<=b[1]||t>=p[0]&&t<=p[1]){o=i[s].index;break}}else{var x=Math.min(f,h),w=Math.max(f,h);if(t>(x+l)/2&&t<=(w+l)/2){o=i[s].index;break}}}else for(var O=0;O<u;O++)if(O===0&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O>0&&O<u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2&&t<=(n[O].coordinate+n[O+1].coordinate)/2||O===u-1&&t>(n[O].coordinate+n[O-1].coordinate)/2){o=n[O].index;break}return o},Tc=function(t){var r=t,n=r.type.displayName,i=t.props,a=i.stroke,o=i.fill,u;switch(n){case"Line":u=a;break;case"Area":case"Radar":u=a&&a!=="none"?a:o;break;default:u=o;break}return u},Ck=function(t){var r=t.barSize,n=t.stackGroups,i=n===void 0?{}:n;if(!i)return{};for(var a={},o=Object.keys(i),u=0,c=o.length;u<c;u++)for(var s=i[o[u]].stackGroups,f=Object.keys(s),l=0,h=f.length;l<h;l++){var d=s[f[l]],p=d.items,v=d.cateAxisId,y=p.filter(function(w){return lt(w.type).indexOf("Bar")>=0});if(y&&y.length){var b=y[0].props.barSize,x=y[0].props[v];a[x]||(a[x]=[]),a[x].push({item:y[0],stackList:y.slice(1),barSize:J(b)?r:b})}}return a},kk=function(t){var r=t.barGap,n=t.barCategoryGap,i=t.bandSize,a=t.sizeList,o=a===void 0?[]:a,u=t.maxBarSize,c=o.length;if(c<1)return null;var s=cr(r,i,0,!0),f,l=[];if(o[0].barSize===+o[0].barSize){var h=!1,d=i/c,p=o.reduce(function(O,g){return O+g.barSize||0},0);p+=(c-1)*s,p>=i&&(p-=(c-1)*s,s=0),p>=i&&d>0&&(h=!0,d*=.9,p=c*d);var v=(i-p)/2>>0,y={offset:v-s,size:0};f=o.reduce(function(O,g){var m={item:g.item,position:{offset:y.offset+y.size+s,size:h?d:g.barSize}},S=[].concat(xf(O),[m]);return y=S[S.length-1].position,g.stackList&&g.stackList.length&&g.stackList.forEach(function(A){S.push({item:A,position:y})}),S},l)}else{var b=cr(n,i,0,!0);i-2*b-(c-1)*s<=0&&(s=0);var x=(i-2*b-(c-1)*s)/c;x>1&&(x>>=0);var w=u===+u?Math.min(x,u):x;f=o.reduce(function(O,g,m){var S=[].concat(xf(O),[{item:g.item,position:{offset:b+(x+s)*m+(x-w)/2,size:w}}]);return g.stackList&&g.stackList.length&&g.stackList.forEach(function(A){S.push({item:A,position:S[S.length-1].position})}),S},l)}return f},Ik=function(t,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),s=Xp({children:a,legendWidth:c});if(s){var f=i||{},l=f.width,h=f.height,d=s.align,p=s.verticalAlign,v=s.layout;if((v==="vertical"||v==="horizontal"&&p==="middle")&&d!=="center"&&L(t[d]))return Ue(Ue({},t),{},ur({},d,t[d]+(l||0)));if((v==="horizontal"||v==="vertical"&&d==="center")&&p!=="middle"&&L(t[p]))return Ue(Ue({},t),{},ur({},p,t[p]+(h||0)))}return t},Dk=function(t,r,n){return J(r)?!0:t==="horizontal"?r==="yAxis":t==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Vp=function(t,r,n,i,a){var o=r.props.children,u=qe(o,Yn).filter(function(s){return Dk(i,a,s.props.direction)});if(u&&u.length){var c=u.map(function(s){return s.props.dataKey});return t.reduce(function(s,f){var l=Re(f,n,0),h=Array.isArray(l)?[Ea(l),Ta(l)]:[l,l],d=c.reduce(function(p,v){var y=Re(f,v,0),b=h[0]-Math.abs(Array.isArray(y)?y[0]:y),x=h[1]+Math.abs(Array.isArray(y)?y[1]:y);return[Math.min(b,p[0]),Math.max(x,p[1])]},[1/0,-1/0]);return[Math.min(d[0],s[0]),Math.max(d[1],s[1])]},[1/0,-1/0])}return null},Nk=function(t,r,n,i,a){var o=r.map(function(u){return Vp(t,u,n,a,i)}).filter(function(u){return!J(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Yp=function(t,r,n,i,a){var o=r.map(function(c){var s=c.props.dataKey;return n==="number"&&s&&Vp(t,c,s,i)||Zr(t,s,n,a)});if(n==="number")return o.reduce(function(c,s){return[Math.min(c[0],s[0]),Math.max(c[1],s[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,s){for(var f=0,l=s.length;f<l;f++)u[s[f]]||(u[s[f]]=!0,c.push(s[f]));return c},[])},Zp=function(t,r){return t==="horizontal"&&r==="xAxis"||t==="vertical"&&r==="yAxis"||t==="centric"&&r==="angleAxis"||t==="radial"&&r==="radiusAxis"},wf=function(t,r,n,i){if(i)return t.map(function(c){return c.coordinate});var a,o,u=t.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},At=function(t,r,n){if(!t)return null;var i=t.scale,a=t.duplicateDomain,o=t.type,u=t.range,c=t.realScaleType==="scaleBand"?i.bandwidth()/2:2,s=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(s=t.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?Je(u[0]-u[1])*2*s:s,r&&(t.ticks||t.niceTicks)){var f=(t.ticks||t.niceTicks).map(function(l){var h=a?a.indexOf(l):l;return{coordinate:i(h)+s,value:l,offset:s}});return f.filter(function(l){return!zn(l.coordinate)})}return t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(l,h){return{coordinate:i(l)+s,value:l,index:h,offset:s}}):i.ticks&&!n?i.ticks(t.tickCount).map(function(l){return{coordinate:i(l)+s,value:l,offset:s}}):i.domain().map(function(l,h){return{coordinate:i(l)+s,value:a?a[l]:l,index:h,offset:s}})},so=new WeakMap,fi=function(t,r){if(typeof r!="function")return t;so.has(t)||so.set(t,new WeakMap);var n=so.get(t);if(n.has(r))return n.get(r);var i=function(){t.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Rk=function(t,r,n){var i=t.scale,a=t.type,o=t.layout,u=t.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:hn(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:Ui(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:Yr(),realScaleType:"point"}:a==="category"?{scale:hn(),realScaleType:"band"}:{scale:Ui(),realScaleType:"linear"};if(Un(i)){var c="scale".concat(va(i));return{scale:(df[c]||Yr)(),realScaleType:df[c]?c:"point"}}return Y(i)?{scale:i}:{scale:Yr(),realScaleType:"point"}},Of=1e-4,Lk=function(t){var r=t.domain();if(!(!r||r.length<=2)){var n=r.length,i=t.range(),a=Math.min(i[0],i[1])-Of,o=Math.max(i[0],i[1])+Of,u=t(r[0]),c=t(r[n-1]);(u<a||u>o||c<a||c>o)&&t.domain([r[0],r[n-1]])}},Bk=function(t,r){if(!t)return null;for(var n=0,i=t.length;n<i;n++)if(t[n].item===r)return t[n].position;return null},Fk=function(t,r){if(!r||r.length!==2||!L(r[0])||!L(r[1]))return t;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[t[0],t[1]];return(!L(t[0])||t[0]<n)&&(a[0]=n),(!L(t[1])||t[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},Wk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=zn(t[u][n][1])?t[u][n][0]:t[u][n][1];c>=0?(t[u][n][0]=a,t[u][n][1]=a+c,a=t[u][n][1]):(t[u][n][0]=o,t[u][n][1]=o+c,o=t[u][n][1])}},Uk=function(t){var r=t.length;if(!(r<=0))for(var n=0,i=t[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=zn(t[o][n][1])?t[o][n][0]:t[o][n][1];u>=0?(t[o][n][0]=a,t[o][n][1]=a+u,a=t[o][n][1]):(t[o][n][0]=0,t[o][n][1]=0)}},zk={sign:Wk,expand:D0,none:sr,silhouette:N0,wiggle:R0,positive:Uk},qk=function(t,r,n){var i=r.map(function(u){return u.props.dataKey}),a=zk[n],o=I0().keys(i).value(function(u,c){return+Re(u,c,0)}).order(To).offset(a);return o(t)},Gk=function(t,r,n,i,a,o){if(!t)return null;var u=o?r.reverse():r,c={},s=u.reduce(function(l,h){var d=h.props,p=d.stackId,v=d.hide;if(v)return l;var y=h.props[n],b=l[y]||{hasStack:!1,stackGroups:{}};if(ye(p)){var x=b.stackGroups[p]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(h),b.hasStack=!0,b.stackGroups[p]=x}else b.stackGroups[qn("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[h]};return Ue(Ue({},l),{},ur({},y,b))},c),f={};return Object.keys(s).reduce(function(l,h){var d=s[h];if(d.hasStack){var p={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(v,y){var b=d.stackGroups[y];return Ue(Ue({},v),{},ur({},y,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:qk(t,b.items,a)}))},p)}return Ue(Ue({},l),{},ur({},h,d))},f)},Hk=function(t,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var s=t.domain();if(!s.length)return null;var f=hk(s,a,u);return t.domain([Ea(f),Ta(f)]),{niceTicks:f}}if(a&&i==="number"){var l=t.domain(),h=dk(l,a,u);return{niceTicks:h}}return null},Sf=function(t){var r=t.axis,n=t.ticks,i=t.bandSize,a=t.entry,o=t.index,u=t.dataKey;if(r.type==="category"){if(!r.allowDuplicatedCategory&&r.dataKey&&!J(a[r.dataKey])){var c=yi(n,"value",a[r.dataKey]);if(c)return c.coordinate+i/2}return n[o]?n[o].coordinate+i/2:null}var s=Re(a,J(u)?r.dataKey:u);return J(s)?null:r.scale(s)},Af=function(t){var r=t.axis,n=t.ticks,i=t.offset,a=t.bandSize,o=t.entry,u=t.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=Re(o,r.dataKey,r.domain[u]);return J(c)?null:r.scale(c)-a/2+i},Kk=function(t){var r=t.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Xk=function(t,r){var n=t.props.stackId;if(ye(n)){var i=r[n];if(i){var a=i.items.indexOf(t);return a>=0?i.stackedData[a]:null}}return null},Vk=function(t){return t.reduce(function(r,n){return[Ea(n.concat([r[0]]).filter(L)),Ta(n.concat([r[1]]).filter(L))]},[1/0,-1/0])},Jp=function(t,r,n){return Object.keys(t).reduce(function(i,a){var o=t[a],u=o.stackedData,c=u.reduce(function(s,f){var l=Vk(f.slice(r,n+1));return[Math.min(s[0],l[0]),Math.max(s[1],l[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},_f=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Pf=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lu=function(t,r,n){if(Y(t))return t(r,n);if(!Array.isArray(t))return r;var i=[];if(L(t[0]))i[0]=n?t[0]:Math.min(t[0],r[0]);else if(_f.test(t[0])){var a=+_f.exec(t[0])[1];i[0]=r[0]-a}else Y(t[0])?i[0]=t[0](r[0]):i[0]=r[0];if(L(t[1]))i[1]=n?t[1]:Math.max(t[1],r[1]);else if(Pf.test(t[1])){var o=+Pf.exec(t[1])[1];i[1]=r[1]+o}else Y(t[1])?i[1]=t[1](r[1]):i[1]=r[1];return i},Xi=function(t,r,n){if(t&&t.scale&&t.scale.bandwidth){var i=t.scale.bandwidth();if(!n||i>0)return i}if(t&&r&&r.length>=2){for(var a=tc(r,function(l){return l.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var s=a[u],f=a[u-1];o=Math.min((s.coordinate||0)-(f.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},$f=function(t,r,n){return!t||!t.length||ja(t,Ne(n,"type.defaultProps.domain"))?r:t},Qp=function(t,r){var n=t.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,s=n.chartType;return Ue(Ue({},Z(t)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:Tc(t),value:Re(r,i),type:c,payload:r,chartType:s})};function wn(e){"@babel/helpers - typeof";return wn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wn(e)}function Tf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ef(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tf(Object(r),!0).forEach(function(n){Yk(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Yk(e,t,r){return t=Zk(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zk(e){var t=Jk(e,"string");return wn(t)==="symbol"?t:String(t)}function Jk(e,t){if(wn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(wn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Vi=Math.PI/180,Qk=function(t){return t*180/Math.PI},Se=function(t,r,n,i){return{x:t+Math.cos(-Vi*i)*n,y:r+Math.sin(-Vi*i)*n}},eI=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},tI=function(t,r){var n=t.x,i=t.y,a=r.cx,o=r.cy,u=eI({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,s=Math.acos(c);return i>o&&(s=2*Math.PI-s),{radius:u,angle:Qk(s),angleInRadian:s}},rI=function(t){var r=t.startAngle,n=t.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},nI=function(t,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return t+u*360},jf=function(t,r){var n=t.x,i=t.y,a=tI({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,s=r.outerRadius;if(o<c||o>s)return!1;if(o===0)return!0;var f=rI(r),l=f.startAngle,h=f.endAngle,d=u,p;if(l<=h){for(;d>h;)d-=360;for(;d<l;)d+=360;p=d>=l&&d<=h}else{for(;d>l;)d-=360;for(;d<h;)d+=360;p=d>=h&&d<=l}return p?Ef(Ef({},r),{},{radius:o,angle:nI(d,r)}):null};function On(e){"@babel/helpers - typeof";return On=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},On(e)}var iI=["offset"];function aI(e){return sI(e)||cI(e)||uI(e)||oI()}function oI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function uI(e,t){if(e){if(typeof e=="string")return fu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fu(e,t)}}function cI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function sI(e){if(Array.isArray(e))return fu(e)}function fu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function lI(e,t){if(e==null)return{};var r=fI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function fI(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Mf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ve(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Mf(Object(r),!0).forEach(function(n){hI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function hI(e,t,r){return t=dI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dI(e){var t=pI(e,"string");return On(t)==="symbol"?t:String(t)}function pI(e,t){if(On(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(On(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Sn(){return Sn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Sn.apply(this,arguments)}var vI=function(t){var r=t.value,n=t.formatter,i=J(t.children)?r:t.children;return Y(n)?n(i):i},yI=function(t,r){var n=Je(r-t),i=Math.min(Math.abs(r-t),360);return n*i},gI=function(t,r,n){var i=t.position,a=t.viewBox,o=t.offset,u=t.className,c=a,s=c.cx,f=c.cy,l=c.innerRadius,h=c.outerRadius,d=c.startAngle,p=c.endAngle,v=c.clockWise,y=(l+h)/2,b=yI(d,p),x=b>=0?1:-1,w,O;i==="insideStart"?(w=d+x*o,O=v):i==="insideEnd"?(w=p-x*o,O=!v):i==="end"&&(w=p+x*o,O=v),O=b<=0?O:!O;var g=Se(s,f,y,w),m=Se(s,f,y,w+(O?1:-1)*359),S="M".concat(g.x,",").concat(g.y,`
    A`).concat(y,",").concat(y,",0,1,").concat(O?0:1,`,
    `).concat(m.x,",").concat(m.y),A=J(t.id)?qn("recharts-radial-line-"):t.id;return P.createElement("text",Sn({},n,{dominantBaseline:"central",className:ne("recharts-radial-bar-label",u)}),P.createElement("defs",null,P.createElement("path",{id:A,d:S})),P.createElement("textPath",{xlinkHref:"#".concat(A)},r))},mI=function(t){var r=t.viewBox,n=t.offset,i=t.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,s=a.outerRadius,f=a.startAngle,l=a.endAngle,h=(f+l)/2;if(i==="outside"){var d=Se(o,u,s+n,h),p=d.x,v=d.y;return{x:p,y:v,textAnchor:p>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var y=(c+s)/2,b=Se(o,u,y,h),x=b.x,w=b.y;return{x,y:w,textAnchor:"middle",verticalAnchor:"middle"}},bI=function(t){var r=t.viewBox,n=t.parentViewBox,i=t.offset,a=t.position,o=r,u=o.x,c=o.y,s=o.width,f=o.height,l=f>=0?1:-1,h=l*i,d=l>0?"end":"start",p=l>0?"start":"end",v=s>=0?1:-1,y=v*i,b=v>0?"end":"start",x=v>0?"start":"end";if(a==="top"){var w={x:u+s/2,y:c-l*i,textAnchor:"middle",verticalAnchor:d};return ve(ve({},w),n?{height:Math.max(c-n.y,0),width:s}:{})}if(a==="bottom"){var O={x:u+s/2,y:c+f+h,textAnchor:"middle",verticalAnchor:p};return ve(ve({},O),n?{height:Math.max(n.y+n.height-(c+f),0),width:s}:{})}if(a==="left"){var g={x:u-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"};return ve(ve({},g),n?{width:Math.max(g.x-n.x,0),height:f}:{})}if(a==="right"){var m={x:u+s+y,y:c+f/2,textAnchor:x,verticalAnchor:"middle"};return ve(ve({},m),n?{width:Math.max(n.x+n.width-m.x,0),height:f}:{})}var S=n?{width:s,height:f}:{};return a==="insideLeft"?ve({x:u+y,y:c+f/2,textAnchor:x,verticalAnchor:"middle"},S):a==="insideRight"?ve({x:u+s-y,y:c+f/2,textAnchor:b,verticalAnchor:"middle"},S):a==="insideTop"?ve({x:u+s/2,y:c+h,textAnchor:"middle",verticalAnchor:p},S):a==="insideBottom"?ve({x:u+s/2,y:c+f-h,textAnchor:"middle",verticalAnchor:d},S):a==="insideTopLeft"?ve({x:u+y,y:c+h,textAnchor:x,verticalAnchor:p},S):a==="insideTopRight"?ve({x:u+s-y,y:c+h,textAnchor:b,verticalAnchor:p},S):a==="insideBottomLeft"?ve({x:u+y,y:c+f-h,textAnchor:x,verticalAnchor:d},S):a==="insideBottomRight"?ve({x:u+s-y,y:c+f-h,textAnchor:b,verticalAnchor:d},S):Le(a)&&(L(a.x)||Wt(a.x))&&(L(a.y)||Wt(a.y))?ve({x:u+cr(a.x,s),y:c+cr(a.y,f),textAnchor:"end",verticalAnchor:"end"},S):ve({x:u+s/2,y:c+f/2,textAnchor:"middle",verticalAnchor:"middle"},S)},xI=function(t){return"cx"in t&&L(t.cx)};function _e(e){var t=e.offset,r=t===void 0?5:t,n=lI(e,iI),i=ve({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,s=i.content,f=i.className,l=f===void 0?"":f,h=i.textBreakAll;if(!a||J(u)&&J(c)&&!U.isValidElement(s)&&!Y(s))return null;if(U.isValidElement(s))return U.cloneElement(s,i);var d;if(Y(s)){if(d=U.createElement(s,i),U.isValidElement(d))return d}else d=vI(i);var p=xI(a),v=Z(i,!0);if(p&&(o==="insideStart"||o==="insideEnd"||o==="end"))return gI(i,d,v);var y=p?mI(i):bI(i);return P.createElement(Di,Sn({className:ne("recharts-label",l)},v,y,{breakAll:h}),d)}_e.displayName="Label";var ev=function(t){var r=t.cx,n=t.cy,i=t.angle,a=t.startAngle,o=t.endAngle,u=t.r,c=t.radius,s=t.innerRadius,f=t.outerRadius,l=t.x,h=t.y,d=t.top,p=t.left,v=t.width,y=t.height,b=t.clockWise,x=t.labelViewBox;if(x)return x;if(L(v)&&L(y)){if(L(l)&&L(h))return{x:l,y:h,width:v,height:y};if(L(d)&&L(p))return{x:d,y:p,width:v,height:y}}return L(l)&&L(h)?{x:l,y:h,width:0,height:0}:L(r)&&L(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:s||0,outerRadius:f||c||u||0,clockWise:b}:t.viewBox?t.viewBox:{}},wI=function(t,r){return t?t===!0?P.createElement(_e,{key:"label-implicit",viewBox:r}):ye(t)?P.createElement(_e,{key:"label-implicit",viewBox:r,value:t}):U.isValidElement(t)?t.type===_e?U.cloneElement(t,{key:"label-implicit",viewBox:r}):P.createElement(_e,{key:"label-implicit",content:t,viewBox:r}):Y(t)?P.createElement(_e,{key:"label-implicit",content:t,viewBox:r}):Le(t)?P.createElement(_e,Sn({viewBox:r},t,{key:"label-implicit"})):null:null},OI=function(t,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&n&&!t.label)return null;var i=t.children,a=ev(t),o=qe(i,_e).map(function(c,s){return U.cloneElement(c,{viewBox:r||a,key:"label-".concat(s)})});if(!n)return o;var u=wI(t.label,r||a);return[u].concat(aI(o))};_e.parseViewBox=ev;_e.renderCallByParent=OI;function SI(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var AI=SI;function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},An(e)}var _I=["valueAccessor"],PI=["data","dataKey","clockWise","id","textBreakAll"];function $I(e){return MI(e)||jI(e)||EI(e)||TI()}function TI(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function EI(e,t){if(e){if(typeof e=="string")return hu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hu(e,t)}}function jI(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function MI(e){if(Array.isArray(e))return hu(e)}function hu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Yi.apply(this,arguments)}function Cf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function kf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Cf(Object(r),!0).forEach(function(n){CI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Cf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function CI(e,t,r){return t=kI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function kI(e){var t=II(e,"string");return An(t)==="symbol"?t:String(t)}function II(e,t){if(An(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(An(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function If(e,t){if(e==null)return{};var r=DI(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function DI(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}var NI=function(t){return Array.isArray(t.value)?AI(t.value):t.value};function Et(e){var t=e.valueAccessor,r=t===void 0?NI:t,n=If(e,_I),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,s=If(n,PI);return!i||!i.length?null:P.createElement(he,{className:"recharts-label-list"},i.map(function(f,l){var h=J(a)?r(f,l):Re(f&&f.payload,a),d=J(u)?{}:{id:"".concat(u,"-").concat(l)};return P.createElement(_e,Yi({},Z(f,!0),s,d,{parentViewBox:f.parentViewBox,value:h,textBreakAll:c,viewBox:_e.parseViewBox(J(o)?f:kf(kf({},f),{},{clockWise:o})),key:"label-".concat(l),index:l}))}))}Et.displayName="LabelList";function RI(e,t){return e?e===!0?P.createElement(Et,{key:"labelList-implicit",data:t}):P.isValidElement(e)||Y(e)?P.createElement(Et,{key:"labelList-implicit",data:t,content:e}):Le(e)?P.createElement(Et,Yi({data:t},e,{key:"labelList-implicit"})):null:null}function LI(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&r&&!e.label)return null;var n=e.children,i=qe(n,Et).map(function(o,u){return U.cloneElement(o,{data:t,key:"labelList-".concat(u)})});if(!r)return i;var a=RI(e.label,t);return[a].concat($I(i))}Et.renderCallByParent=LI;function _n(e){"@babel/helpers - typeof";return _n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_n(e)}function du(){return du=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},du.apply(this,arguments)}function Df(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Nf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Df(Object(r),!0).forEach(function(n){BI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Df(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function BI(e,t,r){return t=FI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FI(e){var t=WI(e,"string");return _n(t)==="symbol"?t:String(t)}function WI(e,t){if(_n(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(_n(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var UI=function(t,r){var n=Je(r-t),i=Math.min(Math.abs(r-t),359.999);return n*i},hi=function(t){var r=t.cx,n=t.cy,i=t.radius,a=t.angle,o=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,f=c*(u?1:-1)+i,l=Math.asin(c/f)/Vi,h=s?a:a+o*l,d=Se(r,n,f,h),p=Se(r,n,i,h),v=s?a-o*l:a,y=Se(r,n,f*Math.cos(l*Vi),v);return{center:d,circleTangency:p,lineTangency:y,theta:l}},tv=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.startAngle,u=t.endAngle,c=UI(o,u),s=o+c,f=Se(r,n,a,o),l=Se(r,n,a,s),h="M ".concat(f.x,",").concat(f.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>s),`,
    `).concat(l.x,",").concat(l.y,`
  `);if(i>0){var d=Se(r,n,i,o),p=Se(r,n,i,s);h+="L ".concat(p.x,",").concat(p.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=s),`,
            `).concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},zI=function(t){var r=t.cx,n=t.cy,i=t.innerRadius,a=t.outerRadius,o=t.cornerRadius,u=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,f=t.endAngle,l=Je(f-s),h=hi({cx:r,cy:n,radius:a,angle:s,sign:l,cornerRadius:o,cornerIsExternal:c}),d=h.circleTangency,p=h.lineTangency,v=h.theta,y=hi({cx:r,cy:n,radius:a,angle:f,sign:-l,cornerRadius:o,cornerIsExternal:c}),b=y.circleTangency,x=y.lineTangency,w=y.theta,O=c?Math.abs(s-f):Math.abs(s-f)-v-w;if(O<0)return u?"M ".concat(p.x,",").concat(p.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):tv({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f});var g="M ".concat(p.x,",").concat(p.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(O>180),",").concat(+(l<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(x.x,",").concat(x.y,`
  `);if(i>0){var m=hi({cx:r,cy:n,radius:i,angle:s,sign:l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),S=m.circleTangency,A=m.lineTangency,_=m.theta,E=hi({cx:r,cy:n,radius:i,angle:f,sign:-l,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),$=E.circleTangency,T=E.lineTangency,M=E.theta,k=c?Math.abs(s-f):Math.abs(s-f)-_-M;if(k<0&&o===0)return"".concat(g,"L").concat(r,",").concat(n,"Z");g+="L".concat(T.x,",").concat(T.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat($.x,",").concat($.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(k>180),",").concat(+(l>0),",").concat(S.x,",").concat(S.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(l<0),",").concat(A.x,",").concat(A.y,"Z")}else g+="L".concat(r,",").concat(n,"Z");return g},qI={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},rv=function(t){var r=Nf(Nf({},qI),t),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,l=r.endAngle,h=r.className;if(o<a||f===l)return null;var d=ne("recharts-sector",h),p=o-a,v=cr(u,p,0,!0),y;return v>0&&Math.abs(f-l)<360?y=zI({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(v,p/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:f,endAngle:l}):y=tv({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:f,endAngle:l}),P.createElement("path",du({},Z(r,!0),{className:d,d:y,role:"img"}))};function Pn(e){"@babel/helpers - typeof";return Pn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Pn(e)}function pu(){return pu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pu.apply(this,arguments)}function Rf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Lf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rf(Object(r),!0).forEach(function(n){GI(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function GI(e,t,r){return t=HI(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function HI(e){var t=KI(e,"string");return Pn(t)==="symbol"?t:String(t)}function KI(e,t){if(Pn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Pn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Bf={curveBasisClosed:S0,curveBasisOpen:A0,curveBasis:O0,curveBumpX:c0,curveBumpY:s0,curveLinearClosed:_0,curveLinear:ga,curveMonotoneX:P0,curveMonotoneY:$0,curveNatural:T0,curveStep:E0,curveStepAfter:M0,curveStepBefore:j0},di=function(t){return t.x===+t.x&&t.y===+t.y},zr=function(t){return t.x},qr=function(t){return t.y},XI=function(t,r){if(Y(t))return t;var n="curve".concat(va(t));return(n==="curveMonotone"||n==="curveBump")&&r?Bf["".concat(n).concat(r==="vertical"?"Y":"X")]:Bf[n]||ga},VI=function(t){var r=t.type,n=r===void 0?"linear":r,i=t.points,a=i===void 0?[]:i,o=t.baseLine,u=t.layout,c=t.connectNulls,s=c===void 0?!1:c,f=XI(n,u),l=s?a.filter(function(v){return di(v)}):a,h;if(Array.isArray(o)){var d=s?o.filter(function(v){return di(v)}):o,p=l.map(function(v,y){return Lf(Lf({},v),{},{base:d[y]})});return u==="vertical"?h=ri().y(qr).x1(zr).x0(function(v){return v.base.x}):h=ri().x(zr).y1(qr).y0(function(v){return v.base.y}),h.defined(di).curve(f),h(p)}return u==="vertical"&&L(o)?h=ri().y(qr).x1(zr).x0(o):L(o)?h=ri().x(zr).y1(qr).y0(o):h=rd().x(zr).y(qr),h.defined(di).curve(f),h(l)},vu=function(t){var r=t.className,n=t.points,i=t.path,a=t.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?VI(t):i;return P.createElement("path",pu({},Z(t),gi(t),{className:ne("recharts-curve",r),d:o,ref:a}))};function $n(e){"@babel/helpers - typeof";return $n=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$n(e)}function Zi(){return Zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Zi.apply(this,arguments)}function YI(e,t){return e2(e)||QI(e,t)||JI(e,t)||ZI()}function ZI(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function JI(e,t){if(e){if(typeof e=="string")return Ff(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ff(e,t)}}function Ff(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function QI(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function e2(e){if(Array.isArray(e))return e}function Wf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Uf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Wf(Object(r),!0).forEach(function(n){t2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Wf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function t2(e,t,r){return t=r2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function r2(e){var t=n2(e,"string");return $n(t)==="symbol"?t:String(t)}function n2(e,t){if($n(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if($n(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var zf=function(t,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,s=i>=0&&n>=0||i<0&&n<0?1:0,f;if(o>0&&a instanceof Array){for(var l=[0,0,0,0],h=0,d=4;h<d;h++)l[h]=a[h]>o?o:a[h];f="M".concat(t,",").concat(r+u*l[0]),l[0]>0&&(f+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(r)),f+="L ".concat(t+n-c*l[1],",").concat(r),l[1]>0&&(f+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,`,
        `).concat(t+n,",").concat(r+u*l[1])),f+="L ".concat(t+n,",").concat(r+i-u*l[2]),l[2]>0&&(f+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,`,
        `).concat(t+n-c*l[2],",").concat(r+i)),f+="L ".concat(t+c*l[3],",").concat(r+i),l[3]>0&&(f+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,`,
        `).concat(t,",").concat(r+i-u*l[3])),f+="Z"}else if(o>0&&a===+a&&a>0){var p=Math.min(o,a);f="M ".concat(t,",").concat(r+u*p,`
            A `).concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+c*p,",").concat(r,`
            L `).concat(t+n-c*p,",").concat(r,`
            A `).concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+n,",").concat(r+u*p,`
            L `).concat(t+n,",").concat(r+i-u*p,`
            A `).concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+n-c*p,",").concat(r+i,`
            L `).concat(t+c*p,",").concat(r+i,`
            A `).concat(p,",").concat(p,",0,0,").concat(s,",").concat(t,",").concat(r+i-u*p," Z")}else f="M ".concat(t,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return f},i2=function(t,r){if(!t||!r)return!1;var n=t.x,i=t.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var s=Math.min(a,a+u),f=Math.max(a,a+u),l=Math.min(o,o+c),h=Math.max(o,o+c);return n>=s&&n<=f&&i>=l&&i<=h}return!1},a2={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},Ec=function(t){var r=Uf(Uf({},a2),t),n=U.useRef(),i=U.useState(-1),a=YI(i,2),o=a[0],u=a[1];U.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var O=n.current.getTotalLength();O&&u(O)}catch{}},[]);var c=r.x,s=r.y,f=r.width,l=r.height,h=r.radius,d=r.className,p=r.animationEasing,v=r.animationDuration,y=r.animationBegin,b=r.isAnimationActive,x=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||f===0||l===0)return null;var w=ne("recharts-rectangle",d);return x?P.createElement(dt,{canBegin:o>0,from:{width:f,height:l,x:c,y:s},to:{width:f,height:l,x:c,y:s},duration:v,animationEasing:p,isActive:x},function(O){var g=O.width,m=O.height,S=O.x,A=O.y;return P.createElement(dt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,isActive:b,easing:p},P.createElement("path",Zi({},Z(r,!0),{className:w,d:zf(S,A,g,m,h),ref:n})))}):P.createElement("path",Zi({},Z(r,!0),{className:w,d:zf(c,s,f,l,h)}))};function yu(){return yu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},yu.apply(this,arguments)}var jc=function(t){var r=t.cx,n=t.cy,i=t.r,a=t.className,o=ne("recharts-dot",a);return r===+r&&n===+n&&i===+i?P.createElement("circle",yu({},Z(t),gi(t),{className:o,cx:r,cy:n,r:i})):null};function Tn(e){"@babel/helpers - typeof";return Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tn(e)}var o2=["x","y","top","left","width","height","className"];function gu(){return gu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},gu.apply(this,arguments)}function qf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function u2(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?qf(Object(r),!0).forEach(function(n){c2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):qf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function c2(e,t,r){return t=s2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s2(e){var t=l2(e,"string");return Tn(t)==="symbol"?t:String(t)}function l2(e,t){if(Tn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Tn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function f2(e,t){if(e==null)return{};var r=h2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function h2(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}var d2=function(t,r,n,i,a,o){return"M".concat(t,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},p2=function(t){var r=t.x,n=r===void 0?0:r,i=t.y,a=i===void 0?0:i,o=t.top,u=o===void 0?0:o,c=t.left,s=c===void 0?0:c,f=t.width,l=f===void 0?0:f,h=t.height,d=h===void 0?0:h,p=t.className,v=f2(t,o2),y=u2({x:n,y:a,top:u,left:s,width:l,height:d},v);return!L(n)||!L(a)||!L(l)||!L(d)||!L(u)||!L(s)?null:P.createElement("path",gu({},Z(y,!0),{className:ne("recharts-cross",p),d:d2(n,a,l,d,u,s)}))},v2=Ad,y2=v2(Object.getPrototypeOf,Object),g2=y2,m2=gt,b2=g2,x2=mt,w2="[object Object]",O2=Function.prototype,S2=Object.prototype,nv=O2.toString,A2=S2.hasOwnProperty,_2=nv.call(Object);function P2(e){if(!x2(e)||m2(e)!=w2)return!1;var t=b2(e);if(t===null)return!0;var r=A2.call(t,"constructor")&&t.constructor;return typeof r=="function"&&r instanceof r&&nv.call(r)==_2}var $2=P2,T2=gt,E2=mt,j2="[object Boolean]";function M2(e){return e===!0||e===!1||E2(e)&&T2(e)==j2}var C2=M2;function En(e){"@babel/helpers - typeof";return En=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},En(e)}function Ji(){return Ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ji.apply(this,arguments)}function k2(e,t){return R2(e)||N2(e,t)||D2(e,t)||I2()}function I2(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function D2(e,t){if(e){if(typeof e=="string")return Gf(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Gf(e,t)}}function Gf(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function N2(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function R2(e){if(Array.isArray(e))return e}function Hf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Kf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Hf(Object(r),!0).forEach(function(n){L2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Hf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function L2(e,t,r){return t=B2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B2(e){var t=F2(e,"string");return En(t)==="symbol"?t:String(t)}function F2(e,t){if(En(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(En(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Xf=function(t,r,n,i,a){var o=n-i,u;return u="M ".concat(t,",").concat(r),u+="L ".concat(t+n,",").concat(r),u+="L ".concat(t+n-o/2,",").concat(r+a),u+="L ".concat(t+n-o/2-i,",").concat(r+a),u+="L ".concat(t,",").concat(r," Z"),u},W2={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},U2=function(t){var r=Kf(Kf({},W2),t),n=U.useRef(),i=U.useState(-1),a=k2(i,2),o=a[0],u=a[1];U.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,s=r.y,f=r.upperWidth,l=r.lowerWidth,h=r.height,d=r.className,p=r.animationEasing,v=r.animationDuration,y=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||s!==+s||f!==+f||l!==+l||h!==+h||f===0&&l===0||h===0)return null;var x=ne("recharts-trapezoid",d);return b?P.createElement(dt,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:h,x:c,y:s},to:{upperWidth:f,lowerWidth:l,height:h,x:c,y:s},duration:v,animationEasing:p,isActive:b},function(w){var O=w.upperWidth,g=w.lowerWidth,m=w.height,S=w.x,A=w.y;return P.createElement(dt,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:v,easing:p},P.createElement("path",Ji({},Z(r,!0),{className:x,d:Xf(S,A,O,g,m),ref:n})))}):P.createElement("g",null,P.createElement("path",Ji({},Z(r,!0),{className:x,d:Xf(c,s,f,l,h)})))},z2=["option","shapeType","propTransformer","activeClassName","isActive"];function jn(e){"@babel/helpers - typeof";return jn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jn(e)}function q2(e,t){if(e==null)return{};var r=G2(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function G2(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Vf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vf(Object(r),!0).forEach(function(n){H2(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function H2(e,t,r){return t=K2(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function K2(e){var t=X2(e,"string");return jn(t)==="symbol"?t:String(t)}function X2(e,t){if(jn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(jn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function V2(e,t){return Qi(Qi({},t),e)}function Y2(e,t){return e==="symbols"}function Yf(e){var t=e.shapeType,r=e.elementProps;switch(t){case"rectangle":return P.createElement(Ec,r);case"trapezoid":return P.createElement(U2,r);case"sector":return P.createElement(rv,r);case"symbols":if(Y2(t))return P.createElement(Ku,r);break;default:return null}}function Z2(e){return U.isValidElement(e)?e.props:e}function J2(e){var t=e.option,r=e.shapeType,n=e.propTransformer,i=n===void 0?V2:n,a=e.activeClassName,o=a===void 0?"recharts-active-shape":a,u=e.isActive,c=q2(e,z2),s;if(U.isValidElement(t))s=U.cloneElement(t,Qi(Qi({},c),Z2(t)));else if(Y(t))s=t(c);else if($2(t)&&!C2(t)){var f=i(t,c);s=P.createElement(Yf,{shapeType:r,elementProps:f})}else{var l=c;s=P.createElement(Yf,{shapeType:r,elementProps:l})}return u?P.createElement(he,{className:o},s):s}function ka(e,t){return t!=null&&"trapezoids"in e.props}function Ia(e,t){return t!=null&&"sectors"in e.props}function Mn(e,t){return t!=null&&"points"in e.props}function Q2(e,t){var r,n,i=e.x===(t==null||(r=t.labelViewBox)===null||r===void 0?void 0:r.x)||e.x===t.x,a=e.y===(t==null||(n=t.labelViewBox)===null||n===void 0?void 0:n.y)||e.y===t.y;return i&&a}function eD(e,t){var r=e.endAngle===t.endAngle,n=e.startAngle===t.startAngle;return r&&n}function tD(e,t){var r=e.x===t.x,n=e.y===t.y,i=e.z===t.z;return r&&n&&i}function rD(e,t){var r;return ka(e,t)?r=Q2:Ia(e,t)?r=eD:Mn(e,t)&&(r=tD),r}function nD(e,t){var r;return ka(e,t)?r="trapezoids":Ia(e,t)?r="sectors":Mn(e,t)&&(r="points"),r}function iD(e,t){if(ka(e,t)){var r;return(r=t.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(Ia(e,t)){var n;return(n=t.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Mn(e,t)?t.payload:{}}function aD(e){var t=e.activeTooltipItem,r=e.graphicalItem,n=e.itemData,i=nD(r,t),a=iD(r,t),o=n.filter(function(c,s){var f=ja(a,c),l=r.props[i].filter(function(p){var v=rD(r,t);return v(p,t)}),h=r.props[i].indexOf(l[l.length-1]),d=s===h;return f&&d}),u=n.indexOf(o[o.length-1]);return u}var oD=Math.ceil,uD=Math.max;function cD(e,t,r,n){for(var i=-1,a=uD(oD((t-e)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=e,e+=r;return o}var sD=cD,lD=Vd,Zf=1/0,fD=17976931348623157e292;function hD(e){if(!e)return e===0?e:0;if(e=lD(e),e===Zf||e===-Zf){var t=e<0?-1:1;return t*fD}return e===e?e:0}var iv=hD,dD=sD,pD=wa,lo=iv;function vD(e){return function(t,r,n){return n&&typeof n!="number"&&pD(t,r,n)&&(r=n=void 0),t=lo(t),r===void 0?(r=t,t=0):r=lo(r),n=n===void 0?t<r?1:-1:lo(n),dD(t,r,n,e)}}var yD=vD,gD=yD,mD=gD(),ea=mD;function Cn(e){"@babel/helpers - typeof";return Cn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Cn(e)}function Jf(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Qf(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Jf(Object(r),!0).forEach(function(n){av(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Jf(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function av(e,t,r){return t=bD(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bD(e){var t=xD(e,"string");return Cn(t)==="symbol"?t:String(t)}function xD(e,t){if(Cn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Cn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wD=["Webkit","Moz","O","ms"],OD=function(t,r){if(!t)return null;var n=t.replace(/(\w)/,function(a){return a.toUpperCase()}),i=wD.reduce(function(a,o){return Qf(Qf({},a),{},av({},o+n,r))},{});return i[t]=r,i};function br(e){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},br(e)}function ta(){return ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ta.apply(this,arguments)}function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(n){Ie(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function SD(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function th(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ov(n.key),n)}}function AD(e,t,r){return t&&th(e.prototype,t),r&&th(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _D(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mu(e,t)}function mu(e,t){return mu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},mu(e,t)}function PD(e){var t=TD();return function(){var n=ra(e),i;if(t){var a=ra(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return $D(this,i)}}function $D(e,t){if(t&&(br(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tt(e)}function tt(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function TD(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ra(e){return ra=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ra(e)}function Ie(e,t,r){return t=ov(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ov(e){var t=ED(e,"string");return br(t)==="symbol"?t:String(t)}function ED(e,t){if(br(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(br(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var jD=function(t){var r=t.data,n=t.startIndex,i=t.endIndex,a=t.x,o=t.width,u=t.travellerWidth;if(!r||!r.length)return{};var c=r.length,s=Yr().domain(ea(0,c)).range([a,a+o-u]),f=s.domain().map(function(l){return s(l)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:s(n),endX:s(i),scale:s,scaleValues:f}},rh=function(t){return t.changedTouches&&!!t.changedTouches.length},kn=function(e){_D(r,e);var t=PD(r);function r(n){var i;return SD(this,r),i=t.call(this,n),Ie(tt(i),"handleDrag",function(a){i.leaveTimer&&(clearTimeout(i.leaveTimer),i.leaveTimer=null),i.state.isTravellerMoving?i.handleTravellerMove(a):i.state.isSlideMoving&&i.handleSlideDrag(a)}),Ie(tt(i),"handleTouchMove",function(a){a.changedTouches!=null&&a.changedTouches.length>0&&i.handleDrag(a.changedTouches[0])}),Ie(tt(i),"handleDragEnd",function(){i.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var a=i.props,o=a.endIndex,u=a.onDragEnd,c=a.startIndex;u==null||u({endIndex:o,startIndex:c})}),i.detachDragEndListener()}),Ie(tt(i),"handleLeaveWrapper",function(){(i.state.isTravellerMoving||i.state.isSlideMoving)&&(i.leaveTimer=window.setTimeout(i.handleDragEnd,i.props.leaveTimeOut))}),Ie(tt(i),"handleEnterSlideOrTraveller",function(){i.setState({isTextActive:!0})}),Ie(tt(i),"handleLeaveSlideOrTraveller",function(){i.setState({isTextActive:!1})}),Ie(tt(i),"handleSlideDragStart",function(a){var o=rh(a)?a.changedTouches[0]:a;i.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:o.pageX}),i.attachDragEndListener()}),i.travellerDragStartHandlers={startX:i.handleTravellerDragStart.bind(tt(i),"startX"),endX:i.handleTravellerDragStart.bind(tt(i),"endX")},i.state={},i}return AD(r,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(i){var a=i.startX,o=i.endX,u=this.state.scaleValues,c=this.props,s=c.gap,f=c.data,l=f.length-1,h=Math.min(a,o),d=Math.max(a,o),p=r.getIndexInRange(u,h),v=r.getIndexInRange(u,d);return{startIndex:p-p%s,endIndex:v===l?l:v-v%s}}},{key:"getTextOfTick",value:function(i){var a=this.props,o=a.data,u=a.tickFormatter,c=a.dataKey,s=Re(o[i],c,i);return Y(u)?u(s,i):s}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(i){var a=this.state,o=a.slideMoveStartX,u=a.startX,c=a.endX,s=this.props,f=s.x,l=s.width,h=s.travellerWidth,d=s.startIndex,p=s.endIndex,v=s.onChange,y=i.pageX-o;y>0?y=Math.min(y,f+l-h-c,f+l-h-u):y<0&&(y=Math.max(y,f-u,f-c));var b=this.getIndex({startX:u+y,endX:c+y});(b.startIndex!==d||b.endIndex!==p)&&v&&v(b),this.setState({startX:u+y,endX:c+y,slideMoveStartX:i.pageX})}},{key:"handleTravellerDragStart",value:function(i,a){var o=rh(a)?a.changedTouches[0]:a;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:i,brushMoveStartX:o.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(i){var a,o=this.state,u=o.brushMoveStartX,c=o.movingTravellerId,s=o.endX,f=o.startX,l=this.state[c],h=this.props,d=h.x,p=h.width,v=h.travellerWidth,y=h.onChange,b=h.gap,x=h.data,w={startX:this.state.startX,endX:this.state.endX},O=i.pageX-u;O>0?O=Math.min(O,d+p-v-l):O<0&&(O=Math.max(O,d-l)),w[c]=l+O;var g=this.getIndex(w),m=g.startIndex,S=g.endIndex,A=function(){var E=x.length-1;return c==="startX"&&(s>f?m%b===0:S%b===0)||s<f&&S===E||c==="endX"&&(s>f?S%b===0:m%b===0)||s>f&&S===E};this.setState((a={},Ie(a,c,l+O),Ie(a,"brushMoveStartX",i.pageX),a),function(){y&&A()&&y(g)})}},{key:"handleTravellerMoveKeyboard",value:function(i,a){var o=this,u=this.state,c=u.scaleValues,s=u.startX,f=u.endX,l=this.state[a],h=c.indexOf(l);if(h!==-1){var d=h+i;if(!(d===-1||d>=c.length)){var p=c[d];a==="startX"&&p>=f||a==="endX"&&p<=s||this.setState(Ie({},a,p),function(){o.props.onChange(o.getIndex({startX:o.state.startX,endX:o.state.endX}))})}}}},{key:"renderBackground",value:function(){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.fill,f=i.stroke;return P.createElement("rect",{stroke:f,fill:s,x:a,y:o,width:u,height:c})}},{key:"renderPanorama",value:function(){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.data,f=i.children,l=i.padding,h=U.Children.only(f);return h?P.cloneElement(h,{x:a,y:o,width:u,height:c,margin:l,compact:!0,data:s}):null}},{key:"renderTravellerLayer",value:function(i,a){var o=this,u=this.props,c=u.y,s=u.travellerWidth,f=u.height,l=u.traveller,h=u.ariaLabel,d=u.data,p=u.startIndex,v=u.endIndex,y=Math.max(i,this.props.x),b=fo(fo({},Z(this.props)),{},{x:y,y:c,width:s,height:f}),x=h||"Min value: ".concat(d[p].name,", Max value: ").concat(d[v].name);return P.createElement(he,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":i,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[a],onTouchStart:this.travellerDragStartHandlers[a],onKeyDown:function(O){["ArrowLeft","ArrowRight"].includes(O.key)&&(O.preventDefault(),O.stopPropagation(),o.handleTravellerMoveKeyboard(O.key==="ArrowRight"?1:-1,a))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},r.renderTraveller(l,b))}},{key:"renderSlide",value:function(i,a){var o=this.props,u=o.y,c=o.height,s=o.stroke,f=o.travellerWidth,l=Math.min(i,a)+f,h=Math.max(Math.abs(a-i)-f,0);return P.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:s,fillOpacity:.2,x:l,y:u,width:h,height:c})}},{key:"renderText",value:function(){var i=this.props,a=i.startIndex,o=i.endIndex,u=i.y,c=i.height,s=i.travellerWidth,f=i.stroke,l=this.state,h=l.startX,d=l.endX,p=5,v={pointerEvents:"none",fill:f};return P.createElement(he,{className:"recharts-brush-texts"},P.createElement(Di,ta({textAnchor:"end",verticalAnchor:"middle",x:Math.min(h,d)-p,y:u+c/2},v),this.getTextOfTick(a)),P.createElement(Di,ta({textAnchor:"start",verticalAnchor:"middle",x:Math.max(h,d)+s+p,y:u+c/2},v),this.getTextOfTick(o)))}},{key:"render",value:function(){var i=this.props,a=i.data,o=i.className,u=i.children,c=i.x,s=i.y,f=i.width,l=i.height,h=i.alwaysShowText,d=this.state,p=d.startX,v=d.endX,y=d.isTextActive,b=d.isSlideMoving,x=d.isTravellerMoving,w=d.isTravellerFocused;if(!a||!a.length||!L(c)||!L(s)||!L(f)||!L(l)||f<=0||l<=0)return null;var O=ne("recharts-brush",o),g=P.Children.count(u)===1,m=OD("userSelect","none");return P.createElement(he,{className:O,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:m},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(p,v),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(v,"endX"),(y||b||x||w||h)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(i){var a=i.x,o=i.y,u=i.width,c=i.height,s=i.stroke,f=Math.floor(o+c/2)-1;return P.createElement(P.Fragment,null,P.createElement("rect",{x:a,y:o,width:u,height:c,fill:s,stroke:"none"}),P.createElement("line",{x1:a+1,y1:f,x2:a+u-1,y2:f,fill:"none",stroke:"#fff"}),P.createElement("line",{x1:a+1,y1:f+2,x2:a+u-1,y2:f+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(i,a){var o;return P.isValidElement(i)?o=P.cloneElement(i,a):Y(i)?o=i(a):o=r.renderDefaultTraveller(a),o}},{key:"getDerivedStateFromProps",value:function(i,a){var o=i.data,u=i.width,c=i.x,s=i.travellerWidth,f=i.updateId,l=i.startIndex,h=i.endIndex;if(o!==a.prevData||f!==a.prevUpdateId)return fo({prevData:o,prevTravellerWidth:s,prevUpdateId:f,prevX:c,prevWidth:u},o&&o.length?jD({data:o,width:u,x:c,travellerWidth:s,startIndex:l,endIndex:h}):{scale:null,scaleValues:null});if(a.scale&&(u!==a.prevWidth||c!==a.prevX||s!==a.prevTravellerWidth)){a.scale.range([c,c+u-s]);var d=a.scale.domain().map(function(p){return a.scale(p)});return{prevData:o,prevTravellerWidth:s,prevUpdateId:f,prevX:c,prevWidth:u,startX:a.scale(i.startIndex),endX:a.scale(i.endIndex),scaleValues:d}}return null}},{key:"getIndexInRange",value:function(i,a){for(var o=i.length,u=0,c=o-1;c-u>1;){var s=Math.floor((u+c)/2);i[s]>a?c=s:u=s}return a>=i[c]?c:u}}]),r}(U.PureComponent);Ie(kn,"displayName","Brush");Ie(kn,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var MD=ec;function CD(e,t){var r;return MD(e,function(n,i,a){return r=t(n,i,a),!r}),!!r}var kD=CD,ID=gd,DD=jt,ND=kD,RD=je,LD=wa;function BD(e,t,r){var n=RD(e)?ID:ND;return r&&LD(e,t,r)&&(t=void 0),n(e,DD(t))}var FD=BD,it=function(t,r){var n=t.alwaysShow,i=t.ifOverflow;return n&&(i="extendDomain"),i===r},nh=Ld;function WD(e,t,r){t=="__proto__"&&nh?nh(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}var UD=WD,zD=UD,qD=Nd,GD=jt;function HD(e,t){var r={};return t=GD(t),qD(e,function(n,i,a){zD(r,i,t(n,i,a))}),r}var KD=HD;function XD(e,t){for(var r=-1,n=e==null?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}var VD=XD,YD=ec;function ZD(e,t){var r=!0;return YD(e,function(n,i,a){return r=!!t(n,i,a),r}),r}var JD=ZD,QD=VD,eN=JD,tN=jt,rN=je,nN=wa;function iN(e,t,r){var n=rN(e)?QD:eN;return r&&nN(e,t,r)&&(t=void 0),n(e,tN(t))}var uv=iN;function In(e){"@babel/helpers - typeof";return In=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},In(e)}var aN=["x","y"];function bu(){return bu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},bu.apply(this,arguments)}function ih(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Gr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?ih(Object(r),!0).forEach(function(n){oN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ih(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function oN(e,t,r){return t=uN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uN(e){var t=cN(e,"string");return In(t)==="symbol"?t:String(t)}function cN(e,t){if(In(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(In(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function sN(e,t){if(e==null)return{};var r=lN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function lN(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function fN(e,t){var r=e.x,n=e.y,i=sN(e,aN),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),s="".concat(t.height||i.height),f=parseInt(s,10),l="".concat(t.width||i.width),h=parseInt(l,10);return Gr(Gr(Gr(Gr(Gr({},t),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:f,width:h,name:t.name,radius:t.radius})}function ah(e){return P.createElement(J2,bu({shapeType:"rectangle",propTransformer:fN,activeClassName:"recharts-active-bar"},e))}var hN=["value","background"];function xr(e){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xr(e)}function dN(e,t){if(e==null)return{};var r=pN(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function pN(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function na(){return na=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},na.apply(this,arguments)}function oh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function me(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?oh(Object(r),!0).forEach(function(n){$t(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):oh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function vN(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function uh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,cv(n.key),n)}}function yN(e,t,r){return t&&uh(e.prototype,t),r&&uh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function gN(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xu(e,t)}function xu(e,t){return xu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},xu(e,t)}function mN(e){var t=xN();return function(){var n=ia(e),i;if(t){var a=ia(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return bN(this,i)}}function bN(e,t){if(t&&(xr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Kr(e)}function Kr(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xN(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ia(e){return ia=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ia(e)}function $t(e,t,r){return t=cv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function cv(e){var t=wN(e,"string");return xr(t)==="symbol"?t:String(t)}function wN(e,t){if(xr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(xr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Da=function(e){gN(r,e);var t=mN(r);function r(){var n;vN(this,r);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=t.call.apply(t,[this].concat(a)),$t(Kr(n),"state",{isAnimationFinished:!1}),$t(Kr(n),"id",qn("recharts-bar-")),$t(Kr(n),"handleAnimationEnd",function(){var u=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),u&&u()}),$t(Kr(n),"handleAnimationStart",function(){var u=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),u&&u()}),n}return yN(r,[{key:"renderRectanglesStatically",value:function(i){var a=this,o=this.props,u=o.shape,c=o.dataKey,s=o.activeIndex,f=o.activeBar,l=Z(this.props);return i&&i.map(function(h,d){var p=d===s,v=p?f:u,y=me(me(me({},l),h),{},{isActive:p,option:v,index:d,dataKey:c,onAnimationStart:a.handleAnimationStart,onAnimationEnd:a.handleAnimationEnd});return P.createElement(he,na({className:"recharts-bar-rectangle"},mi(a.props,h,d),{key:"rectangle-".concat(h==null?void 0:h.x,"-").concat(h==null?void 0:h.y,"-").concat(h==null?void 0:h.value)}),P.createElement(ah,y))})}},{key:"renderRectanglesWithAnimation",value:function(){var i=this,a=this.props,o=a.data,u=a.layout,c=a.isAnimationActive,s=a.animationBegin,f=a.animationDuration,l=a.animationEasing,h=a.animationId,d=this.state.prevData;return P.createElement(dt,{begin:s,duration:f,isActive:c,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(p){var v=p.t,y=o.map(function(b,x){var w=d&&d[x];if(w){var O=Ye(w.x,b.x),g=Ye(w.y,b.y),m=Ye(w.width,b.width),S=Ye(w.height,b.height);return me(me({},b),{},{x:O(v),y:g(v),width:m(v),height:S(v)})}if(u==="horizontal"){var A=Ye(0,b.height),_=A(v);return me(me({},b),{},{y:b.y+b.height-_,height:_})}var E=Ye(0,b.width),$=E(v);return me(me({},b),{},{width:$})});return P.createElement(he,null,i.renderRectanglesStatically(y))})}},{key:"renderRectangles",value:function(){var i=this.props,a=i.data,o=i.isAnimationActive,u=this.state.prevData;return o&&a&&a.length&&(!u||!ja(u,a))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(a)}},{key:"renderBackground",value:function(){var i=this,a=this.props,o=a.data,u=a.dataKey,c=a.activeIndex,s=Z(this.props.background);return o.map(function(f,l){f.value;var h=f.background,d=dN(f,hN);if(!h)return null;var p=me(me(me(me(me({},d),{},{fill:"#eee"},h),s),mi(i.props,f,l)),{},{onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd,dataKey:u,index:l,key:"background-bar-".concat(l),className:"recharts-bar-background-rectangle"});return P.createElement(ah,na({option:i.props.background,isActive:l===c},p))})}},{key:"renderErrorBar",value:function(i,a){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,u=o.data,c=o.xAxis,s=o.yAxis,f=o.layout,l=o.children,h=qe(l,Yn);if(!h)return null;var d=f==="vertical"?u[0].height/2:u[0].width/2,p=function(b,x){var w=Array.isArray(b.value)?b.value[1]:b.value;return{x:b.x,y:b.y,value:w,errorVal:Re(b,x)}},v={clipPath:i?"url(#clipPath-".concat(a,")"):null};return P.createElement(he,v,h.map(function(y){return P.cloneElement(y,{key:"error-bar-".concat(a,"-").concat(y.props.dataKey),data:u,xAxis:c,yAxis:s,layout:f,offset:d,dataPointFormatter:p})}))}},{key:"render",value:function(){var i=this.props,a=i.hide,o=i.data,u=i.className,c=i.xAxis,s=i.yAxis,f=i.left,l=i.top,h=i.width,d=i.height,p=i.isAnimationActive,v=i.background,y=i.id;if(a||!o||!o.length)return null;var b=this.state.isAnimationFinished,x=ne("recharts-bar",u),w=c&&c.allowDataOverflow,O=s&&s.allowDataOverflow,g=w||O,m=J(y)?this.id:y;return P.createElement(he,{className:x},w||O?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(m)},P.createElement("rect",{x:w?f:f-h/2,y:O?l:l-d/2,width:w?h:h*2,height:O?d:d*2}))):null,P.createElement(he,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(m,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,m),(!p||b)&&Et.renderCallByParent(this.props,o))}}],[{key:"getDerivedStateFromProps",value:function(i,a){return i.animationId!==a.prevAnimationId?{prevAnimationId:i.animationId,curData:i.data,prevData:a.curData}:i.data!==a.curData?{curData:i.data}:null}}]),r}(U.PureComponent);$t(Da,"displayName","Bar");$t(Da,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!0,isAnimationActive:!ft.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});$t(Da,"getComposedData",function(e){var t=e.props,r=e.item,n=e.barPosition,i=e.bandSize,a=e.xAxis,o=e.yAxis,u=e.xAxisTicks,c=e.yAxisTicks,s=e.stackedData,f=e.dataStartIndex,l=e.displayedData,h=e.offset,d=Bk(n,r);if(!d)return null;var p=t.layout,v=r.props,y=v.dataKey,b=v.children,x=v.minPointSize,w=p==="horizontal"?o:a,O=s?w.scale.domain():null,g=Kk({numericAxis:w}),m=qe(b,Zd),S=l.map(function(A,_){var E,$,T,M,k,C;if(s?E=Fk(s[f+_],O):(E=Re(A,y),Array.isArray(E)||(E=[g,E])),p==="horizontal"){var N,I=[o.scale(E[0]),o.scale(E[1])],R=I[0],B=I[1];$=Af({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:A,index:_}),T=(N=B??R)!==null&&N!==void 0?N:void 0,M=d.size;var q=R-B;if(k=Number.isNaN(q)?0:q,C={x:$,y:o.y,width:M,height:o.height},Math.abs(x)>0&&Math.abs(k)<Math.abs(x)){var re=Je(k||x)*(Math.abs(x)-Math.abs(k));T-=re,k+=re}}else{var G=[a.scale(E[0]),a.scale(E[1])],ee=G[0],Te=G[1];if($=ee,T=Af({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:A,index:_}),M=Te-ee,k=d.size,C={x:a.x,y:T,width:a.width,height:k},Math.abs(x)>0&&Math.abs(M)<Math.abs(x)){var we=Je(M||x)*(Math.abs(x)-Math.abs(M));M+=we}}return me(me(me({},A),{},{x:$,y:T,width:M,height:k,value:s?E:E[1],payload:A,background:C},m&&m[_]&&m[_].props),{},{tooltipPayload:[Qp(r,A)],tooltipPosition:{x:$+M/2,y:T+k/2}})});return me({data:S,layout:p},h)});function Dn(e){"@babel/helpers - typeof";return Dn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Dn(e)}function ON(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function ch(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,sv(n.key),n)}}function SN(e,t,r){return t&&ch(e.prototype,t),r&&ch(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function sh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Xe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?sh(Object(r),!0).forEach(function(n){Na(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function Na(e,t,r){return t=sv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function sv(e){var t=AN(e,"string");return Dn(t)==="symbol"?t:String(t)}function AN(e,t){if(Dn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Dn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _N=function(t,r,n,i,a){var o=t.width,u=t.height,c=t.layout,s=t.children,f=Object.keys(r),l={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},h=!!ut(s,Da);return f.reduce(function(d,p){var v=r[p],y=v.orientation,b=v.domain,x=v.padding,w=x===void 0?{}:x,O=v.mirror,g=v.reversed,m="".concat(y).concat(O?"Mirror":""),S,A,_,E,$;if(v.type==="number"&&(v.padding==="gap"||v.padding==="no-gap")){var T=b[1]-b[0],M=1/0,k=v.categoricalDomain.sort();k.forEach(function(Te,we){we>0&&(M=Math.min((Te||0)-(k[we-1]||0),M))});var C=M/T,N=v.layout==="vertical"?n.height:n.width;if(v.padding==="gap"&&(S=C*N/2),v.padding==="no-gap"){var I=cr(t.barCategoryGap,C*N),R=C*N/2;S=R-I-(R-I)/N*I}}i==="xAxis"?A=[n.left+(w.left||0)+(S||0),n.left+n.width-(w.right||0)-(S||0)]:i==="yAxis"?A=c==="horizontal"?[n.top+n.height-(w.bottom||0),n.top+(w.top||0)]:[n.top+(w.top||0)+(S||0),n.top+n.height-(w.bottom||0)-(S||0)]:A=v.range,g&&(A=[A[1],A[0]]);var B=Rk(v,a,h),q=B.scale,re=B.realScaleType;q.domain(b).range(A),Lk(q);var G=Hk(q,Xe(Xe({},v),{},{realScaleType:re}));i==="xAxis"?($=y==="top"&&!O||y==="bottom"&&O,_=n.left,E=l[m]-$*v.height):i==="yAxis"&&($=y==="left"&&!O||y==="right"&&O,_=l[m]-$*v.width,E=n.top);var ee=Xe(Xe(Xe({},v),G),{},{realScaleType:re,x:_,y:E,scale:q,width:i==="xAxis"?n.width:v.width,height:i==="yAxis"?n.height:v.height});return ee.bandSize=Xi(ee,G),!v.hide&&i==="xAxis"?l[m]+=($?-1:1)*ee.height:v.hide||(l[m]+=($?-1:1)*ee.width),Xe(Xe({},d),{},Na({},p,ee))},{})},lv=function(t,r){var n=t.x,i=t.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},PN=function(t){var r=t.x1,n=t.y1,i=t.x2,a=t.y2;return lv({x:r,y:n},{x:i,y:a})},fv=function(){function e(t){ON(this,e),this.scale=t}return SN(e,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new e(r)}}]),e}();Na(fv,"EPS",1e-4);var Mc=function(t){var r=Object.keys(t).reduce(function(n,i){return Xe(Xe({},n),{},Na({},i,fv.create(t[i])))},{});return Xe(Xe({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return KD(i,function(c,s){return r[s].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return uv(i,function(a,o){return r[o].isInRange(a)})}})};function $N(e){return(e%180+180)%180}var TN=function(t){var r=t.width,n=t.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=$N(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)};function Nn(e){"@babel/helpers - typeof";return Nn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nn(e)}function lh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function fh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?lh(Object(r),!0).forEach(function(n){EN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):lh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function EN(e,t,r){return t=jN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jN(e){var t=MN(e,"string");return Nn(t)==="symbol"?t:String(t)}function MN(e,t){if(Nn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Nn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function CN(e,t){return NN(e)||DN(e,t)||IN(e,t)||kN()}function kN(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function IN(e,t){if(e){if(typeof e=="string")return hh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hh(e,t)}}function hh(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function DN(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function NN(e){if(Array.isArray(e))return e}function wu(){return wu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},wu.apply(this,arguments)}var RN=function(t,r){var n;return P.isValidElement(t)?n=P.cloneElement(t,r):Y(t)?n=t(r):n=P.createElement("line",wu({},r,{className:"recharts-reference-line-line"})),n},LN=function(t,r,n,i,a){var o=a.viewBox,u=o.x,c=o.y,s=o.width,f=o.height,l=a.position;if(n){var h=a.y,d=a.yAxis.orientation,p=t.y.apply(h,{position:l});if(it(a,"discard")&&!t.y.isInRange(p))return null;var v=[{x:u+s,y:p},{x:u,y:p}];return d==="left"?v.reverse():v}if(r){var y=a.x,b=a.xAxis.orientation,x=t.x.apply(y,{position:l});if(it(a,"discard")&&!t.x.isInRange(x))return null;var w=[{x,y:c+f},{x,y:c}];return b==="top"?w.reverse():w}if(i){var O=a.segment,g=O.map(function(m){return t.apply(m,{position:l})});return it(a,"discard")&&FD(g,function(m){return!t.isInRange(m)})?null:g}return null};function Cc(e){var t=e.x,r=e.y,n=e.segment,i=e.xAxis,a=e.yAxis,o=e.shape,u=e.className,c=e.alwaysShow,s=e.clipPathId;qt(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=Mc({x:i.scale,y:a.scale}),l=ye(t),h=ye(r),d=n&&n.length===2,p=LN(f,l,h,d,e);if(!p)return null;var v=CN(p,2),y=v[0],b=y.x,x=y.y,w=v[1],O=w.x,g=w.y,m=it(e,"hidden")?"url(#".concat(s,")"):void 0,S=fh(fh({clipPath:m},Z(e,!0)),{},{x1:b,y1:x,x2:O,y2:g});return P.createElement(he,{className:ne("recharts-reference-line",u)},RN(o,S),_e.renderCallByParent(e,PN({x1:b,y1:x,x2:O,y2:g})))}Cc.displayName="ReferenceLine";Cc.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"};function Rn(e){"@babel/helpers - typeof";return Rn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(e)}function Ou(){return Ou=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Ou.apply(this,arguments)}function dh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ph(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dh(Object(r),!0).forEach(function(n){BN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function BN(e,t,r){return t=FN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function FN(e){var t=WN(e,"string");return Rn(t)==="symbol"?t:String(t)}function WN(e,t){if(Rn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Rn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var UN=function(t){var r=t.x,n=t.y,i=t.xAxis,a=t.yAxis,o=Mc({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return it(t,"discard")&&!o.isInRange(u)?null:u};function Zn(e){var t=e.x,r=e.y,n=e.r,i=e.alwaysShow,a=e.clipPathId,o=ye(t),u=ye(r);if(qt(i===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!o||!u)return null;var c=UN(e);if(!c)return null;var s=c.x,f=c.y,l=e.shape,h=e.className,d=it(e,"hidden")?"url(#".concat(a,")"):void 0,p=ph(ph({clipPath:d},Z(e,!0)),{},{cx:s,cy:f});return P.createElement(he,{className:ne("recharts-reference-dot",h)},Zn.renderDot(l,p),_e.renderCallByParent(e,{x:s-n,y:f-n,width:2*n,height:2*n}))}Zn.displayName="ReferenceDot";Zn.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1};Zn.renderDot=function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):Y(e)?r=e(t):r=P.createElement(jc,Ou({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"})),r};function Ln(e){"@babel/helpers - typeof";return Ln=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ln(e)}function Su(){return Su=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Su.apply(this,arguments)}function vh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function yh(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vh(Object(r),!0).forEach(function(n){zN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function zN(e,t,r){return t=qN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function qN(e){var t=GN(e,"string");return Ln(t)==="symbol"?t:String(t)}function GN(e,t){if(Ln(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ln(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var HN=function(t,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,s=a.y2,f=a.xAxis,l=a.yAxis;if(!f||!l)return null;var h=Mc({x:f.scale,y:l.scale}),d={x:t?h.x.apply(o,{position:"start"}):h.x.rangeMin,y:n?h.y.apply(c,{position:"start"}):h.y.rangeMin},p={x:r?h.x.apply(u,{position:"end"}):h.x.rangeMax,y:i?h.y.apply(s,{position:"end"}):h.y.rangeMax};return it(a,"discard")&&(!h.isInRange(d)||!h.isInRange(p))?null:lv(d,p)};function Jn(e){var t=e.x1,r=e.x2,n=e.y1,i=e.y2,a=e.className,o=e.alwaysShow,u=e.clipPathId;qt(o===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var c=ye(t),s=ye(r),f=ye(n),l=ye(i),h=e.shape;if(!c&&!s&&!f&&!l&&!h)return null;var d=HN(c,s,f,l,e);if(!d&&!h)return null;var p=it(e,"hidden")?"url(#".concat(u,")"):void 0;return P.createElement(he,{className:ne("recharts-reference-area",a)},Jn.renderRect(h,yh(yh({clipPath:p},Z(e,!0)),d)),_e.renderCallByParent(e,d))}Jn.displayName="ReferenceArea";Jn.defaultProps={isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1};Jn.renderRect=function(e,t){var r;return P.isValidElement(e)?r=P.cloneElement(e,t):Y(e)?r=e(t):r=P.createElement(Ec,Su({},t,{className:"recharts-reference-area-rect"})),r};function hv(e,t,r){if(t<1)return[];if(t===1&&r===void 0)return e;for(var n=[],i=0;i<e.length;i+=t)if(r===void 0||r(e[i])===!0)n.push(e[i]);else return;return n}function KN(e,t,r){var n={width:e.width+t.width,height:e.height+t.height};return TN(n,r)}function XN(e,t,r){var n=r==="width",i=e.x,a=e.y,o=e.width,u=e.height;return t===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function aa(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function VN(e,t){return hv(e,t+1)}function YN(e,t,r,n,i){for(var a=(n||[]).slice(),o=t.start,u=t.end,c=0,s=1,f=o,l=function(){var p=n==null?void 0:n[c];if(p===void 0)return{v:hv(n,s)};var v=c,y,b=function(){return y===void 0&&(y=r(p,v)),y},x=p.coordinate,w=c===0||aa(e,x,b,f,u);w||(c=0,f=o,s+=1),w&&(f=x+e*(b()/2+i),c+=s)},h;s<=a.length;)if(h=l(),h)return h.v;return[]}function Bn(e){"@babel/helpers - typeof";return Bn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bn(e)}function gh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ae(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?gh(Object(r),!0).forEach(function(n){ZN(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ZN(e,t,r){return t=JN(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function JN(e){var t=QN(e,"string");return Bn(t)==="symbol"?t:String(t)}function QN(e,t){if(Bn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Bn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function eR(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=t.start,c=t.end,s=function(h){var d=a[h],p,v=function(){return p===void 0&&(p=r(d,h)),p};if(h===o-1){var y=e*(d.coordinate+e*v()/2-c);a[h]=d=Ae(Ae({},d),{},{tickCoord:y>0?d.coordinate-y*e:d.coordinate})}else a[h]=d=Ae(Ae({},d),{},{tickCoord:d.coordinate});var b=aa(e,d.tickCoord,v,u,c);b&&(c=d.tickCoord-e*(v()/2+i),a[h]=Ae(Ae({},d),{},{isShow:!0}))},f=o-1;f>=0;f--)s(f);return a}function tR(e,t,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=t.start,s=t.end;if(a){var f=n[u-1],l=r(f,u-1),h=e*(f.coordinate+e*l/2-s);o[u-1]=f=Ae(Ae({},f),{},{tickCoord:h>0?f.coordinate-h*e:f.coordinate});var d=aa(e,f.tickCoord,function(){return l},c,s);d&&(s=f.tickCoord-e*(l/2+i),o[u-1]=Ae(Ae({},f),{},{isShow:!0}))}for(var p=a?u-1:u,v=function(x){var w=o[x],O,g=function(){return O===void 0&&(O=r(w,x)),O};if(x===0){var m=e*(w.coordinate-e*g()/2-c);o[x]=w=Ae(Ae({},w),{},{tickCoord:m<0?w.coordinate-m*e:w.coordinate})}else o[x]=w=Ae(Ae({},w),{},{tickCoord:w.coordinate});var S=aa(e,w.tickCoord,g,c,s);S&&(c=w.tickCoord+e*(g()/2+i),o[x]=Ae(Ae({},w),{},{isShow:!0}))},y=0;y<p;y++)v(y);return o}function Au(e,t,r){var n=e.tick,i=e.ticks,a=e.viewBox,o=e.minTickGap,u=e.orientation,c=e.interval,s=e.tickFormatter,f=e.unit,l=e.angle;if(!i||!i.length||!n)return[];if(L(c)||ft.isSsr)return VN(i,typeof c=="number"&&L(c)?c:0);var h=[],d=u==="top"||u==="bottom"?"width":"height",p=f&&d==="width"?Vr(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},v=function(w,O){var g=Y(s)?s(w.value,O):w.value;return d==="width"?KN(Vr(g,{fontSize:t,letterSpacing:r}),p,l):Vr(g,{fontSize:t,letterSpacing:r})[d]},y=i.length>=2?Je(i[1].coordinate-i[0].coordinate):1,b=XN(a,y,d);return c==="equidistantPreserveStart"?YN(y,b,v,i,o):(c==="preserveStart"||c==="preserveStartEnd"?h=tR(y,b,v,i,o,c==="preserveStartEnd"):h=eR(y,b,v,i,o),h.filter(function(x){return x.isShow}))}var rR=["viewBox"],nR=["viewBox"],iR=["ticks"];function wr(e){"@babel/helpers - typeof";return wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wr(e)}function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},nr.apply(this,arguments)}function mh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Pe(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?mh(Object(r),!0).forEach(function(n){kc(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function ho(e,t){if(e==null)return{};var r=aR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function aR(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function oR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function bh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,dv(n.key),n)}}function uR(e,t,r){return t&&bh(e.prototype,t),r&&bh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function cR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_u(e,t)}function _u(e,t){return _u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_u(e,t)}function sR(e){var t=hR();return function(){var n=oa(e),i;if(t){var a=oa(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return lR(this,i)}}function lR(e,t){if(t&&(wr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fR(e)}function fR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function hR(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function oa(e){return oa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},oa(e)}function kc(e,t,r){return t=dv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function dv(e){var t=dR(e,"string");return wr(t)==="symbol"?t:String(t)}function dR(e,t){if(wr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(wr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Jr=function(e){cR(r,e);var t=sR(r);function r(n){var i;return oR(this,r),i=t.call(this,n),i.state={fontSize:"",letterSpacing:""},i}return uR(r,[{key:"shouldComponentUpdate",value:function(i,a){var o=i.viewBox,u=ho(i,rR),c=this.props,s=c.viewBox,f=ho(c,nR);return!ar(o,s)||!ar(u,f)||!ar(a,this.state)}},{key:"componentDidMount",value:function(){var i=this.layerReference;if(i){var a=i.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];a&&this.setState({fontSize:window.getComputedStyle(a).fontSize,letterSpacing:window.getComputedStyle(a).letterSpacing})}}},{key:"getTickLineCoord",value:function(i){var a=this.props,o=a.x,u=a.y,c=a.width,s=a.height,f=a.orientation,l=a.tickSize,h=a.mirror,d=a.tickMargin,p,v,y,b,x,w,O=h?-1:1,g=i.tickSize||l,m=L(i.tickCoord)?i.tickCoord:i.coordinate;switch(f){case"top":p=v=i.coordinate,b=u+ +!h*s,y=b-O*g,w=y-O*d,x=m;break;case"left":y=b=i.coordinate,v=o+ +!h*c,p=v-O*g,x=p-O*d,w=m;break;case"right":y=b=i.coordinate,v=o+ +h*c,p=v+O*g,x=p+O*d,w=m;break;default:p=v=i.coordinate,b=u+ +h*s,y=b+O*g,w=y+O*d,x=m;break}return{line:{x1:p,y1:y,x2:v,y2:b},tick:{x,y:w}}}},{key:"getTickTextAnchor",value:function(){var i=this.props,a=i.orientation,o=i.mirror,u;switch(a){case"left":u=o?"start":"end";break;case"right":u=o?"end":"start";break;default:u="middle";break}return u}},{key:"getTickVerticalAnchor",value:function(){var i=this.props,a=i.orientation,o=i.mirror,u="end";switch(a){case"left":case"right":u="middle";break;case"top":u=o?"start":"end";break;default:u=o?"end":"start";break}return u}},{key:"renderAxisLine",value:function(){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.orientation,f=i.mirror,l=i.axisLine,h=Pe(Pe(Pe({},Z(this.props)),Z(l)),{},{fill:"none"});if(s==="top"||s==="bottom"){var d=+(s==="top"&&!f||s==="bottom"&&f);h=Pe(Pe({},h),{},{x1:a,y1:o+d*c,x2:a+u,y2:o+d*c})}else{var p=+(s==="left"&&!f||s==="right"&&f);h=Pe(Pe({},h),{},{x1:a+p*u,y1:o,x2:a+p*u,y2:o+c})}return P.createElement("line",nr({},h,{className:ne("recharts-cartesian-axis-line",Ne(l,"className"))}))}},{key:"renderTicks",value:function(i,a,o){var u=this,c=this.props,s=c.tickLine,f=c.stroke,l=c.tick,h=c.tickFormatter,d=c.unit,p=Au(Pe(Pe({},this.props),{},{ticks:i}),a,o),v=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),b=Z(this.props),x=Z(l),w=Pe(Pe({},b),{},{fill:"none"},Z(s)),O=p.map(function(g,m){var S=u.getTickLineCoord(g),A=S.line,_=S.tick,E=Pe(Pe(Pe(Pe({textAnchor:v,verticalAnchor:y},b),{},{stroke:"none",fill:f},x),_),{},{index:m,payload:g,visibleTicksCount:p.length,tickFormatter:h});return P.createElement(he,nr({className:"recharts-cartesian-axis-tick",key:"tick-".concat(g.value,"-").concat(g.coordinate,"-").concat(g.tickCoord)},mi(u.props,g,m)),s&&P.createElement("line",nr({},w,A,{className:ne("recharts-cartesian-axis-tick-line",Ne(s,"className"))})),l&&r.renderTickItem(l,E,"".concat(Y(h)?h(g.value,m):g.value).concat(d||"")))});return P.createElement("g",{className:"recharts-cartesian-axis-ticks"},O)}},{key:"render",value:function(){var i=this,a=this.props,o=a.axisLine,u=a.width,c=a.height,s=a.ticksGenerator,f=a.className,l=a.hide;if(l)return null;var h=this.props,d=h.ticks,p=ho(h,iR),v=d;return Y(s)&&(v=d&&d.length>0?s(this.props):s(p)),u<=0||c<=0||!v||!v.length?null:P.createElement(he,{className:ne("recharts-cartesian-axis",f),ref:function(b){i.layerReference=b}},o&&this.renderAxisLine(),this.renderTicks(v,this.state.fontSize,this.state.letterSpacing),_e.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(i,a,o){var u;return P.isValidElement(i)?u=P.cloneElement(i,a):Y(i)?u=i(a):u=P.createElement(Di,nr({},a,{className:"recharts-cartesian-axis-tick-value"}),o),u}}]),r}(U.Component);kc(Jr,"displayName","CartesianAxis");kc(Jr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var pR=["x1","y1","x2","y2","key"],vR=["offset"];function Or(e){"@babel/helpers - typeof";return Or=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Or(e)}function Pu(){return Pu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Pu.apply(this,arguments)}function xh(e,t){if(e==null)return{};var r=yR(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function yR(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function wh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function Ot(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wh(Object(r),!0).forEach(function(n){Ic(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function gR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Oh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,pv(n.key),n)}}function mR(e,t,r){return t&&Oh(e.prototype,t),r&&Oh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function bR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&$u(e,t)}function $u(e,t){return $u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$u(e,t)}function xR(e){var t=SR();return function(){var n=ua(e),i;if(t){var a=ua(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return wR(this,i)}}function wR(e,t){if(t&&(Or(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return OR(e)}function OR(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function SR(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ua(e){return ua=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ua(e)}function Ic(e,t,r){return t=pv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pv(e){var t=AR(e,"string");return Or(t)==="symbol"?t:String(t)}function AR(e,t){if(Or(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Or(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var vv=function(e){bR(r,e);var t=xR(r);function r(){return gR(this,r),t.apply(this,arguments)}return mR(r,[{key:"renderHorizontal",value:function(i){var a=this,o=this.props,u=o.x,c=o.width,s=o.horizontal;if(!i||!i.length)return null;var f=i.map(function(l,h){var d=Ot(Ot({},a.props),{},{x1:u,y1:l,x2:u+c,y2:l,key:"line-".concat(h),index:h});return r.renderLineItem(s,d)});return P.createElement("g",{className:"recharts-cartesian-grid-horizontal"},f)}},{key:"renderVertical",value:function(i){var a=this,o=this.props,u=o.y,c=o.height,s=o.vertical;if(!i||!i.length)return null;var f=i.map(function(l,h){var d=Ot(Ot({},a.props),{},{x1:l,y1:u,x2:l,y2:u+c,key:"line-".concat(h),index:h});return r.renderLineItem(s,d)});return P.createElement("g",{className:"recharts-cartesian-grid-vertical"},f)}},{key:"renderVerticalStripes",value:function(i){var a=this.props.verticalFill;if(!a||!a.length)return null;var o=this.props,u=o.fillOpacity,c=o.x,s=o.y,f=o.width,l=o.height,h=i.map(function(p){return Math.round(p+c-c)}).sort(function(p,v){return p-v});c!==h[0]&&h.unshift(0);var d=h.map(function(p,v){var y=!h[v+1],b=y?c+f-p:h[v+1]-p;if(b<=0)return null;var x=v%a.length;return P.createElement("rect",{key:"react-".concat(v),x:p,y:s,width:b,height:l,stroke:"none",fill:a[x],fillOpacity:u,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},d)}},{key:"renderHorizontalStripes",value:function(i){var a=this.props.horizontalFill;if(!a||!a.length)return null;var o=this.props,u=o.fillOpacity,c=o.x,s=o.y,f=o.width,l=o.height,h=i.map(function(p){return Math.round(p+s-s)}).sort(function(p,v){return p-v});s!==h[0]&&h.unshift(0);var d=h.map(function(p,v){var y=!h[v+1],b=y?s+l-p:h[v+1]-p;if(b<=0)return null;var x=v%a.length;return P.createElement("rect",{key:"react-".concat(v),y:p,x:c,height:b,width:f,stroke:"none",fill:a[x],fillOpacity:u,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},d)}},{key:"renderBackground",value:function(){var i=this.props.fill;if(!i||i==="none")return null;var a=this.props,o=a.fillOpacity,u=a.x,c=a.y,s=a.width,f=a.height;return P.createElement("rect",{x:u,y:c,width:s,height:f,stroke:"none",fill:i,fillOpacity:o,className:"recharts-cartesian-grid-bg"})}},{key:"render",value:function(){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,s=i.horizontal,f=i.vertical,l=i.horizontalCoordinatesGenerator,h=i.verticalCoordinatesGenerator,d=i.xAxis,p=i.yAxis,v=i.offset,y=i.chartWidth,b=i.chartHeight,x=i.syncWithTicks,w=i.horizontalValues,O=i.verticalValues;if(!L(u)||u<=0||!L(c)||c<=0||!L(a)||a!==+a||!L(o)||o!==+o)return null;var g=this.props,m=g.horizontalPoints,S=g.verticalPoints;if((!m||!m.length)&&Y(l)){var A=w&&w.length;m=l({yAxis:p?Ot(Ot({},p),{},{ticks:A?w:p.ticks}):void 0,width:y,height:b,offset:v},A?!0:x)}if((!S||!S.length)&&Y(h)){var _=O&&O.length;S=h({xAxis:d?Ot(Ot({},d),{},{ticks:_?O:d.ticks}):void 0,width:y,height:b,offset:v},_?!0:x)}return P.createElement("g",{className:"recharts-cartesian-grid"},this.renderBackground(),s&&this.renderHorizontal(m),f&&this.renderVertical(S),s&&this.renderHorizontalStripes(m),f&&this.renderVerticalStripes(S))}}],[{key:"renderLineItem",value:function(i,a){var o;if(P.isValidElement(i))o=P.cloneElement(i,a);else if(Y(i))o=i(a);else{var u=a.x1,c=a.y1,s=a.x2,f=a.y2,l=a.key,h=xh(a,pR),d=Z(h);d.offset;var p=xh(d,vR);o=P.createElement("line",Pu({},p,{x1:u,y1:c,x2:s,y2:f,fill:"none",key:l}))}return o}}]),r}(U.PureComponent);Ic(vv,"displayName","CartesianGrid");Ic(vv,"defaultProps",{horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]});var _R=["type","layout","connectNulls","ref"];function Sr(e){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Sr(e)}function PR(e,t){if(e==null)return{};var r=$R(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function $R(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function Qr(){return Qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qr.apply(this,arguments)}function Sh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function ke(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Sh(Object(r),!0).forEach(function(n){Ve(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function er(e){return MR(e)||jR(e)||ER(e)||TR()}function TR(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ER(e,t){if(e){if(typeof e=="string")return Tu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Tu(e,t)}}function jR(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function MR(e){if(Array.isArray(e))return Tu(e)}function Tu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function CR(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ah(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,yv(n.key),n)}}function kR(e,t,r){return t&&Ah(e.prototype,t),r&&Ah(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function IR(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Eu(e,t)}function Eu(e,t){return Eu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Eu(e,t)}function DR(e){var t=RR();return function(){var n=ca(e),i;if(t){var a=ca(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return NR(this,i)}}function NR(e,t){if(t&&(Sr(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _t(e)}function _t(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function RR(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function ca(e){return ca=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ca(e)}function Ve(e,t,r){return t=yv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yv(e){var t=LR(e,"string");return Sr(t)==="symbol"?t:String(t)}function LR(e,t){if(Sr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Sr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ra=function(e){IR(r,e);var t=DR(r);function r(){var n;CR(this,r);for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return n=t.call.apply(t,[this].concat(a)),Ve(_t(n),"state",{isAnimationFinished:!0,totalLength:0}),Ve(_t(n),"generateSimpleStrokeDasharray",function(u,c){return"".concat(c,"px ").concat(u-c,"px")}),Ve(_t(n),"getStrokeDasharray",function(u,c,s){var f=s.reduce(function(x,w){return x+w});if(!f)return n.generateSimpleStrokeDasharray(c,u);for(var l=Math.floor(u/f),h=u%f,d=c-u,p=[],v=0,y=0;v<s.length;y+=s[v],++v)if(y+s[v]>h){p=[].concat(er(s.slice(0,v)),[h-y]);break}var b=p.length%2===0?[0,d]:[d];return[].concat(er(r.repeat(s,l)),er(p),b).map(function(x){return"".concat(x,"px")}).join(", ")}),Ve(_t(n),"id",qn("recharts-line-")),Ve(_t(n),"pathRef",function(u){n.mainCurve=u}),Ve(_t(n),"handleAnimationEnd",function(){n.setState({isAnimationFinished:!0}),n.props.onAnimationEnd&&n.props.onAnimationEnd()}),Ve(_t(n),"handleAnimationStart",function(){n.setState({isAnimationFinished:!1}),n.props.onAnimationStart&&n.props.onAnimationStart()}),n}return kR(r,[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var i=this.getTotalLength();this.setState({totalLength:i})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var i=this.getTotalLength();i!==this.state.totalLength&&this.setState({totalLength:i})}}},{key:"getTotalLength",value:function(){var i=this.mainCurve;try{return i&&i.getTotalLength&&i.getTotalLength()||0}catch{return 0}}},{key:"renderErrorBar",value:function(i,a){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,u=o.points,c=o.xAxis,s=o.yAxis,f=o.layout,l=o.children,h=qe(l,Yn);if(!h)return null;var d=function(y,b){return{x:y.x,y:y.y,value:y.value,errorVal:Re(y.payload,b)}},p={clipPath:i?"url(#clipPath-".concat(a,")"):null};return P.createElement(he,p,h.map(function(v){return P.cloneElement(v,{key:"bar-".concat(v.props.dataKey),data:u,xAxis:c,yAxis:s,layout:f,dataPointFormatter:d})}))}},{key:"renderDots",value:function(i,a,o){var u=this.props.isAnimationActive;if(u&&!this.state.isAnimationFinished)return null;var c=this.props,s=c.dot,f=c.points,l=c.dataKey,h=Z(this.props),d=Z(s,!0),p=f.map(function(y,b){var x=ke(ke(ke({key:"dot-".concat(b),r:3},h),d),{},{value:y.value,dataKey:l,cx:y.x,cy:y.y,index:b,payload:y.payload});return r.renderDotItem(s,x)}),v={clipPath:i?"url(#clipPath-".concat(a?"":"dots-").concat(o,")"):null};return P.createElement(he,Qr({className:"recharts-line-dots",key:"dots"},v),p)}},{key:"renderCurveStatically",value:function(i,a,o,u){var c=this.props,s=c.type,f=c.layout,l=c.connectNulls;c.ref;var h=PR(c,_R),d=ke(ke(ke({},Z(h,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:a?"url(#clipPath-".concat(o,")"):null,points:i},u),{},{type:s,layout:f,connectNulls:l});return P.createElement(vu,Qr({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(i,a){var o=this,u=this.props,c=u.points,s=u.strokeDasharray,f=u.isAnimationActive,l=u.animationBegin,h=u.animationDuration,d=u.animationEasing,p=u.animationId,v=u.animateNewValues,y=u.width,b=u.height,x=this.state,w=x.prevPoints,O=x.totalLength;return P.createElement(dt,{begin:l,duration:h,isActive:f,easing:d,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(g){var m=g.t;if(w){var S=w.length/c.length,A=c.map(function(M,k){var C=Math.floor(k*S);if(w[C]){var N=w[C],I=Ye(N.x,M.x),R=Ye(N.y,M.y);return ke(ke({},M),{},{x:I(m),y:R(m)})}if(v){var B=Ye(y*2,M.x),q=Ye(b/2,M.y);return ke(ke({},M),{},{x:B(m),y:q(m)})}return ke(ke({},M),{},{x:M.x,y:M.y})});return o.renderCurveStatically(A,i,a)}var _=Ye(0,O),E=_(m),$;if(s){var T="".concat(s).split(/[,\s]+/gim).map(function(M){return parseFloat(M)});$=o.getStrokeDasharray(E,O,T)}else $=o.generateSimpleStrokeDasharray(O,E);return o.renderCurveStatically(c,i,a,{strokeDasharray:$})})}},{key:"renderCurve",value:function(i,a){var o=this.props,u=o.points,c=o.isAnimationActive,s=this.state,f=s.prevPoints,l=s.totalLength;return c&&u&&u.length&&(!f&&l>0||!ja(f,u))?this.renderCurveWithAnimation(i,a):this.renderCurveStatically(u,i,a)}},{key:"render",value:function(){var i,a=this.props,o=a.hide,u=a.dot,c=a.points,s=a.className,f=a.xAxis,l=a.yAxis,h=a.top,d=a.left,p=a.width,v=a.height,y=a.isAnimationActive,b=a.id;if(o||!c||!c.length)return null;var x=this.state.isAnimationFinished,w=c.length===1,O=ne("recharts-line",s),g=f&&f.allowDataOverflow,m=l&&l.allowDataOverflow,S=g||m,A=J(b)?this.id:b,_=(i=Z(u))!==null&&i!==void 0?i:{r:3,strokeWidth:2},E=_.r,$=E===void 0?3:E,T=_.strokeWidth,M=T===void 0?2:T,k=ob(u)?u:{},C=k.clipDot,N=C===void 0?!0:C,I=$*2+M;return P.createElement(he,{className:O},g||m?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(A)},P.createElement("rect",{x:g?d:d-p/2,y:m?h:h-v/2,width:g?p:p*2,height:m?v:v*2})),!N&&P.createElement("clipPath",{id:"clipPath-dots-".concat(A)},P.createElement("rect",{x:d-I/2,y:h-I/2,width:p+I,height:v+I}))):null,!w&&this.renderCurve(S,A),this.renderErrorBar(S,A),(w||u)&&this.renderDots(S,N,A),(!y||x)&&Et.renderCallByParent(this.props,c))}}],[{key:"getDerivedStateFromProps",value:function(i,a){return i.animationId!==a.prevAnimationId?{prevAnimationId:i.animationId,curPoints:i.points,prevPoints:a.curPoints}:i.points!==a.curPoints?{curPoints:i.points}:null}},{key:"repeat",value:function(i,a){for(var o=i.length%2!==0?[].concat(er(i),[0]):i,u=[],c=0;c<a;++c)u=[].concat(er(u),er(o));return u}},{key:"renderDotItem",value:function(i,a){var o;if(P.isValidElement(i))o=P.cloneElement(i,a);else if(Y(i))o=i(a);else{var u=ne("recharts-line-dot",i?i.className:"");o=P.createElement(jc,Qr({},a,{className:u}))}return o}}]),r}(U.PureComponent);Ve(Ra,"displayName","Line");Ve(Ra,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!ft.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1});Ve(Ra,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,o=e.dataKey,u=e.bandSize,c=e.displayedData,s=e.offset,f=t.layout,l=c.map(function(h,d){var p=Re(h,o);return f==="horizontal"?{x:Sf({axis:r,ticks:i,bandSize:u,entry:h,index:d}),y:J(p)?null:n.scale(p),value:p,payload:h}:{x:J(p)?null:r.scale(p),y:Sf({axis:n,ticks:a,bandSize:u,entry:h,index:d}),value:p,payload:h}});return ke({points:l,layout:f},s)});var Dc=function(){return null};Dc.displayName="XAxis";Dc.defaultProps={allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0};var Nc=function(){return null};Nc.displayName="YAxis";Nc.defaultProps={allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1};var BR=jt,FR=Gn,WR=ba;function UR(e){return function(t,r,n){var i=Object(t);if(!FR(t)){var a=BR(r);t=WR(t),r=function(u){return a(i[u],u,i)}}var o=e(t,r,n);return o>-1?i[a?t[o]:o]:void 0}}var zR=UR,qR=iv;function GR(e){var t=qR(e),r=t%1;return t===t?r?t-r:t:0}var HR=GR,KR=jd,XR=jt,VR=HR,YR=Math.max;function ZR(e,t,r){var n=e==null?0:e.length;if(!n)return-1;var i=r==null?0:VR(r);return i<0&&(i=YR(n+i,0)),KR(e,XR(t),i)}var JR=ZR,QR=zR,eL=JR,tL=QR(eL),rL=tL,nL="Invariant failed";function iL(e,t){if(!e)throw new Error(nL)}function gv(e){var t=e.cx,r=e.cy,n=e.radius,i=e.startAngle,a=e.endAngle,o=Se(t,r,n,i),u=Se(t,r,n,a);return{points:[o,u],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}function _h(e){return cL(e)||uL(e)||oL(e)||aL()}function aL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function oL(e,t){if(e){if(typeof e=="string")return ju(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ju(e,t)}}function uL(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function cL(e){if(Array.isArray(e))return ju(e)}function ju(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Mu=function(t,r,n,i,a){var o=qe(t,Cc),u=qe(t,Zn),c=[].concat(_h(o),_h(u)),s=qe(t,Jn),f="".concat(i,"Id"),l=i[0],h=r;if(c.length&&(h=c.reduce(function(v,y){if(y.props[f]===n&&it(y.props,"extendDomain")&&L(y.props[l])){var b=y.props[l];return[Math.min(v[0],b),Math.max(v[1],b)]}return v},h)),s.length){var d="".concat(l,"1"),p="".concat(l,"2");h=s.reduce(function(v,y){if(y.props[f]===n&&it(y.props,"extendDomain")&&L(y.props[d])&&L(y.props[p])){var b=y.props[d],x=y.props[p];return[Math.min(v[0],b,x),Math.max(v[1],b,x)]}return v},h)}return a&&a.length&&(h=a.reduce(function(v,y){return L(y)?[Math.min(v[0],y),Math.max(v[1],y)]:v},h)),h},Cu={},sL={get exports(){return Cu},set exports(e){Cu=e}};(function(e){var t=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,s,f){this.fn=c,this.context=s,this.once=f||!1}function a(c,s,f,l,h){if(typeof f!="function")throw new TypeError("The listener must be a function");var d=new i(f,l||c,h),p=r?r+s:s;return c._events[p]?c._events[p].fn?c._events[p]=[c._events[p],d]:c._events[p].push(d):(c._events[p]=d,c._eventsCount++),c}function o(c,s){--c._eventsCount===0?c._events=new n:delete c._events[s]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var s=[],f,l;if(this._eventsCount===0)return s;for(l in f=this._events)t.call(f,l)&&s.push(r?l.slice(1):l);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(f)):s},u.prototype.listeners=function(s){var f=r?r+s:s,l=this._events[f];if(!l)return[];if(l.fn)return[l.fn];for(var h=0,d=l.length,p=new Array(d);h<d;h++)p[h]=l[h].fn;return p},u.prototype.listenerCount=function(s){var f=r?r+s:s,l=this._events[f];return l?l.fn?1:l.length:0},u.prototype.emit=function(s,f,l,h,d,p){var v=r?r+s:s;if(!this._events[v])return!1;var y=this._events[v],b=arguments.length,x,w;if(y.fn){switch(y.once&&this.removeListener(s,y.fn,void 0,!0),b){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,f),!0;case 3:return y.fn.call(y.context,f,l),!0;case 4:return y.fn.call(y.context,f,l,h),!0;case 5:return y.fn.call(y.context,f,l,h,d),!0;case 6:return y.fn.call(y.context,f,l,h,d,p),!0}for(w=1,x=new Array(b-1);w<b;w++)x[w-1]=arguments[w];y.fn.apply(y.context,x)}else{var O=y.length,g;for(w=0;w<O;w++)switch(y[w].once&&this.removeListener(s,y[w].fn,void 0,!0),b){case 1:y[w].fn.call(y[w].context);break;case 2:y[w].fn.call(y[w].context,f);break;case 3:y[w].fn.call(y[w].context,f,l);break;case 4:y[w].fn.call(y[w].context,f,l,h);break;default:if(!x)for(g=1,x=new Array(b-1);g<b;g++)x[g-1]=arguments[g];y[w].fn.apply(y[w].context,x)}}return!0},u.prototype.on=function(s,f,l){return a(this,s,f,l,!1)},u.prototype.once=function(s,f,l){return a(this,s,f,l,!0)},u.prototype.removeListener=function(s,f,l,h){var d=r?r+s:s;if(!this._events[d])return this;if(!f)return o(this,d),this;var p=this._events[d];if(p.fn)p.fn===f&&(!h||p.once)&&(!l||p.context===l)&&o(this,d);else{for(var v=0,y=[],b=p.length;v<b;v++)(p[v].fn!==f||h&&!p[v].once||l&&p[v].context!==l)&&y.push(p[v]);y.length?this._events[d]=y.length===1?y[0]:y:o(this,d)}return this},u.prototype.removeAllListeners=function(s){var f;return s?(f=r?r+s:s,this._events[f]&&o(this,f)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,e.exports=u})(sL);const lL=Cu;var po=new lL,vo="recharts.syncMouseEvents";function Fn(e){"@babel/helpers - typeof";return Fn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fn(e)}function fL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ph(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,mv(n.key),n)}}function hL(e,t,r){return t&&Ph(e.prototype,t),r&&Ph(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function yo(e,t,r){return t=mv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mv(e){var t=dL(e,"string");return Fn(t)==="symbol"?t:String(t)}function dL(e,t){if(Fn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Fn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var pL=function(){function e(){fL(this,e),yo(this,"activeIndex",0),yo(this,"coordinateList",[]),yo(this,"layout","horizontal")}return hL(e,[{key:"setDetails",value:function(r){var n=r.coordinateList,i=n===void 0?[]:n,a=r.container,o=a===void 0?null:a,u=r.layout,c=u===void 0?null:u,s=r.offset,f=s===void 0?null:s,l=r.mouseHandlerCallback,h=l===void 0?null:l;this.coordinateList=i??this.coordinateList,this.container=o??this.container,this.layout=c??this.layout,this.offset=f??this.offset,this.mouseHandlerCallback=h??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,s=((r=window)===null||r===void 0?void 0:r.scrollX)||0,f=((n=window)===null||n===void 0?void 0:n.scrollY)||0,l=a+c+s,h=o+this.offset.top+u/2+f;this.mouseHandlerCallback({pageX:l,pageY:h})}}}]),e}();function vL(e,t,r){if(r==="number"&&t===!0&&Array.isArray(e)){var n=e==null?void 0:e[0],i=e==null?void 0:e[1];if(n&&i&&L(n)&&L(i))return!0}return!1}function yL(e,t,r){var n,i,a,o;if(e==="horizontal")n=t.x,a=n,i=r.top,o=r.top+r.height;else if(e==="vertical")i=t.y,o=i,n=r.left,a=r.left+r.width;else if(t.cx!=null&&t.cy!=null)if(e==="centric"){var u=t.cx,c=t.cy,s=t.innerRadius,f=t.outerRadius,l=t.angle,h=Se(u,c,s,l),d=Se(u,c,f,l);n=h.x,i=h.y,a=d.x,o=d.y}else return gv(t);return[{x:n,y:i},{x:a,y:o}]}function gL(e,t,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:e==="horizontal"?t.x-i:r.left+.5,y:e==="horizontal"?r.top+.5:t.y-i,width:e==="horizontal"?n:r.width-1,height:e==="horizontal"?r.height-1:n}}var mL=["item"],bL=["children","className","width","height","style","compact","title","desc"];function Ar(e){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ar(e)}function $h(e,t){return OL(e)||wL(e,t)||bv(e,t)||xL()}function xL(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wL(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,s=!1;try{if(a=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==t);c=!0);}catch(f){s=!0,i=f}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(s)throw i}}return u}}function OL(e){if(Array.isArray(e))return e}function ir(){return ir=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ir.apply(this,arguments)}function Th(e,t){if(e==null)return{};var r=SL(e,t),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function SL(e,t){if(e==null)return{};var r={},n=Object.keys(e),i,a;for(a=0;a<n.length;a++)i=n[a],!(t.indexOf(i)>=0)&&(r[i]=e[i]);return r}function AL(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Eh(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xv(n.key),n)}}function _L(e,t,r){return t&&Eh(e.prototype,t),r&&Eh(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function PL(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ku(e,t)}function ku(e,t){return ku=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ku(e,t)}function $L(e){var t=EL();return function(){var n=sa(e),i;if(t){var a=sa(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return TL(this,i)}}function TL(e,t){if(t&&(Ar(t)==="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V(e)}function V(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function EL(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function sa(e){return sa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},sa(e)}function _r(e){return CL(e)||ML(e)||bv(e)||jL()}function jL(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bv(e,t){if(e){if(typeof e=="string")return Iu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Iu(e,t)}}function ML(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function CL(e){if(Array.isArray(e))return Iu(e)}function Iu(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function jh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,n)}return r}function j(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jh(Object(r),!0).forEach(function(n){z(e,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jh(Object(r)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(r,n))})}return e}function z(e,t,r){return t=xv(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xv(e){var t=kL(e,"string");return Ar(t)==="symbol"?t:String(t)}function kL(e,t){if(Ar(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var n=r.call(e,t||"default");if(Ar(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var IL={xAxis:["bottom","top"],yAxis:["left","right"]},DL={width:"100%",height:"100%"},wv={x:0,y:0},NL=function(t,r){return r==="horizontal"?t.x:r==="vertical"?t.y:r==="centric"?t.angle:t.radius},RL=function(t,r,n,i){var a=r.find(function(f){return f&&f.index===n});if(a){if(t==="horizontal")return{x:a.coordinate,y:i.y};if(t==="vertical")return{x:i.x,y:a.coordinate};if(t==="centric"){var o=a.coordinate,u=i.radius;return j(j(j({},i),Se(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,s=i.angle;return j(j(j({},i),Se(i.cx,i.cy,c,s)),{},{angle:s,radius:c})}return wv},La=function(t,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var s=c.props.data;return s&&s.length?[].concat(_r(u),_r(s)):u},[]);return o.length>0?o:t&&t.length&&L(i)&&L(a)?t.slice(i,a+1):[]};function Ov(e){return e==="number"?[0,"auto"]:void 0}var Sv=function(t,r,n,i){var a=t.graphicalItems,o=t.tooltipAxis,u=La(r,t);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,s){var f,l=s.props.hide;if(l)return c;var h=(f=s.props.data)!==null&&f!==void 0?f:r;h&&t.dataStartIndex+t.dataEndIndex!==0&&(h=h.slice(t.dataStartIndex,t.dataEndIndex+1));var d;if(o.dataKey&&!o.allowDuplicatedCategory){var p=h===void 0?u:h;d=yi(p,o.dataKey,i)}else d=h&&h[n]||u[n];return d?[].concat(_r(c),[Qp(s,d)]):c},[])},Mh=function(t,r,n,i){var a=i||{x:t.chartX,y:t.chartY},o=NL(a,n),u=t.orderedTooltipTicks,c=t.tooltipAxis,s=t.tooltipTicks,f=Mk(o,u,s,c);if(f>=0&&s){var l=s[f]&&s[f].value,h=Sv(t,r,f,l),d=RL(n,u,f,a);return{activeTooltipIndex:f,activeLabel:l,activePayload:h,activeCoordinate:d}}return null},LL=function(t,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=t.stackOffset,d=Zp(f,a);return n.reduce(function(p,v){var y,b=v.props,x=b.type,w=b.dataKey,O=b.allowDataOverflow,g=b.allowDuplicatedCategory,m=b.scale,S=b.ticks,A=b.includeHidden,_=v.props[o];if(p[_])return p;var E=La(t.data,{graphicalItems:i.filter(function(G){return G.props[o]===_}),dataStartIndex:c,dataEndIndex:s}),$=E.length,T,M,k;vL(v.props.domain,O,x)&&(T=lu(v.props.domain,null,O),d&&(x==="number"||m!=="auto")&&(k=Zr(E,w,"category")));var C=Ov(x);if(!T||T.length===0){var N,I=(N=v.props.domain)!==null&&N!==void 0?N:C;if(w){if(T=Zr(E,w,x),x==="category"&&d){var R=Zm(T);g&&R?(M=T,T=ea(0,$)):g||(T=$f(I,T,v).reduce(function(G,ee){return G.indexOf(ee)>=0?G:[].concat(_r(G),[ee])},[]))}else if(x==="category")g?T=T.filter(function(G){return G!==""&&!J(G)}):T=$f(I,T,v).reduce(function(G,ee){return G.indexOf(ee)>=0||ee===""||J(ee)?G:[].concat(_r(G),[ee])},[]);else if(x==="number"){var B=Nk(E,i.filter(function(G){return G.props[o]===_&&(A||!G.props.hide)}),w,a,f);B&&(T=B)}d&&(x==="number"||m!=="auto")&&(k=Zr(E,w,"category"))}else d?T=ea(0,$):u&&u[_]&&u[_].hasStack&&x==="number"?T=h==="expand"?[0,1]:Jp(u[_].stackGroups,c,s):T=Yp(E,i.filter(function(G){return G.props[o]===_&&(A||!G.props.hide)}),x,f,!0);if(x==="number")T=Mu(l,T,_,a,S),I&&(T=lu(I,T,O));else if(x==="category"&&I){var q=I,re=T.every(function(G){return q.indexOf(G)>=0});re&&(T=q)}}return j(j({},p),{},z({},_,j(j({},v.props),{},{axisType:a,domain:T,categoricalDomain:k,duplicateDomain:M,originalDomain:(y=v.props.domain)!==null&&y!==void 0?y:C,isCategorical:d,layout:f})))},{})},BL=function(t,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.layout,l=t.children,h=La(t.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:s}),d=h.length,p=Zp(f,a),v=-1;return n.reduce(function(y,b){var x=b.props[o],w=Ov("number");if(!y[x]){v++;var O;return p?O=ea(0,d):u&&u[x]&&u[x].hasStack?(O=Jp(u[x].stackGroups,c,s),O=Mu(l,O,x,a)):(O=lu(w,Yp(h,n.filter(function(g){return g.props[o]===x&&!g.props.hide}),"number",f),i.defaultProps.allowDataOverflow),O=Mu(l,O,x,a)),j(j({},y),{},z({},x,j(j({axisType:a},i.defaultProps),{},{hide:!0,orientation:Ne(IL,"".concat(a,".").concat(v%2),null),domain:O,originalDomain:w,isCategorical:p,layout:f})))}return y},{})},FL=function(t,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,s=r.dataEndIndex,f=t.children,l="".concat(i,"Id"),h=qe(f,a),d={};return h&&h.length?d=LL(t,{axes:h,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s}):o&&o.length&&(d=BL(t,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:l,stackGroups:u,dataStartIndex:c,dataEndIndex:s})),d},WL=function(t){var r=St(t),n=At(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:tc(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:Xi(r,n)}},Ch=function(t){var r=t.children,n=t.defaultShowTooltip,i=ut(r,kn),a=0,o=0;return t.data&&t.data.length!==0&&(o=t.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},UL=function(t){return!t||!t.length?!1:t.some(function(r){var n=lt(r&&r.type);return n&&n.indexOf("Bar")>=0})},kh=function(t){return t==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:t==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:t==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},zL=function(t,r){var n=t.props,i=t.graphicalItems,a=t.xAxisMap,o=a===void 0?{}:a,u=t.yAxisMap,c=u===void 0?{}:u,s=n.width,f=n.height,l=n.children,h=n.margin||{},d=ut(l,kn),p=ut(l,nn),v=Object.keys(c).reduce(function(g,m){var S=c[m],A=S.orientation;return!S.mirror&&!S.hide?j(j({},g),{},z({},A,g[A]+S.width)):g},{left:h.left||0,right:h.right||0}),y=Object.keys(o).reduce(function(g,m){var S=o[m],A=S.orientation;return!S.mirror&&!S.hide?j(j({},g),{},z({},A,Ne(g,"".concat(A))+S.height)):g},{top:h.top||0,bottom:h.bottom||0}),b=j(j({},y),v),x=b.bottom;d&&(b.bottom+=d.props.height||kn.defaultProps.height),p&&r&&(b=Ik(b,i,n,r));var w=s-b.left-b.right,O=f-b.top-b.bottom;return j(j({brushBottom:x},b),{},{width:Math.max(w,0),height:Math.max(O,0)})},qL=function(t){var r,n=t.chartName,i=t.GraphicalChild,a=t.defaultTooltipEventType,o=a===void 0?"axis":a,u=t.validateTooltipEventTypes,c=u===void 0?["axis"]:u,s=t.axisComponents,f=t.legendContent,l=t.formatAxisMap,h=t.defaultProps,d=function(y,b){var x=b.graphicalItems,w=b.stackGroups,O=b.offset,g=b.updateId,m=b.dataStartIndex,S=b.dataEndIndex,A=y.barSize,_=y.layout,E=y.barGap,$=y.barCategoryGap,T=y.maxBarSize,M=kh(_),k=M.numericAxisName,C=M.cateAxisName,N=UL(x),I=N&&Ck({barSize:A,stackGroups:w}),R=[];return x.forEach(function(B,q){var re=La(y.data,{graphicalItems:[B],dataStartIndex:m,dataEndIndex:S}),G=B.props,ee=G.dataKey,Te=G.maxBarSize,we=B.props["".concat(k,"Id")],xt=B.props["".concat(C,"Id")],wt={},Me=s.reduce(function(Dt,Nt){var ei,Ba=b["".concat(Nt.axisType,"Map")],Rc=B.props["".concat(Nt.axisType,"Id")];Ba&&Ba[Rc]||Nt.axisType==="zAxis"||iL(!1);var Lc=Ba[Rc];return j(j({},Dt),{},(ei={},z(ei,Nt.axisType,Lc),z(ei,"".concat(Nt.axisType,"Ticks"),At(Lc)),ei))},wt),F=Me[C],K=Me["".concat(C,"Ticks")],X=w&&w[we]&&w[we].hasStack&&Xk(B,w[we].stackGroups),D=lt(B.type).indexOf("Bar")>=0,le=Xi(F,K),Q=[];if(D){var fe,pe,Ce=J(Te)?T:Te,et=(fe=(pe=Xi(F,K,!0))!==null&&pe!==void 0?pe:Ce)!==null&&fe!==void 0?fe:0;Q=kk({barGap:E,barCategoryGap:$,bandSize:et!==le?et:le,sizeList:I[xt],maxBarSize:Ce}),et!==le&&(Q=Q.map(function(Dt){return j(j({},Dt),{},{position:j(j({},Dt.position),{},{offset:Dt.position.offset-et/2})})}))}var Qn=B&&B.type&&B.type.getComposedData;if(Qn){var It;R.push({props:j(j({},Qn(j(j({},Me),{},{displayedData:re,props:y,dataKey:ee,item:B,bandSize:le,barPosition:Q,offset:O,stackedData:X,layout:_,dataStartIndex:m,dataEndIndex:S}))),{},(It={key:B.key||"item-".concat(q)},z(It,k,Me[k]),z(It,C,Me[C]),z(It,"animationId",g),It)),childIndex:sb(B,y.children),item:B})}}),R},p=function(y,b){var x=y.props,w=y.dataStartIndex,O=y.dataEndIndex,g=y.updateId;if(!Jc({props:x}))return null;var m=x.children,S=x.layout,A=x.stackOffset,_=x.data,E=x.reverseStackOrder,$=kh(S),T=$.numericAxisName,M=$.cateAxisName,k=qe(m,i),C=Gk(_,k,"".concat(T,"Id"),"".concat(M,"Id"),A,E),N=s.reduce(function(re,G){var ee="".concat(G.axisType,"Map");return j(j({},re),{},z({},ee,FL(x,j(j({},G),{},{graphicalItems:k,stackGroups:G.axisType===T&&C,dataStartIndex:w,dataEndIndex:O}))))},{}),I=zL(j(j({},N),{},{props:x,graphicalItems:k}),b==null?void 0:b.legendBBox);Object.keys(N).forEach(function(re){N[re]=l(x,N[re],I,re.replace("Map",""),n)});var R=N["".concat(M,"Map")],B=WL(R),q=d(x,j(j({},N),{},{dataStartIndex:w,dataEndIndex:O,updateId:g,graphicalItems:k,stackGroups:C,offset:I}));return j(j({formattedGraphicalItems:q,graphicalItems:k,offset:I,stackGroups:C},B),N)};return r=function(v){PL(b,v);var y=$L(b);function b(x){var w,O,g;return AL(this,b),g=y.call(this,x),z(V(g),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),z(V(g),"accessibilityManager",new pL),z(V(g),"handleLegendBBoxUpdate",function(m){if(m){var S=g.state,A=S.dataStartIndex,_=S.dataEndIndex,E=S.updateId;g.setState(j({legendBBox:m},p({props:g.props,dataStartIndex:A,dataEndIndex:_,updateId:E},j(j({},g.state),{},{legendBBox:m}))))}}),z(V(g),"handleReceiveSyncEvent",function(m,S,A){if(g.props.syncId===m){if(A===g.eventEmitterSymbol&&typeof g.props.syncMethod!="function")return;g.applySyncEvent(S)}}),z(V(g),"handleBrushChange",function(m){var S=m.startIndex,A=m.endIndex;if(S!==g.state.dataStartIndex||A!==g.state.dataEndIndex){var _=g.state.updateId;g.setState(function(){return j({dataStartIndex:S,dataEndIndex:A},p({props:g.props,dataStartIndex:S,dataEndIndex:A,updateId:_},g.state))}),g.triggerSyncEvent({dataStartIndex:S,dataEndIndex:A})}}),z(V(g),"handleMouseEnter",function(m){var S=g.getMouseInfo(m);if(S){var A=j(j({},S),{},{isTooltipActive:!0});g.setState(A),g.triggerSyncEvent(A);var _=g.props.onMouseEnter;Y(_)&&_(A,m)}}),z(V(g),"triggeredAfterMouseMove",function(m){var S=g.getMouseInfo(m),A=S?j(j({},S),{},{isTooltipActive:!0}):{isTooltipActive:!1};g.setState(A),g.triggerSyncEvent(A);var _=g.props.onMouseMove;Y(_)&&_(A,m)}),z(V(g),"handleItemMouseEnter",function(m){g.setState(function(){return{isTooltipActive:!0,activeItem:m,activePayload:m.tooltipPayload,activeCoordinate:m.tooltipPosition||{x:m.cx,y:m.cy}}})}),z(V(g),"handleItemMouseLeave",function(){g.setState(function(){return{isTooltipActive:!1}})}),z(V(g),"handleMouseMove",function(m){m.persist(),g.throttleTriggeredAfterMouseMove(m)}),z(V(g),"handleMouseLeave",function(m){var S={isTooltipActive:!1};g.setState(S),g.triggerSyncEvent(S);var A=g.props.onMouseLeave;Y(A)&&A(S,m)}),z(V(g),"handleOuterEvent",function(m){var S=cb(m),A=Ne(g.props,"".concat(S));if(S&&Y(A)){var _,E;/.*touch.*/i.test(S)?E=g.getMouseInfo(m.changedTouches[0]):E=g.getMouseInfo(m),A((_=E)!==null&&_!==void 0?_:{},m)}}),z(V(g),"handleClick",function(m){var S=g.getMouseInfo(m);if(S){var A=j(j({},S),{},{isTooltipActive:!0});g.setState(A),g.triggerSyncEvent(A);var _=g.props.onClick;Y(_)&&_(A,m)}}),z(V(g),"handleMouseDown",function(m){var S=g.props.onMouseDown;if(Y(S)){var A=g.getMouseInfo(m);S(A,m)}}),z(V(g),"handleMouseUp",function(m){var S=g.props.onMouseUp;if(Y(S)){var A=g.getMouseInfo(m);S(A,m)}}),z(V(g),"handleTouchMove",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.throttleTriggeredAfterMouseMove(m.changedTouches[0])}),z(V(g),"handleTouchStart",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseDown(m.changedTouches[0])}),z(V(g),"handleTouchEnd",function(m){m.changedTouches!=null&&m.changedTouches.length>0&&g.handleMouseUp(m.changedTouches[0])}),z(V(g),"triggerSyncEvent",function(m){g.props.syncId!==void 0&&po.emit(vo,g.props.syncId,m,g.eventEmitterSymbol)}),z(V(g),"applySyncEvent",function(m){var S=g.props,A=S.layout,_=S.syncMethod,E=g.state.updateId,$=m.dataStartIndex,T=m.dataEndIndex;if(m.dataStartIndex!==void 0||m.dataEndIndex!==void 0)g.setState(j({dataStartIndex:$,dataEndIndex:T},p({props:g.props,dataStartIndex:$,dataEndIndex:T,updateId:E},g.state)));else if(m.activeTooltipIndex!==void 0){var M=m.chartX,k=m.chartY,C=m.activeTooltipIndex,N=g.state,I=N.offset,R=N.tooltipTicks;if(!I)return;if(typeof _=="function")C=_(R,m);else if(_==="value"){C=-1;for(var B=0;B<R.length;B++)if(R[B].value===m.activeLabel){C=B;break}}var q=j(j({},I),{},{x:I.left,y:I.top}),re=Math.min(M,q.x+q.width),G=Math.min(k,q.y+q.height),ee=R[C]&&R[C].value,Te=Sv(g.state,g.props.data,C),we=R[C]?{x:A==="horizontal"?R[C].coordinate:re,y:A==="horizontal"?G:R[C].coordinate}:wv;g.setState(j(j({},m),{},{activeLabel:ee,activeCoordinate:we,activePayload:Te,activeTooltipIndex:C}))}else g.setState(m)}),z(V(g),"verticalCoordinatesGenerator",function(m,S){var A=m.xAxis,_=m.width,E=m.height,$=m.offset;return wf(Au(j(j(j({},Jr.defaultProps),A),{},{ticks:At(A,!0),viewBox:{x:0,y:0,width:_,height:E}})),$.left,$.left+$.width,S)}),z(V(g),"horizontalCoordinatesGenerator",function(m,S){var A=m.yAxis,_=m.width,E=m.height,$=m.offset;return wf(Au(j(j(j({},Jr.defaultProps),A),{},{ticks:At(A,!0),viewBox:{x:0,y:0,width:_,height:E}})),$.top,$.top+$.height,S)}),z(V(g),"axesTicksGenerator",function(m){return At(m,!0)}),z(V(g),"renderCursor",function(m){var S,A=g.state,_=A.isTooltipActive,E=A.activeCoordinate,$=A.activePayload,T=A.offset,M=A.activeTooltipIndex,k=A.tooltipAxisBandSize,C=g.getTooltipEventType(),N=(S=m.props.active)!==null&&S!==void 0?S:_;if(!m||!m.props.cursor||!N||!E||n!=="ScatterChart"&&C!=="axis")return null;var I=g.props.layout,R,B=vu;if(n==="ScatterChart")R=E,B=p2;else if(n==="BarChart")R=gL(I,E,T,k),B=Ec;else if(I==="radial"){var q=gv(E),re=q.cx,G=q.cy,ee=q.radius,Te=q.startAngle,we=q.endAngle;R={cx:re,cy:G,startAngle:Te,endAngle:we,innerRadius:ee,outerRadius:ee},B=rv}else R={points:yL(I,E,T)},B=vu;var xt=m.key||"_recharts-cursor",wt=j(j(j(j({stroke:"#ccc",pointerEvents:"none"},T),R),Z(m.props.cursor)),{},{payload:$,payloadIndex:M,key:xt,className:"recharts-tooltip-cursor"});return U.isValidElement(m.props.cursor)?U.cloneElement(m.props.cursor,wt):U.createElement(B,wt)}),z(V(g),"renderPolarAxis",function(m,S,A){var _=Ne(m,"type.axisType"),E=Ne(g.state,"".concat(_,"Map")),$=E&&E[m.props["".concat(_,"Id")]];return U.cloneElement(m,j(j({},$),{},{className:_,key:m.key||"".concat(S,"-").concat(A),ticks:At($,!0)}))}),z(V(g),"renderXAxis",function(m,S,A){var _=g.state.xAxisMap,E=_[m.props.xAxisId];return g.renderAxis(E,m,S,A)}),z(V(g),"renderYAxis",function(m,S,A){var _=g.state.yAxisMap,E=_[m.props.yAxisId];return g.renderAxis(E,m,S,A)}),z(V(g),"renderGrid",function(m){var S=g.state,A=S.xAxisMap,_=S.yAxisMap,E=S.offset,$=g.props,T=$.width,M=$.height,k=St(A),C=rL(_,function(R){return uv(R.domain,Number.isFinite)}),N=C||St(_),I=m.props||{};return U.cloneElement(m,{key:m.key||"grid",x:L(I.x)?I.x:E.left,y:L(I.y)?I.y:E.top,width:L(I.width)?I.width:E.width,height:L(I.height)?I.height:E.height,xAxis:k,yAxis:N,offset:E,chartWidth:T,chartHeight:M,verticalCoordinatesGenerator:I.verticalCoordinatesGenerator||g.verticalCoordinatesGenerator,horizontalCoordinatesGenerator:I.horizontalCoordinatesGenerator||g.horizontalCoordinatesGenerator})}),z(V(g),"renderPolarGrid",function(m){var S=m.props,A=S.radialLines,_=S.polarAngles,E=S.polarRadius,$=g.state,T=$.radiusAxisMap,M=$.angleAxisMap,k=St(T),C=St(M),N=C.cx,I=C.cy,R=C.innerRadius,B=C.outerRadius;return U.cloneElement(m,{polarAngles:Array.isArray(_)?_:At(C,!0).map(function(q){return q.coordinate}),polarRadius:Array.isArray(E)?E:At(k,!0).map(function(q){return q.coordinate}),cx:N,cy:I,innerRadius:R,outerRadius:B,key:m.key||"polar-grid",radialLines:A})}),z(V(g),"renderLegend",function(){var m=g.state.formattedGraphicalItems,S=g.props,A=S.children,_=S.width,E=S.height,$=g.props.margin||{},T=_-($.left||0)-($.right||0),M=Xp({children:A,formattedGraphicalItems:m,legendWidth:T,legendContent:f});if(!M)return null;var k=M.item,C=Th(M,mL);return U.cloneElement(k,j(j({},C),{},{chartWidth:_,chartHeight:E,margin:$,onBBoxUpdate:g.handleLegendBBoxUpdate}))}),z(V(g),"renderTooltip",function(){var m,S=g.props.children,A=ut(S,tr);if(!A)return null;var _=g.state,E=_.isTooltipActive,$=_.activeCoordinate,T=_.activePayload,M=_.activeLabel,k=_.offset,C=(m=A.props.active)!==null&&m!==void 0?m:E;return U.cloneElement(A,{viewBox:j(j({},k),{},{x:k.left,y:k.top}),active:C,label:M,payload:C?T:[],coordinate:$})}),z(V(g),"renderBrush",function(m){var S=g.props,A=S.margin,_=S.data,E=g.state,$=E.offset,T=E.dataStartIndex,M=E.dataEndIndex,k=E.updateId;return U.cloneElement(m,{key:m.key||"_recharts-brush",onChange:fi(g.handleBrushChange,m.props.onChange),data:_,x:L(m.props.x)?m.props.x:$.left,y:L(m.props.y)?m.props.y:$.top+$.height+$.brushBottom-(A.bottom||0),width:L(m.props.width)?m.props.width:$.width,startIndex:T,endIndex:M,updateId:"brush-".concat(k)})}),z(V(g),"renderReferenceElement",function(m,S,A){if(!m)return null;var _=V(g),E=_.clipPathId,$=g.state,T=$.xAxisMap,M=$.yAxisMap,k=$.offset,C=m.props,N=C.xAxisId,I=C.yAxisId;return U.cloneElement(m,{key:m.key||"".concat(S,"-").concat(A),xAxis:T[N],yAxis:M[I],viewBox:{x:k.left,y:k.top,width:k.width,height:k.height},clipPathId:E})}),z(V(g),"renderActivePoints",function(m){var S=m.item,A=m.activePoint,_=m.basePoint,E=m.childIndex,$=m.isRange,T=[],M=S.props.key,k=S.item.props,C=k.activeDot,N=k.dataKey,I=j(j({index:E,dataKey:N,cx:A.x,cy:A.y,r:4,fill:Tc(S.item),strokeWidth:2,stroke:"#fff",payload:A.payload,value:A.value,key:"".concat(M,"-activePoint-").concat(E)},Z(C)),gi(C));return T.push(b.renderActiveDot(C,I)),_?T.push(b.renderActiveDot(C,j(j({},I),{},{cx:_.x,cy:_.y,key:"".concat(M,"-basePoint-").concat(E)}))):$&&T.push(null),T}),z(V(g),"renderGraphicChild",function(m,S,A){var _=g.filterFormatItem(m,S,A);if(!_)return null;var E=g.getTooltipEventType(),$=g.state,T=$.isTooltipActive,M=$.tooltipAxis,k=$.activeTooltipIndex,C=$.activeLabel,N=g.props.children,I=ut(N,tr),R=_.props,B=R.points,q=R.isRange,re=R.baseLine,G=_.item.props,ee=G.activeDot,Te=G.hide,we=G.activeBar,xt=G.activeShape,wt=!!(!Te&&T&&I&&(ee||we||xt)),Me={};E!=="axis"&&I&&I.props.trigger==="click"?Me={onClick:fi(g.handleItemMouseEnter,m.props.onClick)}:E!=="axis"&&(Me={onMouseLeave:fi(g.handleItemMouseLeave,m.props.onMouseLeave),onMouseEnter:fi(g.handleItemMouseEnter,m.props.onMouseEnter)});var F=U.cloneElement(m,j(j({},_.props),Me));function K(Nt){return typeof M.dataKey=="function"?M.dataKey(Nt.payload):null}if(wt)if(k>=0){var X,D;if(M.dataKey&&!M.allowDuplicatedCategory){var le=typeof M.dataKey=="function"?K:"payload.".concat(M.dataKey.toString());X=yi(B,le,C),D=q&&re&&yi(re,le,C)}else X=B==null?void 0:B[k],D=q&&re&&re[k];if(xt||we){var Q=m.props.activeIndex!==void 0?m.props.activeIndex:k;return[U.cloneElement(m,j(j(j({},_.props),Me),{},{activeIndex:Q})),null,null]}if(!J(X))return[F].concat(_r(g.renderActivePoints({item:_,activePoint:X,basePoint:D,childIndex:k,isRange:q})))}else{var fe,pe=(fe=g.getItemByXY(g.state.activeCoordinate))!==null&&fe!==void 0?fe:{graphicalItem:F},Ce=pe.graphicalItem,et=Ce.item,Qn=et===void 0?m:et,It=Ce.childIndex,Dt=j(j(j({},_.props),Me),{},{activeIndex:It});return[U.cloneElement(Qn,Dt),null,null]}return q?[F,null,null]:[F,null]}),z(V(g),"renderCustomized",function(m,S,A){return U.cloneElement(m,j(j({key:"recharts-customized-".concat(A)},g.props),g.state))}),z(V(g),"renderMap",{CartesianGrid:{handler:g.renderGrid,once:!0},ReferenceArea:{handler:g.renderReferenceElement},ReferenceLine:{handler:g.renderReferenceElement},ReferenceDot:{handler:g.renderReferenceElement},XAxis:{handler:g.renderXAxis},YAxis:{handler:g.renderYAxis},Brush:{handler:g.renderBrush,once:!0},Bar:{handler:g.renderGraphicChild},Line:{handler:g.renderGraphicChild},Area:{handler:g.renderGraphicChild},Radar:{handler:g.renderGraphicChild},RadialBar:{handler:g.renderGraphicChild},Scatter:{handler:g.renderGraphicChild},Pie:{handler:g.renderGraphicChild},Funnel:{handler:g.renderGraphicChild},Tooltip:{handler:g.renderCursor,once:!0},PolarGrid:{handler:g.renderPolarGrid,once:!0},PolarAngleAxis:{handler:g.renderPolarAxis},PolarRadiusAxis:{handler:g.renderPolarAxis},Customized:{handler:g.renderCustomized}}),g.clipPathId="".concat((w=x.id)!==null&&w!==void 0?w:qn("recharts"),"-clip"),g.throttleTriggeredAfterMouseMove=Yd(g.triggeredAfterMouseMove,(O=x.throttleDelay)!==null&&O!==void 0?O:1e3/60),g.state={},g}return _L(b,[{key:"componentDidMount",value:function(){var w,O;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(w=this.props.margin.left)!==null&&w!==void 0?w:0,top:(O=this.props.margin.top)!==null&&O!==void 0?O:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout})}},{key:"getSnapshotBeforeUpdate",value:function(w,O){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==O.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==w.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==w.margin){var g,m;this.accessibilityManager.setDetails({offset:{left:(g=this.props.margin.left)!==null&&g!==void 0?g:0,top:(m=this.props.margin.top)!==null&&m!==void 0?m:0}})}return null}},{key:"componentDidUpdate",value:function(){}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var w=ut(this.props.children,tr);if(w&&typeof w.props.shared=="boolean"){var O=w.props.shared?"axis":"item";return c.indexOf(O)>=0?O:o}return o}},{key:"getMouseInfo",value:function(w){if(!this.container)return null;var O=this.container,g=O.getBoundingClientRect(),m=vE(g),S={chartX:Math.round(w.pageX-m.left),chartY:Math.round(w.pageY-m.top)},A=g.width/O.offsetWidth||1,_=this.inRange(S.chartX,S.chartY,A);if(!_)return null;var E=this.state,$=E.xAxisMap,T=E.yAxisMap,M=this.getTooltipEventType();if(M!=="axis"&&$&&T){var k=St($).scale,C=St(T).scale,N=k&&k.invert?k.invert(S.chartX):null,I=C&&C.invert?C.invert(S.chartY):null;return j(j({},S),{},{xValue:N,yValue:I})}var R=Mh(this.state,this.props.data,this.props.layout,_);return R?j(j({},S),R):null}},{key:"inRange",value:function(w,O){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,m=this.props.layout,S=w/g,A=O/g;if(m==="horizontal"||m==="vertical"){var _=this.state.offset,E=S>=_.left&&S<=_.left+_.width&&A>=_.top&&A<=_.top+_.height;return E?{x:S,y:A}:null}var $=this.state,T=$.angleAxisMap,M=$.radiusAxisMap;if(T&&M){var k=St(T);return jf({x:S,y:A},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var w=this.props.children,O=this.getTooltipEventType(),g=ut(w,tr),m={};g&&O==="axis"&&(g.props.trigger==="click"?m={onClick:this.handleClick}:m={onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd});var S=gi(this.props,this.handleOuterEvent);return j(j({},S),m)}},{key:"addListener",value:function(){po.on(vo,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){po.removeListener(vo,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(w,O,g){for(var m=this.state.formattedGraphicalItems,S=0,A=m.length;S<A;S++){var _=m[S];if(_.item===w||_.props.key===w.key||O===lt(_.item.type)&&g===_.childIndex)return _}return null}},{key:"renderAxis",value:function(w,O,g,m){var S=this.props,A=S.width,_=S.height;return P.createElement(Jr,ir({},w,{className:ne("recharts-".concat(w.axisType," ").concat(w.axisType),w.className),key:O.key||"".concat(g,"-").concat(m),viewBox:{x:0,y:0,width:A,height:_},ticksGenerator:this.axesTicksGenerator}))}},{key:"renderClipPath",value:function(){var w=this.clipPathId,O=this.state.offset,g=O.left,m=O.top,S=O.height,A=O.width;return P.createElement("defs",null,P.createElement("clipPath",{id:w},P.createElement("rect",{x:g,y:m,height:S,width:A})))}},{key:"getXScales",value:function(){var w=this.state.xAxisMap;return w?Object.entries(w).reduce(function(O,g){var m=$h(g,2),S=m[0],A=m[1];return j(j({},O),{},z({},S,A.scale))},{}):null}},{key:"getYScales",value:function(){var w=this.state.yAxisMap;return w?Object.entries(w).reduce(function(O,g){var m=$h(g,2),S=m[0],A=m[1];return j(j({},O),{},z({},S,A.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(w){var O;return(O=this.state.xAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getYScaleByAxisId",value:function(w){var O;return(O=this.state.yAxisMap)===null||O===void 0||(O=O[w])===null||O===void 0?void 0:O.scale}},{key:"getItemByXY",value:function(w){var O=this.state,g=O.formattedGraphicalItems,m=O.activeItem;if(g&&g.length)for(var S=0,A=g.length;S<A;S++){var _=g[S],E=_.props,$=_.item,T=lt($.type);if(T==="Bar"){var M=(E.data||[]).find(function(I){return i2(w,I)});if(M)return{graphicalItem:_,payload:M}}else if(T==="RadialBar"){var k=(E.data||[]).find(function(I){return jf(w,I)});if(k)return{graphicalItem:_,payload:k}}else if(ka(_,m)||Ia(_,m)||Mn(_,m)){var C=aD({graphicalItem:_,activeTooltipItem:m,itemData:$.props.data}),N=$.props.activeIndex===void 0?C:$.props.activeIndex;return{graphicalItem:j(j({},_),{},{childIndex:N}),payload:Mn(_,m)?$.props.data[C]:_.props.data[C]}}}return null}},{key:"render",value:function(){var w=this;if(!Jc(this))return null;var O=this.props,g=O.children,m=O.className,S=O.width,A=O.height,_=O.style,E=O.compact,$=O.title,T=O.desc,M=Th(O,bL),k=Z(M);if(E)return P.createElement(wo,ir({},k,{width:S,height:A,title:$,desc:T}),this.renderClipPath(),es(g,this.renderMap));if(this.props.accessibilityLayer){var C,N;k.tabIndex=(C=this.props.tabIndex)!==null&&C!==void 0?C:0,k.role=(N=this.props.role)!==null&&N!==void 0?N:"img",k.onKeyDown=function(R){w.accessibilityManager.keyboardEvent(R)},k.onFocus=function(){w.accessibilityManager.focus()}}var I=this.parseEventsOfWrapper();return P.createElement("div",ir({className:ne("recharts-wrapper",m),style:j({position:"relative",cursor:"default",width:S,height:A},_)},I,{ref:function(B){w.container=B},role:"region"}),P.createElement(wo,ir({},k,{width:S,height:A,title:$,desc:T,style:DL}),this.renderClipPath(),es(g,this.renderMap)),this.renderLegend(),this.renderTooltip())}}]),b}(U.Component),z(r,"displayName",n),z(r,"defaultProps",j({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},h)),z(r,"getDerivedStateFromProps",function(v,y){var b=v.dataKey,x=v.data,w=v.children,O=v.width,g=v.height,m=v.layout,S=v.stackOffset,A=v.margin;if(y.updateId===void 0){var _=Ch(v);return j(j(j({},_),{},{updateId:0},p(j(j({props:v},_),{},{updateId:0}),y)),{},{prevDataKey:b,prevData:x,prevWidth:O,prevHeight:g,prevLayout:m,prevStackOffset:S,prevMargin:A,prevChildren:w})}if(b!==y.prevDataKey||x!==y.prevData||O!==y.prevWidth||g!==y.prevHeight||m!==y.prevLayout||S!==y.prevStackOffset||!ar(A,y.prevMargin)){var E=Ch(v),$={chartX:y.chartX,chartY:y.chartY,isTooltipActive:y.isTooltipActive},T=j(j({},Mh(y,x,m)),{},{updateId:y.updateId+1}),M=j(j(j({},E),$),T);return j(j(j({},M),p(j({props:v},M),y)),{},{prevDataKey:b,prevData:x,prevWidth:O,prevHeight:g,prevLayout:m,prevStackOffset:S,prevMargin:A,prevChildren:w})}if(!zh(w,y.prevChildren)){var k=!J(x),C=k?y.updateId:y.updateId+1;return j(j({updateId:C},p(j(j({props:v},y),{},{updateId:C}),y)),{},{prevChildren:w})}return null}),z(r,"renderActiveDot",function(v,y){var b;return U.isValidElement(v)?b=U.cloneElement(v,y):Y(v)?b=v(y):b=P.createElement(jc,y),P.createElement(he,{className:"recharts-active-dot",key:y.key},b)}),r},XL=qL({chartName:"LineChart",GraphicalChild:Ra,axisComponents:[{axisType:"xAxis",AxisComp:Dc},{axisType:"yAxis",AxisComp:Nc}],formatAxisMap:_N});export{vv as C,XL as L,KL as R,tr as T,Dc as X,Nc as Y,nn as a,Ra as b};
//# sourceMappingURL=recharts-0fd68a7c.js.map
