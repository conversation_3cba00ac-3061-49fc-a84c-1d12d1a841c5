import{j as t}from"./client-d6fc67cc.js";import{r as y,R as ye}from"./react-25c2faed.js";import{s as i,U as je}from"./styled-components-00fe3932.js";import{t as le}from"./tradeStorage-a5c0ed9a.js";import{L as ce,c as we}from"./router-2c168ac3.js";import"./dolAnalysis-cc48a373.js";import{S as me}from"./setupTransformer-489cc905.js";import"./main-681bb6a1.js";const Te=()=>{const[r,e]=y.useState({trades:[],isLoading:!0,error:null});return y.useEffect(()=>{(async()=>{try{e(u=>({...u,isLoading:!0,error:null}));const d=[...await le.getAllTrades()].sort((u,g)=>new Date(g.trade.date).getTime()-new Date(u.trade.date).getTime());e({trades:d,isLoading:!1,error:null})}catch(c){console.error("Error fetching trades:",c),e(d=>({...d,isLoading:!1,error:"Failed to load trades. Please try again."}))}})()},[]),{...r,refreshTrades:async()=>{try{e(d=>({...d,isLoading:!0,error:null}));const c=[...await le.getAllTrades()].sort((d,u)=>new Date(u.trade.date).getTime()-new Date(d.trade.date).getTime());e({trades:c,isLoading:!1,error:null})}catch(n){console.error("Error refreshing trades:",n),e(c=>({...c,isLoading:!1,error:"Failed to refresh trades. Please try again."}))}}}};function Se(r){const[e,s]=y.useState({symbol:"",direction:"",setup:"",modelType:"",result:"",dateFrom:"",dateTo:"",primarySetupType:"",secondarySetupType:"",liquidityTaken:"",patternQualityMin:"",patternQualityMax:"",dolType:"",dolEffectivenessMin:"",dolEffectivenessMax:""}),n=o=>{const{name:a,value:m}=o.target;s(T=>({...T,[a]:m}))},c=()=>{s({symbol:"",direction:"",setup:"",modelType:"",result:"",dateFrom:"",dateTo:"",primarySetupType:"",secondarySetupType:"",liquidityTaken:"",patternQualityMin:"",patternQualityMax:"",dolType:"",dolEffectivenessMin:"",dolEffectivenessMax:""})},d=y.useMemo(()=>r?r.filter(o=>{const{trade:a,setup:m,analysis:T}=o;if(e.symbol&&a.market&&!a.market.toLowerCase().includes(e.symbol.toLowerCase())||e.direction&&a.direction!==e.direction||e.setup&&(m==null?void 0:m.primary_setup)!==e.setup||e.modelType&&a.model_type!==e.modelType)return!1;if(e.result){const b=(a.achieved_pl||0)>0;if(e.result==="win"&&!b||e.result==="loss"&&b)return!1}if(e.dateFrom){const b=new Date(a.date),E=new Date(e.dateFrom);if(b<E)return!1}if(e.dateTo){const b=new Date(a.date),E=new Date(e.dateTo);if(E.setHours(23,59,59,999),b>E)return!1}if(e.primarySetupType&&(m==null?void 0:m.primary_setup)!==e.primarySetupType||e.secondarySetupType&&(m==null?void 0:m.secondary_setup)!==e.secondarySetupType||e.liquidityTaken&&(m==null?void 0:m.liquidity_taken)!==e.liquidityTaken)return!1;if(e.patternQualityMin&&a.pattern_quality_rating){const b=parseInt(e.patternQualityMin);if(!isNaN(b)&&a.pattern_quality_rating<b)return!1}if(e.patternQualityMax&&a.pattern_quality_rating){const b=parseInt(e.patternQualityMax);if(!isNaN(b)&&a.pattern_quality_rating>b)return!1}return e.dolType&&(T==null?void 0:T.dol_target_type)!==e.dolType?!1:(e.dolEffectivenessMin,e.dolEffectivenessMax,!0)}):[],[r,e]),u=y.useMemo(()=>{if(!r)return[];const o=r.map(a=>{var m;return(m=a.setup)==null?void 0:m.primary_setup}).filter(a=>!!a);return Array.from(new Set(o))},[r]),g=y.useMemo(()=>{if(!r)return[];const o=r.map(a=>a.trade.model_type).filter(a=>!!a);return Array.from(new Set(o))},[r]),f=y.useMemo(()=>{if(!r)return[];const o=r.map(a=>{var m;return(m=a.setup)==null?void 0:m.primary_setup}).filter(a=>!!a);return Array.from(new Set(o))},[r]),p=y.useMemo(()=>{if(!r)return[];const o=r.map(a=>{var m;return(m=a.setup)==null?void 0:m.secondary_setup}).filter(a=>!!a);return Array.from(new Set(o))},[r]),v=y.useMemo(()=>{if(!r)return[];const o=r.map(a=>{var m;return(m=a.setup)==null?void 0:m.liquidity_taken}).filter(a=>!!a);return Array.from(new Set(o))},[r]),l=y.useMemo(()=>{if(!r)return[];const o=r.filter(a=>a.analysis).map(a=>{var m;return(m=a.analysis)==null?void 0:m.dol_target_type}).filter(a=>!!a);return Array.from(new Set(o))},[r]);return{filters:e,setFilters:s,handleFilterChange:n,resetFilters:c,filteredTrades:d,uniqueSetups:u,uniqueModelTypes:g,uniquePrimarySetupTypes:f,uniqueSecondarySetupTypes:p,uniqueLiquidityTypes:v,uniqueDOLTypes:l}}function Ce(r,e=!1){const[s,n]=y.useState({}),c=g=>{e&&n(f=>({...f,[g]:!f[g]}))},d=g=>e&&s[g];return{sortedTrades:y.useMemo(()=>r?[...r].sort((g,f)=>{const p=new Date(g.trade.date).getTime();return new Date(f.trade.date).getTime()-p}):[],[r]),expandedRows:s,toggleRowExpansion:c,isRowExpanded:d}}const Ne=i.div.withConfig({displayName:"TradeHeader",componentId:"sc-t6zdbv-0"})(["display:grid;grid-template-columns:var(--grid-template-columns,repeat(auto-fit,minmax(100px,1fr)));align-items:center;font-weight:600;color:",";background-color:transparent;padding:"," ",";position:sticky;top:0;z-index:1;backdrop-filter:blur(8px);"],({theme:r})=>r.colors.textSecondary,({theme:r})=>r.spacing.sm,({theme:r})=>r.spacing.md),ke=i.div.withConfig({displayName:"TradeDetail",componentId:"sc-t6zdbv-1"})(["overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 ",";"],({theme:r})=>r.spacing.xs),_e=({visibleColumns:r})=>t.jsx(Ne,{children:r.map(e=>t.jsx(ke,{children:e.label},e.id))}),Ie=i.div.withConfig({displayName:"TradeItem",componentId:"sc-1t4hice-0"})(["display:grid;grid-template-columns:var(--grid-template-columns,repeat(auto-fit,minmax(100px,1fr)));align-items:center;padding:",";background-color:",";border-radius:",";transition:all ",";cursor:",";position:relative;&:hover{background-color:",";}"],({theme:r})=>r.spacing.md,({theme:r})=>r.colors.cardBackground,({theme:r})=>r.borderRadius.sm,({theme:r})=>r.transitions.fast,({expanded:r})=>r!==void 0?"pointer":"default",({theme:r})=>r.colors.chartGrid),Le=({trade:r,visibleColumns:e,expanded:s,toggleRowExpansion:n})=>t.jsx(Ie,{expanded:s,onClick:()=>n(r.trade.id),children:e.map(c=>t.jsx(ye.Fragment,{children:c.accessor(r)},c.id))}),De=i.div.withConfig({displayName:"ExpandedContent",componentId:"sc-1aofrgt-0"})(["padding:",";background-color:",";border-radius:",";margin-top:",";margin-bottom:",";"],({theme:r})=>r.spacing.md,({theme:r})=>r.colors.background,({theme:r})=>r.borderRadius.sm,({theme:r})=>r.spacing.xs,({theme:r})=>r.spacing.md),J=i.div.withConfig({displayName:"ExpandedSection",componentId:"sc-1aofrgt-1"})(["margin-bottom:",";"],({theme:r})=>r.spacing.md),K=i.h3.withConfig({displayName:"SectionTitle",componentId:"sc-1aofrgt-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:r})=>r.fontSizes.md,({theme:r})=>r.colors.textPrimary,({theme:r})=>r.spacing.sm),ne=i.div.withConfig({displayName:"DetailGrid",componentId:"sc-1aofrgt-3"})(["display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:",";"],({theme:r})=>r.spacing.md),L=i.div.withConfig({displayName:"DetailItem",componentId:"sc-1aofrgt-4"})(["display:flex;flex-direction:column;"]),F=i.span.withConfig({displayName:"DetailLabel",componentId:"sc-1aofrgt-5"})(["font-size:",";color:",";"],({theme:r})=>r.fontSizes.sm,({theme:r})=>r.colors.textSecondary),D=i.span.withConfig({displayName:"DetailValue",componentId:"sc-1aofrgt-6"})(["font-size:",";color:",";"],({theme:r})=>r.fontSizes.md,({theme:r})=>r.colors.textPrimary),Fe=i.div.withConfig({displayName:"ActionButtons",componentId:"sc-1aofrgt-7"})(["display:flex;gap:",";margin-top:",";"],({theme:r})=>r.spacing.md,({theme:r})=>r.spacing.md),qe=i(ce).withConfig({displayName:"ActionButton",componentId:"sc-1aofrgt-8"})(["padding:"," ",";background-color:",";color:white;border-radius:",";text-decoration:none;font-size:",";font-weight:500;transition:background-color ",";&:hover{background-color:",";}"],({theme:r})=>r.spacing.xs,({theme:r})=>r.spacing.md,({theme:r})=>r.colors.primary,({theme:r})=>r.borderRadius.sm,({theme:r})=>r.fontSizes.sm,({theme:r})=>r.transitions.fast,({theme:r})=>r.colors.primaryDark),Ee=({trade:r})=>{var e,s,n,c,d;return t.jsxs(De,{children:[t.jsxs(J,{children:[t.jsx(K,{children:"Trade Details"}),t.jsxs(ne,{children:[t.jsxs(L,{children:[t.jsx(F,{children:"Market"}),t.jsx(D,{children:r.trade.market||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Date"}),t.jsx(D,{children:r.trade.date})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Direction"}),t.jsx(D,{children:r.trade.direction})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Entry Price"}),t.jsxs(D,{children:["$",(r.trade.entry_price||0).toFixed(2)]})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Exit Price"}),t.jsxs(D,{children:["$",(r.trade.exit_price||0).toFixed(2)]})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Contracts"}),t.jsx(D,{children:r.trade.no_of_contracts||0})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Profit/Loss"}),t.jsxs(D,{style:{color:(r.trade.achieved_pl||0)>0?"green":(r.trade.achieved_pl||0)<0?"red":"inherit"},children:["$",(r.trade.achieved_pl||0).toFixed(2)]})]}),t.jsxs(L,{children:[t.jsx(F,{children:"R-Multiple"}),t.jsx(D,{children:((e=r.trade.r_multiple)==null?void 0:e.toFixed(2))||"N/A"})]})]})]}),r.setup&&t.jsxs(J,{children:[t.jsx(K,{children:"Strategy"}),t.jsxs(ne,{children:[t.jsxs(L,{children:[t.jsx(F,{children:"Model Type"}),t.jsx(D,{children:r.trade.model_type||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Session"}),t.jsx(D,{children:r.trade.session||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Primary Setup"}),t.jsx(D,{children:r.setup.primary_setup||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Secondary Setup"}),t.jsx(D,{children:r.setup.secondary_setup||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Liquidity Taken"}),t.jsx(D,{children:r.setup.liquidity_taken||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Pattern Quality"}),t.jsx(D,{children:r.trade.pattern_quality_rating||"N/A"})]})]})]}),r.analysis&&t.jsxs(J,{children:[t.jsx(K,{children:"Analysis"}),t.jsxs(ne,{children:[t.jsxs(L,{children:[t.jsx(F,{children:"Execution Quality"}),t.jsx(D,{children:((s=r.analysis)==null?void 0:s.execution_quality)||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Lessons Learned"}),t.jsx(D,{children:((n=r.analysis)==null?void 0:n.lessons_learned)||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Emotional State"}),t.jsx(D,{children:((c=r.analysis)==null?void 0:c.emotional_state)||"N/A"})]}),t.jsxs(L,{children:[t.jsx(F,{children:"Market Conditions"}),t.jsx(D,{children:((d=r.analysis)==null?void 0:d.market_conditions)||"N/A"})]})]})]}),r.trade.notes&&t.jsxs(J,{children:[t.jsx(K,{children:"Notes"}),t.jsx(L,{children:t.jsx(D,{children:r.trade.notes})})]}),t.jsx(Fe,{children:t.jsx(qe,{to:`/trade-journal/edit/${r.trade.id}`,children:"Edit Trade"})})]})},Ae=i.div.withConfig({displayName:"EmptyContainer",componentId:"sc-aep78a-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";background-color:",";border-radius:",";text-align:center;"],({theme:r})=>r.spacing.xl,({theme:r})=>r.colors.background,({theme:r})=>r.borderRadius.md),Me=i.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-aep78a-1"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:r})=>r.fontSizes.lg,({theme:r})=>r.colors.textPrimary,({theme:r})=>r.spacing.sm),ze=i.p.withConfig({displayName:"EmptyDescription",componentId:"sc-aep78a-2"})(["font-size:",";color:",";margin:0 0 "," 0;max-width:500px;"],({theme:r})=>r.fontSizes.md,({theme:r})=>r.colors.textSecondary,({theme:r})=>r.spacing.lg),Pe=i(ce).withConfig({displayName:"AddTradeButton",componentId:"sc-aep78a-3"})(["display:inline-flex;align-items:center;justify-content:center;padding:"," ",";background-color:",";color:white;border-radius:",";font-weight:500;text-decoration:none;transition:background-color ",";&:hover{background-color:",";}"],({theme:r})=>r.spacing.sm,({theme:r})=>r.spacing.lg,({theme:r})=>r.colors.primary,({theme:r})=>r.borderRadius.md,({theme:r})=>r.transitions.fast,({theme:r})=>r.colors.primaryDark),Re=({filtered:r})=>t.jsxs(Ae,{children:[t.jsx(Me,{children:r?"No matching trades found":"No trades yet"}),t.jsx(ze,{children:r?"Try adjusting your filters to see more results.":"Start tracking your trades to gain insights into your trading performance."}),!r&&t.jsx(Pe,{to:"/trade/new",children:"+ Add Your First Trade"})]}),Oe=je(["0%{background-position:-1000px 0;}100%{background-position:1000px 0;}"]),$e=i.div.withConfig({displayName:"LoadingContainer",componentId:"sc-54bieh-0"})(["display:flex;flex-direction:column;gap:",";padding:"," 0;"],({theme:r})=>r.spacing.md,({theme:r})=>r.spacing.md),Be=i.div.withConfig({displayName:"LoadingRow",componentId:"sc-54bieh-1"})(["height:60px;background:linear-gradient( to right,"," 8%,"," 18%,"," 33% );background-size:2000px 100%;animation:"," 1.5s infinite linear;border-radius:",";"],({theme:r})=>r.colors.background,({theme:r})=>r.colors.cardBackground,({theme:r})=>r.colors.background,Oe,({theme:r})=>r.borderRadius.sm),Ve=({rowCount:r=5})=>t.jsx($e,{children:Array.from({length:r}).map((e,s)=>t.jsx(Be,{},s))}),Ge=i.div.withConfig({displayName:"TradeListContainer",componentId:"sc-1e6ylnq-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:r})=>r.spacing.md),z=i.div.withConfig({displayName:"TradeDetail",componentId:"sc-1e6ylnq-1"})(["overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding:0 ",";color:",";font-weight:",";"],({theme:r})=>r.spacing.xs,({theme:r,profit:e,loss:s})=>e?r.colors.success:s?r.colors.danger:r.colors.textPrimary,({profit:r,loss:e})=>r||e?600:"normal"),He=i.span.withConfig({displayName:"Badge",componentId:"sc-1e6ylnq-2"})(["display:inline-block;padding:",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;background-color:",";color:",";"],({theme:r})=>`${r.spacing.xxs} ${r.spacing.xs}`,({theme:r})=>r.borderRadius.sm,({theme:r})=>r.fontSizes.xs,({theme:r,type:e})=>e==="long"?r.colors.successLight||"rgba(76, 175, 80, 0.1)":e==="short"?r.colors.dangerLight||"rgba(244, 67, 54, 0.1)":r.colors.background,({theme:r,type:e})=>e==="long"?r.colors.success:e==="short"?r.colors.danger:r.colors.textPrimary),de=({trades:r,isLoading:e=!1,expandable:s=!1,onEditTrade:n})=>{const c=we(),{sortedTrades:d,toggleRowExpansion:u,isRowExpanded:g}=Ce(r,s),f=l=>{n?n(l):c(`/trade/edit/${l}`)},p=y.useMemo(()=>[{id:"date",label:"Date",accessor:l=>t.jsx(z,{children:l.trade.date})},{id:"symbol",label:"Symbol",accessor:l=>t.jsx(z,{children:l.trade.market||"MNQ"})},{id:"direction",label:"Direction",accessor:l=>t.jsx(z,{children:t.jsx(He,{type:l.trade.direction.toLowerCase(),children:l.trade.direction})})},{id:"setup",label:"Setup",accessor:l=>{var o,a,m;if(l.trade.setupComponents){const T=me.getShortDisplayString(l.trade.setupComponents);return t.jsx(z,{children:T})}if((o=l.trade.setupComponents)!=null&&o.constant&&((a=l.trade.setupComponents)!=null&&a.entry)){const T=me.getShortDisplayString(l.trade.setupComponents);return t.jsx(z,{children:T})}return t.jsx(z,{children:((m=l.setup)==null?void 0:m.primary_setup)||"N/A"})}},{id:"entry",label:"Entry",accessor:l=>t.jsxs(z,{children:["$",(l.trade.entry_price||0).toFixed(2)]})},{id:"exit",label:"Exit",accessor:l=>t.jsxs(z,{children:["$",(l.trade.exit_price||0).toFixed(2)]})},{id:"size",label:"Size",accessor:l=>t.jsx(z,{children:l.trade.no_of_contracts||0})},{id:"profitLoss",label:"P/L",accessor:l=>t.jsxs(z,{profit:(l.trade.achieved_pl||0)>0,loss:(l.trade.achieved_pl||0)<0,children:["$",(l.trade.achieved_pl||0).toFixed(2)]})},{id:"actions",label:"Actions",accessor:l=>t.jsx(z,{children:t.jsx("button",{onClick:()=>f(l.trade.id),style:{padding:"4px 8px",fontSize:"12px",border:"1px solid #ccc",borderRadius:"4px",background:"white",cursor:"pointer"},children:"Edit"})})}],[f]),v=`repeat(${p.length}, 1fr)`;return e?t.jsx(Ve,{rowCount:5}):!d||d.length===0?t.jsx(Re,{filtered:r&&r.length>0}):t.jsxs(Ge,{style:{"--grid-template-columns":v},children:[t.jsx(_e,{visibleColumns:p}),d.map(l=>t.jsxs(ye.Fragment,{children:[t.jsx(Le,{trade:l,visibleColumns:p,expanded:g(l.trade.id),toggleRowExpansion:u}),g(l.trade.id)&&t.jsx(Ee,{trade:l})]},l.trade.id))]})},Qe=()=>t.jsx("span",{children:"📤"}),Ye=()=>t.jsx("span",{children:"📄"}),ge=()=>t.jsx("span",{children:"✅"}),Ue=()=>t.jsx("span",{children:"❌"}),Je=()=>t.jsx("span",{children:"⚠️"}),Ke=()=>t.jsx("span",{children:"💾"}),We=({onImportComplete:r})=>{const[e,s]=y.useState(null),[n,c]=y.useState(null),[d,u]=y.useState(null),[g,f]=y.useState("idle"),[p,v]=y.useState(null);y.useState("csv");const l={date:["date"],model_type:["model type"],direction:["direction"],market:["market"],entry_price:["entry price"],exit_price:["exit price"],achieved_pl:["achieved p/l"],r_multiple:["r-multiple"],risk_points:["risk (points)"],no_of_contracts:["no. of contracts"],win_loss:["win/loss"],pattern_quality_rating:["pattern quality rating (1-5)"],session:["session (time block)"],entry_time:["entry time"],exit_time:["exit time"],setup:["setup"],primary_setup:["primary setup"],secondary_setup:["secondary setup"],notes:["notes"],rd_type:["rd type"],draw_on_liquidity:["draw on liquidity"],fvg_date:["fvg date"],entry_version:["entry version"],liquidity_taken:["liquidity taken"],additional_fvgs:["additional fvgs"],dol:["dol"],tradingview_link:["tradingview link"],dol_target_type:["dol target type"],beyond_target:["beyond target"],clustering:["clustering"],path_quality:["path quality"],idr_context:["idr context"],sequential_fvg_rd:["sequential-fvg-rd"],dol_notes:["dol notes"]},o=["RD-Cont","FVG-RD","Combined"],a=["NY Open","Lunch Macro","MOC","London Open","Asian Session","Pre-Market","After Hours","NY AM","NY PM"],m=["MNQ","NQ","ES","MES","YM","MYM","RTY","M2K"],T=y.useCallback(N=>{const h=N.target.files[0];h&&h.type==="text/csv"&&(s(h),f("processing"),E(h))},[]),b=N=>{const h=[];let S="",C=!1;for(let I=0;I<N.length;I++){const q=N[I];q==='"'?C=!C:q===","&&!C?(h.push(S.trim()),S=""):S+=q}return h.push(S.trim()),h},E=async N=>{try{const S=(await N.text()).split(`
`).filter(M=>M.trim());if(S.length===0){console.error("CSV file is empty"),f("idle");return}console.log("Total lines found:",S.length);const C=S[1],I=b(C).map(M=>M.trim().toLowerCase().replace(/"/g,""));console.log("Parsed headers:",I);const q=S.slice(2).filter(M=>M.trim()).map(M=>{const $=b(M),B={};return I.forEach((k,R)=>{var x;B[k]=((x=$[R])==null?void 0:x.trim().replace(/"/g,""))||""}),B});console.log("Parsed rows:",q.length),console.log("Sample row:",q[0]),c({headers:I,rows:q}),P(I,q)}catch(h){console.error("CSV parsing error:",h),f("idle")}},P=(N,h)=>{console.log("Starting column mapping..."),console.log("Available headers:",N);const S={};Object.entries(l).forEach(([k,R])=>{const x=N.find(pe=>R.some(w=>pe.toLowerCase().includes(w.toLowerCase())));S[k]=x||"NOT FOUND"}),console.log("Column mapping results:",S);const C=h.map((k,R)=>{const x={};if(Object.entries(l).forEach(([w,se])=>{const V=N.find(_=>se.some(j=>_.toLowerCase().includes(j.toLowerCase())));if(V&&k[V]){let _=k[V].trim();if(w==="model_type"){let j=_.replace(".","");const O=o.find(ue=>j.toLowerCase().includes(ue.toLowerCase())||j.toLowerCase().replace(/[-\s]/g,"").includes(ue.toLowerCase().replace(/[-\s]/g,"")));x[w]=O||"Combined"}else if(w==="direction"){const j=_.toLowerCase();j.includes("long")||j.includes("buy")||j==="l"?x[w]="Long":j.includes("short")||j.includes("sell")||j==="s"?x[w]="Short":x[w]=_}else if(w==="win_loss"){const j=_.toLowerCase();j.includes("win")||j.includes("profit")||j==="w"?x[w]="Win":j.includes("loss")||j.includes("lose")||j==="l"?x[w]="Loss":x[w]=_}else if(w==="session"){const j=a.find(O=>_.toLowerCase().includes(O.toLowerCase())||O.toLowerCase().includes(_.toLowerCase()));x[w]=j||_}else if(w==="market"){const j=m.find(O=>_.toLowerCase().includes(O.toLowerCase()));x[w]=j||_}else if(["entry_price","exit_price","r_multiple","risk_points"].includes(w)){const j=parseFloat(_.replace(/[^-0-9.]/g,""));x[w]=isNaN(j)?null:j}else if(w==="achieved_pl"){const j=parseFloat(_.replace(/[\$,]/g,""));x[w]=isNaN(j)?null:j}else if(w==="pattern_quality_rating"){const j=parseFloat(_)||3;x[w]=Math.max(1,Math.min(5,j))}else if(w==="no_of_contracts"){const j=parseFloat(_)||1;x[w]=Math.max(.1,j)}else["entry_time","exit_time"].includes(w)?x[w]=_.includes(":")?_:null:x[w]=_}}),x.model_type||(x.model_type="Combined"),x.direction||(x.direction="Long"),x.market||(x.market="MNQ"),x.pattern_quality_rating||(x.pattern_quality_rating=3),x.no_of_contracts||(x.no_of_contracts=1),x.date)try{const w=x.date.split("/");if(w.length===3){const[se,V,_]=w;x.date=`${_}-${se.padStart(2,"0")}-${V.padStart(2,"0")}`}}catch{x.date=new Date().toISOString().split("T")[0]}else x.date=new Date().toISOString().split("T")[0];return R<3&&(console.log(`Row ${R} mapped:`,x),console.log(`Has date: ${!!x.date}, Has entry_price: ${!!x.entry_price}, Has exit_price: ${!!x.exit_price}`)),x.entry_price||x.exit_price||x.achieved_pl?x:(R<3&&console.log(`Row ${R} rejected: no price data`),null)}).filter(Boolean);u(C);const I=C.filter(k=>k.date&&k.model_type&&(k.entry_price||k.exit_price)),q=C.filter(k=>k.model_type&&!o.includes(k.model_type)).length,M=C.filter(k=>k.date&&!k.entry_price&&!k.exit_price).length,$=C.filter(k=>k.win_loss==="Win").length,B=C.filter(k=>k.win_loss==="Loss").length;v({totalRows:h.length,validTrades:I.length,unmappedModels:q,missingPrices:M,winningTrades:$,losingTrades:B,skipped:h.length-I.length,winRate:I.length>0?($/($+B)*100).toFixed(1):0}),f("preview")},te=async()=>{f("processing");try{const N=d.map(async h=>{const S={...h,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},C={primary_setup:h.primary_setup||null,secondary_setup:h.secondary_setup||null,liquidity_taken:h.liquidity_taken||null,additional_fvgs:h.additional_fvgs||null,dol:h.dol||null},I={fvg_date:h.fvg_date||h.date,rd_type:h.rd_type||null,entry_version:h.entry_version||null,draw_on_liquidity:h.draw_on_liquidity||null},q={tradingview_link:h.tradingview_link||null,dol_target_type:h.dol_target_type||null,beyond_target:h.beyond_target||null,clustering:h.clustering||null,path_quality:h.path_quality||null,idr_context:h.idr_context||null,sequential_fvg_rd:h.sequential_fvg_rd||null,dol_notes:h.dol_notes||`Imported from CSV on ${new Date().toLocaleDateString()}. Original notes: ${h.notes||"None"}`},M={trade:S,fvg_details:I,setup:C,analysis:q};return le.saveTradeWithDetails(M)});await Promise.all(N),f("imported"),setTimeout(()=>{r==null||r()},2e3)}catch(N){console.error("Import failed:",N),f("preview")}},U=()=>{const N=[Object.keys(d[0]||{}).join(","),...d.map(I=>Object.values(I).map(q=>`"${q}"`).join(","))].join(`
`),h=new Blob([N],{type:"text/csv"}),S=URL.createObjectURL(h),C=document.createElement("a");C.href=S,C.download="cleaned_trades.csv",C.click()};return t.jsxs("div",{className:"max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg",children:[t.jsxs("div",{className:"mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"📊 Legacy Trade Data Import"}),t.jsx("p",{className:"text-gray-600",children:"Import your FVG Models trading data into the ADHD Trading Dashboard."}),t.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[t.jsx("strong",{children:"Your CSV format detected:"})," FVG Models v3 with 40 columns including all FVG-specific fields"]})]}),g==="idle"&&t.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[t.jsx("div",{className:"mx-auto text-4xl mb-4",children:t.jsx(Qe,{})}),t.jsx("div",{className:"mb-4",children:t.jsxs("label",{htmlFor:"csv-upload",className:"cursor-pointer",children:[t.jsx("span",{className:"text-lg font-medium text-blue-600 hover:text-blue-500",children:"Upload Your FVG Models CSV"}),t.jsx("input",{id:"csv-upload",type:"file",accept:".csv",onChange:T,className:"sr-only"})]})}),t.jsx("p",{className:"text-sm text-gray-500",children:'Select your "FVGModelsv3 Data Entry Form 2.csv" file'})]}),g==="processing"&&t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),t.jsx("p",{className:"text-lg text-gray-600",children:"Processing your FVG Models data..."})]}),g==="preview"&&p&&t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[t.jsx("h3",{className:"text-lg font-semibold mb-3",children:"Import Summary"}),t.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(Ye,{}),t.jsxs("span",{className:"ml-2",children:[p.totalRows," total rows"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(ge,{}),t.jsxs("span",{className:"ml-2",children:[p.validTrades," valid trades"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(Je,{}),t.jsxs("span",{className:"ml-2",children:[p.unmappedModels," unknown models"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx(Ue,{}),t.jsxs("span",{className:"ml-2",children:[p.skipped," skipped"]})]})]}),t.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm pt-2 border-t border-gray-200",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx("span",{className:"text-green-600",children:"✅"}),t.jsxs("span",{className:"ml-2",children:[p.winningTrades," wins"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx("span",{className:"text-red-600",children:"❌"}),t.jsxs("span",{className:"ml-2",children:[p.losingTrades," losses"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx("span",{className:"text-blue-600",children:"📊"}),t.jsxs("span",{className:"ml-2",children:[p.winRate,"% win rate"]})]}),t.jsxs("div",{className:"flex items-center",children:[t.jsx("span",{className:"text-yellow-600",children:"⚠️"}),t.jsxs("span",{className:"ml-2",children:[p.missingPrices," missing prices"]})]})]})]}),t.jsxs("div",{className:"bg-white border rounded-lg overflow-hidden",children:[t.jsx("div",{className:"px-4 py-3 bg-gray-50 border-b",children:t.jsx("h3",{className:"text-lg font-semibold",children:"Preview (First 5 trades)"})}),t.jsx("div",{className:"overflow-x-auto",children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsx("tr",{children:Object.keys(d[0]||{}).slice(0,10).map(N=>t.jsx("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:N},N))})}),t.jsx("tbody",{className:"divide-y divide-gray-200",children:d.slice(0,5).map((N,h)=>t.jsx("tr",{className:"hover:bg-gray-50",children:Object.entries(N).slice(0,10).map(([S,C],I)=>t.jsx("td",{className:"px-3 py-2 text-sm text-gray-900",children:typeof C=="number"&&C!==null?C.toFixed(2):C||"—"},I))},h))})]})})]}),t.jsxs("div",{className:"flex gap-4",children:[t.jsxs("button",{onClick:te,className:"flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium",children:["Import ",p.validTrades," Trades"]}),t.jsxs("button",{onClick:U,className:"bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2",children:[t.jsx(Ke,{}),"Download Cleaned CSV"]}),t.jsx("button",{onClick:()=>f("idle"),className:"bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium",children:"Start Over"})]})]}),g==="imported"&&t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"mx-auto text-6xl mb-4",children:t.jsx(ge,{})}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Import Complete!"}),t.jsxs("p",{className:"text-gray-600 mb-6",children:[p==null?void 0:p.validTrades," trades have been imported into your dashboard"]}),t.jsx("button",{onClick:()=>f("idle"),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Import Another File"})]})]})},Xe=i.div.withConfig({displayName:"FilterGroup",componentId:"sc-xvs4dn-0"})(["display:flex;flex-direction:column;gap:",";min-width:150px;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),Ze=i.label.withConfig({displayName:"FilterLabel",componentId:"sc-xvs4dn-1"})(["font-size:",";font-weight:600;color:",";text-transform:uppercase;letter-spacing:0.025em;cursor:pointer;&::before{content:'🏎️';font-size:10px;margin-right:",";opacity:0.7;}"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),oe=i.input.withConfig({displayName:"FilterInput",componentId:"sc-xvs4dn-2"})(["padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";transition:all 0.2s ease;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;transform:translateY(-1px);}&:hover:not(:disabled){border-color:","80;}&:disabled{opacity:0.5;cursor:not-allowed;}&::placeholder{color:",";font-style:italic;}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r,$hasValue:e})=>{var s,n;return e?((s=r.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=r.colors)==null?void 0:n.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.sm)||"4px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),er=i.select.withConfig({displayName:"FilterSelect",componentId:"sc-xvs4dn-3"})(["padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";cursor:pointer;transition:all 0.2s ease;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;transform:translateY(-1px);}&:hover:not(:disabled){border-color:",`80;}&:disabled{opacity:0.5;cursor:not-allowed;}background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right 8px center;background-size:16px;padding-right:32px;appearance:none;`],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r,$hasValue:e})=>{var s,n;return e?((s=r.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=r.colors)==null?void 0:n.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.sm)||"4px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),fe=i.option.withConfig({displayName:"FilterOption",componentId:"sc-xvs4dn-4"})(["background:",";color:",";padding:",";"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),rr=({name:r,label:e,type:s,value:n,onChange:c,options:d=[],placeholder:u,disabled:g=!1,className:f})=>{const p=`filter-field-${r}`,v=n!==""&&n!==null&&n!==void 0,l=a=>{g||c(r,a)},o=()=>{switch(s){case"text":return t.jsx(oe,{id:p,type:"text",value:n||"",onChange:a=>l(a.target.value),placeholder:u,disabled:g,$hasValue:v});case"number":return t.jsx(oe,{id:p,type:"number",value:n||"",onChange:a=>l(parseFloat(a.target.value)||0),placeholder:u,disabled:g,$hasValue:v});case"date":return t.jsx(oe,{id:p,type:"date",value:n||"",onChange:a=>l(a.target.value),disabled:g,$hasValue:v});case"select":return t.jsxs(er,{id:p,value:n||"",onChange:a=>l(a.target.value),disabled:g,$hasValue:v,children:[t.jsx(fe,{value:"",children:"All"}),d.map(a=>t.jsx(fe,{value:a.value,children:a.label},a.value))]});default:return null}};return t.jsxs(Xe,{className:f,children:[t.jsx(Ze,{htmlFor:p,children:e}),o()]})},tr=({initialFilters:r={},onFiltersChange:e,onReset:s}={})=>{const[n,c]=y.useState(r),d=y.useCallback((o,a)=>{c(m=>{const T={...m,[o]:a};return e&&e(T),T})},[e]),u=y.useCallback(o=>{c(a=>{const m={...a,...Object.fromEntries(Object.entries(o).filter(([,T])=>T!==void 0).map(([T,b])=>[T,b]))};return e&&e(m),m})},[e]),g=y.useCallback(()=>{c(r),s&&s(),e&&e(r)},[r,s,e]),f=y.useMemo(()=>Object.values(n).some(o=>o!==""&&o!==null&&o!==void 0),[n]),p=y.useMemo(()=>Object.values(n).filter(o=>o!==""&&o!==null&&o!==void 0).length,[n]),v=y.useCallback(o=>n[o]||"",[n]),l=y.useCallback(o=>{const a=n[o];return a!==""&&a!==null&&a!==void 0},[n]);return{filters:n,updateFilter:d,updateFilters:u,resetFilters:g,hasActiveFilters:f,activeFilterCount:p,getFilterValue:v,hasFilterValue:l}},W=(r=1,e=10)=>Array.from({length:e-r+1},(s,n)=>({value:r+n,label:(r+n).toString()})),he=[{name:"symbol",label:"Symbol",type:"text",placeholder:"AAPL, MSFT, etc.",group:"basic",order:1},{name:"direction",label:"Direction",type:"select",options:[{value:"Long",label:"Long"},{value:"Short",label:"Short"}],group:"basic",order:2},{name:"result",label:"Result",type:"select",options:[{value:"win",label:"Wins"},{value:"loss",label:"Losses"}],group:"basic",order:3},{name:"dateFrom",label:"From Date",type:"date",group:"dates",order:4},{name:"dateTo",label:"To Date",type:"date",group:"dates",order:5},{name:"setup",label:"Setup",type:"select",options:[],group:"trading",order:6},{name:"modelType",label:"Model Type",type:"select",options:[],group:"trading",order:7},{name:"primarySetupType",label:"Primary Setup",type:"select",options:[],group:"trading",order:8},{name:"secondarySetupType",label:"Secondary Setup",type:"select",options:[],group:"trading",order:9},{name:"liquidityTaken",label:"Liquidity Taken",type:"select",options:[],group:"trading",order:10},{name:"patternQualityMin",label:"Pattern Quality Min",type:"select",options:W(1,10),group:"analysis",order:11},{name:"patternQualityMax",label:"Pattern Quality Max",type:"select",options:W(1,10),group:"analysis",order:12},{name:"dolType",label:"DOL Type",type:"select",options:[],group:"analysis",order:13},{name:"dolEffectivenessMin",label:"DOL Effectiveness Min",type:"select",options:W(1,10),group:"analysis",order:14},{name:"dolEffectivenessMax",label:"DOL Effectiveness Max",type:"select",options:W(1,10),group:"analysis",order:15}],sr=r=>he.filter(e=>e.group===r).sort((e,s)=>e.order-s.order),nr=()=>["basic","dates","trading","analysis"],or={basic:"Basic Filters",dates:"Date Range",trading:"Trading Setup",analysis:"Analysis & Quality"},ar={basic:"Filter by symbol, direction, and trade results",dates:"Filter trades by date range",trading:"Filter by trading setups and strategies",analysis:"Filter by pattern quality and DOL analysis"},ir=r=>he.map(e=>{switch(e.name){case"setup":return{...e,options:r.uniqueSetups.map(s=>({value:s,label:s}))};case"modelType":return{...e,options:r.uniqueModelTypes.map(s=>({value:s,label:s}))};case"primarySetupType":return{...e,options:r.uniquePrimarySetupTypes.map(s=>({value:s,label:s}))};case"secondarySetupType":return{...e,options:r.uniqueSecondarySetupTypes.map(s=>({value:s,label:s}))};case"liquidityTaken":return{...e,options:r.uniqueLiquidityTypes.map(s=>({value:s,label:s}))};case"dolType":return{...e,options:r.uniqueDOLTypes.map(s=>({value:s,label:s}))};default:return e}}),lr=i.div.withConfig({displayName:"FilterContainer",componentId:"sc-55eayi-0"})(["display:flex;flex-direction:column;gap:",";background:",";border:1px solid ",";border-radius:",";padding:",";position:relative;overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,"," 0%,transparent 100% );}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),cr=i.div.withConfig({displayName:"FilterHeader",componentId:"sc-55eayi-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),dr=i.h3.withConfig({displayName:"FilterTitle",componentId:"sc-55eayi-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:1px;span{color:",";}"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),pr=i.div.withConfig({displayName:"FilterBadge",componentId:"sc-55eayi-3"})(["background:",";color:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;min-width:24px;text-align:center;border:1px solid ",";"],({$count:r,theme:e})=>{var s,n;return r>0?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=e.colors)==null?void 0:n.surface)||"var(--bg-secondary)"},({$count:r,theme:e})=>{var s,n;return r>0?((s=e.colors)==null?void 0:s.textInverse)||"#ffffff":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.full)||"9999px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xs)||"0.75rem"},({$count:r,theme:e})=>{var s,n;return r>0?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=e.colors)==null?void 0:n.border)||"var(--border-primary)"}),ur=i.div.withConfig({displayName:"FilterGroup",componentId:"sc-55eayi-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),mr=i.div.withConfig({displayName:"GroupHeader",componentId:"sc-55eayi-5"})(["display:flex;flex-direction:column;gap:",";padding-bottom:",";border-bottom:1px solid ",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),gr=i.h4.withConfig({displayName:"GroupTitle",componentId:"sc-55eayi-6"})(["font-size:",";font-weight:600;color:",";margin:0;text-transform:uppercase;letter-spacing:0.5px;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.md)||"1rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"}),fr=i.p.withConfig({displayName:"GroupDescription",componentId:"sc-55eayi-7"})(["font-size:",";color:",";margin:0;font-style:italic;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xs)||"0.75rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),xr=i.div.withConfig({displayName:"FieldGrid",componentId:"sc-55eayi-8"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));gap:",";@media (max-width:768px){grid-template-columns:1fr;}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),yr=i.div.withConfig({displayName:"ActionBar",componentId:"sc-55eayi-9"})(["display:flex;justify-content:flex-end;gap:",";padding-top:",";border-top:1px solid ",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),hr=i.button.withConfig({displayName:"ActionButton",componentId:"sc-55eayi-10"})(["background:",";color:",";border:1px solid ",";border-radius:",";padding:"," ",";font-size:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;&:hover:not(:disabled){background:",";transform:translateY(-1px);}&:disabled{opacity:0.5;cursor:not-allowed;}"],({$variant:r,theme:e})=>{var s;return r==="primary"?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":"transparent"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.textInverse)||"#ffffff":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=e.colors)==null?void 0:n.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.primaryDark)||"var(--primary-dark)":((n=e.colors)==null?void 0:n.surface)||"var(--bg-secondary)"}),br=({initialFilters:r={},onFiltersChange:e,onReset:s,uniqueData:n={uniqueSetups:[],uniqueModelTypes:[],uniquePrimarySetupTypes:[],uniqueSecondarySetupTypes:[],uniqueLiquidityTypes:[],uniqueDOLTypes:[]},disabled:c=!1,className:d})=>{const{filters:u,updateFilter:g,resetFilters:f,hasActiveFilters:p,activeFilterCount:v}=tr({initialFilters:r,onFiltersChange:e,onReset:s}),l=ir(n),o=nr();return t.jsxs(lr,{className:d,children:[t.jsxs(cr,{children:[t.jsxs(dr,{children:["🏎️ ",t.jsx("span",{children:"FILTERS"})]}),t.jsx(pr,{$count:v,children:v})]}),o.map(a=>{const T=sr(a).map(b=>l.find(E=>E.name===b.name)).filter(Boolean);return T.length===0?null:t.jsxs(ur,{children:[t.jsxs(mr,{children:[t.jsx(gr,{children:or[a]}),t.jsx(fr,{children:ar[a]})]}),t.jsx(xr,{children:T.map(b=>t.jsx(rr,{name:b.name,label:b.label,type:b.type,value:u[b.name]||"",onChange:g,options:b.options,placeholder:b.placeholder,disabled:c},b.name))})]},a)}),t.jsx(yr,{children:t.jsx(hr,{$variant:"secondary",onClick:f,disabled:c||!p,children:"Reset Filters"})})]})},vr=({filters:r,handleFilterChange:e,resetFilters:s,uniqueSetups:n,uniqueModelTypes:c,uniquePrimarySetupTypes:d,uniqueSecondarySetupTypes:u,uniqueLiquidityTypes:g,uniqueDOLTypes:f})=>{const p=y.useMemo(()=>({uniqueSetups:n,uniqueModelTypes:c,uniquePrimarySetupTypes:d,uniqueSecondarySetupTypes:u,uniqueLiquidityTypes:g,uniqueDOLTypes:f}),[n,c,d,u,g,f]),v=l=>{Object.entries(l).forEach(([o,a])=>{if(r[o]!==a){const m={target:{name:o,value:String(a)}};e(m)}})};return t.jsx(br,{initialFilters:r,onFiltersChange:v,onReset:s,uniqueData:p})},jr=i.div.withConfig({displayName:"ContentSection",componentId:"sc-h98974-0"})(["background-color:",";border-radius:",";padding:",";box-shadow:",";"],({theme:r})=>r.colors.surface,({theme:r})=>r.borderRadius.md,({theme:r})=>r.spacing.lg,({theme:r})=>r.shadows.sm),wr=i.h2.withConfig({displayName:"SectionTitle",componentId:"sc-h98974-1"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:r})=>r.fontSizes.lg,({theme:r})=>r.colors.textPrimary,({theme:r})=>r.spacing.md),Tr=i.div.withConfig({displayName:"ErrorMessage",componentId:"sc-h98974-2"})(["color:",";padding:",";background-color:",";border-radius:",";margin-bottom:",";"],({theme:r})=>r.colors.danger,({theme:r})=>r.spacing.md,({theme:r})=>r.colors.background,({theme:r})=>r.borderRadius.sm,({theme:r})=>r.spacing.md),be=({error:r,showFilters:e,filteredTrades:s,isLoading:n,filters:c,handleFilterChange:d,resetFilters:u,uniqueSetups:g,uniqueModelTypes:f,uniquePrimarySetupTypes:p,uniqueSecondarySetupTypes:v,uniqueLiquidityTypes:l,uniqueDOLTypes:o})=>t.jsxs(t.Fragment,{children:[r&&t.jsx(Tr,{children:r}),t.jsxs(jr,{children:[t.jsx(wr,{children:"Recent Trades"}),e&&t.jsx(vr,{filters:c,handleFilterChange:d,resetFilters:u,uniqueSetups:g,uniqueModelTypes:f,uniquePrimarySetupTypes:p,uniqueSecondarySetupTypes:v,uniqueLiquidityTypes:l,uniqueDOLTypes:o}),t.jsx(de,{trades:s,isLoading:n,expandable:!0})]})]}),Sr=i.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1slgjn-0"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"}),Cr=i.div.withConfig({displayName:"F1Header",componentId:"sc-1slgjn-1"})(["display:flex;align-items:center;justify-content:space-between;padding:",";background:linear-gradient( 135deg,"," 0%,rgba(75,85,99,0.1) 100% );border:1px solid ",";border-radius:",";position:relative;overflow:hidden;&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,"," 0%,transparent 100% );}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),Nr=i.h1.withConfig({displayName:"F1Title",componentId:"sc-1slgjn-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:2px;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;span{color:",";font-weight:800;}"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.h2)||"1.5rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),kr=i.div.withConfig({displayName:"JournalIndicator",componentId:"sc-1slgjn-3"})(["display:flex;align-items:center;gap:",";color:",";font-weight:700;text-transform:uppercase;letter-spacing:1px;font-size:",";padding:"," ",";border-radius:",";border:1px solid ",";background:",";&::before{content:'📊';font-size:12px;}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({$hasData:r,theme:e})=>{var s,n;return r?((s=e.colors)==null?void 0:s.success)||"var(--success-color)":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.full)||"9999px"},({$hasData:r,theme:e})=>{var s,n;return r?((s=e.colors)==null?void 0:s.success)||"var(--success-color)":((n=e.colors)==null?void 0:n.border)||"var(--border-primary)"},({$hasData:r,theme:e})=>{var s;return r?`${((s=e.colors)==null?void 0:s.success)||"var(--success-color)"}20`:"transparent"}),_r=i.div.withConfig({displayName:"SubHeader",componentId:"sc-1slgjn-4"})(["display:flex;justify-content:space-between;align-items:center;padding-bottom:",";border-bottom:1px solid ",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),Ir=i.div.withConfig({displayName:"TitleSection",componentId:"sc-1slgjn-5"})(["display:flex;align-items:center;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),Lr=i.h2.withConfig({displayName:"SubTitle",componentId:"sc-1slgjn-6"})(["font-size:",";margin:0;color:",";font-weight:600;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xxl)||"1.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"}),Dr=i.div.withConfig({displayName:"StatsContainer",componentId:"sc-1slgjn-7"})(["display:flex;align-items:center;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),ae=i.span.withConfig({displayName:"StatBadge",componentId:"sc-1slgjn-8"})(["background:","20;color:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;border:1px solid ","40;text-transform:uppercase;letter-spacing:0.5px;"],({$variant:r,theme:e})=>{var s,n,c,d;switch(r){case"total":return((s=e.colors)==null?void 0:s.surface)||"var(--bg-secondary)";case"filtered":return((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)";case"active":return((c=e.colors)==null?void 0:c.success)||"var(--success-color)";default:return((d=e.colors)==null?void 0:d.surface)||"var(--bg-secondary)"}},({$variant:r,theme:e})=>{var s,n,c,d;switch(r){case"total":return((s=e.colors)==null?void 0:s.textPrimary)||"#ffffff";case"filtered":return((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)";case"active":return((c=e.colors)==null?void 0:c.success)||"var(--success-color)";default:return((d=e.colors)==null?void 0:d.textPrimary)||"#ffffff"}},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xxs)||"2px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.full)||"9999px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xs)||"0.75rem"},({$variant:r,theme:e})=>{var s,n,c,d;switch(r){case"total":return((s=e.colors)==null?void 0:s.border)||"var(--border-primary)";case"filtered":return((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)";case"active":return((c=e.colors)==null?void 0:c.success)||"var(--success-color)";default:return((d=e.colors)==null?void 0:d.border)||"var(--border-primary)"}}),Fr=i.div.withConfig({displayName:"ActionsContainer",componentId:"sc-1slgjn-9"})(["display:flex;align-items:center;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),ie=i.button.withConfig({displayName:"ActionButton",componentId:"sc-1slgjn-10"})(["background:",";color:",";border:1px solid ",";border-radius:",";padding:"," ",";font-size:",";font-weight:600;cursor:pointer;display:flex;align-items:center;gap:",";transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;min-width:100px;justify-content:center;&:hover:not(:disabled){background:",";transform:translateY(-1px);box-shadow:0 4px 8px ",";}&:active:not(:disabled){transform:translateY(0);}&:disabled{opacity:0.5;cursor:not-allowed;}"],({$variant:r,theme:e})=>{var s;return r==="primary"?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":"transparent"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.textInverse)||"#ffffff":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=e.colors)==null?void 0:n.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({$variant:r,theme:e})=>{var s,n;return r==="primary"?((s=e.colors)==null?void 0:s.primaryDark)||"var(--primary-dark)":((n=e.colors)==null?void 0:n.surface)||"var(--bg-secondary)"},({$variant:r,theme:e})=>{var s;return r==="primary"?`${((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)"}40`:"rgba(0, 0, 0, 0.1)"}),qr=i(ce).withConfig({displayName:"AddTradeLink",componentId:"sc-1slgjn-11"})(["background:",";color:",";border:1px solid ",";border-radius:",";padding:"," ",";font-size:",";font-weight:600;text-decoration:none;display:flex;align-items:center;gap:",";transition:all 0.2s ease;text-transform:uppercase;letter-spacing:0.025em;min-width:120px;justify-content:center;&:hover{background:",";transform:translateY(-1px);box-shadow:0 4px 8px ","40;}&:active{transform:translateY(0);}"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.success)||"var(--success-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textInverse)||"#ffffff"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.success)||"var(--success-color)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.successDark)||"var(--success-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.success)||"var(--success-color)"}),Er=i.div.withConfig({displayName:"ModalOverlay",componentId:"sc-1slgjn-12"})(["position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.8);display:flex;align-items:center;justify-content:center;z-index:1000;padding:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),Ar=i.div.withConfig({displayName:"ModalContent",componentId:"sc-1slgjn-13"})(["background:",";border:1px solid ",";border-radius:",";max-width:800px;width:100%;max-height:90vh;overflow-y:auto;position:relative;"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"}),Mr=i.div.withConfig({displayName:"ModalHeader",componentId:"sc-1slgjn-14"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),zr=i.h2.withConfig({displayName:"ModalTitle",componentId:"sc-1slgjn-15"})(["margin:0;color:",";font-size:",";font-weight:600;"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xl)||"1.25rem"}),Pr=i.button.withConfig({displayName:"CloseButton",componentId:"sc-1slgjn-16"})(["background:none;border:none;color:",";font-size:24px;cursor:pointer;padding:4px;border-radius:4px;&:hover{background:",";color:",";}"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"}),Rr=({className:r,isLoading:e=!1,isRefreshing:s=!1,tradeCount:n=0,filteredCount:c,hasActiveFilters:d=!1,onRefresh:u,onExport:g,onImport:f,title:p="Trade Journal"})=>{const[v,l]=y.useState(!1),o=n>0,a=d&&c!==void 0&&c!==n,m=()=>{l(!0),f==null||f()},T=()=>{l(!1),u==null||u()};return t.jsxs(Sr,{className:r,children:[t.jsxs(Cr,{children:[t.jsxs(Nr,{children:["🏎️ TRADING ",t.jsx("span",{children:"JOURNAL"})]}),t.jsx(kr,{$hasData:o,children:o?`${n} TRADES`:"NO TRADES"})]}),t.jsxs(_r,{children:[t.jsxs(Ir,{children:[t.jsx(Lr,{children:p}),t.jsxs(Dr,{children:[o&&t.jsxs(ae,{$variant:"total",children:["📊 ",n.toLocaleString()," total"]}),a&&t.jsxs(ae,{$variant:"filtered",children:["🔍 ",c.toLocaleString()," filtered"]}),d&&t.jsx(ae,{$variant:"active",children:"⚡ filters active"})]})]}),t.jsxs(Fr,{children:[u&&t.jsxs(ie,{$variant:"secondary",onClick:u,disabled:e,title:e?"Refreshing trades...":"Refresh trade data",children:[e||s?"⏳":"🔄",e?"Refreshing":"Refresh"]}),t.jsx(ie,{$variant:"secondary",onClick:m,disabled:e,title:"Import legacy trade data from CSV",children:"📥 Import"}),g&&t.jsx(ie,{$variant:"secondary",onClick:g,disabled:e||!o,title:"Export trade data",children:"📊 Export"}),t.jsx(qr,{to:"/trade/new",title:"Add new trade",children:"➕ Add Trade"})]})]}),v&&t.jsx(Er,{onClick:()=>l(!1),children:t.jsxs(Ar,{onClick:b=>b.stopPropagation(),children:[t.jsxs(Mr,{children:[t.jsx(zr,{children:"🏎️ Import Legacy Trade Data"}),t.jsx(Pr,{onClick:()=>l(!1),children:"×"})]}),t.jsx(We,{onImportComplete:T})]})})]})},Or=i.div.withConfig({displayName:"TabsContainer",componentId:"sc-18uhsr1-0"})(["display:flex;gap:0;margin:"," 0 "," 0;border-bottom:1px solid ",";position:relative;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),$r=i.button.withConfig({displayName:"Tab",componentId:"sc-18uhsr1-1"})(["padding:"," ",";border:none;background:transparent;color:",";cursor:",";transition:all 0.2s ease;font-weight:",";font-size:",";position:relative;border-bottom:2px solid transparent;text-transform:uppercase;letter-spacing:0.025em;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;display:flex;align-items:center;gap:",";&::after{content:'';position:absolute;bottom:-1px;left:0;right:0;height:2px;background:",";transform:scaleX(",");transition:transform 0.2s ease;transform-origin:center;}&:hover:not(:disabled){color:",";transform:translateY(-1px);&::after{transform:scaleX(1);background:",";}}&:active:not(:disabled){transform:translateY(0);}"," @media (max-width:768px){padding:"," ",";font-size:",";}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({$isActive:r,theme:e})=>{var s,n;return r?((s=e.colors)==null?void 0:s.textPrimary)||"#ffffff":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$disabled:r})=>r?"not-allowed":"pointer",({$isActive:r})=>r?"600":"400",({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.md)||"1rem"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({$isActive:r})=>r?1:0,({$isActive:r,theme:e})=>{var s,n;return r?((s=e.colors)==null?void 0:s.textPrimary)||"#ffffff":((n=e.colors)==null?void 0:n.textPrimary)||"#ffffff"},({$isActive:r,theme:e})=>{var s,n;return r?((s=e.colors)==null?void 0:s.primary)||"var(--primary-color)":((n=e.colors)==null?void 0:n.textSecondary)||"var(--text-secondary)"},({$disabled:r})=>r&&`
    opacity: 0.5;
    cursor: not-allowed;
  `,({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"}),Br=i.span.withConfig({displayName:"TabIcon",componentId:"sc-18uhsr1-2"})(["font-size:16px;@media (max-width:768px){font-size:14px;}"]),Vr=i.span.withConfig({displayName:"TabLabel",componentId:"sc-18uhsr1-3"})(["@media (max-width:768px){display:none;}"]),X=i.span.withConfig({displayName:"TabBadge",componentId:"sc-18uhsr1-4"})(["background:",";color:",";padding:2px 6px;border-radius:",";font-size:",";font-weight:600;min-width:20px;text-align:center;line-height:1;margin-left:",";@media (max-width:768px){display:none;}"],({$variant:r,theme:e})=>{var s,n,c,d;switch(r){case"count":return((s=e.colors)==null?void 0:s.surface)||"var(--bg-secondary)";case"active":return((n=e.colors)==null?void 0:n.primary)||"var(--primary-color)";case"new":return((c=e.colors)==null?void 0:c.success)||"var(--success-color)";default:return((d=e.colors)==null?void 0:d.surface)||"var(--bg-secondary)"}},({$variant:r,theme:e})=>{var s,n,c,d;switch(r){case"count":return((s=e.colors)==null?void 0:s.textSecondary)||"var(--text-secondary)";case"active":return((n=e.colors)==null?void 0:n.textInverse)||"#ffffff";case"new":return((c=e.colors)==null?void 0:c.textInverse)||"#ffffff";default:return((d=e.colors)==null?void 0:d.textSecondary)||"var(--text-secondary)"}},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.full)||"9999px"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xs)||"0.75rem"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),xe={all:{icon:"📋",label:"All Trades",description:"Complete trade history and journal entries"},recent:{icon:"⚡",label:"Recent",description:"Latest trades and recent activity"},filters:{icon:"🔍",label:"Filters",description:"Advanced filtering and search options"},stats:{icon:"📊",label:"Statistics",description:"Performance metrics and analytics"}},Gr=({activeTab:r,onTabChange:e,disabled:s=!1,tradeCounts:n,hasActiveFilters:c=!1,className:d})=>{const u=p=>{s||e(p)},g=(p,v)=>{(p.key==="Enter"||p.key===" ")&&!s&&(p.preventDefault(),e(v))},f=p=>{if(!n)return null;switch(p){case"all":return n.total>0?t.jsx(X,{$variant:"count",children:n.total}):null;case"recent":return n.recent>0?t.jsx(X,{$variant:"new",children:n.recent}):null;case"filters":return c?t.jsx(X,{$variant:"active",children:"ON"}):null;case"stats":return n.total>0?t.jsx(X,{$variant:"count",children:"📈"}):null;default:return null}};return t.jsx(Or,{className:d,role:"tablist",children:Object.keys(xe).map(p=>{const v=xe[p],l=r===p,o=f(p);return t.jsxs($r,{$isActive:l,$disabled:s,onClick:()=>u(p),onKeyDown:a=>g(a,p),disabled:s,role:"tab","aria-selected":l,"aria-controls":`journal-panel-${p}`,tabIndex:s?-1:0,title:v.description,children:[t.jsx(Br,{children:v.icon}),t.jsx(Vr,{children:v.label}),o]},p)})})},A=["all","recent","filters","stats"],Hr="adhd-trading-dashboard:journal:active-tab",Qr=(r,e)=>{try{const s=localStorage.getItem(r);if(s&&A.includes(s))return s}catch(s){console.warn("Failed to load journal tab from localStorage:",s)}return e},Yr=(r,e)=>{try{localStorage.setItem(r,e)}catch(s){console.warn("Failed to save journal tab to localStorage:",s)}},Ur=({defaultTab:r="all",storageKey:e=Hr}={})=>{const[s,n]=y.useState(()=>Qr(e,r)),[c,d]=y.useState(!1),u=y.useCallback(l=>{A.includes(l)&&(n(l),Yr(e,l),l==="filters"&&d(!0))},[e]),g=y.useCallback(()=>{const o=(A.indexOf(s)+1)%A.length;u(A[o])},[s,u]),f=y.useCallback(()=>{const l=A.indexOf(s),o=l===0?A.length-1:l-1;u(A[o])},[s,u]),p=y.useCallback(l=>s===l,[s]),v=y.useCallback(l=>A.indexOf(l),[]);return y.useEffect(()=>{const l=o=>{var a,m,T,b,E;if(!(((a=document.activeElement)==null?void 0:a.tagName)==="INPUT"||((m=document.activeElement)==null?void 0:m.tagName)==="TEXTAREA"||((T=document.activeElement)==null?void 0:T.tagName)==="SELECT")){if((o.ctrlKey||o.metaKey)&&!o.shiftKey)switch(o.key){case"ArrowLeft":o.preventDefault(),f();break;case"ArrowRight":o.preventDefault(),g();break}if(o.key>="1"&&o.key<="4"&&!o.ctrlKey&&!o.metaKey){const P=parseInt(o.key)-1;P<A.length&&(o.preventDefault(),u(A[P]))}if(o.altKey&&!o.ctrlKey&&!o.metaKey)switch(o.key.toLowerCase()){case"a":o.preventDefault(),u("all");break;case"r":o.preventDefault(),u("recent");break;case"f":o.preventDefault(),u("filters");break;case"s":o.preventDefault(),u("stats");break}o.key.toLowerCase()==="f"&&!o.ctrlKey&&!o.metaKey&&!o.altKey&&((b=document.activeElement)==null?void 0:b.tagName)!=="INPUT"&&((E=document.activeElement)==null?void 0:E.tagName)!=="TEXTAREA"&&(o.preventDefault(),s==="filters"?d(!c):u("filters"))}};return window.addEventListener("keydown",l),()=>window.removeEventListener("keydown",l)},[g,f,u,s,c]),{activeTab:s,setActiveTab:u,nextTab:g,previousTab:f,isTabActive:p,getTabIndex:v,availableTabs:A,showFilters:c,setShowFilters:d}},G=i.div.withConfig({displayName:"EmptyState",componentId:"sc-1qq0ya8-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:300px;background:",";border-radius:",";border:1px solid ",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"48px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"}),H=i.div.withConfig({displayName:"EmptyIcon",componentId:"sc-1qq0ya8-1"})(["font-size:48px;margin-bottom:",";opacity:0.7;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),Q=i.h3.withConfig({displayName:"EmptyTitle",componentId:"sc-1qq0ya8-2"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"}),Y=i.p.withConfig({displayName:"EmptyMessage",componentId:"sc-1qq0ya8-3"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),Jr=i.div.withConfig({displayName:"StatsContainer",componentId:"sc-1qq0ya8-4"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:",";margin-bottom:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"}),Z=i.div.withConfig({displayName:"StatCard",componentId:"sc-1qq0ya8-5"})(["background:",";border:1px solid ",";border-radius:",";padding:",";text-align:center;"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),ee=i.div.withConfig({displayName:"StatValue",componentId:"sc-1qq0ya8-6"})(["font-size:",";font-weight:700;color:",";margin-bottom:",";"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xxl)||"2rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),re=i.div.withConfig({displayName:"StatLabel",componentId:"sc-1qq0ya8-7"})(["font-size:",";color:",";text-transform:uppercase;letter-spacing:0.025em;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),Kr=({data:r,isLoading:e,error:s,showFilters:n,handlers:c})=>s?t.jsxs(G,{children:[t.jsx(H,{children:"⚠️"}),t.jsx(Q,{children:"Error Loading Trades"}),t.jsx(Y,{children:s})]}):!e&&r.trades.length===0?t.jsxs(G,{children:[t.jsx(H,{children:"📋"}),t.jsx(Q,{children:"No Trades Found"}),t.jsx(Y,{children:"Start building your trading journal by adding your first trade."})]}):t.jsx(be,{error:s,showFilters:n,filteredTrades:r.filteredTrades,isLoading:e,filters:r.filters,handleFilterChange:c.handleFilterChange,resetFilters:c.resetFilters,uniqueSetups:r.uniqueSetups,uniqueModelTypes:r.uniqueModelTypes,uniquePrimarySetupTypes:r.uniquePrimarySetupTypes,uniqueSecondarySetupTypes:r.uniqueSecondarySetupTypes,uniqueLiquidityTypes:r.uniqueLiquidityTypes,uniqueDOLTypes:r.uniqueDOLTypes}),Wr=({data:r,isLoading:e})=>!e&&r.recentTrades.length===0?t.jsxs(G,{children:[t.jsx(H,{children:"⚡"}),t.jsx(Q,{children:"No Recent Trades"}),t.jsx(Y,{children:"Recent trades from the last 7 days will appear here."})]}):t.jsx(de,{trades:r.recentTrades,isLoading:e}),Xr=({data:r,isLoading:e,handlers:s})=>t.jsx(be,{error:null,showFilters:!0,filteredTrades:r.filteredTrades,isLoading:e,filters:r.filters,handleFilterChange:s.handleFilterChange,resetFilters:s.resetFilters,uniqueSetups:r.uniqueSetups,uniqueModelTypes:r.uniqueModelTypes,uniquePrimarySetupTypes:r.uniquePrimarySetupTypes,uniqueSecondarySetupTypes:r.uniqueSecondarySetupTypes,uniqueLiquidityTypes:r.uniqueLiquidityTypes,uniqueDOLTypes:r.uniqueDOLTypes}),Zr=({data:r,isLoading:e})=>{if(!e&&r.trades.length===0)return t.jsxs(G,{children:[t.jsx(H,{children:"📊"}),t.jsx(Q,{children:"No Statistics Available"}),t.jsx(Y,{children:"Trade statistics will be calculated once you have recorded trades."})]});const s=r.trades.length,n=r.trades.filter(u=>u.trade.win_loss==="Win").length,c=s>0?(n/s*100).toFixed(1):"0",d=r.trades.reduce((u,g)=>u+(g.trade.achieved_pl||0),0);return t.jsxs("div",{children:[t.jsxs(Jr,{children:[t.jsxs(Z,{children:[t.jsx(ee,{children:s}),t.jsx(re,{children:"Total Trades"})]}),t.jsxs(Z,{children:[t.jsxs(ee,{children:[c,"%"]}),t.jsx(re,{children:"Win Rate"})]}),t.jsxs(Z,{children:[t.jsxs(ee,{children:["$",d.toFixed(2)]}),t.jsx(re,{children:"Total P&L"})]}),t.jsxs(Z,{children:[t.jsx(ee,{children:r.uniqueSetups.length}),t.jsx(re,{children:"Unique Setups"})]})]}),t.jsx(de,{trades:r.trades,isLoading:e})]})},et={all:{id:"all",title:"All Trades",description:"Complete trade history and journal entries",icon:"📋",component:Kr,showInMobile:!0,requiresData:!1},recent:{id:"recent",title:"Recent Trades",description:"Latest trades and recent activity",icon:"⚡",component:Wr,showInMobile:!0,requiresData:!1},filters:{id:"filters",title:"Advanced Filters",description:"Advanced filtering and search options",icon:"🔍",component:Xr,showInMobile:!1,requiresData:!1},stats:{id:"stats",title:"Statistics",description:"Performance metrics and analytics",icon:"📊",component:Zr,showInMobile:!0,requiresData:!0}},rt=r=>et[r],tt=r=>{const{activeTab:e}=r,s=rt(e);if(!s)return t.jsxs(G,{children:[t.jsx(H,{children:"❌"}),t.jsx(Q,{children:"Unknown Tab"}),t.jsxs(Y,{children:['Tab "',e,'" not found.']})]});const n=s.component;return t.jsx("div",{id:`journal-panel-${e}`,role:"tabpanel","aria-labelledby":`journal-tab-${e}`,children:t.jsx(n,{...r})})},st=i.div.withConfig({displayName:"Container",componentId:"sc-dh1wuj-0"})(["display:flex;flex-direction:column;gap:",";background:",";color:",";min-height:100vh;padding:",";max-width:1400px;margin:0 auto;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),nt=i.div.withConfig({displayName:"ContentArea",componentId:"sc-dh1wuj-1"})(["display:flex;flex-direction:column;gap:",";flex:1;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),ot=i.div.withConfig({displayName:"TabContentContainer",componentId:"sc-dh1wuj-2"})(["animation:fadeIn 0.3s ease-in-out;@keyframes fadeIn{from{opacity:0;transform:translateY(10px);}to{opacity:1;transform:translateY(0);}}"]),at=i.div.withConfig({displayName:"LoadingState",componentId:"sc-dh1wuj-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"48px"}),it=i.div.withConfig({displayName:"LoadingIcon",componentId:"sc-dh1wuj-4"})(["font-size:48px;margin-bottom:",";opacity:0.7;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:0.7;}50%{opacity:0.3;}}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),lt=i.p.withConfig({displayName:"LoadingText",componentId:"sc-dh1wuj-5"})(["font-size:",";color:",";margin:0;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),ct=i.div.withConfig({displayName:"ErrorState",componentId:"sc-dh1wuj-6"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:400px;background:","10;border:1px solid ","40;border-radius:",";margin:"," 0;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"48px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.error)||"var(--error-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.error)||"var(--error-color)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),dt=i.div.withConfig({displayName:"ErrorIcon",componentId:"sc-dh1wuj-7"})(["font-size:48px;margin-bottom:",";color:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.error)||"var(--error-color)"}),pt=i.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-dh1wuj-8"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.error)||"var(--error-color)"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"}),ut=i.p.withConfig({displayName:"ErrorMessage",componentId:"sc-dh1wuj-9"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),mt=i.button.withConfig({displayName:"RetryButton",componentId:"sc-dh1wuj-10"})(["margin-top:",";padding:"," ",";background:",";color:white;border:none;border-radius:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;&:hover{background:",";transform:translateY(-1px);}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primaryDark)||"var(--primary-dark)"}),ve=()=>t.jsxs(at,{children:[t.jsx(it,{children:"📋"}),t.jsx(lt,{children:"Loading Trade Journal..."})]}),gt=({error:r,onRetry:e})=>t.jsxs(ct,{children:[t.jsx(dt,{children:"⚠️"}),t.jsx(pt,{children:"Journal Error"}),t.jsx(ut,{children:r}),t.jsx(mt,{onClick:e,children:"Try Again"})]}),ft=({initialTab:r})=>{const{trades:e,isLoading:s,error:n,refreshTrades:c}=Te(),{filters:d,handleFilterChange:u,resetFilters:g,filteredTrades:f,uniqueSetups:p,uniqueModelTypes:v,uniquePrimarySetupTypes:l,uniqueSecondarySetupTypes:o,uniqueLiquidityTypes:a,uniqueDOLTypes:m}=Se(e),{activeTab:T,setActiveTab:b,showFilters:E}=Ur({defaultTab:r||"all"}),P=y.useMemo(()=>{const S=new Date;return S.setDate(S.getDate()-7),e.filter(C=>new Date(C.date)>=S)},[e]),te=y.useMemo(()=>({total:e.length,recent:P.length,filtered:f.length}),[e.length,P.length,f.length]),U=y.useMemo(()=>Object.values(d).some(S=>S!==""&&S!==null&&S!==void 0),[d]),N={activeTab:T,data:{trades:e,filteredTrades:f,recentTrades:P,filters:d,uniqueSetups:p,uniqueModelTypes:v,uniquePrimarySetupTypes:l,uniqueSecondarySetupTypes:o,uniqueLiquidityTypes:a,uniqueDOLTypes:m},isLoading:s,error:n,showFilters:E,handlers:{handleFilterChange:u,resetFilters:g,refreshTrades:()=>c&&c()}},h=()=>{console.log("Export trades:",f)};return n?t.jsx(gt,{error:n,onRetry:()=>c&&c()}):t.jsxs(st,{children:[t.jsx(Rr,{isLoading:s,tradeCount:e.length,filteredCount:f.length,hasActiveFilters:U,onRefresh:c,onExport:h}),t.jsx(Gr,{activeTab:T,onTabChange:b,disabled:s,tradeCounts:te,hasActiveFilters:U}),t.jsx(nt,{children:t.jsx(ot,{children:t.jsx(y.Suspense,{fallback:t.jsx(ve,{}),children:t.jsx(tt,{...N})})})})]})},xt=r=>t.jsx(y.Suspense,{fallback:t.jsx(ve,{}),children:t.jsx(ft,{...r})}),Ct=({className:r,initialTab:e})=>t.jsx(xt,{className:r,initialTab:e});export{Ct as default};
//# sourceMappingURL=TradeJournal-fde9d003.js.map
