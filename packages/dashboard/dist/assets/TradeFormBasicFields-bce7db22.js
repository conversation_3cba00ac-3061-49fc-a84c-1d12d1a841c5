import{j as n}from"./client-d6fc67cc.js";import{r as h}from"./react-25c2faed.js";import{s as u}from"./styled-components-00fe3932.js";const N=u.div.withConfig({displayName:"FieldContainer",componentId:"sc-1b388th-0"})(["display:flex;flex-direction:column;gap:",";min-width:0;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),S=u.label.withConfig({displayName:"FieldLabel",componentId:"sc-1b388th-1"})(["font-size:",";font-weight:600;color:",";cursor:pointer;text-transform:uppercase;letter-spacing:0.025em;",""],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({$required:r,theme:e})=>{var i;return r&&`
    &::after {
      content: '*';
      color: ${((i=e.colors)==null?void 0:i.primary)||"var(--primary-color)"};
      margin-left: 4px;
      font-weight: 700;
    }
  `}),T=u.input.withConfig({displayName:"BaseInput",componentId:"sc-1b388th-2"})(["padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";transition:all 0.2s ease;width:100%;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;transform:translateY(-1px);}&:disabled{opacity:0.5;cursor:not-allowed;}",""],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r,$hasError:e})=>{var i,m;return e?((i=r.colors)==null?void 0:i.error)||"var(--error-color)":((m=r.colors)==null?void 0:m.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({$fieldType:r,theme:e})=>{var i;switch(r){case"price":case"quantity":return`
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          text-align: right;
          font-weight: 600;
        `;case"percentage":return`
          text-align: right;
          &::after {
            content: '%';
            position: absolute;
            right: 12px;
            color: ${((i=e.colors)==null?void 0:i.textSecondary)||"var(--text-secondary)"};
          }
        `;default:return""}}),k=u.select.withConfig({displayName:"Select",componentId:"sc-1b388th-3"})(["padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";cursor:pointer;transition:all 0.2s ease;width:100%;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ",`20;transform:translateY(-1px);}&:disabled{opacity:0.5;cursor:not-allowed;}appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23dc2626' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right 12px center;background-size:16px;padding-right:40px;`],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.sm)||"8px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r,$hasError:e})=>{var i,m;return e?((i=r.colors)==null?void 0:i.error)||"var(--error-color)":((m=r.colors)==null?void 0:m.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"},({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),j=u.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1b388th-4"})(["font-size:",";color:",";font-weight:500;display:flex;align-items:center;gap:",";&::before{content:'⚠️';font-size:12px;}"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.xs)||"0.75rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.error)||"var(--error-color)"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),L=({name:r,label:e,type:i,value:m,onChange:g,options:b=[],inputProps:f={},error:l,required:x=!1,disabled:p=!1,placeholder:d,className:t})=>{const a=`trade-field-${r}`,o=()=>{switch(i){case"price":case"quantity":case"percentage":return"number";case"date":return"date";default:return"text"}},s=()=>{const y={id:a,name:r,value:m||"",onChange:g,required:x,disabled:p,placeholder:d,...f};switch(i){case"price":return{...y,type:"number",step:"0.01",min:"0"};case"quantity":return{...y,type:"number",step:"1",min:"0"};case"percentage":return{...y,type:"number",step:"0.1",min:"0",max:"100"};default:return{...y,type:o()}}},c=()=>i==="select"?n.jsxs(k,{id:a,name:r,value:m||"",onChange:g,required:x,disabled:p,$hasError:!!l,children:[!x&&n.jsxs("option",{value:"",children:["Select ",e]}),b.map(y=>n.jsx("option",{value:y.value,children:y.label},y.value))]}):n.jsx(T,{...s(),$hasError:!!l,$fieldType:i});return n.jsxs(N,{className:t,children:[n.jsx(S,{htmlFor:a,$required:x,children:e}),c(),l&&n.jsx(j,{role:"alert",children:l})]})},O=[{value:"long",label:"Long"},{value:"short",label:"Short"}],q=[{value:"win",label:"Win"},{value:"loss",label:"Loss"},{value:"breakeven",label:"Breakeven"}],D=[{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"Combined",label:"Combined"}],E=[{value:"NY Open",label:"NY Open"},{value:"London Open",label:"London Open"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"MOC",label:"MOC"},{value:"Overnight",label:"Overnight"}],G=[{value:"Bullish",label:"Bullish"},{value:"Bearish",label:"Bearish"},{value:"Neutral",label:"Neutral"}],z=[{value:"Hit",label:"Hit"},{value:"Missed",label:"Missed"},{value:"Partial",label:"Partial"},{value:"Pending",label:"Pending"}],F=[{name:"date",label:"Date",type:"date",required:!0,group:"basic"},{name:"symbol",label:"Symbol",type:"text",required:!0,placeholder:"e.g., AAPL, SPY, NQ",group:"basic",inputProps:{style:{textTransform:"uppercase"}}},{name:"direction",label:"Direction",type:"select",required:!0,options:O,group:"basic"},{name:"result",label:"Result",type:"select",required:!0,options:q,group:"basic"},{name:"entryPrice",label:"Entry Price",type:"price",required:!0,group:"pricing"},{name:"exitPrice",label:"Exit Price",type:"price",required:!0,group:"pricing"},{name:"quantity",label:"Quantity",type:"quantity",required:!0,group:"pricing"},{name:"profit",label:"Profit/Loss ($)",type:"price",required:!0,group:"pricing"},{name:"model",label:"Model",type:"select",options:D,group:"strategy"},{name:"session",label:"Session",type:"select",options:E,group:"strategy"},{name:"patternQuality",label:"Pattern Quality (1-10)",type:"number",group:"strategy",inputProps:{min:1,max:10,step:1}},{name:"dolTarget",label:"DOL Target",type:"text",placeholder:"Draw on Liquidity target",group:"analysis"},{name:"rdType",label:"RD Type",type:"select",options:G,group:"analysis"},{name:"drawOnLiquidity",label:"Draw on Liquidity",type:"select",options:z,group:"analysis"},{name:"entryVersion",label:"Entry Version",type:"text",placeholder:"Entry version/iteration",group:"analysis"}],R=r=>F.filter(e=>e.group===r),M=r=>F.find(e=>e.name===r),_=[{key:"basic",title:"Basic Information",description:"Essential trade details and identification",icon:"📊"},{key:"pricing",title:"Pricing & P&L",description:"Entry, exit prices and profit/loss calculations",icon:"💰"},{key:"strategy",title:"Strategy & Setup",description:"Trading model, session, and pattern quality",icon:"🎯"},{key:"analysis",title:"Analysis & DOL",description:"Draw on liquidity and advanced analysis",icon:"🔍"}],B={symbol:{pattern:/^[A-Z]{1,5}$/,message:"Symbol must be 1-5 uppercase letters"},entryPrice:{min:.01,message:"Entry price must be greater than 0"},exitPrice:{min:.01,message:"Exit price must be greater than 0"},quantity:{min:1,message:"Quantity must be at least 1"},patternQuality:{min:1,max:10,message:"Pattern quality must be between 1 and 10"}},A=u.div.withConfig({displayName:"GroupsContainer",componentId:"sc-1ydxn15-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"}),$=u.div.withConfig({displayName:"FieldGroup",componentId:"sc-1ydxn15-1"})(["background:",";border:1px solid ",";border-radius:",";overflow:hidden;transition:all 0.2s ease;&:hover{border-color:","40;box-shadow:0 8px 25px -5px rgba(0,0,0,0.1);}"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.surface)||"var(--bg-secondary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.lg)||"8px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),U=u.div.withConfig({displayName:"GroupHeader",componentId:"sc-1ydxn15-2"})(["padding:",";border-bottom:1px solid ",";background:",";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:4px;height:100%;background:",";}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.border)||"var(--border-primary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),Q=u.div.withConfig({displayName:"GroupTitleRow",componentId:"sc-1ydxn15-3"})(["display:flex;align-items:center;gap:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),Y=u.div.withConfig({displayName:"GroupIcon",componentId:"sc-1ydxn15-4"})(["font-size:24px;display:flex;align-items:center;justify-content:center;width:40px;height:40px;background:","20;border-radius:",";border:1px solid ","40;"],({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"},({theme:r})=>{var e;return((e=r.borderRadius)==null?void 0:e.md)||"6px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.primary)||"var(--primary-color)"}),H=u.h3.withConfig({displayName:"GroupTitle",componentId:"sc-1ydxn15-5"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:0.025em;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"}),W=u.p.withConfig({displayName:"GroupDescription",componentId:"sc-1ydxn15-6"})(["font-size:",";color:",";margin:"," 0 0 0;line-height:1.5;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.sm)||"0.875rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xs)||"4px"}),Z=u.div.withConfig({displayName:"GroupContent",componentId:"sc-1ydxn15-7"})(["padding:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"}),J=u.div.withConfig({displayName:"FieldsGrid",componentId:"sc-1ydxn15-8"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:",";@media (max-width:768px){grid-template-columns:1fr;gap:",";}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.lg)||"24px"},({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),K=({formValues:r,handleChange:e,handlePriceChange:i,validationErrors:m,disabled:g=!1,className:b})=>{const f=l=>["entryPrice","exitPrice","quantity"].includes(l.name)?x=>{i(x)}:e;return n.jsx(A,{className:b,children:_.map(l=>{const x=R(l.key);return x.length===0?null:n.jsxs($,{children:[n.jsx(U,{children:n.jsxs(Q,{children:[n.jsx(Y,{children:l.icon}),n.jsxs("div",{children:[n.jsx(H,{children:l.title}),n.jsx(W,{children:l.description})]})]})}),n.jsx(Z,{children:n.jsx(J,{children:x.map(p=>n.jsx(L,{name:p.name,label:p.label,type:p.type,value:r[p.name],onChange:f(p),options:p.options,inputProps:p.inputProps,error:m[p.name],required:p.required,disabled:g,placeholder:p.placeholder},p.name))})})]},l.key)})})},w=["entryPrice","exitPrice","quantity","direction"],X=({formValues:r,setFormValues:e,setValidationErrors:i,calculateProfitLoss:m})=>{const g=h.useCallback((d,t)=>{const a=M(d),o=B[d];if(a!=null&&a.required&&(!t||t===""))return`${a.label} is required`;if(!t||t==="")return null;if(o){if("pattern"in o&&!o.pattern.test(t))return o.message;if("min"in o){const s=parseFloat(t);if(isNaN(s)||s<o.min)return o.message}if("max"in o){const s=parseFloat(t);if(isNaN(s)||s>o.max)return o.message}}switch(d){case"date":const s=new Date(t);if(isNaN(s.getTime()))return"Please enter a valid date";if(s>new Date)return"Date cannot be in the future";break;case"entryPrice":case"exitPrice":case"quantity":case"profit":const c=parseFloat(t);if(isNaN(c))return"Please enter a valid number";break}return null},[]),b=h.useCallback(d=>{const{name:t,value:a}=d.target;e(s=>({...s,[t]:a}));const o=g(t,a);i(s=>{const c={...s};return o?c[t]=o:delete c[t],c})},[g,e,i]),f=h.useCallback(()=>{const d=parseFloat(r.entryPrice)||0,t=parseFloat(r.exitPrice)||0,a=parseFloat(r.quantity)||0,o=r.direction;return d===0||t===0||a===0?0:(o==="long"?t-d:d-t)*a},[r.entryPrice,r.exitPrice,r.quantity,r.direction]),l=h.useCallback(d=>{const{name:t,value:a}=d.target;e(s=>{const c={...s,[t]:a};if(w.every(y=>y===t?a:c[y])){const y=parseFloat(t==="entryPrice"?a:c.entryPrice)||0,v=parseFloat(t==="exitPrice"?a:c.exitPrice)||0,P=parseFloat(t==="quantity"?a:c.quantity)||0,I=t==="direction"?a:c.direction;if(y>0&&v>0&&P>0){const C=I==="long"?v-y:y-v;c.profit=(C*P).toFixed(2)}}return c});const o=g(t,a);i(s=>{const c={...s};return o?c[t]=o:delete c[t],c}),m&&setTimeout(m,0)},[g,e,i,m]),x=h.useCallback(()=>{const d={};let t=!0;return Object.entries(r).forEach(([a,o])=>{const s=g(a,o);s&&(d[a]=s,t=!1)}),i(d),t},[r,g,i]),p=h.useCallback(d=>w.includes(d),[]);return{handleChange:b,handlePriceChange:l,validateField:g,validateAllFields:x,calculateProfitFromValues:f,isCalculationField:p}},V=u.div.withConfig({displayName:"Container",componentId:"sc-1e5c8z1-0"})(["display:flex;flex-direction:column;gap:",";background:",";color:",";"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.background)||"var(--bg-primary)"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textPrimary)||"#ffffff"}),ee=u.div.withConfig({displayName:"LoadingState",componentId:"sc-1e5c8z1-1"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:200px;"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.xl)||"32px"}),re=u.div.withConfig({displayName:"LoadingIcon",componentId:"sc-1e5c8z1-2"})(["font-size:48px;margin-bottom:",";opacity:0.7;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:0.7;}50%{opacity:0.3;}}"],({theme:r})=>{var e;return((e=r.spacing)==null?void 0:e.md)||"12px"}),te=u.p.withConfig({displayName:"LoadingText",componentId:"sc-1e5c8z1-3"})(["font-size:",";color:",";margin:0;"],({theme:r})=>{var e;return((e=r.fontSizes)==null?void 0:e.lg)||"1.125rem"},({theme:r})=>{var e;return((e=r.colors)==null?void 0:e.textSecondary)||"var(--text-secondary)"}),ne=()=>n.jsxs(ee,{children:[n.jsx(re,{children:"📝"}),n.jsx(te,{children:"Loading Form Fields..."})]}),ie=({formValues:r,setFormValues:e,handleChange:i,validationErrors:m,setValidationErrors:g,calculateProfitLoss:b,disabled:f=!1})=>{const l=e&&g?X({formValues:r,setFormValues:e,validationErrors:m,setValidationErrors:g,calculateProfitLoss:b}):null,x=(l==null?void 0:l.handleChange)||i,p=(l==null?void 0:l.handlePriceChange)||i;return n.jsx(n.Fragment,{children:n.jsx(K,{formValues:r,handleChange:x,handlePriceChange:p,validationErrors:m,disabled:f})})},ae=r=>n.jsx(V,{className:r.className,children:n.jsx(h.Suspense,{fallback:n.jsx(ne,{}),children:n.jsx(ie,{...r})})}),ce=r=>n.jsx(ae,{...r});export{ce as T};
//# sourceMappingURL=TradeFormBasicFields-bce7db22.js.map
