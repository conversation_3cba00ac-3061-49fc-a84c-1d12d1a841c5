class s{static componentsToDescription(t){const a=[];return t.constant&&a.push(t.constant),t.action&&t.action!=="None"&&a.push(`→ ${t.action}`),t.variable&&t.variable!=="None"&&a.push(`+ ${t.variable}`),t.entry&&a.push(`[${t.entry}]`),a.join(" ")}static descriptionToComponents(t){if(!t||typeof t!="string")return null;try{const a={constant:"",action:"None",variable:"None",entry:""},e=t.match(/\[([^\]]+)\]/);e&&(a.entry=e[1],t=t.replace(e[0],"").trim());const r=t.match(/\+\s*([^→]+?)(?=\s*→|$)/);r&&(a.variable=r[1].trim(),t=t.replace(r[0],"").trim());const n=t.match(/→\s*([^+]+?)(?=\s*\+|$)/);return n&&(a.action=n[1].trim(),t=t.replace(n[0],"").trim()),t&&(a.constant=t.trim()),a.constant&&a.entry?a:null}catch(a){return console.warn("Failed to parse setup description:",t,a),null}}static isComponentFormat(t){return!t||typeof t!="string"?!1:/\[([^\]]+)\]/.test(t)}static getDisplayString(t){const a=this.componentsToDescription(t);return a||"No setup configured"}static getShortDisplayString(t){if(!t||!t.constant||!t.entry)return"No setup";const a=[],e=t.constant.replace("-FVG","").replace("Top/Bottom-","");a.push(e);const r=t.entry.replace("-Entry","");return a.push(`[${r}]`),a.join(" ")}static fromDatabaseFields(t,a,e,r){return!t||!r?null:{constant:t,action:a||"None",variable:e||"None",entry:r}}static validateComponents(t){const a=[];return t.constant||a.push("Constant element is required"),t.entry||a.push("Entry method is required"),t.action===""&&(t.action="None"),t.variable===""&&(t.variable="None"),{isValid:a.length===0,errors:a}}static createDefault(){return{constant:"",action:"None",variable:"None",entry:""}}static clone(t){return{constant:t.constant,action:t.action||"None",variable:t.variable||"None",entry:t.entry}}}export{s as S};
//# sourceMappingURL=setupTransformer-489cc905.js.map
