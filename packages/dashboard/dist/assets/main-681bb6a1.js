var W=Object.defineProperty;var J=(e,n,i)=>n in e?W(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i;var O=(e,n,i)=>(J(e,typeof n!="symbol"?n+"":n,i),i);import{j as r,c as Y}from"./client-d6fc67cc.js";import{r as l,R as X}from"./react-25c2faed.js";import{u as K,N as Q,O as Z,R as ee,a as u,b as re,B as oe}from"./router-2c168ac3.js";import{s as t,W as te,F as ne}from"./styled-components-00fe3932.js";const _=t.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),ie=t.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),G=t.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),y=t.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),P=t.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),F=t.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),ae=t.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),H=t.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),se=t.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),ce=t(H).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),de=({error:e,resetError:n,isAppLevel:i,name:a,onSkip:s})=>{const c=()=>{window.location.reload()};return i?r.jsx(_,{isAppLevel:!0,children:r.jsxs(ie,{children:[r.jsx(G,{isAppLevel:!0,children:"Something went wrong"}),r.jsx(y,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),r.jsxs(P,{children:[r.jsx("summary",{children:"Technical Details"}),r.jsx(y,{children:e.message}),e.stack&&r.jsx(F,{children:e.stack})]}),r.jsx(ce,{onClick:c,children:"Reload Application"})]})}):r.jsxs(_,{children:[r.jsx(G,{children:a?`Error in ${a}`:"Something went wrong"}),r.jsx(y,{children:a?`We encountered a problem while loading ${a}. You can try again${s?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),r.jsxs(P,{children:[r.jsx("summary",{children:"Technical Details"}),r.jsx(y,{children:e.message}),e.stack&&r.jsx(F,{children:e.stack})]}),r.jsxs(ae,{children:[r.jsx(H,{onClick:n,children:"Try Again"}),s&&r.jsx(se,{onClick:s,children:"Skip This Feature"})]})]})};class le extends l.Component{constructor(i){super(i);O(this,"resetError",()=>{this.setState({hasError:!1,error:null})});this.state={hasError:!1,error:null}}static getDerivedStateFromError(i){return{hasError:!0,error:i}}componentDidCatch(i,a){const{name:s}=this.props,c=s?`ErrorBoundary(${s})`:"ErrorBoundary";console.error(`Error caught by ${c}:`,i,a),this.props.onError&&this.props.onError(i,a)}componentDidUpdate(i){this.state.hasError&&this.props.resetOnPropsChange&&i.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:i,error:a}=this.state,{children:s,fallback:c,name:g,isFeatureBoundary:x,onSkip:h}=this.props;return i&&a?typeof c=="function"?c({error:a,resetError:this.resetError}):c||r.jsx(de,{error:a,resetError:this.resetError,isAppLevel:!x,name:g,onSkip:h}):s}}const pe=({isAppLevel:e=!1,isFeatureBoundary:n=!1,children:i,...a})=>{const s=e?"app":n?"feature":"component",c={resetOnPropsChange:s!=="app",resetOnUnmount:s!=="app",isFeatureBoundary:s==="feature"};return r.jsx(le,{...c,...a,children:i})},me=e=>r.jsx(pe,{isAppLevel:!0,...e}),o={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#0600EF",f1BlueDark:"#0500CC",f1BlueLight:"#4169E1",f1MercedesGreen:"#00D2BE",f1MercedesGreenDark:"#00A896",f1MercedesGreenLight:"#00FFE5",f1McLarenOrange:"#FF8700",f1McLarenOrangeDark:"#E67600",f1McLarenOrangeLight:"#FFA500",f1RacingYellow:"#FFD320",f1RacingYellowDark:"#E6BE1D",f1RacingYellowLight:"#FFDC4A",f1Carbon:"#1A1A1A",f1Silver:"#C0C0C0",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",orange:"#ff9800",orangeDark:"#f57c00",orangeLight:"#ffb74d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},f={background:"#0f0f0f",surface:"#1a1a1a",cardBackground:"#1a1a1a",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:o.green,warning:o.yellow,error:o.red,info:o.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:o.f1Red,profit:o.green,loss:o.red,neutral:o.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},I={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},E={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},L={light:300,regular:400,medium:500,semibold:600,bold:700},B={tight:1.25,normal:1.5,relaxed:1.75},N={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},D={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},A={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},T={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},M={fast:"0.1s",normal:"0.3s",slow:"0.5s"},R={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},fe="modulepreload",ge=function(e){return"/"+e},q={},b=function(n,i,a){if(!i||i.length===0)return n();const s=document.getElementsByTagName("link");return Promise.all(i.map(c=>{if(c=ge(c),c in q)return;q[c]=!0;const g=c.endsWith(".css"),x=g?'[rel="stylesheet"]':"";if(!!a)for(let m=s.length-1;m>=0;m--){const p=s[m];if(p.href===c&&(!g||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${c}"]${x}`))return;const d=document.createElement("link");if(d.rel=g?"stylesheet":fe,g||(d.as="script",d.crossOrigin=""),d.href=c,document.head.appendChild(d),g)return new Promise((m,p)=>{d.addEventListener("load",m),d.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${c}`)))})})).then(()=>n())},U={name:"mercedes-green",colors:{primary:o.f1MercedesGreen,primaryDark:o.f1MercedesGreenDark,primaryLight:o.f1MercedesGreenLight,secondary:o.f1Blue,secondaryDark:o.f1BlueDark,secondaryLight:o.f1BlueLight,accent:o.f1McLarenOrange,accentDark:o.f1McLarenOrangeDark,accentLight:o.f1McLarenOrangeLight,success:o.f1MercedesGreen,warning:o.f1McLarenOrange,error:o.f1Red,danger:o.f1Red,info:o.f1Blue,background:f.background,surface:f.surface,elevated:o.gray700,cardBackground:f.surface,border:f.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:f.textPrimary,textSecondary:f.textSecondary,textDisabled:f.textDisabled,textInverse:f.textInverse,chartGrid:f.chartGrid,chartLine:f.chartLine,chartAxis:o.gray400,chartTooltip:f.tooltipBackground,profit:o.f1MercedesGreen,loss:o.f1Red,neutral:o.f1Silver,tabActive:o.f1MercedesGreen,tabInactive:o.gray600,tooltipBackground:f.tooltipBackground,modalBackground:f.modalBackground,sidebarBackground:o.gray800,headerBackground:"rgba(0, 0, 0, 0.2)",sessionActive:o.f1MercedesGreen,sessionOptimal:o.f1MercedesGreenLight,sessionCaution:o.f1RacingYellow,sessionTransition:o.f1McLarenOrange,sessionInactive:o.gray600,performanceExcellent:o.f1MercedesGreen,performanceGood:o.f1Blue,performanceAverage:o.f1Silver,performancePoor:o.f1McLarenOrange,performanceAvoid:o.f1Red},spacing:I,breakpoints:D,fontSizes:E,fontWeights:L,lineHeights:B,fontFamilies:N,borderRadius:A,shadows:T,transitions:M,zIndex:R},he={name:"f1-official",colors:{primary:"#e10600",primaryDark:"#b30500",primaryLight:"#ff1e1e",secondary:"#15151e",secondaryDark:"#0f0f17",secondaryLight:"#1e1e2e",accent:"#ffd700",accentDark:"#e6be1d",accentLight:"#ffdc4a",success:"#00ff41",warning:"#ffd700",error:"#ff1e1e",danger:"#ff1e1e",info:"#00b4d8",background:"#15151e",surface:"#1e1e2e",cardBackground:"#2a2a3a",elevated:"#353545",border:"#3a3a4a",divider:"#4a4a5a",textPrimary:"#ffffff",textSecondary:"#b8b8c8",textDisabled:"#8b8b9b",textInverse:"#15151e",chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:"#e10600",chartAxis:"#b8b8c8",chartTooltip:"rgba(42, 42, 58, 0.9)",profit:"#00ff41",loss:"#ff1e1e",neutral:"#b8b8c8",tabActive:"#e10600",tabInactive:"#8b8b9b",tooltipBackground:"rgba(42, 42, 58, 0.9)",modalBackground:"rgba(21, 21, 30, 0.8)",sidebarBackground:"#1e1e2e",headerBackground:"rgba(21, 21, 30, 0.9)",sessionActive:"#e10600",sessionOptimal:"#ffd700",sessionCaution:"#ff8700",sessionTransition:"#00b4d8",sessionInactive:"#8b8b9b",performanceExcellent:"#00ff41",performanceGood:"#ffd700",performanceAverage:"#ff8700",performancePoor:"#ff1e1e",performanceAvoid:"#8b8b9b"},spacing:I,breakpoints:D,fontSizes:E,fontWeights:L,lineHeights:B,fontFamilies:N,borderRadius:A,shadows:T,transitions:M,zIndex:R},ue={name:"mercedes-dark",colors:{primary:o.f1Silver,primaryDark:o.gray500,primaryLight:o.gray300,secondary:o.f1MercedesGreenDark,secondaryDark:"#006B5D",secondaryLight:o.f1MercedesGreen,accent:o.f1Silver,accentDark:o.gray500,accentLight:o.gray300,success:o.f1MercedesGreen,warning:o.f1Silver,error:o.red,danger:o.red,info:o.f1MercedesGreenDark,background:o.gray900,surface:o.gray800,elevated:o.gray700,cardBackground:o.gray800,border:o.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:o.white,textSecondary:o.gray200,textDisabled:o.gray400,textInverse:o.gray900,chartGrid:f.chartGrid,chartLine:o.f1Blue,chartAxis:o.gray400,chartTooltip:f.tooltipBackground,profit:o.f1MercedesGreen,loss:o.red,neutral:o.f1Silver,tabActive:o.f1Silver,tabInactive:o.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:o.gray900,headerBackground:"rgba(0, 0, 0, 0.3)",sessionActive:o.f1MercedesGreen,sessionOptimal:o.f1MercedesGreenLight,sessionCaution:o.f1Silver,sessionTransition:o.gray300,sessionInactive:o.gray600,performanceExcellent:o.f1MercedesGreen,performanceGood:o.f1Silver,performanceAverage:o.gray400,performancePoor:o.gray500,performanceAvoid:o.red},spacing:I,breakpoints:D,fontSizes:E,fontWeights:L,lineHeights:B,fontFamilies:N,borderRadius:A,shadows:T,transitions:M,zIndex:R},xe=te(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),be=xe,ye={"mercedes-green":U,"f1-official":he,dark:ue},z=U,ve=e=>e==="f1"||e==="formula1"||e==="formula-1"?"mercedes-green":e==="light"?"f1-official":e,w=e=>{const n=ve(e);return ye[n]||z},V=l.createContext({theme:z,setTheme:()=>{}}),we=()=>l.useContext(V),ke=({initialTheme:e=z,persistTheme:n=!0,storageKey:i="adhd-dashboard-theme",children:a})=>{const[s,c]=l.useState(()=>{if(n&&typeof window<"u"){const d=window.localStorage.getItem(i);if(d)try{const m=w(d);return m||JSON.parse(d)}catch(m){console.error("Failed to parse stored theme:",m)}}return typeof e=="string"?w(e):e});l.useEffect(()=>{typeof document<"u"&&document.documentElement.setAttribute("data-theme",s.name)},[s.name]);const g=h=>{const d=typeof h=="string"?w(h):h;c(d),n&&typeof window<"u"&&window.localStorage.setItem(i,d.name||JSON.stringify(d))},x=({children:h})=>r.jsxs(ne,{theme:s,children:[r.jsx(be,{}),h]});return r.jsx(V.Provider,{value:{theme:s,setTheme:g},children:r.jsx(x,{children:a})})},je=t.div.withConfig({displayName:"ControlsPanel",componentId:"sc-733piu-0"})(["position:fixed;top:20px;right:20px;background:var(--bg-secondary);border:2px solid var(--primary-color);border-radius:8px;padding:var(--spacing-md);box-shadow:0 8px 32px rgba(0,0,0,0.4);backdrop-filter:blur(10px);z-index:1000;min-width:280px;transform:",";transition:transform 0.3s ease-in-out;@media (max-width:768px){top:10px;right:10px;left:10px;min-width:auto;}"],({$isVisible:e})=>e?"translateX(0)":"translateX(calc(100% + 40px))"),Ce=t.div.withConfig({displayName:"ControlsHeader",componentId:"sc-733piu-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--spacing-md);padding-bottom:var(--spacing-sm);border-bottom:1px solid rgba(255,255,255,0.1);"]),Se=t.h3.withConfig({displayName:"Title",componentId:"sc-733piu-2"})(["font-family:var(--font-body);font-size:var(--font-size-md);font-weight:600;color:var(--text-primary);margin:0;"]),Ie=t.button.withConfig({displayName:"CloseButton",componentId:"sc-733piu-3"})(["background:none;border:none;color:var(--text-secondary);font-size:20px;cursor:pointer;padding:4px;border-radius:4px;transition:color var(--transition-normal);&:hover{color:var(--text-primary);}&:focus{outline:2px solid var(--primary-color);outline-offset:2px;}"]),k=t.div.withConfig({displayName:"ControlGroup",componentId:"sc-733piu-4"})(["margin-bottom:var(--spacing-md);&:last-child{margin-bottom:0;}"]),j=t.label.withConfig({displayName:"ControlLabel",componentId:"sc-733piu-5"})(["display:flex;align-items:center;gap:var(--spacing-sm);font-family:var(--font-body);font-size:var(--font-size-sm);color:var(--text-primary);cursor:pointer;padding:var(--spacing-xs) 0;"]),C=t.input.withConfig({displayName:"ControlCheckbox",componentId:"sc-733piu-6"})(["width:18px;height:18px;accent-color:var(--primary-color);cursor:pointer;&:focus{outline:2px solid var(--primary-color);outline-offset:2px;}"]),S=t.div.withConfig({displayName:"ControlDescription",componentId:"sc-733piu-7"})(["font-size:var(--font-size-xs);color:var(--text-secondary);margin-top:var(--spacing-xxs);line-height:1.4;"]),Ee=t.button.withConfig({displayName:"ToggleButton",componentId:"sc-733piu-8"})(["position:fixed;top:20px;right:20px;background:var(--bg-secondary);border:2px solid var(--primary-color);border-radius:50%;width:48px;height:48px;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:999;transition:all var(--transition-normal);color:var(--primary-color);font-size:20px;&:hover{background:var(--primary-color);color:var(--bg-secondary);transform:scale(1.05);}&:focus{outline:2px solid var(--primary-color);outline-offset:4px;}@media (max-width:768px){top:10px;right:10px;width:44px;height:44px;font-size:18px;}"]),Le=({isVisible:e=!1,onPreferencesChange:n,className:i})=>{const[a,s]=l.useState(e),[c,g]=l.useState({motionEnabled:!0,lowStimulationMode:!1,autoDetectMotion:!0});l.useEffect(()=>{const d=localStorage.getItem("accessibility-preferences");if(d)try{const p=JSON.parse(d);g(p),x(p)}catch(p){console.warn("Failed to parse accessibility preferences:",p)}if(window.matchMedia("(prefers-reduced-motion: reduce)").matches&&c.autoDetectMotion){const p={...c,motionEnabled:!1};g(p),x(p)}},[]);const x=d=>{const m=document.documentElement;m.setAttribute("data-motion-enabled",d.motionEnabled.toString()),d.lowStimulationMode?m.setAttribute("data-accessibility-mode","low-stimulation"):m.removeAttribute("data-accessibility-mode")},h=(d,m)=>{const p={...c,[d]:m};g(p),localStorage.setItem("accessibility-preferences",JSON.stringify(p)),x(p),n==null||n(p)};return r.jsxs(r.Fragment,{children:[r.jsx(Ee,{$isActive:a,onClick:()=>s(!a),"aria-label":"Toggle accessibility controls","aria-expanded":a,"aria-controls":"accessibility-panel",children:"♿"}),r.jsxs(je,{$isVisible:a,className:i,id:"accessibility-panel",role:"dialog","aria-labelledby":"accessibility-title",children:[r.jsxs(Ce,{children:[r.jsx(Se,{id:"accessibility-title",children:"Accessibility Controls"}),r.jsx(Ie,{onClick:()=>s(!1),"aria-label":"Close accessibility controls",children:"×"})]}),r.jsxs(k,{children:[r.jsxs(j,{children:[r.jsx(C,{type:"checkbox",checked:c.motionEnabled,onChange:d=>h("motionEnabled",d.target.checked),"aria-describedby":"motion-description"}),"Enable Animations"]}),r.jsx(S,{id:"motion-description",children:"Controls all visual animations and transitions in the interface"})]}),r.jsxs(k,{children:[r.jsxs(j,{children:[r.jsx(C,{type:"checkbox",checked:c.lowStimulationMode,onChange:d=>h("lowStimulationMode",d.target.checked),"aria-describedby":"stimulation-description"}),"Low Stimulation Mode"]}),r.jsx(S,{id:"stimulation-description",children:"Reduces visual effects, glows, and racing elements for calmer experience"})]}),r.jsxs(k,{children:[r.jsxs(j,{children:[r.jsx(C,{type:"checkbox",checked:c.autoDetectMotion,onChange:d=>h("autoDetectMotion",d.target.checked),"aria-describedby":"auto-detect-description"}),"Auto-Detect Motion Preference"]}),r.jsx(S,{id:"auto-detect-description",children:"Automatically respects your system's reduced motion setting"})]})]})]})},Be=t.header.withConfig({displayName:"HeaderContainer",componentId:"sc-7htwke-0"})(["display:flex;align-items:center;justify-content:space-between;height:64px;padding:0 ",";background-color:",";border-bottom:1px solid var(--border-primary);color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.surface,({theme:e})=>e.colors.textPrimary),Ne=t.div.withConfig({displayName:"LeftSection",componentId:"sc-7htwke-1"})(["display:flex;align-items:center;"]),De=t.button.withConfig({displayName:"MenuButton",componentId:"sc-7htwke-2"})(["display:flex;align-items:center;justify-content:center;width:40px;height:40px;border:none;background:none;color:",";cursor:pointer;transition:color ",";&:hover{color:",";}"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary),Ae=t.div.withConfig({displayName:"Logo",componentId:"sc-7htwke-3"})(["display:flex;align-items:center;margin-left:",";font-size:",";font-weight:bold;color:",";text-transform:uppercase;letter-spacing:2px;font-family:'Orbitron','Inter',sans-serif;&::before{content:'🏎️';margin-right:",";font-size:1.2em;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs),Te=t.div.withConfig({displayName:"SessionInfo",componentId:"sc-7htwke-4"})(["display:flex;flex-direction:column;align-items:flex-start;margin-left:",";@media (max-width:","){display:none;}"],({theme:e})=>e.spacing.md,({theme:e})=>e.breakpoints.sm),Me=t.div.withConfig({displayName:"SessionTitle",componentId:"sc-7htwke-5"})(["font-size:",";color:var(--text-secondary);text-transform:uppercase;letter-spacing:1px;line-height:1;"],({theme:e})=>e.fontSizes.sm),Re=t.div.withConfig({displayName:"SessionYear",componentId:"sc-7htwke-6"})(["font-size:",";color:",";font-weight:bold;text-transform:uppercase;letter-spacing:1px;line-height:1;"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.colors.primary),ze=t.div.withConfig({displayName:"RightSection",componentId:"sc-7htwke-7"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>e.spacing.md),Oe=t.div.withConfig({displayName:"UserMenu",componentId:"sc-7htwke-8"})(["display:flex;align-items:center;cursor:pointer;padding:"," ",";border-radius:",";transition:background-color ",";&:hover{background-color:rgba(255,255,255,0.1);}"],({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.sm,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.transitions.fast),_e=t.div.withConfig({displayName:"Avatar",componentId:"sc-7htwke-9"})(["width:32px;height:32px;border-radius:50%;background-color:",";display:flex;align-items:center;justify-content:center;color:white;font-weight:bold;"],({theme:e})=>e.colors.primary),Ge=({toggleSidebar:e,sidebarOpen:n})=>r.jsxs(Be,{children:[r.jsxs(Ne,{children:[r.jsx(De,{onClick:e,"aria-label":"Toggle sidebar",children:n?r.jsx("span",{children:"☰"}):r.jsx("span",{children:"☰"})}),r.jsx(Ae,{children:"TRADING"}),r.jsxs(Te,{children:[r.jsx(Me,{children:"2025 SESSION 1"}),r.jsx(Re,{children:"LIVE DASHBOARD"})]})]}),r.jsx(ze,{children:r.jsx(Oe,{children:r.jsx(_e,{children:"JD"})})})]}),Pe=t.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-vmq176-0"})(["height:100%;background-color:",";border-right:1px solid var(--border-primary);display:flex;flex-direction:column;overflow:hidden;transition:width ",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.normal),Fe=t.div.withConfig({displayName:"LogoContainer",componentId:"sc-vmq176-1"})(["height:64px;display:flex;align-items:center;justify-content:",";padding:0 ",";border-bottom:1px solid var(--border-primary);"],({isOpen:e})=>e?"flex-start":"center",({theme:e,isOpen:n})=>n?e.spacing.md:"0"),qe=t.div.withConfig({displayName:"Logo",componentId:"sc-vmq176-2"})(["font-size:",";font-weight:bold;color:",";white-space:nowrap;overflow:hidden;opacity:",";transition:opacity ",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.colors.primary,({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal),$e=t.nav.withConfig({displayName:"NavContainer",componentId:"sc-vmq176-3"})(["flex:1;overflow-y:auto;padding:"," 0;"],({theme:e})=>e.spacing.md),He=t(Q).withConfig({displayName:"NavItem",componentId:"sc-vmq176-4"})(["display:flex;align-items:center;padding:"," ",";justify-content:",";color:var(--text-secondary);text-decoration:none;transition:background-color ",",color ",";&:hover{background-color:rgba(255,255,255,0.05);color:",";}&.active{color:",";background-color:rgba(255,255,255,0.05);border-left:3px solid ",";}"],({theme:e})=>e.spacing.sm,({theme:e,$isOpen:n})=>n?e.spacing.md:"0",({$isOpen:e})=>e?"flex-start":"center",({theme:e})=>e.transitions.fast,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),Ue=t.div.withConfig({displayName:"Icon",componentId:"sc-vmq176-5"})(["display:flex;align-items:center;justify-content:center;width:24px;height:24px;margin-right:",";"],({theme:e})=>e.spacing.sm),Ve=t.span.withConfig({displayName:"Label",componentId:"sc-vmq176-6"})(["white-space:nowrap;opacity:",";transition:opacity ",";overflow:hidden;max-width:",";"],({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal,({isOpen:e})=>e?"200px":"0"),We=t.div.withConfig({displayName:"Footer",componentId:"sc-vmq176-7"})(["padding:"," ",";border-top:1px solid ",";font-size:",";color:",";white-space:nowrap;opacity:",";transition:opacity ",";text-align:center;"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({isOpen:e})=>e?1:0,({theme:e})=>e.transitions.normal),Je=({isOpen:e})=>{const n=K(),i=[{path:"/",label:"Dashboard",icon:"📊"},{path:"/daily-guide",label:"Daily Guide",icon:"📅"},{path:"/journal",label:"Trade Journal",icon:"📓"},{path:"/analysis",label:"Analysis",icon:"📈"},{path:"/settings",label:"Settings",icon:"⚙️"}];return r.jsxs(Pe,{isOpen:e,children:[r.jsx(Fe,{isOpen:e,children:r.jsx(qe,{isOpen:e,children:"ADHD"})}),r.jsx($e,{children:i.map(a=>r.jsxs(He,{to:a.path,$isOpen:e,className:n.pathname===a.path?"active":"",children:[r.jsx(Ue,{children:a.icon}),r.jsx(Ve,{isOpen:e,children:a.label})]},a.path))}),r.jsx(We,{isOpen:e,children:"v1.0.0"})]})},Ye=t.div.withConfig({displayName:"LayoutContainer",componentId:"sc-1wfskt0-0"})(["display:flex;height:100vh;width:100%;overflow:hidden;background-color:",";"],({theme:e})=>e.colors.background),Xe=t.div.withConfig({displayName:"SidebarContainer",componentId:"sc-1wfskt0-1"})(["width:",";height:100%;transition:width ",";flex-shrink:0;@media (max-width:","){position:fixed;z-index:",";width:",";box-shadow:",";}"],({isOpen:e})=>e?"240px":"64px",({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md,({theme:e})=>e.zIndex.fixed,({isOpen:e})=>e?"240px":"0",({isOpen:e,theme:n})=>e?n.shadows.lg:"none"),Ke=t.div.withConfig({displayName:"ContentContainer",componentId:"sc-1wfskt0-2"})(["flex:1;display:flex;flex-direction:column;overflow:hidden;transition:margin-left ",";@media (max-width:","){margin-left:0;width:100%;}"],({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md),Qe=t.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1wfskt0-3"})(["height:64px;flex-shrink:0;"]),Ze=t.main.withConfig({displayName:"MainContent",componentId:"sc-1wfskt0-4"})(["flex:1;overflow:auto;padding:",";@media (max-width:","){padding:",";}"],({theme:e})=>e.spacing.lg,({theme:e})=>e.breakpoints.sm,({theme:e})=>e.spacing.md),er=t.div.withConfig({displayName:"Overlay",componentId:"sc-1wfskt0-5"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);z-index:",";opacity:",";visibility:",";transition:opacity ",",visibility ",";@media (min-width:","){display:none;}"],({theme:e})=>e.zIndex.modal-1,({isVisible:e})=>e?1:0,({isVisible:e})=>e?"visible":"hidden",({theme:e})=>e.transitions.normal,({theme:e})=>e.transitions.normal,({theme:e})=>e.breakpoints.md),rr=()=>{const[e,n]=l.useState(!0),i=()=>{n(!e)},a=()=>{n(!1)};return r.jsxs(Ye,{children:[r.jsx(Xe,{isOpen:e,children:r.jsx(Je,{isOpen:e})}),r.jsx(er,{isVisible:e,onClick:a}),r.jsxs(Ke,{sidebarOpen:e,children:[r.jsx(Qe,{children:r.jsx(Ge,{toggleSidebar:i,sidebarOpen:e})}),r.jsx(Ze,{children:r.jsx(Z,{})})]}),r.jsx(Le,{onPreferencesChange:s=>{console.log("Accessibility preferences updated:",s)}})]})},or=t.div.withConfig({displayName:"LoadingContainer",componentId:"sc-1u55s8z-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%;padding:",";"],({theme:e})=>e.spacing.lg),tr=t.div.withConfig({displayName:"Spinner",componentId:"sc-1u55s8z-1"})(["width:40px;height:40px;border-radius:50%;border:3px solid rgba(255,255,255,0.1);border-top-color:",";animation:spin 1s linear infinite;@keyframes spin{to{transform:rotate(360deg);}}"],({theme:e})=>e.colors.primary),nr=t.div.withConfig({displayName:"LoadingText",componentId:"sc-1u55s8z-2"})(["margin-top:",";color:",";font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.md),ir=()=>r.jsxs(or,{children:[r.jsx(tr,{}),r.jsx(nr,{children:"Loading..."})]}),ar=l.lazy(()=>b(()=>import("./TradingDashboard-cac63645.js"),["assets/TradingDashboard-cac63645.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/setupTransformer-489cc905.js","assets/tradeStorage-a5c0ed9a.js","assets/recharts-0fd68a7c.js","assets/TradeFormBasicFields-bce7db22.js"])),sr=l.lazy(()=>b(()=>import("./DailyGuide-ac65bbb9.js"),["assets/DailyGuide-ac65bbb9.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/tradeStorage-a5c0ed9a.js","assets/Card-1e58b487.js"])),cr=l.lazy(()=>b(()=>import("./TradeJournal-fde9d003.js"),["assets/TradeJournal-fde9d003.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/tradeStorage-a5c0ed9a.js","assets/router-2c168ac3.js","assets/dolAnalysis-cc48a373.js","assets/setupTransformer-489cc905.js"])),dr=l.lazy(()=>b(()=>import("./TradeAnalysis-ccd21b25.js"),["assets/TradeAnalysis-ccd21b25.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/tradeStorage-a5c0ed9a.js","assets/styled-components-00fe3932.js","assets/Card-1e58b487.js"])),$=l.lazy(()=>b(()=>import("./TradeForm-3723b041.js"),["assets/TradeForm-3723b041.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/patternQuality-31c523c9.js","assets/dolAnalysis-cc48a373.js","assets/router-2c168ac3.js","assets/tradeStorage-a5c0ed9a.js","assets/TradeFormBasicFields-bce7db22.js"])),lr=l.lazy(()=>b(()=>import("./Settings-ff0bb2e7.js"),["assets/Settings-ff0bb2e7.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/router-2c168ac3.js"])),pr=l.lazy(()=>b(()=>import("./NotFound-89e7ec7d.js"),["assets/NotFound-89e7ec7d.js","assets/client-d6fc67cc.js","assets/react-25c2faed.js","assets/styled-components-00fe3932.js","assets/router-2c168ac3.js"])),mr=()=>r.jsx(l.Suspense,{fallback:r.jsx(ir,{}),children:r.jsxs(ee,{children:[r.jsxs(u,{path:"/",element:r.jsx(rr,{}),children:[r.jsx(u,{index:!0,element:r.jsx(ar,{})}),r.jsx(u,{path:"daily-guide",element:r.jsx(sr,{})}),r.jsx(u,{path:"journal",element:r.jsx(cr,{})}),r.jsx(u,{path:"analysis",element:r.jsx(dr,{})}),r.jsx(u,{path:"trade/new",element:r.jsx($,{})}),r.jsx(u,{path:"trade/:id",element:r.jsx($,{})}),r.jsx(u,{path:"settings",element:r.jsx(lr,{})}),r.jsx(u,{path:"*",element:r.jsx(pr,{})})]}),r.jsx(u,{path:"/dashboard",element:r.jsx(re,{to:"/",replace:!0})})]})}),fr=({children:e})=>{const n=i=>{console.error("Application Error:",i)};return r.jsx(me,{onError:n,name:"Application",children:e})},gr=t.div.withConfig({displayName:"TestPanel",componentId:"sc-rl0yhg-0"})(["position:fixed;top:20px;right:20px;background:var(--session-card-bg);border:1px solid var(--session-card-border);border-radius:12px;padding:16px;min-width:300px;z-index:1000;box-shadow:var(--shadow-lg);"]),hr=t.button.withConfig({displayName:"ThemeButton",componentId:"sc-rl0yhg-1"})(["background:",";color:",";border:1px solid var(--session-card-border);border-radius:6px;padding:8px 12px;margin:4px;cursor:pointer;font-size:12px;font-weight:600;transition:all 0.2s ease;&:hover{background:var(--primary-color);color:var(--session-text-primary);}"],({isActive:e})=>e?"var(--primary-color)":"var(--session-card-bg)",({isActive:e})=>e?"var(--session-text-primary)":"var(--session-text-secondary)"),ur=t.div.withConfig({displayName:"SampleCard",componentId:"sc-rl0yhg-2"})(["background:var(--session-card-bg);border:1px solid var(--session-card-border);border-left:4px solid var(--session-card-accent);border-radius:8px;padding:12px;margin:8px 0;box-shadow:var(--shadow-sm);"]),xr=t.div.withConfig({displayName:"SampleText",componentId:"sc-rl0yhg-3"})(["color:var(--session-text-primary);font-size:14px;font-weight:600;margin-bottom:4px;"]),br=t.div.withConfig({displayName:"SampleSecondary",componentId:"sc-rl0yhg-4"})(["color:var(--session-text-secondary);font-size:12px;"]),v=t.div.withConfig({displayName:"StatusBadge",componentId:"sc-rl0yhg-5"})(["display:inline-block;background:",";color:var(--session-text-primary);padding:4px 8px;border-radius:12px;font-size:10px;font-weight:600;text-transform:uppercase;margin:2px;"],({status:e})=>{switch(e){case"success":return"var(--success-color)";case"warning":return"var(--warning-color)";case"error":return"var(--error-color)";case"info":return"var(--info-color)";default:return"var(--session-card-border)"}}),yr=t.button.withConfig({displayName:"CloseButton",componentId:"sc-rl0yhg-6"})(["position:absolute;top:8px;right:8px;background:none;border:none;color:var(--session-text-secondary);cursor:pointer;font-size:16px;padding:4px;&:hover{color:var(--session-text-primary);}"]),vr=({onClose:e})=>{const{theme:n,setTheme:i}=we(),a=[{id:"mercedes-green",name:"Mercedes Green"},{id:"f1-official",name:"F1 Official"},{id:"dark",name:"Dark"}];return r.jsxs(gr,{children:[r.jsx(yr,{onClick:e,children:"×"}),r.jsx("h3",{style:{color:"var(--session-text-primary)",margin:"0 0 12px 0"},children:"Theme Test Panel"}),r.jsxs("div",{style:{marginBottom:"16px"},children:[r.jsxs("div",{style:{color:"var(--session-text-secondary)",fontSize:"12px",marginBottom:"8px"},children:["Current: ",n.name]}),a.map(s=>r.jsx(hr,{isActive:n.name===s.id,onClick:()=>i(s.id),children:s.name},s.id))]}),r.jsxs(ur,{children:[r.jsx(xr,{children:"Sample Session Card"}),r.jsx(br,{children:"This shows the theme colors in action"}),r.jsxs("div",{style:{marginTop:"8px"},children:[r.jsx(v,{status:"success",children:"SUCCESS"}),r.jsx(v,{status:"warning",children:"WARNING"}),r.jsx(v,{status:"error",children:"ERROR"}),r.jsx(v,{status:"info",children:"INFO"})]})]}),r.jsxs("div",{style:{color:"var(--session-text-secondary)",fontSize:"10px",marginTop:"12px",lineHeight:"1.4"},children:["Test theme switching to verify:",r.jsx("br",{}),"• Clean backgrounds (no muddy layering)",r.jsx("br",{}),"• Proper accent colors",r.jsx("br",{}),"• High contrast text",r.jsx("br",{}),"• Consistent borders"]})]})};function wr(){const[e,n]=l.useState(!1),i=a=>{a.ctrlKey&&a.key==="t"&&(a.preventDefault(),n(!e))};return l.useEffect(()=>(document.addEventListener("keydown",i),()=>document.removeEventListener("keydown",i)),[e]),r.jsx(fr,{children:r.jsx(ke,{initialTheme:"mercedes-green",children:r.jsxs(oe,{children:[r.jsx(mr,{}),e&&r.jsx(vr,{onClose:()=>n(!1)})]})})})}const kr=e=>{e&&e instanceof Function&&b(()=>import("./web-vitals-60d3425a.js"),[]).then(({getCLS:n,getFID:i,getFCP:a,getLCP:s,getTTFB:c})=>{n(e),i(e),a(e),s(e),c(e)})};const jr=()=>{if(console.log("ADHD Trading Dashboard initializing..."),!document.getElementById("root")){console.error("Root element not found, creating a fallback element");const i=document.createElement("div");i.id="root",document.body.appendChild(i)}Y.createRoot(document.getElementById("root")).render(r.jsx(X.StrictMode,{children:r.jsx(wr,{})})),kr()};window.addEventListener("error",e=>{console.error("Error:",e.error||e.message)});jr();export{b as _,we as u};
//# sourceMappingURL=main-681bb6a1.js.map
