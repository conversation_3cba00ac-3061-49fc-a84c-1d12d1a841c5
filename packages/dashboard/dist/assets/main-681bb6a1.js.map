{"version": 3, "mappings": "iaAwCA,MAAMA,EAAwBC,MAAGC,mEAEpBC,4FAAWA,KAAMC,WAAa,IAAM,YAEfD,EAAMC,WAAa,UAAY,UAE5DD,GACDA,EAAMC,YACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GASD,EAGGC,GAAmBJ,MAAGC,8DAM3B,mHAEKI,EAAoBC,KAAEL,gIAEZC,GAAWA,EAAMC,WAAa,SAAW,UAExCD,GAAWA,EAAMC,WAAa,SAAW,MAAO,EAG3DI,EAAsBC,IAACP,iEAEZC,yCAAWA,KAAMC,WAAa,SAAW,MAAO,EAG3DM,EAAsBC,UAAOT,iEASlC,sGAEKU,EAAoBC,MAAGX,+DAO5B,gIAEKY,GAAyBb,MAAGC,oEAIjC,2DAEKa,EAAqBC,SAAMd,gEAahC,uMAEKe,GAAoBD,SAAMd,+DAc/B,4OAEKgB,GAAeC,EAAOJ,CAAW,EAACb,iEAGvC,mCAKKkB,GAAkBA,CAAC,CACvBC,QACAC,aACAlB,aACAmB,OACAC,QAOF,IAAM,CACJ,MAAMC,EAAeA,IAAM,CACzBC,OAAOC,SAASC,QAAO,EAGzB,OAAIxB,EAECyB,SAAe,WAAU,GACxB,gBAACxB,GACC,WAACwB,SAAW,WAAU,GAAC,SAAoB,yBAC1CA,SAAa,WAAU,YAExB,oGACCnB,EACC,iBAAC,WAAQ,SAAiB,sBAC1BmB,MAACrB,EAAca,YAAMS,OAAQ,GAC5BT,EAAMU,OAAUF,SAAYR,WAAMU,MAAM,GAC3C,EACCF,UAAa,QAASJ,EAAc,SAAkB,uBACzD,EACF,UAKDzB,EACC,iBAACM,EAAYiB,YAAO,YAAYA,IAAS,uBAAuB,EAChEM,MAACrB,GACEe,SACGA,4CAA0CA,uBACxCC,EAAS,wBAA0B,MAErC,iDACN,UACCd,EACC,iBAAC,WAAQ,SAAiB,sBAC1BmB,MAACrB,EAAca,YAAMS,OAAQ,GAC5BT,EAAMU,OAAUF,SAAYR,WAAMU,MAAM,GAC3C,SACCjB,GACC,WAACe,SAAY,QAASP,EAAY,SAAS,cAC1CE,GAAUK,MAACZ,GAAW,SAASO,EAAQ,SAAiB,uBAC3D,CACF,GAEJ,EAUO,MAAMQ,WAAsBC,WAAkD,CACnFC,YAAY/B,EAA2B,CACrC,MAAMA,CAAK,EAsDbmB,oBAAaA,IAAY,CACvB,KAAKa,SAAS,CACZC,SAAU,GACVf,MAAO,KACR,IAzDD,KAAKgB,MAAQ,CACXD,SAAU,GACVf,MAAO,KAEX,CAEA,OAAOiB,yBAAyBjB,EAAkC,CAEzD,OACLe,SAAU,GACVf,QAEJ,CAEAkB,kBAAkBlB,EAAcmB,EAA4B,CAEpD,MAAEjB,QAAS,KAAKpB,MAChBsC,EAAelB,EAAO,iBAAiBA,KAAU,gBACvDmB,QAAQrB,MAAM,mBAAmBoB,KAAiBpB,EAAOmB,CAAS,EAG9D,KAAKrC,MAAMwC,SACRxC,WAAMwC,QAAQtB,EAAOmB,CAAS,CAWvC,CAEAI,mBAAmBC,EAAqC,CAGpD,KAAKR,MAAMD,UACX,KAAKjC,MAAM2C,oBACXD,EAAUE,WAAa,KAAK5C,MAAM4C,UAElC,KAAKzB,WAAW,CAEpB,CAEA0B,sBAA6B,CAEvB,KAAKX,MAAMD,UAAY,KAAKjC,MAAM8C,gBACpC,KAAK3B,WAAW,CAEpB,CASA4B,QAAoB,CACZ,MAAEd,WAAUf,SAAU,KAAKgB,MAC3B,CAAEU,WAAUI,WAAU5B,OAAM6B,oBAAmB5B,UAAW,KAAKrB,MAErE,OAAIiC,GAAYf,EAEV,OAAO8B,GAAa,WACfA,EAAS,CAAE9B,QAAOC,WAAY,KAAKA,WAAY,EAC7C6B,GAMTtB,MAACT,GACC,SACA,WAAY,KAAKE,WACjB,WAAY,CAAC8B,EACb,OACA,QACA,GAKCL,CACT,CACF,CCxRO,MAAMM,GAA4DA,CAAC,CACxEjD,aAAa,GACbgD,oBAAoB,GACpBL,WACA,GAAG5C,CACL,IAAM,CAEJ,MAAMmD,EAAelD,EAAa,MAAQgD,EAAoB,UAAY,YAGpEG,EAA4C,CAChDT,mBAAoBQ,IAAiB,MACrCL,eAAgBK,IAAiB,MACjCF,kBAAmBE,IAAiB,WAGtC,aAAQtB,GAAkBuB,MAAc,GAAIpD,EAAO,UAAsB,EAC3E,EAOaqD,GAEErD,GACL0B,UAAqB,WAAU,GAAK1B,IAAS,GCmD1CsD,EAAyB,CAEpCC,MAAO,UACPC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,WAAY,UACZC,YAAa,UAGbC,gBAAiB,UACjBC,oBAAqB,UACrBC,qBAAsB,UACtBC,gBAAiB,UACjBC,oBAAqB,UACrBC,qBAAsB,UACtBC,eAAgB,UAChBC,mBAAoB,UACpBC,oBAAqB,UACrBC,SAAU,UACVC,SAAU,UAGVC,MAAO,UACPC,MAAO,UACPC,OAAQ,UACRC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UAGTC,MAAO,UACPC,UAAW,UACXC,WAAY,UACZC,OAAQ,UACRC,WAAY,UACZC,YAAa,UACbC,OAAQ,UACRC,WAAY,UACZC,YAAa,UACbC,IAAK,UACLC,QAAS,UACTC,SAAU,UACVC,KAAM,UACNC,SAAU,UACVC,UAAW,UACXC,OAAQ,UACRC,WAAY,UACZC,YAAa,UAGbC,mBAAoB,2BACpBC,mBAAoB,oBACtB,EAGaC,EAA4B,CACvCC,WAAY,UACZC,QAAS,UACTC,eAAgB,UAChBC,OAAQ,UACRC,QAAS,2BACTC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,YAAa,UACbC,QAAS5D,EAAW8B,MACpB+B,QAAS7D,EAAWiC,OACpBrE,MAAOoC,EAAWuC,IAClBuB,KAAM9D,EAAW0C,KAGjBqB,UAAW,2BACXC,UAAWhE,EAAWC,MAGtBgE,OAAQjE,EAAW8B,MACnBoC,KAAMlE,EAAWuC,IACjB4B,QAASnE,EAAWwB,QAGpB4C,kBAAmB,wBACnBC,gBAAiB,uBACnB,EAiCaC,EAAU,CACrBC,IAAK,MACLC,GAAI,MACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,GAAI,OACJC,IAAK,MACP,EAGaC,EAAY,CACvBN,GAAI,UACJC,GAAI,WACJC,GAAI,OACJC,GAAI,WACJC,GAAI,UACJC,IAAK,SACLE,KAAM,SACNC,GAAI,SACJC,GAAI,OACJnI,GAAI,UACJoI,GAAI,SACJC,GAAI,UACJC,GAAI,MACN,EAGaC,EAAc,CACzBC,MAAO,IACPC,QAAS,IACTC,OAAQ,IACRC,SAAU,IACVC,KAAM,GACR,EAGaC,EAAc,CACzBC,MAAO,KACPC,OAAQ,IACRC,QAAS,IACX,EAGaC,EAAe,CAC1BC,KAAM,uIACNC,QACE,uIACFC,KAAM,sFACR,EAGaC,EAAc,CACzB3B,GAAI,QACJC,GAAI,QACJC,GAAI,QACJC,GAAI,SACJC,GAAI,QACN,EAGawB,EAAe,CAC1B5B,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,MACJC,GAAI,OACJyB,KAAM,SACNC,OAAQ,KACV,EAGaC,EAAU,CACrB9B,GAAI,+BACJC,GAAI,+BACJC,GAAI,gCACN,EAGa6B,EAAc,CACzBC,KAAM,OACNZ,OAAQ,OACRa,KAAM,MACR,EAGaC,EAAS,CACpBC,KAAM,EACNC,QAAS,GACTC,MAAO,GACPC,QAAS,GACTC,QAAS,GACTC,MAAO,GACT,stBC/RaC,EAAiB,CAC5BpJ,KAAM,iBACNqJ,OAAQ,CAENC,QAASpH,EAAWO,gBACpB8G,YAAarH,EAAWQ,oBACxB8G,aAActH,EAAWS,qBAGzB8G,UAAWvH,EAAWI,OACtBoH,cAAexH,EAAWK,WAC1BoH,eAAgBzH,EAAWM,YAG3BoH,OAAQ1H,EAAWU,gBACnBiH,WAAY3H,EAAWW,oBACvBiH,YAAa5H,EAAWY,qBAGxBgD,QAAS5D,EAAWO,gBACpBsD,QAAS7D,EAAWU,gBACpB9C,MAAOoC,EAAWC,MAClB4H,OAAQ7H,EAAWC,MACnB6D,KAAM9D,EAAWI,OAGjB+C,WAAYD,EAAeC,WAC3BC,QAASF,EAAeE,QACxB0E,SAAU9H,EAAW2B,QACrB0B,eAAgBH,EAAeE,QAC/BE,OAAQJ,EAAeI,OACvBC,QAAS,2BAGTC,YAAaN,EAAeM,YAC5BC,cAAeP,EAAeO,cAC9BC,aAAcR,EAAeQ,aAC7BC,YAAaT,EAAeS,YAG5BI,UAAWb,EAAea,UAC1BC,UAAWd,EAAec,UAC1B+D,UAAW/H,EAAWwB,QACtBwG,aAAc9E,EAAekB,kBAG7BH,OAAQjE,EAAWO,gBACnB2D,KAAMlE,EAAWC,MACjBkE,QAASnE,EAAWiB,SAGpBgH,UAAWjI,EAAWO,gBACtB2H,YAAalI,EAAW0B,QAGxB0C,kBAAmBlB,EAAekB,kBAClCC,gBAAiBnB,EAAemB,gBAChC8D,kBAAmBnI,EAAW4B,QAC9BwG,iBAAkB,qBAGlBC,cAAerI,EAAWO,gBAC1B+H,eAAgBtI,EAAWS,qBAC3B8H,eAAgBvI,EAAWa,eAC3B2H,kBAAmBxI,EAAWU,gBAC9B+H,gBAAiBzI,EAAW0B,QAG5BgH,qBAAsB1I,EAAWO,gBACjCoI,gBAAiB3I,EAAWI,OAC5BwI,mBAAoB5I,EAAWiB,SAC/B4H,gBAAiB7I,EAAWU,gBAC5BoI,iBAAkB9I,EAAWC,KAC/B,EACAqE,UACA6B,cACArB,YACAO,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,ECrFaoC,GAAyB,CACpCjL,KAAM,cACNqJ,OAAQ,CAENC,QAAS,UACTC,YAAa,UACbC,aAAc,UAGdC,UAAW,UACXC,cAAe,UACfC,eAAgB,UAGhBC,OAAQ,UACRC,WAAY,UACZC,YAAa,UAGbhE,QAAS,UACTC,QAAS,UACTjG,MAAO,UACPiK,OAAQ,UACR/D,KAAM,UAGNX,WAAY,UACZC,QAAS,UACTC,eAAgB,UAChByE,SAAU,UAGVxE,OAAQ,UACRC,QAAS,UAGTC,YAAa,UACbC,cAAe,UACfC,aAAc,UACdC,YAAa,UAGbI,UAAW,2BACXC,UAAW,UACX+D,UAAW,UACXC,aAAc,wBAGd/D,OAAQ,UACRC,KAAM,UACNC,QAAS,UAGT8D,UAAW,UACXC,YAAa,UAGb9D,kBAAmB,wBACnBC,gBAAiB,wBACjB8D,kBAAmB,UACnBC,iBAAkB,wBAGlBC,cAAe,UACfC,eAAgB,UAChBC,eAAgB,UAChBC,kBAAmB,UACnBC,gBAAiB,UAGjBC,qBAAsB,UACtBC,gBAAiB,UACjBC,mBAAoB,UACpBC,gBAAiB,UACjBC,iBAAkB,SACpB,EACAxE,UACA6B,cACArB,YACAO,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,ECrFaqC,GAAmB,CAC9BlL,KAAM,gBACNqJ,OAAQ,CAENC,QAASpH,EAAWiB,SACpBoG,YAAarH,EAAWyB,QACxB6F,aAActH,EAAWuB,QAGzBgG,UAAWvH,EAAWQ,oBACtBgH,cAAe,UACfC,eAAgBzH,EAAWO,gBAG3BmH,OAAQ1H,EAAWiB,SACnB0G,WAAY3H,EAAWyB,QACvBmG,YAAa5H,EAAWuB,QAGxBqC,QAAS5D,EAAWO,gBACpBsD,QAAS7D,EAAWiB,SACpBrD,MAAOoC,EAAWuC,IAClBsF,OAAQ7H,EAAWuC,IACnBuB,KAAM9D,EAAWQ,oBAGjB2C,WAAYnD,EAAW6B,QACvBuB,QAASpD,EAAW4B,QACpBkG,SAAU9H,EAAW2B,QACrB0B,eAAgBrD,EAAW4B,QAC3B0B,OAAQtD,EAAW2B,QACnB4B,QAAS,2BAGTC,YAAaxD,EAAWkB,MACxBuC,cAAezD,EAAWsB,QAC1BoC,aAAc1D,EAAWwB,QACzBmC,YAAa3D,EAAW6B,QAGxBkC,UAAWb,EAAea,UAC1BC,UAAWhE,EAAWI,OACtB2H,UAAW/H,EAAWwB,QACtBwG,aAAc9E,EAAekB,kBAG7BH,OAAQjE,EAAWO,gBACnB2D,KAAMlE,EAAWuC,IACjB4B,QAASnE,EAAWiB,SAGpBgH,UAAWjI,EAAWiB,SACtBiH,YAAalI,EAAW0B,QAGxB0C,kBAAmB,wBACnBC,gBAAiB,wBACjB8D,kBAAmBnI,EAAW6B,QAC9BuG,iBAAkB,qBAGlBC,cAAerI,EAAWO,gBAC1B+H,eAAgBtI,EAAWS,qBAC3B8H,eAAgBvI,EAAWiB,SAC3BuH,kBAAmBxI,EAAWuB,QAC9BkH,gBAAiBzI,EAAW0B,QAG5BgH,qBAAsB1I,EAAWO,gBACjCoI,gBAAiB3I,EAAWiB,SAC5B2H,mBAAoB5I,EAAWwB,QAC/BqH,gBAAiB7I,EAAWyB,QAC5BqH,iBAAkB9I,EAAWuC,GAC/B,EACA+B,UACA6B,cACArB,YACAO,cACAM,cACAI,eACAK,eACAG,UACAC,cACAG,QACF,ECnGasC,GAAeC,g3CAsCT,CAAC,CAAEC,OAAM,IAAMA,EAAMpD,aAAaC,KAC7B,CAAC,CAAEmD,OAAM,IAAMA,EAAMhC,OAAOhE,WACvC,CAAC,CAAEgG,OAAM,IAAMA,EAAMhC,OAAO3D,YAyB5B,CAAC,CAAE2F,OAAM,IAAMA,EAAMhC,OAAOC,QAqBvB,CAAC,CAAE+B,OAAM,IAAMA,EAAMhC,OAAOhE,WAI5B,CAAC,CAAEgG,OAAM,IAAMA,EAAMhC,OAAO7D,OAK5B,CAAC,CAAE6F,OAAM,IAAMA,EAAMhC,OAAOC,QAKrB,CAAC,CAAE+B,OAAM,IAAMA,EAAMhC,OAAOC,QAM7B,CAAC,CAAE+B,OAAM,IAAMA,EAAMhC,OAAOC,QACvC,CAAC,CAAE+B,OAAM,IAAMA,EAAMhC,OAAOxD,WAAW,EAIpDyF,GAAeH,GC7GTI,GAAgC,CACpC,iBAAkBnC,EAClB,cAAe6B,GACfO,KAAMN,EACR,EAGMO,EAAerC,EAGfsC,GAAoBC,GAEpBA,IAAiB,MAAQA,IAAiB,YAAcA,IAAiB,YACpE,iBAGLA,IAAiB,QACZ,cAEFA,EAIHC,EAAYC,GAA6B,CACvCC,QAAoBJ,GAAiBG,CAAS,EAC7CN,UAAOO,CAAiB,GAAKL,CACtC,EAGaM,EAAeC,gBAGzB,CACDX,MAAOI,EACPQ,SAAUA,IAAM,CAAC,CACnB,CAAC,EAGYC,GAAWA,IAAMC,aAAWJ,CAAY,EAkBxCK,GAAgBA,CAAC,CAC5BC,eAAeZ,EACfa,eAAe,GACfC,aAAa,uBACb/K,UACkB,IAAM,CAIxB,KAAM,CAAC6J,EAAOmB,CAAa,EAAIC,WAAgB,IAAM,CAE/CH,MAAgB,OAAOnM,OAAW,IAAa,CACjD,MAAMuM,EAAcvM,OAAOwM,aAAaC,QAAQL,CAAU,EAE1D,GAAIG,EACE,IAEIG,QAAcjB,EAASc,CAAW,EACxC,OAAIG,GAKgBC,KAAKC,MAAML,CAAW,QAEnC5M,GACCA,cAAM,gCAAiCA,CAAK,CACtD,EAMGkN,OADe,OAAOX,GAAiB,SAAWT,EAASS,CAAY,EAAIA,CAC3EW,CACR,EAGDC,YAAU,IAAM,CACV,OAAOC,SAAa,KACtBA,SAASC,gBAAgBC,aAAa,aAAc/B,EAAMrL,IAAI,CAChE,EACC,CAACqL,EAAMrL,IAAI,CAAC,EAGTiM,QAAYoB,GAA6B,CAC7C,MAAMC,EAAc,OAAOD,GAAa,SAAWzB,EAASyB,CAAQ,EAAIA,EACxEb,EAAcc,CAAW,EAGrBhB,GAAgB,OAAOnM,OAAW,KAC7BwM,oBAAaY,QAAQhB,EAAYe,EAAYtN,MAAQ8M,KAAKU,UAAUF,CAAW,CAAC,CACzF,EAIIG,EAAwDA,CAAC,CAAEjM,cAE7DkM,OAACC,IAAoB,QACnB,gBAACxC,GAAY,IACZ3J,CACH,IAKJ,OACGlB,QAAa,SAAb,CAAsB,MAAO,CAAE+K,QAAOY,YACrC,eAACwB,EAAcjM,YAAS,CAC1B,EAEJ,EChHMoM,GAAuBlP,MAAGC,sbAYjB,CAAC,CAAEkP,YAAW,IAAOA,EAAa,gBAAkB,+BAAgC,EAW7FC,GAAwBpP,MAAGC,mEAOhC,qLAEKoP,GAAe/O,KAAEL,0DAMtB,sHAEKqP,GAAqBvO,SAAMd,gEAkBhC,4QAEKsP,EAAsBvP,MAAGC,iEAM9B,sEAEKuP,EAAsBC,QAAKxP,iEAShC,8LAEKyP,EAAyBC,QAAK1P,oEAUnC,mJAEK2P,EAA4B5P,MAAGC,uEAKpC,gHAEK4P,GAAsB9O,SAAMd,iEAoCjC,6iBAKY6P,GAA8DA,CAAC,CAC1EC,YAAY,GACZC,sBACAC,WACF,IAAM,CACJ,KAAM,CAACC,EAAWC,CAAY,EAAIpC,WAASgC,CAAS,EAC9C,CAACK,EAAaC,CAAc,EAAItC,WAAmC,CACvEuC,cAAe,GACfC,mBAAoB,GACpBC,iBAAkB,GACnB,EAGDjC,YAAU,IAAM,CACRkC,QAAaxC,aAAaC,QAAQ,2BAA2B,EACnE,GAAIuC,EACE,IACIC,QAAStC,KAAKC,MAAMoC,CAAU,EACpCJ,EAAeK,CAAM,EACrBC,EAAiBD,CAAM,QAChBtP,GACCwP,aAAK,6CAA8CxP,CAAK,CAClE,CAKEyP,GADyBpP,OAAOqP,WAAW,kCAAkC,EAAEC,SACvDX,EAAYI,iBAAkB,CACxD,MAAMQ,EAAe,CAAE,GAAGZ,EAAaE,cAAe,IACtDD,EAAeW,CAAY,EAC3BL,EAAiBK,CAAY,EAEjC,EAAG,CAAE,GAGCL,QAAoBM,GAAoC,CAC5D,MAAMC,EAAO1C,SAASC,gBAGtByC,EAAKxC,aAAa,sBAAuBuC,EAAMX,cAAca,UAAU,EAGnEF,EAAMV,mBACH7B,eAAa,0BAA2B,iBAAiB,EAE9DwC,EAAKE,gBAAgB,yBAAyB,CAChD,EAIIC,EAAyBA,CAACC,EAAqCC,IAAmB,CACtF,MAAMC,EAAiB,CAAE,GAAGpB,EAAa,CAACkB,CAAG,EAAGC,GAChDlB,EAAemB,CAAc,EAG7BvD,aAAaY,QAAQ,4BAA6BT,KAAKU,UAAU0C,CAAc,CAAC,EAGhFb,EAAiBa,CAAc,EAG/BxB,WAAsBwB,EAAc,EAGtC,OAEIxC,mCAACa,GACC,WAAWK,EACX,QAAS,IAAMC,EAAa,CAACD,CAAS,EACtC,aAAW,gCACX,gBAAeA,EACf,gBAAc,sBAAqB,SAGrC,MAEAlB,OAACE,GACC,YAAYgB,EACZ,YACA,GAAG,sBACH,KAAK,SACL,kBAAgB,sBAEhB,iBAACd,GACC,WAACxN,UAAM,GAAG,sBAAsB,SAAsB,2BACtDA,MAAC0N,IACC,QAAS,IAAMa,EAAa,EAAK,EACjC,aAAW,+BAA8B,SAG3C,OACF,SAECZ,EACC,kBAACC,EACC,iBAACE,EACC,MAAK,WACL,QAASU,EAAYE,cACrB,SAAiBe,KAAuB,gBAAiBI,EAAEC,OAAOC,OAAO,EACzE,mBAAiB,qBAAoB,uBAGzC,EACC/P,SAAmB,GAAG,qBAAoB,SAE3C,qEACF,SAEC2N,EACC,kBAACC,EACC,iBAACE,EACC,MAAK,WACL,QAASU,EAAYG,mBACrB,SAAiBc,KAAuB,qBAAsBI,EAAEC,OAAOC,OAAO,EAC9E,mBAAiB,0BAAyB,0BAG9C,EACC/P,SAAmB,GAAG,0BAAyB,SAEhD,8EACF,SAEC2N,EACC,kBAACC,EACC,iBAACE,EACC,MAAK,WACL,QAASU,EAAYI,iBACrB,SAAiBa,KAAuB,mBAAoBI,EAAEC,OAAOC,OAAO,EAC5E,mBAAiB,0BAAyB,mCAG9C,EACC/P,SAAmB,GAAG,0BAAyB,SAEhD,iEACF,GACF,CACF,GAEJ,EC1SMgQ,GAAyBC,SAAM5R,gPAKtB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQI,GACtB,CAAC,CAAEyE,OAAM,IAAMA,EAAMhC,OAAO/D,QAEvC,CAAC,CAAE+F,OAAM,IAAMA,EAAMhC,OAAO3D,WAAW,EAI5C8K,GAAqB9R,MAAGC,gEAG7B,wCAGK8R,GAAoBhR,SAAMd,iPAQrB,CAAC,CAAE0M,OAAM,IAAMA,EAAMhC,OAAO3D,YAEjB,CAAC,CAAE2F,OAAM,IAAMA,EAAM3C,YAAYC,KAG1C,CAAC,CAAE0C,OAAM,IAAMA,EAAMhC,OAAOC,OAAO,EAK1CoH,GAAchS,MAAGC,ySAGN,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQG,GAC/B,CAAC,CAAE0E,OAAM,IAAMA,EAAMrE,UAAUF,GAEnC,CAAC,CAAEuE,OAAM,IAAMA,EAAMhC,OAAOC,QAOnB,CAAC,CAAE+B,OAAM,IAAMA,EAAM7E,QAAQE,EAAE,EAM7CiK,GAAqBjS,MAAGC,sLAIb,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQI,GAEvB,CAAC,CAAEyE,OAAM,IAAMA,EAAMhD,YAAY1B,EAAE,EAMpDiK,GAAsBlS,MAAGC,4KAChB,CAAC,CAAE0M,OAAM,IAAMA,EAAMrE,UAAUL,EAAE,EAQ1CkK,GAAqBnS,MAAGC,0KACf,CAAC,CAAE0M,OAAM,IAAMA,EAAMrE,UAAUN,GACnC,CAAC,CAAE2E,OAAM,IAAMA,EAAMhC,OAAOC,OAAO,EAQxCwH,GAAsBpS,MAAGC,gHAGtB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQI,EAAE,EAIlCmK,GAAkBrS,MAAGC,qOAId,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQE,GAAM,CAAC,CAAE2E,OAAM,IAAMA,EAAM7E,QAAQG,GAC1D,CAAC,CAAE0E,OAAM,IAAMA,EAAM/C,aAAa3B,GACpB,CAAC,CAAE0E,OAAM,IAAMA,EAAM3C,YAAYC,IAAI,EAQhEqI,GAAgBtS,MAAGC,oNAIH,CAAC,CAAE0M,OAAM,IAAMA,EAAMhC,OAAOC,OAAO,EAgBnD2H,GAAgCA,CAAC,CAAEC,gBAAeC,aAAY,WAE/Db,GACC,kBAACE,GACC,iBAACC,GAAW,SAASS,EAAe,aAAW,iBAC5CC,WAAe7Q,cAAK,SAAC,MAAWA,cAAK,YAAC,GACzC,EACAA,MAACoQ,IAAK,SAAO,mBACZC,GACC,iBAACC,IAAa,SAAc,mBAC5BtQ,MAACuQ,IAAY,SAAc,oBAC7B,GACF,EAEAvQ,MAACwQ,IACC,SAACxQ,UACC,eAAC0Q,GAAO,eAAE,EACZ,CACF,EACF,ICjJEI,GAA0BC,QAAK1S,kOAEf,CAAC,CAAE0M,OAAM,IAAMA,EAAMhC,OAAO/D,QAK5B,CAAC,CAAE+F,OAAM,IAAMA,EAAM3C,YAAYX,MAAM,EAIvDuJ,GAAuB5S,MAAGC,qMAIX,CAAC,CAAE4S,QAAO,IAAOA,EAAS,aAAe,SAC/C,CAAC,CAAElG,QAAOkG,QAAO,IAAOA,EAASlG,EAAM7E,QAAQI,GAAK,GAAI,EAKjE8J,GAAchS,MAAGC,+KACR,CAAC,CAAE0M,OAAM,IAAMA,EAAMrE,UAAUH,GAEnC,CAAC,CAAEwE,OAAM,IAAMA,EAAMhC,OAAOC,QAG1B,CAAC,CAAEiI,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAElG,OAAM,IAAMA,EAAM3C,YAAYX,MAAM,EAIzDyJ,GAAsBC,MAAG9S,6GAGlB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQI,EAAE,EAItC8K,GAAU9R,EAAO+R,CAAO,EAAChT,mXAGlB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQG,GACpC,CAAC,CAAE0E,QAAOuG,SAAQ,IAAOA,EAAUvG,EAAM7E,QAAQI,GAAK,IACvC,CAAC,CAAEgL,SAAQ,IAAOA,EAAU,aAAe,SAG/B,CAAC,CAAEvG,OAAM,IAAMA,EAAM3C,YAAYC,KACtD,CAAC,CAAE0C,OAAM,IAAMA,EAAM3C,YAAYC,KAIhC,CAAC,CAAE0C,OAAM,IAAMA,EAAMhC,OAAO3D,YAI5B,CAAC,CAAE2F,OAAM,IAAMA,EAAMhC,OAAOC,QAEZ,CAAC,CAAE+B,OAAM,IAAMA,EAAMhC,OAAOC,OAAO,EAK1DuI,GAAcnT,MAAGC,+JAML,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQG,EAAE,EAI3CmL,GAAeC,OAAIpT,qJAEZ,CAAC,CAAE4S,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAElG,OAAM,IAAMA,EAAM3C,YAAYX,OAE1C,CAAC,CAAEwJ,QAAO,IAAOA,EAAS,QAAU,GAAI,EAIjDS,GAAgBtT,MAAGC,2MACZ,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQG,GAAM,CAAC,CAAE0E,OAAM,IAAMA,EAAM7E,QAAQI,GACnD,CAAC,CAAEyE,OAAM,IAAMA,EAAMhC,OAAO7D,OACvC,CAAC,CAAE6F,OAAM,IAAMA,EAAMrE,UAAUL,GACnC,CAAC,CAAE0E,OAAM,IAAMA,EAAMhC,OAAO1D,cAE1B,CAAC,CAAE4L,QAAO,IAAOA,EAAS,EAAI,EACnB,CAAC,CAAElG,OAAM,IAAMA,EAAM3C,YAAYX,MAAM,EAWzDkK,GAAkCA,CAAC,CAAEV,QAAO,IAAM,CACtD,MAAMnR,EAAW8R,IAEXC,EAAW,CACf,CAAEC,KAAM,IAAKjE,MAAO,YAAakE,KAAM,MACvC,CAAED,KAAM,eAAgBjE,MAAO,cAAekE,KAAM,MACpD,CAAED,KAAM,WAAYjE,MAAO,gBAAiBkE,KAAM,MAClD,CAAED,KAAM,YAAajE,MAAO,WAAYkE,KAAM,MAC9C,CAAED,KAAM,YAAajE,MAAO,WAAYkE,KAAM,KAAM,EAIpD,cAACjB,IAAiB,SAChB,gBAACE,IAAc,SACb,eAACZ,GAAK,UAAgB,gBAAI,CAC5B,SAECc,GACEW,YAASG,IACRC,UAACb,IAEC,GAAIa,EAAKH,KACT,QAASb,EACT,UAAWnR,EAASoS,WAAaD,EAAKH,KAAO,SAAW,GAExD,UAAC9R,UAAMiS,WAAKF,IAAK,GAChB/R,UAAM,SAAiBiS,WAAKpE,MAAM,CAN9BoE,KAAKH,IAOZ,CACD,EACH,EAEA9R,MAAC0R,GAAO,UAAgB,SAAM,UAChC,GAEJ,ECnIMS,GAAyB/T,MAAGC,sJAKZ,CAAC,CAAE0M,OAAM,IAAMA,EAAMhC,OAAOhE,UAAU,EAItD+L,GAA0B1S,MAAGC,kNACxB,CAAC,CAAE4S,QAAO,IAAOA,EAAS,QAAU,OAEzB,CAAC,CAAElG,OAAM,IAAMA,EAAM3C,YAAYX,OAGhC,CAAC,CAAEsD,OAAM,IAAMA,EAAMhD,YAAYzB,GAEzC,CAAC,CAAEyE,OAAM,IAAMA,EAAMxC,OAAOM,MAC9B,CAAC,CAAEoI,QAAO,IAAOA,EAAS,QAAU,IAC/B,CAAC,CAAEA,SAAQlG,OAAM,IAAOkG,EAASlG,EAAM5C,QAAQ5B,GAAK,MAAO,EAKvE6L,GAA0BhU,MAAGC,mNAKP,CAAC,CAAE0M,OAAM,IAAMA,EAAM3C,YAAYX,OAEtC,CAAC,CAAEsD,OAAM,IAAMA,EAAMhD,YAAYzB,EAAE,EAOpD0J,GAAyB5R,MAAGC,qEAGjC,kCAGKgU,GAAqBC,OAAIjU,6IAGlB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQK,GAEnB,CAAC,CAAEwE,OAAM,IAAMA,EAAMhD,YAAY1B,GACzC,CAAC,CAAE0E,OAAM,IAAMA,EAAM7E,QAAQI,EAAE,EAKxCiM,GAAiBnU,MAAGC,oQAOb,CAAC,CAAE0M,OAAM,IAAMA,EAAMxC,OAAOG,MAAQ,EACpC,CAAC,CAAEyF,WAAU,IAAOA,EAAY,EAAI,EACjC,CAAC,CAAEA,WAAU,IAAOA,EAAY,UAAY,SACpC,CAAC,CAAEpD,OAAM,IAAMA,EAAM3C,YAAYX,OACxC,CAAC,CAAEsD,OAAM,IAAMA,EAAM3C,YAAYX,OAE3B,CAAC,CAAEsD,OAAM,IAAMA,EAAMhD,YAAYzB,EAAE,EAQpDkM,GAAuBA,IAAM,CACjC,KAAM,CAAC3B,EAAa4B,CAAc,EAAItG,WAAS,EAAI,EAG7CyE,EAAgBA,IAAM,CAC1B6B,EAAe,CAAC5B,CAAW,GAIvB6B,EAAeA,IAAM,CACzBD,EAAe,EAAK,GAGtB,cACGN,GAEC,iBAACrB,IAAiB,OAAQD,EACxB,eAACc,GAAQ,QAAQd,EAAY,CAC/B,GAGC7Q,UAAQ,UAAW6Q,EAAa,QAAS6B,EAAa,EAGvDtF,OAACgF,IAAiB,cAChB,gBAACpC,GACC,gBAACW,GAAO,iBAA8B,aAAyB,GACjE,EAEC3Q,UACC,SAACA,SAAM,GACT,GACF,EAGAA,MAACkO,GACC,qBAA8BmB,IACpBsD,YAAI,qCAAsCtD,CAAK,GACvD,CAEN,GAEJ,EC3HMuD,GAA0BxU,MAAGC,kLAMtB,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQK,EAAE,EAGtCsM,GAAiBzU,MAAGC,yPAKJ,CAAC,CAAE0M,OAAM,IAAMA,EAAMhC,OAAOC,OAAO,EAUnD8J,GAAqB1U,MAAGC,+GACd,CAAC,CAAE0M,OAAM,IAAMA,EAAM7E,QAAQI,GAClC,CAAC,CAAEyE,OAAM,IAAMA,EAAMhC,OAAO1D,cACxB,CAAC,CAAE0F,OAAM,IAAMA,EAAMrE,UAAUJ,EAAE,EAM1CyM,GAA0BA,WAE3BH,GACC,iBAACC,GAAO,IACR7S,MAAC8S,IAAY,SAAU,cACzB,ICnCEE,GAAYC,OAAK,IAAMC,aAAO,gCAAgD,wRAAC,EAC/EC,GAAaF,OAAK,UAAM,OAAO,0BAA+C,8LAAC,EAC/EG,GAAeH,OAAK,IAAMC,aAAO,4BAAwC,yQAAC,EAC1EG,GAAgBJ,OAAK,IAAMC,aAAO,6BAA0C,iMAAC,EAC7EI,EAAYL,OAAK,IAAMC,aAAO,yBAAqC,8SAAC,EACpEK,GAAWN,OAAK,IAAMC,aAAO,wBAA+B,4JAAC,EAC7DM,GAAWP,OAAK,IAAMC,aAAO,wBAAwB,EAAC,4JAOtDO,GAAsBA,UAEvBC,WAAS,gBAAWX,GAAa,IAChC,gBAACY,GAEC,kBAACC,GAAM,KAAK,IAAI,QAAS5T,MAACwS,OACxB,gBAACoB,GAAM,MAAK,GAAC,QAAS5T,MAACgT,IAAS,GAAI,QACnCY,EAAM,MAAK,cAAc,QAAS5T,MAACmT,OAAc,QACjDS,EAAM,MAAK,UAAU,QAAS5T,MAACoT,OAAgB,QAC/CQ,EAAM,MAAK,WAAW,QAAS5T,MAACqT,OAAiB,QACjDO,EAAM,MAAK,YAAY,QAAS5T,MAACsT,MAAa,QAC9CM,EAAM,MAAK,YAAY,QAAS5T,MAACsT,MAAa,QAC9CM,EAAM,MAAK,WAAW,QAAS5T,MAACuT,OAAY,QAC5CK,EAAM,MAAK,IAAI,QAAS5T,MAACwT,OAAY,GACxC,EAGAxT,MAAC4T,EAAM,MAAK,aAAa,QAAU5T,UAAS,GAAG,IAAI,QAAO,IAAI,GAChE,EACF,GCvBS2B,GAAoDA,CAAC,CAAET,UAAS,IAAM,CAC3E2S,QAAerU,GAAiB,CAE5BA,cAAM,qBAAsBA,CAAK,GAW3C,aACGsU,GAAwB,SAASD,EAAa,KAAK,cACjD3S,UACH,EAEJ,EC7BM6S,GAAmB3V,MAAGC,8DAW3B,kNAEK2V,GAAqB7U,SAAMd,4UACjB,CAAC,CAAE4V,UAAS,IACxBA,EAAW,uBAAyB,yBAC7B,CAAC,CAAEA,UAAS,IACnBA,EAAW,8BAAgC,+BAA+B,EAgBxEC,GAAoB9V,MAAGC,+DAQ5B,+MAEK8V,GAAoB/V,MAAGC,+DAK5B,2FAEK+V,GAAyBhW,MAAGC,oEAGjC,2DAEKgW,EAAqBjW,MAAGC,mPAEd,CAAC,CAAEiW,QAAO,IAAM,CAC5B,OAAQA,EAAM,CACZ,IAAK,UAAkB,6BACvB,IAAK,UAAkB,6BACvB,IAAK,QAAgB,2BACrB,IAAK,OAAe,0BACpB,QAAgB,kCAClB,CACF,CAAC,EAUG5G,GAAqBvO,SAAMd,gEAchC,iMAMYkW,GAAgDA,CAAC,CAAEC,SAAQ,IAAM,CACtE,MAAEzJ,QAAOY,YAAaC,GAAS,EAE/BX,EAAS,CACb,CAAEwJ,GAAI,iBAAkB/U,KAAM,kBAC9B,CAAE+U,GAAI,cAAe/U,KAAM,eAC3B,CAAE+U,GAAI,OAAQ/U,KAAM,OAAQ,EAG9B,cACGqU,GACC,WAAC/T,UAAY,QAASwU,EAAS,SAAC,MAEhCxU,MAAC,MAAG,MAAO,CAAE0U,MAAO,8BAA+BC,OAAQ,cAAe,SAE1E,qBAEAvH,OAAC,OAAI,MAAO,CAAEwH,aAAc,MAC1B,mBAAC,OAAI,MAAO,CAAEF,MAAO,gCAAiCG,SAAU,OAAQD,aAAc,KAAQ,wBAClF7J,EAAMrL,MAClB,EACCuL,EAAO+G,IACN8C,SAACd,IAEC,SAAUjJ,EAAMrL,OAASoV,EAAEL,GAC3B,QAAS,IAAM9I,EAASmJ,EAAEL,EAAE,EAE3BK,WAAEpV,IAJEoV,IAAEL,EAKT,CACD,GACH,SAECP,GACC,iBAACC,IAAW,SAAmB,wBAC/BnU,MAACoU,IAAgB,SAAqC,0CACtDhH,OAAC,OAAI,MAAO,CAAE2H,UAAW,KACvB,YAAC/U,SAAY,OAAO,UAAU,SAAO,YACpCA,SAAY,OAAO,UAAU,SAAO,YACpCA,SAAY,OAAO,QAAQ,SAAK,UAChCA,SAAY,OAAO,OAAO,SAAI,UACjC,GACF,EAEAoN,OAAC,OAAI,MAAO,CACVsH,MAAO,gCACPG,SAAU,OACVE,UAAW,OACXC,WAAY,KACZ,oDAEC,KAAK,oDACL,KAAK,mCACL,KAAK,iCACL,KAAK,4BACR,CACF,GAEJ,ECpJA,SAASC,IAAM,CACb,KAAM,CAACC,EAAeC,CAAgB,EAAIhJ,WAAS,EAAK,EAGlDiJ,EAAiBvF,GAAqB,CACtCA,EAAEwF,SAAWxF,EAAEH,MAAQ,MACzBG,EAAEyF,eAAe,EACjBH,EAAiB,CAACD,CAAa,EACjC,EAIFvI,mBAAU,KACC4I,0BAAiB,UAAWH,CAAa,EAC3C,IAAMxI,SAAS4I,oBAAoB,UAAWJ,CAAa,GACjE,CAACF,CAAa,CAAC,QAGfvT,GACC,gBAACmK,IAAc,aAAa,iBAC1B,gBAAC2J,GACC,iBAAChC,GAAS,IACTyB,GAAkBlV,UAAe,QAAS,IAAMmV,EAAiB,EAAK,EAAK,GAC9E,EACF,CACF,EAEJ,CClCA,MAAMO,GAAmBC,GAAgC,CACnDA,GAAeA,aAAuBC,UACjC1C,uCAAY,MAAE2C,KAAK,CAAC,CAAEC,SAAQC,SAAQC,SAAQC,SAAQC,aAAc,CACzEJ,EAAOH,CAAW,EAClBI,EAAOJ,CAAW,EAClBK,EAAOL,CAAW,EAClBM,EAAON,CAAW,EAClBO,EAAQP,CAAW,EACpB,CAEL,ECHA,MAAMrD,GAAOA,IAAM,CAOjB,GANAzR,QAAQ8R,IAAI,wCAAwC,EAMhD,CAHgB/F,SAASuJ,eAAe,MAAM,EAGhC,CAChBtV,QAAQrB,MAAM,qDAAqD,EAC7D4W,QAAexJ,SAASyJ,cAAc,KAAK,EACjDD,EAAa3B,GAAK,OACT7M,cAAK0O,YAAYF,CAAY,EAI3BG,EAASC,WAAW5J,SAASuJ,eAAe,MAAM,CAAgB,EAG1E9U,aACFoV,EAAM,WAAN,CACC,SAACzW,WAAG,EACN,CACF,EAGgB0V,IAClB,EAGA7V,OAAO0V,iBAAiB,QAAoBmB,IAC1C7V,QAAQrB,MAAM,SAAUkX,EAAMlX,OAASkX,EAAMzW,OAAO,CACtD,CAAC,EAGDqS,GAAK", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "props", "isAppLevel", "ErrorCard", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h3", "ErrorMessage", "p", "ErrorDetails", "details", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pre", "ButtonContainer", "RetryButton", "button", "Ski<PERSON><PERSON><PERSON>on", "ReloadButton", "styled", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error", "resetError", "name", "onSkip", "handleReload", "window", "location", "reload", "jsx", "message", "stack", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "setState", "<PERSON><PERSON><PERSON><PERSON>", "state", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "boundaryName", "console", "onError", "componentDidUpdate", "prevProps", "resetOnPropsChange", "children", "componentWillUnmount", "resetOnUnmount", "render", "fallback", "isFeatureBoundary", "UnifiedErrorBoundary", "boundaryType", "defaultProps", "AppError<PERSON>ou<PERSON>ry", "baseColors", "f1Red", "f1RedDark", "f1RedLight", "f1Blue", "f1BlueDark", "f1BlueLight", "f1MercedesGreen", "f1MercedesGreenDark", "f1MercedesGreenLight", "f1McLarenOrange", "f1McLarenOrangeDark", "f1McLarenOrangeLight", "f1RacingYellow", "f1RacingYellowDark", "f1RacingYellowLight", "f1Carbon", "f1Silver", "white", "black", "gray50", "gray100", "gray200", "gray300", "gray400", "gray500", "gray600", "gray700", "gray800", "gray900", "green", "greenDark", "greenLight", "yellow", "yellowDark", "yellowLight", "orange", "orangeDark", "orangeLight", "red", "redDark", "redLight", "blue", "blueDark", "blueLight", "purple", "purpleDark", "purpleLight", "whiteTransparent10", "blackTransparent10", "darkModeColors", "background", "surface", "cardBackground", "border", "divider", "textPrimary", "textSecondary", "textDisabled", "textInverse", "success", "warning", "info", "chartGrid", "chartLine", "profit", "loss", "neutral", "tooltipBackground", "modalBackground", "spacing", "xxs", "xs", "sm", "md", "lg", "xl", "xxl", "fontSizes", "xxxl", "h1", "h2", "h4", "h5", "h6", "fontWeights", "light", "regular", "medium", "semibold", "bold", "lineHeights", "tight", "normal", "relaxed", "fontFamilies", "body", "heading", "mono", "breakpoints", "borderRadius", "pill", "circle", "shadows", "transitions", "fast", "slow", "zIndex", "base", "overlay", "modal", "popover", "tooltip", "fixed", "f1Theme", "colors", "primary", "primaryDark", "primaryLight", "secondary", "secondaryDark", "secondaryLight", "accent", "accentDark", "accentLight", "danger", "elevated", "chartAxis", "chartTooltip", "tabActive", "tabInactive", "sidebarBackground", "headerBackground", "sessionActive", "sessionOptimal", "sessionCaution", "sessionTransition", "sessionInactive", "performanceExcellent", "performanceGood", "performanceAverage", "performancePoor", "performanceAvoid", "f1OfficialTheme", "darkTheme", "GlobalStyles", "createGlobalStyle", "theme", "GlobalStyles$1", "themes", "dark", "defaultTheme", "migrateThemeName", "oldThemeName", "getTheme", "themeName", "migratedThemeName", "ThemeContext", "createContext", "setTheme", "useTheme", "useContext", "ThemeProvider", "initialTheme", "persistTheme", "storageKey", "setThemeState", "useState", "storedTheme", "localStorage", "getItem", "themeByName", "JSON", "parse", "resolvedTheme", "useEffect", "document", "documentElement", "setAttribute", "newTheme", "themeObject", "setItem", "stringify", "ThemeWrapper", "jsxs", "StyledThemeProvider", "ControlsPanel", "$isVisible", "ControlsHeader", "Title", "CloseButton", "ControlGroup", "ControlLabel", "label", "ControlCheckbox", "input", "ControlDescription", "ToggleButton", "AccessibilityControls", "isVisible", "onPreferencesChange", "className", "showPanel", "setShowPanel", "preferences", "setPreferences", "motionEnabled", "lowStimulationMode", "autoDetectMotion", "savedPrefs", "parsed", "applyPreferences", "warn", "prefersReducedMotion", "matchMedia", "matches", "updatedPrefs", "prefs", "root", "toString", "removeAttribute", "handlePreferenceChange", "key", "value", "newPreferences", "e", "target", "checked", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "header", "LeftSection", "MenuButton", "Logo", "SessionInfo", "SessionTitle", "SessionYear", "RightSection", "UserMenu", "Avatar", "Header", "toggleSidebar", "sidebarOpen", "SidebarContainer", "aside", "LogoContainer", "isOpen", "NavContainer", "nav", "NavItem", "NavLink", "$isOpen", "Icon", "Label", "span", "Footer", "Sidebar", "useLocation", "navItems", "path", "icon", "map", "item", "pathname", "LayoutContainer", "ContentContainer", "MainContent", "main", "Overlay", "MainLayout", "setSidebarOpen", "closeSidebar", "log", "LoadingContainer", "Spinner", "LoadingText", "LoadingScreen", "Dashboard", "lazy", "__vitePreload", "DailyGuide", "TradeJournal", "TradeAnalysis", "TradeForm", "Settings", "NotFound", "AppRoutes", "Suspense", "Routes", "Route", "handleError", "UnifiedAppErrorBoundary", "TestPanel", "ThemeButton", "isActive", "SampleCard", "SampleText", "SampleSecondary", "StatusBadge", "status", "ThemeTestPanel", "onClose", "id", "color", "margin", "marginBottom", "fontSize", "t", "marginTop", "lineHeight", "App", "showThemeTest", "setShowThemeTest", "handleKeyDown", "ctrl<PERSON>ey", "preventDefault", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "getElementById", "fallbackRoot", "createElement", "append<PERSON><PERSON><PERSON>", "ReactDOM", "createRoot", "React", "event"], "sources": ["../../../shared/src/components/molecules/ErrorBoundary.tsx", "../../../shared/src/components/molecules/UnifiedErrorBoundary.tsx", "../../../shared/src/theme/tokens.ts", "../../../shared/src/theme/f1Theme.ts", "../../../shared/src/theme/f1OfficialTheme.ts", "../../../shared/src/theme/darkTheme.ts", "../../../shared/src/theme/GlobalStyles.tsx", "../../../shared/src/theme/ThemeProvider.tsx", "../../src/components/accessibility/AccessibilityControls.tsx", "../../src/layouts/Header.tsx", "../../src/layouts/Sidebar.tsx", "../../src/layouts/MainLayout.tsx", "../../src/components/molecules/LoadingScreen.tsx", "../../src/routes/routes.tsx", "../../src/components/AppErrorBoundary.tsx", "../../src/components/ThemeTestPanel.tsx", "../../src/App.tsx", "../../src/reportWebVitals.ts", "../../src/index.tsx"], "sourcesContent": ["/**\n * Error Boundary Component\n *\n * A React error boundary component that catches errors in its child component tree\n * and displays a fallback UI instead of crashing the entire application.\n *\n * This is a unified error boundary that can be used at any level of the application.\n */\nimport { Component, ErrorInfo, ReactNode } from 'react';\nimport styled from 'styled-components';\n\n// Error boundary props\nexport interface ErrorBoundaryProps {\n  /** The children to render */\n  children: ReactNode;\n  /** Custom fallback component to render when an error occurs */\n  fallback?: ReactNode | ((props: { error: Error; resetError: () => void }) => ReactNode);\n  /** Function to call when an error occurs */\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  /** Whether to reset the error boundary when the children prop changes */\n  resetOnPropsChange?: boolean;\n  /** Whether to reset the error boundary when the component unmounts */\n  resetOnUnmount?: boolean;\n  /** Name of the boundary for identification in logs */\n  name?: string;\n  /** Whether this is a feature-level boundary */\n  isFeatureBoundary?: boolean;\n  /** Function to call when the user chooses to skip a feature (only for feature boundaries) */\n  onSkip?: () => void;\n}\n\n// Error boundary state\ninterface ErrorBoundaryState {\n  /** Whether an error has occurred */\n  hasError: boolean;\n  /** The error that occurred */\n  error: Error | null;\n}\n\n// Styled components\nconst ErrorContainer = styled.div<{ isAppLevel?: boolean }>`\n  padding: 1.5rem;\n  margin: ${(props) => (props.isAppLevel ? '0' : '1rem 0')};\n  border-radius: 0.5rem;\n  background-color: ${(props) => (props.isAppLevel ? '#1a1f2c' : '#f44336')};\n  color: #ffffff;\n  ${(props) =>\n    props.isAppLevel &&\n    `\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    min-height: 100vh;\n    width: 100%;\n    max-width: 600px;\n    margin: 0 auto;\n  `}\n`;\n\nconst ErrorCard = styled.div`\n  background-color: #252a37;\n  border-radius: 0.5rem;\n  padding: 2rem;\n  width: 100%;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n`;\n\nconst ErrorTitle = styled.h3<{ isAppLevel?: boolean }>`\n  margin-top: 0;\n  font-size: ${(props) => (props.isAppLevel ? '1.5rem' : '1.25rem')};\n  font-weight: 700;\n  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};\n`;\n\nconst ErrorMessage = styled.p<{ isAppLevel?: boolean }>`\n  margin-bottom: 1rem;\n  text-align: ${(props) => (props.isAppLevel ? 'center' : 'left')};\n`;\n\nconst ErrorDetails = styled.details`\n  margin-bottom: 1rem;\n\n  summary {\n    cursor: pointer;\n    color: #2196f3;\n    font-weight: 500;\n    margin-bottom: 0.5rem;\n  }\n`;\n\nconst ErrorStack = styled.pre`\n  font-size: 0.875rem;\n  background-color: rgba(0, 0, 0, 0.1);\n  padding: 0.5rem;\n  border-radius: 0.25rem;\n  overflow: auto;\n  max-height: 200px;\n`;\n\nconst ButtonContainer = styled.div`\n  display: flex;\n  gap: 0.5rem;\n  justify-content: flex-start;\n`;\n\nconst RetryButton = styled.button`\n  background-color: #ffffff;\n  color: #f44336;\n  border: none;\n  border-radius: 0.25rem;\n  padding: 0.5rem 1rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #f5f5f5;\n  }\n`;\n\nconst SkipButton = styled.button`\n  padding: 0.5rem 1rem;\n  background-color: transparent;\n  color: #ffffff;\n  border: 1px solid #ffffff;\n  border-radius: 0.25rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\nconst ReloadButton = styled(RetryButton)`\n  margin-top: 1rem;\n  width: 100%;\n`;\n\n/**\n * Default fallback UI for the error boundary\n */\nconst DefaultFallback = ({\n  error,\n  resetError,\n  isAppLevel,\n  name,\n  onSkip,\n}: {\n  error: Error;\n  resetError: () => void;\n  isAppLevel?: boolean;\n  name?: string;\n  onSkip?: () => void;\n}) => {\n  const handleReload = () => {\n    window.location.reload();\n  };\n\n  if (isAppLevel) {\n    return (\n      <ErrorContainer isAppLevel>\n        <ErrorCard>\n          <ErrorTitle isAppLevel>Something went wrong</ErrorTitle>\n          <ErrorMessage isAppLevel>\n            We're sorry, but an unexpected error has occurred. Please try reloading the application.\n          </ErrorMessage>\n          <ErrorDetails>\n            <summary>Technical Details</summary>\n            <ErrorMessage>{error.message}</ErrorMessage>\n            {error.stack && <ErrorStack>{error.stack}</ErrorStack>}\n          </ErrorDetails>\n          <ReloadButton onClick={handleReload}>Reload Application</ReloadButton>\n        </ErrorCard>\n      </ErrorContainer>\n    );\n  }\n\n  return (\n    <ErrorContainer>\n      <ErrorTitle>{name ? `Error in ${name}` : 'Something went wrong'}</ErrorTitle>\n      <ErrorMessage>\n        {name\n          ? `We encountered a problem while loading ${name}. You can try again${\n              onSkip ? ' or skip this feature' : ''\n            }.`\n          : 'An unexpected error occurred. Please try again.'}\n      </ErrorMessage>\n      <ErrorDetails>\n        <summary>Technical Details</summary>\n        <ErrorMessage>{error.message}</ErrorMessage>\n        {error.stack && <ErrorStack>{error.stack}</ErrorStack>}\n      </ErrorDetails>\n      <ButtonContainer>\n        <RetryButton onClick={resetError}>Try Again</RetryButton>\n        {onSkip && <SkipButton onClick={onSkip}>Skip This Feature</SkipButton>}\n      </ButtonContainer>\n    </ErrorContainer>\n  );\n};\n\n/**\n * Error Boundary Component\n *\n * A unified React error boundary component that catches errors in its child component tree\n * and displays a fallback UI instead of crashing the entire application.\n *\n * This component can be used at both the application level and feature level.\n */\nexport class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\n    // Log the error to an error reporting service\n    const { name } = this.props;\n    const boundaryName = name ? `ErrorBoundary(${name})` : 'ErrorBoundary';\n    console.error(`Error caught by ${boundaryName}:`, error, errorInfo);\n\n    // Call the onError callback if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // Here we could add Sentry integration\n    // if (typeof window !== 'undefined' && window.Sentry) {\n    //   window.Sentry.withScope((scope) => {\n    //     scope.setTag('boundary', name || 'unnamed');\n    //     scope.setExtra('componentStack', errorInfo.componentStack);\n    //     window.Sentry.captureException(error);\n    //   });\n    // }\n  }\n\n  componentDidUpdate(prevProps: ErrorBoundaryProps): void {\n    // Reset the error state if the children prop changes and resetOnPropsChange is true\n    if (\n      this.state.hasError &&\n      this.props.resetOnPropsChange &&\n      prevProps.children !== this.props.children\n    ) {\n      this.resetError();\n    }\n  }\n\n  componentWillUnmount(): void {\n    // Reset the error state if resetOnUnmount is true\n    if (this.state.hasError && this.props.resetOnUnmount) {\n      this.resetError();\n    }\n  }\n\n  resetError = (): void => {\n    this.setState({\n      hasError: false,\n      error: null,\n    });\n  };\n\n  render(): ReactNode {\n    const { hasError, error } = this.state;\n    const { children, fallback, name, isFeatureBoundary, onSkip } = this.props;\n\n    if (hasError && error) {\n      // Render the fallback UI if an error occurred\n      if (typeof fallback === 'function') {\n        return fallback({ error, resetError: this.resetError });\n      } else if (fallback) {\n        return fallback;\n      }\n\n      // Use the default fallback\n      return (\n        <DefaultFallback\n          error={error}\n          resetError={this.resetError}\n          isAppLevel={!isFeatureBoundary}\n          name={name}\n          onSkip={onSkip}\n        />\n      );\n    }\n\n    // Otherwise, render the children\n    return children;\n  }\n}\n\nexport default ErrorBoundary;\n", "/**\n * Unified Error Boundary\n *\n * A unified error boundary component that can be used at both the application level\n * and feature level, replacing the previous three-layer architecture.\n */\nimport React from 'react';\nimport { ErrorBoundary, ErrorBoundaryProps } from './ErrorBoundary';\n\nexport interface UnifiedErrorBoundaryProps extends Omit<ErrorBoundaryProps, 'isFeatureBoundary'> {\n  /** Whether this is an application-level boundary */\n  isAppLevel?: boolean;\n  /** Whether this is a feature-level boundary */\n  isFeatureBoundary?: boolean;\n}\n\n/**\n * Unified Error Boundary\n *\n * A wrapper around the base ErrorBoundary component that provides a simpler API\n * for common use cases.\n */\nexport const UnifiedErrorBoundary: React.FC<UnifiedErrorBoundaryProps> = ({\n  isAppLevel = false,\n  isFeatureBoundary = false,\n  children,\n  ...props\n}) => {\n  // Determine the boundary type based on props\n  const boundaryType = isAppLevel ? 'app' : isFeatureBoundary ? 'feature' : 'component';\n\n  // Set appropriate defaults based on boundary type\n  const defaultProps: Partial<ErrorBoundaryProps> = {\n    resetOnPropsChange: boundaryType !== 'app', // App-level boundaries should not reset on props change\n    resetOnUnmount: boundaryType !== 'app', // App-level boundaries should not reset on unmount\n    isFeatureBoundary: boundaryType === 'feature',\n  };\n\n  return <ErrorBoundary {...defaultProps} {...props} children={children} />;\n};\n\n/**\n * App Error Boundary\n *\n * A specialized error boundary for the application level.\n */\nexport const AppErrorBoundary: React.FC<\n  Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'>\n> = (props) => {\n  return <UnifiedErrorBoundary isAppLevel {...props} />;\n};\n\n/**\n * Feature Error Boundary\n *\n * A specialized error boundary for feature modules.\n */\nexport const FeatureErrorBoundary: React.FC<\n  Omit<UnifiedErrorBoundaryProps, 'isAppLevel' | 'isFeatureBoundary'> & { featureName: string }\n> = ({ featureName, children, ...props }) => {\n  return (\n    <UnifiedErrorBoundary isFeatureBoundary name={featureName} children={children} {...props} />\n  );\n};\n\nexport default UnifiedErrorBoundary;\n", "/**\n * Theme Tokens\n *\n * Design tokens for the theme system\n */\n\n// Base colors interface\nexport interface BaseColors {\n  // F1 Racing Team Colors\n  f1Red: string;\n  f1RedDark: string;\n  f1RedLight: string;\n  f1Blue: string;\n  f1BlueDark: string;\n  f1BlueLight: string;\n\n  // F1 Racing Performance Colors\n  f1MercedesGreen: string;\n  f1MercedesGreenDark: string;\n  f1MercedesGreenLight: string;\n  f1McLarenOrange: string;\n  f1McLarenOrangeDark: string;\n  f1McLarenOrangeLight: string;\n  f1RacingYellow: string;\n  f1RacingYellowDark: string;\n  f1RacingYellowLight: string;\n  f1Carbon: string;\n  f1Silver: string;\n\n  // Neutrals\n  white: string;\n  black: string;\n  gray50: string;\n  gray100: string;\n  gray200: string;\n  gray300: string;\n  gray400: string;\n  gray500: string;\n  gray600: string;\n  gray700: string;\n  gray800: string;\n  gray900: string;\n\n  // Status colors\n  green: string;\n  greenDark: string;\n  greenLight: string;\n  yellow: string;\n  yellowDark: string;\n  yellowLight: string;\n  orange: string;\n  orangeDark: string;\n  orangeLight: string;\n  red: string;\n  redDark: string;\n  redLight: string;\n  blue: string;\n  blueDark: string;\n  blueLight: string;\n  purple: string;\n  purpleDark: string;\n  purpleLight: string;\n\n  // Transparent colors\n  whiteTransparent10: string;\n  blackTransparent10: string;\n}\n\n// Color mode interface\nexport interface ColorMode {\n  // Base colors\n  background: string;\n  surface: string;\n  cardBackground: string;\n  border: string;\n  divider: string;\n  textPrimary: string;\n  textSecondary: string;\n  textDisabled: string;\n  textInverse: string;\n  success: string;\n  warning: string;\n  error: string;\n  info: string;\n\n  // Chart colors\n  chartGrid: string;\n  chartLine: string;\n\n  // Trading specific colors\n  profit: string;\n  loss: string;\n  neutral: string;\n\n  // Component specific colors\n  tooltipBackground: string;\n  modalBackground: string;\n}\n\n// Base colors\nexport const baseColors: BaseColors = {\n  // F1 Racing Team Colors\n  f1Red: '#e10600', // Ferrari Red - CRITICAL ALERTS ONLY\n  f1RedDark: '#c10500',\n  f1RedLight: '#ff3b36',\n  f1Blue: '#0600EF', // Racing Blue - Information & Neutral\n  f1BlueDark: '#0500CC',\n  f1BlueLight: '#4169E1',\n\n  // F1 Racing Performance Colors\n  f1MercedesGreen: '#00D2BE', // Active, Success, Optimal\n  f1MercedesGreenDark: '#00A896',\n  f1MercedesGreenLight: '#00FFE5',\n  f1McLarenOrange: '#FF8700', // Warnings, Transitions\n  f1McLarenOrangeDark: '#E67600',\n  f1McLarenOrangeLight: '#FFA500',\n  f1RacingYellow: '#FFD320', // Caution, Pending\n  f1RacingYellowDark: '#E6BE1D',\n  f1RacingYellowLight: '#FFDC4A',\n  f1Carbon: '#1A1A1A', // Base background\n  f1Silver: '#C0C0C0', // Secondary text\n\n  // Neutrals\n  white: '#ffffff',\n  black: '#000000',\n  gray50: '#f9fafb',\n  gray100: '#f3f4f6',\n  gray200: '#e5e7eb',\n  gray300: '#d1d5db',\n  gray400: '#9ca3af',\n  gray500: '#6b7280',\n  gray600: '#4b5563',\n  gray700: '#374151',\n  gray800: '#1f2937',\n  gray900: '#111827',\n\n  // Status colors\n  green: '#4caf50',\n  greenDark: '#388e3c',\n  greenLight: '#81c784',\n  yellow: '#ffeb3b',\n  yellowDark: '#fbc02d',\n  yellowLight: '#fff59d',\n  orange: '#ff9800',\n  orangeDark: '#f57c00',\n  orangeLight: '#ffb74d',\n  red: '#f44336',\n  redDark: '#d32f2f',\n  redLight: '#e57373',\n  blue: '#2196f3',\n  blueDark: '#1976d2',\n  blueLight: '#64b5f6',\n  purple: '#9c27b0',\n  purpleDark: '#7b1fa2',\n  purpleLight: '#ba68c8',\n\n  // Transparent colors\n  whiteTransparent10: 'rgba(255, 255, 255, 0.1)',\n  blackTransparent10: 'rgba(0, 0, 0, 0.1)',\n};\n\n// Dark mode colors - Enhanced F1 Racing Theme\nexport const darkModeColors: ColorMode = {\n  background: '#0f0f0f',\n  surface: '#1a1a1a',\n  cardBackground: '#1a1a1a',\n  border: '#333333',\n  divider: 'rgba(255, 255, 255, 0.1)',\n  textPrimary: '#ffffff',\n  textSecondary: '#aaaaaa',\n  textDisabled: '#666666',\n  textInverse: '#1a1f2c',\n  success: baseColors.green,\n  warning: baseColors.yellow,\n  error: baseColors.red,\n  info: baseColors.blue,\n\n  // Chart colors\n  chartGrid: 'rgba(255, 255, 255, 0.1)',\n  chartLine: baseColors.f1Red,\n\n  // Trading specific colors\n  profit: baseColors.green,\n  loss: baseColors.red,\n  neutral: baseColors.gray400,\n\n  // Component specific colors\n  tooltipBackground: 'rgba(37, 42, 55, 0.9)',\n  modalBackground: 'rgba(26, 31, 44, 0.8)',\n};\n\n// Light mode colors\nexport const lightModeColors: ColorMode = {\n  background: '#f5f5f5',\n  surface: '#ffffff',\n  cardBackground: '#ffffff',\n  border: '#e0e0e0',\n  divider: 'rgba(0, 0, 0, 0.1)',\n  textPrimary: '#333333',\n  textSecondary: '#666666',\n  textDisabled: '#999999',\n  textInverse: '#ffffff',\n  success: baseColors.green,\n  warning: baseColors.yellow,\n  error: baseColors.red,\n  info: baseColors.blue,\n\n  // Chart colors\n  chartGrid: 'rgba(0, 0, 0, 0.1)',\n  chartLine: baseColors.f1Red,\n\n  // Trading specific colors\n  profit: baseColors.green,\n  loss: baseColors.red,\n  neutral: baseColors.gray400,\n\n  // Component specific colors\n  tooltipBackground: 'rgba(255, 255, 255, 0.9)',\n  modalBackground: 'rgba(255, 255, 255, 0.8)',\n};\n\n// Spacing\nexport const spacing = {\n  xxs: '4px',\n  xs: '8px',\n  sm: '12px',\n  md: '16px',\n  lg: '24px',\n  xl: '32px',\n  xxl: '48px',\n};\n\n// Font sizes\nexport const fontSizes = {\n  xs: '0.75rem',\n  sm: '0.875rem',\n  md: '1rem',\n  lg: '1.125rem',\n  xl: '1.25rem',\n  xxl: '1.5rem',\n  xxxl: '2.5rem', // Added missing xxxl size\n  h1: '2.5rem',\n  h2: '2rem',\n  h3: '1.75rem',\n  h4: '1.5rem',\n  h5: '1.25rem',\n  h6: '1rem',\n};\n\n// Font weights\nexport const fontWeights = {\n  light: 300,\n  regular: 400,\n  medium: 500,\n  semibold: 600,\n  bold: 700,\n};\n\n// Line heights\nexport const lineHeights = {\n  tight: 1.25,\n  normal: 1.5,\n  relaxed: 1.75,\n};\n\n// Font families\nexport const fontFamilies = {\n  body: \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif\",\n  heading:\n    \"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif\",\n  mono: \"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace\",\n};\n\n// Breakpoints\nexport const breakpoints = {\n  xs: '480px',\n  sm: '640px',\n  md: '768px',\n  lg: '1024px',\n  xl: '1280px',\n};\n\n// Border radius\nexport const borderRadius = {\n  xs: '2px',\n  sm: '4px',\n  md: '6px',\n  lg: '8px',\n  xl: '12px',\n  pill: '9999px',\n  circle: '50%',\n};\n\n// Shadows\nexport const shadows = {\n  sm: '0 1px 3px rgba(0, 0, 0, 0.1)',\n  md: '0 4px 6px rgba(0, 0, 0, 0.1)',\n  lg: '0 10px 15px rgba(0, 0, 0, 0.1)',\n};\n\n// Transitions\nexport const transitions = {\n  fast: '0.1s',\n  normal: '0.3s',\n  slow: '0.5s',\n};\n\n// Z-index\nexport const zIndex = {\n  base: 1,\n  overlay: 10,\n  modal: 20,\n  popover: 30,\n  tooltip: 40,\n  fixed: 100,\n};\n", "/**\n * Formula 1 Theme\n *\n * This file contains the Formula 1 inspired theme for the ADHD Trading Dashboard.\n */\n\nimport { Theme } from './types';\nimport {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\n\n/**\n * Mercedes F1 Theme\n *\n * Pure Mercedes F1 team theme with Petronas teal/green colors.\n * Authentic Mercedes-AMG Petronas Formula One Team aesthetic.\n */\nexport const f1Theme: Theme = {\n  name: 'mercedes-green',\n  colors: {\n    // Primary colors - Mercedes Green for active/positive states\n    primary: baseColors.f1MercedesGreen,\n    primaryDark: baseColors.f1MercedesGreenDark,\n    primaryLight: baseColors.f1MercedesGreenLight,\n\n    // Secondary colors - Racing Blue for information\n    secondary: baseColors.f1Blue,\n    secondaryDark: baseColors.f1BlueDark,\n    secondaryLight: baseColors.f1BlueLight,\n\n    // Accent colors - McLaren Orange for transitions\n    accent: baseColors.f1McLarenOrange,\n    accentDark: baseColors.f1McLarenOrangeDark,\n    accentLight: baseColors.f1McLarenOrangeLight,\n\n    // F1 Racing Status Colors\n    success: baseColors.f1MercedesGreen, // Green flag - optimal performance\n    warning: baseColors.f1McLarenOrange, // Orange flag - caution/transitions\n    error: baseColors.f1Red, // Red flag - critical alerts only\n    danger: baseColors.f1Red, // Ferrari red for genuine danger\n    info: baseColors.f1Blue, // Racing blue for information\n\n    // Neutral colors\n    background: darkModeColors.background,\n    surface: darkModeColors.surface,\n    elevated: baseColors.gray700, // Added elevated color for F1 theme\n    cardBackground: darkModeColors.surface,\n    border: darkModeColors.border,\n    divider: 'rgba(255, 255, 255, 0.1)',\n\n    // Text colors\n    textPrimary: darkModeColors.textPrimary,\n    textSecondary: darkModeColors.textSecondary,\n    textDisabled: darkModeColors.textDisabled,\n    textInverse: darkModeColors.textInverse,\n\n    // Chart colors\n    chartGrid: darkModeColors.chartGrid,\n    chartLine: darkModeColors.chartLine,\n    chartAxis: baseColors.gray400,\n    chartTooltip: darkModeColors.tooltipBackground,\n\n    // F1 Racing Trading Colors\n    profit: baseColors.f1MercedesGreen, // Green flag for profitable trades\n    loss: baseColors.f1Red, // Red flag for losses only\n    neutral: baseColors.f1Silver, // Silver for neutral data\n\n    // F1 Racing Tab Colors\n    tabActive: baseColors.f1MercedesGreen, // Active tabs use Mercedes green\n    tabInactive: baseColors.gray600,\n\n    // Component specific colors\n    tooltipBackground: darkModeColors.tooltipBackground,\n    modalBackground: darkModeColors.modalBackground,\n    sidebarBackground: baseColors.gray800,\n    headerBackground: 'rgba(0, 0, 0, 0.2)',\n\n    // F1 Racing Session States\n    sessionActive: baseColors.f1MercedesGreen, // Active sessions - green flag\n    sessionOptimal: baseColors.f1MercedesGreenLight, // Optimal windows - bright green\n    sessionCaution: baseColors.f1RacingYellow, // Caution periods - yellow flag\n    sessionTransition: baseColors.f1McLarenOrange, // Transition periods - orange\n    sessionInactive: baseColors.gray600, // Inactive sessions - neutral\n\n    // F1 Racing Performance States\n    performanceExcellent: baseColors.f1MercedesGreen,\n    performanceGood: baseColors.f1Blue,\n    performanceAverage: baseColors.f1Silver,\n    performancePoor: baseColors.f1McLarenOrange,\n    performanceAvoid: baseColors.f1Red,\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n", "/**\n * F1 Official Theme\n *\n * Authentic F1 app theme with official F1 colors and timing screen aesthetics.\n * Matches CSS variables in [data-theme='f1-official'].\n */\n\nimport {\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\nimport { Theme } from './types';\n\n/**\n * F1 Official Theme\n *\n * Authentic F1 timing screen theme with official F1 red and gold colors.\n * Designed to match the official F1 app aesthetic.\n */\nexport const f1OfficialTheme: Theme = {\n  name: 'f1-official',\n  colors: {\n    // F1 Brand Colors - Official Red Primary\n    primary: '#e10600',\n    primaryDark: '#b30500',\n    primaryLight: '#ff1e1e',\n\n    // F1 Secondary Colors - Deep Navy\n    secondary: '#15151e',\n    secondaryDark: '#0f0f17',\n    secondaryLight: '#1e1e2e',\n\n    // F1 Accent Colors - Championship Gold\n    accent: '#ffd700',\n    accentDark: '#e6be1d',\n    accentLight: '#ffdc4a',\n\n    // F1 Status Colors - Timing Screen Colors\n    success: '#00ff41', // F1 timing green (sector improvements)\n    warning: '#ffd700', // F1 yellow flags\n    error: '#ff1e1e', // F1 timing red (sector losses)\n    danger: '#ff1e1e', // F1 danger red (same as error for F1 theme)\n    info: '#00b4d8', // F1 information blue\n\n    // Background Colors - F1 App Style\n    background: '#15151e',\n    surface: '#1e1e2e',\n    cardBackground: '#2a2a3a',\n    elevated: '#353545',\n\n    // Border Colors\n    border: '#3a3a4a',\n    divider: '#4a4a5a',\n\n    // Text Colors - High Contrast F1 Style\n    textPrimary: '#ffffff',\n    textSecondary: '#b8b8c8',\n    textDisabled: '#8b8b9b',\n    textInverse: '#15151e',\n\n    // Chart Colors\n    chartGrid: 'rgba(255, 255, 255, 0.1)',\n    chartLine: '#e10600',\n    chartAxis: '#b8b8c8',\n    chartTooltip: 'rgba(42, 42, 58, 0.9)',\n\n    // Trading Colors\n    profit: '#00ff41', // F1 timing green\n    loss: '#ff1e1e', // F1 timing red\n    neutral: '#b8b8c8',\n\n    // Tab Colors\n    tabActive: '#e10600',\n    tabInactive: '#8b8b9b',\n\n    // Component Colors\n    tooltipBackground: 'rgba(42, 42, 58, 0.9)',\n    modalBackground: 'rgba(21, 21, 30, 0.8)',\n    sidebarBackground: '#1e1e2e',\n    headerBackground: 'rgba(21, 21, 30, 0.9)',\n\n    // F1 Session States\n    sessionActive: '#e10600', // F1 red for active sessions\n    sessionOptimal: '#ffd700', // Gold for optimal windows\n    sessionCaution: '#ff8700', // Orange for caution periods\n    sessionTransition: '#00b4d8', // Blue for transitions\n    sessionInactive: '#8b8b9b', // Muted for inactive\n\n    // F1 Performance States\n    performanceExcellent: '#00ff41', // Timing green\n    performanceGood: '#ffd700', // Championship gold\n    performanceAverage: '#ff8700', // Warning orange\n    performancePoor: '#ff1e1e', // Timing red\n    performanceAvoid: '#8b8b9b', // Muted gray\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n\nexport default f1OfficialTheme;\n", "/**\n * Dark Theme\n *\n * This file contains the dark theme for the ADHD Trading Dashboard.\n */\n\nimport { Theme } from './types';\nimport {\n  baseColors,\n  darkModeColors,\n  spacing,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  breakpoints,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n} from './tokens';\n\n/**\n * Mercedes Dark Theme\n *\n * Mercedes-inspired dark theme with silver accents and sophisticated contrast.\n * Professional Mercedes F1 pit wall aesthetic for extended use.\n */\nexport const darkTheme: Theme = {\n  name: 'mercedes-dark',\n  colors: {\n    // Mercedes-inspired primary colors (Silver/Teal accents)\n    primary: baseColors.f1Silver,\n    primaryDark: baseColors.gray500,\n    primaryLight: baseColors.gray300,\n\n    // Secondary colors - Subtle Mercedes teal\n    secondary: baseColors.f1MercedesGreenDark,\n    secondaryDark: '#006B5D',\n    secondaryLight: baseColors.f1MercedesGreen,\n\n    // Accent colors - Mercedes silver\n    accent: baseColors.f1Silver,\n    accentDark: baseColors.gray500,\n    accentLight: baseColors.gray300,\n\n    // Mercedes-themed status colors\n    success: baseColors.f1MercedesGreen, // Mercedes green for success\n    warning: baseColors.f1Silver, // Silver for warnings\n    error: baseColors.red, // Keep red for errors\n    danger: baseColors.red, // Keep red for danger\n    info: baseColors.f1MercedesGreenDark, // Dark teal for info\n\n    // Neutral colors\n    background: baseColors.gray900, // Slightly different from F1 theme\n    surface: baseColors.gray800,\n    elevated: baseColors.gray700, // Added elevated color for dark theme\n    cardBackground: baseColors.gray800,\n    border: baseColors.gray700,\n    divider: 'rgba(255, 255, 255, 0.1)',\n\n    // Text colors - Improved contrast for dark theme\n    textPrimary: baseColors.white, // #ffffff - High contrast\n    textSecondary: baseColors.gray200, // #e5e7eb - Better contrast than gray300\n    textDisabled: baseColors.gray400, // #9ca3af - More visible than gray500\n    textInverse: baseColors.gray900,\n\n    // Chart colors\n    chartGrid: darkModeColors.chartGrid,\n    chartLine: baseColors.f1Blue, // Using blue instead of red\n    chartAxis: baseColors.gray400,\n    chartTooltip: darkModeColors.tooltipBackground,\n\n    // Mercedes-themed trading colors\n    profit: baseColors.f1MercedesGreen, // Mercedes green for profits\n    loss: baseColors.red, // Keep red for losses\n    neutral: baseColors.f1Silver, // Mercedes silver for neutral\n\n    // Mercedes tab colors\n    tabActive: baseColors.f1Silver, // Silver for active tabs\n    tabInactive: baseColors.gray600,\n\n    // Component specific colors\n    tooltipBackground: 'rgba(26, 32, 44, 0.9)', // Slightly different from F1 theme\n    modalBackground: 'rgba(26, 32, 44, 0.8)',\n    sidebarBackground: baseColors.gray900,\n    headerBackground: 'rgba(0, 0, 0, 0.3)',\n\n    // Mercedes-themed session states\n    sessionActive: baseColors.f1MercedesGreen, // Mercedes green for active\n    sessionOptimal: baseColors.f1MercedesGreenLight, // Bright Mercedes green\n    sessionCaution: baseColors.f1Silver, // Silver for caution\n    sessionTransition: baseColors.gray300, // Light gray for transitions\n    sessionInactive: baseColors.gray600, // Gray for inactive\n\n    // Mercedes performance states\n    performanceExcellent: baseColors.f1MercedesGreen,\n    performanceGood: baseColors.f1Silver,\n    performanceAverage: baseColors.gray400,\n    performancePoor: baseColors.gray500,\n    performanceAvoid: baseColors.red,\n  },\n  spacing,\n  breakpoints,\n  fontSizes,\n  fontWeights,\n  lineHeights,\n  fontFamilies,\n  borderRadius,\n  shadows,\n  transitions,\n  zIndex,\n};\n", "/**\n * Global Styles\n *\n * This file contains global styles for the application, including CSS reset and font loading.\n */\nimport { createGlobalStyle } from 'styled-components';\nimport { Theme } from './types';\n\n/**\n * Global Styles Component\n *\n * Provides CSS reset and global styles for the application.\n */\nexport const GlobalStyles = createGlobalStyle<{ theme: Theme }>`\n  /* Note: Font loading should be done in the HTML head, not in createGlobalStyle */\n  /* The @import rule in createGlobalStyle can cause FOUC (Flash of Unstyled Content) */\n\n  /* CSS Reset */\n  *, *::before, *::after {\n    box-sizing: border-box;\n  }\n\n  html, body, div, span, applet, object, iframe,\n  h1, h2, h3, h4, h5, h6, p, blockquote, pre,\n  a, abbr, acronym, address, big, cite, code,\n  del, dfn, em, img, ins, kbd, q, s, samp,\n  small, strike, strong, sub, sup, tt, var,\n  b, u, i, center,\n  dl, dt, dd, ol, ul, li,\n  fieldset, form, label, legend,\n  table, caption, tbody, tfoot, thead, tr, th, td,\n  article, aside, canvas, details, embed,\n  figure, figcaption, footer, header, hgroup,\n  menu, nav, output, ruby, section, summary,\n  time, mark, audio, video {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    font-size: 100%;\n    font: inherit;\n    vertical-align: baseline;\n  }\n\n  /* HTML5 display-role reset for older browsers */\n  article, aside, details, figcaption, figure,\n  footer, header, hgroup, menu, nav, section {\n    display: block;\n  }\n\n  body {\n    line-height: 1.5;\n    font-family: ${({ theme }) => theme.fontFamilies.body};\n    background-color: ${({ theme }) => theme.colors.background};\n    color: ${({ theme }) => theme.colors.textPrimary};\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n\n  ol, ul {\n    list-style: none;\n  }\n\n  blockquote, q {\n    quotes: none;\n  }\n\n  blockquote:before, blockquote:after,\n  q:before, q:after {\n    content: '';\n    content: none;\n  }\n\n  table {\n    border-collapse: collapse;\n    border-spacing: 0;\n  }\n\n  a {\n    color: ${({ theme }) => theme.colors.primary};\n    text-decoration: none;\n\n    &:hover {\n      text-decoration: underline;\n    }\n  }\n\n  button, input, select, textarea {\n    font-family: inherit;\n    font-size: inherit;\n    line-height: inherit;\n  }\n\n  /* Scrollbar styling */\n  ::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n\n  ::-webkit-scrollbar-track {\n    background: ${({ theme }) => theme.colors.background};\n  }\n\n  ::-webkit-scrollbar-thumb {\n    background: ${({ theme }) => theme.colors.border};\n    border-radius: 4px;\n  }\n\n  ::-webkit-scrollbar-thumb:hover {\n    background: ${({ theme }) => theme.colors.primary};\n  }\n\n  /* Focus styles */\n  :focus {\n    outline: 2px solid ${({ theme }) => theme.colors.primary};\n    outline-offset: 2px;\n  }\n\n  /* Selection styles */\n  ::selection {\n    background-color: ${({ theme }) => theme.colors.primary};\n    color: ${({ theme }) => theme.colors.textInverse};\n  }\n`;\n\nexport default GlobalStyles;\n", "/**\n * Theme Provider Component\n *\n * This component provides the theme context to the application.\n */\n\nimport React, { useState, createContext, useContext, useEffect } from 'react';\nimport { ThemeProvider as StyledThemeProvider, DefaultTheme } from 'styled-components';\nimport { Theme } from './types';\nimport { f1Theme } from './f1Theme';\nimport { f1OfficialTheme } from './f1OfficialTheme';\nimport { darkTheme } from './darkTheme';\nimport GlobalStyles from './GlobalStyles';\n\n// Map of available themes - Standardized naming\nconst themes: Record<string, Theme> = {\n  'mercedes-green': f1Theme,\n  'f1-official': f1OfficialTheme,\n  dark: darkTheme,\n};\n\n// Default theme\nconst defaultTheme = f1Theme;\n\n// Migration helper for old theme names\nconst migrateThemeName = (oldThemeName: string): string => {\n  // Migrate old F1 theme names to Mercedes Green\n  if (oldThemeName === 'f1' || oldThemeName === 'formula1' || oldThemeName === 'formula-1') {\n    return 'mercedes-green';\n  }\n  // Migrate light theme to F1 Official\n  if (oldThemeName === 'light') {\n    return 'f1-official';\n  }\n  return oldThemeName;\n};\n\n// Helper to get a theme by name\nconst getTheme = (themeName: string): Theme => {\n  const migratedThemeName = migrateThemeName(themeName);\n  return themes[migratedThemeName] || defaultTheme;\n};\n\n// Create theme context\nexport const ThemeContext = createContext<{\n  theme: Theme;\n  setTheme: (theme: Theme | string) => void;\n}>({\n  theme: defaultTheme,\n  setTheme: () => {},\n});\n\n// Hook to use the theme\nexport const useTheme = () => useContext(ThemeContext);\n\ninterface ThemeProviderProps {\n  /** The initial theme to use */\n  initialTheme?: string | Theme;\n  /** Whether to store the theme in local storage */\n  persistTheme?: boolean;\n  /** The key to use for storing the theme in local storage */\n  storageKey?: string;\n  /** The child components */\n  children: React.ReactNode;\n}\n\n/**\n * Theme Provider Component\n *\n * Provides theme context to the application and handles theme switching.\n */\nexport const ThemeProvider = ({\n  initialTheme = defaultTheme,\n  persistTheme = true,\n  storageKey = 'adhd-dashboard-theme',\n  children,\n}: ThemeProviderProps) => {\n  // console.log('ThemeProvider rendering with initialTheme:', initialTheme);\n\n  // Initial theme setup\n  const [theme, setThemeState] = useState<Theme>(() => {\n    // Try to load from localStorage\n    if (persistTheme && typeof window !== 'undefined') {\n      const storedTheme = window.localStorage.getItem(storageKey);\n\n      if (storedTheme) {\n        try {\n          // Try to get the theme by name first\n          const themeByName = getTheme(storedTheme);\n          if (themeByName) {\n            return themeByName;\n          }\n\n          // Otherwise, try to parse as JSON\n          const parsedTheme = JSON.parse(storedTheme) as Theme;\n          return parsedTheme;\n        } catch (error) {\n          console.error('Failed to parse stored theme:', error);\n        }\n      }\n    }\n\n    // Fall back to initial theme\n    const resolvedTheme = typeof initialTheme === 'string' ? getTheme(initialTheme) : initialTheme;\n    return resolvedTheme;\n  });\n\n  // Sync data-theme attribute with theme changes\n  useEffect(() => {\n    if (typeof document !== 'undefined') {\n      document.documentElement.setAttribute('data-theme', theme.name);\n    }\n  }, [theme.name]);\n\n  // Theme change handler\n  const setTheme = (newTheme: Theme | string) => {\n    const themeObject = typeof newTheme === 'string' ? getTheme(newTheme) : newTheme;\n    setThemeState(themeObject);\n\n    // Save to localStorage if enabled\n    if (persistTheme && typeof window !== 'undefined') {\n      window.localStorage.setItem(storageKey, themeObject.name || JSON.stringify(themeObject));\n    }\n  };\n\n  // Create a wrapper component to avoid TypeScript issues\n  const ThemeWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n    return (\n      <StyledThemeProvider theme={theme as DefaultTheme}>\n        <GlobalStyles />\n        {children}\n      </StyledThemeProvider>\n    );\n  };\n\n  // Provide the theme context\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme }}>\n      <ThemeWrapper>{children}</ThemeWrapper>\n    </ThemeContext.Provider>\n  );\n};\n", "/**\n * Accessibility Controls Component\n * \n * Provides user-controllable accessibility features for neurodivergent users:\n * - Motion toggle (enable/disable animations)\n * - Low stimulation mode (removes visual effects)\n * - Automatic reduced motion detection\n * - Persistent user preferences\n */\n\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\n\nexport interface AccessibilityPreferences {\n  motionEnabled: boolean;\n  lowStimulationMode: boolean;\n  autoDetectMotion: boolean;\n}\n\ninterface AccessibilityControlsProps {\n  /** Whether to show the controls panel */\n  isVisible?: boolean;\n  /** Callback when preferences change */\n  onPreferencesChange?: (preferences: AccessibilityPreferences) => void;\n  /** Additional CSS class */\n  className?: string;\n}\n\n// Styled Components\nconst ControlsPanel = styled.div<{ $isVisible: boolean }>`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: var(--bg-secondary);\n  border: 2px solid var(--primary-color);\n  border-radius: 8px;\n  padding: var(--spacing-md);\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);\n  backdrop-filter: blur(10px);\n  z-index: 1000;\n  min-width: 280px;\n  transform: ${({ $isVisible }) => ($isVisible ? 'translateX(0)' : 'translateX(calc(100% + 40px))')};\n  transition: transform 0.3s ease-in-out;\n\n  @media (max-width: 768px) {\n    top: 10px;\n    right: 10px;\n    left: 10px;\n    min-width: auto;\n  }\n`;\n\nconst ControlsHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-md);\n  padding-bottom: var(--spacing-sm);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Title = styled.h3`\n  font-family: var(--font-body);\n  font-size: var(--font-size-md);\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0;\n`;\n\nconst CloseButton = styled.button`\n  background: none;\n  border: none;\n  color: var(--text-secondary);\n  font-size: 20px;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: color var(--transition-normal);\n\n  &:hover {\n    color: var(--text-primary);\n  }\n\n  &:focus {\n    outline: 2px solid var(--primary-color);\n    outline-offset: 2px;\n  }\n`;\n\nconst ControlGroup = styled.div`\n  margin-bottom: var(--spacing-md);\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\nconst ControlLabel = styled.label`\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  font-family: var(--font-body);\n  font-size: var(--font-size-sm);\n  color: var(--text-primary);\n  cursor: pointer;\n  padding: var(--spacing-xs) 0;\n`;\n\nconst ControlCheckbox = styled.input`\n  width: 18px;\n  height: 18px;\n  accent-color: var(--primary-color);\n  cursor: pointer;\n\n  &:focus {\n    outline: 2px solid var(--primary-color);\n    outline-offset: 2px;\n  }\n`;\n\nconst ControlDescription = styled.div`\n  font-size: var(--font-size-xs);\n  color: var(--text-secondary);\n  margin-top: var(--spacing-xxs);\n  line-height: 1.4;\n`;\n\nconst ToggleButton = styled.button<{ $isActive: boolean }>`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: var(--bg-secondary);\n  border: 2px solid var(--primary-color);\n  border-radius: 50%;\n  width: 48px;\n  height: 48px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  z-index: 999;\n  transition: all var(--transition-normal);\n  color: var(--primary-color);\n  font-size: 20px;\n\n  &:hover {\n    background: var(--primary-color);\n    color: var(--bg-secondary);\n    transform: scale(1.05);\n  }\n\n  &:focus {\n    outline: 2px solid var(--primary-color);\n    outline-offset: 4px;\n  }\n\n  @media (max-width: 768px) {\n    top: 10px;\n    right: 10px;\n    width: 44px;\n    height: 44px;\n    font-size: 18px;\n  }\n`;\n\n/**\n * Accessibility Controls Component\n */\nexport const AccessibilityControls: React.FC<AccessibilityControlsProps> = ({\n  isVisible = false,\n  onPreferencesChange,\n  className,\n}) => {\n  const [showPanel, setShowPanel] = useState(isVisible);\n  const [preferences, setPreferences] = useState<AccessibilityPreferences>({\n    motionEnabled: true,\n    lowStimulationMode: false,\n    autoDetectMotion: true,\n  });\n\n  // Load preferences from localStorage on mount\n  useEffect(() => {\n    const savedPrefs = localStorage.getItem('accessibility-preferences');\n    if (savedPrefs) {\n      try {\n        const parsed = JSON.parse(savedPrefs);\n        setPreferences(parsed);\n        applyPreferences(parsed);\n      } catch (error) {\n        console.warn('Failed to parse accessibility preferences:', error);\n      }\n    }\n\n    // Check system preference for reduced motion\n    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;\n    if (prefersReducedMotion && preferences.autoDetectMotion) {\n      const updatedPrefs = { ...preferences, motionEnabled: false };\n      setPreferences(updatedPrefs);\n      applyPreferences(updatedPrefs);\n    }\n  }, []);\n\n  // Apply preferences to document\n  const applyPreferences = (prefs: AccessibilityPreferences) => {\n    const root = document.documentElement;\n    \n    // Apply motion settings\n    root.setAttribute('data-motion-enabled', prefs.motionEnabled.toString());\n    \n    // Apply stimulation mode\n    if (prefs.lowStimulationMode) {\n      root.setAttribute('data-accessibility-mode', 'low-stimulation');\n    } else {\n      root.removeAttribute('data-accessibility-mode');\n    }\n  };\n\n  // Handle preference changes\n  const handlePreferenceChange = (key: keyof AccessibilityPreferences, value: boolean) => {\n    const newPreferences = { ...preferences, [key]: value };\n    setPreferences(newPreferences);\n    \n    // Save to localStorage\n    localStorage.setItem('accessibility-preferences', JSON.stringify(newPreferences));\n    \n    // Apply to document\n    applyPreferences(newPreferences);\n    \n    // Notify parent component\n    onPreferencesChange?.(newPreferences);\n  };\n\n  return (\n    <>\n      <ToggleButton\n        $isActive={showPanel}\n        onClick={() => setShowPanel(!showPanel)}\n        aria-label=\"Toggle accessibility controls\"\n        aria-expanded={showPanel}\n        aria-controls=\"accessibility-panel\"\n      >\n        ♿\n      </ToggleButton>\n\n      <ControlsPanel \n        $isVisible={showPanel} \n        className={className}\n        id=\"accessibility-panel\"\n        role=\"dialog\"\n        aria-labelledby=\"accessibility-title\"\n      >\n        <ControlsHeader>\n          <Title id=\"accessibility-title\">Accessibility Controls</Title>\n          <CloseButton\n            onClick={() => setShowPanel(false)}\n            aria-label=\"Close accessibility controls\"\n          >\n            ×\n          </CloseButton>\n        </ControlsHeader>\n\n        <ControlGroup>\n          <ControlLabel>\n            <ControlCheckbox\n              type=\"checkbox\"\n              checked={preferences.motionEnabled}\n              onChange={(e) => handlePreferenceChange('motionEnabled', e.target.checked)}\n              aria-describedby=\"motion-description\"\n            />\n            Enable Animations\n          </ControlLabel>\n          <ControlDescription id=\"motion-description\">\n            Controls all visual animations and transitions in the interface\n          </ControlDescription>\n        </ControlGroup>\n\n        <ControlGroup>\n          <ControlLabel>\n            <ControlCheckbox\n              type=\"checkbox\"\n              checked={preferences.lowStimulationMode}\n              onChange={(e) => handlePreferenceChange('lowStimulationMode', e.target.checked)}\n              aria-describedby=\"stimulation-description\"\n            />\n            Low Stimulation Mode\n          </ControlLabel>\n          <ControlDescription id=\"stimulation-description\">\n            Reduces visual effects, glows, and racing elements for calmer experience\n          </ControlDescription>\n        </ControlGroup>\n\n        <ControlGroup>\n          <ControlLabel>\n            <ControlCheckbox\n              type=\"checkbox\"\n              checked={preferences.autoDetectMotion}\n              onChange={(e) => handlePreferenceChange('autoDetectMotion', e.target.checked)}\n              aria-describedby=\"auto-detect-description\"\n            />\n            Auto-Detect Motion Preference\n          </ControlLabel>\n          <ControlDescription id=\"auto-detect-description\">\n            Automatically respects your system's reduced motion setting\n          </ControlDescription>\n        </ControlGroup>\n      </ControlsPanel>\n    </>\n  );\n};\n\nexport default AccessibilityControls;\n", "/**\n * Header Component\n *\n * This component displays the application header with navigation controls.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\n// Header container\nconst HeaderContainer = styled.header`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 64px;\n  padding: 0 ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-bottom: 1px solid var(--border-primary);\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\n// Left section\nconst LeftSection = styled.div`\n  display: flex;\n  align-items: center;\n`;\n\n// Hamburger menu button\nconst MenuButton = styled.button`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  cursor: pointer;\n  transition: color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\n// F1 Racing Logo\nconst Logo = styled.div`\n  display: flex;\n  align-items: center;\n  margin-left: ${({ theme }) => theme.spacing.sm};\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  font-weight: bold;\n  color: ${({ theme }) => theme.colors.primary};\n  text-transform: uppercase;\n  letter-spacing: 2px;\n  font-family: 'Orbitron', 'Inter', sans-serif;\n\n  &::before {\n    content: '🏎️';\n    margin-right: ${({ theme }) => theme.spacing.xs};\n    font-size: 1.2em;\n  }\n`;\n\n// Session Info\nconst SessionInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  margin-left: ${({ theme }) => theme.spacing.md};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    display: none;\n  }\n`;\n\n// Session Title\nconst SessionTitle = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: var(--text-secondary);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  line-height: 1;\n`;\n\n// Session Year\nconst SessionYear = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.primary};\n  font-weight: bold;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  line-height: 1;\n`;\n\n// Right section\nconst RightSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\n// User menu\nconst UserMenu = styled.div`\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: ${({ theme }) => theme.spacing.xs} ${({ theme }) => theme.spacing.sm};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n`;\n\n// Avatar\nconst Avatar = styled.div`\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: ${({ theme }) => theme.colors.primary};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: bold;\n`;\n\ninterface HeaderProps {\n  toggleSidebar: () => void;\n  sidebarOpen: boolean;\n}\n\n/**\n * Header Component - F1 Racing Style\n */\nconst Header: React.FC<HeaderProps> = ({ toggleSidebar, sidebarOpen }) => {\n  return (\n    <HeaderContainer>\n      <LeftSection>\n        <MenuButton onClick={toggleSidebar} aria-label=\"Toggle sidebar\">\n          {sidebarOpen ? <span>☰</span> : <span>☰</span>}\n        </MenuButton>\n        <Logo>TRADING</Logo>\n        <SessionInfo>\n          <SessionTitle>2025 SESSION 1</SessionTitle>\n          <SessionYear>LIVE DASHBOARD</SessionYear>\n        </SessionInfo>\n      </LeftSection>\n\n      <RightSection>\n        <UserMenu>\n          <Avatar>JD</Avatar>\n        </UserMenu>\n      </RightSection>\n    </HeaderContainer>\n  );\n};\n\nexport default Header;\n", "/**\n * Sidebar Component\n *\n * This component displays the application sidebar with navigation links.\n */\n\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport styled from 'styled-components';\n\n// Sidebar container\nconst SidebarContainer = styled.aside<{ isOpen: boolean }>`\n  height: 100%;\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-right: 1px solid var(--border-primary);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: width ${({ theme }) => theme.transitions.normal};\n`;\n\n// Logo container\nconst LogoContainer = styled.div<{ isOpen: boolean }>`\n  height: 64px;\n  display: flex;\n  align-items: center;\n  justify-content: ${({ isOpen }) => (isOpen ? 'flex-start' : 'center')};\n  padding: 0 ${({ theme, isOpen }) => (isOpen ? theme.spacing.md : '0')};\n  border-bottom: 1px solid var(--border-primary);\n`;\n\n// Logo\nconst Logo = styled.div<{ isOpen: boolean }>`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: bold;\n  color: ${({ theme }) => theme.colors.primary};\n  white-space: nowrap;\n  overflow: hidden;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n`;\n\n// Navigation container\nconst NavContainer = styled.nav`\n  flex: 1;\n  overflow-y: auto;\n  padding: ${({ theme }) => theme.spacing.md} 0;\n`;\n\n// Navigation item\nconst NavItem = styled(NavLink)<{ $isOpen: boolean }>`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.sm}\n    ${({ theme, $isOpen }) => ($isOpen ? theme.spacing.md : '0')};\n  justify-content: ${({ $isOpen }) => ($isOpen ? 'flex-start' : 'center')};\n  color: var(--text-secondary);\n  text-decoration: none;\n  transition: background-color ${({ theme }) => theme.transitions.fast},\n    color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: rgba(255, 255, 255, 0.05);\n    color: ${({ theme }) => theme.colors.textPrimary};\n  }\n\n  &.active {\n    color: ${({ theme }) => theme.colors.primary};\n    background-color: rgba(255, 255, 255, 0.05);\n    border-left: 3px solid ${({ theme }) => theme.colors.primary};\n  }\n`;\n\n// Icon\nconst Icon = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\n// Label\nconst Label = styled.span<{ isOpen: boolean }>`\n  white-space: nowrap;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n  overflow: hidden;\n  max-width: ${({ isOpen }) => (isOpen ? '200px' : '0')};\n`;\n\n// Footer\nconst Footer = styled.div<{ isOpen: boolean }>`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.md};\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  white-space: nowrap;\n  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};\n  transition: opacity ${({ theme }) => theme.transitions.normal};\n  text-align: center;\n`;\n\ninterface SidebarProps {\n  isOpen: boolean;\n}\n\n/**\n * Sidebar Component\n */\nconst Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {\n  const location = useLocation();\n\n  const navItems = [\n    { path: '/', label: 'Dashboard', icon: '📊' },\n    { path: '/daily-guide', label: 'Daily Guide', icon: '📅' },\n    { path: '/journal', label: 'Trade Journal', icon: '📓' },\n    { path: '/analysis', label: 'Analysis', icon: '📈' },\n    { path: '/settings', label: 'Settings', icon: '⚙️' },\n  ];\n\n  return (\n    <SidebarContainer isOpen={isOpen}>\n      <LogoContainer isOpen={isOpen}>\n        <Logo isOpen={isOpen}>ADHD</Logo>\n      </LogoContainer>\n\n      <NavContainer>\n        {navItems.map((item) => (\n          <NavItem\n            key={item.path}\n            to={item.path}\n            $isOpen={isOpen}\n            className={location.pathname === item.path ? 'active' : ''}\n          >\n            <Icon>{item.icon}</Icon>\n            <Label isOpen={isOpen}>{item.label}</Label>\n          </NavItem>\n        ))}\n      </NavContainer>\n\n      <Footer isOpen={isOpen}>v1.0.0</Footer>\n    </SidebarContainer>\n  );\n};\n\nexport default Sidebar;\n", "/**\n * Main Layout Component\n *\n * This component provides the main layout structure for the application.\n */\n\nimport React, { useState } from 'react';\nimport { Outlet } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { AccessibilityControls } from '../components/accessibility/AccessibilityControls';\nimport Header from './Header';\nimport Sidebar from './Sidebar';\n\n// Main container\nconst LayoutContainer = styled.div`\n  display: flex;\n  height: 100vh;\n  width: 100%;\n  overflow: hidden;\n  background-color: ${({ theme }) => theme.colors.background};\n`;\n\n// Sidebar container\nconst SidebarContainer = styled.div<{ isOpen: boolean }>`\n  width: ${({ isOpen }) => (isOpen ? '240px' : '64px')};\n  height: 100%;\n  transition: width ${({ theme }) => theme.transitions.normal};\n  flex-shrink: 0;\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {\n    position: fixed;\n    z-index: ${({ theme }) => theme.zIndex.fixed};\n    width: ${({ isOpen }) => (isOpen ? '240px' : '0')};\n    box-shadow: ${({ isOpen, theme }) => (isOpen ? theme.shadows.lg : 'none')};\n  }\n`;\n\n// Main content container\nconst ContentContainer = styled.div<{ sidebarOpen: boolean }>`\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  transition: margin-left ${({ theme }) => theme.transitions.normal};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {\n    margin-left: 0;\n    width: 100%;\n  }\n`;\n\n// Header container\nconst HeaderContainer = styled.div`\n  height: 64px;\n  flex-shrink: 0;\n`;\n\n// Main content\nconst MainContent = styled.main`\n  flex: 1;\n  overflow: auto;\n  padding: ${({ theme }) => theme.spacing.lg};\n\n  @media (max-width: ${({ theme }) => theme.breakpoints.sm}) {\n    padding: ${({ theme }) => theme.spacing.md};\n  }\n`;\n\n// Overlay for mobile sidebar\nconst Overlay = styled.div<{ isVisible: boolean }>`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: ${({ theme }) => theme.zIndex.modal - 1};\n  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};\n  visibility: ${({ isVisible }) => (isVisible ? 'visible' : 'hidden')};\n  transition: opacity ${({ theme }) => theme.transitions.normal},\n    visibility ${({ theme }) => theme.transitions.normal};\n\n  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {\n    display: none;\n  }\n`;\n\n/**\n * Main Layout Component\n */\nconst MainLayout: React.FC = () => {\n  const [sidebarOpen, setSidebarOpen] = useState(true);\n\n  // Toggle sidebar\n  const toggleSidebar = () => {\n    setSidebarOpen(!sidebarOpen);\n  };\n\n  // Close sidebar (for mobile)\n  const closeSidebar = () => {\n    setSidebarOpen(false);\n  };\n\n  return (\n    <LayoutContainer>\n      {/* Sidebar */}\n      <SidebarContainer isOpen={sidebarOpen}>\n        <Sidebar isOpen={sidebarOpen} />\n      </SidebarContainer>\n\n      {/* Overlay for mobile */}\n      <Overlay isVisible={sidebarOpen} onClick={closeSidebar} />\n\n      {/* Main content */}\n      <ContentContainer sidebarOpen={sidebarOpen}>\n        <HeaderContainer>\n          <Header toggleSidebar={toggleSidebar} sidebarOpen={sidebarOpen} />\n        </HeaderContainer>\n\n        <MainContent>\n          <Outlet />\n        </MainContent>\n      </ContentContainer>\n\n      {/* Accessibility Controls - Available globally */}\n      <AccessibilityControls\n        onPreferencesChange={prefs => {\n          console.log('Accessibility preferences updated:', prefs);\n        }}\n      />\n    </LayoutContainer>\n  );\n};\n\nexport default MainLayout;\n", "/**\n * Loading Screen Component\n *\n * Displays a loading animation while content is being loaded.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\nconst LoadingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Spinner = styled.div`\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  border: 3px solid rgba(255, 255, 255, 0.1);\n  border-top-color: ${({ theme }) => theme.colors.primary};\n  animation: spin 1s linear infinite;\n\n  @keyframes spin {\n    to {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\nconst LoadingText = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n`;\n\n/**\n * Loading Screen Component\n */\nconst LoadingScreen: React.FC = () => {\n  return (\n    <LoadingContainer>\n      <Spinner />\n      <LoadingText>Loading...</LoadingText>\n    </LoadingContainer>\n  );\n};\n\nexport default LoadingScreen;\n", "/**\n * Application Routes\n *\n * This file defines the application routes using React Router.\n */\n\nimport React, { lazy, Suspense } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport MainLayout from '../layouts/MainLayout';\nimport LoadingScreen from '../components/molecules/LoadingScreen';\n\n// Lazy-loaded feature components for code splitting\nconst Dashboard = lazy(() => import('../features/trading-dashboard/TradingDashboard'));\nconst DailyGuide = lazy(() => import('../features/daily-guide/components/DailyGuide'));\nconst TradeJournal = lazy(() => import('../features/trade-journal/TradeJournal'));\nconst TradeAnalysis = lazy(() => import('../features/trade-analysis/TradeAnalysis'));\nconst TradeForm = lazy(() => import('../features/trade-journal/TradeForm'));\nconst Settings = lazy(() => import('../features/settings/Settings'));\nconst NotFound = lazy(() => import('../components/NotFound'));\n\n/**\n * AppRoutes Component\n *\n * Defines the application routes using React Router.\n */\nconst AppRoutes: React.FC = () => {\n  return (\n    <Suspense fallback={<LoadingScreen />}>\n      <Routes>\n        {/* Main layout routes */}\n        <Route path=\"/\" element={<MainLayout />}>\n          <Route index element={<Dashboard />} />\n          <Route path=\"daily-guide\" element={<DailyGuide />} />\n          <Route path=\"journal\" element={<TradeJournal />} />\n          <Route path=\"analysis\" element={<TradeAnalysis />} />\n          <Route path=\"trade/new\" element={<TradeForm />} />\n          <Route path=\"trade/:id\" element={<TradeForm />} />\n          <Route path=\"settings\" element={<Settings />} />\n          <Route path=\"*\" element={<NotFound />} />\n        </Route>\n\n        {/* Redirect from legacy paths */}\n        <Route path=\"/dashboard\" element={<Navigate to=\"/\" replace />} />\n      </Routes>\n    </Suspense>\n  );\n};\n\nexport default AppRoutes;\n", "/**\n * App Error Boundary\n *\n * A top-level error boundary for the entire application.\n * This is a simplified version that uses the unified error boundary approach.\n */\nimport React from 'react';\nimport { AppErrorBoundary as UnifiedAppErrorBoundary } from '@adhd-trading-dashboard/shared';\n\n/**\n * App Error Boundary Props\n */\nexport interface AppErrorBoundaryProps {\n  children: React.ReactNode;\n}\n\n/**\n * App Error Boundary\n *\n * A top-level error boundary for the entire application.\n */\nexport const AppErrorBoundary: React.FC<AppErrorBoundaryProps> = ({ children }) => {\n  const handleError = (error: Error) => {\n    // Log the error to the console\n    console.error('Application Error:', error);\n\n    // Here you could also log to an error tracking service like Sentry\n    // if (typeof window !== 'undefined' && window.Sentry) {\n    //   window.Sentry.withScope((scope) => {\n    //     scope.setTag('boundary', 'app');\n    //     window.Sentry.captureException(error);\n    //   });\n    // }\n  };\n\n  return (\n    <UnifiedAppErrorBoundary onError={handleError} name=\"Application\">\n      {children}\n    </UnifiedAppErrorBoundary>\n  );\n};\n\nexport default AppErrorBoundary;\n", "/**\n * Theme Test Panel\n *\n * Component for testing theme switching and visual consistency.\n * Displays sample components in different themes to verify styling.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useTheme } from '@adhd-trading-dashboard/shared';\n\nconst TestPanel = styled.div`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-radius: 12px;\n  padding: 16px;\n  min-width: 300px;\n  z-index: 1000;\n  box-shadow: var(--shadow-lg);\n`;\n\nconst ThemeButton = styled.button<{ isActive: boolean }>`\n  background: ${({ isActive }) => \n    isActive ? 'var(--primary-color)' : 'var(--session-card-bg)'};\n  color: ${({ isActive }) => \n    isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};\n  border: 1px solid var(--session-card-border);\n  border-radius: 6px;\n  padding: 8px 12px;\n  margin: 4px;\n  cursor: pointer;\n  font-size: 12px;\n  font-weight: 600;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: var(--primary-color);\n    color: var(--session-text-primary);\n  }\n`;\n\nconst SampleCard = styled.div`\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-left: 4px solid var(--session-card-accent);\n  border-radius: 8px;\n  padding: 12px;\n  margin: 8px 0;\n  box-shadow: var(--shadow-sm);\n`;\n\nconst SampleText = styled.div`\n  color: var(--session-text-primary);\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 4px;\n`;\n\nconst SampleSecondary = styled.div`\n  color: var(--session-text-secondary);\n  font-size: 12px;\n`;\n\nconst StatusBadge = styled.div<{ status: 'success' | 'warning' | 'error' | 'info' }>`\n  display: inline-block;\n  background: ${({ status }) => {\n    switch (status) {\n      case 'success': return 'var(--success-color)';\n      case 'warning': return 'var(--warning-color)';\n      case 'error': return 'var(--error-color)';\n      case 'info': return 'var(--info-color)';\n      default: return 'var(--session-card-border)';\n    }\n  }};\n  color: var(--session-text-primary);\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n  margin: 2px;\n`;\n\nconst CloseButton = styled.button`\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  background: none;\n  border: none;\n  color: var(--session-text-secondary);\n  cursor: pointer;\n  font-size: 16px;\n  padding: 4px;\n  \n  &:hover {\n    color: var(--session-text-primary);\n  }\n`;\n\ninterface ThemeTestPanelProps {\n  onClose: () => void;\n}\n\nexport const ThemeTestPanel: React.FC<ThemeTestPanelProps> = ({ onClose }) => {\n  const { theme, setTheme } = useTheme();\n\n  const themes = [\n    { id: 'mercedes-green', name: 'Mercedes Green' },\n    { id: 'f1-official', name: 'F1 Official' },\n    { id: 'dark', name: 'Dark' },\n  ];\n\n  return (\n    <TestPanel>\n      <CloseButton onClick={onClose}>×</CloseButton>\n      \n      <h3 style={{ color: 'var(--session-text-primary)', margin: '0 0 12px 0' }}>\n        Theme Test Panel\n      </h3>\n      \n      <div style={{ marginBottom: '16px' }}>\n        <div style={{ color: 'var(--session-text-secondary)', fontSize: '12px', marginBottom: '8px' }}>\n          Current: {theme.name}\n        </div>\n        {themes.map((t) => (\n          <ThemeButton\n            key={t.id}\n            isActive={theme.name === t.id}\n            onClick={() => setTheme(t.id)}\n          >\n            {t.name}\n          </ThemeButton>\n        ))}\n      </div>\n\n      <SampleCard>\n        <SampleText>Sample Session Card</SampleText>\n        <SampleSecondary>This shows the theme colors in action</SampleSecondary>\n        <div style={{ marginTop: '8px' }}>\n          <StatusBadge status=\"success\">SUCCESS</StatusBadge>\n          <StatusBadge status=\"warning\">WARNING</StatusBadge>\n          <StatusBadge status=\"error\">ERROR</StatusBadge>\n          <StatusBadge status=\"info\">INFO</StatusBadge>\n        </div>\n      </SampleCard>\n\n      <div style={{ \n        color: 'var(--session-text-secondary)', \n        fontSize: '10px', \n        marginTop: '12px',\n        lineHeight: '1.4'\n      }}>\n        Test theme switching to verify:\n        <br />• Clean backgrounds (no muddy layering)\n        <br />• Proper accent colors\n        <br />• High contrast text\n        <br />• Consistent borders\n      </div>\n    </TestPanel>\n  );\n};\n\nexport default ThemeTestPanel;\n", "// React import handled by <PERSON><PERSON><PERSON> transform\nimport { useState, useEffect } from 'react';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ThemeProvider } from '@adhd-trading-dashboard/shared';\nimport { AppRoutes } from './routes';\nimport AppErrorBoundary from './components/AppErrorBoundary';\nimport { ThemeTestPanel } from './components/ThemeTestPanel';\nimport './styles/unified-theme.css';\nimport './styles/components/session-cards.css';\nimport './styles/components/pd-arrays.css';\n\n/**\n * Main App component for the ADHD Trading Dashboard\n * Using BrowserRouter for better URL structure\n */\nfunction App() {\n  const [showThemeTest, setShowThemeTest] = useState(false);\n\n  // Show theme test panel on Ctrl+T\n  const handleKeyDown = (e: KeyboardEvent) => {\n    if (e.ctrlKey && e.key === 't') {\n      e.preventDefault();\n      setShowThemeTest(!showThemeTest);\n    }\n  };\n\n  // Add keyboard listener\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [showThemeTest]);\n\n  return (\n    <AppErrorBoundary>\n      <ThemeProvider initialTheme=\"mercedes-green\">\n        <BrowserRouter>\n          <AppRoutes />\n          {showThemeTest && <ThemeTestPanel onClose={() => setShowThemeTest(false)} />}\n        </BrowserRouter>\n      </ThemeProvider>\n    </AppErrorBoundary>\n  );\n}\n\nexport default App;\n", "/**\n * reportWebVitals.ts\n *\n * Web Vitals reporting utility\n */\n\nimport { ReportHandler } from \"web-vitals\";\n\nconst reportWebVitals = (onPerfEntry?: ReportHandler) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import(\"web-vitals\").then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\nexport default reportWebVitals;\n", "/**\n * ADHD Trading Dashboard - Main Entry Point\n */\n// Import DevTools configuration first\nimport './devtools-config';\n\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\nimport reportWebVitals from './reportWebVitals';\nimport './styles/variables.css';\nimport './styles/global.css';\nimport './styles/f1-theme.css';\n\n// IMPORTANT: All code must be after imports\nconst main = () => {\n  console.log('ADHD Trading Dashboard initializing...');\n\n  // Get the root element\n  const rootElement = document.getElementById('root');\n\n  // Create a fallback root element if needed\n  if (!rootElement) {\n    console.error('Root element not found, creating a fallback element');\n    const fallbackRoot = document.createElement('div');\n    fallbackRoot.id = 'root';\n    document.body.appendChild(fallbackRoot);\n  }\n\n  // Create the React root\n  const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);\n\n  // Render the app\n  root.render(\n    <React.StrictMode>\n      <App />\n    </React.StrictMode>\n  );\n\n  // Performance measurement\n  reportWebVitals();\n};\n\n// Simple error handler\nwindow.addEventListener('error', (event) => {\n  console.error('Error:', event.error || event.message);\n});\n\n// Execute the main function\nmain();\n"], "file": "assets/main-681bb6a1.js"}