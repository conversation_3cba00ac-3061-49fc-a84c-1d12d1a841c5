{"version": 3, "file": "tradeStorage-a5c0ed9a.js", "sources": ["../../../shared/src/services/tradeStorage.ts"], "sourcesContent": ["/**\n * Trade Storage Service\n *\n * This service provides methods for storing and retrieving trade data\n * using IndexedDB with the new schema structure.\n */\n\n// Import all types from centralized location\nimport { PerformanceMetrics, TradeRecord, CompleteTradeData, TradeFilters } from '../types/trading';\n\n// Typed constants for database field names\nconst TRADE_FIELDS = {\n  MODEL_TYPE: 'model_type' as const,\n  WIN_LOSS: 'win_loss' as const,\n  R_MULTIPLE: 'r_multiple' as const,\n  DATE: 'date' as const,\n  SESSION: 'session' as const,\n  DIRECTION: 'direction' as const,\n  MARKET: 'market' as const,\n  ACHIEVED_PL: 'achieved_pl' as const,\n  PATTERN_QUALITY_RATING: 'pattern_quality_rating' as const,\n} as const;\n\n/**\n * Trade Storage Service\n */\nclass TradeStorageService {\n  private dbName = 'adhd-trading-dashboard';\n  private version = 2; // Increment version for schema changes\n  private db: IDBDatabase | null = null;\n\n  // Store names for different tables\n  private stores = {\n    trades: 'trades',\n    fvg_details: 'trade_fvg_details',\n    setups: 'trade_setups',\n    analysis: 'trade_analysis',\n    sessions: 'trading_sessions',\n  };\n\n  /**\n   * Initialize the database with new schema\n   * @returns A promise that resolves when the database is initialized\n   */\n  private async initDB(): Promise<IDBDatabase> {\n    if (this.db) {\n      return this.db;\n    }\n\n    return new Promise((resolve, reject) => {\n      const request = indexedDB.open(this.dbName, this.version);\n\n      request.onupgradeneeded = (event) => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // Create trades store\n        if (!db.objectStoreNames.contains(this.stores.trades)) {\n          const tradesStore = db.createObjectStore(this.stores.trades, {\n            keyPath: 'id',\n            autoIncrement: true,\n          });\n\n          // Create indexes for performance\n          tradesStore.createIndex(TRADE_FIELDS.DATE, TRADE_FIELDS.DATE, { unique: false });\n          tradesStore.createIndex(TRADE_FIELDS.MODEL_TYPE, TRADE_FIELDS.MODEL_TYPE, {\n            unique: false,\n          });\n          tradesStore.createIndex(TRADE_FIELDS.SESSION, TRADE_FIELDS.SESSION, { unique: false });\n          tradesStore.createIndex(TRADE_FIELDS.WIN_LOSS, TRADE_FIELDS.WIN_LOSS, { unique: false });\n          tradesStore.createIndex(TRADE_FIELDS.R_MULTIPLE, TRADE_FIELDS.R_MULTIPLE, {\n            unique: false,\n          });\n        }\n\n        // Create FVG details store\n        if (!db.objectStoreNames.contains(this.stores.fvg_details)) {\n          const fvgStore = db.createObjectStore(this.stores.fvg_details, {\n            keyPath: 'id',\n            autoIncrement: true,\n          });\n          fvgStore.createIndex('trade_id', 'trade_id', { unique: false });\n        }\n\n        // Create setups store\n        if (!db.objectStoreNames.contains(this.stores.setups)) {\n          const setupsStore = db.createObjectStore(this.stores.setups, {\n            keyPath: 'id',\n            autoIncrement: true,\n          });\n          setupsStore.createIndex('trade_id', 'trade_id', { unique: false });\n        }\n\n        // Create analysis store\n        if (!db.objectStoreNames.contains(this.stores.analysis)) {\n          const analysisStore = db.createObjectStore(this.stores.analysis, {\n            keyPath: 'id',\n            autoIncrement: true,\n          });\n          analysisStore.createIndex('trade_id', 'trade_id', { unique: false });\n        }\n\n        // Create sessions store\n        if (!db.objectStoreNames.contains(this.stores.sessions)) {\n          const sessionsStore = db.createObjectStore(this.stores.sessions, {\n            keyPath: 'id',\n            autoIncrement: true,\n          });\n          sessionsStore.createIndex('name', 'name', { unique: true });\n\n          // Pre-populate with common sessions\n          const commonSessions = [\n            {\n              name: 'Pre-Market',\n              start_time: '04:00:00',\n              end_time: '09:30:00',\n              description: 'Pre-market trading hours',\n            },\n            {\n              name: 'NY Open',\n              start_time: '09:30:00',\n              end_time: '10:30:00',\n              description: 'New York opening hour',\n            },\n            {\n              name: '10:50-11:10',\n              start_time: '10:50:00',\n              end_time: '11:10:00',\n              description: 'Mid-morning macro window',\n            },\n            {\n              name: '11:50-12:10',\n              start_time: '11:50:00',\n              end_time: '12:10:00',\n              description: 'Pre-lunch macro window',\n            },\n            {\n              name: 'Lunch Macro',\n              start_time: '12:00:00',\n              end_time: '13:30:00',\n              description: 'Lunch time trading',\n            },\n            {\n              name: '13:50-14:10',\n              start_time: '13:50:00',\n              end_time: '14:10:00',\n              description: 'Post-lunch macro window',\n            },\n            {\n              name: '14:50-15:10',\n              start_time: '14:50:00',\n              end_time: '15:10:00',\n              description: 'Pre-close macro window',\n            },\n            {\n              name: '15:15-15:45',\n              start_time: '15:15:00',\n              end_time: '15:45:00',\n              description: 'Late afternoon window',\n            },\n            {\n              name: 'MOC',\n              start_time: '15:45:00',\n              end_time: '16:00:00',\n              description: 'Market on close',\n            },\n            {\n              name: 'Post MOC',\n              start_time: '16:00:00',\n              end_time: '20:00:00',\n              description: 'After hours trading',\n            },\n          ];\n\n          // Add sessions after store is created\n          request.transaction?.addEventListener('complete', () => {\n            const transaction = db.transaction([this.stores.sessions], 'readwrite');\n            const store = transaction.objectStore(this.stores.sessions);\n            commonSessions.forEach((session) => store.add(session));\n          });\n        }\n      };\n\n      request.onsuccess = (event) => {\n        this.db = (event.target as IDBOpenDBRequest).result;\n        resolve(this.db);\n      };\n\n      request.onerror = (event) => {\n        console.error('Error opening IndexedDB:', event);\n        reject(new Error('Failed to open IndexedDB'));\n      };\n    });\n  }\n\n  /**\n   * Save a complete trade with all related details\n   * @param tradeData Complete trade data including all related tables\n   * @returns A promise that resolves with the saved trade ID\n   */\n  async saveTradeWithDetails(tradeData: CompleteTradeData): Promise<number> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(\n          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],\n          'readwrite'\n        );\n\n        transaction.onerror = (event) => {\n          console.error('Transaction error:', event);\n          reject(new Error('Failed to save trade with details'));\n        };\n\n        // Save main trade record first\n        const tradesStore = transaction.objectStore(this.stores.trades);\n        const tradeRecord = {\n          ...tradeData.trade,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString(),\n        };\n\n        const tradeRequest = tradesStore.add(tradeRecord);\n\n        tradeRequest.onsuccess = () => {\n          const tradeId = tradeRequest.result as number;\n\n          // Save related data with the trade ID\n          const promises: Promise<void>[] = [];\n\n          if (tradeData.fvg_details) {\n            const fvgStore = transaction.objectStore(this.stores.fvg_details);\n            const fvgData = { ...tradeData.fvg_details, trade_id: tradeId };\n            promises.push(\n              new Promise((res, rej) => {\n                const req = fvgStore.add(fvgData);\n                req.onsuccess = () => res();\n                req.onerror = () => rej(new Error('Failed to save FVG details'));\n              })\n            );\n          }\n\n          if (tradeData.setup) {\n            const setupStore = transaction.objectStore(this.stores.setups);\n            const setupData = { ...tradeData.setup, trade_id: tradeId };\n            promises.push(\n              new Promise((res, rej) => {\n                const req = setupStore.add(setupData);\n                req.onsuccess = () => res();\n                req.onerror = () => rej(new Error('Failed to save setup data'));\n              })\n            );\n          }\n\n          if (tradeData.analysis) {\n            const analysisStore = transaction.objectStore(this.stores.analysis);\n            const analysisData = { ...tradeData.analysis, trade_id: tradeId };\n            promises.push(\n              new Promise((res, rej) => {\n                const req = analysisStore.add(analysisData);\n                req.onsuccess = () => res();\n                req.onerror = () => rej(new Error('Failed to save analysis data'));\n              })\n            );\n          }\n\n          transaction.oncomplete = () => {\n            resolve(tradeId);\n          };\n        };\n\n        tradeRequest.onerror = (event) => {\n          console.error('Error saving trade:', event);\n          reject(new Error('Failed to save trade'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in saveTradeWithDetails:', error);\n      throw new Error('Failed to save trade with details');\n    }\n  }\n\n  /**\n   * Get a complete trade by ID with all related data\n   * @param id The ID of the trade to get\n   * @returns A promise that resolves with the complete trade data\n   */\n  async getTradeById(id: number): Promise<CompleteTradeData | null> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(\n          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],\n          'readonly'\n        );\n\n        const tradesStore = transaction.objectStore(this.stores.trades);\n        const tradeRequest = tradesStore.get(id);\n\n        tradeRequest.onsuccess = () => {\n          const trade = tradeRequest.result;\n          if (!trade) {\n            resolve(null);\n            return;\n          }\n\n          const result: CompleteTradeData = { trade };\n\n          // Get FVG details\n          const fvgStore = transaction.objectStore(this.stores.fvg_details);\n          const fvgIndex = fvgStore.index('trade_id');\n          const fvgRequest = fvgIndex.get(id);\n\n          fvgRequest.onsuccess = () => {\n            if (fvgRequest.result) {\n              result.fvg_details = fvgRequest.result;\n            }\n\n            // Get setup data\n            const setupStore = transaction.objectStore(this.stores.setups);\n            const setupIndex = setupStore.index('trade_id');\n            const setupRequest = setupIndex.get(id);\n\n            setupRequest.onsuccess = () => {\n              if (setupRequest.result) {\n                result.setup = setupRequest.result;\n              }\n\n              // Get analysis data\n              const analysisStore = transaction.objectStore(this.stores.analysis);\n              const analysisIndex = analysisStore.index('trade_id');\n              const analysisRequest = analysisIndex.get(id);\n\n              analysisRequest.onsuccess = () => {\n                if (analysisRequest.result) {\n                  result.analysis = analysisRequest.result;\n                }\n                resolve(result);\n              };\n\n              analysisRequest.onerror = (event) => {\n                console.error('Error getting analysis data:', event);\n                resolve(result); // Return partial data\n              };\n            };\n\n            setupRequest.onerror = (event) => {\n              console.error('Error getting setup data:', event);\n              resolve(result); // Return partial data\n            };\n          };\n\n          fvgRequest.onerror = (event) => {\n            console.error('Error getting FVG details:', event);\n            resolve(result); // Return partial data\n          };\n        };\n\n        tradeRequest.onerror = (event) => {\n          console.error('Error getting trade:', event);\n          reject(new Error('Failed to get trade'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in getTradeById:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get performance metrics from all trades\n   * @returns A promise that resolves with performance metrics\n   */\n  async getPerformanceMetrics(): Promise<PerformanceMetrics> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction([this.stores.trades], 'readonly');\n        const store = transaction.objectStore(this.stores.trades);\n        const request = store.getAll();\n\n        request.onsuccess = () => {\n          const trades: TradeRecord[] = request.result;\n\n          if (trades.length === 0) {\n            resolve({\n              totalTrades: 0,\n              winningTrades: 0,\n              losingTrades: 0,\n              winRate: 0,\n              profitFactor: 0,\n              averageWin: 0,\n              averageLoss: 0,\n              largestWin: 0,\n              largestLoss: 0,\n              totalPnl: 0,\n              maxDrawdown: 0,\n              maxDrawdownPercent: 0,\n              sharpeRatio: 0,\n              sortinoRatio: 0,\n              calmarRatio: 0,\n              averageRMultiple: 0,\n              expectancy: 0,\n              sqn: 0,\n              period: 'all',\n              startDate: '',\n              endDate: '',\n            });\n            return;\n          }\n\n          // Calculate basic metrics\n          const totalTrades = trades.length;\n          const winningTrades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === 'Win').length;\n          const losingTrades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === 'Loss').length;\n          const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n\n          // Calculate P&L metrics\n          const pnlValues = trades\n            .filter((t) => t.achieved_pl !== undefined)\n            .map((t) => t.achieved_pl!);\n          const totalPnl = pnlValues.reduce((sum, pnl) => sum + pnl, 0);\n\n          const wins = pnlValues.filter((pnl) => pnl > 0);\n          const losses = pnlValues.filter((pnl) => pnl < 0);\n\n          const averageWin =\n            wins.length > 0 ? wins.reduce((sum, win) => sum + win, 0) / wins.length : 0;\n          const averageLoss =\n            losses.length > 0\n              ? Math.abs(losses.reduce((sum, loss) => sum + loss, 0) / losses.length)\n              : 0;\n          const largestWin = wins.length > 0 ? Math.max(...wins) : 0;\n          const largestLoss = losses.length > 0 ? Math.abs(Math.min(...losses)) : 0;\n\n          const grossProfit = wins.reduce((sum, win) => sum + win, 0);\n          const grossLoss = Math.abs(losses.reduce((sum, loss) => sum + loss, 0));\n          const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : 0;\n\n          // Calculate R-multiple metrics\n          const rMultiples = trades\n            .filter((t) => t[TRADE_FIELDS.R_MULTIPLE] !== undefined)\n            .map((t) => t[TRADE_FIELDS.R_MULTIPLE]!);\n          const averageRMultiple =\n            rMultiples.length > 0\n              ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length\n              : 0;\n          const expectancy = averageRMultiple * (winRate / 100);\n\n          // Calculate drawdown (simplified)\n          let runningPnl = 0;\n          let peak = 0;\n          let maxDrawdown = 0;\n\n          for (const trade of trades) {\n            if (trade.achieved_pl !== undefined) {\n              runningPnl += trade.achieved_pl;\n              if (runningPnl > peak) {\n                peak = runningPnl;\n              }\n              const drawdown = peak - runningPnl;\n              if (drawdown > maxDrawdown) {\n                maxDrawdown = drawdown;\n              }\n            }\n          }\n\n          const maxDrawdownPercent = peak > 0 ? (maxDrawdown / peak) * 100 : 0;\n\n          // Calculate SQN (System Quality Number)\n          const sqn =\n            rMultiples.length > 0\n              ? (Math.sqrt(rMultiples.length) * averageRMultiple) /\n                Math.sqrt(\n                  rMultiples.reduce((sum, r) => sum + Math.pow(r - averageRMultiple, 2), 0) /\n                    rMultiples.length\n                )\n              : 0;\n\n          // Get date range\n          const dates = trades.map((t) => t.date).sort();\n          const startDate = dates.length > 0 ? dates[0] : '';\n          const endDate = dates.length > 0 ? dates[dates.length - 1] : '';\n\n          resolve({\n            totalTrades,\n            winningTrades,\n            losingTrades,\n            winRate,\n            profitFactor,\n            averageWin,\n            averageLoss,\n            largestWin,\n            largestLoss,\n            totalPnl,\n            maxDrawdown,\n            maxDrawdownPercent,\n            sharpeRatio: 0, // Would need daily returns to calculate\n            sortinoRatio: 0, // Would need daily returns to calculate\n            calmarRatio: 0, // Would need daily returns to calculate\n            averageRMultiple,\n            expectancy,\n            sqn,\n            period: 'all',\n            startDate,\n            endDate,\n          });\n        };\n\n        request.onerror = (event) => {\n          console.error('Error getting performance metrics:', event);\n          reject(new Error('Failed to get performance metrics'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in getPerformanceMetrics:', error);\n      throw new Error('Failed to get performance metrics');\n    }\n  }\n\n  /**\n   * Filter trades based on criteria\n   * @param filters The filter criteria\n   * @returns A promise that resolves with filtered trades\n   */\n  async filterTrades(filters: TradeFilters): Promise<CompleteTradeData[]> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(\n          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],\n          'readonly'\n        );\n\n        const tradesStore = transaction.objectStore(this.stores.trades);\n        const request = tradesStore.getAll();\n\n        request.onsuccess = async () => {\n          let trades: TradeRecord[] = request.result;\n\n          // Apply filters\n          if (filters.dateFrom) {\n            trades = trades.filter((t) => t.date >= filters.dateFrom!);\n          }\n          if (filters.dateTo) {\n            trades = trades.filter((t) => t.date <= filters.dateTo!);\n          }\n          if (filters.model_type) {\n            trades = trades.filter((t) => t[TRADE_FIELDS.MODEL_TYPE] === filters.model_type);\n          }\n          if (filters.session) {\n            trades = trades.filter((t) => t[TRADE_FIELDS.SESSION] === filters.session);\n          }\n          if (filters.direction) {\n            trades = trades.filter((t) => t[TRADE_FIELDS.DIRECTION] === filters.direction);\n          }\n          if (filters.win_loss) {\n            trades = trades.filter((t) => t[TRADE_FIELDS.WIN_LOSS] === filters.win_loss);\n          }\n          if (filters.market) {\n            trades = trades.filter((t) => t[TRADE_FIELDS.MARKET] === filters.market);\n          }\n          if (filters.min_r_multiple !== undefined) {\n            trades = trades.filter(\n              (t) =>\n                t[TRADE_FIELDS.R_MULTIPLE] !== undefined &&\n                t[TRADE_FIELDS.R_MULTIPLE]! >= filters.min_r_multiple!\n            );\n          }\n          if (filters.max_r_multiple !== undefined) {\n            trades = trades.filter(\n              (t) =>\n                t[TRADE_FIELDS.R_MULTIPLE] !== undefined &&\n                t[TRADE_FIELDS.R_MULTIPLE]! <= filters.max_r_multiple!\n            );\n          }\n          if (filters.min_pattern_quality !== undefined) {\n            trades = trades.filter(\n              (t) =>\n                t[TRADE_FIELDS.PATTERN_QUALITY_RATING] !== undefined &&\n                t[TRADE_FIELDS.PATTERN_QUALITY_RATING]! >= filters.min_pattern_quality!\n            );\n          }\n          if (filters.max_pattern_quality !== undefined) {\n            trades = trades.filter(\n              (t) =>\n                t[TRADE_FIELDS.PATTERN_QUALITY_RATING] !== undefined &&\n                t[TRADE_FIELDS.PATTERN_QUALITY_RATING]! <= filters.max_pattern_quality!\n            );\n          }\n\n          // Get related data for each filtered trade\n          const results: CompleteTradeData[] = [];\n\n          for (const trade of trades) {\n            const completeData: CompleteTradeData = { trade };\n\n            // Get FVG details\n            const fvgStore = transaction.objectStore(this.stores.fvg_details);\n            const fvgIndex = fvgStore.index('trade_id');\n            const fvgRequest = fvgIndex.get(trade.id!);\n\n            await new Promise<void>((res) => {\n              fvgRequest.onsuccess = () => {\n                if (fvgRequest.result) {\n                  completeData.fvg_details = fvgRequest.result;\n                }\n                res();\n              };\n              fvgRequest.onerror = () => res();\n            });\n\n            // Get setup data\n            const setupStore = transaction.objectStore(this.stores.setups);\n            const setupIndex = setupStore.index('trade_id');\n            const setupRequest = setupIndex.get(trade.id!);\n\n            await new Promise<void>((res) => {\n              setupRequest.onsuccess = () => {\n                if (setupRequest.result) {\n                  completeData.setup = setupRequest.result;\n                }\n                res();\n              };\n              setupRequest.onerror = () => res();\n            });\n\n            // Get analysis data\n            const analysisStore = transaction.objectStore(this.stores.analysis);\n            const analysisIndex = analysisStore.index('trade_id');\n            const analysisRequest = analysisIndex.get(trade.id!);\n\n            await new Promise<void>((res) => {\n              analysisRequest.onsuccess = () => {\n                if (analysisRequest.result) {\n                  completeData.analysis = analysisRequest.result;\n                }\n                res();\n              };\n              analysisRequest.onerror = () => res();\n            });\n\n            results.push(completeData);\n          }\n\n          resolve(results);\n        };\n\n        request.onerror = (event) => {\n          console.error('Error filtering trades:', event);\n          reject(new Error('Failed to filter trades'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in filterTrades:', error);\n      throw new Error('Failed to filter trades');\n    }\n  }\n\n  /**\n   * Get all trades (simplified version for backward compatibility)\n   * @returns A promise that resolves with all trades\n   */\n  async getAllTrades(): Promise<CompleteTradeData[]> {\n    try {\n      return await this.filterTrades({});\n    } catch (error) {\n      console.error('Error in getAllTrades:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Delete a trade and all related data\n   * @param id The ID of the trade to delete\n   * @returns A promise that resolves when the trade is deleted\n   */\n  async deleteTrade(id: number): Promise<void> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(\n          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],\n          'readwrite'\n        );\n\n        transaction.onerror = (event) => {\n          console.error('Transaction error:', event);\n          reject(new Error('Failed to delete trade'));\n        };\n\n        // Delete from all related tables (foreign key cascade simulation)\n        const fvgStore = transaction.objectStore(this.stores.fvg_details);\n        const fvgIndex = fvgStore.index('trade_id');\n        const fvgRequest = fvgIndex.openCursor(IDBKeyRange.only(id));\n\n        fvgRequest.onsuccess = (event) => {\n          const cursor = (event.target as IDBRequest).result;\n          if (cursor) {\n            cursor.delete();\n            cursor.continue();\n          }\n        };\n\n        const setupStore = transaction.objectStore(this.stores.setups);\n        const setupIndex = setupStore.index('trade_id');\n        const setupRequest = setupIndex.openCursor(IDBKeyRange.only(id));\n\n        setupRequest.onsuccess = (event) => {\n          const cursor = (event.target as IDBRequest).result;\n          if (cursor) {\n            cursor.delete();\n            cursor.continue();\n          }\n        };\n\n        const analysisStore = transaction.objectStore(this.stores.analysis);\n        const analysisIndex = analysisStore.index('trade_id');\n        const analysisRequest = analysisIndex.openCursor(IDBKeyRange.only(id));\n\n        analysisRequest.onsuccess = (event) => {\n          const cursor = (event.target as IDBRequest).result;\n          if (cursor) {\n            cursor.delete();\n            cursor.continue();\n          }\n        };\n\n        // Delete main trade record\n        const tradesStore = transaction.objectStore(this.stores.trades);\n        const tradeRequest = tradesStore.delete(id);\n\n        transaction.oncomplete = () => {\n          resolve();\n        };\n\n        tradeRequest.onerror = (event) => {\n          console.error('Error deleting trade:', event);\n          reject(new Error('Failed to delete trade'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in deleteTrade:', error);\n      throw new Error('Failed to delete trade');\n    }\n  }\n\n  /**\n   * Update a trade with all related data\n   * @param id The trade ID to update\n   * @param tradeData Updated trade data\n   * @returns A promise that resolves when the trade is updated\n   */\n  async updateTradeWithDetails(id: number, tradeData: CompleteTradeData): Promise<void> {\n    try {\n      const db = await this.initDB();\n      return new Promise((resolve, reject) => {\n        const transaction = db.transaction(\n          [this.stores.trades, this.stores.fvg_details, this.stores.setups, this.stores.analysis],\n          'readwrite'\n        );\n\n        transaction.onerror = (event) => {\n          console.error('Transaction error:', event);\n          reject(new Error('Failed to update trade'));\n        };\n\n        // Update main trade record\n        const tradesStore = transaction.objectStore(this.stores.trades);\n        const updatedTrade = {\n          ...tradeData.trade,\n          id,\n          updated_at: new Date().toISOString(),\n        };\n\n        const tradeRequest = tradesStore.put(updatedTrade);\n\n        tradeRequest.onsuccess = () => {\n          // Update or create related data\n          if (tradeData.fvg_details) {\n            const fvgStore = transaction.objectStore(this.stores.fvg_details);\n            const fvgData = { ...tradeData.fvg_details, trade_id: id };\n            fvgStore.put(fvgData);\n          }\n\n          if (tradeData.setup) {\n            const setupStore = transaction.objectStore(this.stores.setups);\n            const setupData = { ...tradeData.setup, trade_id: id };\n            setupStore.put(setupData);\n          }\n\n          if (tradeData.analysis) {\n            const analysisStore = transaction.objectStore(this.stores.analysis);\n            const analysisData = { ...tradeData.analysis, trade_id: id };\n            analysisStore.put(analysisData);\n          }\n        };\n\n        transaction.oncomplete = () => {\n          resolve();\n        };\n\n        tradeRequest.onerror = (event) => {\n          console.error('Error updating trade:', event);\n          reject(new Error('Failed to update trade'));\n        };\n      });\n    } catch (error) {\n      console.error('Error in updateTradeWithDetails:', error);\n      throw new Error('Failed to update trade');\n    }\n  }\n}\n\n// Create a singleton instance\nexport const tradeStorage = new TradeStorageService();\n\n// Export the singleton as tradeStorageService for consistency\nexport const tradeStorageService = tradeStorage;\n\n// Re-export types for convenience\nexport type {\n  CompleteTradeData,\n  TradeFilters,\n  TradeRecord,\n  TradeFvgDetails,\n  TradeSetup,\n  TradeAnalysisRecord,\n  PerformanceMetrics,\n  Trade, // Legacy interface for backward compatibility\n} from '../types/trading';\n\nexport default tradeStorage;\n"], "names": ["TRADE_FIELDS", "MODEL_TYPE", "WIN_LOSS", "R_MULTIPLE", "DATE", "SESSION", "DIRECTION", "MARKET", "ACHIEVED_PL", "PATTERN_QUALITY_RATING", "TradeStorageService", "dbN<PERSON>", "version", "db", "stores", "trades", "fvg_details", "setups", "analysis", "sessions", "initDB", "Promise", "resolve", "reject", "request", "indexedDB", "open", "onupgradeneeded", "event", "_a", "target", "result", "objectStoreNames", "contains", "tradesStore", "createObjectStore", "keyP<PERSON>", "autoIncrement", "createIndex", "unique", "commonSessions", "name", "start_time", "end_time", "description", "transaction", "addEventListener", "store", "objectStore", "for<PERSON>ach", "session", "add", "onsuccess", "onerror", "error", "Error", "saveTradeWithDetails", "tradeData", "tradeRecord", "trade", "created_at", "Date", "toISOString", "updated_at", "tradeRequest", "tradeId", "promises", "fvgStore", "fvgData", "trade_id", "push", "res", "rej", "req", "setup", "setupStore", "setupData", "analysisStore", "analysisData", "oncomplete", "getTradeById", "id", "get", "fvgRequest", "index", "setupRequest", "analysisRequest", "getPerformanceMetrics", "getAll", "length", "totalTrades", "winningTrades", "losingTrades", "winRate", "profitFactor", "averageWin", "averageLoss", "largestWin", "largestLoss", "totalPnl", "maxDrawdown", "maxDrawdownPercent", "sharpeRatio", "sortinoRatio", "calmarRatio", "averageRMultiple", "expectancy", "sqn", "period", "startDate", "endDate", "filter", "t", "pnlValues", "achieved_pl", "undefined", "map", "reduce", "sum", "pnl", "wins", "losses", "win", "Math", "abs", "loss", "max", "min", "grossProfit", "grossLoss", "r<PERSON><PERSON><PERSON><PERSON>", "r", "runningPnl", "peak", "drawdown", "sqrt", "pow", "dates", "date", "sort", "filterTrades", "filters", "dateFrom", "dateTo", "model_type", "direction", "win_loss", "market", "min_r_multiple", "max_r_multiple", "min_pattern_quality", "max_pattern_quality", "results", "completeData", "getAllTrades", "deleteTrade", "openCursor", "IDBKeyRange", "only", "cursor", "delete", "continue", "updateTradeWithDetails", "updatedTrade", "put", "tradeStorage", "tradeStorageService"], "mappings": "wKAWA,MAAMA,EAAe,CACnBC,WAAY,aACZC,SAAU,WACVC,WAAY,aACZC,KAAM,OACNC,QAAS,UACTC,UAAW,YACXC,OAAQ,SACRC,YAAa,cACbC,uBAAwB,wBAC1B,EAKA,MAAMC,CAAoB,CAA1B,cACUC,EAAAA,cAAS,0BACTC,EAAAA,eAAU,GACVC,EAAAA,UAAyB,MAGzBC,EAAAA,cAAS,CACfC,OAAQ,SACRC,YAAa,oBACbC,OAAQ,eACRC,SAAU,iBACVC,SAAU,kBAAA,GAOZ,MAAcC,QAA+B,CAC3C,OAAI,KAAKP,GACA,KAAKA,GAGP,IAAIQ,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMC,EAAUC,UAAUC,KAAK,KAAKf,OAAQ,KAAKC,OAAO,EAExDY,EAAQG,gBAA6BC,GAAA,CAzC3C,IAAAC,EA0CchB,MAAAA,EAAMe,EAAME,OAA4BC,OAG9C,GAAI,CAAClB,EAAGmB,iBAAiBC,SAAS,KAAKnB,OAAOC,MAAM,EAAG,CACrD,MAAMmB,EAAcrB,EAAGsB,kBAAkB,KAAKrB,OAAOC,OAAQ,CAC3DqB,QAAS,KACTC,cAAe,EAAA,CAChB,EAGDH,EAAYI,YAAYtC,EAAaI,KAAMJ,EAAaI,KAAM,CAAEmC,OAAQ,EAAA,CAAO,EAC/EL,EAAYI,YAAYtC,EAAaC,WAAYD,EAAaC,WAAY,CACxEsC,OAAQ,EAAA,CACT,EACDL,EAAYI,YAAYtC,EAAaK,QAASL,EAAaK,QAAS,CAAEkC,OAAQ,EAAA,CAAO,EACrFL,EAAYI,YAAYtC,EAAaE,SAAUF,EAAaE,SAAU,CAAEqC,OAAQ,EAAA,CAAO,EACvFL,EAAYI,YAAYtC,EAAaG,WAAYH,EAAaG,WAAY,CACxEoC,OAAQ,EAAA,CACT,EA+BH,GA3BK1B,EAAGmB,iBAAiBC,SAAS,KAAKnB,OAAOE,WAAW,GACtCH,EAAGsB,kBAAkB,KAAKrB,OAAOE,YAAa,CAC7DoB,QAAS,KACTC,cAAe,EAAA,CAChB,EACQC,YAAY,WAAY,WAAY,CAAEC,OAAQ,EAAA,CAAO,EAI3D1B,EAAGmB,iBAAiBC,SAAS,KAAKnB,OAAOG,MAAM,GAC9BJ,EAAGsB,kBAAkB,KAAKrB,OAAOG,OAAQ,CAC3DmB,QAAS,KACTC,cAAe,EAAA,CAChB,EACWC,YAAY,WAAY,WAAY,CAAEC,OAAQ,EAAA,CAAO,EAI9D1B,EAAGmB,iBAAiBC,SAAS,KAAKnB,OAAOI,QAAQ,GAC9BL,EAAGsB,kBAAkB,KAAKrB,OAAOI,SAAU,CAC/DkB,QAAS,KACTC,cAAe,EAAA,CAChB,EACaC,YAAY,WAAY,WAAY,CAAEC,OAAQ,EAAA,CAAO,EAIjE,CAAC1B,EAAGmB,iBAAiBC,SAAS,KAAKnB,OAAOK,QAAQ,EAAG,CACjCN,EAAGsB,kBAAkB,KAAKrB,OAAOK,SAAU,CAC/DiB,QAAS,KACTC,cAAe,EAAA,CAChB,EACaC,YAAY,OAAQ,OAAQ,CAAEC,OAAQ,EAAA,CAAM,EAG1D,MAAMC,EAAiB,CACrB,CACEC,KAAM,aACNC,WAAY,WACZC,SAAU,WACVC,YAAa,0BAAA,EAEf,CACEH,KAAM,UACNC,WAAY,WACZC,SAAU,WACVC,YAAa,uBAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,0BAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,wBAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,oBAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,yBAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,wBAAA,EAEf,CACEH,KAAM,cACNC,WAAY,WACZC,SAAU,WACVC,YAAa,uBAAA,EAEf,CACEH,KAAM,MACNC,WAAY,WACZC,SAAU,WACVC,YAAa,iBAAA,EAEf,CACEH,KAAM,WACNC,WAAY,WACZC,SAAU,WACVC,YAAa,qBAAA,CACd,GAIKC,EAAAA,EAAAA,cAAAA,MAAAA,EAAaC,iBAAiB,WAAY,IAAM,CAEtD,MAAMC,EADclC,EAAGgC,YAAY,CAAC,KAAK/B,OAAOK,QAAQ,EAAG,WAAW,EAC5C6B,YAAY,KAAKlC,OAAOK,QAAQ,EAC1DqB,EAAeS,QAASC,GAAYH,EAAMI,IAAID,CAAO,CAAC,CAAA,GAE1D,EAGF1B,EAAQ4B,UAAuBxB,GAAA,CACxBf,KAAAA,GAAMe,EAAME,OAA4BC,OAC7CT,EAAQ,KAAKT,EAAE,CAAA,EAGjBW,EAAQ6B,QAAqBzB,GAAA,CACnB0B,QAAAA,MAAM,2BAA4B1B,CAAK,EACxCL,EAAA,IAAIgC,MAAM,0BAA0B,CAAC,CAAA,CAC9C,CACD,CACH,CAOA,MAAMC,qBAAqBC,EAA+C,CACpE,GAAA,CACI5C,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMsB,EAAchC,EAAGgC,YACrB,CAAC,KAAK/B,OAAOC,OAAQ,KAAKD,OAAOE,YAAa,KAAKF,OAAOG,OAAQ,KAAKH,OAAOI,QAAQ,EACtF,WACF,EAEA2B,EAAYQ,QAAqBzB,GAAA,CACvB0B,QAAAA,MAAM,qBAAsB1B,CAAK,EAClCL,EAAA,IAAIgC,MAAM,mCAAmC,CAAC,CAAA,EAIvD,MAAMrB,EAAcW,EAAYG,YAAY,KAAKlC,OAAOC,MAAM,EACxD2C,EAAc,CAClB,GAAGD,EAAUE,MACbC,WAAY,IAAIC,KAAK,EAAEC,YAAY,EACnCC,WAAY,IAAIF,KAAK,EAAEC,YAAY,CAAA,EAG/BE,EAAe9B,EAAYiB,IAAIO,CAAW,EAEhDM,EAAaZ,UAAY,IAAM,CAC7B,MAAMa,EAAUD,EAAajC,OAGvBmC,EAA4B,CAAA,EAElC,GAAIT,EAAUzC,YAAa,CACzB,MAAMmD,EAAWtB,EAAYG,YAAY,KAAKlC,OAAOE,WAAW,EAC1DoD,EAAU,CAAE,GAAGX,EAAUzC,YAAaqD,SAAUJ,CAAAA,EACtDC,EAASI,KACP,IAAIjD,QAAQ,CAACkD,EAAKC,IAAQ,CAClBC,MAAAA,EAAMN,EAAShB,IAAIiB,CAAO,EAC5BhB,EAAAA,UAAY,IAAMmB,IACtBE,EAAIpB,QAAU,IAAMmB,EAAI,IAAIjB,MAAM,4BAA4B,CAAC,CAChE,CAAA,CACH,EAGF,GAAIE,EAAUiB,MAAO,CACnB,MAAMC,EAAa9B,EAAYG,YAAY,KAAKlC,OAAOG,MAAM,EACvD2D,EAAY,CAAE,GAAGnB,EAAUiB,MAAOL,SAAUJ,CAAAA,EAClDC,EAASI,KACP,IAAIjD,QAAQ,CAACkD,EAAKC,IAAQ,CAClBC,MAAAA,EAAME,EAAWxB,IAAIyB,CAAS,EAChCxB,EAAAA,UAAY,IAAMmB,IACtBE,EAAIpB,QAAU,IAAMmB,EAAI,IAAIjB,MAAM,2BAA2B,CAAC,CAC/D,CAAA,CACH,EAGF,GAAIE,EAAUvC,SAAU,CACtB,MAAM2D,EAAgBhC,EAAYG,YAAY,KAAKlC,OAAOI,QAAQ,EAC5D4D,EAAe,CAAE,GAAGrB,EAAUvC,SAAUmD,SAAUJ,CAAAA,EACxDC,EAASI,KACP,IAAIjD,QAAQ,CAACkD,EAAKC,IAAQ,CAClBC,MAAAA,EAAMI,EAAc1B,IAAI2B,CAAY,EACtC1B,EAAAA,UAAY,IAAMmB,IACtBE,EAAIpB,QAAU,IAAMmB,EAAI,IAAIjB,MAAM,8BAA8B,CAAC,CAClE,CAAA,CACH,EAGFV,EAAYkC,WAAa,IAAM,CAC7BzD,EAAQ2C,CAAO,CAAA,CACjB,EAGFD,EAAaX,QAAqBzB,GAAA,CACxB0B,QAAAA,MAAM,sBAAuB1B,CAAK,EACnCL,EAAA,IAAIgC,MAAM,sBAAsB,CAAC,CAAA,CAC1C,CACD,QACMD,GACCA,cAAAA,MAAM,iCAAkCA,CAAK,EAC/C,IAAIC,MAAM,mCAAmC,CACrD,CACF,CAOA,MAAMyB,aAAaC,EAA+C,CAC5D,GAAA,CACIpE,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMsB,EAAchC,EAAGgC,YACrB,CAAC,KAAK/B,OAAOC,OAAQ,KAAKD,OAAOE,YAAa,KAAKF,OAAOG,OAAQ,KAAKH,OAAOI,QAAQ,EACtF,UACF,EAGM8C,EADcnB,EAAYG,YAAY,KAAKlC,OAAOC,MAAM,EAC7BmE,IAAID,CAAE,EAEvCjB,EAAaZ,UAAY,IAAM,CAC7B,MAAMO,EAAQK,EAAajC,OAC3B,GAAI,CAAC4B,EAAO,CACVrC,EAAQ,IAAI,EACZ,OAGF,MAAMS,EAA4B,CAAE4B,MAAAA,CAAAA,EAK9BwB,EAFWtC,EAAYG,YAAY,KAAKlC,OAAOE,WAAW,EACtCoE,MAAM,UAAU,EACdF,IAAID,CAAE,EAElCE,EAAW/B,UAAY,IAAM,CACvB+B,EAAWpD,SACbA,EAAOf,YAAcmE,EAAWpD,QAM5BsD,MAAAA,EAFaxC,EAAYG,YAAY,KAAKlC,OAAOG,MAAM,EAC/BmE,MAAM,UAAU,EACdF,IAAID,CAAE,EAEtCI,EAAajC,UAAY,IAAM,CACzBiC,EAAatD,SACfA,EAAO2C,MAAQW,EAAatD,QAMxBuD,MAAAA,EAFgBzC,EAAYG,YAAY,KAAKlC,OAAOI,QAAQ,EAC9BkE,MAAM,UAAU,EACdF,IAAID,CAAE,EAE5CK,EAAgBlC,UAAY,IAAM,CAC5BkC,EAAgBvD,SAClBA,EAAOb,SAAWoE,EAAgBvD,QAEpCT,EAAQS,CAAM,CAAA,EAGhBuD,EAAgBjC,QAAqBzB,GAAA,CAC3B0B,QAAAA,MAAM,+BAAgC1B,CAAK,EACnDN,EAAQS,CAAM,CAAA,CAChB,EAGFsD,EAAahC,QAAqBzB,GAAA,CACxB0B,QAAAA,MAAM,4BAA6B1B,CAAK,EAChDN,EAAQS,CAAM,CAAA,CAChB,EAGFoD,EAAW9B,QAAqBzB,GAAA,CACtB0B,QAAAA,MAAM,6BAA8B1B,CAAK,EACjDN,EAAQS,CAAM,CAAA,CAChB,EAGFiC,EAAaX,QAAqBzB,GAAA,CACxB0B,QAAAA,MAAM,uBAAwB1B,CAAK,EACpCL,EAAA,IAAIgC,MAAM,qBAAqB,CAAC,CAAA,CACzC,CACD,QACMD,GACCA,eAAAA,MAAM,yBAA0BA,CAAK,EACtC,IACT,CACF,CAMA,MAAMiC,uBAAqD,CACrD,GAAA,CACI1E,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CAGhCC,MAAAA,EAFcX,EAAGgC,YAAY,CAAC,KAAK/B,OAAOC,MAAM,EAAG,UAAU,EACzCiC,YAAY,KAAKlC,OAAOC,MAAM,EAClCyE,SAEtBhE,EAAQ4B,UAAY,IAAM,CACxB,MAAMrC,EAAwBS,EAAQO,OAElChB,GAAAA,EAAO0E,SAAW,EAAG,CACfnE,EAAA,CACNoE,YAAa,EACbC,cAAe,EACfC,aAAc,EACdC,QAAS,EACTC,aAAc,EACdC,WAAY,EACZC,YAAa,EACbC,WAAY,EACZC,YAAa,EACbC,SAAU,EACVC,YAAa,EACbC,mBAAoB,EACpBC,YAAa,EACbC,aAAc,EACdC,YAAa,EACbC,iBAAkB,EAClBC,WAAY,EACZC,IAAK,EACLC,OAAQ,MACRC,UAAW,GACXC,QAAS,EAAA,CACV,EACD,OAIF,MAAMpB,EAAc3E,EAAO0E,OACrBE,EAAgB5E,EAAOgG,OAAQC,GAAMA,EAAEhH,EAAaE,QAAQ,IAAM,KAAK,EAAEuF,OACzEG,EAAe7E,EAAOgG,OAAQC,GAAMA,EAAEhH,EAAaE,QAAQ,IAAM,MAAM,EAAEuF,OACzEI,EAAUH,EAAc,EAAKC,EAAgBD,EAAe,IAAM,EAGlEuB,EAAYlG,EACfgG,OAAcC,GAAAA,EAAEE,cAAgBC,MAAS,EACzCC,IAAWJ,GAAAA,EAAEE,WAAY,EACtBf,EAAWc,EAAUI,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAK,CAAC,EAEtDC,EAAOP,EAAUF,OAAQQ,GAAQA,EAAM,CAAC,EACxCE,EAASR,EAAUF,OAAQQ,GAAQA,EAAM,CAAC,EAE1CxB,EACJyB,EAAK/B,OAAS,EAAI+B,EAAKH,OAAO,CAACC,EAAKI,IAAQJ,EAAMI,EAAK,CAAC,EAAIF,EAAK/B,OAAS,EACtEO,EACJyB,EAAOhC,OAAS,EACZkC,KAAKC,IAAIH,EAAOJ,OAAO,CAACC,EAAKO,IAASP,EAAMO,EAAM,CAAC,EAAIJ,EAAOhC,MAAM,EACpE,EACAQ,EAAauB,EAAK/B,OAAS,EAAIkC,KAAKG,IAAI,GAAGN,CAAI,EAAI,EACnDtB,EAAcuB,EAAOhC,OAAS,EAAIkC,KAAKC,IAAID,KAAKI,IAAI,GAAGN,CAAM,CAAC,EAAI,EAElEO,EAAcR,EAAKH,OAAO,CAACC,EAAKI,IAAQJ,EAAMI,EAAK,CAAC,EACpDO,EAAYN,KAAKC,IAAIH,EAAOJ,OAAO,CAACC,EAAKO,IAASP,EAAMO,EAAM,CAAC,CAAC,EAChE/B,EAAemC,EAAY,EAAID,EAAcC,EAAY,EAGzDC,EAAanH,EAChBgG,OAAQC,GAAMA,EAAEhH,EAAaG,UAAU,IAAMgH,MAAS,EACtDC,IAAKJ,GAAMA,EAAEhH,EAAaG,UAAU,CAAE,EACnCsG,EACJyB,EAAWzC,OAAS,EAChByC,EAAWb,OAAO,CAACC,EAAKa,IAAMb,EAAMa,EAAG,CAAC,EAAID,EAAWzC,OACvD,EACAiB,EAAaD,GAAoBZ,EAAU,KAGjD,IAAIuC,EAAa,EACbC,EAAO,EACPjC,EAAc,EAElB,UAAWzC,KAAS5C,EACd4C,GAAAA,EAAMuD,cAAgBC,OAAW,CACnCiB,GAAczE,EAAMuD,YAChBkB,EAAaC,IACRD,EAAAA,GAET,MAAME,EAAWD,EAAOD,EACpBE,EAAWlC,IACCkC,EAAAA,GAKpB,MAAMjC,EAAqBgC,EAAO,EAAKjC,EAAciC,EAAQ,IAAM,EAG7D1B,EACJuB,EAAWzC,OAAS,EACfkC,KAAKY,KAAKL,EAAWzC,MAAM,EAAIgB,EAChCkB,KAAKY,KACHL,EAAWb,OAAO,CAACC,EAAKa,IAAMb,EAAMK,KAAKa,IAAIL,EAAI1B,EAAkB,CAAC,EAAG,CAAC,EACtEyB,EAAWzC,MACf,EACA,EAGAgD,EAAQ1H,EAAOqG,OAAWJ,EAAE0B,IAAI,EAAEC,OAClC9B,EAAY4B,EAAMhD,OAAS,EAAIgD,EAAM,CAAC,EAAI,GAC1C3B,EAAU2B,EAAMhD,OAAS,EAAIgD,EAAMA,EAAMhD,OAAS,CAAC,EAAI,GAErDnE,EAAA,CACNoE,YAAAA,EACAC,cAAAA,EACAC,aAAAA,EACAC,QAAAA,EACAC,aAAAA,EACAC,WAAAA,EACAC,YAAAA,EACAC,WAAAA,EACAC,YAAAA,EACAC,SAAAA,EACAC,YAAAA,EACAC,mBAAAA,EACAC,YAAa,EACbC,aAAc,EACdC,YAAa,EACbC,iBAAAA,EACAC,WAAAA,EACAC,IAAAA,EACAC,OAAQ,MACRC,UAAAA,EACAC,QAAAA,CAAAA,CACD,CAAA,EAGHtF,EAAQ6B,QAAqBzB,GAAA,CACnB0B,QAAAA,MAAM,qCAAsC1B,CAAK,EAClDL,EAAA,IAAIgC,MAAM,mCAAmC,CAAC,CAAA,CACvD,CACD,QACMD,GACCA,cAAAA,MAAM,kCAAmCA,CAAK,EAChD,IAAIC,MAAM,mCAAmC,CACrD,CACF,CAOA,MAAMqF,aAAaC,EAAqD,CAClE,GAAA,CACIhI,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMsB,EAAchC,EAAGgC,YACrB,CAAC,KAAK/B,OAAOC,OAAQ,KAAKD,OAAOE,YAAa,KAAKF,OAAOG,OAAQ,KAAKH,OAAOI,QAAQ,EACtF,UACF,EAGMM,EADcqB,EAAYG,YAAY,KAAKlC,OAAOC,MAAM,EAClCyE,SAE5BhE,EAAQ4B,UAAY,SAAY,CAC9B,IAAIrC,EAAwBS,EAAQO,OAGhC8G,EAAQC,WACV/H,EAASA,EAAOgG,OAAQC,GAAMA,EAAE0B,MAAQG,EAAQC,QAAS,GAEvDD,EAAQE,SACVhI,EAASA,EAAOgG,OAAQC,GAAMA,EAAE0B,MAAQG,EAAQE,MAAO,GAErDF,EAAQG,aACDjI,EAAAA,EAAOgG,OAAcC,GAAAA,EAAEhH,EAAaC,UAAU,IAAM4I,EAAQG,UAAU,GAE7EH,EAAQ3F,UACDnC,EAAAA,EAAOgG,OAAcC,GAAAA,EAAEhH,EAAaK,OAAO,IAAMwI,EAAQ3F,OAAO,GAEvE2F,EAAQI,YACDlI,EAAAA,EAAOgG,OAAcC,GAAAA,EAAEhH,EAAaM,SAAS,IAAMuI,EAAQI,SAAS,GAE3EJ,EAAQK,WACDnI,EAAAA,EAAOgG,OAAcC,GAAAA,EAAEhH,EAAaE,QAAQ,IAAM2I,EAAQK,QAAQ,GAEzEL,EAAQM,SACDpI,EAAAA,EAAOgG,OAAcC,GAAAA,EAAEhH,EAAaO,MAAM,IAAMsI,EAAQM,MAAM,GAErEN,EAAQO,iBAAmBjC,SAC7BpG,EAASA,EAAOgG,OAEZC,GAAAA,EAAEhH,EAAaG,UAAU,IAAMgH,QAC/BH,EAAEhH,EAAaG,UAAU,GAAM0I,EAAQO,cAC3C,GAEEP,EAAQQ,iBAAmBlC,SAC7BpG,EAASA,EAAOgG,OAEZC,GAAAA,EAAEhH,EAAaG,UAAU,IAAMgH,QAC/BH,EAAEhH,EAAaG,UAAU,GAAM0I,EAAQQ,cAC3C,GAEER,EAAQS,sBAAwBnC,SAClCpG,EAASA,EAAOgG,OAEZC,GAAAA,EAAEhH,EAAaS,sBAAsB,IAAM0G,QAC3CH,EAAEhH,EAAaS,sBAAsB,GAAMoI,EAAQS,mBACvD,GAEET,EAAQU,sBAAwBpC,SAClCpG,EAASA,EAAOgG,OAEZC,GAAAA,EAAEhH,EAAaS,sBAAsB,IAAM0G,QAC3CH,EAAEhH,EAAaS,sBAAsB,GAAMoI,EAAQU,mBACvD,GAIF,MAAMC,EAA+B,CAAA,EAErC,UAAW7F,KAAS5C,EAAQ,CAC1B,MAAM0I,EAAkC,CAAE9F,MAAAA,CAAAA,EAKpCwB,EAFWtC,EAAYG,YAAY,KAAKlC,OAAOE,WAAW,EACtCoE,MAAM,UAAU,EACdF,IAAIvB,EAAMsB,EAAG,EAEnC,MAAA,IAAI5D,QAAuBkD,GAAA,CAC/BY,EAAW/B,UAAY,IAAM,CACvB+B,EAAWpD,SACb0H,EAAazI,YAAcmE,EAAWpD,QAEpCwC,GAAA,EAEKlB,EAAAA,QAAU,IAAMkB,GAAI,CAChC,EAKD,MAAMc,EAFaxC,EAAYG,YAAY,KAAKlC,OAAOG,MAAM,EAC/BmE,MAAM,UAAU,EACdF,IAAIvB,EAAMsB,EAAG,EAEvC,MAAA,IAAI5D,QAAuBkD,GAAA,CAC/Bc,EAAajC,UAAY,IAAM,CACzBiC,EAAatD,SACf0H,EAAa/E,MAAQW,EAAatD,QAEhCwC,GAAA,EAEOlB,EAAAA,QAAU,IAAMkB,GAAI,CAClC,EAKD,MAAMe,EAFgBzC,EAAYG,YAAY,KAAKlC,OAAOI,QAAQ,EAC9BkE,MAAM,UAAU,EACdF,IAAIvB,EAAMsB,EAAG,EAE7C,MAAA,IAAI5D,QAAuBkD,GAAA,CAC/Be,EAAgBlC,UAAY,IAAM,CAC5BkC,EAAgBvD,SAClB0H,EAAavI,SAAWoE,EAAgBvD,QAEtCwC,GAAA,EAEUlB,EAAAA,QAAU,IAAMkB,GAAI,CACrC,EAEDiF,EAAQlF,KAAKmF,CAAY,EAG3BnI,EAAQkI,CAAO,CAAA,EAGjBhI,EAAQ6B,QAAqBzB,GAAA,CACnB0B,QAAAA,MAAM,0BAA2B1B,CAAK,EACvCL,EAAA,IAAIgC,MAAM,yBAAyB,CAAC,CAAA,CAC7C,CACD,QACMD,GACCA,cAAAA,MAAM,yBAA0BA,CAAK,EACvC,IAAIC,MAAM,yBAAyB,CAC3C,CACF,CAMA,MAAMmG,cAA6C,CAC7C,GAAA,CACF,OAAO,MAAM,KAAKd,aAAa,CAAA,CAAE,QAC1BtF,GACCA,eAAAA,MAAM,yBAA0BA,CAAK,EACtC,EACT,CACF,CAOA,MAAMqG,YAAY1E,EAA2B,CACvC,GAAA,CACIpE,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMsB,EAAchC,EAAGgC,YACrB,CAAC,KAAK/B,OAAOC,OAAQ,KAAKD,OAAOE,YAAa,KAAKF,OAAOG,OAAQ,KAAKH,OAAOI,QAAQ,EACtF,WACF,EAEA2B,EAAYQ,QAAqBzB,GAAA,CACvB0B,QAAAA,MAAM,qBAAsB1B,CAAK,EAClCL,EAAA,IAAIgC,MAAM,wBAAwB,CAAC,CAAA,EAM5C,MAAM4B,EAFWtC,EAAYG,YAAY,KAAKlC,OAAOE,WAAW,EACtCoE,MAAM,UAAU,EACdwE,WAAWC,YAAYC,KAAK7E,CAAE,CAAC,EAE3DE,EAAW/B,UAAuBxB,GAAA,CAC1BmI,MAAAA,EAAUnI,EAAME,OAAsBC,OACxCgI,IACFA,EAAOC,OAAO,EACdD,EAAOE,SAAS,EAClB,EAKF,MAAM5E,EAFaxC,EAAYG,YAAY,KAAKlC,OAAOG,MAAM,EAC/BmE,MAAM,UAAU,EACdwE,WAAWC,YAAYC,KAAK7E,CAAE,CAAC,EAE/DI,EAAajC,UAAuBxB,GAAA,CAC5BmI,MAAAA,EAAUnI,EAAME,OAAsBC,OACxCgI,IACFA,EAAOC,OAAO,EACdD,EAAOE,SAAS,EAClB,EAKF,MAAM3E,EAFgBzC,EAAYG,YAAY,KAAKlC,OAAOI,QAAQ,EAC9BkE,MAAM,UAAU,EACdwE,WAAWC,YAAYC,KAAK7E,CAAE,CAAC,EAErEK,EAAgBlC,UAAuBxB,GAAA,CAC/BmI,MAAAA,EAAUnI,EAAME,OAAsBC,OACxCgI,IACFA,EAAOC,OAAO,EACdD,EAAOE,SAAS,EAClB,EAKIjG,MAAAA,EADcnB,EAAYG,YAAY,KAAKlC,OAAOC,MAAM,EAC7BiJ,OAAO/E,CAAE,EAE1CpC,EAAYkC,WAAa,IAAM,CACrBzD,GAAA,EAGV0C,EAAaX,QAAqBzB,GAAA,CACxB0B,QAAAA,MAAM,wBAAyB1B,CAAK,EACrCL,EAAA,IAAIgC,MAAM,wBAAwB,CAAC,CAAA,CAC5C,CACD,QACMD,GACCA,cAAAA,MAAM,wBAAyBA,CAAK,EACtC,IAAIC,MAAM,wBAAwB,CAC1C,CACF,CAQA,MAAM2G,uBAAuBjF,EAAYxB,EAA6C,CAChF,GAAA,CACI5C,MAAAA,EAAK,MAAM,KAAKO,SACtB,OAAO,IAAIC,QAAQ,CAACC,EAASC,IAAW,CACtC,MAAMsB,EAAchC,EAAGgC,YACrB,CAAC,KAAK/B,OAAOC,OAAQ,KAAKD,OAAOE,YAAa,KAAKF,OAAOG,OAAQ,KAAKH,OAAOI,QAAQ,EACtF,WACF,EAEA2B,EAAYQ,QAAqBzB,GAAA,CACvB0B,QAAAA,MAAM,qBAAsB1B,CAAK,EAClCL,EAAA,IAAIgC,MAAM,wBAAwB,CAAC,CAAA,EAI5C,MAAMrB,EAAcW,EAAYG,YAAY,KAAKlC,OAAOC,MAAM,EACxDoJ,EAAe,CACnB,GAAG1G,EAAUE,MACbsB,GAAAA,EACAlB,WAAY,IAAIF,KAAK,EAAEC,YAAY,CAAA,EAG/BE,EAAe9B,EAAYkI,IAAID,CAAY,EAEjDnG,EAAaZ,UAAY,IAAM,CAE7B,GAAIK,EAAUzC,YAAa,CACzB,MAAMmD,EAAWtB,EAAYG,YAAY,KAAKlC,OAAOE,WAAW,EAC1DoD,EAAU,CAAE,GAAGX,EAAUzC,YAAaqD,SAAUY,CAAAA,EACtDd,EAASiG,IAAIhG,CAAO,EAGtB,GAAIX,EAAUiB,MAAO,CACnB,MAAMC,EAAa9B,EAAYG,YAAY,KAAKlC,OAAOG,MAAM,EACvD2D,EAAY,CAAE,GAAGnB,EAAUiB,MAAOL,SAAUY,CAAAA,EAClDN,EAAWyF,IAAIxF,CAAS,EAG1B,GAAInB,EAAUvC,SAAU,CACtB,MAAM2D,EAAgBhC,EAAYG,YAAY,KAAKlC,OAAOI,QAAQ,EAC5D4D,EAAe,CAAE,GAAGrB,EAAUvC,SAAUmD,SAAUY,CAAAA,EACxDJ,EAAcuF,IAAItF,CAAY,EAChC,EAGFjC,EAAYkC,WAAa,IAAM,CACrBzD,GAAA,EAGV0C,EAAaX,QAAqBzB,GAAA,CACxB0B,QAAAA,MAAM,wBAAyB1B,CAAK,EACrCL,EAAA,IAAIgC,MAAM,wBAAwB,CAAC,CAAA,CAC5C,CACD,QACMD,GACCA,cAAAA,MAAM,mCAAoCA,CAAK,EACjD,IAAIC,MAAM,wBAAwB,CAC1C,CACF,CACF,CAGa8G,MAAAA,EAAe,IAAI3J,EAGnB4J,EAAsBD"}