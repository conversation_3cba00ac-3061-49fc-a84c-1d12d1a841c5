{"version": 3, "file": "simple-ffedbc53.js", "sources": ["../../src/simple-index.tsx"], "sourcesContent": ["// Import DevTools configuration first\nimport './devtools-config';\n\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\n// import SimpleApp from './SimpleApp'; // Removed as file doesn't exist\n\n// Get the root element\nconst rootElement = document.getElementById('root');\n\n// Create a fallback root element if needed\nif (!rootElement) {\n  console.error('Root element not found, creating a fallback element');\n  const fallbackRoot = document.createElement('div');\n  fallbackRoot.id = 'root';\n  document.body.appendChild(fallbackRoot);\n}\n\n// Create the React root\nconst root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);\n\n// Render the app\nroot.render(\n  <React.StrictMode>\n    <div>Simple App placeholder - SimpleApp component not found</div>\n  </React.StrictMode>\n);\n"], "names": ["rootElement", "document", "getElementById", "console", "error", "fallbackRoot", "createElement", "id", "body", "append<PERSON><PERSON><PERSON>", "root", "ReactDOM", "createRoot", "render", "React", "jsx"], "mappings": "wFAQA,MAAMA,EAAcC,SAASC,eAAe,MAAM,EAGlD,GAAI,CAACF,EAAa,CAChBG,QAAQC,MAAM,qDAAqD,EAC7DC,MAAAA,EAAeJ,SAASK,cAAc,KAAK,EACjDD,EAAaE,GAAK,OACTC,SAAAA,KAAKC,YAAYJ,CAAY,EAIxC,MAAMK,EAAOC,EAASC,WAAWX,SAASC,eAAe,MAAM,CAAgB,EAG/EQ,EAAKG,aACFC,EAAM,WAAN,CACC,SAACC,MAAA,MAAA,CAAI,SAAsD,wDAAA,CAAA,CAC7D,CAAA,CACF"}