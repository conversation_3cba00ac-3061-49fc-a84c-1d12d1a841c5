import{j as o}from"./client-d6fc67cc.js";import{r as p}from"./react-25c2faed.js";import{s as n}from"./styled-components-00fe3932.js";import{u as A}from"./main-681bb6a1.js";import"./router-2c168ac3.js";const D=n.div.withConfig({displayName:"HeaderContainer",componentId:"sc-1vd40ql-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid ",";margin-bottom:",";position:relative;&::after{content:'';position:absolute;bottom:-2px;left:0;width:60px;height:4px;background:linear-gradient( 90deg,"," 0%,","80 50%,transparent 100% );border-radius:2px;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),M=n.div.withConfig({displayName:"TitleSection",componentId:"sc-1vd40ql-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),L=n.h1.withConfig({displayName:"Title",componentId:"sc-1vd40ql-2"})(["font-size:",";font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xxl)||"2rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),P=n.p.withConfig({displayName:"Subtitle",componentId:"sc-1vd40ql-3"})(["font-size:",";color:",";margin:0;font-weight:400;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"}),O=n.div.withConfig({displayName:"ActionsSection",componentId:"sc-1vd40ql-4"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),J=n.div.withConfig({displayName:"StatusIndicator",componentId:"sc-1vd40ql-5"})(["display:flex;align-items:center;gap:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.full)||"9999px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({$hasChanges:e,theme:r})=>{var s,t,a,d,c,g;return e?`
        background: ${((s=r.colors)==null?void 0:s.warning)||"var(--warning-color)"}20;
        color: ${((t=r.colors)==null?void 0:t.warning)||"var(--warning-color)"};
        border: 1px solid ${((a=r.colors)==null?void 0:a.warning)||"var(--warning-color)"}40;
      `:`
        background: ${((d=r.colors)==null?void 0:d.success)||"var(--success-color)"}20;
        color: ${((c=r.colors)==null?void 0:c.success)||"var(--success-color)"};
        border: 1px solid ${((g=r.colors)==null?void 0:g.success)||"var(--success-color)"}40;
      `}),q=n.div.withConfig({displayName:"StatusDot",componentId:"sc-1vd40ql-6"})(["width:6px;height:6px;border-radius:50%;background:",";animation:",";@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"],({$hasChanges:e,theme:r})=>{var s,t;return e?((s=r.colors)==null?void 0:s.warning)||"var(--warning-color)":((t=r.colors)==null?void 0:t.success)||"var(--success-color)"},({$hasChanges:e})=>e?"pulse 2s infinite":"none"),k=n.button.withConfig({displayName:"ActionButton",componentId:"sc-1vd40ql-7"})(["padding:"," ",";border-radius:",";font-size:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;border:1px solid;text-transform:uppercase;letter-spacing:0.025em;min-width:80px;"," &:active:not(:disabled){transform:translateY(0);}&:disabled{opacity:0.5;cursor:not-allowed;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({$variant:e,theme:r})=>{var s,t,a,d,c,g,l,m,x,f;return e==="primary"?`
        background: ${((s=r.colors)==null?void 0:s.primary)||"var(--primary-color)"};
        color: white;
        border-color: ${((t=r.colors)==null?void 0:t.primary)||"var(--primary-color)"};
        
        &:hover:not(:disabled) {
          background: ${((a=r.colors)==null?void 0:a.primaryDark)||"var(--primary-dark)"};
          border-color: ${((d=r.colors)==null?void 0:d.primaryDark)||"var(--primary-dark)"};
          transform: translateY(-1px);
          box-shadow: 0 4px 8px ${((c=r.colors)==null?void 0:c.primary)||"var(--primary-color)"}40;
        }
      `:`
        background: transparent;
        color: ${((g=r.colors)==null?void 0:g.textSecondary)||"var(--text-secondary)"};
        border-color: ${((l=r.colors)==null?void 0:l.border)||"var(--border-primary)"};
        
        &:hover:not(:disabled) {
          color: ${((m=r.colors)==null?void 0:m.textPrimary)||"#ffffff"};
          border-color: ${((x=r.colors)==null?void 0:x.textPrimary)||"#ffffff"};
          background: ${((f=r.colors)==null?void 0:f.surface)||"var(--bg-secondary)"};
        }
      `}),H=({className:e,hasUnsavedChanges:r=!1,onSave:s,onReset:t})=>o.jsxs(D,{className:e,children:[o.jsxs(M,{children:[o.jsx(L,{children:"Settings"}),o.jsx(P,{children:"Configure your trading dashboard preferences and system settings"})]}),o.jsxs(O,{children:[o.jsxs(J,{$hasChanges:r,children:[o.jsx(q,{$hasChanges:r}),r?"Unsaved Changes":"All Saved"]}),r&&o.jsxs(o.Fragment,{children:[o.jsx(k,{$variant:"secondary",onClick:t,title:"Reset to last saved state",children:"Reset"}),o.jsx(k,{$variant:"primary",onClick:s,title:"Save current settings",children:"Save"})]})]})]}),G=n.div.withConfig({displayName:"FieldContainer",componentId:"sc-fpjz2x-0"})(["display:flex;flex-direction:column;gap:",";padding:"," 0;border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"var(--border-primary)"}),B=n.div.withConfig({displayName:"FieldRow",componentId:"sc-fpjz2x-1"})(["display:flex;justify-content:space-between;align-items:flex-start;gap:",";@media (max-width:768px){flex-direction:column;align-items:stretch;gap:",";}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Y=n.div.withConfig({displayName:"LabelSection",componentId:"sc-fpjz2x-2"})(["flex:1;min-width:0;"]),W=n.label.withConfig({displayName:"FieldLabel",componentId:"sc-fpjz2x-3"})(["display:block;font-size:",";font-weight:600;color:",";margin-bottom:",";cursor:pointer;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.md)||"1rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),_=n.p.withConfig({displayName:"FieldDescription",componentId:"sc-fpjz2x-4"})(["font-size:",";color:",";margin:0;line-height:1.4;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"}),X=n.div.withConfig({displayName:"ControlSection",componentId:"sc-fpjz2x-5"})(["flex-shrink:0;min-width:120px;@media (max-width:768px){min-width:0;}"]),K=n.input.withConfig({displayName:"Input",componentId:"sc-fpjz2x-6"})(["width:100%;padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";transition:all 0.2s ease;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;}&:disabled{opacity:0.5;cursor:not-allowed;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"var(--bg-primary)"},({theme:e,$hasError:r})=>{var s,t;return r?((s=e.colors)==null?void 0:s.error)||"var(--error-color)":((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),U=n.select.withConfig({displayName:"Select",componentId:"sc-fpjz2x-7"})(["width:100%;padding:"," ",";background:",";border:1px solid ",";border-radius:",";color:",";font-size:",";cursor:pointer;transition:all 0.2s ease;&:focus{outline:none;border-color:",";box-shadow:0 0 0 3px ","20;}&:disabled{opacity:0.5;cursor:not-allowed;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"var(--bg-primary)"},({theme:e,$hasError:r})=>{var s,t;return r?((s=e.colors)==null?void 0:s.error)||"var(--error-color)":((t=e.colors)==null?void 0:t.border)||"var(--border-primary)"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),V=n.label.withConfig({displayName:"ToggleContainer",componentId:"sc-fpjz2x-8"})(["position:relative;display:inline-block;width:52px;height:28px;cursor:pointer;"]),Q=n.input.withConfig({displayName:"ToggleInput",componentId:"sc-fpjz2x-9"})(["opacity:0;width:0;height:0;&:checked + span{background:",";}&:checked + span:before{transform:translateX(24px);}&:focus + span{box-shadow:0 0 0 3px ","20;}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),Z=n.span.withConfig({displayName:"ToggleSlider",componentId:"sc-fpjz2x-10"})(["position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background:",";transition:all 0.3s ease;border-radius:28px;&:before{position:absolute;content:'';height:20px;width:20px;left:4px;bottom:4px;background:white;transition:all 0.3s ease;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,0.2);}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"var(--border-primary)"}),rr=n.div.withConfig({displayName:"ErrorMessage",componentId:"sc-fpjz2x-11"})(["font-size:",";color:",";margin-top:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"var(--error-color)"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),b=({name:e,label:r,description:s,type:t,value:a,onChange:d,options:c=[],inputProps:g={},error:l,disabled:m=!1,className:x})=>{const f=`settings-field-${e}`,h=i=>{m||d(e,i)},y=()=>{switch(t){case"text":case"number":return o.jsx(K,{id:f,type:t,value:a||"",onChange:i=>h(t==="number"?parseFloat(i.target.value)||0:i.target.value),disabled:m,$hasError:!!l,...g});case"select":return o.jsx(U,{id:f,value:a||"",onChange:i=>h(i.target.value),disabled:m,$hasError:!!l,children:c.map(i=>o.jsx("option",{value:i.value,children:i.label},i.value))});case"toggle":return o.jsxs(V,{children:[o.jsx(Q,{id:f,type:"checkbox",checked:!!a,onChange:i=>h(i.target.checked),disabled:m}),o.jsx(Z,{})]});default:return null}};return o.jsxs(G,{className:x,children:[o.jsxs(B,{children:[o.jsxs(Y,{children:[o.jsx(W,{htmlFor:f,children:r}),s&&o.jsx(_,{children:s})]}),o.jsx(X,{children:y()})]}),l&&o.jsx(rr,{role:"alert",children:l})]})},er=n.div.withConfig({displayName:"FormContainer",componentId:"sc-az6y7r-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"}),I=n.div.withConfig({displayName:"FormSection",componentId:"sc-az6y7r-1"})(["background:",";border:1px solid ",";border-radius:",";overflow:hidden;box-shadow:0 4px 6px -1px rgba(0,0,0,0.1);transition:all 0.2s ease;&:hover{border-color:","40;box-shadow:0 8px 25px -5px rgba(0,0,0,0.1);}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"var(--bg-secondary)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"var(--border-primary)"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),N=n.div.withConfig({displayName:"SectionHeader",componentId:"sc-az6y7r-2"})(["padding:",";border-bottom:1px solid ",";background:",";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:4px;height:100%;background:",";}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"var(--border-primary)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"var(--bg-primary)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"}),z=n.h2.withConfig({displayName:"SectionTitle",componentId:"sc-az6y7r-3"})(["font-size:",";font-weight:700;color:",";margin:0 0 "," 0;text-transform:uppercase;letter-spacing:0.025em;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"1.125rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),E=n.p.withConfig({displayName:"SectionDescription",componentId:"sc-az6y7r-4"})(["font-size:",";color:",";margin:0;line-height:1.5;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"}),F=n.div.withConfig({displayName:"SectionContent",componentId:"sc-az6y7r-5"})(["padding:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"}),T=n.div.withConfig({displayName:"FieldGroup",componentId:"sc-az6y7r-6"})(["display:flex;flex-direction:column;"]),or=[{value:"mercedes-green",label:"Mercedes Green"},{value:"f1-official",label:"F1 Official"},{value:"dark",label:"Dark Theme"}],tr=({data:e,onChange:r,errors:s={},disabled:t=!1,className:a})=>o.jsxs(er,{className:a,children:[o.jsxs(I,{children:[o.jsxs(N,{children:[o.jsx(z,{children:"Appearance"}),o.jsx(E,{children:"Customize the visual appearance and theme of your trading dashboard"})]}),o.jsx(F,{children:o.jsx(T,{children:o.jsx(b,{name:"theme",label:"Theme",description:"Choose your preferred visual theme",type:"select",value:e.theme,onChange:r,options:or,error:s.theme,disabled:t})})})]}),o.jsxs(I,{children:[o.jsxs(N,{children:[o.jsx(z,{children:"General Settings"}),o.jsx(E,{children:"Configure general application behavior and performance settings"})]}),o.jsx(F,{children:o.jsxs(T,{children:[o.jsx(b,{name:"refreshInterval",label:"Data Refresh Interval",description:"How often to refresh dashboard data (in minutes)",type:"number",value:e.refreshInterval,onChange:r,inputProps:{min:1,max:60,step:1,style:{width:"100px"}},error:s.refreshInterval,disabled:t}),o.jsx(b,{name:"showNotifications",label:"Desktop Notifications",description:"Enable desktop notifications for important events and alerts",type:"toggle",value:e.showNotifications,onChange:r,error:s.showNotifications,disabled:t}),o.jsx(b,{name:"enableAdvancedMetrics",label:"Advanced Metrics",description:"Show additional performance metrics and detailed analytics",type:"toggle",value:e.enableAdvancedMetrics,onChange:r,error:s.enableAdvancedMetrics,disabled:t}),o.jsx(b,{name:"autoSaveJournal",label:"Auto-Save Trade Journal",description:"Automatically save trade entries as you type to prevent data loss",type:"toggle",value:e.autoSaveJournal,onChange:r,error:s.autoSaveJournal,disabled:t})]})})]})]}),R="adhd-trading-dashboard:settings",$={theme:"mercedes-green",refreshInterval:5,showNotifications:!0,enableAdvancedMetrics:!1,autoSaveJournal:!0},nr=e=>e==="f1"||e==="formula1"||e==="formula-1"?"mercedes-green":e==="light"?"f1-official":e,sr=()=>{try{const e=localStorage.getItem(R);if(e){const r=JSON.parse(e);return r.theme&&(r.theme=nr(r.theme)),{...$,...r}}}catch(e){console.warn("Failed to load settings from localStorage:",e)}return $},ir=e=>{try{localStorage.setItem(R,JSON.stringify(e))}catch(r){throw console.error("Failed to save settings to localStorage:",r),new Error("Failed to save settings")}},j=(e,r)=>{switch(e){case"theme":return!r||typeof r!="string"?"Theme is required":["mercedes-green","f1-official","dark"].includes(r)?null:"Invalid theme selection";case"refreshInterval":return typeof r!="number"||isNaN(r)?"Refresh interval must be a number":r<1||r>60?"Refresh interval must be between 1 and 60 minutes":null;case"showNotifications":case"enableAdvancedMetrics":case"autoSaveJournal":return typeof r!="boolean"?"Invalid boolean value":null;default:return null}},S=e=>{const r={};return Object.entries(e).forEach(([s,t])=>{const a=j(s,t);a&&(r[s]=a)}),r},ar=()=>{const{setTheme:e}=A(),[r,s]=p.useState(()=>sr()),[t,a]=p.useState(r),[d,c]=p.useState({}),[g,l]=p.useState(!1),m=p.useMemo(()=>JSON.stringify(t)!==JSON.stringify(r),[t,r]),x=p.useMemo(()=>Object.keys(d).length===0,[d]),f=p.useCallback((i,u)=>{a(v=>({...v,[i]:u}));const C=j(i,u);c(v=>{const w={...v};return C?w[i]=C:delete w[i],w}),i==="theme"&&e(u)},[e]),h=p.useCallback(async()=>{const i=S(t);if(c(i),Object.keys(i).length>0)throw new Error("Please fix validation errors before saving");l(!0);try{await new Promise(u=>setTimeout(u,500)),ir(t),s(t),e(t.theme),console.log("Settings saved successfully:",t)}catch(u){throw console.error("Failed to save settings:",u),u}finally{l(!1)}},[t,e]),y=p.useCallback(()=>{a(r),c({}),e(r.theme)},[r,e]);return p.useEffect(()=>{const i=S(t);c(i)},[t]),{data:t,savedData:r,hasUnsavedChanges:m,errors:d,isValid:x,isSaving:g,handleChange:f,handleSave:h,handleReset:y,validateField:j,validateForm:S}},cr=n.div.withConfig({displayName:"Container",componentId:"sc-1g70k6l-0"})(["display:flex;flex-direction:column;min-height:100vh;background:",";color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"var(--bg-primary)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),lr=n.div.withConfig({displayName:"ContentWrapper",componentId:"sc-1g70k6l-1"})(["flex:1;max-width:1200px;width:100%;margin:0 auto;padding:"," ",";@media (max-width:768px){padding:"," ",";}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),dr=n.div.withConfig({displayName:"LoadingState",componentId:"sc-1g70k6l-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:200px;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"}),pr=n.div.withConfig({displayName:"LoadingIcon",componentId:"sc-1g70k6l-3"})(["font-size:48px;margin-bottom:",";opacity:0.7;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:0.7;}50%{opacity:0.3;}}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),gr=n.p.withConfig({displayName:"LoadingText",componentId:"sc-1g70k6l-4"})(["font-size:",";color:",";margin:0;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"1.125rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"}),mr=n.div.withConfig({displayName:"ErrorState",componentId:"sc-1g70k6l-5"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:",";text-align:center;min-height:200px;background:","10;border:1px solid ","40;border-radius:",";margin:"," 0;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"var(--error-color)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"var(--error-color)"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"}),fr=n.div.withConfig({displayName:"ErrorIcon",componentId:"sc-1g70k6l-6"})(["font-size:48px;margin-bottom:",";color:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"var(--error-color)"}),xr=n.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-1g70k6l-7"})(["font-size:",";font-weight:600;color:",";margin:0 0 "," 0;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"1.125rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"var(--error-color)"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),ur=n.p.withConfig({displayName:"ErrorMessage",componentId:"sc-1g70k6l-8"})(["font-size:",";color:",";margin:0;max-width:400px;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"var(--text-secondary)"}),hr=n.button.withConfig({displayName:"RetryButton",componentId:"sc-1g70k6l-9"})(["margin-top:",";padding:"," ",";background:",";color:white;border:none;border-radius:",";font-weight:600;cursor:pointer;transition:all 0.2s ease;&:hover{background:",";transform:translateY(-1px);}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"var(--primary-color)"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primaryDark)||"var(--primary-dark)"}),yr=n.div.withConfig({displayName:"SuccessNotification",componentId:"sc-1g70k6l-10"})(["position:fixed;top:20px;right:20px;background:",";color:white;padding:"," ",";border-radius:",";font-weight:600;box-shadow:0 10px 25px -5px rgba(0,0,0,0.1);transform:translateX(",");opacity:",";transition:all 0.3s ease;z-index:1000;"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.success)||"var(--success-color)"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({$visible:e})=>e?"0":"100%",({$visible:e})=>e?"1":"0"),br=()=>o.jsxs(dr,{children:[o.jsx(pr,{children:"⚙️"}),o.jsx(gr,{children:"Loading Settings..."})]}),vr=({error:e,onRetry:r})=>o.jsxs(mr,{children:[o.jsx(fr,{children:"⚠️"}),o.jsx(xr,{children:"Settings Error"}),o.jsx(ur,{children:e}),o.jsx(hr,{onClick:r,children:"Try Again"})]}),wr=()=>{const{data:e,hasUnsavedChanges:r,errors:s,isSaving:t,handleChange:a,handleSave:d,handleReset:c}=ar(),[g,l]=p.useState(null),[m,x]=p.useState(!1),f=async()=>{try{l(null),await d(),x(!0),setTimeout(()=>x(!1),3e3)}catch(y){l(y instanceof Error?y.message:"Failed to save settings")}},h=()=>{l(null)};return g?o.jsx(vr,{error:g,onRetry:h}):o.jsxs(o.Fragment,{children:[o.jsx(H,{hasUnsavedChanges:r,onSave:f,onReset:c}),o.jsx(tr,{data:e,onChange:a,errors:s,disabled:t}),o.jsx(yr,{$visible:m,children:"✅ Settings saved successfully!"})]})},Sr=({className:e})=>o.jsx(cr,{className:e,children:o.jsx(lr,{children:o.jsx(p.Suspense,{fallback:o.jsx(br,{}),children:o.jsx(wr,{})})})}),zr=()=>o.jsx(Sr,{});export{zr as default};
//# sourceMappingURL=Settings-ff0bb2e7.js.map
