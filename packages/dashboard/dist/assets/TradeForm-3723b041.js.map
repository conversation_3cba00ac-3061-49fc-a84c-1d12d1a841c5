{"version": 3, "file": "TradeForm-3723b041.js", "sources": ["../../../shared/src/types/tradingSessions.ts", "../../../shared/src/constants/setupElements.ts", "../../../shared/src/components/atoms/Input.tsx", "../../../shared/src/components/atoms/TimePicker.tsx", "../../../shared/src/components/atoms/SelectDropdown.tsx", "../../../shared/src/config/tradingSessionsConfig.ts", "../../../shared/src/utils/sessionUtils.ts", "../../../shared/src/hooks/useSessionSelection.ts", "../../../shared/src/components/molecules/HierarchicalSessionSelector.tsx", "../../../shared/src/components/organisms/DashboardSection.tsx", "../../../shared/src/components/trade/SetupBuilder.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormHeader.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormTimingFields.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormRiskFields.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormStrategyFields.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormActions.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormMessages.tsx", "../../src/features/trade-journal/components/trade-form/TradeFormLoading.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/TradeDOLAnalysis.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLTypeSelector.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLStrengthSelector.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLReactionSelector.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLContextSelector.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLDetailedAnalysis.tsx", "../../src/features/trade-journal/components/trade-dol-analysis/DOLEffectivenessRating.tsx", "../../src/features/trade-journal/components/trade-pattern-quality/CriterionSelector.tsx", "../../src/features/trade-journal/components/trade-pattern-quality/PatternQualityAssessment.tsx", "../../src/features/trade-journal/components/trade-analysis-section/TradeAnalysisSection.tsx", "../../src/features/trade-journal/TradeForm.tsx"], "sourcesContent": ["/**\n * Trading Sessions - Hierarchical Session System\n *\n * Provides a layered structure for trading sessions and macro periods:\n * - Broad sessions (NY AM, NY PM, London, etc.)\n * - Specific macro periods within each session\n * - Time validation and inheritance logic\n */\n\n/**\n * Time range interface\n */\nexport interface TimeRange {\n  start: string; // HH:MM:SS format\n  end: string; // HH:MM:SS format\n}\n\n/**\n * Session type enumeration\n */\nexport enum SessionType {\n  // Major Sessions\n  LONDON = 'london',\n  NEW_YORK_AM = 'new-york-am',\n  NEW_YORK_PM = 'new-york-pm',\n  ASIA = 'asia',\n\n  // Extended Sessions\n  PRE_MARKET = 'pre-market',\n  AFTER_HOURS = 'after-hours',\n  OVERNIGHT = 'overnight',\n}\n\n/**\n * Macro period types\n */\nexport enum MacroPeriodType {\n  // Morning Macros\n  MORNING_BREAKOUT = 'morning-breakout', // 9:50-10:10\n  MID_MORNING_REVERSION = 'mid-morning-reversion', // 10:50-11:10\n\n  // Lunch Macros\n  PRE_LUNCH = 'pre-lunch', // 11:50-12:10\n  LUNCH_MACRO_EXTENDED = 'lunch-macro-extended', // 11:30-13:30 (spans sessions)\n  LUNCH_MACRO = 'lunch-macro', // 12:00-13:30\n  POST_LUNCH = 'post-lunch', // 13:50-14:10\n\n  // Afternoon Macros\n  PRE_CLOSE = 'pre-close', // 14:50-15:10\n  POWER_HOUR = 'power-hour', // 15:15-15:45\n  MOC = 'moc', // 15:45-16:00\n\n  // London Macros\n  LONDON_OPEN = 'london-open',\n  LONDON_NY_OVERLAP = 'london-ny-overlap',\n\n  // Custom/Other\n  CUSTOM = 'custom',\n}\n\n/**\n * Macro period definition\n */\nexport interface MacroPeriod {\n  /** Unique identifier */\n  id: string;\n  /** Macro period type */\n  type: MacroPeriodType;\n  /** Display name */\n  name: string;\n  /** Time range */\n  timeRange: TimeRange;\n  /** Description */\n  description: string;\n  /** Trading characteristics */\n  characteristics: string[];\n  /** Typical volatility level (1-5) */\n  volatilityLevel: number;\n  /** Typical volume level (1-5) */\n  volumeLevel: number;\n  /** Whether this is a high-probability setup period */\n  isHighProbability: boolean;\n  /** Nested sub-periods within this macro (for overlapping scenarios) */\n  subPeriods?: MacroPeriod[];\n  /** Parent macro period (if this is a sub-period) */\n  parentMacro?: MacroPeriodType;\n  /** Whether this macro spans multiple sessions */\n  isMultiSession?: boolean;\n  /** Sessions this macro spans (if multi-session) */\n  spansSessions?: SessionType[];\n}\n\n/**\n * Trading session definition\n */\nexport interface TradingSession {\n  /** Unique identifier */\n  id: string;\n  /** Session type */\n  type: SessionType;\n  /** Display name */\n  name: string;\n  /** Time range for the entire session */\n  timeRange: TimeRange;\n  /** Description */\n  description: string;\n  /** Timezone (e.g., 'America/New_York', 'Europe/London') */\n  timezone: string;\n  /** Market characteristics */\n  characteristics: string[];\n  /** Child macro periods */\n  macroPeriods: MacroPeriod[];\n  /** Whether this session is currently active */\n  isActive?: boolean;\n  /** Session color for UI display */\n  color: string;\n}\n\n/**\n * Session hierarchy structure\n */\nexport interface SessionHierarchy {\n  /** All available sessions */\n  sessions: TradingSession[];\n  /** Quick lookup by session type */\n  sessionsByType: Record<SessionType, TradingSession>;\n  /** Quick lookup by macro period type */\n  macrosByType: Record<\n    MacroPeriodType,\n    MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] }\n  >;\n  /** Multi-session macro periods that span across sessions */\n  multiSessionMacros: MacroPeriod[];\n}\n\n/**\n * Session selection state\n */\nexport interface SessionSelection {\n  /** Selected session (if any) */\n  session?: SessionType;\n  /** Selected macro period (if any) */\n  macroPeriod?: MacroPeriodType;\n  /** Custom time range (if neither session nor macro selected) */\n  customTimeRange?: TimeRange;\n  /** Display label for the selection */\n  displayLabel: string;\n  /** Whether this is a broad session or specific macro */\n  selectionType: 'session' | 'macro' | 'custom';\n}\n\n/**\n * Time validation result\n */\nexport interface TimeValidationResult {\n  /** Whether the time is valid */\n  isValid: boolean;\n  /** Error message if invalid */\n  error?: string;\n  /** Warning message if applicable */\n  warning?: string;\n  /** Suggested session/macro if time falls within known periods */\n  suggestedSession?: SessionType;\n  /** Suggested macro period if time falls within known periods */\n  suggestedMacro?: MacroPeriodType;\n}\n\n/**\n * Session filter options\n */\nexport interface SessionFilterOptions {\n  /** Include only active sessions */\n  activeOnly?: boolean;\n  /** Include only high-probability periods */\n  highProbabilityOnly?: boolean;\n  /** Minimum volatility level */\n  minVolatility?: number;\n  /** Maximum volatility level */\n  maxVolatility?: number;\n  /** Specific session types to include */\n  sessionTypes?: SessionType[];\n  /** Specific macro types to include */\n  macroTypes?: MacroPeriodType[];\n}\n\n/**\n * Session analytics data\n */\nexport interface SessionAnalytics {\n  /** Session or macro period identifier */\n  id: string;\n  /** Total trades in this period */\n  totalTrades: number;\n  /** Win rate percentage */\n  winRate: number;\n  /** Average R-multiple */\n  averageRMultiple: number;\n  /** Total P&L */\n  totalPnl: number;\n  /** Best performing setups in this period */\n  topSetups: string[];\n  /** Performance trend (improving, declining, stable) */\n  trend: 'improving' | 'declining' | 'stable';\n}\n", "/**\n * Setup Elements Constants\n * \n * Modular setup construction elements for the ADHD Trading Dashboard\n * These constants define the atomic elements that can be combined to create\n * infinite setup combinations without pre-defining every possibility.\n */\n\nexport const SETUP_ELEMENTS = {\n  constant: {\n    parentArrays: [\n      'NWOG', \n      'Old-NWOG', \n      'NDOG', \n      'Old-NDOG', \n      'Monthly-FVG', \n      'Weekly-FVG', \n      'Daily-FVG',\n      '15min-Top/Bottom-FVG', \n      '1h-Top/Bottom-FVG'\n    ],\n    fvgTypes: [\n      'Strong-FVG', \n      'AM-FPFVG', \n      'PM-FPFVG', \n      'Asia-FPFVG', \n      'Premarket-FPFVG', \n      'MNOR-FVG', \n      'Macro-FVG', \n      'News-FVG', \n      'Top/Bottom-FVG'\n    ]\n  },\n  action: {\n    liquidityEvents: [\n      'None', \n      'London-H/L', \n      'Premarket-H/L', \n      '09:30-Opening-Range-H/L',\n      'Lunch-H/L', \n      'Prev-Day-H/L', \n      'Prev-Week-H/L', \n      'Monthly-H/L', \n      'Macro-H/L'\n    ]\n  },\n  variable: {\n    rdTypes: [\n      'None', \n      'True-RD', \n      'IMM-RD', \n      'Dispersed-RD', \n      'Wide-Gap-RD'\n    ]\n  },\n  entry: {\n    methods: [\n      'Simple-Entry', \n      'Complex-Entry', \n      'Complex-Entry/Mini'\n    ]\n  }\n} as const;\n\n/**\n * Valid trading models (excluding invalid concepts)\n */\nexport const VALID_TRADING_MODELS = [\n  'RD-Cont',\n  'FVG-RD', \n  'Combined'\n] as const;\n\n/**\n * Type definitions for setup elements\n */\nexport type SetupConstant = typeof SETUP_ELEMENTS.constant.parentArrays[number] | typeof SETUP_ELEMENTS.constant.fvgTypes[number];\nexport type SetupAction = typeof SETUP_ELEMENTS.action.liquidityEvents[number];\nexport type SetupVariable = typeof SETUP_ELEMENTS.variable.rdTypes[number];\nexport type SetupEntry = typeof SETUP_ELEMENTS.entry.methods[number];\nexport type TradingModel = typeof VALID_TRADING_MODELS[number];\n", "/**\n * Input Component\n *\n * A customizable input component that follows the design system.\n */\nimport React, { useState, useRef } from 'react';\nimport styled, { css } from 'styled-components';\n\nexport type InputSize = 'small' | 'medium' | 'large';\n\n// Create a custom type that extends the HTML input attributes but overrides the size property\ntype CustomInputHTMLAttributes = Omit<\n  React.InputHTMLAttributes<HTMLInputElement>,\n  'onChange' | 'size'\n> & {\n  size?: InputSize;\n};\n\nexport interface InputProps extends CustomInputHTMLAttributes {\n  /** The value of the input */\n  value: string;\n  /** Function called when the input value changes */\n  onChange: (value: string) => void;\n  /** The placeholder text */\n  placeholder?: string;\n  /** Whether the input is disabled */\n  disabled?: boolean;\n  /** The error message */\n  error?: string;\n  /** The input type */\n  type?: string;\n  /** The input name */\n  name?: string;\n  /** The input id */\n  id?: string;\n  /** Additional CSS class names */\n  className?: string;\n  /** Whether the input is required */\n  required?: boolean;\n  /** Input autocomplete attribute */\n  autoComplete?: string;\n  /** Label for the input */\n  label?: string;\n  /** Helper text to display below the input */\n  helperText?: string;\n  /** Icon to display at the start of the input */\n  startIcon?: React.ReactNode;\n  /** Icon to display at the end of the input */\n  endIcon?: React.ReactNode;\n  /** Whether the input is in a loading state */\n  loading?: boolean;\n  /** Whether the input is in a success state */\n  success?: boolean;\n  /** Whether the input should have a clear button */\n  clearable?: boolean;\n  /** Function called when the input is cleared */\n  onClear?: () => void;\n  /** Maximum character count */\n  maxLength?: number;\n  /** Whether to show character count */\n  showCharCount?: boolean;\n  /** Size of the input */\n  size?: 'small' | 'medium' | 'large';\n  /** Whether the input should be full width */\n  fullWidth?: boolean;\n}\n\n// Define the props that InputWrapper will accept\ntype InputWrapperProps = {\n  fullWidth?: boolean;\n};\n\nconst InputWrapper = styled.div<InputWrapperProps>`\n  display: flex;\n  flex-direction: column;\n  width: ${({ fullWidth }) => (fullWidth ? '100%' : 'auto')};\n  position: relative;\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-bottom: ${({ theme }) => theme.spacing.xxs};\n  font-weight: ${({ theme }) => theme.fontWeights?.medium || 500};\n`;\n\n// Define the props that InputContainer will accept\ntype InputContainerProps = {\n  hasError?: boolean;\n  hasSuccess?: boolean;\n  disabled?: boolean;\n  $size?: InputSize;\n  hasStartIcon?: boolean;\n  hasEndIcon?: boolean;\n  isFocused?: boolean;\n};\n\nconst InputContainer = styled.div<InputContainerProps>`\n  display: flex;\n  align-items: center;\n  position: relative;\n  width: 100%;\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  border: 1px solid\n    ${({ theme, hasError, hasSuccess, isFocused }) => {\n      if (hasError) return theme.colors.error;\n      if (hasSuccess) return theme.colors.success;\n      if (isFocused) return theme.colors.primary;\n      return theme.colors.border;\n    }};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: all ${({ theme }) => theme.transitions?.fast || '0.2s ease'};\n\n  ${({ disabled, theme }) =>\n    disabled &&\n    css`\n      opacity: 0.6;\n      background-color: ${theme.colors.background};\n      cursor: not-allowed;\n    `}\n\n  ${({ isFocused, theme, hasError, hasSuccess }) =>\n    isFocused &&\n    css`\n      box-shadow: 0 0 0 2px\n        ${hasError\n          ? `${theme.colors.error}33`\n          : hasSuccess\n          ? `${theme.colors.success}33`\n          : `${theme.colors.primary}33`};\n    `}\n\n  ${({ $size }) => {\n    switch ($size) {\n      case 'small':\n        return css`\n          height: 32px;\n        `;\n      case 'large':\n        return css`\n          height: 48px;\n        `;\n      default:\n        return css`\n          height: 40px;\n        `;\n    }\n  }}\n`;\n\nconst IconContainer = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\n// Define the props that StyledInput will accept\ntype StyledInputProps = {\n  hasStartIcon?: boolean;\n  hasEndIcon?: boolean;\n  $size?: InputSize;\n};\n\nconst StyledInput = styled.input<StyledInputProps>`\n  flex: 1;\n  border: none;\n  background: transparent;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  width: 100%;\n  outline: none;\n\n  &:disabled {\n    cursor: not-allowed;\n  }\n\n  &::placeholder {\n    color: ${({ theme }) => theme.colors.textDisabled};\n  }\n\n  ${({ hasStartIcon }) =>\n    hasStartIcon &&\n    css`\n      padding-left: 0;\n    `}\n\n  ${({ hasEndIcon }) =>\n    hasEndIcon &&\n    css`\n      padding-right: 0;\n    `}\n\n  ${({ $size, theme }) => {\n    if ($size === 'small') {\n      return css`\n        font-size: ${theme.fontSizes.xs};\n        padding: ${theme.spacing.xxs} ${theme.spacing.xs};\n      `;\n    } else if ($size === 'large') {\n      return css`\n        font-size: ${theme.fontSizes.md};\n        padding: ${theme.spacing.sm} ${theme.spacing.md};\n      `;\n    } else {\n      return css`\n        font-size: ${theme.fontSizes.sm};\n        padding: ${theme.spacing.xs} ${theme.spacing.sm};\n      `;\n    }\n  }}\n`;\n\nconst ClearButton = styled.button`\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: ${({ theme }) => theme.colors.textDisabled};\n  padding: 0 ${({ theme }) => theme.spacing.xs};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.textSecondary};\n  }\n\n  &:focus {\n    outline: none;\n  }\n`;\n\nconst HelperTextContainer = styled.div<{ hasError?: boolean; hasSuccess?: boolean }>`\n  display: flex;\n  justify-content: space-between;\n  margin-top: ${({ theme }) => theme.spacing.xxs};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme, hasError, hasSuccess }) => {\n    if (hasError) return theme.colors.error;\n    if (hasSuccess) return theme.colors.success;\n    return theme.colors.textSecondary;\n  }};\n`;\n\n/**\n * Input Component\n *\n * A customizable input component that follows the design system.\n */\nexport const Input: React.FC<InputProps> = ({\n  value,\n  onChange,\n  placeholder = '',\n  disabled = false,\n  error = '',\n  type = 'text',\n  name = '',\n  id = '',\n  className = '',\n  required = false,\n  autoComplete = '',\n  label = '',\n  helperText = '',\n  startIcon,\n  endIcon,\n  loading = false,\n  success = false,\n  clearable = false,\n  onClear,\n  maxLength,\n  showCharCount = false,\n  size = 'medium' as InputSize,\n  fullWidth = false,\n  ...rest\n}) => {\n  const [isFocused, setIsFocused] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const handleClear = () => {\n    if (onClear) {\n      onClear();\n    } else {\n      onChange('');\n    }\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {\n    setIsFocused(true);\n    if (rest.onFocus) {\n      rest.onFocus(e);\n    }\n  };\n\n  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {\n    setIsFocused(false);\n    if (rest.onBlur) {\n      rest.onBlur(e);\n    }\n  };\n\n  // Clear button should only show when there's a value and the input is not disabled\n  const showClearButton = clearable && value && !disabled;\n\n  // Character count\n  const charCount = value?.length || 0;\n  const showCount = showCharCount || (maxLength !== undefined && maxLength > 0);\n\n  return (\n    <InputWrapper className={className} fullWidth={fullWidth}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && ' *'}\n        </Label>\n      )}\n\n      <InputContainer\n        hasError={!!error}\n        hasSuccess={!!success}\n        disabled={!!disabled}\n        $size={size}\n        hasStartIcon={!!startIcon}\n        hasEndIcon={!!(endIcon || showClearButton)}\n        isFocused={!!isFocused}\n      >\n        {startIcon && <IconContainer>{startIcon}</IconContainer>}\n\n        <StyledInput\n          ref={inputRef}\n          type={type}\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          placeholder={placeholder}\n          disabled={!!(disabled || loading)}\n          name={name}\n          id={id}\n          required={!!required}\n          autoComplete={autoComplete}\n          hasStartIcon={!!startIcon}\n          hasEndIcon={!!(endIcon || showClearButton)}\n          // Pass size as a custom prop to avoid conflict with HTML input size\n          $size={size}\n          maxLength={maxLength}\n          onFocus={handleFocus}\n          onBlur={handleBlur}\n          {...rest}\n        />\n\n        {showClearButton && (\n          <ClearButton type=\"button\" onClick={handleClear} tabIndex={-1}>\n            ✕\n          </ClearButton>\n        )}\n\n        {endIcon && <IconContainer>{endIcon}</IconContainer>}\n      </InputContainer>\n\n      {(error || helperText || showCount) && (\n        <HelperTextContainer hasError={!!error} hasSuccess={!!success}>\n          <div>{error || helperText}</div>\n          {showCount && (\n            <div>\n              {charCount}\n              {maxLength !== undefined && `/${maxLength}`}\n            </div>\n          )}\n        </HelperTextContainer>\n      )}\n    </InputWrapper>\n  );\n};\n", "/**\n * TimePicker Component\n *\n * A reusable time picker component for selecting hours and minutes.\n * MOVED TO SHARED: This component was being imported across multiple features.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface TimePickerProps {\n  id: string;\n  name: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\n  label?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  placeholder?: string;\n  min?: string;\n  max?: string;\n}\n\nconst TimePickerContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst TimeInput = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: border-color ${({ theme }) => theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:disabled {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * TimePicker Component\n *\n * A reusable time picker component for selecting hours and minutes.\n */\nconst TimePicker: React.FC<TimePickerProps> = ({\n  id,\n  name,\n  value,\n  onChange,\n  label,\n  required = false,\n  disabled = false,\n  className,\n  placeholder = \"HH:MM\",\n  min,\n  max,\n}) => {\n  return (\n    <TimePickerContainer className={className}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && <span style={{ color: \"red\" }}> *</span>}\n        </Label>\n      )}\n      <TimeInput\n        id={id}\n        name={name}\n        type=\"time\"\n        value={value}\n        onChange={onChange}\n        required={required}\n        disabled={disabled}\n        placeholder={placeholder}\n        min={min}\n        max={max}\n      />\n    </TimePickerContainer>\n  );\n};\n\nexport default TimePicker;\n", "/**\n * SelectDropdown Component\n *\n * A reusable dropdown component for selecting from a list of options.\n * MOVED TO SHARED: This component was being imported across multiple features.\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\n\ninterface SelectOption {\n  value: string;\n  label: string;\n}\n\ninterface SelectDropdownProps {\n  id: string;\n  name: string;\n  value: string;\n  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;\n  options: SelectOption[];\n  label?: string;\n  required?: boolean;\n  disabled?: boolean;\n  className?: string;\n  placeholder?: string;\n}\n\nconst SelectContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst Select = styled.select`\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.surface};\n  transition: border-color ${({ theme }) => theme.transitions.fast};\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n\n  &:disabled {\n    background-color: ${({ theme }) => theme.colors.chartGrid};\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * SelectDropdown Component\n *\n * A reusable dropdown component for selecting from a list of options.\n */\nconst SelectDropdown: React.FC<SelectDropdownProps> = ({\n  id,\n  name,\n  value,\n  onChange,\n  options,\n  label,\n  required = false,\n  disabled = false,\n  className,\n  placeholder,\n}) => {\n  return (\n    <SelectContainer className={className}>\n      {label && (\n        <Label htmlFor={id}>\n          {label}\n          {required && <span style={{ color: \"red\" }}> *</span>}\n        </Label>\n      )}\n      <Select\n        id={id}\n        name={name}\n        value={value}\n        onChange={onChange}\n        required={required}\n        disabled={disabled}\n      >\n        {placeholder && (\n          <option value=\"\" disabled>\n            {placeholder}\n          </option>\n        )}\n        {options.map((option) => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </Select>\n    </SelectContainer>\n  );\n};\n\nexport default SelectDropdown;\n", "/**\n * Trading Sessions Configuration\n *\n * Defines the hierarchical structure of trading sessions and macro periods\n * with your specific time blocks and characteristics.\n */\n\nimport {\n  TradingSession,\n  MacroPeriod,\n  SessionType,\n  MacroPeriodType,\n  SessionHierarchy,\n} from '../types/tradingSessions';\n\n/**\n * Macro Period Definitions\n */\nexport const MACRO_PERIODS: Record<MacroPeriodType, Omit<MacroPeriod, 'id'>> = {\n  [MacroPeriodType.MORNING_BREAKOUT]: {\n    type: MacroPeriodType.MORNING_BREAKOUT,\n    name: '9:50-10:10 Macro',\n    timeRange: { start: '09:50:00', end: '10:10:00' },\n    description: 'Morning breakout period - high volatility after market open',\n    characteristics: ['High Volume', 'Breakout Setups', 'Gap Fills', 'Opening Range'],\n    volatilityLevel: 5,\n    volumeLevel: 5,\n    isHighProbability: true,\n  },\n\n  [MacroPeriodType.MID_MORNING_REVERSION]: {\n    type: MacroPeriodType.MID_MORNING_REVERSION,\n    name: '10:50-11:10 Macro',\n    timeRange: { start: '10:50:00', end: '11:10:00' },\n    description: 'Mid-morning reversion period - mean reversion opportunities',\n    characteristics: ['Mean Reversion', 'Pullback Setups', 'Support/Resistance Tests'],\n    volatilityLevel: 3,\n    volumeLevel: 3,\n    isHighProbability: true,\n  },\n\n  [MacroPeriodType.PRE_LUNCH]: {\n    type: MacroPeriodType.PRE_LUNCH,\n    name: '11:50-12:10 Macro',\n    timeRange: { start: '11:50:00', end: '12:10:00' },\n    description: 'Pre-lunch macro window - specific high-activity period within lunch session',\n    characteristics: ['Consolidation', 'Range Trading', 'Pre-Lunch Activity'],\n    volatilityLevel: 2,\n    volumeLevel: 2,\n    isHighProbability: false,\n    parentMacro: MacroPeriodType.LUNCH_MACRO_EXTENDED,\n  },\n\n  [MacroPeriodType.LUNCH_MACRO_EXTENDED]: {\n    type: MacroPeriodType.LUNCH_MACRO_EXTENDED,\n    name: 'Lunch Macro (11:30-13:30)',\n    timeRange: { start: '11:30:00', end: '13:30:00' },\n    description: 'Extended lunch period spanning late morning through early afternoon',\n    characteristics: ['Multi-Session', 'Lunch Trading', 'Lower Volume', 'Transition Period'],\n    volatilityLevel: 2,\n    volumeLevel: 2,\n    isHighProbability: false,\n    isMultiSession: true,\n    spansSessions: [SessionType.NEW_YORK_AM, SessionType.NEW_YORK_PM],\n    subPeriods: [], // Will be populated with PRE_LUNCH macro\n  },\n\n  [MacroPeriodType.LUNCH_MACRO]: {\n    type: MacroPeriodType.LUNCH_MACRO,\n    name: 'Lunch Macro (12:00-13:30)',\n    timeRange: { start: '12:00:00', end: '13:30:00' },\n    description: 'Traditional lunch time trading - typically lower volume',\n    characteristics: ['Low Volume', 'Range Bound', 'Choppy Price Action'],\n    volatilityLevel: 2,\n    volumeLevel: 1,\n    isHighProbability: false,\n  },\n\n  [MacroPeriodType.POST_LUNCH]: {\n    type: MacroPeriodType.POST_LUNCH,\n    name: '13:50-14:10 Macro',\n    timeRange: { start: '13:50:00', end: '14:10:00' },\n    description: 'Post-lunch macro window',\n    characteristics: ['Volume Pickup', 'Trend Resumption'],\n    volatilityLevel: 3,\n    volumeLevel: 3,\n    isHighProbability: false,\n  },\n\n  [MacroPeriodType.PRE_CLOSE]: {\n    type: MacroPeriodType.PRE_CLOSE,\n    name: '14:50-15:10 Macro',\n    timeRange: { start: '14:50:00', end: '15:10:00' },\n    description: 'Pre-close macro window',\n    characteristics: ['Institutional Activity', 'Position Adjustments'],\n    volatilityLevel: 3,\n    volumeLevel: 4,\n    isHighProbability: false,\n  },\n\n  [MacroPeriodType.POWER_HOUR]: {\n    type: MacroPeriodType.POWER_HOUR,\n    name: '15:15-15:45 Macro (Power Hour)',\n    timeRange: { start: '15:15:00', end: '15:45:00' },\n    description: 'Last hour macro - high activity before close',\n    characteristics: ['High Volume', 'Institutional Flows', 'EOD Positioning'],\n    volatilityLevel: 4,\n    volumeLevel: 5,\n    isHighProbability: true,\n  },\n\n  [MacroPeriodType.MOC]: {\n    type: MacroPeriodType.MOC,\n    name: 'MOC (Market on Close)',\n    timeRange: { start: '15:45:00', end: '16:00:00' },\n    description: 'Market on close period',\n    characteristics: ['MOC Orders', 'Final Positioning', 'High Volume'],\n    volatilityLevel: 4,\n    volumeLevel: 5,\n    isHighProbability: false,\n  },\n\n  [MacroPeriodType.LONDON_OPEN]: {\n    type: MacroPeriodType.LONDON_OPEN,\n    name: 'London Open',\n    timeRange: { start: '08:00:00', end: '09:00:00' },\n    description: 'London market opening hour',\n    characteristics: ['European Activity', 'Currency Moves', 'News Reactions'],\n    volatilityLevel: 4,\n    volumeLevel: 4,\n    isHighProbability: true,\n  },\n\n  [MacroPeriodType.LONDON_NY_OVERLAP]: {\n    type: MacroPeriodType.LONDON_NY_OVERLAP,\n    name: 'London/NY Overlap',\n    timeRange: { start: '14:00:00', end: '16:00:00' },\n    description: 'London and New York session overlap',\n    characteristics: ['Highest Volume', 'Major Moves', 'Cross-Market Activity'],\n    volatilityLevel: 5,\n    volumeLevel: 5,\n    isHighProbability: true,\n  },\n\n  [MacroPeriodType.CUSTOM]: {\n    type: MacroPeriodType.CUSTOM,\n    name: 'Custom Period',\n    timeRange: { start: '00:00:00', end: '23:59:59' },\n    description: 'User-defined custom time period',\n    characteristics: ['Custom'],\n    volatilityLevel: 3,\n    volumeLevel: 3,\n    isHighProbability: false,\n  },\n};\n\n/**\n * Trading Session Definitions\n */\n/**\n * Build session hierarchy with overlapping macro support\n */\nexport const buildSessionHierarchy = (): SessionHierarchy => {\n  // Build sessions with their macro periods\n  const sessions: TradingSession[] = Object.values(TRADING_SESSIONS).map((sessionConfig) => ({\n    id: sessionConfig.type,\n    ...sessionConfig,\n  }));\n\n  // Build the extended lunch macro with its sub-periods\n  const extendedLunchMacro: MacroPeriod = {\n    ...MACRO_PERIODS[MacroPeriodType.LUNCH_MACRO_EXTENDED],\n    id: 'lunch-macro-extended',\n    subPeriods: [\n      {\n        ...MACRO_PERIODS[MacroPeriodType.PRE_LUNCH],\n        id: 'pre-lunch-sub',\n      },\n    ],\n  };\n\n  // Multi-session macros that span across sessions\n  const multiSessionMacros: MacroPeriod[] = [extendedLunchMacro];\n\n  // Build macros lookup with proper parent/span relationships\n  const macrosByType: Record<\n    MacroPeriodType,\n    MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] }\n  > = {} as any;\n\n  // Add session-specific macros\n  sessions.forEach((session) => {\n    session.macroPeriods.forEach((macro) => {\n      macrosByType[macro.type] = {\n        ...macro,\n        parentSession: session.type,\n      };\n    });\n  });\n\n  // Add multi-session macros\n  multiSessionMacros.forEach((macro) => {\n    macrosByType[macro.type] = {\n      ...macro,\n      spansSessions: macro.spansSessions,\n    };\n  });\n\n  return {\n    sessions,\n    sessionsByType: sessions.reduce((acc, session) => {\n      acc[session.type] = session;\n      return acc;\n    }, {} as Record<SessionType, TradingSession>),\n    macrosByType,\n    multiSessionMacros,\n  };\n};\n\n/**\n * Build session hierarchy from database sessions (legacy support)\n */\nexport const buildSessionHierarchyFromDatabase = (\n  dbSessions: Array<{\n    name: string;\n    start_time: string;\n    end_time: string;\n    description: string;\n  }>\n): SessionHierarchy => {\n  // Map database sessions to our hierarchical structure\n  const sessionMap: Record<\n    string,\n    { session?: SessionType; macro?: MacroPeriodType; isMultiSession?: boolean }\n  > = {\n    'Pre-Market': { session: SessionType.PRE_MARKET },\n    'NY Open': { session: SessionType.NEW_YORK_AM },\n    '10:50-11:10': {\n      session: SessionType.NEW_YORK_AM,\n      macro: MacroPeriodType.MID_MORNING_REVERSION,\n    },\n    '11:50-12:10': { session: SessionType.NEW_YORK_AM, macro: MacroPeriodType.PRE_LUNCH },\n    'Lunch Macro': { macro: MacroPeriodType.LUNCH_MACRO_EXTENDED, isMultiSession: true },\n    '13:50-14:10': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.POST_LUNCH },\n    '14:50-15:10': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.PRE_CLOSE },\n    '15:15-15:45': { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.POWER_HOUR },\n    MOC: { session: SessionType.NEW_YORK_PM, macro: MacroPeriodType.MOC },\n    'Post MOC': { session: SessionType.AFTER_HOURS },\n  };\n\n  const baseHierarchy = buildSessionHierarchy();\n\n  // Add database-defined macro periods to appropriate sessions\n  dbSessions.forEach((dbSession) => {\n    const mapping = sessionMap[dbSession.name];\n    if (mapping?.macro && !mapping.isMultiSession) {\n      const parentSession = baseHierarchy.sessions.find((s) => s.type === mapping.session);\n      if (parentSession) {\n        const macroConfig = MACRO_PERIODS[mapping.macro];\n        if (macroConfig) {\n          parentSession.macroPeriods.push({\n            id: `${mapping.macro}-db`,\n            ...macroConfig,\n            timeRange: {\n              start: dbSession.start_time,\n              end: dbSession.end_time,\n            },\n            description: dbSession.description,\n          });\n        }\n      }\n    }\n  });\n\n  return baseHierarchy;\n};\n\nexport const TRADING_SESSIONS: Record<SessionType, Omit<TradingSession, 'id'>> = {\n  [SessionType.NEW_YORK_AM]: {\n    type: SessionType.NEW_YORK_AM,\n    name: 'New York AM Session',\n    timeRange: { start: '09:30:00', end: '12:00:00' },\n    description: 'New York morning session - high activity and volatility',\n    timezone: 'America/New_York',\n    characteristics: ['High Volume', 'Trend Development', 'Breakout Opportunities'],\n    color: '#dc2626', // F1 Red\n    macroPeriods: [\n      { ...MACRO_PERIODS[MacroPeriodType.MORNING_BREAKOUT], id: 'morning-breakout' },\n      { ...MACRO_PERIODS[MacroPeriodType.MID_MORNING_REVERSION], id: 'mid-morning-reversion' },\n      { ...MACRO_PERIODS[MacroPeriodType.PRE_LUNCH], id: 'pre-lunch' },\n    ],\n  },\n\n  [SessionType.NEW_YORK_PM]: {\n    type: SessionType.NEW_YORK_PM,\n    name: 'New York PM Session',\n    timeRange: { start: '12:00:00', end: '16:00:00' },\n    description: 'New York afternoon session - institutional activity increases toward close',\n    timezone: 'America/New_York',\n    characteristics: ['Institutional Flows', 'EOD Positioning', 'Power Hour Activity'],\n    color: '#dc2626', // F1 Red\n    macroPeriods: [\n      { ...MACRO_PERIODS[MacroPeriodType.LUNCH_MACRO], id: 'lunch-macro' },\n      { ...MACRO_PERIODS[MacroPeriodType.POST_LUNCH], id: 'post-lunch' },\n      { ...MACRO_PERIODS[MacroPeriodType.PRE_CLOSE], id: 'pre-close' },\n      { ...MACRO_PERIODS[MacroPeriodType.POWER_HOUR], id: 'power-hour' },\n      { ...MACRO_PERIODS[MacroPeriodType.MOC], id: 'moc' },\n    ],\n  },\n\n  [SessionType.LONDON]: {\n    type: SessionType.LONDON,\n    name: 'London Session',\n    timeRange: { start: '08:00:00', end: '16:00:00' },\n    description: 'London trading session - European market activity',\n    timezone: 'Europe/London',\n    characteristics: ['European Activity', 'Currency Focus', 'News-Driven'],\n    color: '#1f2937', // Dark Gray\n    macroPeriods: [\n      { ...MACRO_PERIODS[MacroPeriodType.LONDON_OPEN], id: 'london-open' },\n      { ...MACRO_PERIODS[MacroPeriodType.LONDON_NY_OVERLAP], id: 'london-ny-overlap' },\n    ],\n  },\n\n  [SessionType.ASIA]: {\n    type: SessionType.ASIA,\n    name: 'Asia Session',\n    timeRange: { start: '18:00:00', end: '03:00:00' },\n    description: 'Asian trading session - typically lower volatility',\n    timezone: 'Asia/Tokyo',\n    characteristics: ['Lower Volume', 'Range Trading', 'News Reactions'],\n    color: '#4b5563', // Gray\n    macroPeriods: [],\n  },\n\n  [SessionType.PRE_MARKET]: {\n    type: SessionType.PRE_MARKET,\n    name: 'Pre-Market',\n    timeRange: { start: '04:00:00', end: '09:30:00' },\n    description: 'Pre-market trading hours',\n    timezone: 'America/New_York',\n    characteristics: ['Low Volume', 'News Reactions', 'Gap Setups'],\n    color: '#6b7280', // Light Gray\n    macroPeriods: [],\n  },\n\n  [SessionType.AFTER_HOURS]: {\n    type: SessionType.AFTER_HOURS,\n    name: 'After Hours',\n    timeRange: { start: '16:00:00', end: '20:00:00' },\n    description: 'After-hours trading',\n    timezone: 'America/New_York',\n    characteristics: ['Low Volume', 'Earnings Reactions', 'News-Driven'],\n    color: '#6b7280', // Light Gray\n    macroPeriods: [],\n  },\n\n  [SessionType.OVERNIGHT]: {\n    type: SessionType.OVERNIGHT,\n    name: 'Overnight',\n    timeRange: { start: '20:00:00', end: '04:00:00' },\n    description: 'Overnight session',\n    timezone: 'America/New_York',\n    characteristics: ['Very Low Volume', 'Futures Activity'],\n    color: '#374151', // Dark Gray\n    macroPeriods: [],\n  },\n};\n", "/**\n * Session Utilities\n *\n * Provides utilities for working with the hierarchical session system:\n * - Time validation and parsing\n * - Session/macro period lookup\n * - Intelligent selection logic\n * - Time range validation\n */\n\nimport {\n  TradingSession,\n  MacroPeriod,\n  SessionType,\n  MacroPeriodType,\n  SessionHierarchy,\n  SessionSelection,\n  TimeValidationResult,\n  TimeRange,\n  SessionFilterOptions,\n} from '../types/tradingSessions';\nimport { buildSessionHierarchy } from '../config/tradingSessionsConfig';\n\n/**\n * Session Utilities Class\n */\nexport class SessionUtils {\n  private static hierarchy: SessionHierarchy | null = null;\n\n  /**\n   * Initialize and get the session hierarchy\n   */\n  static getSessionHierarchy(): SessionHierarchy {\n    if (!this.hierarchy) {\n      this.hierarchy = this.buildHierarchy();\n    }\n    return this.hierarchy;\n  }\n\n  /**\n   * Build the complete session hierarchy with overlapping macro support\n   */\n  private static buildHierarchy(): SessionHierarchy {\n    return buildSessionHierarchy();\n  }\n\n  /**\n   * Parse time string to minutes since midnight\n   */\n  static timeToMinutes(timeStr: string): number {\n    const [hours, minutes, seconds = 0] = timeStr.split(':').map(Number);\n    return hours * 60 + minutes + seconds / 60;\n  }\n\n  /**\n   * Convert minutes since midnight to time string\n   */\n  static minutesToTime(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = Math.floor(minutes % 60);\n    const secs = Math.floor((minutes % 1) * 60);\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs\n      .toString()\n      .padStart(2, '0')}`;\n  }\n\n  /**\n   * Check if a time falls within a time range\n   */\n  static isTimeInRange(time: string, range: TimeRange): boolean {\n    const timeMinutes = this.timeToMinutes(time);\n    const startMinutes = this.timeToMinutes(range.start);\n    const endMinutes = this.timeToMinutes(range.end);\n\n    // Handle overnight ranges (end < start)\n    if (endMinutes < startMinutes) {\n      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;\n    }\n\n    return timeMinutes >= startMinutes && timeMinutes <= endMinutes;\n  }\n\n  /**\n   * Validate a time and suggest appropriate session/macro with overlapping support\n   */\n  static validateTime(time: string): TimeValidationResult {\n    // Basic time format validation\n    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;\n    if (!timeRegex.test(time)) {\n      return {\n        isValid: false,\n        error: 'Invalid time format. Use HH:MM or HH:MM:SS format.',\n      };\n    }\n\n    const hierarchy = this.getSessionHierarchy();\n    const matchingMacros: Array<{ type: MacroPeriodType; macro: any; isSubPeriod: boolean }> = [];\n\n    // Check for macro period matches (including overlapping ones)\n    for (const [macroType, macro] of Object.entries(hierarchy.macrosByType)) {\n      if (this.isTimeInRange(time, macro.timeRange)) {\n        matchingMacros.push({\n          type: macroType as MacroPeriodType,\n          macro,\n          isSubPeriod: !!macro.parentMacro,\n        });\n      }\n    }\n\n    // If we have matches, prioritize the most specific (sub-periods over parent macros)\n    if (matchingMacros.length > 0) {\n      // Sort by specificity: sub-periods first, then by time range size (smaller = more specific)\n      const sortedMatches = matchingMacros.sort((a, b) => {\n        if (a.isSubPeriod && !b.isSubPeriod) return -1;\n        if (!a.isSubPeriod && b.isSubPeriod) return 1;\n\n        // If both are same type, prefer smaller time range\n        const aRange =\n          this.timeToMinutes(a.macro.timeRange.end) - this.timeToMinutes(a.macro.timeRange.start);\n        const bRange =\n          this.timeToMinutes(b.macro.timeRange.end) - this.timeToMinutes(b.macro.timeRange.start);\n        return aRange - bRange;\n      });\n\n      const bestMatch = sortedMatches[0];\n      const hasOverlapping = matchingMacros.length > 1;\n\n      return {\n        isValid: true,\n        suggestedMacro: bestMatch.type,\n        suggestedSession: bestMatch.macro.parentSession || bestMatch.macro.spansSessions?.[0],\n        warning: hasOverlapping\n          ? `Time falls within ${matchingMacros.length} overlapping macro periods. Suggesting most specific: ${bestMatch.macro.name}`\n          : undefined,\n      };\n    }\n\n    // Check for session matches\n    for (const session of hierarchy.sessions) {\n      if (this.isTimeInRange(time, session.timeRange)) {\n        return {\n          isValid: true,\n          suggestedSession: session.type,\n          warning: 'Time falls within session but not in a specific macro period.',\n        };\n      }\n    }\n\n    return {\n      isValid: true,\n      warning: 'Time does not fall within any defined session or macro period.',\n    };\n  }\n\n  /**\n   * Get session by type\n   */\n  static getSession(sessionType: SessionType): TradingSession | null {\n    const hierarchy = this.getSessionHierarchy();\n    return hierarchy.sessionsByType[sessionType] || null;\n  }\n\n  /**\n   * Get macro period by type\n   */\n  static getMacroPeriod(\n    macroType: MacroPeriodType\n  ): (MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] }) | null {\n    const hierarchy = this.getSessionHierarchy();\n    return hierarchy.macrosByType[macroType] || null;\n  }\n\n  /**\n   * Get all macro periods for a session\n   */\n  static getMacroPeriodsForSession(sessionType: SessionType): MacroPeriod[] {\n    const session = this.getSession(sessionType);\n    return session?.macroPeriods || [];\n  }\n\n  /**\n   * Create a session selection\n   */\n  static createSessionSelection(\n    session?: SessionType,\n    macroPeriod?: MacroPeriodType,\n    customTimeRange?: TimeRange\n  ): SessionSelection {\n    if (macroPeriod) {\n      const macro = this.getMacroPeriod(macroPeriod);\n      return {\n        session: macro?.parentSession,\n        macroPeriod,\n        displayLabel: macro?.name || 'Unknown Macro',\n        selectionType: 'macro',\n      };\n    }\n\n    if (session) {\n      const sessionData = this.getSession(session);\n      return {\n        session,\n        displayLabel: sessionData?.name || 'Unknown Session',\n        selectionType: 'session',\n      };\n    }\n\n    if (customTimeRange) {\n      return {\n        customTimeRange,\n        displayLabel: `${customTimeRange.start} - ${customTimeRange.end}`,\n        selectionType: 'custom',\n      };\n    }\n\n    return {\n      displayLabel: 'No Selection',\n      selectionType: 'custom',\n    };\n  }\n\n  /**\n   * Filter sessions and macros based on criteria\n   */\n  static filterSessions(options: SessionFilterOptions = {}): {\n    sessions: TradingSession[];\n    macros: (MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] })[];\n  } {\n    const hierarchy = this.getSessionHierarchy();\n    let sessions = [...hierarchy.sessions];\n    let macros = Object.values(hierarchy.macrosByType);\n\n    // Filter by active status\n    if (options.activeOnly) {\n      sessions = sessions.filter((s) => s.isActive);\n    }\n\n    // Filter by session types\n    if (options.sessionTypes?.length) {\n      sessions = sessions.filter((s) => options.sessionTypes!.includes(s.type));\n    }\n\n    // Filter by macro types\n    if (options.macroTypes?.length) {\n      macros = macros.filter((m) => options.macroTypes!.includes(m.type));\n    }\n\n    // Filter by high probability\n    if (options.highProbabilityOnly) {\n      macros = macros.filter((m) => m.isHighProbability);\n    }\n\n    // Filter by volatility\n    if (options.minVolatility !== undefined) {\n      macros = macros.filter((m) => m.volatilityLevel >= options.minVolatility!);\n    }\n    if (options.maxVolatility !== undefined) {\n      macros = macros.filter((m) => m.volatilityLevel <= options.maxVolatility!);\n    }\n\n    return { sessions, macros };\n  }\n\n  /**\n   * Get current active session based on current time\n   */\n  static getCurrentSession(): SessionSelection | null {\n    const now = new Date();\n    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now\n      .getMinutes()\n      .toString()\n      .padStart(2, '0')}:00`;\n\n    const validation = this.validateTime(currentTime);\n\n    if (validation.suggestedMacro) {\n      return this.createSessionSelection(validation.suggestedSession, validation.suggestedMacro);\n    }\n\n    if (validation.suggestedSession) {\n      return this.createSessionSelection(validation.suggestedSession);\n    }\n\n    return null;\n  }\n\n  /**\n   * Check if two time ranges overlap\n   */\n  static timeRangesOverlap(range1: TimeRange, range2: TimeRange): boolean {\n    const start1 = this.timeToMinutes(range1.start);\n    const end1 = this.timeToMinutes(range1.end);\n    const start2 = this.timeToMinutes(range2.start);\n    const end2 = this.timeToMinutes(range2.end);\n\n    return Math.max(start1, start2) < Math.min(end1, end2);\n  }\n\n  /**\n   * Get display options for UI dropdowns\n   */\n  static getDisplayOptions(): {\n    sessionOptions: Array<{ value: SessionType; label: string; group: string }>;\n    macroOptions: Array<{\n      value: MacroPeriodType;\n      label: string;\n      group: string;\n      parentSession: SessionType;\n    }>;\n  } {\n    const hierarchy = this.getSessionHierarchy();\n\n    const sessionOptions = hierarchy.sessions.map((session) => ({\n      value: session.type,\n      label: session.name,\n      group: 'Sessions',\n    }));\n\n    const macroOptions = Object.values(hierarchy.macrosByType)\n      .filter((macro) => macro.parentSession) // Only include macros with valid parent sessions\n      .map((macro) => ({\n        value: macro.type,\n        label: macro.name,\n        group: hierarchy.sessionsByType[macro.parentSession!]?.name || 'Other',\n        parentSession: macro.parentSession!,\n      }));\n\n    return { sessionOptions, macroOptions };\n  }\n\n  /**\n   * Get all overlapping macro periods for a given time\n   */\n  static getOverlappingMacros(time: string): Array<{\n    type: MacroPeriodType;\n    macro: MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] };\n    isSubPeriod: boolean;\n    isMultiSession: boolean;\n  }> {\n    const hierarchy = this.getSessionHierarchy();\n    const overlapping: Array<{\n      type: MacroPeriodType;\n      macro: MacroPeriod & { parentSession?: SessionType; spansSessions?: SessionType[] };\n      isSubPeriod: boolean;\n      isMultiSession: boolean;\n    }> = [];\n\n    for (const [macroType, macro] of Object.entries(hierarchy.macrosByType)) {\n      if (this.isTimeInRange(time, macro.timeRange)) {\n        overlapping.push({\n          type: macroType as MacroPeriodType,\n          macro,\n          isSubPeriod: !!macro.parentMacro,\n          isMultiSession: !!macro.spansSessions,\n        });\n      }\n    }\n\n    return overlapping.sort((a, b) => {\n      // Sort by specificity: sub-periods first, then by time range size\n      if (a.isSubPeriod && !b.isSubPeriod) return -1;\n      if (!a.isSubPeriod && b.isSubPeriod) return 1;\n\n      const aRange =\n        this.timeToMinutes(a.macro.timeRange.end) - this.timeToMinutes(a.macro.timeRange.start);\n      const bRange =\n        this.timeToMinutes(b.macro.timeRange.end) - this.timeToMinutes(b.macro.timeRange.start);\n      return aRange - bRange;\n    });\n  }\n\n  /**\n   * Get multi-session macros\n   */\n  static getMultiSessionMacros(): MacroPeriod[] {\n    const hierarchy = this.getSessionHierarchy();\n    return hierarchy.multiSessionMacros || [];\n  }\n\n  /**\n   * Check if a macro period has sub-periods\n   */\n  static hasSubPeriods(macroType: MacroPeriodType): boolean {\n    const macro = this.getMacroPeriod(macroType);\n    return !!(macro?.subPeriods && macro.subPeriods.length > 0);\n  }\n\n  /**\n   * Get sub-periods for a macro\n   */\n  static getSubPeriods(macroType: MacroPeriodType): MacroPeriod[] {\n    const macro = this.getMacroPeriod(macroType);\n    return macro?.subPeriods || [];\n  }\n\n  /**\n   * Convert legacy session string to new session selection\n   */\n  static convertLegacySession(legacySession: string): SessionSelection | null {\n    // Map legacy session names to new system (updated with new lunch macro)\n    const legacyMapping: Record<string, { session?: SessionType; macro?: MacroPeriodType }> = {\n      'NY Open': { session: SessionType.NEW_YORK_AM },\n      'London Open': { session: SessionType.LONDON },\n      'Lunch Macro': { macro: MacroPeriodType.LUNCH_MACRO_EXTENDED }, // Updated to use extended lunch macro\n      'Lunch Macro (11:30-13:30)': { macro: MacroPeriodType.LUNCH_MACRO_EXTENDED },\n      'Lunch Macro (12:00-13:30)': { macro: MacroPeriodType.LUNCH_MACRO },\n      MOC: { macro: MacroPeriodType.MOC },\n      Overnight: { session: SessionType.OVERNIGHT },\n      'Pre-Market': { session: SessionType.PRE_MARKET },\n      'After Hours': { session: SessionType.AFTER_HOURS },\n      'Power Hour': { macro: MacroPeriodType.POWER_HOUR },\n      '10:50-11:10': { macro: MacroPeriodType.MID_MORNING_REVERSION },\n      '11:50-12:10': { macro: MacroPeriodType.PRE_LUNCH },\n      '15:15-15:45': { macro: MacroPeriodType.POWER_HOUR },\n    };\n\n    const mapping = legacyMapping[legacySession];\n    if (!mapping) return null;\n\n    return this.createSessionSelection(mapping.session, mapping.macro);\n  }\n}\n", "/**\n * useSessionSelection Hook\n *\n * React hook for managing hierarchical session selection with intelligent\n * validation and backward compatibility.\n */\n\nimport { useState, useCallback, useMemo, useEffect } from 'react';\nimport {\n  SessionSelection,\n  SessionType,\n  MacroPeriodType,\n  TimeRange,\n  SessionFilterOptions,\n  TimeValidationResult,\n} from '../types/tradingSessions';\nimport { SessionUtils } from '../utils/sessionUtils';\n\nexport interface UseSessionSelectionOptions {\n  /** Initial session selection */\n  initialSelection?: SessionSelection;\n  /** Whether to auto-detect current session */\n  autoDetectCurrent?: boolean;\n  /** Filter options for available sessions/macros */\n  filterOptions?: SessionFilterOptions;\n  /** Callback when selection changes */\n  onSelectionChange?: (selection: SessionSelection) => void;\n  /** Whether to validate time inputs */\n  validateTimes?: boolean;\n}\n\nexport interface UseSessionSelectionReturn {\n  // Current selection state\n  selection: SessionSelection;\n\n  // Selection methods\n  selectSession: (sessionType: SessionType) => void;\n  selectMacro: (macroType: MacroPeriodType) => void;\n  selectCustomRange: (timeRange: TimeRange) => void;\n  clearSelection: () => void;\n\n  // Validation\n  validateTime: (time: string) => TimeValidationResult;\n  isValidSelection: boolean;\n\n  // Available options (filtered)\n  availableSessions: Array<{ value: SessionType; label: string; group: string }>;\n  availableMacros: Array<{\n    value: MacroPeriodType;\n    label: string;\n    group: string;\n    parentSession: SessionType;\n  }>;\n\n  // Hierarchical options (grouped by parent session)\n  hierarchicalOptions: Array<{\n    session: SessionType;\n    sessionLabel: string;\n    macros: Array<{ value: MacroPeriodType; label: string }>;\n  }>;\n\n  // Current session detection\n  currentSession: SessionSelection | null;\n  isCurrentSessionActive: boolean;\n\n  // Utility methods\n  getSessionDetails: (sessionType: SessionType) => any;\n  getMacroDetails: (macroType: MacroPeriodType) => any;\n  convertLegacySession: (legacySession: string) => SessionSelection | null;\n}\n\n/**\n * useSessionSelection Hook\n */\nexport const useSessionSelection = (\n  options: UseSessionSelectionOptions = {}\n): UseSessionSelectionReturn => {\n  const {\n    initialSelection,\n    autoDetectCurrent = false,\n    filterOptions = {},\n    onSelectionChange,\n    validateTimes = true,\n  } = options;\n\n  // State\n  const [selection, setSelection] = useState<SessionSelection>(\n    initialSelection || {\n      displayLabel: 'No Selection',\n      selectionType: 'custom',\n    }\n  );\n\n  // Get current session\n  const currentSession = useMemo(() => {\n    return SessionUtils.getCurrentSession();\n  }, []);\n\n  // Check if current session is active\n  const isCurrentSessionActive = useMemo(() => {\n    return currentSession !== null;\n  }, [currentSession]);\n\n  // Get filtered options\n  const { availableSessions, availableMacros } = useMemo(() => {\n    const { sessions, macros } = SessionUtils.filterSessions(filterOptions);\n    const { sessionOptions, macroOptions } = SessionUtils.getDisplayOptions();\n\n    const filteredSessionOptions = sessionOptions.filter((opt) =>\n      sessions.some((s) => s.type === opt.value)\n    );\n\n    const filteredMacroOptions = macroOptions.filter((opt) =>\n      macros.some((m) => m.type === opt.value)\n    );\n\n    return {\n      availableSessions: filteredSessionOptions,\n      availableMacros: filteredMacroOptions,\n    };\n  }, [filterOptions]);\n\n  // Create hierarchical options\n  const hierarchicalOptions = useMemo(() => {\n    // const hierarchy = SessionUtils.getSessionHierarchy(); // Available for future session-specific logic\n\n    return availableSessions.map((sessionOpt) => {\n      // const session = hierarchy.sessionsByType[sessionOpt.value]; // Available if needed for future enhancements\n      const sessionMacros = availableMacros\n        .filter((macro) => macro.parentSession === sessionOpt.value)\n        .map((macro) => ({\n          value: macro.value,\n          label: macro.label,\n        }));\n\n      return {\n        session: sessionOpt.value,\n        sessionLabel: sessionOpt.label,\n        macros: sessionMacros,\n      };\n    });\n  }, [availableSessions, availableMacros]);\n\n  // Auto-detect current session on mount\n  useEffect(() => {\n    if (autoDetectCurrent && currentSession && !initialSelection) {\n      setSelection(currentSession);\n    }\n  }, [autoDetectCurrent, currentSession, initialSelection]);\n\n  // Notify on selection change\n  useEffect(() => {\n    onSelectionChange?.(selection);\n  }, [selection, onSelectionChange]);\n\n  // Selection methods\n  const selectSession = useCallback((sessionType: SessionType) => {\n    const newSelection = SessionUtils.createSessionSelection(sessionType);\n    setSelection(newSelection);\n  }, []);\n\n  const selectMacro = useCallback((macroType: MacroPeriodType) => {\n    const newSelection = SessionUtils.createSessionSelection(undefined, macroType);\n    setSelection(newSelection);\n  }, []);\n\n  const selectCustomRange = useCallback((timeRange: TimeRange) => {\n    const newSelection = SessionUtils.createSessionSelection(undefined, undefined, timeRange);\n    setSelection(newSelection);\n  }, []);\n\n  const clearSelection = useCallback(() => {\n    setSelection({\n      displayLabel: 'No Selection',\n      selectionType: 'custom',\n    });\n  }, []);\n\n  // Validation\n  const validateTime = useCallback(\n    (time: string): TimeValidationResult => {\n      if (!validateTimes) {\n        return { isValid: true };\n      }\n      return SessionUtils.validateTime(time);\n    },\n    [validateTimes]\n  );\n\n  const isValidSelection = useMemo(() => {\n    if (selection.selectionType === 'session' && selection.session) {\n      return SessionUtils.getSession(selection.session) !== null;\n    }\n    if (selection.selectionType === 'macro' && selection.macroPeriod) {\n      return SessionUtils.getMacroPeriod(selection.macroPeriod) !== null;\n    }\n    if (selection.selectionType === 'custom' && selection.customTimeRange) {\n      const startValid = validateTime(selection.customTimeRange.start);\n      const endValid = validateTime(selection.customTimeRange.end);\n      return startValid.isValid && endValid.isValid;\n    }\n    return selection.selectionType === 'custom' && !selection.customTimeRange;\n  }, [selection, validateTime]);\n\n  // Utility methods\n  const getSessionDetails = useCallback((sessionType: SessionType) => {\n    return SessionUtils.getSession(sessionType);\n  }, []);\n\n  const getMacroDetails = useCallback((macroType: MacroPeriodType) => {\n    return SessionUtils.getMacroPeriod(macroType);\n  }, []);\n\n  const convertLegacySession = useCallback((legacySession: string) => {\n    return SessionUtils.convertLegacySession(legacySession);\n  }, []);\n\n  return {\n    // State\n    selection,\n\n    // Selection methods\n    selectSession,\n    selectMacro,\n    selectCustomRange,\n    clearSelection,\n\n    // Validation\n    validateTime,\n    isValidSelection,\n\n    // Options\n    availableSessions,\n    availableMacros,\n    hierarchicalOptions,\n\n    // Current session\n    currentSession,\n    isCurrentSessionActive,\n\n    // Utilities\n    getSessionDetails,\n    getMacroDetails,\n    convertLegacySession,\n  };\n};\n", "/**\n * Hierarchical Session Selector Component\n *\n * A sophisticated session selector that supports both broad sessions\n * and specific macro periods with intelligent grouping and validation.\n */\n\nimport React, { useState, useMemo } from 'react';\nimport styled from 'styled-components';\nimport { SessionType, MacroPeriodType, SessionSelection } from '../../types/tradingSessions';\nimport { useSessionSelection } from '../../hooks/useSessionSelection';\nimport { SessionUtils } from '../../utils/sessionUtils';\n\nexport interface HierarchicalSessionSelectorProps {\n  /** Current selection */\n  value?: SessionSelection;\n  /** Change handler */\n  onChange: (selection: SessionSelection) => void;\n  /** Whether to show macro periods */\n  showMacroPeriods?: boolean;\n  /** Whether to show current session indicator */\n  showCurrentSession?: boolean;\n  /** Whether to allow custom time ranges */\n  allowCustomRange?: boolean;\n  /** Placeholder text */\n  placeholder?: string;\n  /** Whether the selector is disabled */\n  disabled?: boolean;\n  /** Error message */\n  error?: string;\n  /** CSS class name */\n  className?: string;\n}\n\nconst Container = styled.div<{ hasError?: boolean }>`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.sm || '8px'};\n`;\n\nconst SelectorContainer = styled.div<{ hasError?: boolean; disabled?: boolean }>`\n  position: relative;\n  border: 1px solid\n    ${({ theme, hasError }) =>\n      hasError ? theme.colors?.error || '#ef4444' : theme.colors?.border || '#4b5563'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};\n  transition: all 0.2s ease;\n  opacity: ${({ disabled }) => (disabled ? 0.6 : 1)};\n  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};\n\n  &:hover {\n    border-color: ${({ theme, hasError }) =>\n      hasError ? theme.colors?.error || '#ef4444' : theme.colors?.primary || '#dc2626'}40;\n  }\n\n  &:focus-within {\n    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || '#dc2626'}20;\n  }\n`;\n\nconst SelectedValue = styled.div`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n`;\n\nconst DropdownIcon = styled.div<{ isOpen: boolean }>`\n  transition: transform 0.2s ease;\n  transform: ${({ isOpen }) => (isOpen ? 'rotate(180deg)' : 'rotate(0deg)')};\n  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};\n`;\n\nconst DropdownMenu = styled.div<{ isOpen: boolean }>`\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);\n  max-height: 400px;\n  overflow-y: auto;\n  display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};\n`;\n\nconst MultiSessionGroup = styled.div`\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};\n  background: ${({ theme }) => theme.colors?.background || '#111827'};\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst MultiSessionHeader = styled.div<{ isSelected?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme, isSelected }) =>\n    isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.surface || '#1f2937'};\n  color: ${({ theme, isSelected }) =>\n    isSelected ? '#ffffff' : theme.colors?.textPrimary || '#ffffff'};\n  font-weight: 600;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: background-color 0.2s ease;\n  border-left: 3px solid ${({ theme }) => theme.colors?.warning || '#f59e0b'};\n\n  &:hover {\n    background: ${({ theme, isSelected }) =>\n      isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.border || '#4b5563'}40;\n  }\n`;\n\nconst MultiSessionIndicator = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.warning || '#f59e0b'};\n  font-weight: 500;\n`;\n\nconst SessionGroup = styled.div`\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst SessionHeader = styled.div<{ isSelected?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme, isSelected }) =>\n    isSelected ? theme.colors?.primary || '#dc2626' : 'transparent'};\n  color: ${({ theme, isSelected }) =>\n    isSelected ? '#ffffff' : theme.colors?.textPrimary || '#ffffff'};\n  font-weight: 600;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  transition: background-color 0.2s ease;\n\n  &:hover {\n    background: ${({ theme, isSelected }) =>\n      isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.border || '#4b5563'}40;\n  }\n`;\n\nconst MacroList = styled.div`\n  background: ${({ theme }) => theme.colors?.background || '#111827'};\n`;\n\nconst MacroItem = styled.div<{ isSelected?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n    ${({ theme }) => theme.spacing?.lg || '24px'};\n  color: ${({ theme, isSelected }) =>\n    isSelected ? theme.colors?.primary || '#dc2626' : theme.colors?.textSecondary || '#9ca3af'};\n  cursor: pointer;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  transition: all 0.2s ease;\n  border-left: 3px solid\n    ${({ theme, isSelected }) => (isSelected ? theme.colors?.primary || '#dc2626' : 'transparent')};\n\n  &:hover {\n    background: ${({ theme }) => theme.colors?.border || '#4b5563'}20;\n    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  }\n`;\n\nconst CurrentSessionIndicator = styled.div`\n  display: inline-flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.success || '#10b981'};\n  font-weight: 500;\n`;\n\nconst ErrorMessage = styled.div`\n  color: ${({ theme }) => theme.colors?.error || '#ef4444'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};\n`;\n\n/**\n * Hierarchical Session Selector Component\n */\nexport const HierarchicalSessionSelector: React.FC<HierarchicalSessionSelectorProps> = ({\n  value,\n  onChange,\n  showMacroPeriods = true,\n  showCurrentSession = true,\n  placeholder = 'Select session or macro period',\n  disabled = false,\n  error,\n  className,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const { hierarchicalOptions, currentSession, selectSession, selectMacro } = useSessionSelection({\n    onSelectionChange: onChange,\n  });\n\n  // Get multi-session macros\n  const multiSessionMacros = useMemo(() => {\n    return SessionUtils.getMultiSessionMacros();\n  }, []);\n\n  // Display value\n  const displayValue = useMemo(() => {\n    if (value?.displayLabel) {\n      return value.displayLabel;\n    }\n    return placeholder;\n  }, [value, placeholder]);\n\n  // Handle session selection\n  const handleSessionSelect = (sessionType: SessionType) => {\n    selectSession(sessionType);\n    setIsOpen(false);\n  };\n\n  // Handle macro selection\n  const handleMacroSelect = (macroType: MacroPeriodType) => {\n    selectMacro(macroType);\n    setIsOpen(false);\n  };\n\n  // Check if session is selected\n  const isSessionSelected = (sessionType: SessionType) => {\n    return value?.session === sessionType && value?.selectionType === 'session';\n  };\n\n  // Check if macro is selected\n  const isMacroSelected = (macroType: MacroPeriodType) => {\n    return value?.macroPeriod === macroType && value?.selectionType === 'macro';\n  };\n\n  // Check if session is current\n  const isCurrentSession = (sessionType: SessionType) => {\n    return currentSession?.session === sessionType;\n  };\n\n  return (\n    <Container className={className} hasError={!!error}>\n      <SelectorContainer\n        hasError={!!error}\n        disabled={disabled}\n        onClick={() => !disabled && setIsOpen(!isOpen)}\n      >\n        <SelectedValue>\n          <span>{displayValue}</span>\n          <DropdownIcon isOpen={isOpen}>▼</DropdownIcon>\n        </SelectedValue>\n\n        <DropdownMenu isOpen={isOpen}>\n          {/* Multi-Session Macros */}\n          {showMacroPeriods && multiSessionMacros.length > 0 && (\n            <MultiSessionGroup>\n              {multiSessionMacros.map((macro) => (\n                <MultiSessionHeader\n                  key={macro.type}\n                  isSelected={isMacroSelected(macro.type)}\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    handleMacroSelect(macro.type);\n                  }}\n                >\n                  <span>{macro.name}</span>\n                  <MultiSessionIndicator>🌐 MULTI-SESSION</MultiSessionIndicator>\n                </MultiSessionHeader>\n              ))}\n            </MultiSessionGroup>\n          )}\n\n          {/* Regular Session Groups */}\n          {hierarchicalOptions.map(({ session, sessionLabel, macros }) => (\n            <SessionGroup key={session}>\n              <SessionHeader\n                isSelected={isSessionSelected(session)}\n                onClick={(e) => {\n                  e.stopPropagation();\n                  handleSessionSelect(session);\n                }}\n              >\n                <span>{sessionLabel}</span>\n                {showCurrentSession && isCurrentSession(session) && (\n                  <CurrentSessionIndicator>🔴 LIVE</CurrentSessionIndicator>\n                )}\n              </SessionHeader>\n\n              {showMacroPeriods && macros.length > 0 && (\n                <MacroList>\n                  {macros.map(({ value: macroValue, label }) => (\n                    <MacroItem\n                      key={macroValue}\n                      isSelected={isMacroSelected(macroValue)}\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        handleMacroSelect(macroValue);\n                      }}\n                    >\n                      {label}\n                      {/* Show if this macro has sub-periods */}\n                      {SessionUtils.hasSubPeriods(macroValue) && (\n                        <span style={{ marginLeft: '8px', fontSize: '0.75rem', opacity: 0.7 }}>\n                          📋 Has sub-periods\n                        </span>\n                      )}\n                    </MacroItem>\n                  ))}\n                </MacroList>\n              )}\n            </SessionGroup>\n          ))}\n        </DropdownMenu>\n      </SelectorContainer>\n\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n    </Container>\n  );\n};\n", "/**\n * Dashboard Section Component\n *\n * A composable dashboard section that can render different types of content\n * based on the section name. This implements the composition pattern to\n * reduce coupling between dashboard components.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nconst SectionContainer = styled.div`\n  background: ${({ theme }) => theme.colors.surface};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  padding-bottom: ${({ theme }) => theme.spacing.sm};\n  border-bottom: 1px solid ${({ theme }) => theme.colors.border};\n`;\n\nconst SectionTitle = styled.h2`\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  margin: 0;\n`;\n\nconst SectionActions = styled.div`\n  display: flex;\n  gap: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst SectionContent = styled.div`\n  min-height: 200px;\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n  color: ${({ theme }) => theme.colors.danger};\n  text-align: center;\n`;\n\nexport interface DashboardSectionProps {\n  /** Unique identifier for the section */\n  name: string;\n  /** Display title for the section */\n  title?: string;\n  /** Content to render in the section */\n  children?: React.ReactNode;\n  /** Action buttons or controls */\n  actions?: React.ReactNode;\n  /** Loading state */\n  isLoading?: boolean;\n  /** Error state */\n  error?: string | null;\n  /** Custom className for styling */\n  className?: string;\n  /** Whether the section can be collapsed */\n  collapsible?: boolean;\n  /** Whether the section is initially collapsed */\n  defaultCollapsed?: boolean;\n}\n\nconst DashboardSection: React.FC<DashboardSectionProps> = ({\n  name,\n  title,\n  children,\n  actions,\n  isLoading = false,\n  error = null,\n  className,\n  collapsible = false,\n  defaultCollapsed = false,\n}) => {\n  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);\n\n  const handleToggleCollapse = () => {\n    if (collapsible) {\n      setIsCollapsed(!isCollapsed);\n    }\n  };\n\n  const displayTitle = title || name.charAt(0).toUpperCase() + name.slice(1);\n\n  const renderContent = () => {\n    if (error) {\n      return (\n        <ErrorState>\n          <div>\n            <div>Error loading {name}</div>\n            <div style={{ fontSize: '0.9em', marginTop: '8px' }}>{error}</div>\n          </div>\n        </ErrorState>\n      );\n    }\n\n    if (isLoading) {\n      return <LoadingState>Loading {name}...</LoadingState>;\n    }\n\n    if (!children) {\n      return <LoadingState>No {name} data available</LoadingState>;\n    }\n\n    return children;\n  };\n\n  return (\n    <SectionContainer className={className} data-section={name}>\n      <SectionHeader>\n        <SectionTitle \n          onClick={handleToggleCollapse}\n          style={{ cursor: collapsible ? 'pointer' : 'default' }}\n        >\n          {displayTitle}\n          {collapsible && (\n            <span style={{ marginLeft: '8px', fontSize: '0.8em' }}>\n              {isCollapsed ? '▶' : '▼'}\n            </span>\n          )}\n        </SectionTitle>\n        {actions && <SectionActions>{actions}</SectionActions>}\n      </SectionHeader>\n      \n      {!isCollapsed && (\n        <SectionContent>\n          {renderContent()}\n        </SectionContent>\n      )}\n    </SectionContainer>\n  );\n};\n\nexport default DashboardSection;\n", "/**\n * Setup Builder Component\n *\n * Modular setup construction matrix that replaces complex dropdown-based\n * setup classification with atomic elements that can be combined infinitely.\n *\n * MOVED TO SHARED: This component was causing circular dependencies by being\n * imported across multiple features. Now it's a shared component that can be\n * used by any feature without creating coupling.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport { SetupComponents } from '../../types';\nimport { SETUP_ELEMENTS } from '../../constants';\n\n// F1 Racing Theme Styled Components\nconst BuilderContainer = styled.div`\n  background: #1a1a1a;\n  border: 1px solid #4b5563;\n  border-radius: 8px;\n  padding: 24px;\n  margin-bottom: 16px;\n`;\n\nconst SectionTitle = styled.h3`\n  color: #ffffff;\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n  border-bottom: 2px solid #dc2626;\n  padding-bottom: 8px;\n`;\n\nconst MatrixGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst ElementSection = styled.div`\n  background: #262626;\n  border: 1px solid #4b5563;\n  border-radius: 6px;\n  padding: 16px;\n`;\n\nconst ElementTitle = styled.h4`\n  color: #ffffff;\n  font-size: 0.9rem;\n  font-weight: 600;\n  margin-bottom: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst Select = styled.select`\n  width: 100%;\n  padding: 8px 12px;\n  background: #0f0f0f;\n  border: 1px solid #4b5563;\n  border-radius: 4px;\n  color: #ffffff;\n  font-size: 0.9rem;\n\n  &:focus {\n    outline: none;\n    border-color: #dc2626;\n    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);\n  }\n\n  option {\n    background: #0f0f0f;\n    color: #ffffff;\n  }\n`;\n\nconst PreviewContainer = styled.div`\n  background: #0f0f0f;\n  border: 1px solid #4b5563;\n  border-radius: 6px;\n  padding: 16px;\n  margin-top: 16px;\n`;\n\nconst PreviewText = styled.div`\n  color: #ffffff;\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.9rem;\n  line-height: 1.4;\n  min-height: 20px;\n`;\n\nconst RequiredIndicator = styled.span`\n  color: #dc2626;\n  margin-left: 4px;\n`;\n\nconst OptionalIndicator = styled.span`\n  color: #9ca3af;\n  font-size: 0.8rem;\n  margin-left: 4px;\n`;\n\ninterface SetupBuilderProps {\n  onSetupChange: (components: SetupComponents) => void;\n  initialComponents?: SetupComponents;\n}\n\nconst SetupBuilder: React.FC<SetupBuilderProps> = ({ onSetupChange, initialComponents }) => {\n  const [components, setComponents] = useState<SetupComponents>({\n    constant: initialComponents?.constant || '',\n    action: initialComponents?.action || 'None',\n    variable: initialComponents?.variable || 'None',\n    entry: initialComponents?.entry || '',\n  });\n\n  // Update parent component when components change\n  useEffect(() => {\n    if (components.constant && components.entry) {\n      onSetupChange(components);\n    }\n  }, [components, onSetupChange]);\n\n  const handleComponentChange = (elementType: keyof SetupComponents, value: string) => {\n    setComponents((prev) => ({\n      ...prev,\n      [elementType]: value,\n    }));\n  };\n\n  const generatePreview = (): string => {\n    const { constant, action, variable, entry } = components;\n\n    if (!constant || !entry) {\n      return 'Select required elements to see setup preview...';\n    }\n\n    let preview = constant;\n\n    if (action && action !== 'None') {\n      preview += ` → ${action}`;\n    }\n\n    if (variable && variable !== 'None') {\n      preview += ` → ${variable}`;\n    }\n\n    preview += ` [${entry}]`;\n\n    return preview;\n  };\n\n  return (\n    <BuilderContainer>\n      <SectionTitle>Setup Construction Matrix</SectionTitle>\n\n      <MatrixGrid>\n        {/* Constant Element */}\n        <ElementSection>\n          <ElementTitle>\n            Constant Element\n            <RequiredIndicator>*</RequiredIndicator>\n          </ElementTitle>\n          <Select\n            value={components.constant}\n            onChange={(e) => handleComponentChange('constant', e.target.value)}\n          >\n            <option value=\"\">Select Constant</option>\n            {SETUP_ELEMENTS.constant.parentArrays.map((option) => (\n              <option key={option} value={option}>\n                {option}\n              </option>\n            ))}\n            {SETUP_ELEMENTS.constant.fvgTypes.map((option) => (\n              <option key={option} value={option}>\n                {option}\n              </option>\n            ))}\n          </Select>\n        </ElementSection>\n\n        {/* Action Element */}\n        <ElementSection>\n          <ElementTitle>\n            Action Element\n            <OptionalIndicator>(optional)</OptionalIndicator>\n          </ElementTitle>\n          <Select\n            value={components.action}\n            onChange={(e) => handleComponentChange('action', e.target.value)}\n          >\n            <option value=\"None\">None</option>\n            {SETUP_ELEMENTS.action.liquidityEvents.map((option) => (\n              <option key={option} value={option}>\n                {option}\n              </option>\n            ))}\n          </Select>\n        </ElementSection>\n\n        {/* Variable Element */}\n        <ElementSection>\n          <ElementTitle>\n            Variable Element\n            <OptionalIndicator>(optional)</OptionalIndicator>\n          </ElementTitle>\n          <Select\n            value={components.variable}\n            onChange={(e) => handleComponentChange('variable', e.target.value)}\n          >\n            <option value=\"None\">None</option>\n            {SETUP_ELEMENTS.variable.rdTypes.map((option) => (\n              <option key={option} value={option}>\n                {option}\n              </option>\n            ))}\n          </Select>\n        </ElementSection>\n\n        {/* Entry Element */}\n        <ElementSection>\n          <ElementTitle>\n            Entry Method\n            <RequiredIndicator>*</RequiredIndicator>\n          </ElementTitle>\n          <Select\n            value={components.entry}\n            onChange={(e) => handleComponentChange('entry', e.target.value)}\n          >\n            <option value=\"\">Select Entry Method</option>\n            {SETUP_ELEMENTS.entry.methods.map((option) => (\n              <option key={option} value={option}>\n                {option}\n              </option>\n            ))}\n          </Select>\n        </ElementSection>\n      </MatrixGrid>\n\n      {/* Setup Preview */}\n      <PreviewContainer>\n        <ElementTitle>Setup Preview</ElementTitle>\n        <PreviewText>{generatePreview()}</PreviewText>\n      </PreviewContainer>\n    </BuilderContainer>\n  );\n};\n\nexport default SetupBuilder;\n", "/**\n * Trade Form Header Component\n *\n * Displays the header for the trade form with title and actions\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeFormValues } from '../../types';\n\nconst PageHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xxl};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\ninterface TradeFormHeaderProps {\n  isNewTrade: boolean;\n  formValues: TradeFormValues;\n}\n\n/**\n * Trade Form Header Component\n */\nconst TradeFormHeader: React.FC<TradeFormHeaderProps> = ({ isNewTrade, formValues }) => {\n  return (\n    <PageHeader>\n      <Title>\n        {isNewTrade ? 'Add New Trade' : `Edit Trade: ${formValues.symbol} (${formValues.date})`}\n      </Title>\n    </PageHeader>\n  );\n};\n\nexport default TradeFormHeader;\n", "/**\n * Trade Form Timing Fields Component\n *\n * REFACTORED: Consolidated timing sections into unified component.\n * Combines entry timing, session selection, and trade timing analysis\n * into a single cohesive section following compositional architecture.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport { MARKET_OPTIONS } from '../../hooks';\nimport {\n  TimePicker,\n  SelectDropdown,\n  Input,\n  HierarchicalSessionSelector,\n  SessionSelection,\n  // SessionUtils, // Removed as unused\n} from '@adhd-trading-dashboard/shared';\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n`;\n\nconst TimingSection = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  overflow: hidden;\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst TimingSectionHeader = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  position: relative;\n\n  /* F1 Racing accent */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 4px;\n    height: 100%;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  }\n`;\n\nconst TimingSectionTitleRow = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst TimingSectionIcon = styled.div`\n  font-size: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n`;\n\nconst TimingSectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst TimingSectionDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;\n  line-height: 1.5;\n`;\n\nconst TimingSectionContent = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.sm || '8px'};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst HelpText = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};\n  line-height: 1.4;\n`;\n\nconst ValidationError = styled.span`\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-weight: 500;\n`;\n\ninterface TradeFormTimingFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  validationErrors: ValidationErrors;\n}\n\n/**\n * Trade Form Timing Fields Component\n */\nconst TradeFormTimingFields: React.FC<TradeFormTimingFieldsProps> = ({\n  formValues,\n  handleChange,\n  validationErrors,\n}) => {\n  return (\n    <Container>\n      {/* Unified Trade Timing Section */}\n      <TimingSection>\n        <TimingSectionHeader>\n          <TimingSectionTitleRow>\n            <TimingSectionIcon>📅</TimingSectionIcon>\n            <div>\n              <TimingSectionTitle>Trade Timing & Execution</TimingSectionTitle>\n              <TimingSectionDescription>\n                Complete timing details from entry to exit including market context\n              </TimingSectionDescription>\n            </div>\n          </TimingSectionTitleRow>\n        </TimingSectionHeader>\n\n        <TimingSectionContent>\n          {/* Entry Timing Row */}\n          <FormRow>\n            <FormGroup>\n              <Label htmlFor='entryDate'>Entry Date</Label>\n              <Input\n                id='entryDate'\n                name='entryDate'\n                type='date'\n                value={formValues.date || ''}\n                onChange={(value: string) => {\n                  const event = {\n                    target: { name: 'entryDate', value },\n                  } as React.ChangeEvent<HTMLInputElement>;\n                  handleChange(event);\n                }}\n              />\n              {validationErrors.entryDate && (\n                <ValidationError>{validationErrors.entryDate}</ValidationError>\n              )}\n              <HelpText>Date when the trade was entered</HelpText>\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor='entryTime'>Entry Time</Label>\n              <TimePicker\n                id='entryTime'\n                name='entryTime'\n                value={formValues.entryTime || ''}\n                onChange={handleChange}\n              />\n              {validationErrors.entryTime && (\n                <ValidationError>{validationErrors.entryTime}</ValidationError>\n              )}\n              <HelpText>Exact time of trade entry</HelpText>\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor='market'>Market</Label>\n              <SelectDropdown\n                id='market'\n                name='market'\n                value={formValues.market || 'Stocks'}\n                onChange={handleChange}\n                options={MARKET_OPTIONS}\n              />\n              <HelpText>Market type being traded</HelpText>\n            </FormGroup>\n          </FormRow>\n\n          {/* Trade Execution Timeline Row */}\n          <FormRow>\n            <FormGroup>\n              <Label htmlFor='rdTime'>RD Formation Time</Label>\n              <TimePicker\n                id='rdTime'\n                name='rdTime'\n                value={formValues.rdTime || ''}\n                onChange={handleChange}\n              />\n              {validationErrors.rdTime && (\n                <ValidationError>{validationErrors.rdTime}</ValidationError>\n              )}\n              <HelpText>Time when the Relative Divergence (RD) pattern formed</HelpText>\n            </FormGroup>\n\n            <FormGroup>\n              <Label htmlFor='exitTime'>Exit Time</Label>\n              <TimePicker\n                id='exitTime'\n                name='exitTime'\n                value={formValues.exitTime || ''}\n                onChange={handleChange}\n              />\n              {validationErrors.exitTime && (\n                <ValidationError>{validationErrors.exitTime}</ValidationError>\n              )}\n              <HelpText>Time when trade was exited</HelpText>\n            </FormGroup>\n          </FormRow>\n\n          {/* Session & Context Row */}\n          <FormRow>\n            <FormGroup>\n              <Label htmlFor='session'>Trading Session</Label>\n              <HierarchicalSessionSelector\n                value={\n                  (formValues.session as unknown as SessionSelection) || { session: '', macro: '' }\n                }\n                onChange={(selection: SessionSelection) => {\n                  const value =\n                    typeof selection === 'string'\n                      ? selection\n                      : `${(selection as any).session}-${(selection as any).macro}`;\n                  const event = {\n                    target: { name: 'session', value },\n                  } as React.ChangeEvent<HTMLSelectElement>;\n                  handleChange(event);\n                }}\n              />\n              <HelpText>Trading session and macro period</HelpText>\n            </FormGroup>\n          </FormRow>\n        </TimingSectionContent>\n      </TimingSection>\n    </Container>\n  );\n};\n\nexport default TradeFormTimingFields;\n", "/**\n * Trade Form Risk Fields Component\n *\n * Displays the risk management fields for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst Input = styled.input`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst HelpText = styled.span`\n  font-size: 0.8rem;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\ninterface TradeFormRiskFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  validationErrors: ValidationErrors;\n}\n\n/**\n * Trade Form Risk Fields Component\n */\nconst TradeFormRiskFields: React.FC<TradeFormRiskFieldsProps> = ({\n  formValues,\n  handleChange,\n  // validationErrors, // Removed as unused\n}) => {\n  return (\n    <>\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor='stopLoss'>Stop Loss</Label>\n          <Input\n            id='stopLoss'\n            name='stopLoss'\n            type='number'\n            step='0.01'\n            value={formValues.stopLoss || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor='takeProfit'>Take Profit</Label>\n          <Input\n            id='takeProfit'\n            name='takeProfit'\n            type='number'\n            step='0.01'\n            value={formValues.takeProfit || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n      </FormRow>\n\n      <FormRow>\n        <FormGroup>\n          <Label htmlFor='riskPoints'>Risk (Points)</Label>\n          <Input\n            id='riskPoints'\n            name='riskPoints'\n            type='number'\n            step='0.01'\n            value={formValues.riskPoints || ''}\n            onChange={handleChange}\n          />\n        </FormGroup>\n\n        <FormGroup>\n          <Label htmlFor='rMultiple'>R-Multiple</Label>\n          <Input\n            id='rMultiple'\n            name='rMultiple'\n            type='number'\n            step='0.01'\n            value={formValues.rMultiple || ''}\n            onChange={handleChange}\n            disabled\n          />\n          <HelpText>Auto-calculated from Risk Points and P/L</HelpText>\n        </FormGroup>\n      </FormRow>\n    </>\n  );\n};\n\nexport default TradeFormRiskFields;\n", "/**\n * Trade Form Strategy Fields Component\n *\n * REFACTORED: Simplified strategy section focusing on core strategy elements.\n * Removed duplicate pattern quality field and legacy setup dropdown.\n * Now uses modern SetupBuilder for setup construction.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport {\n  // Trade, // Removed as unused\n  // TradeFormData, // Removed as unused\n  SetupComponents,\n  SetupBuilder,\n} from '@adhd-trading-dashboard/shared';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\nimport { MODEL_TYPE_OPTIONS } from '../../hooks';\nimport { SelectDropdown } from '@adhd-trading-dashboard/shared';\n\nconst FormRow = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst TextArea = styled.textarea`\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  min-height: 100px;\n\n  &:focus {\n    border-color: ${({ theme }) => theme.colors.primary};\n    outline: none;\n  }\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: ${({ theme }) => theme.spacing.md} 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\ninterface TradeFormStrategyFieldsProps {\n  formValues: TradeFormValues;\n  handleChange: (\n    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>\n  ) => void;\n  validationErrors: ValidationErrors;\n  setFormValues: React.Dispatch<React.SetStateAction<TradeFormValues>>;\n}\n\n/**\n * Trade Form Strategy Fields Component\n */\nconst TradeFormStrategyFields: React.FC<TradeFormStrategyFieldsProps> = ({\n  formValues,\n  handleChange,\n  // validationErrors, // Removed as unused\n  setFormValues,\n}) => {\n  return (\n    <>\n      {/* Core Strategy Section */}\n      <SectionTitle>Core Strategy</SectionTitle>\n      <FormRow>\n        <FormGroup>\n          <SelectDropdown\n            id='modelType'\n            name='modelType'\n            label='Trading Model'\n            value={formValues.modelType || ''}\n            onChange={handleChange}\n            options={MODEL_TYPE_OPTIONS}\n            placeholder='Select Trading Model'\n          />\n        </FormGroup>\n      </FormRow>\n\n      <Divider />\n\n      {/* Setup Construction Matrix */}\n      {setFormValues && (\n        <>\n          <SectionTitle>Setup Construction</SectionTitle>\n          <SetupBuilder\n            onSetupChange={(components: SetupComponents) => {\n              setFormValues(prev => ({\n                ...prev,\n                setupComponents: components,\n              }));\n            }}\n            initialComponents={formValues.setupComponents}\n          />\n          <Divider />\n        </>\n      )}\n\n      {/* Notes Section */}\n      <SectionTitle>Strategy Notes</SectionTitle>\n      <FormGroup>\n        <Label htmlFor='notes'>Trade Notes & Strategy Details</Label>\n        <TextArea\n          id='notes'\n          name='notes'\n          value={formValues.notes || ''}\n          onChange={handleChange}\n          placeholder='Add notes about your trading strategy, setup reasoning, market conditions, etc...'\n        />\n      </FormGroup>\n    </>\n  );\n};\n\nexport default TradeFormStrategyFields;\n", "/**\n * Trade Form Actions Component\n *\n * Displays the action buttons for the trade form\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n\nconst ButtonGroup = styled.div`\n  display: flex;\n  justify-content: flex-end;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Button = styled.button`\n  padding: ${({ theme }) => theme.spacing.sm} ${({ theme }) => theme.spacing.lg};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n`;\n\nconst CancelButton = styled(Button)`\n  background-color: transparent;\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  color: ${({ theme }) => theme.colors.textSecondary};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst SubmitButton = styled(Button)`\n  background-color: ${({ theme }) => theme.colors.primary};\n  border: none;\n  color: white;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\ninterface TradeFormActionsProps {\n  isSubmitting: boolean;\n  isLoading: boolean;\n  isNewTrade: boolean;\n}\n\n/**\n * Trade Form Actions Component\n */\nconst TradeFormActions: React.FC<TradeFormActionsProps> = ({\n  isSubmitting,\n  isLoading,\n  isNewTrade,\n}) => {\n  const navigate = useNavigate();\n\n  return (\n    <ButtonGroup>\n      <CancelButton\n        type=\"button\"\n        onClick={() => {\n          console.log('Cancel button clicked, navigating to journal');\n          navigate('/journal');\n        }}\n      >\n        Cancel\n      </CancelButton>\n      <SubmitButton\n        type=\"submit\"\n        disabled={isSubmitting || isLoading}\n        data-testid={isNewTrade ? 'add-trade-button' : 'update-trade-button'}\n      >\n        {isSubmitting ? 'Saving...' : isNewTrade ? 'Add Trade' : 'Update Trade'}\n      </SubmitButton>\n    </ButtonGroup>\n  );\n};\n\nexport default TradeFormActions;\n", "/**\n * Trade Form Messages Component\n *\n * Displays error and success messages for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n\nconst ErrorMessage = styled.div`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.errorLight || 'rgba(244, 67, 54, 0.1)'};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\nconst SuccessMessage = styled.div`\n  color: ${({ theme }) => theme.colors.success};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.successLight || 'rgba(76, 175, 80, 0.1)'};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n`;\n\nconst TabInfo = styled.div`\n  color: ${({ theme }) => theme.colors.textSecondary};\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  margin-top: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n  font-style: italic;\n`;\n\ninterface TradeFormMessagesProps {\n  error: string | null;\n  success: string | null;\n  showTabInfo?: boolean;\n}\n\n/**\n * Trade Form Messages Component\n */\nconst TradeFormMessages: React.FC<TradeFormMessagesProps> = ({\n  error,\n  success,\n  showTabInfo = true,\n}) => {\n  return (\n    <>\n      {error && <ErrorMessage>{error}</ErrorMessage>}\n      {success && <SuccessMessage>{success}</SuccessMessage>}\n      {showTabInfo && <TabInfo>* Required tab for saving trade</TabInfo>}\n    </>\n  );\n};\n\nexport default TradeFormMessages;\n", "/**\n * Trade Form Loading Component\n *\n * Displays a loading overlay for the trade form\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\n\nconst LoadingOverlay = styled.div`\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.7);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n`;\n\nconst LoadingSpinner = styled.div`\n  border: 4px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top: 4px solid ${({ theme }) => theme.colors.primary};\n  width: 40px;\n  height: 40px;\n  animation: spin 1s linear infinite;\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\nconst LoadingText = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-weight: ${({ theme }) => theme.fontWeights.medium};\n`;\n\ninterface TradeFormLoadingProps {\n  isLoading: boolean;\n}\n\n/**\n * Trade Form Loading Component\n */\nconst TradeFormLoading: React.FC<TradeFormLoadingProps> = ({ isLoading }) => {\n  if (!isLoading) return null;\n\n  return (\n    <LoadingOverlay>\n      <LoadingSpinner />\n      <LoadingText>Loading trade data...</LoadingText>\n    </LoadingOverlay>\n  );\n};\n\nexport default TradeFormLoading;\n", "/**\n * DOL Analysis Component\n *\n * Main component for the DOL analysis section that combines all the individual components\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport {\n  DOLTypeSelector,\n  DOLStrengthSelector,\n  DOLReactionSelector,\n  DOLContextSelector,\n  DOLDetailedAnalysis,\n  DOLEffectivenessRating,\n} from './index';\n\nconst AnalysisContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Introduction = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst IntroTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst IntroText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0;\n  line-height: 1.5;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ValidationError = styled.div`\n  color: ${({ theme }) => theme.colors.error};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  padding: ${({ theme }) => theme.spacing.sm};\n  background-color: ${({ theme }) => theme.colors.errorLight};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\ninterface DOLAnalysisProps {\n  formValues: {\n    dolType?: string;\n    dolStrength?: string;\n    dolReaction?: string;\n    dolContext?: string[];\n    dolPriceAction?: string;\n    dolVolumeProfile?: string;\n    dolTimeOfDay?: string;\n    dolMarketStructure?: string;\n    dolEffectiveness?: string;\n    dolNotes?: string;\n  };\n  onChange: (field: string, value: any) => void;\n  validationErrors: {\n    [key: string]: string;\n  };\n}\n\nconst DOLAnalysis: React.FC<DOLAnalysisProps> = ({ formValues, onChange, validationErrors }) => {\n  return (\n    <AnalysisContainer>\n      <Introduction>\n        <IntroTitle>Draw on Liquidity (DOL) Analysis</IntroTitle>\n        <IntroText>\n          Analyze how price interacted with liquidity levels in this trade. This analysis will help\n          you understand market behavior around key levels and improve your ability to anticipate\n          price movements.\n        </IntroText>\n      </Introduction>\n\n      {/* Display validation errors if any */}\n      {validationErrors.dolAnalysis && (\n        <ValidationError>{validationErrors.dolAnalysis}</ValidationError>\n      )}\n\n      <Divider />\n\n      {/* DOL Type Selector */}\n      <DOLTypeSelector value={formValues.dolType || ''} onChange={onChange} />\n\n      <Divider />\n\n      {/* DOL Strength Selector */}\n      <DOLStrengthSelector value={formValues.dolStrength || ''} onChange={onChange} />\n\n      <Divider />\n\n      {/* DOL Reaction Selector */}\n      <DOLReactionSelector value={formValues.dolReaction || ''} onChange={onChange} />\n\n      <Divider />\n\n      {/* DOL Context Selector */}\n      <DOLContextSelector value={formValues.dolContext || []} onChange={onChange} />\n\n      <Divider />\n\n      {/* DOL Detailed Analysis */}\n      <DOLDetailedAnalysis formValues={formValues} onChange={onChange} />\n\n      <Divider />\n\n      {/* DOL Effectiveness Rating */}\n      <DOLEffectivenessRating\n        value={formValues.dolEffectiveness || '5'}\n        notes={formValues.dolNotes || ''}\n        onChange={onChange}\n      />\n    </AnalysisContainer>\n  );\n};\n\nexport default DOLAnalysis;\n", "/**\n * DOL Type Selector Component\n * \n * Component for selecting the DOL type\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_TYPE_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLTypeSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLTypeSelector: React.FC<DOLTypeSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL type change\n  const handleDOLTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolType', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Type</SectionTitle>\n      <Description>\n        Select the type of liquidity interaction that occurred in this trade.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_TYPE_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolType_${option.value}`}\n                name=\"dolType\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLTypeChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolType_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLTypeSelector;\n", "/**\n * DOL Strength Selector Component\n * \n * Component for selecting the DOL strength\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_STRENGTH_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLStrengthSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLStrengthSelector: React.FC<DOLStrengthSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL strength change\n  const handleDOLStrengthChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolStrength', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Strength</SectionTitle>\n      <Description>\n        Evaluate the strength of the liquidity interaction.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_STRENGTH_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolStrength_${option.value}`}\n                name=\"dolStrength\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLStrengthChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolStrength_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLStrengthSelector;\n", "/**\n * DOL Reaction Selector Component\n * \n * Component for selecting the DOL reaction\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_REACTION_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface DOLReactionSelectorProps {\n  value: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLReactionSelector: React.FC<DOLReactionSelectorProps> = ({ value, onChange }) => {\n  // Handle DOL reaction change\n  const handleDOLReactionChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolReaction', e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Reaction</SectionTitle>\n      <Description>\n        Describe how price reacted after the liquidity interaction.\n      </Description>\n      \n      <RadioGroup>\n        {DOL_REACTION_OPTIONS.map((option) => {\n          const [label, description] = option.label.split(' - ');\n          \n          return (\n            <RadioOption key={option.value}>\n              <RadioInput\n                type=\"radio\"\n                id={`dolReaction_${option.value}`}\n                name=\"dolReaction\"\n                value={option.value}\n                checked={value === option.value}\n                onChange={handleDOLReactionChange}\n              />\n              <RadioLabelContainer>\n                <RadioLabel htmlFor={`dolReaction_${option.value}`}>\n                  {label}\n                </RadioLabel>\n                <RadioDescription>\n                  {description}\n                </RadioDescription>\n              </RadioLabelContainer>\n            </RadioOption>\n          );\n        })}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLReactionSelector;\n", "/**\n * DOL Context Selector Component\n * \n * Component for selecting the DOL context\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DOL_CONTEXT_OPTIONS } from '../../constants/dolAnalysis';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst CheckboxGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CheckboxOption = styled.div`\n  display: flex;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst CheckboxInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst CheckboxLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\ninterface DOLContextSelectorProps {\n  value: string[];\n  onChange: (field: string, value: string[]) => void;\n}\n\nconst DOLContextSelector: React.FC<DOLContextSelectorProps> = ({ value, onChange }) => {\n  // Handle checkbox change\n  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const contextValue = e.target.value;\n    const isChecked = e.target.checked;\n    \n    let newValue: string[];\n    \n    if (isChecked) {\n      // Add to array if checked\n      newValue = [...value, contextValue];\n    } else {\n      // Remove from array if unchecked\n      newValue = value.filter(item => item !== contextValue);\n    }\n    \n    onChange('dolContext', newValue);\n  };\n\n  return (\n    <SelectorContainer>\n      <SectionTitle>DOL Context</SectionTitle>\n      <Description>\n        Select all contextual factors that apply to this liquidity interaction.\n      </Description>\n      \n      <CheckboxGroup>\n        {DOL_CONTEXT_OPTIONS.map((option) => (\n          <CheckboxOption key={option.value}>\n            <CheckboxInput\n              type=\"checkbox\"\n              id={`dolContext_${option.value}`}\n              name=\"dolContext\"\n              value={option.value}\n              checked={value.includes(option.value)}\n              onChange={handleCheckboxChange}\n            />\n            <CheckboxLabel htmlFor={`dolContext_${option.value}`}>\n              {option.label}\n            </CheckboxLabel>\n          </CheckboxOption>\n        ))}\n      </CheckboxGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default DOLContextSelector;\n", "/**\n * <PERSON><PERSON> Detailed Analysis Component\n * \n * Component for entering detailed DOL analysis information\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { \n  DOL_PRICE_ACTION_DESCRIPTIONS, \n  DOL_VOLUME_PROFILE_DESCRIPTIONS,\n  DOL_TIME_OF_DAY_DESCRIPTION,\n  DOL_MARKET_STRUCTURE_DESCRIPTION\n} from '../../constants/dolAnalysis';\n\nconst AnalysisContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst FormGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Label = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst TextArea = styled.textarea`\n  width: 100%;\n  min-height: 80px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface DOLDetailedAnalysisProps {\n  formValues: {\n    dolType?: string;\n    dolStrength?: string;\n    dolPriceAction?: string;\n    dolVolumeProfile?: string;\n    dolTimeOfDay?: string;\n    dolMarketStructure?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLDetailedAnalysis: React.FC<DOLDetailedAnalysisProps> = ({ formValues, onChange }) => {\n  // Get the appropriate price action description based on DOL type\n  const getPriceActionDescription = () => {\n    if (formValues.dolType && DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS]) {\n      return DOL_PRICE_ACTION_DESCRIPTIONS[formValues.dolType as keyof typeof DOL_PRICE_ACTION_DESCRIPTIONS];\n    }\n    return 'Describe the price action during the liquidity interaction.';\n  };\n\n  // Get the appropriate volume profile description based on DOL strength\n  const getVolumeProfileDescription = () => {\n    if (formValues.dolStrength && DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS]) {\n      return DOL_VOLUME_PROFILE_DESCRIPTIONS[formValues.dolStrength as keyof typeof DOL_VOLUME_PROFILE_DESCRIPTIONS];\n    }\n    return 'Describe the volume profile during the liquidity interaction.';\n  };\n\n  // Handle text area change\n  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange(e.target.name, e.target.value);\n  };\n\n  return (\n    <AnalysisContainer>\n      <SectionTitle>Detailed Analysis</SectionTitle>\n      <Description>\n        Provide detailed information about the liquidity interaction.\n      </Description>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolPriceAction\">Price Action</Label>\n        <Description>{getPriceActionDescription()}</Description>\n        <TextArea\n          id=\"dolPriceAction\"\n          name=\"dolPriceAction\"\n          value={formValues.dolPriceAction || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the price action...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolVolumeProfile\">Volume Profile</Label>\n        <Description>{getVolumeProfileDescription()}</Description>\n        <TextArea\n          id=\"dolVolumeProfile\"\n          name=\"dolVolumeProfile\"\n          value={formValues.dolVolumeProfile || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the volume profile...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolTimeOfDay\">Time of Day Significance</Label>\n        <Description>{DOL_TIME_OF_DAY_DESCRIPTION}</Description>\n        <TextArea\n          id=\"dolTimeOfDay\"\n          name=\"dolTimeOfDay\"\n          value={formValues.dolTimeOfDay || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the time of day significance...\"\n        />\n      </FormGroup>\n      \n      <FormGroup>\n        <Label htmlFor=\"dolMarketStructure\">Market Structure</Label>\n        <Description>{DOL_MARKET_STRUCTURE_DESCRIPTION}</Description>\n        <TextArea\n          id=\"dolMarketStructure\"\n          name=\"dolMarketStructure\"\n          value={formValues.dolMarketStructure || ''}\n          onChange={handleTextAreaChange}\n          placeholder=\"Describe the market structure...\"\n        />\n      </FormGroup>\n    </AnalysisContainer>\n  );\n};\n\nexport default DOLDetailedAnalysis;\n", "/**\n * DOL Effectiveness Rating Component\n *\n * Component for rating the effectiveness of the DOL analysis\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport {\n  // DOL_EFFECTIVENESS_OPTIONS, // Removed as unused\n  getDOLEffectivenessColor,\n  getDOLEffectivenessDescription,\n} from '../../constants/dolAnalysis';\n\nconst RatingContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst Description = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RatingSliderContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-top: ${({ theme }) => theme.spacing.md};\n`;\n\nconst SliderContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.md};\n`;\n\nconst Slider = styled.input`\n  flex: 1;\n  height: 8px;\n  -webkit-appearance: none;\n  appearance: none;\n  background: ${({ theme }) => theme.colors.background};\n  outline: none;\n  border-radius: 4px;\n\n  &::-webkit-slider-thumb {\n    -webkit-appearance: none;\n    appearance: none;\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: ${({ theme }) => theme.colors.primary};\n    cursor: pointer;\n  }\n\n  &::-moz-range-thumb {\n    width: 20px;\n    height: 20px;\n    border-radius: 50%;\n    background: ${({ theme }) => theme.colors.primary};\n    cursor: pointer;\n  }\n`;\n\nconst RatingValue = styled.div<{ color: string }>`\n  font-size: 2rem;\n  font-weight: 700;\n  color: ${({ color }) => color};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  border: 3px solid ${({ color }) => color};\n`;\n\nconst RatingDescription = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n  text-align: center;\n`;\n\nconst NotesContainer = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst NotesLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  display: block;\n  margin-bottom: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst NotesTextarea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface DOLEffectivenessRatingProps {\n  value: string;\n  notes: string;\n  onChange: (field: string, value: string) => void;\n}\n\nconst DOLEffectivenessRating: React.FC<DOLEffectivenessRatingProps> = ({\n  value,\n  notes,\n  onChange,\n}) => {\n  // Handle rating change\n  const handleRatingChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange('dolEffectiveness', e.target.value);\n  };\n\n  // Handle notes change\n  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange('dolNotes', e.target.value);\n  };\n\n  // Get rating value as number\n  const ratingValue = value ? parseInt(value) : 5;\n\n  // Get color based on rating\n  const ratingColor = getDOLEffectivenessColor(ratingValue);\n\n  // Get description based on rating\n  const ratingDescription = getDOLEffectivenessDescription(ratingValue);\n\n  return (\n    <RatingContainer>\n      <SectionTitle>DOL Effectiveness Rating</SectionTitle>\n      <Description>\n        Rate how effectively price interacted with the liquidity level and how well this interaction\n        aligned with your trade thesis.\n      </Description>\n\n      <RatingSliderContainer>\n        <SliderContainer>\n          <Slider\n            type='range'\n            min='1'\n            max='10'\n            value={value || '5'}\n            onChange={handleRatingChange}\n          />\n          <RatingValue color={ratingColor}>{ratingValue}</RatingValue>\n        </SliderContainer>\n\n        <RatingDescription>{ratingDescription}</RatingDescription>\n      </RatingSliderContainer>\n\n      <NotesContainer>\n        <NotesLabel htmlFor='dolNotes'>Additional Notes</NotesLabel>\n        <NotesTextarea\n          id='dolNotes'\n          name='dolNotes'\n          value={notes}\n          onChange={handleNotesChange}\n          placeholder='Add any additional notes about the DOL analysis...'\n        />\n      </NotesContainer>\n    </RatingContainer>\n  );\n};\n\nexport default DOLEffectivenessRating;\n", "/**\n * Criterion Selector Component\n * \n * Component for selecting a score for a specific pattern quality criterion\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { ScoreRange } from '../../types';\nimport { SCORE_RANGE_OPTIONS, PATTERN_QUALITY_CRITERIA } from '../../constants/patternQuality';\n\nconst SelectorContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst CriterionTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst CriterionDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: ${({ theme }) => theme.spacing.xs} 0;\n`;\n\nconst RadioGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n  margin-top: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst RadioOption = styled.div`\n  display: flex;\n  align-items: flex-start;\n  padding: ${({ theme }) => theme.spacing.xs};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  transition: background-color ${({ theme }) => theme.transitions.fast};\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.background};\n  }\n`;\n\nconst RadioInput = styled.input`\n  margin-right: ${({ theme }) => theme.spacing.sm};\n  margin-top: 3px;\n`;\n\nconst RadioLabelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\nconst RadioLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  font-weight: 500;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst RadioDescription = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes.xs};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin-top: 2px;\n`;\n\ninterface CriterionSelectorProps {\n  criterion: keyof typeof PATTERN_QUALITY_CRITERIA;\n  value: ScoreRange | '';\n  onChange: (field: string, value: string) => void;\n  fieldName: string;\n}\n\nconst CriterionSelector: React.FC<CriterionSelectorProps> = ({ \n  criterion, \n  value, \n  onChange,\n  fieldName\n}) => {\n  const criterionData = PATTERN_QUALITY_CRITERIA[criterion];\n\n  // Handle score change\n  const handleScoreChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    onChange(fieldName, e.target.value);\n  };\n\n  return (\n    <SelectorContainer>\n      <CriterionTitle>{criterionData.title}</CriterionTitle>\n      <CriterionDescription>{criterionData.description}</CriterionDescription>\n      \n      <RadioGroup>\n        {SCORE_RANGE_OPTIONS.map((option) => (\n          <RadioOption key={option.value}>\n            <RadioInput\n              type=\"radio\"\n              id={`${fieldName}_${option.value}`}\n              name={fieldName}\n              value={option.value}\n              checked={value === option.value}\n              onChange={handleScoreChange}\n            />\n            <RadioLabelContainer>\n              <RadioLabel htmlFor={`${fieldName}_${option.value}`}>\n                {option.label}\n              </RadioLabel>\n              <RadioDescription>\n                {criterionData[option.value as ScoreRange]}\n              </RadioDescription>\n            </RadioLabelContainer>\n          </RadioOption>\n        ))}\n      </RadioGroup>\n    </SelectorContainer>\n  );\n};\n\nexport default CriterionSelector;\n", "/**\n * Pattern Quality Assessment Component\n *\n * Main component for the pattern quality assessment section\n */\n\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport {\n  PATTERN_QUALITY_CRITERIA,\n  calculateTotalScore,\n  convertScoreToRating,\n  getRatingColor,\n  getRatingDescription,\n} from '../../constants/patternQuality';\nimport { ScoreRange } from '../../types';\nimport CriterionSelector from './CriterionSelector';\n\nconst AssessmentContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst Introduction = styled.div`\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst IntroTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.sm} 0;\n`;\n\nconst IntroText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  margin: 0;\n  line-height: 1.5;\n`;\n\nconst Divider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.md} 0;\n`;\n\nconst ScoreSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.md};\n  background-color: ${({ theme }) => theme.colors.background};\n  padding: ${({ theme }) => theme.spacing.lg};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ScoreTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0;\n`;\n\nconst ScoreDetails = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing.xl};\n`;\n\nconst ScoreValue = styled.div<{ color: string }>`\n  font-size: 3rem;\n  font-weight: 700;\n  color: ${({ color }) => color};\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100px;\n  height: 100px;\n  border-radius: 50%;\n  border: 4px solid ${({ color }) => color};\n`;\n\nconst ScoreInfo = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xs};\n`;\n\nconst ScoreDescription = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n`;\n\nconst ScoreBreakdown = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textSecondary};\n`;\n\nconst NotesSection = styled.div`\n  margin-top: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst NotesLabel = styled.label`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  display: block;\n  margin-bottom: ${({ theme }) => theme.spacing.sm};\n`;\n\nconst NotesTextarea = styled.textarea`\n  width: 100%;\n  min-height: 100px;\n  padding: ${({ theme }) => theme.spacing.sm};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.sm};\n  font-size: ${({ theme }) => theme.fontSizes.sm};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  background-color: ${({ theme }) => theme.colors.background};\n  resize: vertical;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors.primary};\n  }\n`;\n\ninterface PatternQualityAssessmentProps {\n  formValues: {\n    patternQualityClarity?: string;\n    patternQualityConfluence?: string;\n    patternQualityContext?: string;\n    patternQualityRisk?: string;\n    patternQualityReward?: string;\n    patternQualityTimeframe?: string;\n    patternQualityVolume?: string;\n    patternQualityNotes?: string;\n    patternQuality?: string;\n  };\n  onChange: (field: string, value: string) => void;\n}\n\nconst PatternQualityAssessment: React.FC<PatternQualityAssessmentProps> = ({\n  formValues,\n  onChange,\n}) => {\n  const [totalScore, setTotalScore] = useState(0);\n  const [rating, setRating] = useState(0);\n\n  // Calculate score and rating when criteria change\n  useEffect(() => {\n    const criteria: Record<string, ScoreRange> = {\n      clarity: (formValues.patternQualityClarity || '') as ScoreRange,\n      confluence: (formValues.patternQualityConfluence || '') as ScoreRange,\n      context: (formValues.patternQualityContext || '') as ScoreRange,\n      risk: (formValues.patternQualityRisk || '') as ScoreRange,\n      reward: (formValues.patternQualityReward || '') as ScoreRange,\n      timeframe: (formValues.patternQualityTimeframe || '') as ScoreRange,\n      volume: (formValues.patternQualityVolume || '') as ScoreRange,\n    };\n\n    // Only calculate if all criteria are filled\n    const allCriteriaFilled = Object.values(criteria).every(value => value && value !== undefined);\n\n    if (allCriteriaFilled) {\n      const score = calculateTotalScore(criteria);\n      const calculatedRating = convertScoreToRating(score);\n\n      setTotalScore(score);\n      setRating(calculatedRating);\n\n      // Update the patternQuality field with the calculated rating\n      onChange('patternQuality', calculatedRating.toString());\n    }\n  }, [\n    formValues.patternQualityClarity,\n    formValues.patternQualityConfluence,\n    formValues.patternQualityContext,\n    formValues.patternQualityRisk,\n    formValues.patternQualityReward,\n    formValues.patternQualityTimeframe,\n    formValues.patternQualityVolume,\n    onChange,\n  ]);\n\n  // Handle notes change\n  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    onChange('patternQualityNotes', e.target.value);\n  };\n\n  return (\n    <AssessmentContainer>\n      <Introduction>\n        <IntroTitle>Pattern Quality Assessment</IntroTitle>\n        <IntroText>\n          Evaluate the quality of your trade setup by rating each criterion below. This assessment\n          will help you objectively analyze your trade patterns and improve your decision-making\n          process.\n        </IntroText>\n      </Introduction>\n\n      <Divider />\n\n      {/* Criterion Selectors */}\n      <CriterionSelector\n        criterion='clarity'\n        value={(formValues.patternQualityClarity || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityClarity'\n      />\n\n      <CriterionSelector\n        criterion='confluence'\n        value={(formValues.patternQualityConfluence || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityConfluence'\n      />\n\n      <CriterionSelector\n        criterion='context'\n        value={(formValues.patternQualityContext || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityContext'\n      />\n\n      <CriterionSelector\n        criterion='risk'\n        value={(formValues.patternQualityRisk || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityRisk'\n      />\n\n      <CriterionSelector\n        criterion='reward'\n        value={(formValues.patternQualityReward || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityReward'\n      />\n\n      <CriterionSelector\n        criterion='timeframe'\n        value={(formValues.patternQualityTimeframe || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityTimeframe'\n      />\n\n      <CriterionSelector\n        criterion='volume'\n        value={(formValues.patternQualityVolume || '') as '' | ScoreRange}\n        onChange={onChange}\n        fieldName='patternQualityVolume'\n      />\n\n      {/* Score Display */}\n      {rating > 0 && (\n        <ScoreSection>\n          <ScoreTitle>Pattern Quality Score</ScoreTitle>\n          <ScoreDetails>\n            <ScoreValue color={getRatingColor(rating)}>{rating}</ScoreValue>\n            <ScoreInfo>\n              <ScoreDescription>{getRatingDescription(rating)}</ScoreDescription>\n              <ScoreBreakdown>\n                Total Score: {totalScore} out of {Object.keys(PATTERN_QUALITY_CRITERIA).length * 5}\n              </ScoreBreakdown>\n            </ScoreInfo>\n          </ScoreDetails>\n        </ScoreSection>\n      )}\n\n      {/* Notes Section */}\n      <NotesSection>\n        <NotesLabel htmlFor='patternQualityNotes'>Additional Notes</NotesLabel>\n        <NotesTextarea\n          id='patternQualityNotes'\n          name='patternQualityNotes'\n          value={formValues.patternQualityNotes || ''}\n          onChange={handleNotesChange}\n          placeholder='Add any additional notes about the pattern quality...'\n        />\n      </NotesSection>\n    </AssessmentContainer>\n  );\n};\n\nexport default PatternQualityAssessment;\n", "/**\n * Trade Analysis Section Component\n *\n * Unified component that consolidates Pattern Quality Assessment and DOL Analysis\n * into a single cohesive section following the DashboardSection pattern.\n *\n * ARCHITECTURE:\n * - Follows compositional design with focused sub-components\n * - Uses DashboardSection wrapper for consistent styling\n * - Maintains TypeScript strict typing\n * - F1 racing theme integration\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { DashboardSection } from '@adhd-trading-dashboard/shared';\nimport PatternQualityAssessment from '../trade-pattern-quality/PatternQualityAssessment';\nimport DOLAnalysis from '../trade-dol-analysis/TradeDOLAnalysis';\nimport { TradeFormValues } from '../../types';\nimport { ValidationErrors } from '../../hooks/useTradeValidation';\n\n// F1 Racing Theme Styled Components\nconst AnalysisContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  overflow: hidden;\n  position: relative;\n\n  /* F1 Racing accent */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 4px;\n    height: 100%;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    z-index: 1;\n  }\n`;\n\nconst SectionHeader = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst HeaderTitleRow = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst HeaderIcon = styled.div`\n  font-size: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  border: 1px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n`;\n\nconst HeaderTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst HeaderDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: ${({ theme }) => theme.spacing?.xs || '4px'} 0 0 0;\n  line-height: 1.5;\n`;\n\nconst SectionContent = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst SectionDivider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  margin: ${({ theme }) => theme.spacing?.xl || '32px'} 0;\n  opacity: 0.5;\n`;\n\nexport interface TradeAnalysisSectionProps {\n  /** Form values containing analysis data */\n  formValues: TradeFormValues;\n  /** Change handler for form updates */\n  onChange: (field: string, value: any) => void;\n  /** Validation errors */\n  validationErrors: ValidationErrors;\n  /** Whether the section is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\n/**\n * Trade Analysis Section Component\n *\n * Consolidates Pattern Quality Assessment and DOL Analysis into a unified section.\n * Follows the established DashboardSection pattern for consistency.\n */\nexport const TradeAnalysisSection: React.FC<TradeAnalysisSectionProps> = ({\n  formValues,\n  onChange,\n  validationErrors,\n  // disabled = false, // Removed as unused\n  className,\n}) => {\n  return (\n    <AnalysisContainer className={className}>\n      <SectionHeader>\n        <HeaderTitleRow>\n          <HeaderIcon>🔍</HeaderIcon>\n          <div>\n            <HeaderTitle>Trade Analysis</HeaderTitle>\n            <HeaderDescription>\n              Comprehensive pattern quality assessment and DOL analysis\n            </HeaderDescription>\n          </div>\n        </HeaderTitleRow>\n      </SectionHeader>\n\n      <SectionContent>\n        {/* Pattern Quality Assessment */}\n        <DashboardSection\n          name='pattern-quality'\n          title='Pattern Quality Assessment'\n          collapsible={true}\n          defaultCollapsed={false}\n        >\n          <PatternQualityAssessment formValues={formValues} onChange={onChange} />\n        </DashboardSection>\n\n        <SectionDivider />\n\n        {/* DOL Analysis */}\n        <DashboardSection\n          name='dol-analysis'\n          title='DOL Analysis'\n          collapsible={true}\n          defaultCollapsed={false}\n        >\n          <DOLAnalysis\n            formValues={formValues}\n            onChange={onChange}\n            validationErrors={validationErrors}\n          />\n        </DashboardSection>\n      </SectionContent>\n    </AnalysisContainer>\n  );\n};\n\nexport default TradeAnalysisSection;\n", "/**\n * Trade Form Page\n *\n * This page displays a form for adding or editing a trade entry.\n * Uses a single scrollable page layout with consolidated sections.\n *\n * REFACTORED: Consolidated duplicate sections and removed legacy components\n * - Unified Analysis section (Pattern Quality + DOL Analysis)\n * - Consolidated timing sections\n * - Removed duplicate pattern quality fields\n * - Modern setup construction with SetupBuilder\n */\n\nimport React from 'react';\nimport { useParams } from 'react-router-dom';\nimport styled from 'styled-components';\n// Removed unused imports - will be added back when needed for real data integration\nimport { TradeAnalysisSection } from './components/trade-analysis-section';\nimport {\n  TradeFormActions,\n  TradeFormBasicFields,\n  TradeFormHeader,\n  TradeFormLoading,\n  TradeFormMessages,\n  TradeFormRiskFields,\n  TradeFormStrategyFields,\n  TradeFormTimingFields,\n} from './components/trade-form';\nimport { useTradeForm } from './hooks/useTradeForm';\n\nconst PageContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.lg};\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ContentSection = styled.div`\n  background-color: ${({ theme }) => theme.colors.surface};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  box-shadow: ${({ theme }) => theme.shadows.sm};\n  position: relative; /* Required for absolute positioning of loading overlay */\n`;\n\nconst Form = styled.form`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing.xl};\n`;\n\nconst SectionContainer = styled.div`\n  background: ${({ theme }) => theme.colors.background};\n  border: 1px solid ${({ theme }) => theme.colors.border};\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  padding: ${({ theme }) => theme.spacing.lg};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes.lg};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;\n  padding-bottom: ${({ theme }) => theme.spacing.sm};\n  border-bottom: 2px solid ${({ theme }) => theme.colors.primary};\n`;\n\nconst SectionDivider = styled.hr`\n  border: none;\n  border-top: 1px solid ${({ theme }) => theme.colors.border};\n  margin: ${({ theme }) => theme.spacing.xl} 0;\n`;\n\n/**\n * TradeForm Component\n *\n * Displays a form for adding or editing trade entries in a single scrollable page\n * with all sections displayed vertically for better workflow.\n */\nconst TradeForm: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n\n  // Enhanced debugging for ID parameter\n  console.log(`TradeForm component mounted with ID parameter: \"${id}\"`);\n  console.log(`Current URL: ${window.location.href}`);\n  console.log(`Is edit mode: ${id && id !== 'new'}`);\n  console.log(`Is new trade: ${id === 'new'}`);\n\n  const {\n    formValues,\n    setFormValues,\n    handleChange,\n    handleSubmit,\n    isSubmitting,\n    isLoading,\n    error,\n    success,\n    validationErrors,\n    isNewTrade,\n    calculateProfitLoss,\n  } = useTradeForm(id);\n\n  return (\n    <PageContainer>\n      <TradeFormHeader isNewTrade={isNewTrade} formValues={formValues} />\n\n      <ContentSection>\n        <TradeFormLoading isLoading={isLoading} />\n\n        <Form onSubmit={handleSubmit}>\n          <TradeFormMessages error={error} success={success} />\n\n          {/* Basic Information Section */}\n          <TradeFormBasicFields\n            formValues={formValues}\n            handleChange={handleChange}\n            validationErrors={validationErrors}\n            calculateProfitLoss={calculateProfitLoss}\n            setFormValues={setFormValues}\n          />\n\n          {/* Timing Section */}\n          <TradeFormTimingFields\n            formValues={formValues}\n            handleChange={handleChange}\n            validationErrors={validationErrors}\n          />\n\n          {/* Strategy & Setup Section - Simplified */}\n          <SectionContainer>\n            <SectionTitle>📊 Strategy & Setup</SectionTitle>\n            <TradeFormStrategyFields\n              formValues={formValues}\n              handleChange={handleChange}\n              validationErrors={validationErrors}\n              setFormValues={setFormValues}\n            />\n          </SectionContainer>\n\n          <SectionDivider />\n\n          {/* Unified Analysis Section */}\n          <TradeAnalysisSection\n            formValues={formValues}\n            onChange={(field, value) => {\n              setFormValues((prev: any) => ({\n                ...prev,\n                [field]: value,\n              }));\n            }}\n            validationErrors={validationErrors}\n          />\n\n          {/* Pricing & P&L Section */}\n          <SectionContainer>\n            <SectionTitle>💰 Pricing & P&L</SectionTitle>\n            <TradeFormRiskFields\n              formValues={formValues}\n              handleChange={handleChange}\n              validationErrors={validationErrors}\n            />\n          </SectionContainer>\n\n          <SectionDivider />\n\n          {/* Form Actions */}\n          <TradeFormActions\n            isSubmitting={isSubmitting}\n            isLoading={isLoading}\n            isNewTrade={isNewTrade}\n          />\n        </Form>\n      </ContentSection>\n    </PageContainer>\n  );\n};\n\nexport default TradeForm;\n"], "names": ["SessionType", "LONDON", "NEW_YORK_AM", "NEW_YORK_PM", "ASIA", "PRE_MARKET", "AFTER_HOURS", "OVERNIGHT", "MacroPeriodType", "MORNING_BREAKOUT", "MID_MORNING_REVERSION", "PRE_LUNCH", "LUNCH_MACRO_EXTENDED", "LUNCH_MACRO", "POST_LUNCH", "PRE_CLOSE", "POWER_HOUR", "MOC", "LONDON_OPEN", "LONDON_NY_OVERLAP", "CUSTOM", "SETUP_ELEMENTS", "constant", "parentArrays", "fvgTypes", "action", "liquidityEvents", "variable", "rdTypes", "entry", "methods", "InputWrapper", "div", "withConfig", "displayName", "componentId", "fullWidth", "Label", "label", "theme", "fontSizes", "sm", "colors", "textSecondary", "spacing", "xxs", "fontWeights", "medium", "InputContainer", "borderRadius", "<PERSON><PERSON><PERSON><PERSON>", "hasSuccess", "isFocused", "error", "success", "primary", "border", "surface", "transitions", "fast", "disabled", "css", "background", "$size", "IconContainer", "xs", "StyledInput", "input", "textPrimary", "textDisabled", "hasStartIcon", "hasEndIcon", "md", "ClearButton", "button", "HelperTextContainer", "Input", "value", "onChange", "placeholder", "type", "name", "id", "className", "required", "autoComplete", "helperText", "startIcon", "endIcon", "loading", "clearable", "onClear", "max<PERSON><PERSON><PERSON>", "showCharCount", "size", "rest", "setIsFocused", "useState", "inputRef", "useRef", "handleClear", "current", "focus", "handleFocus", "e", "onFocus", "handleBlur", "onBlur", "showClearButton", "charCount", "length", "showCount", "undefined", "jsxs", "jsx", "target", "TimePickerContainer", "TimeInput", "chartGrid", "TimePicker", "min", "max", "color", "TimePicker$1", "SelectContainer", "Select", "select", "SelectDropdown", "options", "map", "option", "SelectDropdown$1", "MACRO_PERIODS", "timeRange", "start", "end", "description", "characteristics", "volatilityLevel", "volumeLevel", "isHighProbability", "parentMacro", "isMultiSession", "spansSessions", "subPeriods", "buildSessionHierarchy", "sessions", "Object", "values", "TRADING_SESSIONS", "sessionConfig", "multiSessionMacros", "macrosByType", "for<PERSON>ach", "session", "macroPeriods", "macro", "parentSession", "sessionsByType", "reduce", "acc", "timezone", "SessionUtils", "getSessionHierarchy", "hierarchy", "buildHierarchy", "timeToMinutes", "timeStr", "hours", "minutes", "seconds", "split", "Number", "minutesToTime", "Math", "floor", "mins", "secs", "toString", "padStart", "isTimeInRange", "time", "range", "timeMinutes", "startMinutes", "endMinutes", "validateTime", "test", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "macroType", "entries", "push", "isSubPeriod", "bestMatch", "sort", "a", "b", "aRange", "b<PERSON><PERSON><PERSON>", "hasOverlapping", "<PERSON><PERSON><PERSON><PERSON>", "suggestedSession", "warning", "getSession", "sessionType", "getMacroPeriod", "getMacroPeriodsForSession", "createSessionSelection", "macroPeriod", "customTimeRange", "displayLabel", "selectionType", "sessionData", "filterSessions", "macros", "activeOnly", "filter", "s", "isActive", "sessionTypes", "includes", "macroTypes", "m", "highProbabilityOnly", "minVolatility", "maxVolatility", "getCurrentSession", "now", "Date", "currentTime", "getHours", "getMinutes", "validation", "timeRangesOverlap", "range1", "range2", "start1", "end1", "start2", "end2", "getDisplayOptions", "sessionOptions", "group", "macroOptions", "getOverlappingMacros", "overlapping", "getMultiSessionMacros", "hasSubPeriods", "getSubPeriods", "convertLegacySession", "legacySession", "mapping", "Overnight", "__publicField", "useSessionSelection", "initialSelection", "autoDetectCurrent", "filterOptions", "onSelectionChange", "validateTimes", "selection", "setSelection", "currentSession", "useMemo", "isCurrentSessionActive", "availableSessions", "availableMacros", "filteredSessionOptions", "some", "opt", "filteredMacroOptions", "hierarchicalOptions", "sessionOpt", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "selectSession", "useCallback", "newSelection", "selectMacro", "selectCustomRange", "clearSelection", "isValidSelection", "startValid", "endValid", "getSessionDetails", "getMacroDetails", "Container", "SelectorContainer", "SelectedValue", "DropdownIcon", "isOpen", "DropdownMenu", "MultiSessionGroup", "MultiSessionHeader", "isSelected", "MultiSessionIndicator", "SessionGroup", "<PERSON><PERSON><PERSON><PERSON>", "MacroList", "MacroItem", "lg", "CurrentSessionIndicator", "ErrorMessage", "HierarchicalSessionSelector", "showMacroPeriods", "showCurrentSession", "setIsOpen", "displayValue", "handleSessionSelect", "handleMacroSelect", "isSessionSelected", "isMacroSelected", "isCurrentSession", "stopPropagation", "macroValue", "marginLeft", "fontSize", "opacity", "SectionContainer", "SectionHeader", "SectionTitle", "h2", "SectionActions", "SectionContent", "LoadingState", "ErrorState", "danger", "DashboardSection", "title", "children", "actions", "isLoading", "collapsible", "defaultCollapsed", "isCollapsed", "setIsCollapsed", "React", "handleToggleCollapse", "displayTitle", "char<PERSON>t", "toUpperCase", "slice", "renderContent", "marginTop", "cursor", "DashboardSection$1", "BuilderContainer", "h3", "MatrixGrid", "ElementSection", "ElementTitle", "h4", "PreviewContainer", "PreviewText", "RequiredIndicator", "span", "OptionalIndicator", "SetupBuilder", "onSetupChange", "initialComponents", "components", "setComponents", "handleComponentChange", "elementType", "prev", "generatePreview", "preview", "SetupBuilder$1", "<PERSON><PERSON><PERSON><PERSON>", "Title", "h1", "xxl", "TradeFormHeader", "isNewTrade", "formValues", "symbol", "date", "xl", "TimingSection", "TimingSectionHeader", "TimingSectionTitleRow", "TimingSectionIcon", "TimingSectionTitle", "TimingSectionDescription", "p", "TimingSectionContent", "FormRow", "FormGroup", "HelpText", "ValidationError", "TradeFormTimingFields", "handleChange", "validationErrors", "entryDate", "entryTime", "market", "MARKET_OPTIONS", "rdTime", "exitTime", "event", "TradeFormRiskFields", "Fragment", "stopLoss", "takeProfit", "riskPoints", "rMultiple", "TextArea", "textarea", "Divider", "hr", "TradeFormStrategyFields", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "modelType", "MODEL_TYPE_OPTIONS", "setupComponents", "notes", "ButtonGroup", "<PERSON><PERSON>", "CancelButton", "styled", "SubmitButton", "primaryDark", "TradeFormActions", "isSubmitting", "navigate", "useNavigate", "console", "log", "errorLight", "SuccessMessage", "successLight", "TabInfo", "TradeFormMessages", "showTabInfo", "LoadingOverlay", "LoadingSpinner", "LoadingText", "TradeFormLoading", "AnalysisContainer", "Introduction", "IntroTitle", "IntroText", "DOLAnalysis", "dolAnalysis", "DOLTypeSelector", "dolType", "DOLStrengthSelector", "dol<PERSON><PERSON><PERSON><PERSON>", "DOLReactionSelector", "dolReaction", "DOLContextSelector", "dolContext", "DOLDetailedAnalysis", "DOLEffectivenessRating", "dolEffectiveness", "dolNotes", "Description", "RadioGroup", "RadioOption", "RadioInput", "RadioLabelContainer", "RadioLabel", "RadioDescription", "handleDOLTypeChange", "DOL_TYPE_OPTIONS", "handleDOLStrengthChange", "DOL_STRENGTH_OPTIONS", "handleDOLReactionChange", "DOL_REACTION_OPTIONS", "CheckboxGroup", "CheckboxOption", "CheckboxInput", "CheckboxLabel", "handleCheckboxChange", "contextValue", "isChecked", "checked", "newValue", "item", "DOL_CONTEXT_OPTIONS", "getPriceActionDescription", "DOL_PRICE_ACTION_DESCRIPTIONS", "getVolumeProfileDescription", "DOL_VOLUME_PROFILE_DESCRIPTIONS", "handleTextAreaChange", "dolPriceAction", "dolVolumeProfile", "DOL_TIME_OF_DAY_DESCRIPTION", "dolTimeOfDay", "DOL_MARKET_STRUCTURE_DESCRIPTION", "dolMarketStructure", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RatingS<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Slide<PERSON>", "RatingValue", "RatingDescription", "NotesContainer", "NotesLabel", "NotesTextarea", "handleRatingChange", "handleNotesChange", "ratingValue", "parseInt", "ratingColor", "getDOLEffectivenessColor", "ratingDescription", "getDOLEffectivenessDescription", "CriterionTitle", "CriterionDescription", "CriterionSelector", "criterion", "fieldName", "criterionData", "PATTERN_QUALITY_CRITERIA", "handleScoreChange", "SCORE_RANGE_OPTIONS", "AssessmentContainer", "ScoreSection", "ScoreTitle", "ScoreDetails", "ScoreValue", "ScoreInfo", "ScoreDescription", "ScoreBreakdown", "NotesSection", "PatternQualityAssessment", "totalScore", "setTotalScore", "rating", "setRating", "criteria", "clarity", "patternQualityClarity", "confluence", "patternQualityConfluence", "context", "patternQualityContext", "risk", "patternQualityRisk", "reward", "patternQualityReward", "timeframe", "patternQualityTimeframe", "volume", "patternQualityVolume", "every", "score", "calculateTotalScore", "calculatedRating", "convertScoreToRating", "getRatingColor", "getRatingDescription", "keys", "patternQualityNotes", "HeaderTitleRow", "HeaderIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderDescription", "SectionDivider", "TradeAnalysisSection", "<PERSON><PERSON><PERSON><PERSON>", "ContentSection", "shadows", "Form", "form", "TradeForm", "useParams", "window", "location", "href", "handleSubmit", "calculateProfitLoss", "useTradeForm", "TradeFormBasicFields", "field"], "mappings": "g0BAoBYA,IAAAA,GAAAA,IAEVC,EAAAA,OAAS,SACTC,EAAAA,YAAc,cACdC,EAAAA,YAAc,cACdC,EAAAA,KAAO,OAGPC,EAAAA,WAAa,aACbC,EAAAA,YAAc,cACdC,EAAAA,UAAY,YAVFP,IAAAA,GAAAA,CAAAA,CAAAA,EAgBAQ,GAAAA,IAEVC,EAAAA,iBAAmB,mBACnBC,EAAAA,sBAAwB,wBAGxBC,EAAAA,UAAY,YACZC,EAAAA,qBAAuB,uBACvBC,EAAAA,YAAc,cACdC,EAAAA,WAAa,aAGbC,EAAAA,UAAY,YACZC,EAAAA,WAAa,aACbC,EAAAA,IAAM,MAGNC,EAAAA,YAAc,cACdC,EAAAA,kBAAoB,oBAGpBC,EAAAA,OAAS,SArBCZ,IAAAA,GAAAA,CAAAA,CAAAA,EC5BL,MAAMa,EAAiB,CAC5BC,SAAU,CACRC,aAAc,CACZ,OACA,WACA,OACA,WACA,cACA,aACA,YACA,uBACA,mBAAmB,EAErBC,SAAU,CACR,aACA,WACA,WACA,aACA,kBACA,WACA,YACA,WACA,gBAAgB,CAEpB,EACAC,OAAQ,CACNC,gBAAiB,CACf,OACA,aACA,gBACA,0BACA,YACA,eACA,gBACA,cACA,WAAW,CAEf,EACAC,SAAU,CACRC,QAAS,CACP,OACA,UACA,SACA,eACA,aAAa,CAEjB,EACAC,MAAO,CACLC,QAAS,CACP,eACA,gBACA,oBAAoB,CAExB,CACF,ECUMC,GAAsBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,4CAAA,qBAAA,EAGpB,CAAC,CAAEC,UAAAA,CAAU,IAAOA,EAAY,OAAS,MAAO,EAIrDC,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,gBAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,IAC/B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMO,cAANP,YAAAA,EAAmBQ,SAAU,IAAG,EAc1DC,GAAwBhB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,8EAAA,qBAAA,qBAAA,mBAAA,IAAA,IAAA,IAAA,EAAA,EAKd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAE/C,CAAC,CAAEF,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,EAAYC,UAAAA,CAAU,IACtCF,EAAiBX,EAAMG,OAAOW,MAC9BF,EAAmBZ,EAAMG,OAAOY,QAChCF,EAAkBb,EAAMG,OAAOa,QAC5BhB,EAAMG,OAAOc,OAEJ,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QAC9B,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,cAANnB,YAAAA,EAAmBoB,OAAQ,aAE1D,CAAC,CAAEC,SAAAA,EAAUrB,MAAAA,CAAM,IACnBqB,GACAC,EAEsBtB,CAAAA,gCAAAA,sBAAAA,EAAAA,EAAMG,OAAOoB,UAAU,EAI7C,CAAC,CAAEV,UAAAA,EAAWb,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,CAAW,IAC1CC,GACAS,gCAEMX,EACE,GAAGX,EAAMG,OAAOW,UAChBF,EACA,GAAGZ,EAAMG,OAAOY,YAChB,GAAGf,EAAMG,OAAOa,WAAW,EAGnC,CAAC,CAAEQ,MAAAA,CAAM,IAAM,CACf,OAAQA,EAAK,CACX,IAAK,QACIF,OAAAA,EAAG,CAAA,cAAA,CAAA,EAGZ,IAAK,QACIA,OAAAA,EAAG,CAAA,cAAA,CAAA,EAGZ,QACSA,OAAAA,EAAG,CAAA,cAAA,CAAA,CAGd,CACF,CAAC,EAGGG,GAAuBhC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oEAAA,UAAA,GAAA,EAIjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACjC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAU9CuB,GAAqBC,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,mDAAA,gFAAA,KAAA,IAAA,IAAA,EAAA,EAIrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAS1B,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAO2B,aAGrC,CAAC,CAAEC,aAAAA,CAAa,IAChBA,GACAT,EAAG,CAAA,iBAAA,CAAA,EAIH,CAAC,CAAEU,WAAAA,CAAW,IACdA,GACAV,EAAG,CAAA,kBAAA,CAAA,EAIH,CAAC,CAAEE,MAAAA,EAAOxB,MAAAA,CAAM,IACZwB,IAAU,QACLF,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUyB,GAClB1B,EAAMK,QAAQC,IAAON,EAAMK,QAAQqB,EAAE,EAEzCF,IAAU,QACZF,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUgC,GAClBjC,EAAMK,QAAQH,GAAMF,EAAMK,QAAQ4B,EAAE,EAG1CX,EACQtB,CAAAA,aAAAA,YAAAA,IAAAA,GAAAA,EAAAA,EAAMC,UAAUC,GAClBF,EAAMK,QAAQqB,GAAM1B,EAAMK,QAAQH,EAAE,CAGpD,EAGGgC,GAAqBC,EAAAA,OAAMzC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oDAAA,cAAA,yEAAA,0BAAA,EAItB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO2B,aACxB,CAAC,CAAE9B,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAM/B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAQhDgC,GAA6B3C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yDAAA,cAAA,UAAA,GAAA,EAGtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQC,IAC9B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,EAAOW,SAAAA,EAAUC,WAAAA,CAAW,IAClCD,EAAiBX,EAAMG,OAAOW,MAC9BF,EAAmBZ,EAAMG,OAAOY,QAC7Bf,EAAMG,OAAOC,aACrB,EAQUiC,GAA8BA,CAAC,CAC1CC,MAAAA,EACAC,SAAAA,EACAC,YAAAA,EAAc,GACdnB,SAAAA,EAAW,GACXP,MAAAA,EAAQ,GACR2B,KAAAA,EAAO,OACPC,KAAAA,EAAO,GACPC,GAAAA,EAAK,GACLC,UAAAA,EAAY,GACZC,SAAAA,EAAW,GACXC,aAAAA,EAAe,GACf/C,MAAAA,EAAQ,GACRgD,WAAAA,EAAa,GACbC,UAAAA,EACAC,QAAAA,EACAC,QAAAA,EAAU,GACVnC,QAAAA,EAAU,GACVoC,UAAAA,EAAY,GACZC,QAAAA,EACAC,UAAAA,EACAC,cAAAA,EAAgB,GAChBC,KAAAA,EAAO,SACP1D,UAAAA,EAAY,GACZ,GAAG2D,CACL,IAAM,CACJ,KAAM,CAAC3C,EAAW4C,CAAY,EAAIC,WAAS,EAAK,EAC1CC,EAAWC,SAAyB,IAAI,EAExCC,GAAcA,IAAM,CACpBT,EACMA,IAERb,EAAS,EAAE,EAEToB,EAASG,SACXH,EAASG,QAAQC,OACnB,EAGIC,EAAeC,GAA0C,CAC7DR,EAAa,EAAI,EACbD,EAAKU,SACPV,EAAKU,QAAQD,CAAC,CAChB,EAGIE,EAAcF,GAA0C,CAC5DR,EAAa,EAAK,EACdD,EAAKY,QACPZ,EAAKY,OAAOH,CAAC,CACf,EAIII,GAAkBlB,GAAab,GAAS,CAACjB,EAGzCiD,IAAYhC,GAAAA,YAAAA,EAAOiC,SAAU,EAC7BC,GAAYlB,GAAkBD,IAAcoB,QAAapB,EAAY,EAGzE,OAAAqB,EAAA,KAAClF,GAAa,CAAA,UAAAoD,EAAsB,UAAA/C,EACjCE,SAAAA,CACCA,GAAA2E,EAAA,KAAC5E,GAAM,CAAA,QAAS6C,EACb5C,SAAAA,CAAAA,EACA8C,GAAY,IAAA,EACf,EAGD6B,EAAAA,KAAAjE,GAAA,CACC,SAAU,CAAC,CAACK,EACZ,WAAY,CAAC,CAACC,EACd,SAAU,CAAC,CAACM,EACZ,MAAOkC,EACP,aAAc,CAAC,CAACP,EAChB,WAAY,CAAC,EAAEC,GAAWoB,IAC1B,UAAW,CAAC,CAACxD,EAEZmC,SAAAA,CAAaA,GAAA2B,EAAAA,IAAClD,IAAeuB,SAAUA,CAAA,CAAA,EAExC2B,EAAA,IAAChD,GAAA,CACC,IAAKgC,EACL,KAAAlB,EACA,MAAAH,EACA,SAAW2B,GAAM1B,EAAS0B,EAAEW,OAAOtC,KAAK,EACxC,YAAAE,EACA,SAAU,CAAC,EAAEnB,GAAY6B,GACzB,KAAAR,EACA,GAAAC,EACA,SAAU,CAAC,CAACE,EACZ,aAAAC,EACA,aAAc,CAAC,CAACE,EAChB,WAAY,CAAC,EAAEC,GAAWoB,IAE1B,MAAOd,EACP,UAAAF,EACA,QAASW,EACT,OAAQG,EACJX,GAAAA,CAAAA,CAAK,EAGVa,UACEnC,GAAY,CAAA,KAAK,SAAS,QAAS2B,GAAa,SAAU,GAAG,SAE9D,GAAA,CAAA,EAGDZ,GAAY0B,EAAAA,IAAAlD,GAAA,CAAewB,SAAQA,CAAA,CAAA,CAAA,EACtC,GAEEnC,GAASiC,GAAcyB,KACvBE,EAAAA,KAACtC,GAAoB,CAAA,SAAU,CAAC,CAACtB,EAAO,WAAY,CAAC,CAACC,EACpD,SAAA,CAAC4D,EAAAA,IAAA,MAAA,CAAK7D,YAASiC,CAAW,CAAA,EACzByB,WACE,MACEF,CAAAA,SAAAA,CAAAA,GACAjB,IAAcoB,QAAa,IAAIpB,GAAAA,EAClC,CAAA,EAEJ,CAEJ,CAAA,CAAA,CAEJ,EC7VMwB,GAA6BpF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAG7B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC5B,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAC7B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,YAAYC,OACvC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5CiD,GAAmBlD,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,cAAA,UAAA,qBAAA,4BAAA,sCAAA,iCAAA,uBAAA,EACjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACjB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QACrB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAI1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,QAIxB,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMG,OAAO4E,SAAS,EAUvDC,GAAwCA,CAAC,CAC7CrC,GAAAA,EACAD,KAAAA,EACAJ,MAAAA,EACAC,SAAAA,EACAxC,MAAAA,EACA8C,SAAAA,EAAW,GACXxB,SAAAA,EAAW,GACXuB,UAAAA,EACAJ,YAAAA,EAAc,QACdyC,IAAAA,EACAC,IAAAA,CACF,IAEIR,OAACG,IAAoB,UAAAjC,EAClB7C,SAAAA,CACCA,GAAA2E,EAAA,KAAC5E,GAAM,CAAA,QAAS6C,EACb5C,SAAAA,CAAAA,EACA8C,GAAa8B,EAAA,IAAA,OAAA,CAAK,MAAO,CAAEQ,MAAO,KAAA,EAAS,SAAE,KAAA,CAAA,EAChD,EAEDR,EAAAA,IAAAG,GAAA,CACC,GAAAnC,EACA,KAAAD,EACA,KAAK,OACL,MAAAJ,EACA,SAAAC,EACA,SAAAM,EACA,SAAAxB,EACA,YAAAmB,EACA,IAAAyC,EACA,IAAAC,CAAS,CAAA,CAEb,CAAA,CAAA,EAIJE,GAAeJ,GCtETK,GAAyB5F,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGzB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC5B,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,gBAAA,UAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAC7B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMO,YAAYC,OACvC,CAAC,CAAER,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5CyD,GAAgBC,EAAAA,OAAM7F,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,kBAAA,cAAA,UAAA,qBAAA,4BAAA,sCAAA,iCAAA,uBAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACjB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QACrB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAI1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,QAIxB,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMG,OAAO4E,SAAS,EAUvDS,GAAgDA,CAAC,CACrD7C,GAAAA,EACAD,KAAAA,EACAJ,MAAAA,EACAC,SAAAA,EACAkD,QAAAA,EACA1F,MAAAA,EACA8C,SAAAA,EAAW,GACXxB,SAAAA,EAAW,GACXuB,UAAAA,EACAJ,YAAAA,CACF,IAEIkC,OAACW,IAAgB,UAAAzC,EACd7C,SAAAA,CACCA,GAAA2E,EAAA,KAAC5E,GAAM,CAAA,QAAS6C,EACb5C,SAAAA,CAAAA,EACA8C,GAAa8B,EAAA,IAAA,OAAA,CAAK,MAAO,CAAEQ,MAAO,KAAA,EAAS,SAAE,KAAA,CAAA,EAChD,SAEDG,GACC,CAAA,GAAA3C,EACA,KAAAD,EACA,MAAAJ,EACA,SAAAC,EACA,SAAAM,EACA,SAAAxB,EAECmB,SAAAA,CAAAA,SACE,SAAO,CAAA,MAAM,GAAG,SAAQ,GACtBA,SACHA,EAAA,EAEDiD,EAAQC,IACPC,GAAAhB,EAAAA,IAAC,SAA0B,CAAA,MAAOgB,EAAOrD,MACtCqD,SAAO5F,EAAAA,KAAAA,EADG4F,EAAOrD,KAEpB,CACD,CAAA,EACH,CACF,CAAA,CAAA,EAIJsD,GAAeJ,GC1FFK,EAAkE,CAC7E,CAAC5H,EAAgBC,gBAAgB,EAAG,CAClCuE,KAAMxE,EAAgBC,iBACtBwE,KAAM,mBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,8DACbC,gBAAiB,CAAC,cAAe,kBAAmB,YAAa,eAAe,EAChFC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBE,qBAAqB,EAAG,CACvCsE,KAAMxE,EAAgBE,sBACtBuE,KAAM,oBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,8DACbC,gBAAiB,CAAC,iBAAkB,kBAAmB,0BAA0B,EACjFC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBG,SAAS,EAAG,CAC3BqE,KAAMxE,EAAgBG,UACtBsE,KAAM,oBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,8EACbC,gBAAiB,CAAC,gBAAiB,gBAAiB,oBAAoB,EACxEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,GACnBC,YAAarI,EAAgBI,oBAC/B,EAEA,CAACJ,EAAgBI,oBAAoB,EAAG,CACtCoE,KAAMxE,EAAgBI,qBACtBqE,KAAM,4BACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,sEACbC,gBAAiB,CAAC,gBAAiB,gBAAiB,eAAgB,mBAAmB,EACvFC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,GACnBE,eAAgB,GAChBC,cAAe,CAAC/I,EAAYE,YAAaF,EAAYG,WAAW,EAChE6I,WAAY,EACd,EAEA,CAACxI,EAAgBK,WAAW,EAAG,CAC7BmE,KAAMxE,EAAgBK,YACtBoE,KAAM,4BACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,0DACbC,gBAAiB,CAAC,aAAc,cAAe,qBAAqB,EACpEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBM,UAAU,EAAG,CAC5BkE,KAAMxE,EAAgBM,WACtBmE,KAAM,oBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,0BACbC,gBAAiB,CAAC,gBAAiB,kBAAkB,EACrDC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBO,SAAS,EAAG,CAC3BiE,KAAMxE,EAAgBO,UACtBkE,KAAM,oBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,yBACbC,gBAAiB,CAAC,yBAA0B,sBAAsB,EAClEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBQ,UAAU,EAAG,CAC5BgE,KAAMxE,EAAgBQ,WACtBiE,KAAM,iCACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,+CACbC,gBAAiB,CAAC,cAAe,sBAAuB,iBAAiB,EACzEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBS,GAAG,EAAG,CACrB+D,KAAMxE,EAAgBS,IACtBgE,KAAM,wBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,yBACbC,gBAAiB,CAAC,aAAc,oBAAqB,aAAa,EAClEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBU,WAAW,EAAG,CAC7B8D,KAAMxE,EAAgBU,YACtB+D,KAAM,cACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,6BACbC,gBAAiB,CAAC,oBAAqB,iBAAkB,gBAAgB,EACzEC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBW,iBAAiB,EAAG,CACnC6D,KAAMxE,EAAgBW,kBACtB8D,KAAM,oBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,sCACbC,gBAAiB,CAAC,iBAAkB,cAAe,uBAAuB,EAC1EC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,EAEA,CAACpI,EAAgBY,MAAM,EAAG,CACxB4D,KAAMxE,EAAgBY,OACtB6D,KAAM,gBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,kCACbC,gBAAiB,CAAC,QAAQ,EAC1BC,gBAAiB,EACjBC,YAAa,EACbC,kBAAmB,EACrB,CACF,EAQaK,GAAwBA,IAAwB,CAE3D,MAAMC,EAA6BC,OAAOC,OAAOC,EAAgB,EAAEpB,IAAwBqB,IAAA,CACzFpE,GAAIoE,EAActE,KAClB,GAAGsE,CACH,EAAA,EAeIC,EAAoC,CAZF,CACtC,GAAGnB,EAAc5H,EAAgBI,oBAAoB,EACrDsE,GAAI,uBACJ8D,WAAY,CACV,CACE,GAAGZ,EAAc5H,EAAgBG,SAAS,EAC1CuE,GAAI,eAAA,CACL,CAAA,CAKwD,EAGvDsE,EAGF,CAAA,EAGJN,OAAAA,EAASO,QAAqBC,GAAA,CACpBC,EAAAA,aAAaF,QAAmBG,GAAA,CACzBA,EAAAA,EAAM5E,IAAI,EAAI,CACzB,GAAG4E,EACHC,cAAeH,EAAQ1E,IAAAA,CACzB,CACD,CAAA,CACF,EAGDuE,EAAmBE,QAAmBG,GAAA,CACvBA,EAAAA,EAAM5E,IAAI,EAAI,CACzB,GAAG4E,EACHb,cAAea,EAAMb,aAAAA,CACvB,CACD,EAEM,CACLG,SAAAA,EACAY,eAAgBZ,EAASa,OAAO,CAACC,EAAKN,KAChCA,EAAAA,EAAQ1E,IAAI,EAAI0E,EACbM,GACN,EAAyC,EAC5CR,aAAAA,EACAD,mBAAAA,CAAAA,CAEJ,EA4DaF,GAAoE,CAC/E,CAACrJ,EAAYE,WAAW,EAAG,CACzB8E,KAAMhF,EAAYE,YAClB+E,KAAM,sBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,0DACbyB,SAAU,mBACVxB,gBAAiB,CAAC,cAAe,oBAAqB,wBAAwB,EAC9Ef,MAAO,UACPiC,aAAc,CACZ,CAAE,GAAGvB,EAAc5H,EAAgBC,gBAAgB,EAAGyE,GAAI,kBAAA,EAC1D,CAAE,GAAGkD,EAAc5H,EAAgBE,qBAAqB,EAAGwE,GAAI,uBAAA,EAC/D,CAAE,GAAGkD,EAAc5H,EAAgBG,SAAS,EAAGuE,GAAI,WAAA,CAAa,CAEpE,EAEA,CAAClF,EAAYG,WAAW,EAAG,CACzB6E,KAAMhF,EAAYG,YAClB8E,KAAM,sBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,6EACbyB,SAAU,mBACVxB,gBAAiB,CAAC,sBAAuB,kBAAmB,qBAAqB,EACjFf,MAAO,UACPiC,aAAc,CACZ,CAAE,GAAGvB,EAAc5H,EAAgBK,WAAW,EAAGqE,GAAI,aAAA,EACrD,CAAE,GAAGkD,EAAc5H,EAAgBM,UAAU,EAAGoE,GAAI,YAAA,EACpD,CAAE,GAAGkD,EAAc5H,EAAgBO,SAAS,EAAGmE,GAAI,WAAA,EACnD,CAAE,GAAGkD,EAAc5H,EAAgBQ,UAAU,EAAGkE,GAAI,YAAA,EACpD,CAAE,GAAGkD,EAAc5H,EAAgBS,GAAG,EAAGiE,GAAI,KAAA,CAAO,CAExD,EAEA,CAAClF,EAAYC,MAAM,EAAG,CACpB+E,KAAMhF,EAAYC,OAClBgF,KAAM,iBACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,oDACbyB,SAAU,gBACVxB,gBAAiB,CAAC,oBAAqB,iBAAkB,aAAa,EACtEf,MAAO,UACPiC,aAAc,CACZ,CAAE,GAAGvB,EAAc5H,EAAgBU,WAAW,EAAGgE,GAAI,aAAA,EACrD,CAAE,GAAGkD,EAAc5H,EAAgBW,iBAAiB,EAAG+D,GAAI,mBAAA,CAAqB,CAEpF,EAEA,CAAClF,EAAYI,IAAI,EAAG,CAClB4E,KAAMhF,EAAYI,KAClB6E,KAAM,eACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,qDACbyB,SAAU,aACVxB,gBAAiB,CAAC,eAAgB,gBAAiB,gBAAgB,EACnEf,MAAO,UACPiC,aAAc,CAAA,CAChB,EAEA,CAAC3J,EAAYK,UAAU,EAAG,CACxB2E,KAAMhF,EAAYK,WAClB4E,KAAM,aACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,2BACbyB,SAAU,mBACVxB,gBAAiB,CAAC,aAAc,iBAAkB,YAAY,EAC9Df,MAAO,UACPiC,aAAc,CAAA,CAChB,EAEA,CAAC3J,EAAYM,WAAW,EAAG,CACzB0E,KAAMhF,EAAYM,YAClB2E,KAAM,cACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,sBACbyB,SAAU,mBACVxB,gBAAiB,CAAC,aAAc,qBAAsB,aAAa,EACnEf,MAAO,UACPiC,aAAc,CAAA,CAChB,EAEA,CAAC3J,EAAYO,SAAS,EAAG,CACvByE,KAAMhF,EAAYO,UAClB0E,KAAM,YACNoD,UAAW,CAAEC,MAAO,WAAYC,IAAK,UAAW,EAChDC,YAAa,oBACbyB,SAAU,mBACVxB,gBAAiB,CAAC,kBAAmB,kBAAkB,EACvDf,MAAO,UACPiC,aAAc,CAAA,CAChB,CACF,ECrVO,MAAMO,CAAa,CAMxB,OAAOC,qBAAwC,CACzC,OAAC,KAAKC,YACHA,KAAAA,UAAY,KAAKC,kBAEjB,KAAKD,SACd,CAKA,OAAeC,gBAAmC,CAChD,OAAOpB,GAAsB,CAC/B,CAKA,OAAOqB,cAAcC,EAAyB,CACtC,KAAA,CAACC,EAAOC,EAASC,EAAU,CAAC,EAAIH,EAAQI,MAAM,GAAG,EAAE1C,IAAI2C,MAAM,EAC5DJ,OAAAA,EAAQ,GAAKC,EAAUC,EAAU,EAC1C,CAKA,OAAOG,cAAcJ,EAAyB,CAC5C,MAAMD,EAAQM,KAAKC,MAAMN,EAAU,EAAE,EAC/BO,EAAOF,KAAKC,MAAMN,EAAU,EAAE,EAC9BQ,EAAOH,KAAKC,MAAON,EAAU,EAAK,EAAE,EACnC,MAAA,GAAGD,EAAMU,SAAS,EAAEC,SAAS,EAAG,GAAG,KAAKH,EAAKE,WAAWC,SAAS,EAAG,GAAG,KAAKF,EAChFC,WACAC,SAAS,EAAG,GAAG,GACpB,CAKA,OAAOC,cAAcC,EAAcC,EAA2B,CACtDC,MAAAA,EAAc,KAAKjB,cAAce,CAAI,EACrCG,EAAe,KAAKlB,cAAcgB,EAAMhD,KAAK,EAC7CmD,EAAa,KAAKnB,cAAcgB,EAAM/C,GAAG,EAG/C,OAAIkD,EAAaD,EACRD,GAAeC,GAAgBD,GAAeE,EAGhDF,GAAeC,GAAgBD,GAAeE,CACvD,CAKA,OAAOC,aAAaL,EAAoC,OAGtD,GAAI,CADc,kDACHM,KAAKN,CAAI,EACf,MAAA,CACLO,QAAS,GACTvI,MAAO,oDAAA,EAIL+G,MAAAA,EAAY,KAAKD,sBACjB0B,EAAqF,CAAA,EAGhF,SAAA,CAACC,EAAWlC,CAAK,IAAKT,OAAO4C,QAAQ3B,EAAUZ,YAAY,EAChE,KAAK4B,cAAcC,EAAMzB,EAAMvB,SAAS,GAC1CwD,EAAeG,KAAK,CAClBhH,KAAM8G,EACNlC,MAAAA,EACAqC,YAAa,CAAC,CAACrC,EAAMf,WAAAA,CACtB,EAKDgD,GAAAA,EAAe/E,OAAS,EAAG,CAcvBoF,MAAAA,EAZgBL,EAAeM,KAAK,CAACC,EAAGC,IAAM,CAC9CD,GAAAA,EAAEH,aAAe,CAACI,EAAEJ,YAAoB,MAAA,GACxC,GAAA,CAACG,EAAEH,aAAeI,EAAEJ,YAAoB,MAAA,GAG5C,MAAMK,EACJ,KAAKhC,cAAc8B,EAAExC,MAAMvB,UAAUE,GAAG,EAAI,KAAK+B,cAAc8B,EAAExC,MAAMvB,UAAUC,KAAK,EAClFiE,EACJ,KAAKjC,cAAc+B,EAAEzC,MAAMvB,UAAUE,GAAG,EAAI,KAAK+B,cAAc+B,EAAEzC,MAAMvB,UAAUC,KAAK,EACxF,OAAOgE,EAASC,CAAAA,CACjB,EAE+B,CAAC,EAC3BC,EAAiBX,EAAe/E,OAAS,EAExC,MAAA,CACL8E,QAAS,GACTa,eAAgBP,EAAUlH,KAC1B0H,iBAAkBR,EAAUtC,MAAMC,iBAAiBqC,EAAAA,EAAUtC,MAAMb,gBAAhBmD,YAAAA,EAAgC,IACnFS,QAASH,EACL,qBAAqBX,EAAe/E,+DAA+DoF,EAAUtC,MAAM3E,OACnH+B,MAAAA,EAKG0C,UAAAA,KAAWU,EAAUlB,SAC9B,GAAI,KAAKkC,cAAcC,EAAM3B,EAAQrB,SAAS,EACrC,MAAA,CACLuD,QAAS,GACTc,iBAAkBhD,EAAQ1E,KAC1B2H,QAAS,+DAAA,EAKR,MAAA,CACLf,QAAS,GACTe,QAAS,gEAAA,CAEb,CAKA,OAAOC,WAAWC,EAAiD,CAE1DzC,OADW,KAAKD,sBACNL,eAAe+C,CAAW,GAAK,IAClD,CAKA,OAAOC,eACLhB,EACuF,CAEhF1B,OADW,KAAKD,sBACNX,aAAasC,CAAS,GAAK,IAC9C,CAKA,OAAOiB,0BAA0BF,EAAyC,CAClEnD,MAAAA,EAAU,KAAKkD,WAAWC,CAAW,EACpCnD,OAAAA,GAAAA,YAAAA,EAASC,eAAgB,EAClC,CAKA,OAAOqD,uBACLtD,EACAuD,EACAC,EACkB,CAClB,GAAID,EAAa,CACTrD,MAAAA,EAAQ,KAAKkD,eAAeG,CAAW,EACtC,MAAA,CACLvD,QAASE,GAAAA,YAAAA,EAAOC,cAChBoD,YAAAA,EACAE,cAAcvD,GAAAA,YAAAA,EAAO3E,OAAQ,gBAC7BmI,cAAe,OAAA,EAInB,GAAI1D,EAAS,CACL2D,MAAAA,EAAc,KAAKT,WAAWlD,CAAO,EACpC,MAAA,CACLA,QAAAA,EACAyD,cAAcE,GAAAA,YAAAA,EAAapI,OAAQ,kBACnCmI,cAAe,SAAA,EAInB,OAAIF,EACK,CACLA,gBAAAA,EACAC,aAAc,GAAGD,EAAgB5E,WAAW4E,EAAgB3E,MAC5D6E,cAAe,QAAA,EAIZ,CACLD,aAAc,eACdC,cAAe,QAAA,CAEnB,CAKA,OAAOE,eAAetF,EAAgC,GAGpD,SACMoC,MAAAA,EAAY,KAAKD,sBACvB,IAAIjB,EAAW,CAAC,GAAGkB,EAAUlB,QAAQ,EACjCqE,EAASpE,OAAOC,OAAOgB,EAAUZ,YAAY,EAGjD,OAAIxB,EAAQwF,aACVtE,EAAWA,EAASuE,OAAcC,GAAAA,EAAEC,QAAQ,IAI1C3F,EAAAA,EAAQ4F,eAAR5F,MAAAA,EAAsBlB,SACboC,EAAAA,EAASuE,OAAczF,GAAAA,EAAQ4F,aAAcC,SAASH,EAAE1I,IAAI,CAAC,IAItEgD,EAAAA,EAAQ8F,aAAR9F,MAAAA,EAAoBlB,SACbyG,EAAAA,EAAOE,OAAczF,GAAAA,EAAQ8F,WAAYD,SAASE,EAAE/I,IAAI,CAAC,GAIhEgD,EAAQgG,sBACVT,EAASA,EAAOE,OAAcM,GAAAA,EAAEnF,iBAAiB,GAI/CZ,EAAQiG,gBAAkBjH,SAC5BuG,EAASA,EAAOE,OAAQM,GAAMA,EAAErF,iBAAmBV,EAAQiG,aAAc,GAEvEjG,EAAQkG,gBAAkBlH,SAC5BuG,EAASA,EAAOE,OAAQM,GAAMA,EAAErF,iBAAmBV,EAAQkG,aAAc,GAGpE,CAAEhF,SAAAA,EAAUqE,OAAAA,CAAAA,CACrB,CAKA,OAAOY,mBAA6C,CAC5CC,MAAAA,MAAUC,KACVC,EAAc,GAAGF,EAAIG,SAAWrD,EAAAA,WAAWC,SAAS,EAAG,GAAG,KAAKiD,EAClEI,WAAW,EACXtD,SACAC,EAAAA,SAAS,EAAG,GAAG,OAEZsD,EAAa,KAAK/C,aAAa4C,CAAW,EAEhD,OAAIG,EAAWhC,eACN,KAAKO,uBAAuByB,EAAW/B,iBAAkB+B,EAAWhC,cAAc,EAGvFgC,EAAW/B,iBACN,KAAKM,uBAAuByB,EAAW/B,gBAAgB,EAGzD,IACT,CAKA,OAAOgC,kBAAkBC,EAAmBC,EAA4B,CACtE,MAAMC,EAAS,KAAKvE,cAAcqE,EAAOrG,KAAK,EACxCwG,EAAO,KAAKxE,cAAcqE,EAAOpG,GAAG,EACpCwG,EAAS,KAAKzE,cAAcsE,EAAOtG,KAAK,EACxC0G,EAAO,KAAK1E,cAAcsE,EAAOrG,GAAG,EAEnCuC,OAAAA,KAAKrD,IAAIoH,EAAQE,CAAM,EAAIjE,KAAKtD,IAAIsH,EAAME,CAAI,CACvD,CAKA,OAAOC,mBAQL,CACM7E,MAAAA,EAAY,KAAKD,sBAEjB+E,EAAiB9E,EAAUlB,SAASjB,IAAkByB,IAAA,CAC1D7E,MAAO6E,EAAQ1E,KACf1C,MAAOoH,EAAQzE,KACfkK,MAAO,UACP,EAAA,EAEIC,EAAejG,OAAOC,OAAOgB,EAAUZ,YAAY,EACtDiE,OAAQ7D,GAAUA,EAAMC,aAAa,EACrC5B,IAAgB2B,GAAA,OAAA,OACf/E,MAAO+E,EAAM5E,KACb1C,MAAOsH,EAAM3E,KACbkK,QAAO/E,EAAAA,EAAUN,eAAeF,EAAMC,aAAc,IAA7CO,YAAAA,EAAgDnF,OAAQ,QAC/D4E,cAAeD,EAAMC,aACrB,EAAA,EAEG,MAAA,CAAEqF,eAAAA,EAAgBE,aAAAA,CAAAA,CAC3B,CAKA,OAAOC,qBAAqBhE,EAKzB,CACKjB,MAAAA,EAAY,KAAKD,sBACjBmF,EAKD,CAAA,EAEM,SAAA,CAACxD,EAAWlC,CAAK,IAAKT,OAAO4C,QAAQ3B,EAAUZ,YAAY,EAChE,KAAK4B,cAAcC,EAAMzB,EAAMvB,SAAS,GAC1CiH,EAAYtD,KAAK,CACfhH,KAAM8G,EACNlC,MAAAA,EACAqC,YAAa,CAAC,CAACrC,EAAMf,YACrBC,eAAgB,CAAC,CAACc,EAAMb,aAAAA,CACzB,EAIL,OAAOuG,EAAYnD,KAAK,CAACC,EAAGC,IAAM,CAE5BD,GAAAA,EAAEH,aAAe,CAACI,EAAEJ,YAAoB,MAAA,GACxC,GAAA,CAACG,EAAEH,aAAeI,EAAEJ,YAAoB,MAAA,GAE5C,MAAMK,EACJ,KAAKhC,cAAc8B,EAAExC,MAAMvB,UAAUE,GAAG,EAAI,KAAK+B,cAAc8B,EAAExC,MAAMvB,UAAUC,KAAK,EAClFiE,EACJ,KAAKjC,cAAc+B,EAAEzC,MAAMvB,UAAUE,GAAG,EAAI,KAAK+B,cAAc+B,EAAEzC,MAAMvB,UAAUC,KAAK,EACxF,OAAOgE,EAASC,CAAAA,CACjB,CACH,CAKA,OAAOgD,uBAAuC,CAErCnF,OADW,KAAKD,sBACNZ,oBAAsB,EACzC,CAKA,OAAOiG,cAAc1D,EAAqC,CAClDlC,MAAAA,EAAQ,KAAKkD,eAAehB,CAAS,EAC3C,MAAO,CAAC,EAAElC,GAAAA,MAAAA,EAAOZ,YAAcY,EAAMZ,WAAWlC,OAAS,EAC3D,CAKA,OAAO2I,cAAc3D,EAA2C,CACxDlC,MAAAA,EAAQ,KAAKkD,eAAehB,CAAS,EACpClC,OAAAA,GAAAA,YAAAA,EAAOZ,aAAc,EAC9B,CAKA,OAAO0G,qBAAqBC,EAAgD,CAkBpEC,MAAAA,EAhBoF,CACxF,UAAW,CAAElG,QAAS1J,EAAYE,WAAY,EAC9C,cAAe,CAAEwJ,QAAS1J,EAAYC,MAAO,EAC7C,cAAe,CAAE2J,MAAOpJ,EAAgBI,oBAAqB,EAC7D,4BAA6B,CAAEgJ,MAAOpJ,EAAgBI,oBAAqB,EAC3E,4BAA6B,CAAEgJ,MAAOpJ,EAAgBK,WAAY,EAClEI,IAAK,CAAE2I,MAAOpJ,EAAgBS,GAAI,EAClC4O,UAAW,CAAEnG,QAAS1J,EAAYO,SAAU,EAC5C,aAAc,CAAEmJ,QAAS1J,EAAYK,UAAW,EAChD,cAAe,CAAEqJ,QAAS1J,EAAYM,WAAY,EAClD,aAAc,CAAEsJ,MAAOpJ,EAAgBQ,UAAW,EAClD,cAAe,CAAE4I,MAAOpJ,EAAgBE,qBAAsB,EAC9D,cAAe,CAAEkJ,MAAOpJ,EAAgBG,SAAU,EAClD,cAAe,CAAEiJ,MAAOpJ,EAAgBQ,UAAW,CAAA,EAGvB2O,CAAa,EAC3C,OAAKC,EAEE,KAAK5C,uBAAuB4C,EAAQlG,QAASkG,EAAQhG,KAAK,EAF5C,IAGvB,CACF,CA1YEkG,GADW5F,EACIE,YAAqC,MC+C/C,MAAM2F,GAAsBA,CACjC/H,EAAsC,KACR,CACxB,KAAA,CACJgI,iBAAAA,EACAC,kBAAAA,EAAoB,GACpBC,cAAAA,EAAgB,CAAC,EACjBC,kBAAAA,EACAC,cAAAA,EAAgB,EACdpI,EAAAA,EAGE,CAACqI,EAAWC,CAAY,EAAIrK,EAAAA,SAChC+J,GAAoB,CAClB7C,aAAc,eACdC,cAAe,QAAA,CAEnB,EAGMmD,EAAiBC,EAAAA,QAAQ,IACtBtG,EAAaiE,oBACnB,CAAE,CAAA,EAGCsC,EAAyBD,EAAAA,QAAQ,IAC9BD,IAAmB,KACzB,CAACA,CAAc,CAAC,EAGb,CAAEG,kBAAAA,EAAmBC,gBAAAA,CAAgB,EAAIH,UAAQ,IAAM,CACrD,KAAA,CAAEtH,SAAAA,EAAUqE,OAAAA,CAAAA,EAAWrD,EAAaoD,eAAe4C,CAAa,EAChE,CAAEhB,eAAAA,EAAgBE,aAAAA,CAAAA,EAAiBlF,EAAa+E,oBAEhD2B,EAAyB1B,EAAezB,OAC5CvE,GAAAA,EAAS2H,KAAMnD,GAAMA,EAAE1I,OAAS8L,EAAIjM,KAAK,CAC3C,EAEMkM,GAAuB3B,EAAa3B,OACxCF,GAAAA,EAAOsD,KAAM9C,GAAMA,EAAE/I,OAAS8L,EAAIjM,KAAK,CACzC,EAEO,MAAA,CACL6L,kBAAmBE,EACnBD,gBAAiBI,EAAAA,CACnB,EACC,CAACb,CAAa,CAAC,EAGZc,EAAsBR,EAAAA,QAAQ,IAG3BE,EAAkBzI,IAAoBgJ,GAAA,CAErCC,MAAAA,EAAgBP,EACnBlD,OAAkB7D,GAAAA,EAAMC,gBAAkBoH,EAAWpM,KAAK,EAC1DoD,IAAgB2B,IAAA,CACf/E,MAAO+E,EAAM/E,MACbvC,MAAOsH,EAAMtH,KACb,EAAA,EAEG,MAAA,CACLoH,QAASuH,EAAWpM,MACpBsM,aAAcF,EAAW3O,MACzBiL,OAAQ2D,CAAAA,CACV,CACD,EACA,CAACR,EAAmBC,CAAe,CAAC,EAGvCS,EAAAA,UAAU,IAAM,CACVnB,GAAqBM,GAAkB,CAACP,GAC1CM,EAAaC,CAAc,CAE5B,EAAA,CAACN,EAAmBM,EAAgBP,CAAgB,CAAC,EAGxDoB,EAAAA,UAAU,IAAM,CACdjB,GAAAA,MAAAA,EAAoBE,EAAS,EAC5B,CAACA,EAAWF,CAAiB,CAAC,EAG3BkB,MAAAA,EAAgBC,cAAazE,GAA6B,CACxD0E,MAAAA,EAAerH,EAAa8C,uBAAuBH,CAAW,EACpEyD,EAAaiB,CAAY,CAC3B,EAAG,CAAE,CAAA,EAECC,EAAcF,cAAaxF,GAA+B,CAC9D,MAAMyF,EAAerH,EAAa8C,uBAAuBhG,OAAW8E,CAAS,EAC7EwE,EAAaiB,CAAY,CAC3B,EAAG,CAAE,CAAA,EAECE,EAAoBH,cAAajJ,GAAyB,CAC9D,MAAMkJ,EAAerH,EAAa8C,uBAAuBhG,OAAWA,OAAWqB,CAAS,EACxFiI,EAAaiB,CAAY,CAC3B,EAAG,CAAE,CAAA,EAECG,EAAiBJ,EAAAA,YAAY,IAAM,CAC1BhB,EAAA,CACXnD,aAAc,eACdC,cAAe,QAAA,CAChB,CACH,EAAG,CAAE,CAAA,EAGC1B,EAAe4F,cAClBjG,GACM+E,EAGElG,EAAawB,aAAaL,CAAI,EAF5B,CAAEO,QAAS,EAAA,EAItB,CAACwE,CAAa,CAChB,EAEMuB,EAAmBnB,EAAAA,QAAQ,IAAM,CACrC,GAAIH,EAAUjD,gBAAkB,WAAaiD,EAAU3G,QACrD,OAAOQ,EAAa0C,WAAWyD,EAAU3G,OAAO,IAAM,KAExD,GAAI2G,EAAUjD,gBAAkB,SAAWiD,EAAUpD,YACnD,OAAO/C,EAAa4C,eAAeuD,EAAUpD,WAAW,IAAM,KAEhE,GAAIoD,EAAUjD,gBAAkB,UAAYiD,EAAUnD,gBAAiB,CACrE,MAAM0E,EAAalG,EAAa2E,EAAUnD,gBAAgB5E,KAAK,EACzDuJ,EAAWnG,EAAa2E,EAAUnD,gBAAgB3E,GAAG,EACpDqJ,OAAAA,EAAWhG,SAAWiG,EAASjG,QAExC,OAAOyE,EAAUjD,gBAAkB,UAAY,CAACiD,EAAUnD,eAAAA,EACzD,CAACmD,EAAW3E,CAAY,CAAC,EAGtBoG,EAAoBR,cAAazE,GAC9B3C,EAAa0C,WAAWC,CAAW,EACzC,CAAE,CAAA,EAECkF,EAAkBT,cAAaxF,GAC5B5B,EAAa4C,eAAehB,CAAS,EAC3C,CAAE,CAAA,EAEC4D,EAAuB4B,cAAa3B,GACjCzF,EAAawF,qBAAqBC,CAAa,EACrD,CAAE,CAAA,EAEE,MAAA,CAELU,UAAAA,EAGAgB,cAAAA,EACAG,YAAAA,EACAC,kBAAAA,EACAC,eAAAA,EAGAhG,aAAAA,EACAiG,iBAAAA,EAGAjB,kBAAAA,EACAC,gBAAAA,EACAK,oBAAAA,EAGAT,eAAAA,EACAE,uBAAAA,EAGAqB,kBAAAA,EACAC,gBAAAA,EACArC,qBAAAA,CAAAA,CAEJ,ECnNMsC,GAAmBhQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeE,KAAM,MAAK,EAG5CwP,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,sCAAA,kBAAA,eAAA,qCAAA,mBAAA,yBAAA,mCAAA,yBAAA,MAAA,EAG9B,CAAC,CAAEI,MAAAA,EAAOW,SAAAA,CAAS,aACnBA,OAAAA,IAAWX,EAAAA,EAAMG,SAANH,YAAAA,EAAcc,QAAS,YAAYd,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,WACzD,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBiC,KAAM,OAC5C,CAAC,CAAEjC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckB,UAAW,WAE3C,CAAC,CAAEG,SAAAA,CAAS,IAAOA,EAAW,GAAM,EAC7B,CAAC,CAAEA,SAAAA,CAAS,IAAOA,EAAW,OAAS,OAGvC,CAAC,CAAErB,MAAAA,EAAOW,SAAAA,CAAS,aACjCA,OAAAA,IAAWX,EAAAA,EAAMG,SAANH,YAAAA,EAAcc,QAAS,YAAYd,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,WAIzD,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,WAChC,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,UAAS,EAIvE2O,GAAuBlQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,UAAA,cAAA,gFAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeiC,KAAM,QACtC,CAAC,CAAEjC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,WACxC,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBiC,KAAM,OAAM,EAOrD2N,GAAsBnQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,4CAAA,UAAA,GAAA,EAEhB,CAAC,CAAEiQ,OAAAA,CAAO,IAAOA,EAAS,iBAAmB,eACjD,CAAC,CAAE7P,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,gBAAiB,UAAS,EAG5D0P,GAAsBrQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qEAAA,qBAAA,kBAAA,yFAAA,GAAA,EAMf,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckB,UAAW,WAClC,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,WAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBiC,KAAM,OAI/C,CAAC,CAAE4N,OAAAA,CAAO,IAAOA,EAAS,QAAU,MAAO,EAGlDE,GAA2BtQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,eAAA,oCAAA,EACP,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,WACpD,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,aAAc,UAAS,EAO9DyO,GAA4BvQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,eAAA,UAAA,6JAAA,uBAAA,MAAA,EACxB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeiC,KAAM,QACjC,CAAC,CAAEjC,MAAAA,EAAOiQ,WAAAA,CAAW,aACjCA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,YAAYhB,EAAAA,EAAMG,SAANH,YAAAA,EAAckB,UAAW,WACpE,CAAC,CAAElB,MAAAA,EAAOiQ,WAAAA,CAAW,IAC5BA,OAAAA,OAAAA,EAAa,YAAYjQ,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,WAO/B,CAAC,CAAE7B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcoK,UAAW,WAGjD,CAAC,CAAEpK,MAAAA,EAAOiQ,WAAAA,CAAW,aACjCA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,YAAYhB,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,UAAS,EAInFiP,GAA+BzQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,wBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,8CAAA,cAAA,UAAA,mBAAA,EAG/B,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,OAC9B,CAAC,CAAE1B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiB0B,KAAM,WAC1C,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcoK,UAAW,UAAS,EAItD+F,GAAsB1Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2BAAA,oCAAA,EACF,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,UAAS,EAOvEmP,GAAuB3Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,eAAA,UAAA,0JAAA,MAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeiC,KAAM,QACjC,CAAC,CAAEjC,MAAAA,EAAOiQ,WAAAA,CAAW,IACjCA,OAAAA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,UAAY,eAC3C,CAAC,CAAEhB,MAAAA,EAAOiQ,WAAAA,CAAW,IAC5BA,OAAAA,OAAAA,EAAa,YAAYjQ,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,WASxC,CAAC,CAAE7B,MAAAA,EAAOiQ,WAAAA,CAAW,aACjCA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,YAAYhB,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,UAAS,EAInFoP,GAAmB5Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACZ,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,aAAc,UAAS,EAG9D+O,GAAmB7Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,IAAA,UAAA,6BAAA,mDAAA,uBAAA,YAAA,IAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeE,KAAM,OAC3C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,QAC/B,CAAC,CAAEvQ,MAAAA,EAAOiQ,WAAAA,CAAW,aAC5BA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,YAAYhB,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,gBAAiB,WAEtE,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YAG/C,CAAC,CAAEF,MAAAA,EAAOiQ,WAAAA,CAAW,IAAOA,OAAAA,OAAAA,IAAajQ,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,UAAY,eAGlE,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,WAC5C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,UAAS,EAI5D2O,GAAiC/Q,EAAAA,IAAGC,WAAA,CAAAC,YAAA,0BAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,8CAAA,cAAA,UAAA,mBAAA,EAGjC,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,OAC9B,CAAC,CAAE1B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiB0B,KAAM,WAC1C,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,UAAW,UAAS,EAItD0P,GAAsBhR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,SAAA,cAAA,eAAA,GAAA,EACpB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcc,QAAS,WAClC,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YACrC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAM5CgP,GAA0EA,CAAC,CACtFpO,MAAAA,EACAC,SAAAA,EACAoO,iBAAAA,EAAmB,GACnBC,mBAAAA,EAAqB,GACrBpO,YAAAA,EAAc,iCACdnB,SAAAA,EAAW,GACXP,MAAAA,EACA8B,UAAAA,CACF,IAAM,CACJ,KAAM,CAACiN,EAAQgB,CAAS,EAAInN,WAAS,EAAK,EAEpC,CAAE+K,oBAAAA,EAAqBT,eAAAA,EAAgBc,cAAAA,EAAeG,YAAAA,GAAgBzB,GAAoB,CAC9FI,kBAAmBrL,CAAAA,CACpB,EAGKyE,EAAqBiH,EAAAA,QAAQ,IAC1BtG,EAAaqF,wBACnB,CAAE,CAAA,EAGC8D,EAAe7C,EAAAA,QAAQ,IACvB3L,GAAAA,MAAAA,EAAOsI,aACFtI,EAAMsI,aAERpI,EACN,CAACF,EAAOE,CAAW,CAAC,EAGjBuO,EAAuBzG,GAA6B,CACxDwE,EAAcxE,CAAW,EACzBuG,EAAU,EAAK,CAAA,EAIXG,EAAqBzH,GAA+B,CACxD0F,EAAY1F,CAAS,EACrBsH,EAAU,EAAK,CAAA,EAIXI,EAAqB3G,IAClBhI,GAAAA,YAAAA,EAAO6E,WAAYmD,IAAehI,GAAAA,YAAAA,EAAOuI,iBAAkB,UAI9DqG,EAAmB3H,IAChBjH,GAAAA,YAAAA,EAAOoI,eAAgBnB,IAAajH,GAAAA,YAAAA,EAAOuI,iBAAkB,QAIhEsG,EAAoB7G,IACjB0D,GAAAA,YAAAA,EAAgB7G,WAAYmD,EAGrC,cACGmF,GAAU,CAAA,UAAA7M,EAAsB,SAAU,CAAC,CAAC9B,EAC3C,SAAA,CAAA4D,EAAAA,KAACgL,GACC,CAAA,SAAU,CAAC,CAAC5O,EACZ,SAAAO,EACA,QAAS,IAAM,CAACA,GAAYwP,EAAU,CAAChB,CAAM,EAE7C,SAAA,CAAAnL,OAACiL,GACC,CAAA,SAAA,CAAAhL,EAAAA,IAAC,QAAMmM,SAAaA,CAAA,CAAA,EACpBnM,EAAAA,IAACiL,GAAa,CAAA,OAAAC,EAAgB,SAAC,GAAA,CAAA,CAAA,EACjC,EAEAnL,EAAAA,KAACoL,IAAa,OAAAD,EAEXc,SAAAA,CAAAA,GAAoB3J,EAAmBzC,OAAS,GAC9CI,EAAAA,IAAAoL,GAAA,CACE/I,WAAmBtB,IAAK2B,GACtB3C,OAAAsL,GAAA,CAEC,WAAYkB,EAAgB7J,EAAM5E,IAAI,EACtC,QAAgBwB,GAAA,CACdA,EAAEmN,gBAAgB,EAClBJ,EAAkB3J,EAAM5E,IAAI,CAG9B,EAAA,SAAA,CAACkC,EAAAA,IAAA,OAAA,CAAM0C,WAAM3E,IAAK,CAAA,EAClBiC,EAAAA,IAACuL,IAAsB,SAAgB,kBAAA,CAAA,CARlC7I,CAAAA,EAAAA,EAAM5E,IASb,CACD,EACH,EAIDgM,EAAoB/I,IAAI,CAAC,CAAEyB,QAAAA,EAASyH,aAAAA,EAAc5D,OAAAA,CAAAA,WAChDmF,GACC,CAAA,SAAA,CAAAzL,OAAC0L,IACC,WAAYa,EAAkB9J,CAAO,EACrC,QAAgBlD,GAAA,CACdA,EAAEmN,gBAAgB,EAClBL,EAAoB5J,CAAO,CAG7B,EAAA,SAAA,CAAAxC,EAAAA,IAAC,QAAMiK,SAAaA,CAAA,CAAA,EACnBgC,GAAsBO,EAAiBhK,CAAO,GAC7CxC,EAAAA,IAAC6L,IAAwB,SAAO,UAAA,CAAA,EAEpC,EAECG,GAAoB3F,EAAOzG,OAAS,SAClC8L,GACErF,CAAAA,SAAAA,EAAOtF,IAAI,CAAC,CAAEpD,MAAO+O,EAAYtR,MAAAA,CAAAA,WAC/BuQ,GAEC,CAAA,WAAYY,EAAgBG,CAAU,EACtC,QAAgBpN,GAAA,CACdA,EAAEmN,gBAAgB,EAClBJ,EAAkBK,CAAU,CAG7BtR,EAAAA,SAAAA,CAAAA,EAEA4H,EAAasF,cAAcoE,CAAU,GACpC1M,EAAAA,IAAC,QAAK,MAAO,CAAE2M,WAAY,MAAOC,SAAU,UAAWC,QAAS,EAAA,EAAM,SAEtE,qBAAA,CAAA,GAZGH,CAcP,CACD,EACH,CAAA,CAAA,EAlCelK,CAoCnB,CACD,CAAA,EACH,CAAA,EACF,EAECrG,GAAU6D,EAAAA,IAAA8L,GAAA,CAAc3P,SAAMA,CAAA,CAAA,CACjC,CAAA,CAAA,CAEJ,EChUM2Q,GAA0BhS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,YAAA,kBAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QACtB,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GACxC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GACvB,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CmB,GAAuBjS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,mBAAA,4BAAA,GAAA,EAIb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GAC5B,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,MAAM,EAGzD0Q,GAAsBC,EAAAA,GAAElS,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,cAAA,4BAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACxB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,EAAE,EAK1CsB,GAAwBpS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,GAAA,EAExB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGlC4R,GAAwBrS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAEhC,EAAA,CAAA,mBAAA,CAAA,EAEKmS,GAAsBtS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iFAAA,GAAA,EAKpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C4R,GAAoBvS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iFAAA,qBAAA,EAKlB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAO8R,MAAM,EAyBvCC,GAAoDA,CAAC,CACzDxP,KAAAA,EACAyP,MAAAA,EACAC,SAAAA,EACAC,QAAAA,EACAC,UAAAA,EAAY,GACZxR,MAAAA,EAAQ,KACR8B,UAAAA,EACA2P,YAAAA,EAAc,GACdC,iBAAAA,EAAmB,EACrB,IAAM,CACJ,KAAM,CAACC,EAAaC,CAAc,EAAIC,GAAMjP,SAAS8O,CAAgB,EAE/DI,EAAuBA,IAAM,CAC7BL,GACFG,EAAe,CAACD,CAAW,CAC7B,EAGII,EAAeV,GAASzP,EAAKoQ,OAAO,CAAC,EAAEC,cAAgBrQ,EAAKsQ,MAAM,CAAC,EAEnEC,EAAgBA,IAChBnS,EAEA6D,EAAAA,IAACqN,GACC,CAAA,SAAAtN,EAAAA,KAAC,MACC,CAAA,SAAA,CAAAA,OAAC,MAAI,CAAA,SAAA,CAAA,iBAAehC,CAAAA,EAAK,EACzBiC,MAAC,OAAI,MAAO,CAAE4M,SAAU,QAAS2B,UAAW,KAAA,EAAUpS,SAAMA,EAAA,CAAA,CAC9D,CAAA,CACF,CAAA,EAIAwR,SACMP,GAAa,CAAA,SAAA,CAAA,WAASrP,EAAK,KAAG,CAAA,CAAA,EAGnC0P,UACKL,GAAa,CAAA,SAAA,CAAA,MAAIrP,EAAK,iBAAe,CAAA,CAAA,EAMjD,OACGgC,EAAAA,KAAA+M,GAAA,CAAiB,UAAA7O,EAAsB,eAAcF,EACpD,SAAA,CAAAgC,OAACgN,GACC,CAAA,SAAA,CAAChN,EAAAA,KAAAiN,GAAA,CACC,QAASiB,EACT,MAAO,CAAEO,OAAQZ,EAAc,UAAY,SAE1CM,EAAAA,SAAAA,CAAAA,EACAN,GACE5N,EAAA,IAAA,OAAA,CAAK,MAAO,CAAE2M,WAAY,MAAOC,SAAU,OAAQ,EACjDkB,SAAcA,EAAA,IAAM,GACvB,CAAA,CAAA,EAEJ,EACCJ,GAAY1N,EAAAA,IAAAkN,GAAA,CAAgBQ,SAAQA,CAAA,CAAA,CAAA,EACvC,EAEC,CAACI,GACC9N,EAAAA,IAAAmN,GAAA,CACEmB,aACH,CAEJ,CAAA,CAAA,CAEJ,EAEAG,GAAelB,GCvITmB,GAA0B5T,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAMlC,EAAA,CAAA,gGAAA,CAAA,EAEK+R,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAO7B,EAAA,CAAA,uHAAA,CAAA,EAEK2T,GAAoB9T,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAK5B,EAAA,CAAA,oGAAA,CAAA,EAEK4T,EAAwB/T,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAKhC,EAAA,CAAA,6EAAA,CAAA,EAEK6T,EAAsBC,EAAAA,GAAEhU,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAO7B,EAAA,CAAA,kHAAA,CAAA,EAEK0F,EAAgBC,EAAAA,OAAM7F,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAmB3B,EAAA,CAAA,wPAAA,CAAA,EAEK+T,GAA0BlU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAMlC,EAAA,CAAA,6FAAA,CAAA,EAEKgU,GAAqBnU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAM7B,EAAA,CAAA,sHAAA,CAAA,EAEKiU,GAA2BC,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAGpC,EAAA,CAAA,gCAAA,CAAA,EAEKmU,GAA2BD,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAIpC,EAAA,CAAA,iDAAA,CAAA,EAOKoU,GAA4CA,CAAC,CAAEC,cAAAA,EAAeC,kBAAAA,CAAkB,IAAM,CAC1F,KAAM,CAACC,EAAYC,CAAa,EAAI1Q,WAA0B,CAC5D3E,UAAUmV,GAAAA,YAAAA,EAAmBnV,WAAY,GACzCG,QAAQgV,GAAAA,YAAAA,EAAmBhV,SAAU,OACrCE,UAAU8U,GAAAA,YAAAA,EAAmB9U,WAAY,OACzCE,OAAO4U,GAAAA,YAAAA,EAAmB5U,QAAS,EAAA,CACpC,EAGDuP,EAAAA,UAAU,IAAM,CACVsF,EAAWpV,UAAYoV,EAAW7U,OACpC2U,EAAcE,CAAU,CAC1B,EACC,CAACA,EAAYF,CAAa,CAAC,EAExBI,MAAAA,EAAwBA,CAACC,EAAoChS,IAAkB,CACnF8R,EAAyBG,IAAA,CACvB,GAAGA,EACH,CAACD,CAAW,EAAGhS,CACf,EAAA,CAAA,EAGEkS,EAAkBA,IAAc,CAC9B,KAAA,CAAEzV,SAAAA,EAAUG,OAAAA,EAAQE,SAAAA,EAAUE,MAAAA,CAAU6U,EAAAA,EAE1C,GAAA,CAACpV,GAAY,CAACO,EACT,MAAA,mDAGT,IAAImV,EAAU1V,EAEVG,OAAAA,GAAUA,IAAW,SACvBuV,GAAW,MAAMvV,KAGfE,GAAYA,IAAa,SAC3BqV,GAAW,MAAMrV,KAGnBqV,GAAW,KAAKnV,KAETmV,CAAAA,EAGT,cACGpB,GACC,CAAA,SAAA,CAAA1O,EAAAA,IAACgN,IAAa,SAAyB,2BAAA,CAAA,SAEtC4B,GAEC,CAAA,SAAA,CAAA7O,OAAC8O,EACC,CAAA,SAAA,CAAA9O,OAAC+O,EAAY,CAAA,SAAA,CAAA,mBAEX9O,EAAAA,IAACkP,IAAkB,SAAC,GAAA,CAAA,CAAA,EACtB,EACCnP,EAAA,KAAAY,EAAA,CACC,MAAO6O,EAAWpV,SAClB,SAAiBsV,GAAAA,EAAsB,WAAYpQ,EAAEW,OAAOtC,KAAK,EAEjE,SAAA,CAACqC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAe,kBAAA,EAC/B7F,EAAeC,SAASC,aAAa0G,IAAKC,GACxChB,EAAA,IAAA,SAAA,CAAoB,MAAOgB,EACzBA,SADUA,CAAAA,EAAAA,CAEb,CACD,EACA7G,EAAeC,SAASE,SAASyG,IAAKC,GACpChB,EAAA,IAAA,SAAA,CAAoB,MAAOgB,EACzBA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAGC6N,EACC,CAAA,SAAA,CAAA9O,OAAC+O,EAAY,CAAA,SAAA,CAAA,iBAEX9O,EAAAA,IAACoP,IAAkB,SAAU,YAAA,CAAA,CAAA,EAC/B,EACCrP,EAAA,KAAAY,EAAA,CACC,MAAO6O,EAAWjV,OAClB,SAAiBmV,GAAAA,EAAsB,SAAUpQ,EAAEW,OAAOtC,KAAK,EAE/D,SAAA,CAACqC,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAI,OAAA,EACxB7F,EAAeI,OAAOC,gBAAgBuG,IAAKC,GACzChB,EAAA,IAAA,SAAA,CAAoB,MAAOgB,EACzBA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAGC6N,EACC,CAAA,SAAA,CAAA9O,OAAC+O,EAAY,CAAA,SAAA,CAAA,mBAEX9O,EAAAA,IAACoP,IAAkB,SAAU,YAAA,CAAA,CAAA,EAC/B,EACCrP,EAAA,KAAAY,EAAA,CACC,MAAO6O,EAAW/U,SAClB,SAAiBiV,GAAAA,EAAsB,WAAYpQ,EAAEW,OAAOtC,KAAK,EAEjE,SAAA,CAACqC,EAAA,IAAA,SAAA,CAAO,MAAM,OAAO,SAAI,OAAA,EACxB7F,EAAeM,SAASC,QAAQqG,IAAKC,GACnChB,EAAA,IAAA,SAAA,CAAoB,MAAOgB,EACzBA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,SAGC6N,EACC,CAAA,SAAA,CAAA9O,OAAC+O,EAAY,CAAA,SAAA,CAAA,eAEX9O,EAAAA,IAACkP,IAAkB,SAAC,GAAA,CAAA,CAAA,EACtB,EACCnP,EAAA,KAAAY,EAAA,CACC,MAAO6O,EAAW7U,MAClB,SAAiB+U,GAAAA,EAAsB,QAASpQ,EAAEW,OAAOtC,KAAK,EAE9D,SAAA,CAACqC,EAAA,IAAA,SAAA,CAAO,MAAM,GAAG,SAAmB,sBAAA,EACnC7F,EAAeQ,MAAMC,QAAQmG,IAAKC,GAChChB,EAAA,IAAA,SAAA,CAAoB,MAAOgB,EACzBA,SADUA,CAAAA,EAAAA,CAEb,CACD,CAAA,EACH,CAAA,EACF,CAAA,EACF,SAGCgO,GACC,CAAA,SAAA,CAAAhP,EAAAA,IAAC8O,GAAa,SAAa,eAAA,CAAA,EAC3B9O,EAAAA,IAACiP,GAAaY,CAAAA,SAAAA,EAAkB,CAAA,CAAA,CAAA,EAClC,CACF,CAAA,CAAA,CAEJ,EAEAE,GAAeV,GC/OTW,GAAoBlV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+EAAA,GAAA,EAIV,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CqE,GAAeC,EAAAA,GAAEnV,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACR,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAU6U,IAEnC,CAAC,CAAE9U,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAY5CkT,GAAkDA,CAAC,CAAEC,WAAAA,EAAYC,WAAAA,CAAW,IAE9EtQ,EAAA,IAACgQ,GACC,CAAA,SAAAhQ,EAAA,IAACiQ,GACEI,CAAAA,SAAAA,EAAa,gBAAkB,eAAeC,EAAWC,WAAWD,EAAWE,QAClF,CACF,CAAA,EChBE1F,GAAmBhQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,UAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeoV,KAAM,QAC7B,CAAC,CAAEpV,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,aAAc,qBAChD,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,UAAS,EAG1DwT,GAAuB5V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,kEAAA,iDAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckB,UAAW,uBAClC,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,yBAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBuQ,KAAM,OAKxC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,uBAAsB,EAK5EsU,GAA6B7V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,eAAA,2GAAA,IAAA,EACzB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,QACpB,CAAC,CAAEvQ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,yBACpD,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,aAAc,qBAWzC,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,uBAAsB,EAI1EuU,GAA+B9V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,wBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAG/B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeiC,KAAM,OAAM,EAG7CuT,GAA2B/V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2GAAA,oBAAA,qBAAA,KAAA,EAOpB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,wBACrC,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBiC,KAAM,OACtC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,uBAAsB,EAG9EyU,GAA4BnC,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,4DAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBuQ,KAAM,YAE1C,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,UAAS,EAM1D6T,GAAkCC,EAAAA,EAACjW,WAAA,CAAAC,YAAA,2BAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,yBAAA,EAC1B,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,gBAAiB,yBAC7C,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAI/CkU,GAA8BnW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,uBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EAC1B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,OAAM,EAGjDsF,GAAiBpW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,kBAAA,GAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,QAC1B,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,OAAM,EAGvDuF,EAAmBrW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeE,KAAM,MAAK,EAG5CJ,EAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,kBAAA,mDAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,WACpC,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAKtDqU,EAAkBjC,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,WAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,mBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiB0B,KAAM,WAC1C,CAAC,CAAE1B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,gBAAiB,yBACzC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAInDsU,EAAyBlC,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,kBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,SAAA,cAAA,eAAA,mBAAA,EACxB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcc,QAAS,sBAClC,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YACrC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAenDuU,GAA8DA,CAAC,CACnEhB,WAAAA,EACAiB,aAAAA,EACAC,iBAAAA,CACF,IAEIxR,EAAAA,IAAC8K,GAEC,CAAA,SAAA/K,EAAAA,KAAC2Q,GACC,CAAA,SAAA,CAAC1Q,EAAA,IAAA2Q,GAAA,CACC,gBAACC,GACC,CAAA,SAAA,CAAA5Q,EAAAA,IAAC6Q,IAAkB,SAAE,IAAA,CAAA,SACpB,MACC,CAAA,SAAA,CAAA7Q,EAAAA,IAAC8Q,IAAmB,SAAwB,0BAAA,CAAA,EAC5C9Q,EAAAA,IAAC+Q,IAAwB,SAEzB,qEAAA,CAAA,CAAA,EACF,CAAA,CAAA,CACF,CACF,CAAA,SAECE,GAEC,CAAA,SAAA,CAAAlR,OAACmR,GACC,CAAA,SAAA,CAAAnR,OAACoR,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,YAAY,SAAU,aAAA,EACpC6E,EAAA,IAAAtC,GAAA,CACC,GAAG,YACH,KAAK,YACL,KAAK,OACL,MAAO4S,EAAWE,MAAQ,GAC1B,SAAW7S,GAAkB,CAI3B4T,EAHc,CACZtR,OAAQ,CAAElC,KAAM,YAAaJ,MAAAA,CAAM,CAAA,CAEnB,CAAA,EAClB,EAEH6T,EAAiBC,WACfzR,MAAAqR,EAAA,CAAiBG,WAAiBC,UAAU,EAE/CzR,EAAAA,IAACoR,GAAS,SAA+B,iCAAA,CAAA,CAAA,EAC3C,SAECD,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,YAAY,SAAU,aAAA,EACrC6E,EAAAA,IAACK,GACC,CAAA,GAAG,YACH,KAAK,YACL,MAAOiQ,EAAWoB,WAAa,GAC/B,SAAUH,CAAa,CAAA,EAExBC,EAAiBE,WACf1R,MAAAqR,EAAA,CAAiBG,WAAiBE,UAAU,EAE/C1R,EAAAA,IAACoR,GAAS,SAAyB,2BAAA,CAAA,CAAA,EACrC,SAECD,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,SAAS,SAAM,SAAA,EAC7B6E,EAAA,IAAAa,GAAA,CACC,GAAG,SACH,KAAK,SACL,MAAOyP,EAAWqB,QAAU,SAC5B,SAAUJ,EACV,QAASK,GAAe,EAE1B5R,EAAAA,IAACoR,GAAS,SAAwB,0BAAA,CAAA,CAAA,EACpC,CAAA,EACF,SAGCF,GACC,CAAA,SAAA,CAAAnR,OAACoR,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,SAAS,SAAiB,oBAAA,EACzC6E,EAAAA,IAACK,GACC,CAAA,GAAG,SACH,KAAK,SACL,MAAOiQ,EAAWuB,QAAU,GAC5B,SAAUN,CAAa,CAAA,EAExBC,EAAiBK,QACf7R,MAAAqR,EAAA,CAAiBG,WAAiBK,OAAO,EAE5C7R,EAAAA,IAACoR,GAAS,SAAqD,uDAAA,CAAA,CAAA,EACjE,SAECD,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,WAAW,SAAS,YAAA,EACnC6E,EAAAA,IAACK,GACC,CAAA,GAAG,WACH,KAAK,WACL,MAAOiQ,EAAWwB,UAAY,GAC9B,SAAUP,CAAa,CAAA,EAExBC,EAAiBM,UACf9R,MAAAqR,EAAA,CAAiBG,WAAiBM,SAAS,EAE9C9R,EAAAA,IAACoR,GAAS,SAA0B,4BAAA,CAAA,CAAA,EACtC,CAAA,EACF,EAGApR,EAAA,IAACkR,GACC,CAAA,SAAAnR,EAAAA,KAACoR,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,UAAU,SAAe,kBAAA,EACvC6E,EAAAA,IAAA+L,GAAA,CACC,MACGuE,EAAW9N,SAA2C,CAAEA,QAAS,GAAIE,MAAO,EAAA,EAE/E,SAAWyG,GAAgC,CAKzC,MAAM4I,EAAQ,CACZ9R,OAAQ,CAAElC,KAAM,UAAWJ,MAJ3B,OAAOwL,GAAc,SACjBA,EACA,GAAIA,EAAkB3G,WAAY2G,EAAkBzG,OAEvB,CAAA,EAEnC6O,EAAaQ,CAAK,CAAA,EAClB,EAEJ/R,EAAAA,IAACoR,GAAS,SAAgC,kCAAA,CAAA,CAAA,CAAA,CAC5C,CACF,CAAA,CAAA,EACF,CAAA,CACF,CAAA,CACF,CAAA,ECrQEF,GAAiBpW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC6T,EAAmBrW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC5B,EAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9CiC,EAAeT,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,qBAAA,kBAAA,UAAA,yBAAA,iBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC5B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAGnB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAKjD+U,GAAkBjC,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0BAAA,GAAA,EAEjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAc9CuW,GAA0DA,CAAC,CAC/D1B,WAAAA,EACAiB,aAAAA,CAEF,IAGMxR,EAAA,KAAAkS,WAAA,CAAA,SAAA,CAAAlS,OAACmR,GACC,CAAA,SAAA,CAAAnR,OAACoR,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,WAAW,SAAS,YAAA,EAClC6E,EAAA,IAAAtC,EAAA,CACC,GAAG,WACH,KAAK,WACL,KAAK,SACL,KAAK,OACL,MAAO4S,EAAW4B,UAAY,GAC9B,SAAUX,EAAa,CAAA,EAE3B,SAECJ,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,aAAa,SAAW,cAAA,EACtC6E,EAAA,IAAAtC,EAAA,CACC,GAAG,aACH,KAAK,aACL,KAAK,SACL,KAAK,OACL,MAAO4S,EAAW6B,YAAc,GAChC,SAAUZ,EAAa,CAAA,EAE3B,CAAA,EACF,SAECL,GACC,CAAA,SAAA,CAAAnR,OAACoR,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,aAAa,SAAa,gBAAA,EACxC6E,EAAA,IAAAtC,EAAA,CACC,GAAG,aACH,KAAK,aACL,KAAK,SACL,KAAK,OACL,MAAO4S,EAAW8B,YAAc,GAChC,SAAUb,EAAa,CAAA,EAE3B,SAECJ,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,YAAY,SAAU,aAAA,QACpCuC,EACC,CAAA,GAAG,YACH,KAAK,YACL,KAAK,SACL,KAAK,OACL,MAAO4S,EAAW+B,WAAa,GAC/B,SAAUd,EACV,SAAQ,GAAA,EAEVvR,EAAAA,IAACoR,IAAS,SAAwC,0CAAA,CAAA,CAAA,EACpD,CAAA,EACF,CACF,CAAA,CAAA,EClGEF,GAAiBpW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,GAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlC6T,GAAmBrW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC5B,GAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C6W,GAAkBC,EAAAA,SAAQxX,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,qBAAA,kBAAA,UAAA,0CAAA,iBAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAC5B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAInB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAKjD2Q,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,WAAA,MAAA,KAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAC3B,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GAAQ,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG1EiX,GAAiBC,EAAAA,GAAE1X,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oCAAA,WAAA,KAAA,EAEC,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAerCoV,GAAkEA,CAAC,CACvEpC,WAAAA,EACAiB,aAAAA,EAEAoB,cAAAA,CACF,IAIM5S,EAAA,KAAAkS,WAAA,CAAA,SAAA,CAAAjS,EAAAA,IAACgN,IAAa,SAAa,eAAA,CAAA,EAC3BhN,EAAAA,IAACkR,IACC,SAAClR,EAAAA,IAAAmR,GAAA,CACC,eAACtQ,GACC,CAAA,GAAG,YACH,KAAK,YACL,MAAM,gBACN,MAAOyP,EAAWsC,WAAa,GAC/B,SAAUrB,EACV,QAASsB,GACT,YAAY,sBAAsB,CAAA,CAAA,CAEtC,CACF,CAAA,QAECL,GAAO,EAAA,EAGPG,GAEG5S,EAAA,KAAAkS,WAAA,CAAA,SAAA,CAAAjS,EAAAA,IAACgN,IAAa,SAAkB,oBAAA,CAAA,EAC/BhN,EAAAA,IAAAqP,GAAA,CACC,cAAgBG,GAAgC,CAC9CmD,EAAuB/C,IAAA,CACrB,GAAGA,EACHkD,gBAAiBtD,CACjB,EAAA,CAAA,EAEJ,kBAAmBc,EAAWwC,gBAAgB,QAE/CN,GAAO,EAAA,CAAA,EACV,EAIFxS,EAAAA,IAACgN,IAAa,SAAc,gBAAA,CAAA,SAC3BmE,GACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,GAAA,CAAM,QAAQ,QAAQ,SAA8B,iCAAA,EACpD6E,EAAA,IAAAsS,GAAA,CACC,GAAG,QACH,KAAK,QACL,MAAOhC,EAAWyC,OAAS,GAC3B,SAAUxB,EACV,YAAY,oFAAmF,CAAA,EAEnG,CACF,CAAA,CAAA,EC1HEyB,GAAqBlY,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6CAAA,eAAA,GAAA,EAGrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACtB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGzCqH,GAAgBzV,EAAAA,OAAMzC,WAAA,CAAAC,YAAA,SAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,kBAAA,+DAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GAAM,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GAC1D,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GAGpB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,IAAI,EAGhEyW,GAAeC,EAAOF,EAAM,EAAClY,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iDAAA,UAAA,6BAAA,IAAA,EAEb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OACvC,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAGf,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxDwW,GAAeD,EAAOF,EAAM,EAAClY,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oBAAA,qDAAA,IAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,QAK1B,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMG,OAAO6X,WAAW,EAazDC,GAAoDA,CAAC,CACzDC,aAAAA,EACA5F,UAAAA,EACA0C,WAAAA,CACF,IAAM,CACJ,MAAMmD,EAAWC,KAEjB,cACGT,GACC,CAAA,SAAA,CAAAhT,EAAAA,IAACkT,GACC,CAAA,KAAK,SACL,QAAS,IAAM,CACbQ,QAAQC,IAAI,8CAA8C,EAC1DH,EAAS,UAAU,CAAA,EACnB,SAGJ,SAAA,EACCxT,EAAA,IAAAoT,GAAA,CACC,KAAK,SACL,SAAUG,GAAgB5F,EAC1B,cAAa0C,EAAa,mBAAqB,sBAE9CkD,SAAAA,EAAe,YAAclD,EAAa,YAAc,eAC3D,CACF,CAAA,CAAA,CAEJ,ECxEMvE,GAAsBhR,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,SAAA,cAAA,eAAA,YAAA,qBAAA,kBAAA,GAAA,EACpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOW,MACxB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAC9B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAChC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOoY,YAAc,yBAC7C,CAAC,CAAEvY,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,EAAE,EAGjDsY,GAAwB/Y,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,SAAA,cAAA,eAAA,YAAA,qBAAA,kBAAA,GAAA,EACtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOY,QACxB,CAAC,CAAEf,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAC9B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAChC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOsY,cAAgB,yBAC/C,CAAC,CAAEzY,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,EAAE,EAGjDwY,GAAiBjZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,SAAA,cAAA,eAAA,kBAAA,qBAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cACxB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GAC9B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GAC1B,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAa5C0W,GAAsDA,CAAC,CAC3D7X,MAAAA,EACAC,QAAAA,EACA6X,YAAAA,EAAc,EAChB,IAGO9X,EAAAA,KAAAA,WAAAA,CAAAA,SAAAA,CAASA,GAAA6D,EAAAA,IAAC8L,IAAc3P,SAAMA,CAAA,CAAA,EAC9BC,GAAY4D,EAAAA,IAAA6T,GAAA,CAAgBzX,SAAQA,CAAA,CAAA,EACpC6X,GAAgBjU,EAAAA,IAAA+T,GAAA,CAAQ,SAA+B,iCAAA,CAAA,CAC1D,CAAA,CAAA,EC7CEG,GAAwBpZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gMAAA,GAAA,EAYd,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,EAAE,EAGjD6W,GAAwBrZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2EAAA,2EAAA,8EAAA,EAGP,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,QAInC,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAY5C8W,GAAqBtZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,gBAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACtB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMO,YAAYC,MAAM,EAUlDwY,GAAoDA,CAAC,CAAE1G,UAAAA,CAAU,IAChEA,SAGFuG,GACC,CAAA,SAAA,CAAAlU,EAAA,IAACmU,GAAc,EAAA,EACfnU,EAAAA,IAACoU,IAAY,SAAqB,uBAAA,CAAA,CACpC,CAAA,CAAA,EANqB,KCzCnBE,GAA2BxZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGlC2I,GAAsBzZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACZ,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5C4I,GAAoB7F,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,GAEnC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACvB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCkZ,GAAmBzD,EAAAA,EAACjW,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAK9C+W,EAAiBC,EAAAA,GAAE1X,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oCAAA,WAAA,KAAA,EAEC,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGrC+T,GAAyBvW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,SAAA,cAAA,YAAA,qBAAA,kBAAA,kBAAA,GAAA,EACvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOW,MACxB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACjC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOoY,WAC/B,CAAC,CAAEvY,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GAClC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAsB5CoX,GAA0CA,CAAC,CAAEpE,WAAAA,EAAY1S,SAAAA,EAAU4T,iBAAAA,CAAiB,WAErF8C,GACC,CAAA,SAAA,CAAAvU,OAACwU,GACC,CAAA,SAAA,CAAAvU,EAAAA,IAACwU,IAAW,SAAgC,kCAAA,CAAA,EAC5CxU,EAAAA,IAACyU,IAAS,SAIV,oMAAA,CAAA,CAAA,EACF,EAGCjD,EAAiBmD,aACf3U,MAAAqR,GAAA,CAAiBG,WAAiBmD,YAAY,QAGhDnC,EAAO,EAAA,QAGPoC,GAAgB,CAAA,MAAOtE,EAAWuE,SAAW,GAAI,SAAAjX,EAAmB,QAEpE4U,EAAO,EAAA,QAGPsC,GAAoB,CAAA,MAAOxE,EAAWyE,aAAe,GAAI,SAAAnX,EAAmB,QAE5E4U,EAAO,EAAA,QAGPwC,GAAoB,CAAA,MAAO1E,EAAW2E,aAAe,GAAI,SAAArX,EAAmB,QAE5E4U,EAAO,EAAA,QAGP0C,GAAmB,CAAA,MAAO5E,EAAW6E,YAAc,CAAA,EAAI,SAAAvX,EAAmB,QAE1E4U,EAAO,EAAA,EAGRxS,EAAAA,IAACoV,GAAoB,CAAA,WAAA9E,EAAwB,SAAA1S,CAAmB,CAAA,QAE/D4U,EAAO,EAAA,EAGRxS,EAAAA,IAACqV,GACC,CAAA,MAAO/E,EAAWgF,kBAAoB,IACtC,MAAOhF,EAAWiF,UAAY,GAC9B,SAAA3X,CAAmB,CAAA,CAEvB,CAAA,CAAA,ECnHEmN,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,GAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrC0Y,GAAoB3a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACtB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCma,GAAqB5a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+CAAA,kBAAA,gCAAA,6BAAA,IAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACvB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAG1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxD+Y,GAAoB1Y,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gBAAA,kBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAI3Cqa,GAA6B9a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAGrC,EAAA,CAAA,qCAAA,CAAA,EAEK4a,GAAoBza,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C4Y,GAA0B3G,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAS9CmZ,GAAkDA,CAAC,CAAEjX,MAAAA,EAAOC,SAAAA,CAAS,IAAM,CAEzEmY,MAAAA,EAAuBzW,GAA2C,CAC7D1B,EAAA,UAAW0B,EAAEW,OAAOtC,KAAK,CAAA,EAGpC,cACGoN,GACC,CAAA,SAAA,CAAA/K,EAAAA,IAACgN,IAAa,SAAQ,UAAA,CAAA,EACtBhN,EAAAA,IAACwV,IAAW,SAEZ,uEAAA,CAAA,EAECxV,EAAA,IAAAyV,GAAA,CACEO,SAAiBjV,GAAAA,IAAgBC,GAAA,CAChC,KAAM,CAAC5F,EAAOkG,CAAW,EAAIN,EAAO5F,MAAMqI,MAAM,KAAK,EAErD,cACGiS,GACC,CAAA,SAAA,CAAA1V,MAAC2V,IACC,KAAK,QACL,GAAI,WAAW3U,EAAOrD,QACtB,KAAK,UACL,MAAOqD,EAAOrD,MACd,QAASA,IAAUqD,EAAOrD,MAC1B,SAAUoY,EAAoB,SAE/BH,GACC,CAAA,SAAA,CAAA5V,EAAAA,IAAC6V,GAAW,CAAA,QAAS,WAAW7U,EAAOrD,QACpCvC,SACHA,EAAA,EACA4E,EAAAA,IAAC8V,IACExU,SACHA,CAAA,CAAA,CAAA,EACF,CAAA,GAhBgBN,EAAOrD,KAiBzB,CAEH,CAAA,EACH,CACF,CAAA,CAAA,CAEJ,EC3GMoN,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,GAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrC0Y,GAAoB3a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACtB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCma,GAAqB5a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+CAAA,kBAAA,gCAAA,6BAAA,IAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACvB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAG1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxD+Y,GAAoB1Y,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gBAAA,kBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAI3Cqa,GAA6B9a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAGrC,EAAA,CAAA,qCAAA,CAAA,EAEK4a,GAAoBza,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C4Y,GAA0B3G,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAS9CqZ,GAA0DA,CAAC,CAAEnX,MAAAA,EAAOC,SAAAA,CAAS,IAAM,CAEjFqY,MAAAA,EAA2B3W,GAA2C,CACjE1B,EAAA,cAAe0B,EAAEW,OAAOtC,KAAK,CAAA,EAGxC,cACGoN,GACC,CAAA,SAAA,CAAA/K,EAAAA,IAACgN,IAAa,SAAY,cAAA,CAAA,EAC1BhN,EAAAA,IAACwV,IAAW,SAEZ,qDAAA,CAAA,EAECxV,EAAA,IAAAyV,GAAA,CACES,SAAqBnV,GAAAA,IAAgBC,GAAA,CACpC,KAAM,CAAC5F,EAAOkG,CAAW,EAAIN,EAAO5F,MAAMqI,MAAM,KAAK,EAErD,cACGiS,GACC,CAAA,SAAA,CAAA1V,MAAC2V,IACC,KAAK,QACL,GAAI,eAAe3U,EAAOrD,QAC1B,KAAK,cACL,MAAOqD,EAAOrD,MACd,QAASA,IAAUqD,EAAOrD,MAC1B,SAAUsY,EAAwB,SAEnCL,GACC,CAAA,SAAA,CAAA5V,EAAAA,IAAC6V,GAAW,CAAA,QAAS,eAAe7U,EAAOrD,QACxCvC,SACHA,EAAA,EACA4E,EAAAA,IAAC8V,IACExU,SACHA,CAAA,CAAA,CAAA,EACF,CAAA,GAhBgBN,EAAOrD,KAiBzB,CAEH,CAAA,EACH,CACF,CAAA,CAAA,CAEJ,EC3GMoN,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,GAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrC0Y,GAAoB3a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACtB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCma,GAAqB5a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,+CAAA,kBAAA,gCAAA,6BAAA,IAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACvB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAG1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxD+Y,GAAoB1Y,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gBAAA,kBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAI3Cqa,GAA6B9a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAGrC,EAAA,CAAA,qCAAA,CAAA,EAEK4a,GAAoBza,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C4Y,GAA0B3G,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAS9CuZ,GAA0DA,CAAC,CAAErX,MAAAA,EAAOC,SAAAA,CAAS,IAAM,CAEjFuY,MAAAA,EAA2B7W,GAA2C,CACjE1B,EAAA,cAAe0B,EAAEW,OAAOtC,KAAK,CAAA,EAGxC,cACGoN,GACC,CAAA,SAAA,CAAA/K,EAAAA,IAACgN,IAAa,SAAY,cAAA,CAAA,EAC1BhN,EAAAA,IAACwV,IAAW,SAEZ,6DAAA,CAAA,EAECxV,EAAA,IAAAyV,GAAA,CACEW,SAAqBrV,GAAAA,IAAgBC,GAAA,CACpC,KAAM,CAAC5F,EAAOkG,CAAW,EAAIN,EAAO5F,MAAMqI,MAAM,KAAK,EAErD,cACGiS,GACC,CAAA,SAAA,CAAA1V,MAAC2V,IACC,KAAK,QACL,GAAI,eAAe3U,EAAOrD,QAC1B,KAAK,cACL,MAAOqD,EAAOrD,MACd,QAASA,IAAUqD,EAAOrD,MAC1B,SAAUwY,EAAwB,SAEnCP,GACC,CAAA,SAAA,CAAA5V,EAAAA,IAAC6V,GAAW,CAAA,QAAS,eAAe7U,EAAOrD,QACxCvC,SACHA,EAAA,EACA4E,EAAAA,IAAC8V,IACExU,SACHA,CAAA,CAAA,CAAA,EACF,CAAA,GAhBgBN,EAAOrD,KAiBzB,CAEH,CAAA,EACH,CACF,CAAA,CAAA,CAEJ,EC3GMoN,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,GAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrCsZ,GAAuBvb,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACtB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzC+a,GAAwBxb,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2CAAA,kBAAA,gCAAA,6BAAA,IAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACvB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAG1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxD2Z,GAAuBtZ,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gBAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG3Cib,GAAuBpb,EAAAA,MAAKL,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAQ5CgY,GAAwDA,CAAC,CAAEvX,MAAAA,EAAOC,SAAAA,CAAS,IAAM,CAE/E6Y,MAAAA,EAAwBnX,GAA2C,CACjEoX,MAAAA,EAAepX,EAAEW,OAAOtC,MACxBgZ,EAAYrX,EAAEW,OAAO2W,QAEvBC,IAAAA,EAEAF,EAESE,EAAA,CAAC,GAAGlZ,EAAO+Y,CAAY,EAGlCG,EAAWlZ,EAAM4I,OAAeuQ,GAAAA,IAASJ,CAAY,EAGvD9Y,EAAS,aAAciZ,CAAQ,CAAA,EAGjC,cACG9L,GACC,CAAA,SAAA,CAAA/K,EAAAA,IAACgN,IAAa,SAAW,aAAA,CAAA,EACzBhN,EAAAA,IAACwV,IAAW,SAEZ,yEAAA,CAAA,QAECa,GACEU,CAAAA,SAAAA,GAAoBhW,IAAKC,UACvBsV,GACC,CAAA,SAAA,CAAAtW,EAAAA,IAACuW,IACC,KAAK,WACL,GAAI,cAAcvV,EAAOrD,QACzB,KAAK,aACL,MAAOqD,EAAOrD,MACd,QAASA,EAAMgJ,SAAS3F,EAAOrD,KAAK,EACpC,SAAU8Y,CAAqB,CAAA,QAEhCD,GAAc,CAAA,QAAS,cAAcxV,EAAOrD,QAC1CqD,WAAO5F,KACV,CAAA,CAXmB4F,CAAAA,EAAAA,EAAOrD,KAY5B,CACD,EACH,CACF,CAAA,CAAA,CAEJ,EC7FM2W,GAA2BxZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,EAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrCoU,EAAmBrW,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACnB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAG5CnC,EAAeC,EAAAA,MAAKL,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9C6W,GAAkBC,EAAAA,SAAQxX,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,sCAAA,qBAAA,kBAAA,cAAA,UAAA,qBAAA,sDAAA,IAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACjB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAK9B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAgBjD+Y,GAA0DA,CAAC,CAAE9E,WAAAA,EAAY1S,SAAAA,CAAS,IAAM,CAE5F,MAAMoZ,EAA4BA,IAC5B1G,EAAWuE,SAAWoC,GAA8B3G,EAAWuE,OAAqD,EAC/GoC,GAA8B3G,EAAWuE,OAAqD,EAEhG,8DAIHqC,EAA8BA,IAC9B5G,EAAWyE,aAAeoC,GAAgC7G,EAAWyE,WAA2D,EAC3HoC,GAAgC7G,EAAWyE,WAA2D,EAExG,gEAIHqC,EAAwB9X,GAA8C,CAC1E1B,EAAS0B,EAAEW,OAAOlC,KAAMuB,EAAEW,OAAOtC,KAAK,CAAA,EAGxC,cACG2W,GACC,CAAA,SAAA,CAAAtU,EAAAA,IAACgN,IAAa,SAAiB,mBAAA,CAAA,EAC/BhN,EAAAA,IAACwV,GAAW,SAEZ,+DAAA,CAAA,SAECrE,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,iBAAiB,SAAY,eAAA,EAC5C6E,EAAAA,IAACwV,EAAawB,CAAAA,SAAAA,EAA4B,CAAA,CAAA,EACzChX,EAAA,IAAAsS,GAAA,CACC,GAAG,iBACH,KAAK,iBACL,MAAOhC,EAAW+G,gBAAkB,GACpC,SAAUD,EACV,YAAY,+BAA8B,CAAA,EAE9C,SAECjG,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,mBAAmB,SAAc,iBAAA,EAChD6E,EAAAA,IAACwV,EAAa0B,CAAAA,SAAAA,EAA8B,CAAA,CAAA,EAC3ClX,EAAA,IAAAsS,GAAA,CACC,GAAG,mBACH,KAAK,mBACL,MAAOhC,EAAWgH,kBAAoB,GACtC,SAAUF,EACV,YAAY,iCAAgC,CAAA,EAEhD,SAECjG,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,eAAe,SAAwB,2BAAA,EACtD6E,EAAAA,IAACwV,GAAa+B,SAA4BA,EAAA,CAAA,EACzCvX,EAAA,IAAAsS,GAAA,CACC,GAAG,eACH,KAAK,eACL,MAAOhC,EAAWkH,cAAgB,GAClC,SAAUJ,EACV,YAAY,2CAA0C,CAAA,EAE1D,SAECjG,EACC,CAAA,SAAA,CAACnR,EAAA,IAAA7E,EAAA,CAAM,QAAQ,qBAAqB,SAAgB,mBAAA,EACpD6E,EAAAA,IAACwV,GAAaiC,SAAiCA,EAAA,CAAA,EAC9CzX,EAAA,IAAAsS,GAAA,CACC,GAAG,qBACH,KAAK,qBACL,MAAOhC,EAAWoH,oBAAsB,GACxC,SAAUN,EACV,YAAY,mCAAkC,CAAA,EAElD,CACF,CAAA,CAAA,CAEJ,EC7IMO,GAAyB7c,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAGzB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsB2B,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5CsY,GAAqBxE,EAAAA,EAACjW,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrC6a,GAA+B9c,EAAAA,IAAGC,WAAA,CAAAC,YAAA,wBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAG/B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACtB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGzCua,GAAyB/c,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGzB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGlCwa,GAAgB7a,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wEAAA,uJAAA,4FAAA,mBAAA,EAKX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAU1B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,QAQ5B,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAK/C0b,GAAqBjd,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wCAAA,qHAAA,GAAA,EAGnB,CAAC,CAAEuF,MAAAA,CAAM,IAAMA,EAOJ,CAAC,CAAEA,MAAAA,CAAM,IAAMA,CAAK,EAGpCwX,GAA2Bld,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,qBAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACvB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAIzC0c,GAAwBnd,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGzCsM,GAAoB9c,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,gCAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAEpB,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAG5Cob,GAAuB5F,EAAAA,SAAQxX,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,qBAAA,kBAAA,cAAA,UAAA,qBAAA,sDAAA,IAAA,EAGxB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACjB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAK9B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAUjDgZ,GAAgEA,CAAC,CACrE1X,MAAAA,EACAoV,MAAAA,EACAnV,SAAAA,CACF,IAAM,CAEEwa,MAAAA,EAAsB9Y,GAA2C,CAC5D1B,EAAA,mBAAoB0B,EAAEW,OAAOtC,KAAK,CAAA,EAIvC0a,EAAqB/Y,GAA8C,CAC9D1B,EAAA,WAAY0B,EAAEW,OAAOtC,KAAK,CAAA,EAI/B2a,EAAc3a,EAAQ4a,SAAS5a,CAAK,EAAI,EAGxC6a,EAAcC,GAAyBH,CAAW,EAGlDI,EAAoBC,GAA+BL,CAAW,EAEpE,cACGX,GACC,CAAA,SAAA,CAAA3X,EAAAA,IAACgN,IAAa,SAAwB,0BAAA,CAAA,EACtChN,EAAAA,IAACwV,IAAW,SAGZ,8HAAA,CAAA,SAECoC,GACC,CAAA,SAAA,CAAA7X,OAAC8X,GACC,CAAA,SAAA,CAAC7X,EAAAA,IAAA8X,GAAA,CACC,KAAK,QACL,IAAI,IACJ,IAAI,KACJ,MAAOna,GAAS,IAChB,SAAUya,CAAmB,CAAA,EAE9BpY,EAAA,IAAA+X,GAAA,CAAY,MAAOS,EAAcF,SAAYA,EAAA,CAAA,EAChD,EAEAtY,EAAAA,IAACgY,IAAmBU,SAAkBA,CAAA,CAAA,CAAA,EACxC,SAECT,GACC,CAAA,SAAA,CAACjY,EAAA,IAAAkY,GAAA,CAAW,QAAQ,WAAW,SAAgB,mBAAA,EAC/ClY,EAAAA,IAACmY,GACC,CAAA,GAAG,WACH,KAAK,WACL,MAAOpF,EACP,SAAUsF,EACV,YAAY,oDAAoD,CAAA,CAAA,EAEpE,CACF,CAAA,CAAA,CAEJ,ECnLMtN,GAA2BjQ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACnB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CgN,GAAwBjK,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5C2b,GAA8B7H,EAAAA,EAACjW,WAAA,CAAAC,YAAA,uBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,KAAA,EACtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,cAC3B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGrC0Y,GAAoB3a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,GAAA,EAGpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACtB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCma,GAAqB5a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,+CAAA,kBAAA,gCAAA,6BAAA,IAAA,EAGjB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,GACvB,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMmB,YAAYC,KAG1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,UAAU,EAIxD+Y,GAAoB1Y,EAAAA,MAAKlC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gBAAA,kBAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAI3Cqa,GAA6B9a,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAGrC,EAAA,CAAA,qCAAA,CAAA,EAEK4a,GAAoBza,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GAEnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C4Y,GAA0B3G,EAAAA,KAAIpU,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,EACrB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUyB,GACnC,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAW9Cqd,EAAsDA,CAAC,CAC3DC,UAAAA,EACApb,MAAAA,EACAC,SAAAA,EACAob,UAAAA,CACF,IAAM,CACEC,MAAAA,EAAgBC,GAAyBH,CAAS,EAGlDI,EAAqB7Z,GAA2C,CAC3D0Z,EAAAA,EAAW1Z,EAAEW,OAAOtC,KAAK,CAAA,EAGpC,cACGoN,GACC,CAAA,SAAA,CAAC/K,EAAAA,IAAA4Y,GAAA,CAAgBK,WAAczL,KAAM,CAAA,EACrCxN,EAAAA,IAAC6Y,GAAsBI,CAAAA,SAAAA,EAAc3X,WAAY,CAAA,QAEhDmU,GACE2D,CAAAA,SAAAA,GAAoBrY,IAAKC,UACvB0U,GACC,CAAA,SAAA,CAAA1V,EAAAA,IAAC2V,IACC,KAAK,QACL,GAAI,GAAGqD,KAAahY,EAAOrD,QAC3B,KAAMqb,EACN,MAAOhY,EAAOrD,MACd,QAASA,IAAUqD,EAAOrD,MAC1B,SAAUwb,CAAkB,CAAA,SAE7BvD,GACC,CAAA,SAAA,CAAA5V,EAAAA,IAAC6V,IAAW,QAAS,GAAGmD,KAAahY,EAAOrD,QACzCqD,WAAO5F,KACV,CAAA,EACC4E,EAAA,IAAA8V,GAAA,CACEmD,SAAcjY,EAAAA,EAAOrD,KAAmB,EAC3C,CAAA,EACF,CAhBgBqD,CAAAA,EAAAA,EAAOrD,KAiBzB,CACD,EACH,CACF,CAAA,CAAA,CAEJ,ECvGM0b,GAA6Bve,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAG7B,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGlC2I,GAAsBzZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,iBAAA,GAAA,EACZ,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5C4I,GAAoB7F,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,GAEnC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACvB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAGzCkZ,GAAmBzD,EAAAA,EAACjW,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GACnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAK9C+W,GAAiBC,EAAAA,GAAE1X,WAAA,CAAAC,YAAA,UAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,WAAA,KAAA,EAEC,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,EAAE,EAGrCgc,GAAsBxe,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,qBAAA,YAAA,kBAAA,eAAA,GAAA,EAGtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GAChB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WACrC,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GACvB,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GACrC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGzC2N,GAAoB5K,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,YAAA,EACb,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,GAEnC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAI5Csc,GAAsB1e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGtB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ+U,EAAE,EAGlCgJ,GAAoB3e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wCAAA,uHAAA,GAAA,EAGlB,CAAC,CAAEuF,MAAAA,CAAM,IAAMA,EAOJ,CAAC,CAAEA,MAAAA,CAAM,IAAMA,CAAK,EAGpCkZ,GAAmB5e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQqB,EAAE,EAGlC4c,GAA0B7e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,GAAA,EACpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,GAEnC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,WAAW,EAG5C0c,GAAwB9e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,GAAA,EAClB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOC,aAAa,EAG9Coe,GAAsB/e,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGzCsM,GAAoB9c,EAAAA,MAAKL,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,gCAAA,GAAA,EAChB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUgC,GAEnC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YAEpB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,EAAE,EAG5C4c,GAAuB5F,EAAAA,SAAQxX,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,qBAAA,kBAAA,cAAA,UAAA,qBAAA,sDAAA,IAAA,EAGxB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAaR,GACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMC,UAAUC,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACjB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WAK9B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAmBjDyd,GAAoEA,CAAC,CACzExJ,WAAAA,EACA1S,SAAAA,CACF,IAAM,CACJ,KAAM,CAACmc,EAAYC,CAAa,EAAIjb,WAAS,CAAC,EACxC,CAACkb,EAAQC,CAAS,EAAInb,WAAS,CAAC,EAGtCmL,EAAAA,UAAU,IAAM,CACd,MAAMiQ,EAAuC,CAC3CC,QAAU9J,EAAW+J,uBAAyB,GAC9CC,WAAahK,EAAWiK,0BAA4B,GACpDC,QAAUlK,EAAWmK,uBAAyB,GAC9CC,KAAOpK,EAAWqK,oBAAsB,GACxCC,OAAStK,EAAWuK,sBAAwB,GAC5CC,UAAYxK,EAAWyK,yBAA2B,GAClDC,OAAS1K,EAAW2K,sBAAwB,EAAA,EAM9C,GAF0BhZ,OAAOC,OAAOiY,CAAQ,EAAEe,MAAMvd,GAASA,GAASA,IAAUmC,MAAS,EAEtE,CACfqb,MAAAA,EAAQC,GAAoBjB,CAAQ,EACpCkB,EAAmBC,GAAqBH,CAAK,EAEnDnB,EAAcmB,CAAK,EACnBjB,EAAUmB,CAAgB,EAGjBzd,EAAA,iBAAkByd,EAAiBrX,SAAU,CAAA,EACxD,EACC,CACDsM,EAAW+J,sBACX/J,EAAWiK,yBACXjK,EAAWmK,sBACXnK,EAAWqK,mBACXrK,EAAWuK,qBACXvK,EAAWyK,wBACXzK,EAAW2K,qBACXrd,CAAQ,CACT,EAGKya,MAAAA,EAAqB/Y,GAA8C,CAC9D1B,EAAA,sBAAuB0B,EAAEW,OAAOtC,KAAK,CAAA,EAGhD,cACG0b,GACC,CAAA,SAAA,CAAAtZ,OAACwU,GACC,CAAA,SAAA,CAAAvU,EAAAA,IAACwU,IAAW,SAA0B,4BAAA,CAAA,EACtCxU,EAAAA,IAACyU,IAAS,SAIV,0LAAA,CAAA,CAAA,EACF,QAECjC,GAAO,EAAA,EAGRxS,EAAAA,IAAC8Y,EACC,CAAA,UAAU,UACV,MAAQxI,EAAW+J,uBAAyB,GAC5C,SAAAzc,EACA,UAAU,uBAAuB,CAAA,EAGnCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,aACV,MAAQxI,EAAWiK,0BAA4B,GAC/C,SAAA3c,EACA,UAAU,0BAA0B,CAAA,EAGtCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,UACV,MAAQxI,EAAWmK,uBAAyB,GAC5C,SAAA7c,EACA,UAAU,uBAAuB,CAAA,EAGnCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,OACV,MAAQxI,EAAWqK,oBAAsB,GACzC,SAAA/c,EACA,UAAU,oBAAoB,CAAA,EAGhCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,SACV,MAAQxI,EAAWuK,sBAAwB,GAC3C,SAAAjd,EACA,UAAU,sBAAsB,CAAA,EAGlCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,YACV,MAAQxI,EAAWyK,yBAA2B,GAC9C,SAAAnd,EACA,UAAU,yBAAyB,CAAA,EAGrCoC,EAAAA,IAAC8Y,EACC,CAAA,UAAU,SACV,MAAQxI,EAAW2K,sBAAwB,GAC3C,SAAArd,EACA,UAAU,sBAAsB,CAAA,EAIjCqc,EAAS,GACRla,EAAAA,KAACuZ,GACC,CAAA,SAAA,CAAAtZ,EAAAA,IAACuZ,IAAW,SAAqB,uBAAA,CAAA,SAChCC,GACC,CAAA,SAAA,CAAAxZ,MAACyZ,GAAW,CAAA,MAAO8B,GAAetB,CAAM,EAAIA,SAAOA,EAAA,SAClDP,GACC,CAAA,SAAA,CAAC1Z,EAAA,IAAA2Z,GAAA,CAAkB6B,SAAqBvB,GAAAA,CAAM,CAAE,CAAA,SAC/CL,GAAc,CAAA,SAAA,CAAA,gBACCG,EAAW,WAAS9X,OAAOwZ,KAAKvC,EAAwB,EAAEtZ,OAAS,CAAA,EACnF,CAAA,EACF,CAAA,EACF,CAAA,EACF,SAIDia,GACC,CAAA,SAAA,CAAC7Z,EAAA,IAAAkY,GAAA,CAAW,QAAQ,sBAAsB,SAAgB,mBAAA,EACzDlY,EAAA,IAAAmY,GAAA,CACC,GAAG,sBACH,KAAK,sBACL,MAAO7H,EAAWoL,qBAAuB,GACzC,SAAUrD,EACV,YAAY,wDAAuD,CAAA,EAEvE,CACF,CAAA,CAAA,CAEJ,ECvQM/D,GAA2BxZ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,qBAAA,kBAAA,2HAAA,cAAA,EAG3B,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeoV,KAAM,QAC7B,CAAC,CAAEpV,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckB,UAAW,uBAClC,CAAC,CAAElB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,yBAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBuQ,KAAM,OAY1C,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,uBAAsB,EAK1E0Q,GAAuBjS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,eAAA,4BAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,QACjC,CAAC,CAAEvQ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,aAAc,qBAC9B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,wBAAuB,EAGrFqf,GAAwB7gB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGxB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeiC,KAAM,OAAM,EAG7Cse,GAAoB9gB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,2GAAA,oBAAA,qBAAA,KAAA,EAOb,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,wBACrC,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMU,eAANV,YAAAA,EAAoBiC,KAAM,OACtC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgB,UAAW,uBAAsB,EAG9Ewf,GAAqBlN,EAAAA,GAAE5T,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,4DAAA,EACd,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBuQ,KAAM,YAE1C,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,cAAe,UAAS,EAM1D4e,GAA2B9K,EAAAA,EAACjW,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,yBAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,YAAND,YAAAA,EAAiBE,KAAM,YAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,gBAAiB,yBAC7C,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAe0B,KAAM,MAAK,EAI/CoQ,GAAwBrS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EACpB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeuQ,KAAM,OAAM,EAGjDmQ,GAAwBtJ,EAAAA,GAAE1X,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,oCAAA,WAAA,iBAAA,EAEN,CAAC,CAAEI,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAciB,SAAU,yBACrD,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,UAANL,YAAAA,EAAeoV,KAAM,OAAM,EAuBzCuL,GAA4DA,CAAC,CACxE1L,WAAAA,EACA1S,SAAAA,EACA4T,iBAAAA,EAEAvT,UAAAA,CACF,IAEI8B,OAACuU,IAAkB,UAAArW,EACjB,SAAA,CAAC+B,EAAA,IAAA+M,GAAA,CACC,gBAAC4O,GACC,CAAA,SAAA,CAAA3b,EAAAA,IAAC4b,IAAW,SAAE,IAAA,CAAA,SACb,MACC,CAAA,SAAA,CAAA5b,EAAAA,IAAC6b,IAAY,SAAc,gBAAA,CAAA,EAC3B7b,EAAAA,IAAC8b,IAAiB,SAElB,2DAAA,CAAA,CAAA,EACF,CAAA,CAAA,CACF,CACF,CAAA,SAEC3O,GAEC,CAAA,SAAA,CAAAnN,EAAA,IAACuN,GACC,CAAA,KAAK,kBACL,MAAM,6BACN,YAAa,GACb,iBAAkB,GAElB,SAAAvN,MAAC8Z,GAAyB,CAAA,WAAAxJ,EAAwB,SAAA1S,CAAmB,CAAA,EACvE,QAECme,GAAc,EAAA,EAGd/b,MAAAuN,GAAA,CACC,KAAK,eACL,MAAM,eACN,YAAa,GACb,iBAAkB,GAElB,SAACvN,EAAA,IAAA0U,GAAA,CACC,WAAApE,EACA,SAAA1S,EACA,iBAAA4T,CAAmC,CAAA,EAEvC,CAAA,EACF,CACF,CAAA,CAAA,ECrIEyK,GAAuBnhB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,2CAAA,GAAA,EAGvB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GAGzB,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAGtCsQ,GAAwBphB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oBAAA,kBAAA,YAAA,eAAA,qBAAA,EACX,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOe,QAC/B,CAAC,CAAElB,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GACxC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GAC1B,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAM8gB,QAAQ5gB,EAAE,EAIzC6gB,GAAcC,EAAAA,KAAIthB,WAAA,CAAAC,YAAA,OAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMK,QAAQ+U,EAAE,EAGlC3D,GAA0BhS,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,YAAA,kBAAA,GAAA,EACnB,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOoB,WACtB,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC/B,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMU,aAAauB,GACxC,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,GACvB,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMK,QAAQkQ,EAAE,EAG5CoB,GAAsBC,EAAAA,GAAElS,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,qBAAA,4BAAA,GAAA,EACf,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMC,UAAUsQ,GAEnC,CAAC,CAAEvQ,MAAAA,CAAM,IAAMA,EAAMG,OAAO0B,YACvB,CAAC,CAAE7B,MAAAA,CAAM,IAAMA,EAAMK,QAAQ4B,GACzB,CAAC,CAAEjC,MAAAA,CAAM,IAAMA,EAAMK,QAAQH,GACpB,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMG,OAAOa,OAAO,EAG1D0f,GAAwBtJ,EAAAA,GAAE1X,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,oCAAA,WAAA,KAAA,EAEN,CAAC,CAAEI,MAAAA,CAAM,IAAMA,EAAMG,OAAOc,OAC1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,EAAMK,QAAQ+U,EAAE,EASrC6L,GAAsBA,IAAM,CAC1B,KAAA,CAAEte,GAAAA,GAAOue,GAA0B,EAGjC5I,QAAAA,IAAI,mDAAmD3V,IAAK,EACpE0V,QAAQC,IAAI,gBAAgB6I,OAAOC,SAASC,MAAM,EAClDhJ,QAAQC,IAAI,iBAAiB3V,GAAMA,IAAO,OAAO,EACzC2V,QAAAA,IAAI,iBAAiB3V,IAAO,OAAO,EAErC,KAAA,CACJsS,WAAAA,EACAqC,cAAAA,EACApB,aAAAA,EACAoL,aAAAA,EACApJ,aAAAA,EACA5F,UAAAA,EACAxR,MAAAA,EACAC,QAAAA,EACAoV,iBAAAA,EACAnB,WAAAA,EACAuM,oBAAAA,CAAAA,EACEC,GAAa7e,CAAE,EAEnB,cACGie,GACC,CAAA,SAAA,CAACjc,EAAAA,IAAAoQ,GAAA,CAAgB,WAAAC,EAAwB,WAAAC,CAAuB,CAAA,SAE/D4L,GACC,CAAA,SAAA,CAAAlc,MAACqU,IAAiB,UAAA1G,EAAqB,EAEvC5N,EAAAA,KAACqc,GAAK,CAAA,SAAUO,EACd,SAAA,CAAC3c,EAAAA,IAAAgU,GAAA,CAAkB,MAAA7X,EAAc,QAAAC,CAAiB,CAAA,QAGjD0gB,GACC,CAAA,WAAAxM,EACA,aAAAiB,EACA,iBAAAC,EACA,oBAAAoL,EACA,cAAAjK,EAA6B,EAI9B3S,EAAAA,IAAAsR,GAAA,CACC,WAAAhB,EACA,aAAAiB,EACA,iBAAAC,CAAmC,CAAA,SAIpC1E,GACC,CAAA,SAAA,CAAA9M,EAAAA,IAACgN,IAAa,SAAmB,qBAAA,CAAA,EAChChN,EAAA,IAAA0S,GAAA,CACC,WAAApC,EACA,aAAAiB,EACA,iBAAAC,EACA,cAAAmB,EAA6B,CAAA,EAEjC,QAECoJ,GAAc,EAAA,QAGdC,GACC,CAAA,WAAA1L,EACA,SAAU,CAACyM,EAAOpf,IAAU,CAC1BgV,EAAe/C,IAAe,CAC5B,GAAGA,EACH,CAACmN,CAAK,EAAGpf,CACT,EAAA,GAEJ,iBAAA6T,CAAmC,CAAA,SAIpC1E,GACC,CAAA,SAAA,CAAA9M,EAAAA,IAACgN,IAAa,SAAgB,kBAAA,CAAA,EAC7BhN,EAAAA,IAAAgS,GAAA,CACC,WAAA1B,EACA,aAAAiB,EACA,iBAAAC,CAAmC,CAAA,CAAA,EAEvC,QAECuK,GAAc,EAAA,EAGd/b,EAAAA,IAAAsT,GAAA,CACC,aAAAC,EACA,UAAA5F,EACA,WAAA0C,CAAuB,CAAA,CAAA,EAE3B,CAAA,EACF,CACF,CAAA,CAAA,CAEJ"}