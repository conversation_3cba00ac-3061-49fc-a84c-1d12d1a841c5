import{c as t,j as o}from"./client-d6fc67cc.js";import{R as n}from"./react-25c2faed.js";const r=document.getElementById("root");if(!r){console.error("Root element not found, creating a fallback element");const e=document.createElement("div");e.id="root",document.body.appendChild(e)}const c=t.createRoot(document.getElementById("root"));c.render(o.jsx(n.StrictMode,{children:o.jsx("div",{children:"Simple App placeholder - SimpleApp component not found"})}));
//# sourceMappingURL=simple-ffedbc53.js.map
