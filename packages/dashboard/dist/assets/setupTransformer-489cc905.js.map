{"version": 3, "file": "setupTransformer-489cc905.js", "sources": ["../../src/services/transformers/setupTransformer.ts"], "sourcesContent": ["/**\n * Setup Transformer Service\n *\n * Transforms setup components to human-readable descriptions and handles\n * backward compatibility with existing string-based setup descriptions.\n */\n\nimport { SetupComponents } from '@adhd-trading-dashboard/shared';\n\nexport class SetupTransformer {\n  /**\n   * Convert setup components to a human-readable description\n   * @param components The setup components to transform\n   * @returns A formatted setup description string\n   */\n  static componentsToDescription(components: SetupComponents): string {\n    const parts: string[] = [];\n\n    // Add constant element (required)\n    if (components.constant) {\n      parts.push(components.constant);\n    }\n\n    // Add action element with arrow indicator (optional)\n    if (components.action && components.action !== 'None') {\n      parts.push(`→ ${components.action}`);\n    }\n\n    // Add variable element with plus indicator (optional)\n    if (components.variable && components.variable !== 'None') {\n      parts.push(`+ ${components.variable}`);\n    }\n\n    // Add entry method in brackets (required)\n    if (components.entry) {\n      parts.push(`[${components.entry}]`);\n    }\n\n    return parts.join(' ');\n  }\n\n  /**\n   * Parse a setup description string back to components (for backward compatibility)\n   * @param description The setup description string to parse\n   * @returns Parsed setup components or null if parsing fails\n   */\n  static descriptionToComponents(description: string): SetupComponents | null {\n    if (!description || typeof description !== 'string') {\n      return null;\n    }\n\n    try {\n      // Initialize components\n      const components: SetupComponents = {\n        constant: '',\n        action: 'None',\n        variable: 'None',\n        entry: '',\n      };\n\n      // Extract entry method (in brackets)\n      const entryMatch = description.match(/\\[([^\\]]+)\\]/);\n      if (entryMatch) {\n        components.entry = entryMatch[1];\n        description = description.replace(entryMatch[0], '').trim();\n      }\n\n      // Extract variable element (after +)\n      const variableMatch = description.match(/\\+\\s*([^→]+?)(?=\\s*→|$)/);\n      if (variableMatch) {\n        components.variable = variableMatch[1].trim();\n        description = description.replace(variableMatch[0], '').trim();\n      }\n\n      // Extract action element (after →)\n      const actionMatch = description.match(/→\\s*([^+]+?)(?=\\s*\\+|$)/);\n      if (actionMatch) {\n        components.action = actionMatch[1].trim();\n        description = description.replace(actionMatch[0], '').trim();\n      }\n\n      // Remaining text is the constant element\n      if (description) {\n        components.constant = description.trim();\n      }\n\n      // Validate that we have required elements\n      if (components.constant && components.entry) {\n        return components;\n      }\n\n      return null;\n    } catch (error) {\n      console.warn('Failed to parse setup description:', description, error);\n      return null;\n    }\n  }\n\n  /**\n   * Check if a setup description is in the new component format\n   * @param description The setup description to check\n   * @returns True if it appears to be in component format\n   */\n  static isComponentFormat(description: string): boolean {\n    if (!description || typeof description !== 'string') {\n      return false;\n    }\n\n    // Check for component format indicators\n    const hasEntry = /\\[([^\\]]+)\\]/.test(description);\n\n    // Must have entry method, and optionally action/variable indicators\n    return hasEntry;\n  }\n\n  /**\n   * Get a display-friendly version of setup components\n   * @param components The setup components\n   * @returns A formatted display string\n   */\n  static getDisplayString(components: SetupComponents): string {\n    const description = this.componentsToDescription(components);\n\n    if (!description) {\n      return 'No setup configured';\n    }\n\n    return description;\n  }\n\n  /**\n   * Convert setup components to a short display string for tables\n   * @param components The setup components\n   * @returns A short formatted string for table display\n   */\n  static getShortDisplayString(components: SetupComponents): string {\n    if (!components || !components.constant || !components.entry) {\n      return 'No setup';\n    }\n\n    const parts: string[] = [];\n\n    // Add constant (shortened)\n    const constant = components.constant.replace('-FVG', '').replace('Top/Bottom-', '');\n    parts.push(constant);\n\n    // Add entry method (shortened)\n    const entry = components.entry.replace('-Entry', '');\n    parts.push(`[${entry}]`);\n\n    return parts.join(' ');\n  }\n\n  /**\n   * Convert database fields back to SetupComponents\n   * @param setup_constant Database constant field\n   * @param setup_action Database action field\n   * @param setup_variable Database variable field\n   * @param setup_entry Database entry field\n   * @returns SetupComponents object\n   */\n  static fromDatabaseFields(\n    setup_constant?: string,\n    setup_action?: string,\n    setup_variable?: string,\n    setup_entry?: string\n  ): SetupComponents | null {\n    if (!setup_constant || !setup_entry) {\n      return null;\n    }\n\n    return {\n      constant: setup_constant,\n      action: setup_action || 'None',\n      variable: setup_variable || 'None',\n      entry: setup_entry,\n    };\n  }\n\n  /**\n   * Validate setup components\n   * @param components The setup components to validate\n   * @returns Validation result with errors if any\n   */\n  static validateComponents(components: SetupComponents): {\n    isValid: boolean;\n    errors: string[];\n  } {\n    const errors: string[] = [];\n\n    // Check required fields\n    if (!components.constant) {\n      errors.push('Constant element is required');\n    }\n\n    if (!components.entry) {\n      errors.push('Entry method is required');\n    }\n\n    // Validate optional fields are not empty strings\n    if (components.action === '') {\n      components.action = 'None';\n    }\n\n    if (components.variable === '') {\n      components.variable = 'None';\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  /**\n   * Create a default setup components object\n   * @returns Default setup components\n   */\n  static createDefault(): SetupComponents {\n    return {\n      constant: '',\n      action: 'None',\n      variable: 'None',\n      entry: '',\n    };\n  }\n\n  /**\n   * Clone setup components\n   * @param components The components to clone\n   * @returns A deep copy of the components\n   */\n  static clone(components: SetupComponents): SetupComponents {\n    return {\n      constant: components.constant,\n      action: components.action || 'None',\n      variable: components.variable || 'None',\n      entry: components.entry,\n    };\n  }\n}\n"], "names": ["SetupTransformer", "componentsToDescription", "components", "parts", "constant", "push", "action", "variable", "entry", "join", "descriptionToComponents", "description", "entryMatch", "match", "replace", "trim", "variableMatch", "actionMatch", "error", "warn", "isComponentFormat", "test", "getDisplayString", "getShortDisplayString", "fromDatabaseFields", "setup_constant", "setup_action", "setup_variable", "setup_entry", "validateComponents", "errors", "<PERSON><PERSON><PERSON><PERSON>", "length", "createDefault", "clone"], "mappings": "AASO,MAAMA,CAAiB,CAM5B,OAAOC,wBAAwBC,EAAqC,CAClE,MAAMC,EAAkB,CAAA,EAGxB,OAAID,EAAWE,UACPC,EAAAA,KAAKH,EAAWE,QAAQ,EAI5BF,EAAWI,QAAUJ,EAAWI,SAAW,QACvCD,EAAAA,KAAK,KAAKH,EAAWI,QAAQ,EAIjCJ,EAAWK,UAAYL,EAAWK,WAAa,QAC3CF,EAAAA,KAAK,KAAKH,EAAWK,UAAU,EAInCL,EAAWM,OACPH,EAAAA,KAAK,IAAIH,EAAWM,QAAQ,EAG7BL,EAAMM,KAAK,GAAG,CACvB,CAOA,OAAOC,wBAAwBC,EAA6C,CAC1E,GAAI,CAACA,GAAe,OAAOA,GAAgB,SAClC,OAAA,KAGL,GAAA,CAEF,MAAMT,EAA8B,CAClCE,SAAU,GACVE,OAAQ,OACRC,SAAU,OACVC,MAAO,EAAA,EAIHI,EAAaD,EAAYE,MAAM,cAAc,EAC/CD,IACSJ,EAAAA,MAAQI,EAAW,CAAC,EAC/BD,EAAcA,EAAYG,QAAQF,EAAW,CAAC,EAAG,EAAE,EAAEG,QAIjDC,MAAAA,EAAgBL,EAAYE,MAAM,yBAAyB,EAC7DG,IACFd,EAAWK,SAAWS,EAAc,CAAC,EAAED,KAAK,EAC5CJ,EAAcA,EAAYG,QAAQE,EAAc,CAAC,EAAG,EAAE,EAAED,QAIpDE,MAAAA,EAAcN,EAAYE,MAAM,yBAAyB,EAY3DX,OAXAe,IACFf,EAAWI,OAASW,EAAY,CAAC,EAAEF,KAAK,EACxCJ,EAAcA,EAAYG,QAAQG,EAAY,CAAC,EAAG,EAAE,EAAEF,QAIpDJ,IACSP,EAAAA,SAAWO,EAAYI,QAIhCb,EAAWE,UAAYF,EAAWM,MAC7BN,EAGF,WACAgB,GACCC,eAAAA,KAAK,qCAAsCR,EAAaO,CAAK,EAC9D,IACT,CACF,CAOA,OAAOE,kBAAkBT,EAA8B,CACrD,MAAI,CAACA,GAAe,OAAOA,GAAgB,SAClC,GAIQ,eAAeU,KAAKV,CAAW,CAIlD,CAOA,OAAOW,iBAAiBpB,EAAqC,CACrDS,MAAAA,EAAc,KAAKV,wBAAwBC,CAAU,EAE3D,OAAKS,GACI,qBAIX,CAOA,OAAOY,sBAAsBrB,EAAqC,CAChE,GAAI,CAACA,GAAc,CAACA,EAAWE,UAAY,CAACF,EAAWM,MAC9C,MAAA,WAGT,MAAML,EAAkB,CAAA,EAGlBC,EAAWF,EAAWE,SAASU,QAAQ,OAAQ,EAAE,EAAEA,QAAQ,cAAe,EAAE,EAClFX,EAAME,KAAKD,CAAQ,EAGnB,MAAMI,EAAQN,EAAWM,MAAMM,QAAQ,SAAU,EAAE,EAC7CT,OAAAA,EAAAA,KAAK,IAAIG,IAAQ,EAEhBL,EAAMM,KAAK,GAAG,CACvB,CAUA,OAAOe,mBACLC,EACAC,EACAC,EACAC,EACwB,CACpB,MAAA,CAACH,GAAkB,CAACG,EACf,KAGF,CACLxB,SAAUqB,EACVnB,OAAQoB,GAAgB,OACxBnB,SAAUoB,GAAkB,OAC5BnB,MAAOoB,CAAAA,CAEX,CAOA,OAAOC,mBAAmB3B,EAGxB,CACA,MAAM4B,EAAmB,CAAA,EAGrB,OAAC5B,EAAWE,UACd0B,EAAOzB,KAAK,8BAA8B,EAGvCH,EAAWM,OACdsB,EAAOzB,KAAK,0BAA0B,EAIpCH,EAAWI,SAAW,KACxBJ,EAAWI,OAAS,QAGlBJ,EAAWK,WAAa,KAC1BL,EAAWK,SAAW,QAGjB,CACLwB,QAASD,EAAOE,SAAW,EAC3BF,OAAAA,CAAAA,CAEJ,CAMA,OAAOG,eAAiC,CAC/B,MAAA,CACL7B,SAAU,GACVE,OAAQ,OACRC,SAAU,OACVC,MAAO,EAAA,CAEX,CAOA,OAAO0B,MAAMhC,EAA8C,CAClD,MAAA,CACLE,SAAUF,EAAWE,SACrBE,OAAQJ,EAAWI,QAAU,OAC7BC,SAAUL,EAAWK,UAAY,OACjCC,MAAON,EAAWM,KAAAA,CAEtB,CACF"}