{"version": 3, "file": "DailyGuide-ac65bbb9.js", "sources": ["../../../shared/src/utils/timeZoneUtils.ts", "../../../shared/src/components/atoms/DualTimeDisplay.tsx", "../../../shared/src/state/createStoreContext.tsx", "../../../shared/src/state/createSelector.ts", "../../../shared/src/services/persistState.ts", "../../src/features/daily-guide/state/dailyGuideState.ts", "../../src/features/daily-guide/state/dailyGuideSelectors.ts", "../../src/features/daily-guide/hooks/useDailyGuide.ts", "../../src/features/daily-guide/hooks/useSessionAnalytics.ts", "../../src/features/daily-guide/components/F1GuideHeader.tsx", "../../src/features/daily-guide/components/F1GuideTabs.tsx", "../../src/features/daily-guide/components/useGuideNavigation.ts", "../../src/features/daily-guide/hooks/useEnhancedSetupIntelligence.ts", "../../src/features/daily-guide/hooks/useGranularSessionIntelligence.ts", "../../src/features/daily-guide/hooks/useModelSelectionEngine.ts", "../../src/features/daily-guide/hooks/usePatternQualityScoring.ts", "../../src/features/daily-guide/hooks/useSuccessProbabilityCalculator.ts", "../../src/features/daily-guide/components/DetailedAnalysisPanel.tsx", "../../src/features/daily-guide/components/MarketStateIndicator.tsx", "../../src/features/daily-guide/components/QuickDecisionPanel.tsx", "../../src/features/daily-guide/components/EliteIntelligenceLayout.tsx", "../../src/features/daily-guide/hooks/usePDArrayIntelligence.ts", "../../src/features/daily-guide/components/PDArrayLevels.tsx", "../../src/features/daily-guide/hooks/useEnhancedSessionIntelligence.ts", "../../src/features/daily-guide/components/SessionFocus.tsx", "../../src/features/daily-guide/components/guideTabConfig.tsx", "../../src/features/daily-guide/components/F1GuideContainer.tsx", "../../src/features/daily-guide/components/DailyGuide.tsx"], "sourcesContent": ["/**\n * Time Zone Utilities\n *\n * Comprehensive time zone handling for international traders working with NY sessions.\n * Provides dual-timezone display, proper time formatting, and session time conversion.\n */\n\nexport interface DualTimeDisplay {\n  nyTime: string;\n  localTime: string;\n  nyTimezone: string;\n  localTimezone: string;\n  formatted: string;\n}\n\nexport interface TimeInterval {\n  totalMinutes: number;\n  hours: number;\n  minutes: number;\n  formatted: string;\n}\n\nexport interface SessionTimeInfo {\n  nyStart: string;\n  nyEnd: string;\n  localStart: string;\n  localEnd: string;\n  formatted: string;\n}\n\n/**\n * Get user's local timezone\n */\nexport const getUserTimezone = (): string => {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n};\n\n/**\n * Get timezone abbreviation\n */\nexport const getTimezoneAbbreviation = (timezone: string, date: Date = new Date()): string => {\n  const formatter = new Intl.DateTimeFormat('en', {\n    timeZone: timezone,\n    timeZoneName: 'short',\n  });\n\n  const parts = formatter.formatToParts(date);\n  const timeZonePart = parts.find((part) => part.type === 'timeZoneName');\n  return timeZonePart?.value || timezone;\n};\n\n/**\n * Convert NY time to user's local time\n */\nexport const convertNYToLocal = (nyTime: string, userTimezone?: string): string => {\n  const timezone = userTimezone || getUserTimezone();\n\n  // Parse the NY time\n  const [hours, minutes] = nyTime.split(':').map(Number);\n\n  // FIXED: Use the simplest and most reliable approach\n  // Create a date representing the current day\n  const today = new Date();\n\n  // Create a date string in ISO format for today at the specified time\n  // We'll treat this as if it's in NY timezone\n  const year = today.getFullYear();\n  const month = String(today.getMonth() + 1).padStart(2, '0');\n  const day = String(today.getDate()).padStart(2, '0');\n  const timeStr = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;\n\n  // Create the datetime string\n  const isoString = `${year}-${month}-${day}T${timeStr}`;\n\n  // Parse this as a local date first\n  const localDate = new Date(isoString);\n\n  // Now we need to adjust for the fact that we want this to represent NY time\n  // Get current offset between local time and NY time\n  const nowLocal = new Date();\n  const nowInNY = new Date(nowLocal.toLocaleString('en-US', { timeZone: 'America/New_York' }));\n  const nowInLocal = new Date(nowLocal.toLocaleString('en-US', { timeZone: timezone }));\n\n  // Calculate the offset in milliseconds\n  const offsetMs = nowInLocal.getTime() - nowInNY.getTime();\n\n  // Apply this offset to our target time\n  const adjustedDate = new Date(localDate.getTime() + offsetMs);\n\n  // Format in user's timezone\n  return adjustedDate.toLocaleTimeString('en-GB', {\n    timeZone: timezone,\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n};\n\n/**\n * Convert local time to NY time\n */\nexport const convertLocalToNY = (localTime: string, _userTimezone?: string): string => {\n  // Note: userTimezone parameter available but using getUserTimezone() for consistency\n\n  // Create a date object for today with the local time\n  const today = new Date();\n  const localDateTime = new Date(`${today.toDateString()} ${localTime}:00`);\n\n  // Convert to NY timezone\n  return localDateTime.toLocaleTimeString('en-US', {\n    timeZone: 'America/New_York',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n};\n\n/**\n * Get current time in both NY and local timezone\n */\nexport const getCurrentDualTime = (userTimezone?: string): DualTimeDisplay => {\n  const timezone = userTimezone || getUserTimezone();\n  const now = new Date();\n\n  const nyTime = now.toLocaleTimeString('en-US', {\n    timeZone: 'America/New_York',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n\n  const localTime = now.toLocaleTimeString('en-GB', {\n    timeZone: timezone,\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n\n  const nyTimezone = getTimezoneAbbreviation('America/New_York', now);\n  const localTimezone = getTimezoneAbbreviation(timezone, now);\n\n  return {\n    nyTime,\n    localTime,\n    nyTimezone,\n    localTimezone,\n    formatted: `${nyTime} ${nyTimezone} | ${localTime} ${localTimezone}`,\n  };\n};\n\n/**\n * Get current NY time as minutes since midnight (CRITICAL FOR SESSION CALCULATIONS)\n */\nexport const getCurrentNYMinutes = (): number => {\n  const now = new Date();\n  const nyTime = now.toLocaleTimeString('en-US', {\n    timeZone: 'America/New_York',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n\n  return timeToMinutes(nyTime);\n};\n\n/**\n * Get current NY time as Date object\n */\nexport const getCurrentNYTime = (): Date => {\n  const now = new Date();\n  return new Date(now.toLocaleString('en-US', { timeZone: 'America/New_York' }));\n};\n\n/**\n * Format time interval in user-friendly format\n * Always shows hours and minutes for clarity (e.g., \"2h 5m\" instead of \"125m\")\n */\nexport const formatTimeInterval = (totalMinutes: number): TimeInterval => {\n  const hours = Math.floor(totalMinutes / 60);\n  const minutes = totalMinutes % 60;\n\n  let formatted = '';\n  if (hours > 0) {\n    formatted = minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;\n  } else {\n    formatted = `${minutes}m`;\n  }\n\n  return {\n    totalMinutes,\n    hours,\n    minutes,\n    formatted,\n  };\n};\n\n/**\n * Calculate time until a specific NY time\n * Handles cross-day scenarios properly for trading sessions\n */\nexport const getTimeUntilNYTime = (targetNYTime: string): TimeInterval => {\n  const now = new Date();\n\n  // Get current NY time as a proper Date object\n  const nyTimeString = now.toLocaleString('en-US', { timeZone: 'America/New_York' });\n  const nyNow = new Date(nyTimeString);\n\n  // Create target time for today in NY timezone\n  const [hours, minutes] = targetNYTime.split(':').map(Number);\n  const targetTime = new Date(nyNow);\n  targetTime.setHours(hours, minutes, 0, 0);\n\n  // If target time has passed today, set it for tomorrow\n  if (targetTime <= nyNow) {\n    targetTime.setDate(targetTime.getDate() + 1);\n  }\n\n  const diffMs = targetTime.getTime() - nyNow.getTime();\n  const diffMinutes = Math.floor(diffMs / (1000 * 60));\n\n  return formatTimeInterval(diffMinutes);\n};\n\n/**\n * Calculate time remaining in a session window\n */\nexport const getTimeRemainingInWindow = (windowEndNY: string): TimeInterval => {\n  return getTimeUntilNYTime(windowEndNY);\n};\n\n/**\n * Convert session time range to dual timezone\n */\nexport const convertSessionToDualTime = (\n  nyStart: string,\n  nyEnd: string,\n  userTimezone?: string\n): SessionTimeInfo => {\n  const timezone = userTimezone || getUserTimezone();\n\n  const localStart = convertNYToLocal(nyStart, timezone);\n  const localEnd = convertNYToLocal(nyEnd, timezone);\n\n  const localTimezoneAbbr = getTimezoneAbbreviation(timezone);\n\n  return {\n    nyStart,\n    nyEnd,\n    localStart,\n    localEnd,\n    formatted: `${nyStart}-${nyEnd} NY | ${localStart}-${localEnd} ${localTimezoneAbbr}`,\n  };\n};\n\n/**\n * Check if current time is within a NY session window\n */\nexport const isCurrentTimeInNYWindow = (nyStart: string, nyEnd: string): boolean => {\n  const now = new Date();\n  const nyNow = now.toLocaleTimeString('en-US', {\n    timeZone: 'America/New_York',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n\n  const currentMinutes = timeToMinutes(nyNow);\n  const startMinutes = timeToMinutes(nyStart);\n  const endMinutes = timeToMinutes(nyEnd);\n\n  return currentMinutes >= startMinutes && currentMinutes <= endMinutes;\n};\n\n/**\n * Convert time string to minutes since midnight\n */\nexport const timeToMinutes = (timeStr: string): number => {\n  const [hours, minutes] = timeStr.split(':').map(Number);\n  return hours * 60 + minutes;\n};\n\n/**\n * Convert minutes since midnight to time string\n */\nexport const minutesToTime = (minutes: number): string => {\n  const hours = Math.floor(minutes / 60);\n  const mins = minutes % 60;\n  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n};\n\n/**\n * Get next session information\n */\nexport const getNextSessionInfo = (\n  sessions: Array<{ name: string; nyStart: string; nyEnd: string }>\n): {\n  nextSession: string;\n  timeUntil: TimeInterval;\n  sessionTime: SessionTimeInfo;\n} | null => {\n  const now = new Date();\n  const nyNow = now.toLocaleTimeString('en-US', {\n    timeZone: 'America/New_York',\n    hour: '2-digit',\n    minute: '2-digit',\n    hour12: false,\n  });\n\n  const currentMinutes = timeToMinutes(nyNow);\n\n  // Find next session\n  for (const session of sessions) {\n    const sessionStartMinutes = timeToMinutes(session.nyStart);\n\n    if (sessionStartMinutes > currentMinutes) {\n      return {\n        nextSession: session.name,\n        timeUntil: getTimeUntilNYTime(session.nyStart),\n        sessionTime: convertSessionToDualTime(session.nyStart, session.nyEnd),\n      };\n    }\n  }\n\n  // If no session today, return first session tomorrow\n  if (sessions.length > 0) {\n    const firstSession = sessions[0];\n    const timeUntil = getTimeUntilNYTime(firstSession.nyStart);\n\n    return {\n      nextSession: firstSession.name,\n      timeUntil,\n      sessionTime: convertSessionToDualTime(firstSession.nyStart, firstSession.nyEnd),\n    };\n  }\n\n  return null;\n};\n\n/**\n * Format time for mobile display (compact)\n */\nexport const formatTimeForMobile = (dualTime: DualTimeDisplay): string => {\n  const localFlag = dualTime.localTimezone.includes('GMT') ? '🇮🇪' : '🌍';\n  return `${dualTime.localTime} ${localFlag} | ${dualTime.nyTime} 🇺🇸`;\n};\n\n/**\n * Format time for desktop display (full)\n */\nexport const formatTimeForDesktop = (dualTime: DualTimeDisplay): string => {\n  return `${dualTime.localTime} Local (${dualTime.localTimezone}) | ${dualTime.nyTime} NY (${dualTime.nyTimezone})`;\n};\n\n/**\n * Get session status with time context\n */\nexport const getSessionStatus = (\n  _sessionName: string, // Underscore prefix to indicate intentionally unused\n  nyStart: string,\n  nyEnd: string,\n  userTimezone?: string\n): {\n  isActive: boolean;\n  timeRemaining?: TimeInterval;\n  timeUntilStart?: TimeInterval;\n  sessionTime: SessionTimeInfo;\n  status: 'active' | 'upcoming' | 'ended';\n} => {\n  const isActive = isCurrentTimeInNYWindow(nyStart, nyEnd);\n  const sessionTime = convertSessionToDualTime(nyStart, nyEnd, userTimezone);\n\n  if (isActive) {\n    return {\n      isActive: true,\n      timeRemaining: getTimeRemainingInWindow(nyEnd),\n      sessionTime,\n      status: 'active',\n    };\n  }\n\n  const timeUntilStart = getTimeUntilNYTime(nyStart);\n\n  return {\n    isActive: false,\n    timeUntilStart,\n    sessionTime,\n    status: timeUntilStart.totalMinutes < 24 * 60 ? 'upcoming' : 'ended',\n  };\n};\n\n/**\n * Test timezone conversion (for debugging)\n */\nexport const testTimezoneConversion = (): void => {\n  console.log('🕐 TIMEZONE CONVERSION TEST:');\n  console.log('09:00 NY →', convertNYToLocal('09:00', 'Europe/Dublin'));\n  console.log('11:50 NY →', convertNYToLocal('11:50', 'Europe/Dublin'));\n  console.log('15:15 NY →', convertNYToLocal('15:15', 'Europe/Dublin'));\n\n  const dualTime = getCurrentDualTime('Europe/Dublin');\n  console.log('Current dual time:', dualTime.formatted);\n\n  // Expected results (summer time):\n  // 09:00 NY → 14:00 Irish (5 hour difference)\n  // 11:50 NY → 16:50 Irish (5 hour difference)\n  // 15:15 NY → 20:15 Irish (5 hour difference)\n};\n", "/**\n * Dual Time Display Component\n *\n * Displays time in both NY and local timezone with proper formatting.\n * Optimized for international traders working with NY sessions.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport styled from 'styled-components';\nimport {\n  getCurrentDualTime,\n  formatTimeForMobile,\n  getTimeUntilNYTime,\n  convertSessionToDualTime,\n  type DualTimeDisplay as DualTimeData,\n  type TimeInterval,\n} from '../../utils/timeZoneUtils';\n\nexport interface DualTimeDisplayProps {\n  /** Display mode */\n  mode?: 'current' | 'static' | 'countdown' | 'session';\n  /** Static NY time to display (for static mode) */\n  nyTime?: string;\n  /** Target NY time for countdown */\n  targetNYTime?: string;\n  /** Session start and end times (for session mode) */\n  sessionStart?: string;\n  sessionEnd?: string;\n  /** Display format */\n  format?: 'mobile' | 'desktop' | 'compact';\n  /** Show live indicator */\n  showLive?: boolean;\n  /** Custom className */\n  className?: string;\n  /** Update interval in seconds (default: 1) */\n  updateInterval?: number;\n}\n\nconst TimeContainer = styled.div<{ format: string }>`\n  display: flex;\n  align-items: center;\n  gap: ${({ format }) => (format === 'mobile' ? '4px' : '8px')};\n  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;\n  font-weight: 600;\n`;\n\nconst NYTime = styled.span`\n  color: #3b82f6;\n  font-size: inherit;\n`;\n\nconst LocalTime = styled.span`\n  color: #10b981;\n  font-size: inherit;\n`;\n\nconst Separator = styled.span`\n  color: #6b7280;\n  font-size: inherit;\n`;\n\nconst Timezone = styled.span`\n  color: #9ca3af;\n  font-size: 0.85em;\n  font-weight: 500;\n`;\n\nconst LiveIndicator = styled.span`\n  color: #ef4444;\n  font-size: 0.75em;\n  font-weight: bold;\n  animation: pulse 2s infinite;\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 1;\n    }\n    50% {\n      opacity: 0.5;\n    }\n  }\n`;\n\nconst CountdownContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst CountdownValue = styled.span`\n  color: #f59e0b;\n  font-weight: bold;\n`;\n\nconst CountdownLabel = styled.span`\n  color: #9ca3af;\n  font-size: 0.9em;\n`;\n\n/**\n * Current Time Display Component\n */\nconst CurrentTimeDisplay: React.FC<{\n  format: string;\n  showLive: boolean;\n  updateInterval: number;\n}> = ({ format, showLive, updateInterval }) => {\n  const [dualTime, setDualTime] = useState<DualTimeData>(getCurrentDualTime());\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setDualTime(getCurrentDualTime());\n    }, updateInterval * 1000);\n\n    return () => clearInterval(timer);\n  }, [updateInterval]);\n\n  if (format === 'mobile') {\n    return (\n      <TimeContainer format={format}>\n        <span>{formatTimeForMobile(dualTime)}</span>\n        {showLive && <LiveIndicator>LIVE</LiveIndicator>}\n      </TimeContainer>\n    );\n  }\n\n  if (format === 'compact') {\n    return (\n      <TimeContainer format={format}>\n        <NYTime>{dualTime.nyTime}</NYTime>\n        <Separator>|</Separator>\n        <LocalTime>{dualTime.localTime}</LocalTime>\n        {showLive && <LiveIndicator>LIVE</LiveIndicator>}\n      </TimeContainer>\n    );\n  }\n\n  return (\n    <TimeContainer format={format}>\n      <NYTime>{dualTime.nyTime}</NYTime>\n      <Timezone>{dualTime.nyTimezone}</Timezone>\n      <Separator>|</Separator>\n      <LocalTime>{dualTime.localTime}</LocalTime>\n      <Timezone>{dualTime.localTimezone}</Timezone>\n      {showLive && <LiveIndicator>LIVE</LiveIndicator>}\n    </TimeContainer>\n  );\n};\n\n/**\n * Static Time Display Component\n */\nconst StaticTimeDisplay: React.FC<{\n  nyTime: string;\n  format: string;\n}> = ({ nyTime, format }) => {\n  const dualTime = getCurrentDualTime();\n  const sessionTime = convertSessionToDualTime(nyTime, nyTime);\n\n  if (format === 'mobile') {\n    return (\n      <TimeContainer format={format}>\n        <span>\n          {sessionTime.localStart} 🇮🇪 | {nyTime} 🇺🇸\n        </span>\n      </TimeContainer>\n    );\n  }\n\n  if (format === 'compact') {\n    return (\n      <TimeContainer format={format}>\n        <NYTime>{nyTime}</NYTime>\n        <Separator>|</Separator>\n        <LocalTime>{sessionTime.localStart}</LocalTime>\n      </TimeContainer>\n    );\n  }\n\n  return (\n    <TimeContainer format={format}>\n      <NYTime>{nyTime}</NYTime>\n      <Timezone>{dualTime.nyTimezone}</Timezone>\n      <Separator>|</Separator>\n      <LocalTime>{sessionTime.localStart}</LocalTime>\n      <Timezone>{dualTime.localTimezone}</Timezone>\n    </TimeContainer>\n  );\n};\n\n/**\n * Countdown Display Component\n */\nconst CountdownDisplay: React.FC<{\n  targetNYTime: string;\n  format: string;\n  updateInterval: number;\n}> = ({ targetNYTime, format, updateInterval }) => {\n  const [timeUntil, setTimeUntil] = useState<TimeInterval>(getTimeUntilNYTime(targetNYTime));\n\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setTimeUntil(getTimeUntilNYTime(targetNYTime));\n    }, updateInterval * 1000);\n\n    return () => clearInterval(timer);\n  }, [targetNYTime, updateInterval]);\n\n  if (format === 'mobile') {\n    return (\n      <CountdownContainer>\n        <CountdownValue>{timeUntil.formatted}</CountdownValue>\n        <CountdownLabel>until {targetNYTime}</CountdownLabel>\n      </CountdownContainer>\n    );\n  }\n\n  return (\n    <CountdownContainer>\n      <CountdownLabel>Next in:</CountdownLabel>\n      <CountdownValue>{timeUntil.formatted}</CountdownValue>\n      <CountdownLabel>({targetNYTime} NY)</CountdownLabel>\n    </CountdownContainer>\n  );\n};\n\n/**\n * Session Time Display Component\n */\nconst SessionTimeDisplay: React.FC<{\n  sessionStart: string;\n  sessionEnd: string;\n  format: string;\n}> = ({ sessionStart, sessionEnd, format }) => {\n  const sessionTime = convertSessionToDualTime(sessionStart, sessionEnd);\n\n  if (format === 'mobile') {\n    return (\n      <TimeContainer format={format}>\n        <span>{sessionTime.formatted}</span>\n      </TimeContainer>\n    );\n  }\n\n  if (format === 'compact') {\n    return (\n      <TimeContainer format={format}>\n        <NYTime>\n          {sessionStart}-{sessionEnd}\n        </NYTime>\n        <Separator>|</Separator>\n        <LocalTime>\n          {sessionTime.localStart}-{sessionTime.localEnd}\n        </LocalTime>\n      </TimeContainer>\n    );\n  }\n\n  return (\n    <TimeContainer format={format}>\n      <div>\n        <NYTime>\n          {sessionStart}-{sessionEnd} NY\n        </NYTime>\n      </div>\n      <Separator>|</Separator>\n      <div>\n        <LocalTime>\n          {sessionTime.localStart}-{sessionTime.localEnd} Local\n        </LocalTime>\n      </div>\n    </TimeContainer>\n  );\n};\n\n/**\n * Main Dual Time Display Component\n */\nexport const DualTimeDisplay: React.FC<DualTimeDisplayProps> = (props) => {\n  const {\n    mode = 'current',\n    nyTime,\n    targetNYTime,\n    sessionStart,\n    sessionEnd,\n    format = 'desktop',\n    showLive = false,\n    className,\n    updateInterval = 1,\n  } = props;\n  const containerProps = {\n    className,\n    style: {\n      fontSize: format === 'mobile' ? '14px' : format === 'compact' ? '13px' : '14px',\n    },\n  };\n\n  switch (mode) {\n    case 'static':\n      if (!nyTime) {\n        console.warn('DualTimeDisplay: nyTime is required for static mode');\n        return null;\n      }\n      return (\n        <div {...containerProps}>\n          <StaticTimeDisplay nyTime={nyTime} format={format} />\n        </div>\n      );\n\n    case 'countdown':\n      if (!targetNYTime) {\n        console.warn('DualTimeDisplay: targetNYTime is required for countdown mode');\n        return null;\n      }\n      return (\n        <div {...containerProps}>\n          <CountdownDisplay\n            targetNYTime={targetNYTime}\n            format={format}\n            updateInterval={updateInterval}\n          />\n        </div>\n      );\n\n    case 'session':\n      if (!sessionStart || !sessionEnd) {\n        console.warn('DualTimeDisplay: sessionStart and sessionEnd are required for session mode');\n        return null;\n      }\n      return (\n        <div {...containerProps}>\n          <SessionTimeDisplay sessionStart={sessionStart} sessionEnd={sessionEnd} format={format} />\n        </div>\n      );\n\n    case 'current':\n    default:\n      return (\n        <div {...containerProps}>\n          <CurrentTimeDisplay format={format} showLive={showLive} updateInterval={updateInterval} />\n        </div>\n      );\n  }\n};\n\nexport default DualTimeDisplay;\n", "/**\n * Create Store Context\n *\n * A utility for creating a typed context store with actions and selectors.\n */\nimport React, { createContext, useContext, useReducer, useMemo, ReactNode } from 'react';\n\n/**\n * Action with type and payload\n */\nexport interface Action<T = string, P = any> {\n  type: T;\n  payload?: P;\n}\n\n/**\n * Reducer function type\n */\nexport type Reducer<S, A extends Action> = (state: S, action: A) => S;\n\n/**\n * Selector function type\n */\nexport type Selector<S, R> = (state: S) => R;\n\n/**\n * Action creator function type\n */\nexport type ActionCreator<A extends Action> = (...args: any[]) => A;\n\n/**\n * Dispatch function type\n */\nexport type Dispatch<A extends Action> = (action: A) => void;\n\n/**\n * Store context type\n */\nexport interface StoreContext<S, A extends Action> {\n  state: S;\n  dispatch: Dispatch<A>;\n}\n\n/**\n * Store provider props\n */\nexport interface StoreProviderProps {\n  children: ReactNode;\n  initialState?: any;\n}\n\n/**\n * Create a store context with a reducer\n * \n * @param reducer - The reducer function\n * @param initialState - The initial state\n * @param displayName - The display name for the context\n * @returns An object with the context, provider, and hooks\n */\nexport function createStoreContext<S, A extends Action>(\n  reducer: Reducer<S, A>,\n  initialState: S,\n  displayName = 'StoreContext'\n) {\n  // Create the context\n  const Context = createContext<StoreContext<S, A> | undefined>(undefined);\n  Context.displayName = displayName;\n\n  // Create the provider\n  const Provider: React.FC<StoreProviderProps> = ({ \n    children, \n    initialState: overrideInitialState \n  }) => {\n    const [state, dispatch] = useReducer(reducer, overrideInitialState || initialState);\n    const value = useMemo(() => ({ state, dispatch }), [state]);\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  // Create the hook to use the context\n  function useStore(): StoreContext<S, A> {\n    const context = useContext(Context);\n    if (context === undefined) {\n      throw new Error(`use${displayName} must be used within a ${displayName}Provider`);\n    }\n    return context;\n  }\n\n  // Create a hook to use a selector\n  function useSelector<R>(selector: Selector<S, R>): R {\n    const { state } = useStore();\n    return selector(state);\n  }\n\n  // Create a hook to use an action creator\n  function useAction<T extends ActionCreator<A>>(actionCreator: T): (...args: Parameters<T>) => void {\n    const { dispatch } = useStore();\n    return useMemo(\n      () => (...args: Parameters<T>) => {\n        dispatch(actionCreator(...args));\n      },\n      [dispatch, actionCreator]\n    );\n  }\n\n  // Create a hook to use multiple action creators\n  function useActions<T extends Record<string, ActionCreator<A>>>(\n    actionCreators: T\n  ): { [K in keyof T]: (...args: Parameters<T[K]>) => void } {\n    const { dispatch } = useStore();\n    return useMemo(\n      () => {\n        const boundActionCreators = {} as { [K in keyof T]: (...args: Parameters<T[K]>) => void };\n        for (const key in actionCreators) {\n          boundActionCreators[key] = (...args: Parameters<T[typeof key]>) => {\n            dispatch(actionCreators[key](...args));\n          };\n        }\n        return boundActionCreators;\n      },\n      [dispatch, actionCreators]\n    );\n  }\n\n  return {\n    Context,\n    Provider,\n    useStore,\n    useSelector,\n    useAction,\n    useActions,\n  };\n}\n", "/**\n * Create Selector\n *\n * A utility for creating memoized selectors.\n */\n\n/**\n * Selector function type\n */\nexport type Selector<S, R> = (state: S) => R;\n\n/**\n * Input selector function type\n */\nexport type InputSelector<S, R> = Selector<S, R>;\n\n/**\n * Result function type\n */\nexport type ResultFunc<R, T> = (...args: R[]) => T;\n\n/**\n * Create a memoized selector\n * \n * @param inputSelectors - The input selectors\n * @param resultFunc - The result function\n * @returns A memoized selector\n */\nexport function createSelector<S, R1, T>(\n  inputSelector1: InputSelector<S, R1>,\n  resultFunc: (res1: R1) => T\n): Selector<S, T>;\n\nexport function createSelector<S, R1, R2, T>(\n  inputSelector1: InputSelector<S, R1>,\n  inputSelector2: InputSelector<S, R2>,\n  resultFunc: (res1: R1, res2: R2) => T\n): Selector<S, T>;\n\nexport function createSelector<S, R1, R2, R3, T>(\n  inputSelector1: InputSelector<S, R1>,\n  inputSelector2: InputSelector<S, R2>,\n  inputSelector3: InputSelector<S, R3>,\n  resultFunc: (res1: R1, res2: R2, res3: R3) => T\n): Selector<S, T>;\n\nexport function createSelector<S, R1, R2, R3, R4, T>(\n  inputSelector1: InputSelector<S, R1>,\n  inputSelector2: InputSelector<S, R2>,\n  inputSelector3: InputSelector<S, R3>,\n  inputSelector4: InputSelector<S, R4>,\n  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4) => T\n): Selector<S, T>;\n\nexport function createSelector<S, R1, R2, R3, R4, R5, T>(\n  inputSelector1: InputSelector<S, R1>,\n  inputSelector2: InputSelector<S, R2>,\n  inputSelector3: InputSelector<S, R3>,\n  inputSelector4: InputSelector<S, R4>,\n  inputSelector5: InputSelector<S, R5>,\n  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4, res5: R5) => T\n): Selector<S, T>;\n\nexport function createSelector<S, R1, R2, R3, R4, R5, R6, T>(\n  inputSelector1: InputSelector<S, R1>,\n  inputSelector2: InputSelector<S, R2>,\n  inputSelector3: InputSelector<S, R3>,\n  inputSelector4: InputSelector<S, R4>,\n  inputSelector5: InputSelector<S, R5>,\n  inputSelector6: InputSelector<S, R6>,\n  resultFunc: (res1: R1, res2: R2, res3: R3, res4: R4, res5: R5, res6: R6) => T\n): Selector<S, T>;\n\nexport function createSelector<S, T>(\n  ...args: Array<InputSelector<S, any> | ResultFunc<any, T>>\n): Selector<S, T> {\n  const resultFunc = args.pop() as ResultFunc<any, T>;\n  const inputSelectors = args as Array<InputSelector<S, any>>;\n\n  let lastInputs: any[] | null = null;\n  let lastResult: T | null = null;\n\n  return (state: S) => {\n    const inputs = inputSelectors.map(selector => selector(state));\n    \n    // Check if inputs have changed\n    if (\n      lastInputs === null ||\n      inputs.length !== lastInputs.length ||\n      inputs.some((input, index) => input !== lastInputs![index])\n    ) {\n      // Apply the result function to the new inputs\n      lastResult = resultFunc(...inputs);\n      lastInputs = inputs;\n    }\n    \n    return lastResult as T;\n  };\n}\n", "/**\n * Persist State\n *\n * A utility for persisting state to local storage.\n */\nimport { Reducer, Action } from '../state/createStoreContext';\n\n/**\n * Persist state options\n */\nexport interface PersistStateOptions<S> {\n  /** The key to use for local storage */\n  key: string;\n  /** The initial state */\n  initialState: S;\n  /** The version of the state schema */\n  version?: number;\n  /** A function to migrate state from a previous version */\n  migrate?: (state: any, version: number) => S;\n  /** A function to serialize the state */\n  serialize?: (state: S) => string;\n  /** A function to deserialize the state */\n  deserialize?: (serialized: string) => S;\n  /** A function to filter the state before persisting */\n  filter?: (state: S) => Partial<S>;\n  /** A function to merge the persisted state with the initial state */\n  merge?: (persistedState: Partial<S>, initialState: S) => S;\n  /** Whether to debug the persistence */\n  debug?: boolean;\n}\n\n/**\n * Persist state result\n */\nexport interface PersistStateResult<S, A extends Action> {\n  /** The persisted reducer */\n  reducer: Reducer<S, A>;\n  /** The persisted initial state */\n  initialState: S;\n  /** A function to clear the persisted state */\n  clear: () => void;\n}\n\n/**\n * Create a persisted reducer\n *\n * @param reducer - The reducer function\n * @param options - The persist state options\n * @returns The persisted reducer and initial state\n */\nexport function persistState<S, A extends Action>(\n  reducer: Reducer<S, A>,\n  options: PersistStateOptions<S>\n): PersistStateResult<S, A> {\n  const {\n    key,\n    initialState,\n    version = 1,\n    migrate,\n    serialize = JSON.stringify,\n    deserialize = JSON.parse,\n    filter = (state) => state,\n    merge = (persistedState, initialState) => ({ ...initialState, ...persistedState }),\n    debug = false,\n  } = options;\n\n  // Load persisted state from local storage\n  const loadState = (): Partial<S> | null => {\n    try {\n      const serializedState = localStorage.getItem(key);\n      if (serializedState === null) {\n        return null;\n      }\n\n      const { state, version: persistedVersion } = deserialize(serializedState);\n\n      // Migrate state if version has changed\n      if (persistedVersion !== version && migrate) {\n        if (debug) {\n          console.log(`Migrating state from version ${persistedVersion} to ${version}`);\n        }\n        return migrate(state, persistedVersion);\n      }\n\n      return state;\n    } catch (err) {\n      if (debug) {\n        console.error('Error loading state from local storage:', err);\n      }\n      return null;\n    }\n  };\n\n  // Save state to local storage\n  const saveState = (state: S): void => {\n    try {\n      const filteredState = filter(state);\n      const serializedState = serialize({\n        state: filteredState,\n        version,\n      });\n      localStorage.setItem(key, serializedState);\n    } catch (err) {\n      if (debug) {\n        console.error('Error saving state to local storage:', err);\n      }\n    }\n  };\n\n  // Clear persisted state\n  const clear = (): void => {\n    try {\n      localStorage.removeItem(key);\n    } catch (err) {\n      if (debug) {\n        console.error('Error clearing state from local storage:', err);\n      }\n    }\n  };\n\n  // Load persisted state and merge with initial state\n  const persistedState = loadState();\n  const mergedInitialState = persistedState ? merge(persistedState, initialState) : initialState;\n\n  if (debug && persistedState) {\n    console.log('Loaded persisted state:', persistedState);\n    console.log('Merged initial state:', mergedInitialState);\n  }\n\n  // Create persisted reducer\n  const persistedReducer: Reducer<S, A> = (state, action) => {\n    const newState = reducer(state, action);\n    saveState(newState);\n    return newState;\n  };\n\n  return {\n    reducer: persistedReducer,\n    initialState: mergedInitialState,\n    clear,\n  };\n}\n", "/**\n * Daily Guide State\n *\n * State management for the daily guide feature.\n *\n * This module provides:\n * - State interface and initial state\n * - Action types and action creators\n * - Reducer function\n * - Context provider and hooks\n *\n * @example\n * ```tsx\n * // Wrap your component with the provider\n * <DailyGuideProvider>\n *   <YourComponent />\n * </DailyGuideProvider>\n *\n * // Use the state in your component\n * const { state, dispatch } = useDailyGuideStore();\n * const value = useDailyGuideSelector(selectSomething);\n * const action = useDailyGuideAction(dailyGuideActions.doSomething);\n * ```\n */\nimport { createStoreContext, persistState } from '@adhd-trading-dashboard/shared';\nimport { \n  DailyGuideState, \n  DailyGuideData, \n  TradingPlanItem, \n  DailyGuidePreferences \n} from '../types';\n\n// Action types\nexport enum DailyGuideActionTypes {\n  FETCH_DATA_START = 'dailyGuide/FETCH_DATA_START',\n  FETCH_DATA_SUCCESS = 'dailyGuide/FETCH_DATA_SUCCESS',\n  FETCH_DATA_ERROR = 'dailyGuide/FETCH_DATA_ERROR',\n  UPDATE_TRADING_PLAN_ITEM = 'dailyGuide/UPDATE_TRADING_PLAN_ITEM',\n  ADD_TRADING_PLAN_ITEM = 'dailyGuide/ADD_TRADING_PLAN_ITEM',\n  REMOVE_TRADING_PLAN_ITEM = 'dailyGuide/REMOVE_TRADING_PLAN_ITEM',\n  UPDATE_SELECTED_DATE = 'dailyGuide/UPDATE_SELECTED_DATE',\n  UPDATE_MARKET_OVERVIEW = 'dailyGuide/UPDATE_MARKET_OVERVIEW',\n  UPDATE_KEY_PRICE_LEVELS = 'dailyGuide/UPDATE_KEY_PRICE_LEVELS',\n  UPDATE_PREFERENCES = 'dailyGuide/UPDATE_PREFERENCES',\n  RESET_STATE = 'dailyGuide/RESET_STATE',\n}\n\n// Action interfaces\nexport interface FetchDataStartAction {\n  type: DailyGuideActionTypes.FETCH_DATA_START;\n}\n\nexport interface FetchDataSuccessAction {\n  type: DailyGuideActionTypes.FETCH_DATA_SUCCESS;\n  payload: DailyGuideData;\n}\n\nexport interface FetchDataErrorAction {\n  type: DailyGuideActionTypes.FETCH_DATA_ERROR;\n  payload: string;\n}\n\nexport interface UpdateTradingPlanItemAction {\n  type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM;\n  payload: {\n    id: string;\n    completed: boolean;\n  };\n}\n\nexport interface AddTradingPlanItemAction {\n  type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM;\n  payload: TradingPlanItem;\n}\n\nexport interface RemoveTradingPlanItemAction {\n  type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM;\n  payload: string; // Item ID\n}\n\nexport interface UpdateSelectedDateAction {\n  type: DailyGuideActionTypes.UPDATE_SELECTED_DATE;\n  payload: string; // Date string\n}\n\nexport interface UpdateMarketOverviewAction {\n  type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW;\n  payload: DailyGuideData['marketOverview'];\n}\n\nexport interface UpdateKeyPriceLevelsAction {\n  type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS;\n  payload: DailyGuideData['keyPriceLevels'];\n}\n\nexport interface UpdatePreferencesAction {\n  type: DailyGuideActionTypes.UPDATE_PREFERENCES;\n  payload: Partial<DailyGuidePreferences>;\n}\n\nexport interface ResetStateAction {\n  type: DailyGuideActionTypes.RESET_STATE;\n}\n\n// Union type for all actions\nexport type DailyGuideAction =\n  | FetchDataStartAction\n  | FetchDataSuccessAction\n  | FetchDataErrorAction\n  | UpdateTradingPlanItemAction\n  | AddTradingPlanItemAction\n  | RemoveTradingPlanItemAction\n  | UpdateSelectedDateAction\n  | UpdateMarketOverviewAction\n  | UpdateKeyPriceLevelsAction\n  | UpdatePreferencesAction\n  | ResetStateAction;\n\n// Initial state\nexport const initialDailyGuideState: DailyGuideState = {\n  data: {\n    marketOverview: null,\n    tradingPlan: null,\n    keyPriceLevels: [],\n    watchlist: [],\n    marketNews: [],\n  },\n  isLoading: false,\n  error: null,\n  selectedDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format\n};\n\n// Reducer\nexport const dailyGuideReducer = (\n  state: DailyGuideState,\n  action: DailyGuideAction\n): DailyGuideState => {\n  switch (action.type) {\n    case DailyGuideActionTypes.FETCH_DATA_START:\n      return {\n        ...state,\n        isLoading: true,\n        error: null,\n      };\n    case DailyGuideActionTypes.FETCH_DATA_SUCCESS:\n      return {\n        ...state,\n        data: action.payload,\n        isLoading: false,\n        error: null,\n      };\n    case DailyGuideActionTypes.FETCH_DATA_ERROR:\n      return {\n        ...state,\n        isLoading: false,\n        error: action.payload,\n      };\n    case DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM:\n      if (!state.data.tradingPlan) {\n        return state;\n      }\n      return {\n        ...state,\n        data: {\n          ...state.data,\n          tradingPlan: {\n            ...state.data.tradingPlan,\n            items: state.data.tradingPlan.items.map(item =>\n              item.id === action.payload.id\n                ? { ...item, completed: action.payload.completed }\n                : item\n            ),\n          },\n        },\n      };\n    case DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM:\n      if (!state.data.tradingPlan) {\n        return {\n          ...state,\n          data: {\n            ...state.data,\n            tradingPlan: {\n              items: [action.payload],\n              strategy: '',\n              riskManagement: {\n                maxRiskPerTrade: 0,\n                maxDailyLoss: 0,\n                maxTrades: 0,\n                positionSizing: '',\n              },\n              notes: '',\n            },\n          },\n        };\n      }\n      return {\n        ...state,\n        data: {\n          ...state.data,\n          tradingPlan: {\n            ...state.data.tradingPlan,\n            items: [...state.data.tradingPlan.items, action.payload],\n          },\n        },\n      };\n    case DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM:\n      if (!state.data.tradingPlan) {\n        return state;\n      }\n      return {\n        ...state,\n        data: {\n          ...state.data,\n          tradingPlan: {\n            ...state.data.tradingPlan,\n            items: state.data.tradingPlan.items.filter(\n              item => item.id !== action.payload\n            ),\n          },\n        },\n      };\n    case DailyGuideActionTypes.UPDATE_SELECTED_DATE:\n      return {\n        ...state,\n        selectedDate: action.payload,\n      };\n    case DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW:\n      return {\n        ...state,\n        data: {\n          ...state.data,\n          marketOverview: action.payload,\n        },\n      };\n    case DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS:\n      return {\n        ...state,\n        data: {\n          ...state.data,\n          keyPriceLevels: action.payload,\n        },\n      };\n    case DailyGuideActionTypes.RESET_STATE:\n      return {\n        ...initialDailyGuideState,\n        selectedDate: state.selectedDate, // Preserve selected date\n      };\n    default:\n      return state;\n  }\n};\n\n// Persist state\nconst { reducer: persistedReducer, initialState: persistedInitialState } = persistState(\n  dailyGuideReducer,\n  {\n    key: 'dailyGuide',\n    initialState: initialDailyGuideState,\n    version: 1,\n    filter: (state) => ({\n      // Only persist user preferences, not transient data\n      selectedDate: state.selectedDate,\n    }),\n  }\n);\n\n// Create store context\nexport const {\n  Context: DailyGuideContext,\n  Provider: DailyGuideProvider,\n  useStore: useDailyGuideStore,\n  useSelector: useDailyGuideSelector,\n  useAction: useDailyGuideAction,\n  useActions: useDailyGuideActions,\n} = createStoreContext<DailyGuideState, DailyGuideAction>(\n  persistedReducer,\n  persistedInitialState,\n  'DailyGuideContext'\n);\n\n// Action creators\nexport const dailyGuideActions = {\n  fetchDataStart: (): FetchDataStartAction => ({\n    type: DailyGuideActionTypes.FETCH_DATA_START,\n  }),\n  fetchDataSuccess: (data: DailyGuideData): FetchDataSuccessAction => ({\n    type: DailyGuideActionTypes.FETCH_DATA_SUCCESS,\n    payload: data,\n  }),\n  fetchDataError: (error: string): FetchDataErrorAction => ({\n    type: DailyGuideActionTypes.FETCH_DATA_ERROR,\n    payload: error,\n  }),\n  updateTradingPlanItem: (id: string, completed: boolean): UpdateTradingPlanItemAction => ({\n    type: DailyGuideActionTypes.UPDATE_TRADING_PLAN_ITEM,\n    payload: { id, completed },\n  }),\n  addTradingPlanItem: (item: TradingPlanItem): AddTradingPlanItemAction => ({\n    type: DailyGuideActionTypes.ADD_TRADING_PLAN_ITEM,\n    payload: item,\n  }),\n  removeTradingPlanItem: (id: string): RemoveTradingPlanItemAction => ({\n    type: DailyGuideActionTypes.REMOVE_TRADING_PLAN_ITEM,\n    payload: id,\n  }),\n  updateSelectedDate: (date: string): UpdateSelectedDateAction => ({\n    type: DailyGuideActionTypes.UPDATE_SELECTED_DATE,\n    payload: date,\n  }),\n  updateMarketOverview: (overview: DailyGuideData['marketOverview']): UpdateMarketOverviewAction => ({\n    type: DailyGuideActionTypes.UPDATE_MARKET_OVERVIEW,\n    payload: overview,\n  }),\n  updateKeyPriceLevels: (levels: DailyGuideData['keyPriceLevels']): UpdateKeyPriceLevelsAction => ({\n    type: DailyGuideActionTypes.UPDATE_KEY_PRICE_LEVELS,\n    payload: levels,\n  }),\n  resetState: (): ResetStateAction => ({\n    type: DailyGuideActionTypes.RESET_STATE,\n  }),\n};\n", "/**\n * Daily Guide Selectors\n *\n * Selectors for the daily guide feature.\n */\nimport { createSelector } from '@adhd-trading-dashboard/shared';\nimport { DailyGuideState, MarketSentiment, TradingPlanPriority } from '../types';\n\n// Basic selectors\nexport const selectDailyGuideData = (state: DailyGuideState) => state.data;\nexport const selectMarketOverview = (state: DailyGuideState) => state.data.marketOverview;\nexport const selectTradingPlan = (state: DailyGuideState) => state.data.tradingPlan;\nexport const selectKeyPriceLevels = (state: DailyGuideState) => state.data.keyPriceLevels;\nexport const selectWatchlist = (state: DailyGuideState) => state.data.watchlist;\nexport const selectMarketNews = (state: DailyGuideState) => state.data.marketNews;\nexport const selectIsLoading = (state: DailyGuideState) => state.isLoading;\nexport const selectError = (state: DailyGuideState) => state.error;\nexport const selectSelectedDate = (state: DailyGuideState) => state.selectedDate;\n\n// Derived selectors\nexport const selectTradingPlanItems = createSelector(\n  selectTradingPlan,\n  (tradingPlan) => tradingPlan?.items || []\n);\n\nexport const selectCompletedTradingPlanItems = createSelector(\n  selectTradingPlanItems,\n  (items) => items.filter(item => item.completed)\n);\n\nexport const selectIncompleteTradingPlanItems = createSelector(\n  selectTradingPlanItems,\n  (items) => items.filter(item => !item.completed)\n);\n\nexport const selectTradingPlanCompletion = createSelector(\n  selectTradingPlanItems,\n  selectCompletedTradingPlanItems,\n  (allItems, completedItems) => {\n    if (allItems.length === 0) return 0;\n    return completedItems.length / allItems.length;\n  }\n);\n\nexport const selectTradingPlanItemsByPriority = createSelector(\n  selectTradingPlanItems,\n  (items) => {\n    const result: Record<TradingPlanPriority, typeof items> = {\n      high: [],\n      medium: [],\n      low: [],\n    };\n    \n    items.forEach(item => {\n      result[item.priority].push(item);\n    });\n    \n    return result;\n  }\n);\n\nexport const selectMarketIndices = createSelector(\n  selectMarketOverview,\n  (marketOverview) => marketOverview?.indices || []\n);\n\nexport const selectPositiveIndices = createSelector(\n  selectMarketIndices,\n  (indices) => indices.filter(index => index.change > 0)\n);\n\nexport const selectNegativeIndices = createSelector(\n  selectMarketIndices,\n  (indices) => indices.filter(index => index.change < 0)\n);\n\nexport const selectMarketSentiment = createSelector(\n  selectMarketOverview,\n  (marketOverview) => marketOverview?.sentiment || 'neutral' as MarketSentiment\n);\n\nexport const selectMarketSummary = createSelector(\n  selectMarketOverview,\n  (marketOverview) => marketOverview?.summary || ''\n);\n\nexport const selectEconomicEvents = createSelector(\n  selectMarketOverview,\n  (marketOverview) => marketOverview?.economicEvents || []\n);\n\nexport const selectHighImpactEconomicEvents = createSelector(\n  selectEconomicEvents,\n  (events) => events.filter(event => event.importance === 'high')\n);\n\nexport const selectKeyPriceLevelsBySymbol = createSelector(\n  selectKeyPriceLevels,\n  (levels) => {\n    const result: Record<string, typeof levels[0]> = {};\n    \n    levels.forEach(level => {\n      result[level.symbol] = level;\n    });\n    \n    return result;\n  }\n);\n\nexport const selectWatchlistBySymbol = createSelector(\n  selectWatchlist,\n  (watchlist) => {\n    const result: Record<string, typeof watchlist[0]> = {};\n    \n    watchlist.forEach(item => {\n      result[item.symbol] = item;\n    });\n    \n    return result;\n  }\n);\n\nexport const selectHighImpactMarketNews = createSelector(\n  selectMarketNews,\n  (news) => news.filter(item => item.impact === 'high')\n);\n\nexport const selectMarketNewsByDate = createSelector(\n  selectMarketNews,\n  (news) => {\n    const result: Record<string, typeof news> = {};\n    \n    news.forEach(item => {\n      const date = new Date(item.timestamp).toISOString().split('T')[0];\n      if (!result[date]) {\n        result[date] = [];\n      }\n      result[date].push(item);\n    });\n    \n    return result;\n  }\n);\n\nexport const selectHasData = createSelector(\n  selectMarketOverview,\n  selectTradingPlan,\n  selectKeyPriceLevels,\n  (marketOverview, tradingPlan, keyPriceLevels) => {\n    return !!marketOverview || !!tradingPlan || keyPriceLevels.length > 0;\n  }\n);\n\nexport const selectLastUpdated = createSelector(\n  selectMarketOverview,\n  (marketOverview) => marketOverview?.lastUpdated || null\n);\n\n// Export all selectors\nexport const dailyGuideSelectors = {\n  selectDailyGuideData,\n  selectMarketOverview,\n  selectTradingPlan,\n  selectKeyPriceLevels,\n  selectWatchlist,\n  selectMarketNews,\n  selectIsLoading,\n  selectError,\n  selectSelectedDate,\n  selectTradingPlanItems,\n  selectCompletedTradingPlanItems,\n  selectIncompleteTradingPlanItems,\n  selectTradingPlanCompletion,\n  selectTradingPlanItemsByPriority,\n  selectMarketIndices,\n  selectPositiveIndices,\n  selectNegativeIndices,\n  selectMarketSentiment,\n  selectMarketSummary,\n  selectEconomicEvents,\n  selectHighImpactEconomicEvents,\n  selectKeyPriceLevelsBySymbol,\n  selectWatchlistBySymbol,\n  selectHighImpactMarketNews,\n  selectMarketNewsByDate,\n  selectHasData,\n  selectLastUpdated,\n};\n", "/**\n * Use Daily Guide Hook\n *\n * A custom hook for the daily guide feature.\n */\nimport { useEffect, useCallback } from 'react';\nimport {\n  useDailyGuideSelector,\n  useDailyGuideActions,\n  dailyGuideActions,\n  dailyGuideSelectors,\n} from '../state';\nimport { DailyGuideData, TradingPlanItem, TradingPlanPriority } from '../types';\n\n/**\n * Generate mock data for development\n */\nconst generateMockData = (): DailyGuideData => {\n  // Market overview\n  const mockMarketOverview = {\n    sentiment: ['bullish', 'bearish', 'neutral'][Math.floor(Math.random() * 3)] as\n      | 'bullish'\n      | 'bearish'\n      | 'neutral',\n    summary:\n      'Markets are showing mixed signals with tech stocks outperforming the broader indices. Watch for resistance at key levels.',\n    indices: [\n      {\n        symbol: 'SPY',\n        name: 'S&P 500',\n        value: 4500 + Math.random() * 100,\n        change: Math.random() * 2 - 1,\n        changePercent: (Math.random() * 2 - 1) / 100,\n      },\n      {\n        symbol: 'QQQ',\n        name: '<PERSON>sda<PERSON>',\n        value: 14000 + Math.random() * 500,\n        change: Math.random() * 2 - 1,\n        changePercent: (Math.random() * 2 - 1) / 100,\n      },\n      {\n        symbol: 'DIA',\n        name: '<PERSON>',\n        value: 35000 + Math.random() * 500,\n        change: Math.random() * 2 - 1,\n        changePercent: (Math.random() * 2 - 1) / 100,\n      },\n    ],\n    economicEvents: [\n      {\n        title: 'Fed Interest Rate Decision',\n        time: '14:00',\n        importance: 'high' as 'high' | 'medium' | 'low',\n        expected: '5.25%',\n        previous: '5.25%',\n      },\n      {\n        title: 'Unemployment Claims',\n        time: '08:30',\n        importance: 'medium' as 'high' | 'medium' | 'low',\n        expected: '235K',\n        previous: '240K',\n        actual: '232K',\n      },\n      {\n        title: 'GDP Growth Rate',\n        time: '08:30',\n        importance: 'high' as 'high' | 'medium' | 'low',\n        expected: '2.1%',\n        previous: '2.0%',\n      },\n    ],\n    news: [\n      {\n        id: '1',\n        title: 'Fed signals potential rate cuts in upcoming meeting',\n        source: 'Bloomberg',\n        timestamp: new Date().toISOString(),\n        url: 'https://example.com/news/1',\n        impact: 'high' as 'high' | 'medium' | 'low',\n      },\n      {\n        id: '2',\n        title: 'Tech stocks rally on positive earnings surprises',\n        source: 'CNBC',\n        timestamp: new Date().toISOString(),\n        url: 'https://example.com/news/2',\n        impact: 'medium' as 'high' | 'medium' | 'low',\n      },\n      {\n        id: '3',\n        title: 'Oil prices drop on increased supply concerns',\n        source: 'Reuters',\n        timestamp: new Date().toISOString(),\n        url: 'https://example.com/news/3',\n        impact: 'medium' as 'high' | 'medium' | 'low',\n      },\n    ],\n    lastUpdated: new Date().toISOString(),\n  };\n\n  // Trading plan\n  const mockTradingPlan = {\n    items: [\n      {\n        id: '1',\n        description: 'Wait for market open before placing any trades',\n        priority: 'high' as TradingPlanPriority,\n        completed: false,\n      },\n      {\n        id: '2',\n        description: 'Focus on tech sector for long opportunities',\n        priority: 'medium' as TradingPlanPriority,\n        completed: false,\n      },\n      {\n        id: '3',\n        description: 'Use tight stop losses due to expected volatility',\n        priority: 'high' as TradingPlanPriority,\n        completed: false,\n      },\n      {\n        id: '4',\n        description: 'Review earnings reports for potential opportunities',\n        priority: 'medium' as TradingPlanPriority,\n        completed: false,\n      },\n      {\n        id: '5',\n        description: 'Avoid over-trading in the first hour',\n        priority: 'low' as TradingPlanPriority,\n        completed: false,\n      },\n    ],\n    strategy: 'Focus on momentum plays in tech sector with tight risk management',\n    riskManagement: {\n      maxRiskPerTrade: 1.0,\n      maxDailyLoss: 3.0,\n      maxTrades: 5,\n      positionSizing: '2% of account per trade',\n    },\n    notes: 'Market is showing signs of volatility. Be cautious and wait for clear setups.',\n  };\n\n  // Key price levels\n  const mockKeyPriceLevels = [\n    {\n      symbol: 'SPY',\n      support: ['450.00', '445.75', '442.30'],\n      resistance: ['455.50', '460.00', '462.75'],\n      pivotPoint: '452.25',\n    },\n    {\n      symbol: 'QQQ',\n      support: ['365.20', '360.00', '355.50'],\n      resistance: ['370.00', '375.35', '380.00'],\n      pivotPoint: '367.50',\n    },\n    {\n      symbol: 'AAPL',\n      support: ['175.00', '170.50', '165.75'],\n      resistance: ['180.00', '185.50', '190.25'],\n      pivotPoint: '177.25',\n    },\n  ];\n\n  // Watchlist\n  const mockWatchlist = [\n    {\n      symbol: 'AAPL',\n      name: 'Apple Inc.',\n      price: 175.5,\n      reason: 'Earnings report coming up',\n      setup: 'Breakout above 180',\n      entryPrice: 180.25,\n      stopLoss: 175.0,\n      takeProfit: 190.0,\n    },\n    {\n      symbol: 'MSFT',\n      name: 'Microsoft Corp.',\n      price: 340.75,\n      reason: 'Strong technical pattern',\n      setup: 'Bull flag on daily chart',\n      entryPrice: 342.5,\n      stopLoss: 335.0,\n      takeProfit: 355.0,\n    },\n    {\n      symbol: 'NVDA',\n      name: 'NVIDIA Corp.',\n      price: 450.25,\n      reason: 'AI momentum continues',\n      setup: 'Pullback to support',\n      entryPrice: 445.0,\n      stopLoss: 435.0,\n      takeProfit: 475.0,\n    },\n  ];\n\n  return {\n    marketOverview: mockMarketOverview,\n    tradingPlan: mockTradingPlan,\n    keyPriceLevels: mockKeyPriceLevels,\n    watchlist: mockWatchlist,\n    marketNews: mockMarketOverview.news,\n  };\n};\n\n/**\n * Use Daily Guide Hook\n *\n * A custom hook for the daily guide feature.\n *\n * @returns The daily guide state and actions\n */\nexport function useDailyGuide() {\n  // Access store and actions\n  // Removed unused state destructuring - will be added back when needed\n  const actions = useDailyGuideActions(dailyGuideActions);\n\n  // Use selectors for derived state\n  const selectedDate = useDailyGuideSelector(dailyGuideSelectors.selectSelectedDate);\n  const marketOverview = useDailyGuideSelector(dailyGuideSelectors.selectMarketOverview);\n  const tradingPlan = useDailyGuideSelector(dailyGuideSelectors.selectTradingPlan);\n  const keyPriceLevels = useDailyGuideSelector(dailyGuideSelectors.selectKeyPriceLevels);\n  const watchlist = useDailyGuideSelector(dailyGuideSelectors.selectWatchlist);\n  const marketNews = useDailyGuideSelector(dailyGuideSelectors.selectMarketNews);\n  const isLoading = useDailyGuideSelector(dailyGuideSelectors.selectIsLoading);\n  const error = useDailyGuideSelector(dailyGuideSelectors.selectError);\n  const tradingPlanItems = useDailyGuideSelector(dailyGuideSelectors.selectTradingPlanItems);\n  const tradingPlanCompletion = useDailyGuideSelector(\n    dailyGuideSelectors.selectTradingPlanCompletion\n  );\n  const marketSentiment = useDailyGuideSelector(dailyGuideSelectors.selectMarketSentiment);\n  const marketSummary = useDailyGuideSelector(dailyGuideSelectors.selectMarketSummary);\n  const lastUpdated = useDailyGuideSelector(dailyGuideSelectors.selectLastUpdated);\n\n  // Format current date in a readable format\n  const today = new Date(selectedDate);\n  const currentDate = today.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n\n  // API integration - using mock data for now\n  const fetchData = useCallback(async () => {\n    try {\n      actions.fetchDataStart();\n\n      // In a real app, you would fetch data from an API\n      // For now, we'll use mock data with a timeout to simulate API call\n      await new Promise(resolve => setTimeout(resolve, 800));\n\n      const mockData = generateMockData();\n      actions.fetchDataSuccess(mockData);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Unknown error';\n      console.error('Error fetching daily guide data:', err);\n      actions.fetchDataError(errorMessage);\n    }\n  }, [actions]);\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchData();\n  }, [fetchData]);\n\n  // Fetch data when selected date changes\n  useEffect(() => {\n    fetchData();\n  }, [selectedDate, fetchData]);\n\n  // Event handlers\n  const handleDateChange = useCallback(\n    (date: string) => {\n      actions.updateSelectedDate(date);\n    },\n    [actions]\n  );\n\n  const handleTradingPlanItemToggle = useCallback(\n    (id: string, completed: boolean) => {\n      actions.updateTradingPlanItem(id, completed);\n    },\n    [actions]\n  );\n\n  const handleAddTradingPlanItem = useCallback(\n    (item: TradingPlanItem) => {\n      actions.addTradingPlanItem(item);\n    },\n    [actions]\n  );\n\n  const handleRemoveTradingPlanItem = useCallback(\n    (id: string) => {\n      actions.removeTradingPlanItem(id);\n    },\n    [actions]\n  );\n\n  const handleRefresh = useCallback(() => {\n    fetchData();\n  }, [fetchData]);\n\n  return {\n    // State\n    selectedDate,\n    marketOverview,\n    tradingPlan,\n    keyPriceLevels,\n    watchlist,\n    marketNews,\n    isLoading,\n    error,\n    tradingPlanItems,\n    tradingPlanCompletion,\n    marketSentiment,\n    marketSummary,\n    lastUpdated,\n    currentDate,\n\n    // Actions\n    onDateChange: handleDateChange,\n    onTradingPlanItemToggle: handleTradingPlanItemToggle,\n    onAddTradingPlanItem: handleAddTradingPlanItem,\n    onRemoveTradingPlanItem: handleRemoveTradingPlanItem,\n    onRefresh: handleRefresh,\n  };\n}\n", "/**\n * Session Analytics Hook\n * \n * Analyzes user's trading performance by session timing using real trade data.\n * Provides personalized insights based on actual trading patterns.\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { CompleteTradeData, tradeStorageService } from '@adhd-trading-dashboard/shared';\n\nexport interface SessionPerformance {\n  hour: number;\n  label: string;\n  winRate: number;\n  totalTrades: number;\n  avgRMultiple: number;\n  totalPnL: number;\n  bestSetup: string;\n  performance: 'excellent' | 'good' | 'average' | 'poor' | 'avoid';\n}\n\nexport interface CurrentSessionRecommendation {\n  currentHour: number;\n  sessionLabel: string;\n  recommendation: 'high' | 'medium' | 'low' | 'avoid';\n  winRate: number;\n  bestSetups: string[];\n  riskLevel: 'low' | 'medium' | 'high';\n  actionItems: string[];\n}\n\nexport interface SessionAnalytics {\n  sessionPerformance: SessionPerformance[];\n  currentRecommendation: CurrentSessionRecommendation;\n  bestPerformingHours: number[];\n  worstPerformingHours: number[];\n  totalAnalyzedTrades: number;\n  lastUpdated: Date;\n}\n\n/**\n * Get current EST hour (trading timezone)\n */\nconst getCurrentESTHour = (): number => {\n  const now = new Date();\n  const utc = now.getTime() + (now.getTimezoneOffset() * 60000);\n  const est = new Date(utc + (-5 * 3600000)); // EST is UTC-5\n  return est.getHours();\n};\n\n/**\n * Get session label for hour\n */\nconst getSessionLabel = (hour: number): string => {\n  if (hour >= 4 && hour < 9) return 'Pre-Market';\n  if (hour >= 9 && hour < 11) return 'NY Open';\n  if (hour >= 11 && hour < 14) return 'Mid-Day';\n  if (hour >= 14 && hour < 16) return 'NY Close';\n  if (hour >= 16 && hour < 20) return 'After Hours';\n  return 'Overnight';\n};\n\n/**\n * Determine performance level based on win rate and trade count\n */\nconst getPerformanceLevel = (winRate: number, tradeCount: number): SessionPerformance['performance'] => {\n  if (tradeCount < 3) return 'average'; // Not enough data\n  if (winRate >= 70) return 'excellent';\n  if (winRate >= 60) return 'good';\n  if (winRate >= 45) return 'average';\n  if (winRate >= 30) return 'poor';\n  return 'avoid';\n};\n\n/**\n * Get recommendation level based on performance\n */\nconst getRecommendationLevel = (performance: SessionPerformance['performance']): CurrentSessionRecommendation['recommendation'] => {\n  switch (performance) {\n    case 'excellent': return 'high';\n    case 'good': return 'medium';\n    case 'average': return 'medium';\n    case 'poor': return 'low';\n    case 'avoid': return 'avoid';\n    default: return 'medium';\n  }\n};\n\n/**\n * Session Analytics Hook\n */\nexport const useSessionAnalytics = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for session analytics:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate session analytics\n  const analytics: SessionAnalytics = useMemo(() => {\n    if (trades.length === 0) {\n      return {\n        sessionPerformance: [],\n        currentRecommendation: {\n          currentHour: getCurrentESTHour(),\n          sessionLabel: getSessionLabel(getCurrentESTHour()),\n          recommendation: 'medium',\n          winRate: 0,\n          bestSetups: [],\n          riskLevel: 'medium',\n          actionItems: ['Insufficient data for recommendations'],\n        },\n        bestPerformingHours: [],\n        worstPerformingHours: [],\n        totalAnalyzedTrades: 0,\n        lastUpdated: new Date(),\n      };\n    }\n\n    // Group trades by hour\n    const tradesByHour: { [hour: number]: CompleteTradeData[] } = {};\n    \n    trades.forEach(trade => {\n      if (trade.trade.entry_time) {\n        const hour = parseInt(trade.trade.entry_time.split(':')[0]);\n        if (!tradesByHour[hour]) {\n          tradesByHour[hour] = [];\n        }\n        tradesByHour[hour].push(trade);\n      }\n    });\n\n    // Calculate performance for each hour\n    const sessionPerformance: SessionPerformance[] = [];\n    \n    for (let hour = 0; hour < 24; hour++) {\n      const hourTrades = tradesByHour[hour] || [];\n      const winningTrades = hourTrades.filter(t => t.trade.win_loss === 'Win');\n      const winRate = hourTrades.length > 0 ? (winningTrades.length / hourTrades.length) * 100 : 0;\n      const avgRMultiple = hourTrades.length > 0 \n        ? hourTrades.reduce((sum, t) => sum + (t.trade.r_multiple || 0), 0) / hourTrades.length \n        : 0;\n      const totalPnL = hourTrades.reduce((sum, t) => sum + (t.trade.achieved_pl || 0), 0);\n      \n      // Find best setup for this hour\n      const setupCounts: { [setup: string]: number } = {};\n      hourTrades.forEach(trade => {\n        const setup = trade.trade.model_type || 'Unknown';\n        setupCounts[setup] = (setupCounts[setup] || 0) + 1;\n      });\n      const bestSetup = Object.keys(setupCounts).reduce((a, b) => \n        setupCounts[a] > setupCounts[b] ? a : b, 'None');\n\n      sessionPerformance.push({\n        hour,\n        label: getSessionLabel(hour),\n        winRate,\n        totalTrades: hourTrades.length,\n        avgRMultiple,\n        totalPnL,\n        bestSetup,\n        performance: getPerformanceLevel(winRate, hourTrades.length),\n      });\n    }\n\n    // Get current hour recommendation\n    const currentHour = getCurrentESTHour();\n    const currentSession = sessionPerformance[currentHour];\n    \n    // Find best setups for current session\n    const currentHourTrades = tradesByHour[currentHour] || [];\n    const setupWinRates: { [setup: string]: { wins: number; total: number } } = {};\n    \n    currentHourTrades.forEach(trade => {\n      const setup = trade.trade.model_type || 'Unknown';\n      if (!setupWinRates[setup]) {\n        setupWinRates[setup] = { wins: 0, total: 0 };\n      }\n      setupWinRates[setup].total++;\n      if (trade.trade.win_loss === 'Win') {\n        setupWinRates[setup].wins++;\n      }\n    });\n\n    const bestSetups = Object.entries(setupWinRates)\n      .filter(([_, stats]) => stats.total >= 2) // At least 2 trades\n      .sort(([_, a], [__, b]) => (b.wins / b.total) - (a.wins / a.total))\n      .slice(0, 3)\n      .map(([setup]) => setup);\n\n    // Generate action items\n    const actionItems: string[] = [];\n    if (currentSession.performance === 'excellent') {\n      actionItems.push('🎯 Prime trading time - consider larger position sizes');\n      actionItems.push('📈 Focus on your best setups');\n    } else if (currentSession.performance === 'good') {\n      actionItems.push('✅ Good performance window - trade with confidence');\n      actionItems.push('🎯 Stick to proven setups');\n    } else if (currentSession.performance === 'poor' || currentSession.performance === 'avoid') {\n      actionItems.push('⚠️ Low performance period - reduce position sizes');\n      actionItems.push('🛡️ Focus on risk management');\n      actionItems.push('📚 Consider paper trading or review');\n    } else {\n      actionItems.push('📊 Average performance - trade with standard risk');\n      actionItems.push('🎯 Focus on high-probability setups');\n    }\n\n    const currentRecommendation: CurrentSessionRecommendation = {\n      currentHour,\n      sessionLabel: getSessionLabel(currentHour),\n      recommendation: getRecommendationLevel(currentSession.performance),\n      winRate: currentSession.winRate,\n      bestSetups,\n      riskLevel: currentSession.performance === 'excellent' || currentSession.performance === 'good' ? 'low' : \n                 currentSession.performance === 'average' ? 'medium' : 'high',\n      actionItems,\n    };\n\n    // Find best and worst performing hours\n    const sortedByPerformance = sessionPerformance\n      .filter(s => s.totalTrades >= 3) // Only hours with sufficient data\n      .sort((a, b) => b.winRate - a.winRate);\n\n    const bestPerformingHours = sortedByPerformance.slice(0, 3).map(s => s.hour);\n    const worstPerformingHours = sortedByPerformance.slice(-3).map(s => s.hour);\n\n    return {\n      sessionPerformance,\n      currentRecommendation,\n      bestPerformingHours,\n      worstPerformingHours,\n      totalAnalyzedTrades: trades.length,\n      lastUpdated: new Date(),\n    };\n  }, [trades]);\n\n  return {\n    analytics,\n    isLoading,\n    error,\n    refresh: () => {\n      // Trigger re-fetch\n      setTrades([]);\n    },\n  };\n};\n", "/**\n * F1GuideHeader Component\n * \n * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)\n * F1 racing-themed header for the daily guide feature.\n * \n * BENEFITS:\n * - Focused responsibility (header only)\n * - F1 racing theme with date selector\n * - Consistent with other F1Header components\n * - Better separation of concerns\n * - Reusable across guide contexts\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport interface F1GuideHeaderProps {\n  /** Custom className */\n  className?: string;\n  /** Whether data is loading */\n  isLoading?: boolean;\n  /** Whether refresh is in progress */\n  isRefreshing?: boolean;\n  /** Current date */\n  currentDate?: string;\n  /** Selected date */\n  selectedDate?: string;\n  /** Date change handler */\n  onDateChange?: (date: string) => void;\n  /** Refresh handler */\n  onRefresh?: () => void;\n  /** Custom title */\n  title?: string;\n}\n\nconst HeaderContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst F1Header = styled.div`\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: linear-gradient(\n    135deg,\n    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,\n    rgba(75, 85, 99, 0.1) 100%\n  );\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  position: relative;\n  overflow: hidden;\n\n  /* F1 Racing accent line */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg, \n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%, \n      transparent 100%\n    );\n  }\n`;\n\nconst F1Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  text-transform: uppercase;\n  letter-spacing: 2px;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n\n  span {\n    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    font-weight: 800;\n  }\n`;\n\nconst GuideIndicator = styled.div<{ $hasDate: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  color: ${({ $hasDate, theme }) => \n    $hasDate \n      ? theme.colors?.success || 'var(--success-color)'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  border: 1px solid ${({ $hasDate, theme }) => \n    $hasDate \n      ? theme.colors?.success || 'var(--success-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ $hasDate, theme }) => \n    $hasDate \n      ? `${theme.colors?.success || 'var(--success-color)'}20`\n      : 'transparent'};\n\n  &::before {\n    content: '📅';\n    font-size: 12px;\n  }\n`;\n\nconst SubHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst SubTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};\n  margin: 0;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-weight: 600;\n`;\n\nconst DateBadge = styled.span`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  padding: ${({ theme }) => theme.spacing?.xxs || '2px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst ActionsContainer = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst DateSelector = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.sm || '8px'};\n`;\n\nconst DateInput = styled.input`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 140px;\n\n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n    transform: translateY(-1px);\n  }\n\n  &::-webkit-calendar-picker-indicator {\n    filter: invert(1);\n    cursor: pointer;\n  }\n`;\n\nconst ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`\n  background: ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : 'transparent'};\n  color: ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.textInverse || '#ffffff'\n      : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  border: 1px solid ${({ $variant, theme }) => \n    $variant === 'primary' \n      ? theme.colors?.primary || 'var(--primary-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  transition: all 0.2s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  min-width: 100px;\n  justify-content: center;\n\n  &:hover:not(:disabled) {\n    background: ${({ $variant, theme }) => \n      $variant === 'primary' \n        ? theme.colors?.primaryDark || 'var(--primary-dark)'\n        : theme.colors?.surface || 'var(--bg-secondary)'};\n    transform: translateY(-1px);\n    box-shadow: 0 4px 8px ${({ $variant, theme }) => \n      $variant === 'primary' \n        ? `${theme.colors?.primary || 'var(--primary-color)'}40`\n        : 'rgba(0, 0, 0, 0.1)'};\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * F1GuideHeader Component\n * \n * PATTERN: F1 Header Pattern\n * - Racing-inspired styling with date selector\n * - Current date indicator and navigation\n * - Consistent with F1 design system\n * - Accessible and responsive\n * - Professional trading guide appearance\n */\nexport const F1GuideHeader: React.FC<F1GuideHeaderProps> = ({\n  className,\n  isLoading = false,\n  isRefreshing = false,\n  currentDate,\n  selectedDate,\n  onDateChange,\n  onRefresh,\n  title = 'Daily Trading Guide',\n}) => {\n  const hasDate = !!currentDate;\n  const maxDate = new Date().toISOString().split('T')[0];\n  \n  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (onDateChange) {\n      onDateChange(e.target.value);\n    }\n  };\n  \n  return (\n    <HeaderContainer className={className}>\n      {/* F1 Racing Header */}\n      <F1Header>\n        <F1Title>\n          🏎️ DAILY <span>GUIDE</span>\n        </F1Title>\n        <GuideIndicator $hasDate={hasDate}>\n          {hasDate ? currentDate : 'NO DATE'}\n        </GuideIndicator>\n      </F1Header>\n\n      {/* Sub Header */}\n      <SubHeader>\n        <TitleSection>\n          <SubTitle>{title}</SubTitle>\n          {hasDate && (\n            <DateBadge>\n              📅 {currentDate}\n            </DateBadge>\n          )}\n        </TitleSection>\n        \n        <ActionsContainer>\n          <DateSelector>\n            <DateInput\n              type=\"date\"\n              value={selectedDate || ''}\n              onChange={handleDateChange}\n              max={maxDate}\n              title=\"Select date for trading guide\"\n            />\n          </DateSelector>\n          \n          {onRefresh && (\n            <ActionButton\n              $variant=\"primary\"\n              onClick={onRefresh}\n              disabled={isLoading}\n              title={isLoading ? 'Refreshing guide...' : 'Refresh guide data'}\n            >\n              {isLoading || isRefreshing ? '⏳' : '🔄'}\n              {isLoading ? 'Refreshing' : 'Refresh'}\n            </ActionButton>\n          )}\n        </ActionsContainer>\n      </SubHeader>\n    </HeaderContainer>\n  );\n};\n\nexport default F1GuideHeader;\n", "/**\n * F1GuideTabs Component\n *\n * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)\n * F1 racing-themed tabs for guide navigation.\n *\n * BENEFITS:\n * - Focused responsibility (tab navigation only)\n * - F1 racing theme with smooth animations\n * - Consistent with other F1Tab components\n * - Better separation of concerns\n * - Reusable tab navigation pattern\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type GuideTab = 'overview' | 'plan' | 'levels' | 'news';\n\nexport interface F1GuideTabsProps {\n  /** Currently active tab */\n  activeTab: GuideTab;\n  /** Tab change handler */\n  onTabChange: (tab: GuideTab) => void;\n  /** Whether tabs are disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst TabsContainer = styled.div`\n  display: flex;\n  gap: 0;\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0\n    ${({ theme }) => theme.spacing?.xl || '32px'} 0;\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  position: relative;\n`;\n\nconst Tab = styled.button<{ $isActive: boolean; $disabled?: boolean }>`\n  padding: ${({ theme }) => theme.spacing?.md || '12px'}\n    ${({ theme }) => theme.spacing?.lg || '24px'};\n  border: none;\n  background: transparent;\n  color: ${({ $isActive, theme }) =>\n    $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textSecondary || 'var(--text-secondary)'};\n  cursor: ${({ $disabled }) => ($disabled ? 'not-allowed' : 'pointer')};\n  transition: all 0.2s ease;\n  font-weight: ${({ $isActive }) => ($isActive ? '600' : '400')};\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  position: relative;\n  border-bottom: 2px solid transparent;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n\n  /* F1 Racing active indicator */\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -1px;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    transform: scaleX(${({ $isActive }) => ($isActive ? 1 : 0)});\n    transition: transform 0.2s ease;\n    transform-origin: center;\n  }\n\n  /* F1 Racing hover effect */\n  &:hover:not(:disabled) {\n    color: ${({ $isActive, theme }) =>\n      $isActive ? theme.colors?.textPrimary || '#ffffff' : theme.colors?.textPrimary || '#ffffff'};\n    transform: translateY(-1px);\n\n    &::after {\n      transform: scaleX(1);\n      background: ${({ $isActive, theme }) =>\n        $isActive ? theme.colors?.primary || 'var(--primary-color)' : theme.colors?.textSecondary || 'var(--text-secondary)'};\n    }\n  }\n\n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n\n  /* Disabled styling */\n  ${({ $disabled }) =>\n    $disabled &&\n    `\n    opacity: 0.5;\n    cursor: not-allowed;\n  `}\n\n  /* Mobile responsive */\n  @media (max-width: 768px) {\n    padding: ${({ theme }) => theme.spacing?.sm || '8px'}\n      ${({ theme }) => theme.spacing?.md || '12px'};\n    font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  }\n`;\n\nconst TabIcon = styled.span`\n  font-size: 16px;\n\n  @media (max-width: 768px) {\n    font-size: 14px;\n  }\n`;\n\nconst TabLabel = styled.span`\n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\n/**\n * Tab configuration with icons and labels - ICT-Focused\n */\nconst TAB_CONFIG: Record<GuideTab, { icon: string; label: string; description: string }> = {\n  overview: {\n    icon: '🕘',\n    label: 'Session Focus',\n    description: 'Enhanced ICT session intelligence and timing analysis',\n  },\n  plan: {\n    icon: '🧠',\n    label: 'Elite Intelligence',\n    description: 'Advanced ICT trading intelligence with model selection and pattern scoring',\n  },\n  levels: {\n    icon: '🎯',\n    label: 'PD Array Levels',\n    description: 'ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis',\n  },\n  news: {\n    icon: '📰',\n    label: 'Market News',\n    description: 'Latest market news and events',\n  },\n};\n\n/**\n * F1GuideTabs Component\n *\n * PATTERN: F1 Tabs Pattern\n * - Racing-inspired styling with red accents\n * - Smooth hover animations and transitions\n * - Clear visual feedback for active state\n * - Accessible keyboard navigation\n * - Responsive design for mobile\n */\nexport const F1GuideTabs: React.FC<F1GuideTabsProps> = ({\n  activeTab,\n  onTabChange,\n  disabled = false,\n  className,\n}) => {\n  const handleTabClick = (tab: GuideTab) => {\n    if (!disabled) {\n      onTabChange(tab);\n    }\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent, tab: GuideTab) => {\n    if ((event.key === 'Enter' || event.key === ' ') && !disabled) {\n      event.preventDefault();\n      onTabChange(tab);\n    }\n  };\n\n  return (\n    <TabsContainer className={className} role=\"tablist\">\n      {(Object.keys(TAB_CONFIG) as GuideTab[]).map((tab) => {\n        const config = TAB_CONFIG[tab];\n        const isActive = activeTab === tab;\n\n        return (\n          <Tab\n            key={tab}\n            $isActive={isActive}\n            $disabled={disabled}\n            onClick={() => handleTabClick(tab)}\n            onKeyDown={(e) => handleKeyDown(e, tab)}\n            disabled={disabled}\n            role=\"tab\"\n            aria-selected={isActive}\n            aria-controls={`guide-panel-${tab}`}\n            tabIndex={disabled ? -1 : 0}\n            title={config.description}\n          >\n            <TabIcon>{config.icon}</TabIcon>\n            <TabLabel>{config.label}</TabLabel>\n          </Tab>\n        );\n      })}\n    </TabsContainer>\n  );\n};\n\nexport default F1GuideTabs;\n", "/**\n * useGuideNavigation Hook\n * \n * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)\n * Hook for managing guide navigation and tab state.\n * \n * BENEFITS:\n * - Focused responsibility (navigation only)\n * - Persistent tab state with localStorage\n * - Type-safe navigation handling\n * - Reusable across guide components\n * - Better separation of concerns\n */\n\nimport { useState, useCallback, useEffect } from 'react';\nimport { GuideTab } from './F1GuideTabs';\n\nexport interface UseGuideNavigationProps {\n  /** Default tab to show */\n  defaultTab?: GuideTab;\n  /** Storage key for persistence */\n  storageKey?: string;\n}\n\nexport interface UseGuideNavigationReturn {\n  /** Current active tab */\n  activeTab: GuideTab;\n  /** Change active tab */\n  setActiveTab: (tab: GuideTab) => void;\n  /** Navigate to next tab */\n  nextTab: () => void;\n  /** Navigate to previous tab */\n  previousTab: () => void;\n  /** Check if tab is active */\n  isTabActive: (tab: GuideTab) => boolean;\n  /** Get tab index */\n  getTabIndex: (tab: GuideTab) => number;\n  /** Get all available tabs */\n  availableTabs: GuideTab[];\n}\n\n/**\n * Available tabs in order\n */\nconst AVAILABLE_TABS: GuideTab[] = ['overview', 'plan', 'levels', 'news'];\n\n/**\n * Default storage key\n */\nconst DEFAULT_STORAGE_KEY = 'adhd-trading-dashboard:guide:active-tab';\n\n/**\n * Load tab from localStorage\n */\nconst loadTabFromStorage = (storageKey: string, defaultTab: GuideTab): GuideTab => {\n  try {\n    const stored = localStorage.getItem(storageKey);\n    if (stored && AVAILABLE_TABS.includes(stored as GuideTab)) {\n      return stored as GuideTab;\n    }\n  } catch (error) {\n    console.warn('Failed to load guide tab from localStorage:', error);\n  }\n  return defaultTab;\n};\n\n/**\n * Save tab to localStorage\n */\nconst saveTabToStorage = (storageKey: string, tab: GuideTab): void => {\n  try {\n    localStorage.setItem(storageKey, tab);\n  } catch (error) {\n    console.warn('Failed to save guide tab to localStorage:', error);\n  }\n};\n\n/**\n * useGuideNavigation Hook\n * \n * Manages tab navigation state with persistence and keyboard navigation.\n */\nexport const useGuideNavigation = ({\n  defaultTab = 'overview',\n  storageKey = DEFAULT_STORAGE_KEY,\n}: UseGuideNavigationProps = {}): UseGuideNavigationReturn => {\n  \n  // Initialize active tab from storage or default\n  const [activeTab, setActiveTabState] = useState<GuideTab>(() =>\n    loadTabFromStorage(storageKey, defaultTab)\n  );\n  \n  /**\n   * Set active tab with persistence\n   */\n  const setActiveTab = useCallback((tab: GuideTab) => {\n    if (AVAILABLE_TABS.includes(tab)) {\n      setActiveTabState(tab);\n      saveTabToStorage(storageKey, tab);\n    }\n  }, [storageKey]);\n  \n  /**\n   * Navigate to next tab\n   */\n  const nextTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const nextIndex = (currentIndex + 1) % AVAILABLE_TABS.length;\n    setActiveTab(AVAILABLE_TABS[nextIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Navigate to previous tab\n   */\n  const previousTab = useCallback(() => {\n    const currentIndex = AVAILABLE_TABS.indexOf(activeTab);\n    const previousIndex = currentIndex === 0 ? AVAILABLE_TABS.length - 1 : currentIndex - 1;\n    setActiveTab(AVAILABLE_TABS[previousIndex]);\n  }, [activeTab, setActiveTab]);\n  \n  /**\n   * Check if tab is active\n   */\n  const isTabActive = useCallback((tab: GuideTab): boolean => {\n    return activeTab === tab;\n  }, [activeTab]);\n  \n  /**\n   * Get tab index\n   */\n  const getTabIndex = useCallback((tab: GuideTab): number => {\n    return AVAILABLE_TABS.indexOf(tab);\n  }, []);\n  \n  // Handle keyboard navigation\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Only handle if no input is focused\n      if (document.activeElement?.tagName === 'INPUT' || \n          document.activeElement?.tagName === 'TEXTAREA' ||\n          document.activeElement?.tagName === 'SELECT') {\n        return;\n      }\n      \n      // Handle Ctrl/Cmd + Arrow keys for tab navigation\n      if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {\n        switch (event.key) {\n          case 'ArrowLeft':\n            event.preventDefault();\n            previousTab();\n            break;\n          case 'ArrowRight':\n            event.preventDefault();\n            nextTab();\n            break;\n        }\n      }\n      \n      // Handle number keys for direct tab navigation\n      if (event.key >= '1' && event.key <= '4' && !event.ctrlKey && !event.metaKey) {\n        const tabIndex = parseInt(event.key) - 1;\n        if (tabIndex < AVAILABLE_TABS.length) {\n          event.preventDefault();\n          setActiveTab(AVAILABLE_TABS[tabIndex]);\n        }\n      }\n      \n      // Handle Alt + Tab keys for guide-specific navigation\n      if (event.altKey && !event.ctrlKey && !event.metaKey) {\n        switch (event.key.toLowerCase()) {\n          case 'o':\n            event.preventDefault();\n            setActiveTab('overview');\n            break;\n          case 'p':\n            event.preventDefault();\n            setActiveTab('plan');\n            break;\n          case 'l':\n            event.preventDefault();\n            setActiveTab('levels');\n            break;\n          case 'n':\n            event.preventDefault();\n            setActiveTab('news');\n            break;\n        }\n      }\n\n      // Handle 'g' key to go to guide (common UX pattern)\n      if (event.key.toLowerCase() === 'g' && !event.ctrlKey && !event.metaKey && !event.altKey) {\n        // Only if not in an input field\n        if (document.activeElement?.tagName !== 'INPUT' && \n            document.activeElement?.tagName !== 'TEXTAREA') {\n          event.preventDefault();\n          setActiveTab('overview');\n        }\n      }\n    };\n    \n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [nextTab, previousTab, setActiveTab]);\n  \n  return {\n    activeTab,\n    setActiveTab,\n    nextTab,\n    previousTab,\n    isTabActive,\n    getTabIndex,\n    availableTabs: AVAILABLE_TABS,\n  };\n};\n\nexport default useGuideNavigation;\n", "/**\n * Enhanced Setup Intelligence Hook\n * \n * Analyzes primary_setup, secondary_setup, and liquidity_taken combinations\n * from actual trade data to provide sophisticated setup recommendations.\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nexport interface SetupPerformance {\n  setupName: string;\n  totalTrades: number;\n  winRate: number;\n  avgRMultiple: number;\n  avgQuality: number;\n  bestSession: string;\n  successRate: number;\n}\n\nexport interface SetupCombination {\n  primary: string;\n  secondary: string;\n  liquidity: string;\n  performance: {\n    totalTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    avgQuality: number;\n    totalPnL: number;\n  };\n  sessions: {\n    sessionName: string;\n    winRate: number;\n    trades: number;\n  }[];\n  recommendation: string;\n  priority: 'HIGH' | 'MEDIUM' | 'LOW';\n}\n\nexport interface LiquidityIntelligence {\n  liquidityTarget: string;\n  performance: {\n    totalTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    successRate: number;\n  };\n  bestModels: string[];\n  bestSessions: string[];\n  recommendation: string;\n}\n\nexport interface SetupIntelligenceData {\n  topPrimarySetups: SetupPerformance[];\n  topSecondarySetups: SetupPerformance[];\n  bestCombinations: SetupCombination[];\n  liquidityIntelligence: LiquidityIntelligence[];\n  currentRecommendations: {\n    primarySetup: string;\n    secondarySetup: string;\n    liquidityTarget: string;\n    reasoning: string;\n    expectedWinRate: number;\n    expectedRMultiple: number;\n  };\n}\n\n/**\n * Analyze primary setup performance\n */\nconst analyzePrimarySetups = (trades: CompleteTradeData[]): SetupPerformance[] => {\n  const setupMap = new Map<string, CompleteTradeData[]>();\n  \n  trades.forEach(trade => {\n    const primarySetup = trade.setup?.primary_setup;\n    if (primarySetup) {\n      if (!setupMap.has(primarySetup)) {\n        setupMap.set(primarySetup, []);\n      }\n      setupMap.get(primarySetup)!.push(trade);\n    }\n  });\n  \n  const setupPerformances: SetupPerformance[] = [];\n  \n  setupMap.forEach((setupTrades, setupName) => {\n    const totalTrades = setupTrades.length;\n    const wins = setupTrades.filter(t => t.trade.win_loss === 'Win').length;\n    const winRate = totalTrades > 0 ? (wins / totalTrades) * 100 : 0;\n    \n    const rMultiples = setupTrades\n      .map(t => t.trade.r_multiple)\n      .filter((r): r is number => r !== undefined && r !== null);\n    const avgRMultiple = rMultiples.length > 0 ? \n      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n    \n    const qualities = setupTrades\n      .map(t => t.trade.pattern_quality_rating)\n      .filter((q): q is number => q !== undefined && q !== null);\n    const avgQuality = qualities.length > 0 ? \n      qualities.reduce((sum, q) => sum + q, 0) / qualities.length : 0;\n    \n    // Find best session for this setup\n    const sessionMap = new Map<string, { wins: number; total: number }>();\n    setupTrades.forEach(trade => {\n      const session = trade.trade.session || 'Unknown';\n      if (!sessionMap.has(session)) {\n        sessionMap.set(session, { wins: 0, total: 0 });\n      }\n      const sessionData = sessionMap.get(session)!;\n      sessionData.total++;\n      if (trade.trade.win_loss === 'Win') {\n        sessionData.wins++;\n      }\n    });\n    \n    let bestSession = 'Unknown';\n    let bestSessionWinRate = 0;\n    sessionMap.forEach((data, session) => {\n      const sessionWinRate = data.total > 0 ? (data.wins / data.total) * 100 : 0;\n      if (sessionWinRate > bestSessionWinRate && data.total >= 2) {\n        bestSessionWinRate = sessionWinRate;\n        bestSession = session;\n      }\n    });\n    \n    setupPerformances.push({\n      setupName,\n      totalTrades,\n      winRate,\n      avgRMultiple,\n      avgQuality,\n      bestSession,\n      successRate: winRate\n    });\n  });\n  \n  return setupPerformances\n    .filter(s => s.totalTrades >= 2)\n    .sort((a, b) => b.winRate - a.winRate)\n    .slice(0, 5);\n};\n\n/**\n * Analyze setup combinations (primary + secondary + liquidity)\n */\nconst analyzeSetupCombinations = (trades: CompleteTradeData[]): SetupCombination[] => {\n  const combinationMap = new Map<string, CompleteTradeData[]>();\n  \n  trades.forEach(trade => {\n    const primary = trade.setup?.primary_setup || '';\n    const secondary = trade.setup?.secondary_setup || '';\n    const liquidity = trade.setup?.liquidity_taken || '';\n    \n    if (primary && secondary && liquidity) {\n      const key = `${primary}|${secondary}|${liquidity}`;\n      if (!combinationMap.has(key)) {\n        combinationMap.set(key, []);\n      }\n      combinationMap.get(key)!.push(trade);\n    }\n  });\n  \n  const combinations: SetupCombination[] = [];\n  \n  combinationMap.forEach((comboTrades, key) => {\n    const [primary, secondary, liquidity] = key.split('|');\n    const totalTrades = comboTrades.length;\n    \n    if (totalTrades < 2) return; // Skip combinations with insufficient data\n    \n    const wins = comboTrades.filter(t => t.trade.win_loss === 'Win').length;\n    const winRate = (wins / totalTrades) * 100;\n    \n    const rMultiples = comboTrades\n      .map(t => t.trade.r_multiple)\n      .filter((r): r is number => r !== undefined && r !== null);\n    const avgRMultiple = rMultiples.length > 0 ? \n      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n    \n    const qualities = comboTrades\n      .map(t => t.trade.pattern_quality_rating)\n      .filter((q): q is number => q !== undefined && q !== null);\n    const avgQuality = qualities.length > 0 ? \n      qualities.reduce((sum, q) => sum + q, 0) / qualities.length : 0;\n    \n    const totalPnL = comboTrades\n      .map(t => t.trade.achieved_pl || 0)\n      .reduce((sum, pl) => sum + pl, 0);\n    \n    // Analyze session performance for this combination\n    const sessionMap = new Map<string, { wins: number; total: number }>();\n    comboTrades.forEach(trade => {\n      const session = trade.trade.session || 'Unknown';\n      if (!sessionMap.has(session)) {\n        sessionMap.set(session, { wins: 0, total: 0 });\n      }\n      const sessionData = sessionMap.get(session)!;\n      sessionData.total++;\n      if (trade.trade.win_loss === 'Win') {\n        sessionData.wins++;\n      }\n    });\n    \n    const sessions = Array.from(sessionMap.entries()).map(([sessionName, data]) => ({\n      sessionName,\n      winRate: data.total > 0 ? (data.wins / data.total) * 100 : 0,\n      trades: data.total\n    }));\n    \n    // Generate recommendation\n    let recommendation = '';\n    let priority: SetupCombination['priority'] = 'LOW';\n    \n    if (winRate >= 80 && totalTrades >= 3) {\n      recommendation = `PRIORITIZE - Exceptional combination (${winRate.toFixed(0)}% win rate)`;\n      priority = 'HIGH';\n    } else if (winRate >= 70 && avgRMultiple >= 1.5) {\n      recommendation = `EXECUTE WITH CONFIDENCE - Strong performance (${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)}R avg)`;\n      priority = 'HIGH';\n    } else if (winRate >= 60) {\n      recommendation = `SELECTIVE USE - Good combination (${winRate.toFixed(0)}% win rate)`;\n      priority = 'MEDIUM';\n    } else {\n      recommendation = `AVOID OR IMPROVE - Underperforming combination (${winRate.toFixed(0)}% win rate)`;\n      priority = 'LOW';\n    }\n    \n    combinations.push({\n      primary,\n      secondary,\n      liquidity,\n      performance: {\n        totalTrades,\n        winRate,\n        avgRMultiple,\n        avgQuality,\n        totalPnL\n      },\n      sessions,\n      recommendation,\n      priority\n    });\n  });\n  \n  return combinations\n    .sort((a, b) => b.performance.winRate - a.performance.winRate)\n    .slice(0, 10);\n};\n\n/**\n * Analyze liquidity targeting intelligence\n */\nconst analyzeLiquidityIntelligence = (trades: CompleteTradeData[]): LiquidityIntelligence[] => {\n  const liquidityMap = new Map<string, CompleteTradeData[]>();\n  \n  trades.forEach(trade => {\n    const liquidity = trade.setup?.liquidity_taken;\n    if (liquidity) {\n      if (!liquidityMap.has(liquidity)) {\n        liquidityMap.set(liquidity, []);\n      }\n      liquidityMap.get(liquidity)!.push(trade);\n    }\n  });\n  \n  const liquidityIntelligence: LiquidityIntelligence[] = [];\n  \n  liquidityMap.forEach((liquidityTrades, liquidityTarget) => {\n    const totalTrades = liquidityTrades.length;\n    \n    if (totalTrades < 2) return;\n    \n    const wins = liquidityTrades.filter(t => t.trade.win_loss === 'Win').length;\n    const winRate = (wins / totalTrades) * 100;\n    \n    const rMultiples = liquidityTrades\n      .map(t => t.trade.r_multiple)\n      .filter((r): r is number => r !== undefined && r !== null);\n    const avgRMultiple = rMultiples.length > 0 ? \n      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n    \n    // Find best models for this liquidity target\n    const modelMap = new Map<string, { wins: number; total: number }>();\n    liquidityTrades.forEach(trade => {\n      const model = trade.trade.model_type;\n      if (!modelMap.has(model)) {\n        modelMap.set(model, { wins: 0, total: 0 });\n      }\n      const modelData = modelMap.get(model)!;\n      modelData.total++;\n      if (trade.trade.win_loss === 'Win') {\n        modelData.wins++;\n      }\n    });\n    \n    const bestModels = Array.from(modelMap.entries())\n      .filter(([_, data]) => data.total >= 2)\n      .sort((a, b) => (b[1].wins / b[1].total) - (a[1].wins / a[1].total))\n      .slice(0, 2)\n      .map(([model, _]) => model);\n    \n    // Find best sessions for this liquidity target\n    const sessionMap = new Map<string, { wins: number; total: number }>();\n    liquidityTrades.forEach(trade => {\n      const session = trade.trade.session || 'Unknown';\n      if (!sessionMap.has(session)) {\n        sessionMap.set(session, { wins: 0, total: 0 });\n      }\n      const sessionData = sessionMap.get(session)!;\n      sessionData.total++;\n      if (trade.trade.win_loss === 'Win') {\n        sessionData.wins++;\n      }\n    });\n    \n    const bestSessions = Array.from(sessionMap.entries())\n      .filter(([_, data]) => data.total >= 2)\n      .sort((a, b) => (b[1].wins / b[1].total) - (a[1].wins / a[1].total))\n      .slice(0, 2)\n      .map(([session, _]) => session);\n    \n    // Generate recommendation\n    let recommendation = '';\n    if (winRate >= 75) {\n      recommendation = `PRIORITIZE ${liquidityTarget} - Excellent success rate (${winRate.toFixed(0)}%)`;\n    } else if (winRate >= 60) {\n      recommendation = `TARGET ${liquidityTarget} - Good performance (${winRate.toFixed(0)}%)`;\n    } else {\n      recommendation = `SELECTIVE USE ${liquidityTarget} - Average performance (${winRate.toFixed(0)}%)`;\n    }\n    \n    liquidityIntelligence.push({\n      liquidityTarget,\n      performance: {\n        totalTrades,\n        winRate,\n        avgRMultiple,\n        successRate: winRate\n      },\n      bestModels,\n      bestSessions,\n      recommendation\n    });\n  });\n  \n  return liquidityIntelligence\n    .sort((a, b) => b.performance.winRate - a.performance.winRate)\n    .slice(0, 5);\n};\n\n/**\n * Enhanced Setup Intelligence Hook\n */\nexport const useEnhancedSetupIntelligence = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for setup intelligence:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Generate setup intelligence\n  const setupIntelligence: SetupIntelligenceData = useMemo(() => {\n    if (trades.length === 0) {\n      return {\n        topPrimarySetups: [],\n        topSecondarySetups: [],\n        bestCombinations: [],\n        liquidityIntelligence: [],\n        currentRecommendations: {\n          primarySetup: 'Insufficient data',\n          secondarySetup: 'Insufficient data',\n          liquidityTarget: 'Insufficient data',\n          reasoning: 'Import more trade data to generate recommendations',\n          expectedWinRate: 50,\n          expectedRMultiple: 1.0\n        }\n      };\n    }\n\n    const topPrimarySetups = analyzePrimarySetups(trades);\n    const topSecondarySetups = analyzePrimarySetups(trades); // Reuse for secondary\n    const bestCombinations = analyzeSetupCombinations(trades);\n    const liquidityIntelligence = analyzeLiquidityIntelligence(trades);\n    \n    // Generate current recommendations based on best performing combinations\n    const bestCombo = bestCombinations[0];\n    const currentRecommendations = bestCombo ? {\n      primarySetup: bestCombo.primary,\n      secondarySetup: bestCombo.secondary,\n      liquidityTarget: bestCombo.liquidity,\n      reasoning: `Best performing combination: ${bestCombo.performance.winRate.toFixed(0)}% win rate with ${bestCombo.performance.avgRMultiple.toFixed(1)}R average`,\n      expectedWinRate: bestCombo.performance.winRate,\n      expectedRMultiple: bestCombo.performance.avgRMultiple\n    } : {\n      primarySetup: topPrimarySetups[0]?.setupName || 'No data',\n      secondarySetup: 'No data',\n      liquidityTarget: liquidityIntelligence[0]?.liquidityTarget || 'No data',\n      reasoning: 'Based on individual setup performance',\n      expectedWinRate: topPrimarySetups[0]?.winRate || 50,\n      expectedRMultiple: topPrimarySetups[0]?.avgRMultiple || 1.0\n    };\n\n    return {\n      topPrimarySetups,\n      topSecondarySetups,\n      bestCombinations,\n      liquidityIntelligence,\n      currentRecommendations\n    };\n  }, [trades]);\n\n  return {\n    setupIntelligence,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    }\n  };\n};\n", "/**\n * Granular Session Intelligence Hook\n * \n * Detailed session breakdown with optimal entry timing:\n * - 15-30 minute performance windows\n * - Real-time session alerts and guidance\n * - Session transition intelligence\n * - Optimal window recommendations\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\nimport { ModelType } from './useModelSelectionEngine';\n\nexport interface TimeWindow {\n  start: string; // HH:MM format\n  end: string;\n  label: string;\n  description: string;\n}\n\nexport interface SessionWindow {\n  window: TimeWindow;\n  performance: {\n    totalTrades: number;\n    winningTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    totalPnL: number;\n  };\n  modelPreference: ModelType | null;\n  isOptimal: boolean;\n  isPrimary: boolean;\n  recommendation: string;\n}\n\nexport interface SessionAnalysis {\n  sessionName: string;\n  sessionType: 'NY_OPEN' | 'LUNCH_MACRO' | 'MOC' | 'PRE_MARKET';\n  windows: SessionWindow[];\n  overallPerformance: {\n    totalTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    bestWindow: TimeWindow | null;\n    worstWindow: TimeWindow | null;\n  };\n  currentStatus: {\n    isActive: boolean;\n    currentWindow: TimeWindow | null;\n    timeRemaining: number; // minutes\n    nextWindow: TimeWindow | null;\n    recommendation: string;\n  };\n}\n\nexport interface LiveSessionGuidance {\n  currentTime: string;\n  activeSession: SessionAnalysis | null;\n  nextSession: SessionAnalysis | null;\n  timeToNextOptimal: number; // minutes\n  currentRecommendation: string;\n  urgencyLevel: 'LOW' | 'MEDIUM' | 'HIGH';\n}\n\n/**\n * Session window definitions\n */\nconst SESSION_WINDOWS = {\n  NY_OPEN: [\n    { start: '09:30', end: '09:45', label: 'Market Open', description: 'Initial volatility assessment' },\n    { start: '09:45', end: '10:15', label: 'OPTIMAL WINDOW', description: 'Primary opportunity window' },\n    { start: '10:15', end: '10:45', label: 'Secondary Window', description: 'Continuation opportunities' },\n    { start: '10:45', end: '11:00', label: 'Session Wind-down', description: 'Reduced activity period' }\n  ],\n  LUNCH_MACRO: [\n    { start: '11:50', end: '12:10', label: 'PRIMARY WINDOW', description: 'Highest win rate period' },\n    { start: '12:10', end: '12:30', label: 'Continuation Window', description: 'Follow-through opportunities' },\n    { start: '12:30', end: '13:00', label: 'Midday Consolidation', description: 'Range-bound analysis' },\n    { start: '13:00', end: '13:30', label: 'Afternoon Transition', description: 'Preparation for next session' }\n  ],\n  MOC: [\n    { start: '15:30', end: '15:45', label: 'Pre-Close Setup', description: 'End-of-day positioning' },\n    { start: '15:45', end: '16:00', label: 'CLOSE WINDOW', description: 'Final momentum plays' }\n  ],\n  PRE_MARKET: [\n    { start: '08:00', end: '08:30', label: 'Early Pre-Market', description: 'News reaction analysis' },\n    { start: '08:30', end: '09:00', label: 'Pre-Market Prime', description: 'Setup development' },\n    { start: '09:00', end: '09:30', label: 'Market Prep', description: 'Final positioning' }\n  ]\n};\n\n/**\n * Parse time string to minutes since midnight\n */\nconst timeToMinutes = (timeStr: string): number => {\n  const [hours, minutes] = timeStr.split(':').map(Number);\n  return hours * 60 + minutes;\n};\n\n/**\n * Get current time in HH:MM format\n */\nconst getCurrentTime = (): string => {\n  const now = new Date();\n  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;\n};\n\n/**\n * Check if current time is within a window\n */\nconst isTimeInWindow = (currentTime: string, window: TimeWindow): boolean => {\n  const current = timeToMinutes(currentTime);\n  const start = timeToMinutes(window.start);\n  const end = timeToMinutes(window.end);\n  \n  return current >= start && current <= end;\n};\n\n/**\n * Calculate time remaining in window\n */\nconst getTimeRemaining = (currentTime: string, window: TimeWindow): number => {\n  const current = timeToMinutes(currentTime);\n  const end = timeToMinutes(window.end);\n  \n  return Math.max(0, end - current);\n};\n\n/**\n * Extract session from trade data\n */\nconst extractSession = (trade: CompleteTradeData): string | null => {\n  const session = trade.trade.session;\n  if (session) return session;\n  \n  // Try to extract from entry time\n  const entryTime = trade.trade.entry_time;\n  if (!entryTime) return null;\n  \n  const timeMinutes = timeToMinutes(entryTime);\n  \n  // Map time to session\n  if (timeMinutes >= timeToMinutes('09:30') && timeMinutes <= timeToMinutes('11:00')) {\n    return 'NY Open';\n  } else if (timeMinutes >= timeToMinutes('11:50') && timeMinutes <= timeToMinutes('13:30')) {\n    return 'Lunch Macro';\n  } else if (timeMinutes >= timeToMinutes('15:30') && timeMinutes <= timeToMinutes('16:00')) {\n    return 'MOC';\n  } else if (timeMinutes >= timeToMinutes('08:00') && timeMinutes <= timeToMinutes('09:30')) {\n    return 'Pre-Market';\n  }\n  \n  return null;\n};\n\n/**\n * Analyze session performance\n */\nconst analyzeSession = (\n  sessionType: SessionAnalysis['sessionType'],\n  sessionName: string,\n  trades: CompleteTradeData[]\n): SessionAnalysis => {\n  const windows = SESSION_WINDOWS[sessionType];\n  const sessionTrades = trades.filter(trade => {\n    const session = extractSession(trade);\n    return session === sessionName;\n  });\n\n  // Analyze each window\n  const windowAnalysis: SessionWindow[] = windows.map(window => {\n    const windowTrades = sessionTrades.filter(trade => {\n      const entryTime = trade.trade.entry_time;\n      if (!entryTime) return false;\n      return isTimeInWindow(entryTime, window);\n    });\n\n    const totalTrades = windowTrades.length;\n    const winningTrades = windowTrades.filter(t => t.trade.win_loss === 'Win').length;\n    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n    \n    const rMultiples = windowTrades\n      .map(t => t.trade.r_multiple)\n      .filter((r): r is number => r !== undefined && r !== null);\n    const avgRMultiple = rMultiples.length > 0 ? \n      rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n    \n    const totalPnL = windowTrades\n      .map(t => t.trade.achieved_pl || 0)\n      .reduce((sum, pl) => sum + pl, 0);\n\n    // Determine model preference\n    const rdContTrades = windowTrades.filter(t => t.trade.model_type === 'RD-Cont');\n    const fvgRdTrades = windowTrades.filter(t => t.trade.model_type === 'FVG-RD');\n    \n    const rdContWinRate = rdContTrades.length > 0 ? \n      (rdContTrades.filter(t => t.trade.win_loss === 'Win').length / rdContTrades.length) * 100 : 0;\n    const fvgRdWinRate = fvgRdTrades.length > 0 ? \n      (fvgRdTrades.filter(t => t.trade.win_loss === 'Win').length / fvgRdTrades.length) * 100 : 0;\n\n    let modelPreference: ModelType | null = null;\n    if (rdContTrades.length >= 2 && fvgRdTrades.length >= 2) {\n      modelPreference = rdContWinRate > fvgRdWinRate ? 'RD-Cont' : 'FVG-RD';\n    } else if (rdContTrades.length >= 3) {\n      modelPreference = 'RD-Cont';\n    } else if (fvgRdTrades.length >= 3) {\n      modelPreference = 'FVG-RD';\n    }\n\n    // Determine if this is an optimal window\n    const isOptimal = winRate >= 70 && totalTrades >= 3;\n    const isPrimary = window.label.includes('OPTIMAL') || window.label.includes('PRIMARY');\n\n    // Generate recommendation\n    let recommendation = '';\n    if (isOptimal) {\n      recommendation = `PRIORITIZE - ${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)} avg R`;\n    } else if (winRate >= 60 && totalTrades >= 2) {\n      recommendation = `GOOD OPPORTUNITY - ${winRate.toFixed(0)}% win rate`;\n    } else if (totalTrades < 2) {\n      recommendation = 'INSUFFICIENT DATA - Monitor for opportunities';\n    } else {\n      recommendation = `CAUTION - ${winRate.toFixed(0)}% win rate, consider reduced size`;\n    }\n\n    return {\n      window,\n      performance: {\n        totalTrades,\n        winningTrades,\n        winRate,\n        avgRMultiple,\n        totalPnL\n      },\n      modelPreference,\n      isOptimal,\n      isPrimary,\n      recommendation\n    };\n  });\n\n  // Calculate overall session performance\n  const totalTrades = sessionTrades.length;\n  const winningTrades = sessionTrades.filter(t => t.trade.win_loss === 'Win').length;\n  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n  \n  const rMultiples = sessionTrades\n    .map(t => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined && r !== null);\n  const avgRMultiple = rMultiples.length > 0 ? \n    rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n\n  // Find best and worst windows\n  const windowsWithData = windowAnalysis.filter(w => w.performance.totalTrades > 0);\n  const bestWindow = windowsWithData.length > 0 ? \n    windowsWithData.reduce((best, current) => \n      current.performance.winRate > best.performance.winRate ? current : best\n    ).window : null;\n  \n  const worstWindow = windowsWithData.length > 0 ? \n    windowsWithData.reduce((worst, current) => \n      current.performance.winRate < worst.performance.winRate ? current : worst\n    ).window : null;\n\n  // Current status\n  const currentTime = getCurrentTime();\n  const currentWindow = windows.find(w => isTimeInWindow(currentTime, w)) || null;\n  const timeRemaining = currentWindow ? getTimeRemaining(currentTime, currentWindow) : 0;\n  \n  // Find next window\n  const currentMinutes = timeToMinutes(currentTime);\n  const nextWindow = windows.find(w => timeToMinutes(w.start) > currentMinutes) || null;\n  \n  const isActive = currentWindow !== null;\n  \n  let recommendation = '';\n  if (isActive && currentWindow) {\n    const currentWindowAnalysis = windowAnalysis.find(w => w.window === currentWindow);\n    recommendation = currentWindowAnalysis?.recommendation || 'Monitor for opportunities';\n  } else if (nextWindow) {\n    const timeToNext = timeToMinutes(nextWindow.start) - currentMinutes;\n    recommendation = `Next window: ${nextWindow.label} in ${timeToNext} minutes`;\n  } else {\n    recommendation = 'Session not active';\n  }\n\n  return {\n    sessionName,\n    sessionType,\n    windows: windowAnalysis,\n    overallPerformance: {\n      totalTrades,\n      winRate,\n      avgRMultiple,\n      bestWindow,\n      worstWindow\n    },\n    currentStatus: {\n      isActive,\n      currentWindow,\n      timeRemaining,\n      nextWindow,\n      recommendation\n    }\n  };\n};\n\n/**\n * Granular Session Intelligence Hook\n */\nexport const useGranularSessionIntelligence = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for session intelligence:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Analyze all sessions\n  const sessionAnalyses: SessionAnalysis[] = useMemo(() => {\n    return [\n      analyzeSession('NY_OPEN', 'NY Open', trades),\n      analyzeSession('LUNCH_MACRO', 'Lunch Macro', trades),\n      analyzeSession('MOC', 'MOC', trades),\n      analyzeSession('PRE_MARKET', 'Pre-Market', trades)\n    ];\n  }, [trades]);\n\n  // Generate live session guidance\n  const liveGuidance: LiveSessionGuidance = useMemo(() => {\n    const currentTime = getCurrentTime();\n    const activeSession = sessionAnalyses.find(s => s.currentStatus.isActive) || null;\n    \n    // Find next session\n    const currentMinutes = timeToMinutes(currentTime);\n    const nextSession = sessionAnalyses\n      .filter(s => !s.currentStatus.isActive)\n      .find(s => {\n        const firstWindow = s.windows[0];\n        return timeToMinutes(firstWindow.window.start) > currentMinutes;\n      }) || null;\n\n    // Find time to next optimal window\n    let timeToNextOptimal = 0;\n    let urgencyLevel: LiveSessionGuidance['urgencyLevel'] = 'LOW';\n    \n    if (activeSession) {\n      const optimalWindows = activeSession.windows.filter(w => w.isOptimal || w.isPrimary);\n      const currentWindow = activeSession.currentStatus.currentWindow;\n      \n      if (currentWindow && optimalWindows.some(w => w.window === currentWindow)) {\n        timeToNextOptimal = activeSession.currentStatus.timeRemaining;\n        urgencyLevel = timeToNextOptimal <= 15 ? 'HIGH' : 'MEDIUM';\n      }\n    } else if (nextSession) {\n      const nextOptimal = nextSession.windows.find(w => w.isOptimal || w.isPrimary);\n      if (nextOptimal) {\n        timeToNextOptimal = timeToMinutes(nextOptimal.window.start) - currentMinutes;\n        urgencyLevel = timeToNextOptimal <= 30 ? 'MEDIUM' : 'LOW';\n      }\n    }\n\n    // Generate current recommendation\n    let currentRecommendation = '';\n    if (activeSession) {\n      currentRecommendation = activeSession.currentStatus.recommendation;\n    } else if (nextSession && timeToNextOptimal <= 60) {\n      currentRecommendation = `Prepare for ${nextSession.sessionName} in ${timeToNextOptimal} minutes`;\n    } else {\n      currentRecommendation = 'No active trading sessions - Monitor for setups';\n    }\n\n    return {\n      currentTime,\n      activeSession,\n      nextSession,\n      timeToNextOptimal,\n      currentRecommendation,\n      urgencyLevel\n    };\n  }, [sessionAnalyses]);\n\n  return {\n    sessionAnalyses,\n    liveGuidance,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    }\n  };\n};\n", "/**\n * Model Selection Engine Hook\n *\n * Intelligent real-time model selection (RD-Cont vs FVG-RD) based on:\n * - Market volatility analysis\n * - Liquidity context assessment\n * - Timeframe confluence logic\n * - Previous session performance impact\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nexport type ModelType = 'RD-Cont' | 'FVG-RD';\nexport type VolatilityLevel = 'low' | 'medium' | 'high';\nexport type LiquidityContext = 'void' | 'reaction' | 'mixed';\nexport type ConfidenceLevel = 'LOW' | 'MEDIUM' | 'HIGH';\n\nexport interface MarketConditions {\n  volatility: VolatilityLevel;\n  liquidityContext: LiquidityContext;\n  htfTrend: 'bullish' | 'bearish' | 'neutral';\n  previousSessionSuccess: ModelType | null;\n}\n\nexport interface ModelRecommendation {\n  recommendedModel: ModelType;\n  probability: number;\n  confidence: ConfidenceLevel;\n  reasoning: string;\n  alternativeModel: ModelType;\n  alternativeCondition: string;\n  marketConditions: MarketConditions;\n}\n\nexport interface ModelPerformanceStats {\n  model: ModelType;\n  totalTrades: number;\n  winRate: number;\n  avgRMultiple: number;\n  recentPerformance: number; // Last 10 trades win rate\n  volatilityPerformance: {\n    low: { winRate: number; avgR: number; trades: number };\n    medium: { winRate: number; avgR: number; trades: number };\n    high: { winRate: number; avgR: number; trades: number };\n  };\n}\n\n/**\n * Calculate market volatility based on recent trade data\n */\nconst calculateVolatility = (trades: CompleteTradeData[]): VolatilityLevel => {\n  if (trades.length < 5) return 'medium';\n\n  // Get last 10 trades\n  const recentTrades = trades.slice(-10);\n  const rMultiples = recentTrades\n    .map((t) => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined && r !== null);\n\n  if (rMultiples.length < 3) return 'medium';\n\n  // Calculate standard deviation of R-multiples as volatility proxy\n  const mean = rMultiples.reduce((sum, r) => sum + Math.abs(r), 0) / rMultiples.length;\n  const variance =\n    rMultiples.reduce((sum, r) => sum + Math.pow(Math.abs(r) - mean, 2), 0) / rMultiples.length;\n  const stdDev = Math.sqrt(variance);\n\n  // Classify volatility based on R-multiple standard deviation\n  if (stdDev > 1.5) return 'high';\n  if (stdDev > 0.8) return 'medium';\n  return 'low';\n};\n\n/**\n * Assess liquidity context from trade notes and setup data\n */\nconst assessLiquidityContext = (trades: CompleteTradeData[]): LiquidityContext => {\n  if (trades.length < 3) return 'mixed';\n\n  const recentTrades = trades.slice(-5);\n  let voidCount = 0;\n  let reactionCount = 0;\n\n  recentTrades.forEach((trade) => {\n    const notes = (trade.trade.notes || '').toLowerCase();\n    const setup = (trade.trade.setup || '').toLowerCase();\n    const combined = `${notes} ${setup}`;\n\n    if (combined.includes('void') || combined.includes('gap') || combined.includes('imbalance')) {\n      voidCount++;\n    }\n    if (\n      combined.includes('reaction') ||\n      combined.includes('liquidity') ||\n      combined.includes('sweep')\n    ) {\n      reactionCount++;\n    }\n  });\n\n  if (voidCount > reactionCount) return 'void';\n  if (reactionCount > voidCount) return 'reaction';\n  return 'mixed';\n};\n\n/**\n * Determine HTF trend from recent trade directions and success\n */\nconst determineHTFTrend = (trades: CompleteTradeData[]): 'bullish' | 'bearish' | 'neutral' => {\n  if (trades.length < 5) return 'neutral';\n\n  const recentTrades = trades.slice(-10);\n  const winningTrades = recentTrades.filter((t) => t.trade.win_loss === 'Win');\n\n  if (winningTrades.length < 3) return 'neutral';\n\n  const longWins = winningTrades.filter((t) => t.trade.direction === 'Long').length;\n  const shortWins = winningTrades.filter((t) => t.trade.direction === 'Short').length;\n\n  if (longWins > shortWins * 1.5) return 'bullish';\n  if (shortWins > longWins * 1.5) return 'bearish';\n  return 'neutral';\n};\n\n/**\n * Get most successful model from recent session\n */\nconst getPreviousSessionSuccess = (trades: CompleteTradeData[]): ModelType | null => {\n  if (trades.length < 3) return null;\n\n  // Get trades from last 24 hours\n  const yesterday = new Date();\n  yesterday.setDate(yesterday.getDate() - 1);\n\n  const recentTrades = trades.filter((trade) => {\n    const tradeDate = new Date(trade.trade.date);\n    return tradeDate >= yesterday;\n  });\n\n  if (recentTrades.length < 2) return null;\n\n  const rdContWins = recentTrades.filter(\n    (t) => t.trade.model_type === 'RD-Cont' && t.trade.win_loss === 'Win'\n  ).length;\n\n  const fvgRdWins = recentTrades.filter(\n    (t) => t.trade.model_type === 'FVG-RD' && t.trade.win_loss === 'Win'\n  ).length;\n\n  if (rdContWins > fvgRdWins) return 'RD-Cont';\n  if (fvgRdWins > rdContWins) return 'FVG-RD';\n  return null;\n};\n\n/**\n * Model Selection Engine Hook\n */\nexport const useModelSelectionEngine = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for model selection:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate model performance statistics\n  const modelStats: Record<ModelType, ModelPerformanceStats> = useMemo(() => {\n    const rdContTrades = trades.filter((t) => t.trade.model_type === 'RD-Cont');\n    const fvgRdTrades = trades.filter((t) => t.trade.model_type === 'FVG-RD');\n\n    const calculateStats = (\n      modelTrades: CompleteTradeData[],\n      model: ModelType\n    ): ModelPerformanceStats => {\n      const totalTrades = modelTrades.length;\n      const winningTrades = modelTrades.filter((t) => t.trade.win_loss === 'Win');\n      const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;\n\n      const rMultiples = modelTrades\n        .map((t) => t.trade.r_multiple)\n        .filter((r): r is number => r !== undefined && r !== null);\n      const avgRMultiple =\n        rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n\n      // Recent performance (last 10 trades)\n      const recentTrades = modelTrades.slice(-10);\n      const recentWins = recentTrades.filter((t) => t.trade.win_loss === 'Win').length;\n      const recentPerformance =\n        recentTrades.length > 0 ? (recentWins / recentTrades.length) * 100 : 0;\n\n      // Volatility performance analysis\n      const volatilityPerformance = {\n        low: { winRate: 0, avgR: 0, trades: 0 },\n        medium: { winRate: 0, avgR: 0, trades: 0 },\n        high: { winRate: 0, avgR: 0, trades: 0 },\n      };\n\n      // This is a simplified volatility classification - in real implementation,\n      // you'd want to classify each trade's market conditions at the time\n      const lowVolTrades = modelTrades.filter((t) => (t.trade.r_multiple || 0) <= 1);\n      const highVolTrades = modelTrades.filter((t) => (t.trade.r_multiple || 0) >= 2);\n      const medVolTrades = modelTrades.filter((t) => {\n        const r = t.trade.r_multiple || 0;\n        return r > 1 && r < 2;\n      });\n\n      [\n        { trades: lowVolTrades, key: 'low' as const },\n        { trades: medVolTrades, key: 'medium' as const },\n        { trades: highVolTrades, key: 'high' as const },\n      ].forEach(({ trades: volTrades, key }) => {\n        const wins = volTrades.filter((t) => t.trade.win_loss === 'Win').length;\n        const rs = volTrades\n          .map((t) => t.trade.r_multiple)\n          .filter((r): r is number => r !== undefined);\n\n        volatilityPerformance[key] = {\n          winRate: volTrades.length > 0 ? (wins / volTrades.length) * 100 : 0,\n          avgR: rs.length > 0 ? rs.reduce((sum, r) => sum + r, 0) / rs.length : 0,\n          trades: volTrades.length,\n        };\n      });\n\n      return {\n        model,\n        totalTrades,\n        winRate,\n        avgRMultiple,\n        recentPerformance,\n        volatilityPerformance,\n      };\n    };\n\n    return {\n      'RD-Cont': calculateStats(rdContTrades, 'RD-Cont'),\n      'FVG-RD': calculateStats(fvgRdTrades, 'FVG-RD'),\n    };\n  }, [trades]);\n\n  // Generate intelligent model recommendation\n  const recommendation: ModelRecommendation = useMemo(() => {\n    if (trades.length < 5) {\n      return {\n        recommendedModel: 'RD-Cont',\n        probability: 60,\n        confidence: 'LOW',\n        reasoning: 'Insufficient data for analysis. RD-Cont recommended as default.',\n        alternativeModel: 'FVG-RD',\n        alternativeCondition: 'if clear FVG setups present',\n        marketConditions: {\n          volatility: 'medium',\n          liquidityContext: 'mixed',\n          htfTrend: 'neutral',\n          previousSessionSuccess: null,\n        },\n      };\n    }\n\n    // Analyze current market conditions\n    const volatility = calculateVolatility(trades);\n    const liquidityContext = assessLiquidityContext(trades);\n    const htfTrend = determineHTFTrend(trades);\n    const previousSessionSuccess = getPreviousSessionSuccess(trades);\n\n    const marketConditions: MarketConditions = {\n      volatility,\n      liquidityContext,\n      htfTrend,\n      previousSessionSuccess,\n    };\n\n    // Decision logic based on conditions\n    let score = 0; // Positive = FVG-RD, Negative = RD-Cont\n    const reasons: string[] = [];\n\n    // Volatility analysis\n    if (volatility === 'high') {\n      score += 2;\n      reasons.push('High volatility favors FVG-RD (higher R-multiple potential)');\n    } else if (volatility === 'low') {\n      score -= 2;\n      reasons.push('Low volatility favors RD-Cont (higher win rate)');\n    }\n\n    // Liquidity context\n    if (liquidityContext === 'void') {\n      score += 1.5;\n      reasons.push('Liquidity voids present favor FVG-RD targeting fills');\n    } else if (liquidityContext === 'reaction') {\n      score -= 1.5;\n      reasons.push('Strong liquidity reactions favor RD-Cont continuation patterns');\n    }\n\n    // HTF trend alignment\n    if (htfTrend !== 'neutral') {\n      score -= 1;\n      reasons.push('Clear HTF trend structure favors RD-Cont within established trends');\n    }\n\n    // Previous session performance\n    if (previousSessionSuccess === 'RD-Cont') {\n      score -= 0.5;\n      reasons.push('Recent RD-Cont success adds slight bias');\n    } else if (previousSessionSuccess === 'FVG-RD') {\n      score += 0.5;\n      reasons.push('Recent FVG-RD success adds slight bias');\n    }\n\n    // Model performance comparison\n    const rdContStats = modelStats['RD-Cont'];\n    const fvgRdStats = modelStats['FVG-RD'];\n\n    if (rdContStats.winRate > fvgRdStats.winRate + 10) {\n      score -= 1;\n      reasons.push(\n        `RD-Cont shows superior win rate (${rdContStats.winRate.toFixed(\n          1\n        )}% vs ${fvgRdStats.winRate.toFixed(1)}%)`\n      );\n    } else if (fvgRdStats.avgRMultiple > rdContStats.avgRMultiple + 0.5) {\n      score += 1;\n      reasons.push(\n        `FVG-RD shows superior R-multiple (${fvgRdStats.avgRMultiple.toFixed(\n          1\n        )} vs ${rdContStats.avgRMultiple.toFixed(1)})`\n      );\n    }\n\n    // Determine recommendation\n    const recommendedModel: ModelType = score > 0 ? 'FVG-RD' : 'RD-Cont';\n    const alternativeModel: ModelType = score > 0 ? 'RD-Cont' : 'FVG-RD';\n\n    // Calculate probability and confidence\n    const absScore = Math.abs(score);\n    const probability = Math.min(50 + absScore * 8, 85); // 50-85% range\n\n    let confidence: ConfidenceLevel;\n    if (absScore >= 3) confidence = 'HIGH';\n    else if (absScore >= 1.5) confidence = 'MEDIUM';\n    else confidence = 'LOW';\n\n    const reasoning = reasons.join('; ');\n\n    const alternativeCondition =\n      recommendedModel === 'FVG-RD'\n        ? 'if pattern quality <3.5 or low volatility'\n        : 'if high volatility + clear FVG confluence';\n\n    return {\n      recommendedModel,\n      probability,\n      confidence,\n      reasoning,\n      alternativeModel,\n      alternativeCondition,\n      marketConditions,\n    };\n  }, [trades, modelStats]);\n\n  return {\n    recommendation,\n    modelStats,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    },\n  };\n};\n", "/**\n * Pattern Quality Scoring System Hook\n * \n * Real-time quality assessment (1-5 scale) replacing subjective pattern ratings with:\n * - PD Array confluence scoring\n * - FVG characteristics assessment\n * - RD strength evaluation\n * - Confirmation signal integration\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\nimport { PDArrayElement } from './usePDArrayAnalytics';\n\nexport interface PatternQualityScore {\n  totalScore: number; // 1-5 scale\n  maxScore: number;\n  breakdown: {\n    pdArrayConfluence: number;\n    fvgCharacteristics: number;\n    rdStrength: number;\n    confirmationSignals: number;\n    volumeConfirmation: number;\n  };\n  rating: 'POOR' | 'FAIR' | 'GOOD' | 'EXCELLENT' | 'EXCEPTIONAL';\n  recommendation: string;\n  expectedWinProbability: number;\n}\n\nexport interface LivePatternAnalysis {\n  currentScore: PatternQualityScore;\n  historicalAccuracy: number; // How accurate our scoring has been\n  scoreDistribution: {\n    score: number;\n    count: number;\n    winRate: number;\n    avgRMultiple: number;\n  }[];\n}\n\n/**\n * Extract PD Array elements from trade data\n */\nconst extractPDArrayElements = (trade: CompleteTradeData): PDArrayElement[] => {\n  const elements: PDArrayElement[] = [];\n  const setupInfo = trade.trade.model_type?.toLowerCase() || '';\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const combined = `${setupInfo} ${notes} ${setup}`;\n  \n  // Check for FVG (Fair Value Gaps)\n  if (combined.includes('fvg') || combined.includes('fair value gap') || combined.includes('imbalance')) {\n    elements.push('FVG');\n  }\n  \n  // Check for NWOG (New Week Opening Gap)\n  if (combined.includes('nwog') || combined.includes('new week opening') || combined.includes('weekly gap')) {\n    elements.push('NWOG');\n  }\n  \n  // Check for NDOG (New Day Opening Gap)\n  if (combined.includes('ndog') || combined.includes('new day opening') || combined.includes('daily gap')) {\n    elements.push('NDOG');\n  }\n  \n  // Check for Liquidity\n  if (combined.includes('liquidity') || combined.includes('sweep') || combined.includes('raid') || combined.includes('hunt')) {\n    elements.push('Liquidity');\n  }\n  \n  return elements;\n};\n\n/**\n * Calculate PD Array confluence score (0-2.0 points)\n */\nconst calculatePDArrayConfluence = (elements: PDArrayElement[]): number => {\n  const elementCount = elements.length;\n  \n  if (elementCount === 0) return 0;\n  if (elementCount === 1) return 0.5;\n  if (elementCount === 2) return 1.2;\n  if (elementCount === 3) return 1.7;\n  return 2.0; // 4+ elements\n};\n\n/**\n * Assess FVG characteristics (0-1.5 points)\n */\nconst assessFVGCharacteristics = (trade: CompleteTradeData): number => {\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const combined = `${notes} ${setup}`;\n  \n  if (!combined.includes('fvg') && !combined.includes('fair value gap') && !combined.includes('imbalance')) {\n    return 0;\n  }\n  \n  let score = 0.5; // Base score for having FVG\n  \n  // Size analysis - look for keywords indicating optimal size\n  if (combined.includes('clean') || combined.includes('clear') || combined.includes('perfect')) {\n    score += 0.3;\n  }\n  \n  // HTF location bonus\n  if (combined.includes('htf') || combined.includes('higher timeframe') || combined.includes('daily') || combined.includes('4h')) {\n    score += 0.3;\n  }\n  \n  // Freshness factor\n  if (combined.includes('fresh') || combined.includes('new') || combined.includes('untested')) {\n    score += 0.2;\n  }\n  \n  // Age penalty\n  if (combined.includes('old') || combined.includes('tested') || combined.includes('multiple')) {\n    score -= 0.2;\n  }\n  \n  return Math.min(Math.max(score, 0), 1.5);\n};\n\n/**\n * Evaluate RD strength (0-1.0 points)\n */\nconst evaluateRDStrength = (trade: CompleteTradeData): number => {\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const rdType = trade.trade.rd_type?.toLowerCase() || '';\n  const combined = `${notes} ${setup} ${rdType}`;\n  \n  let score = 0;\n  \n  // Base RD presence\n  if (combined.includes('rd') || combined.includes('reaction') || combined.includes('displacement')) {\n    score += 0.3;\n  }\n  \n  // Displacement speed indicators\n  if (combined.includes('fast') || combined.includes('strong') || combined.includes('aggressive') || combined.includes('sharp')) {\n    score += 0.3;\n  }\n  \n  // Volume confirmation\n  if (combined.includes('volume') || combined.includes('high vol') || combined.includes('heavy')) {\n    score += 0.2;\n  }\n  \n  // Clear structure\n  if (combined.includes('clean') || combined.includes('clear') || combined.includes('obvious')) {\n    score += 0.2;\n  }\n  \n  return Math.min(score, 1.0);\n};\n\n/**\n * Assess confirmation signals (0-1.0 points)\n */\nconst assessConfirmationSignals = (trade: CompleteTradeData): number => {\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const combined = `${notes} ${setup}`;\n  \n  let score = 0;\n  \n  // Entry validation patterns\n  if (combined.includes('doji') || combined.includes('hammer') || combined.includes('engulfing') || combined.includes('pin bar')) {\n    score += 0.3;\n  }\n  \n  // Momentum alignment\n  if (combined.includes('momentum') || combined.includes('follow through') || combined.includes('continuation')) {\n    score += 0.3;\n  }\n  \n  // Multiple timeframe confirmation\n  if (combined.includes('confluence') || combined.includes('alignment') || combined.includes('multiple tf')) {\n    score += 0.4;\n  }\n  \n  return Math.min(score, 1.0);\n};\n\n/**\n * Calculate volume confirmation score (0-0.5 points)\n */\nconst calculateVolumeConfirmation = (trade: CompleteTradeData): number => {\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const combined = `${notes} ${setup}`;\n  \n  if (combined.includes('volume') || combined.includes('vol') || combined.includes('heavy') || combined.includes('spike')) {\n    return 0.5;\n  }\n  \n  return 0;\n};\n\n/**\n * Calculate pattern quality score for a trade\n */\nconst calculatePatternQuality = (trade: CompleteTradeData): PatternQualityScore => {\n  const elements = extractPDArrayElements(trade);\n  \n  const breakdown = {\n    pdArrayConfluence: calculatePDArrayConfluence(elements),\n    fvgCharacteristics: assessFVGCharacteristics(trade),\n    rdStrength: evaluateRDStrength(trade),\n    confirmationSignals: assessConfirmationSignals(trade),\n    volumeConfirmation: calculateVolumeConfirmation(trade)\n  };\n  \n  const totalScore = Object.values(breakdown).reduce((sum, score) => sum + score, 0);\n  const maxScore = 6.0; // Maximum possible score\n  const normalizedScore = Math.min((totalScore / maxScore) * 5, 5); // Convert to 1-5 scale\n  \n  // Determine rating\n  let rating: PatternQualityScore['rating'];\n  if (normalizedScore >= 4.5) rating = 'EXCEPTIONAL';\n  else if (normalizedScore >= 3.5) rating = 'EXCELLENT';\n  else if (normalizedScore >= 2.5) rating = 'GOOD';\n  else if (normalizedScore >= 1.5) rating = 'FAIR';\n  else rating = 'POOR';\n  \n  // Generate recommendation\n  let recommendation: string;\n  if (normalizedScore >= 4.0) {\n    recommendation = 'PRIORITIZE EXECUTION - High probability setup';\n  } else if (normalizedScore >= 3.0) {\n    recommendation = 'EXECUTE WITH CONFIDENCE - Good setup quality';\n  } else if (normalizedScore >= 2.0) {\n    recommendation = 'PROCEED WITH CAUTION - Average setup';\n  } else {\n    recommendation = 'AVOID OR REDUCE SIZE - Low quality setup';\n  }\n  \n  // Calculate expected win probability based on score\n  const expectedWinProbability = Math.min(40 + (normalizedScore * 12), 90);\n  \n  return {\n    totalScore: normalizedScore,\n    maxScore: 5.0,\n    breakdown,\n    rating,\n    recommendation,\n    expectedWinProbability\n  };\n};\n\n/**\n * Pattern Quality Scoring Hook\n */\nexport const usePatternQualityScoring = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for pattern quality scoring:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate live pattern analysis\n  const analysis: LivePatternAnalysis = useMemo(() => {\n    if (trades.length === 0) {\n      return {\n        currentScore: {\n          totalScore: 0,\n          maxScore: 5.0,\n          breakdown: {\n            pdArrayConfluence: 0,\n            fvgCharacteristics: 0,\n            rdStrength: 0,\n            confirmationSignals: 0,\n            volumeConfirmation: 0\n          },\n          rating: 'POOR',\n          recommendation: 'No data available for analysis',\n          expectedWinProbability: 50\n        },\n        historicalAccuracy: 0,\n        scoreDistribution: []\n      };\n    }\n\n    // Calculate scores for all trades\n    const scoredTrades = trades.map(trade => ({\n      trade,\n      score: calculatePatternQuality(trade),\n      actualWin: trade.trade.win_loss === 'Win'\n    }));\n\n    // Calculate historical accuracy of our scoring system\n    const accuracyData = scoredTrades.filter(st => st.score.totalScore > 0);\n    let correctPredictions = 0;\n    \n    accuracyData.forEach(st => {\n      const predictedWin = st.score.expectedWinProbability > 60;\n      if (predictedWin === st.actualWin) {\n        correctPredictions++;\n      }\n    });\n    \n    const historicalAccuracy = accuracyData.length > 0 ? \n      (correctPredictions / accuracyData.length) * 100 : 0;\n\n    // Calculate score distribution\n    const scoreDistribution = [1, 2, 3, 4, 5].map(score => {\n      const tradesInRange = scoredTrades.filter(st => \n        st.score.totalScore >= score - 0.5 && st.score.totalScore < score + 0.5\n      );\n      \n      const wins = tradesInRange.filter(st => st.actualWin).length;\n      const winRate = tradesInRange.length > 0 ? (wins / tradesInRange.length) * 100 : 0;\n      \n      const rMultiples = tradesInRange\n        .map(st => st.trade.trade.r_multiple)\n        .filter((r): r is number => r !== undefined && r !== null);\n      const avgRMultiple = rMultiples.length > 0 ? \n        rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n\n      return {\n        score,\n        count: tradesInRange.length,\n        winRate,\n        avgRMultiple\n      };\n    });\n\n    // Use most recent trade for current score (in real implementation, this would be live market data)\n    const currentScore = trades.length > 0 ? \n      calculatePatternQuality(trades[trades.length - 1]) : \n      {\n        totalScore: 0,\n        maxScore: 5.0,\n        breakdown: {\n          pdArrayConfluence: 0,\n          fvgCharacteristics: 0,\n          rdStrength: 0,\n          confirmationSignals: 0,\n          volumeConfirmation: 0\n        },\n        rating: 'POOR' as const,\n        recommendation: 'No current setup to analyze',\n        expectedWinProbability: 50\n      };\n\n    return {\n      currentScore,\n      historicalAccuracy,\n      scoreDistribution\n    };\n  }, [trades]);\n\n  return {\n    analysis,\n    isLoading,\n    error,\n    calculatePatternQuality, // Export for external use\n    refresh: () => {\n      setTrades([]);\n    }\n  };\n};\n", "/**\n * Success Probability Calculator Hook\n * \n * Real-time probability assessment based on current conditions:\n * - Multi-factor analysis combining model, session, quality, and market conditions\n * - Dynamic risk management recommendations\n * - Position sizing based on confluence factors\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\nimport { ModelType, ModelRecommendation } from './useModelSelectionEngine';\nimport { PatternQualityScore } from './usePatternQualityScoring';\n\nexport interface SuccessProbability {\n  finalProbability: number;\n  confidence: 'LOW' | 'MEDIUM' | 'HIGH';\n  recommendation: 'AVOID' | 'REDUCE_SIZE' | 'STANDARD' | 'INCREASE_SIZE' | 'PRIORITIZE';\n  expectedRMultiple: {\n    min: number;\n    max: number;\n    average: number;\n  };\n  breakdown: {\n    baseModelWinRate: number;\n    sessionBonus: number;\n    qualityBonus: number;\n    newsImpact: number;\n    volumeBonus: number;\n    confluenceBonus: number;\n  };\n  riskManagement: {\n    positionSizing: 'CONSERVATIVE' | 'STANDARD' | 'AGGRESSIVE';\n    maxRiskPercent: number;\n    stopLossMultiplier: number;\n    takeProfitTargets: number[];\n  };\n}\n\nexport interface MarketContext {\n  isNewsDay: boolean;\n  newsImpact: 'LOW' | 'MEDIUM' | 'HIGH';\n  volumeProfile: 'LOW' | 'NORMAL' | 'HIGH';\n  marketHours: 'PRE_MARKET' | 'REGULAR' | 'AFTER_HOURS';\n  dayOfWeek: 'MONDAY' | 'TUESDAY' | 'WEDNESDAY' | 'THURSDAY' | 'FRIDAY';\n}\n\n/**\n * Determine current market context\n */\nconst getCurrentMarketContext = (): MarketContext => {\n  const now = new Date();\n  const hour = now.getHours();\n  const dayOfWeek = ['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'][now.getDay()] as MarketContext['dayOfWeek'];\n  \n  // Determine market hours\n  let marketHours: MarketContext['marketHours'];\n  if (hour < 9 || (hour === 9 && now.getMinutes() < 30)) {\n    marketHours = 'PRE_MARKET';\n  } else if (hour >= 16) {\n    marketHours = 'AFTER_HOURS';\n  } else {\n    marketHours = 'REGULAR';\n  }\n\n  // Simple news detection (in real implementation, this would integrate with news APIs)\n  const isNewsDay = dayOfWeek === 'FRIDAY' || dayOfWeek === 'WEDNESDAY'; // Simplified\n  const newsImpact: MarketContext['newsImpact'] = isNewsDay ? 'MEDIUM' : 'LOW';\n\n  // Volume profile estimation (in real implementation, this would use actual volume data)\n  const volumeProfile: MarketContext['volumeProfile'] = \n    marketHours === 'REGULAR' && (hour >= 9 && hour <= 11) ? 'HIGH' : 'NORMAL';\n\n  return {\n    isNewsDay,\n    newsImpact,\n    volumeProfile,\n    marketHours,\n    dayOfWeek\n  };\n};\n\n/**\n * Calculate session performance bonus\n */\nconst calculateSessionBonus = (\n  currentHour: number,\n  sessionPerformance: { hour: number; winRate: number; totalTrades: number }[]\n): number => {\n  const currentSession = sessionPerformance.find(s => s.hour === currentHour);\n  if (!currentSession || currentSession.totalTrades < 2) return 0;\n\n  // Bonus based on historical session performance\n  if (currentSession.winRate >= 80) return 15;\n  if (currentSession.winRate >= 70) return 10;\n  if (currentSession.winRate >= 60) return 5;\n  if (currentSession.winRate < 40) return -10;\n  return 0;\n};\n\n/**\n * Calculate quality bonus based on pattern score\n */\nconst calculateQualityBonus = (qualityScore: number): number => {\n  if (qualityScore >= 4.5) return 20;\n  if (qualityScore >= 4.0) return 15;\n  if (qualityScore >= 3.5) return 10;\n  if (qualityScore >= 3.0) return 5;\n  if (qualityScore < 2.0) return -15;\n  return 0;\n};\n\n/**\n * Calculate news impact on probability\n */\nconst calculateNewsImpact = (marketContext: MarketContext, model: ModelType): number => {\n  if (marketContext.newsImpact === 'LOW') return 0;\n  \n  // High impact news generally favors RD-Cont over FVG-RD due to volatility\n  if (marketContext.newsImpact === 'HIGH') {\n    return model === 'RD-Cont' ? 5 : -10;\n  }\n  \n  // Medium impact news\n  return model === 'RD-Cont' ? 2 : -5;\n};\n\n/**\n * Calculate volume bonus\n */\nconst calculateVolumeBonus = (marketContext: MarketContext): number => {\n  switch (marketContext.volumeProfile) {\n    case 'HIGH': return 5;\n    case 'LOW': return -5;\n    default: return 0;\n  }\n};\n\n/**\n * Calculate confluence bonus for multiple confirming factors\n */\nconst calculateConfluenceBonus = (\n  modelConfidence: 'LOW' | 'MEDIUM' | 'HIGH',\n  qualityRating: string,\n  sessionBonus: number\n): number => {\n  let confluenceFactors = 0;\n  \n  if (modelConfidence === 'HIGH') confluenceFactors++;\n  if (qualityRating === 'EXCELLENT' || qualityRating === 'EXCEPTIONAL') confluenceFactors++;\n  if (sessionBonus >= 10) confluenceFactors++;\n  \n  // Bonus for multiple confirming factors\n  if (confluenceFactors >= 3) return 10;\n  if (confluenceFactors >= 2) return 5;\n  return 0;\n};\n\n/**\n * Generate risk management recommendations\n */\nconst generateRiskManagement = (\n  probability: number,\n  confidence: 'LOW' | 'MEDIUM' | 'HIGH',\n  qualityScore: number\n): SuccessProbability['riskManagement'] => {\n  let positionSizing: SuccessProbability['riskManagement']['positionSizing'];\n  let maxRiskPercent: number;\n  let stopLossMultiplier: number;\n  let takeProfitTargets: number[];\n\n  if (probability >= 75 && confidence === 'HIGH' && qualityScore >= 4.0) {\n    positionSizing = 'AGGRESSIVE';\n    maxRiskPercent = 2.5;\n    stopLossMultiplier = 1.0;\n    takeProfitTargets = [1.5, 2.5, 4.0];\n  } else if (probability >= 65 && confidence !== 'LOW') {\n    positionSizing = 'STANDARD';\n    maxRiskPercent = 2.0;\n    stopLossMultiplier = 1.0;\n    takeProfitTargets = [1.5, 2.0, 3.0];\n  } else {\n    positionSizing = 'CONSERVATIVE';\n    maxRiskPercent = 1.0;\n    stopLossMultiplier = 0.8;\n    takeProfitTargets = [1.0, 1.5, 2.0];\n  }\n\n  return {\n    positionSizing,\n    maxRiskPercent,\n    stopLossMultiplier,\n    takeProfitTargets\n  };\n};\n\n/**\n * Success Probability Calculator Hook\n */\nexport const useSuccessProbabilityCalculator = (\n  modelRecommendation: ModelRecommendation,\n  patternQuality: PatternQualityScore\n) => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for probability calculation:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate success probability\n  const successProbability: SuccessProbability = useMemo(() => {\n    if (trades.length < 5) {\n      return {\n        finalProbability: 50,\n        confidence: 'LOW',\n        recommendation: 'STANDARD',\n        expectedRMultiple: { min: 1.0, max: 2.0, average: 1.5 },\n        breakdown: {\n          baseModelWinRate: 50,\n          sessionBonus: 0,\n          qualityBonus: 0,\n          newsImpact: 0,\n          volumeBonus: 0,\n          confluenceBonus: 0\n        },\n        riskManagement: {\n          positionSizing: 'CONSERVATIVE',\n          maxRiskPercent: 1.0,\n          stopLossMultiplier: 1.0,\n          takeProfitTargets: [1.0, 1.5, 2.0]\n        }\n      };\n    }\n\n    const marketContext = getCurrentMarketContext();\n    const currentHour = new Date().getHours();\n\n    // Calculate base model win rate\n    const modelTrades = trades.filter(t => t.trade.model_type === modelRecommendation.recommendedModel);\n    const baseModelWinRate = modelTrades.length > 0 ? \n      (modelTrades.filter(t => t.trade.win_loss === 'Win').length / modelTrades.length) * 100 : \n      modelRecommendation.probability;\n\n    // Calculate session performance\n    const sessionPerformance = Array.from({ length: 24 }, (_, hour) => {\n      const hourTrades = trades.filter(t => {\n        const entryTime = t.trade.entry_time;\n        if (!entryTime) return false;\n        const tradeHour = parseInt(entryTime.split(':')[0]);\n        return tradeHour === hour;\n      });\n      \n      return {\n        hour,\n        winRate: hourTrades.length > 0 ? \n          (hourTrades.filter(t => t.trade.win_loss === 'Win').length / hourTrades.length) * 100 : 0,\n        totalTrades: hourTrades.length\n      };\n    });\n\n    // Calculate all bonuses and impacts\n    const sessionBonus = calculateSessionBonus(currentHour, sessionPerformance);\n    const qualityBonus = calculateQualityBonus(patternQuality.totalScore);\n    const newsImpact = calculateNewsImpact(marketContext, modelRecommendation.recommendedModel);\n    const volumeBonus = calculateVolumeBonus(marketContext);\n    const confluenceBonus = calculateConfluenceBonus(\n      modelRecommendation.confidence,\n      patternQuality.rating,\n      sessionBonus\n    );\n\n    // Calculate final probability\n    const finalProbability = Math.min(Math.max(\n      baseModelWinRate + sessionBonus + qualityBonus + newsImpact + volumeBonus + confluenceBonus,\n      20\n    ), 90);\n\n    // Determine confidence level\n    let confidence: 'LOW' | 'MEDIUM' | 'HIGH';\n    const totalBonus = Math.abs(sessionBonus + qualityBonus + confluenceBonus);\n    if (totalBonus >= 25 && modelRecommendation.confidence === 'HIGH') confidence = 'HIGH';\n    else if (totalBonus >= 15) confidence = 'MEDIUM';\n    else confidence = 'LOW';\n\n    // Generate recommendation\n    let recommendation: SuccessProbability['recommendation'];\n    if (finalProbability >= 80 && confidence === 'HIGH') recommendation = 'PRIORITIZE';\n    else if (finalProbability >= 70) recommendation = 'INCREASE_SIZE';\n    else if (finalProbability >= 60) recommendation = 'STANDARD';\n    else if (finalProbability >= 45) recommendation = 'REDUCE_SIZE';\n    else recommendation = 'AVOID';\n\n    // Calculate expected R-multiple range\n    const modelRMultiples = modelTrades\n      .map(t => t.trade.r_multiple)\n      .filter((r): r is number => r !== undefined && r !== null);\n    \n    const avgR = modelRMultiples.length > 0 ? \n      modelRMultiples.reduce((sum, r) => sum + r, 0) / modelRMultiples.length : 1.5;\n    \n    const expectedRMultiple = {\n      min: Math.max(avgR * 0.7, 0.5),\n      max: avgR * 1.5,\n      average: avgR\n    };\n\n    // Generate risk management\n    const riskManagement = generateRiskManagement(finalProbability, confidence, patternQuality.totalScore);\n\n    return {\n      finalProbability,\n      confidence,\n      recommendation,\n      expectedRMultiple,\n      breakdown: {\n        baseModelWinRate,\n        sessionBonus,\n        qualityBonus,\n        newsImpact,\n        volumeBonus,\n        confluenceBonus\n      },\n      riskManagement\n    };\n  }, [trades, modelRecommendation, patternQuality]);\n\n  return {\n    successProbability,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    }\n  };\n};\n", "/**\n * Detailed Analysis Panel Component\n *\n * Expandable sections showing comprehensive trading intelligence.\n * Organized into logical sections with progressive disclosure to prevent\n * information overload while providing access to detailed analysis.\n */\n\nimport React, { useState } from 'react';\nimport styled from 'styled-components';\nimport { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';\nimport { ModelRecommendation } from '../hooks/useModelSelectionEngine';\nimport { PatternQualityScore } from '../hooks/usePatternQualityScoring';\nimport { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';\n\nexport interface DetailedAnalysisPanelProps {\n  /** Model recommendation data */\n  modelRecommendation: ModelRecommendation;\n  /** Pattern quality analysis */\n  patternQuality: PatternQualityScore;\n  /** Success probability calculation */\n  successProbability: SuccessProbability;\n  /** Setup intelligence data */\n  setupIntelligence: SetupIntelligenceData;\n  /** Whether data is loading */\n  isLoading?: boolean;\n  /** Error state */\n  error?: string | null;\n}\n\n// Styled components\nconst PanelContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n`;\n\nconst ExpandableSection = styled.div`\n  background: var(--elite-card-bg);\n  border: 1px solid var(--elite-card-border);\n  border-radius: var(--spacing-xs);\n  overflow: hidden;\n  margin-bottom: var(--spacing-md);\n  box-shadow: var(--shadow-sm);\n`;\n\nconst SectionHeader = styled.button<{ $isExpanded: boolean }>`\n  width: 100%;\n  background: var(--surface-bg);\n  border: none;\n  padding: 16px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n\n  &:hover {\n    background: var(--hover-bg);\n    transform: translateY(-1px);\n  }\n\n  border-bottom: ${({ $isExpanded }) => ($isExpanded ? '1px solid var(--border-color)' : 'none')};\n`;\n\nconst SectionTitle = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary);\n`;\n\nconst SectionSummary = styled.div`\n  font-size: 12px;\n  color: var(--text-secondary);\n  margin-top: 2px;\n`;\n\nconst ExpandIcon = styled.span<{ $isExpanded: boolean }>`\n  font-size: 14px;\n  transition: transform 0.2s ease;\n  transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};\n`;\n\nconst SectionContent = styled.div<{ $isExpanded: boolean }>`\n  max-height: ${({ $isExpanded }) => ($isExpanded ? '1000px' : '0')};\n  overflow: hidden;\n  transition: max-height 0.3s ease;\n  padding: ${({ $isExpanded }) => ($isExpanded ? '20px' : '0 20px')};\n`;\n\nconst DetailGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 16px;\n`;\n\nconst DetailItem = styled.div`\n  background: var(--elite-section-bg);\n  border: 1px solid var(--elite-card-border);\n  border-radius: var(--spacing-xs);\n  padding: var(--spacing-md);\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: var(--primary-color);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n  }\n`;\n\nconst DetailLabel = styled.div`\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--text-secondary);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  margin-bottom: 6px;\n`;\n\nconst DetailValue = styled.div`\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin-bottom: 4px;\n`;\n\nconst DetailDescription = styled.div`\n  font-size: 12px;\n  color: var(--text-muted);\n  line-height: 1.4;\n`;\n\n/**\n * Detailed Analysis Panel Component\n */\nexport const DetailedAnalysisPanel: React.FC<DetailedAnalysisPanelProps> = ({\n  modelRecommendation,\n  patternQuality,\n  successProbability,\n  isLoading = false,\n  error = null,\n}) => {\n  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['model']));\n\n  const toggleSection = (sectionId: string) => {\n    const newExpanded = new Set(expandedSections);\n    if (newExpanded.has(sectionId)) {\n      newExpanded.delete(sectionId);\n    } else {\n      newExpanded.add(sectionId);\n    }\n    setExpandedSections(newExpanded);\n  };\n\n  if (isLoading) {\n    return (\n      <PanelContainer>\n        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--text-secondary)' }}>\n          Loading detailed analysis...\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  if (error) {\n    return (\n      <PanelContainer>\n        <div style={{ textAlign: 'center', padding: '40px', color: 'var(--error-text)' }}>\n          Error loading detailed analysis: {error}\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  const isExpanded = (sectionId: string) => expandedSections.has(sectionId);\n\n  return (\n    <PanelContainer>\n      {/* Model Selection Analysis */}\n      <ExpandableSection>\n        <SectionHeader $isExpanded={isExpanded('model')} onClick={() => toggleSection('model')}>\n          <div>\n            <SectionTitle>🎯 Model Selection Analysis</SectionTitle>\n            <SectionSummary>\n              {modelRecommendation.recommendedModel} • {modelRecommendation.probability.toFixed(0)}%\n              confidence\n            </SectionSummary>\n          </div>\n          <ExpandIcon $isExpanded={isExpanded('model')}>▼</ExpandIcon>\n        </SectionHeader>\n\n        <SectionContent $isExpanded={isExpanded('model')}>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>Recommended Model</DetailLabel>\n              <DetailValue>{modelRecommendation.recommendedModel}</DetailValue>\n              <DetailDescription>{modelRecommendation.confidence} confidence</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Alternative Model</DetailLabel>\n              <DetailValue>{modelRecommendation.alternativeModel}</DetailValue>\n              <DetailDescription>{modelRecommendation.alternativeCondition}</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Market Volatility</DetailLabel>\n              <DetailValue>\n                {modelRecommendation.marketConditions.volatility.toUpperCase()}\n              </DetailValue>\n              <DetailDescription>Current market volatility assessment</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>HTF Trend</DetailLabel>\n              <DetailValue>\n                {modelRecommendation.marketConditions.htfTrend.toUpperCase()}\n              </DetailValue>\n              <DetailDescription>Higher timeframe trend direction</DetailDescription>\n            </DetailItem>\n          </DetailGrid>\n\n          <DetailDescription\n            style={{ padding: '12px', background: 'var(--surface-bg)', borderRadius: '8px' }}\n          >\n            <strong>Reasoning:</strong> {modelRecommendation.reasoning}\n          </DetailDescription>\n        </SectionContent>\n      </ExpandableSection>\n\n      {/* Pattern Quality Analysis */}\n      <ExpandableSection>\n        <SectionHeader $isExpanded={isExpanded('pattern')} onClick={() => toggleSection('pattern')}>\n          <div>\n            <SectionTitle>📊 Pattern Quality Analysis</SectionTitle>\n            <SectionSummary>\n              {patternQuality.totalScore.toFixed(1)}/5.0 • {patternQuality.rating}\n            </SectionSummary>\n          </div>\n          <ExpandIcon $isExpanded={isExpanded('pattern')}>▼</ExpandIcon>\n        </SectionHeader>\n\n        <SectionContent $isExpanded={isExpanded('pattern')}>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>PD Array Confluence</DetailLabel>\n              <DetailValue>{patternQuality.breakdown.pdArrayConfluence.toFixed(1)}</DetailValue>\n              <DetailDescription>Multiple PD array alignment</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>FVG Characteristics</DetailLabel>\n              <DetailValue>{patternQuality.breakdown.fvgCharacteristics.toFixed(1)}</DetailValue>\n              <DetailDescription>Fair value gap quality</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>RD Strength</DetailLabel>\n              <DetailValue>{patternQuality.breakdown.rdStrength.toFixed(1)}</DetailValue>\n              <DetailDescription>Reaction/displacement power</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Confirmation Signals</DetailLabel>\n              <DetailValue>{patternQuality.breakdown.confirmationSignals.toFixed(1)}</DetailValue>\n              <DetailDescription>Supporting technical signals</DetailDescription>\n            </DetailItem>\n          </DetailGrid>\n\n          <DetailDescription\n            style={{ padding: '12px', background: 'var(--surface-bg)', borderRadius: '8px' }}\n          >\n            <strong>Recommendation:</strong> {patternQuality.recommendation}\n          </DetailDescription>\n        </SectionContent>\n      </ExpandableSection>\n\n      {/* Success Probability Analysis */}\n      <ExpandableSection>\n        <SectionHeader\n          $isExpanded={isExpanded('probability')}\n          onClick={() => toggleSection('probability')}\n        >\n          <div>\n            <SectionTitle>🎯 Success Probability Analysis</SectionTitle>\n            <SectionSummary>\n              {successProbability.finalProbability.toFixed(0)}% •{' '}\n              {successProbability.recommendation.replace('_', ' ')}\n            </SectionSummary>\n          </div>\n          <ExpandIcon $isExpanded={isExpanded('probability')}>▼</ExpandIcon>\n        </SectionHeader>\n\n        <SectionContent $isExpanded={isExpanded('probability')}>\n          <DetailGrid>\n            <DetailItem>\n              <DetailLabel>Base Model Win Rate</DetailLabel>\n              <DetailValue>{successProbability.breakdown.baseModelWinRate.toFixed(0)}%</DetailValue>\n              <DetailDescription>Historical model performance</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Session Bonus</DetailLabel>\n              <DetailValue>\n                {successProbability.breakdown.sessionBonus > 0 ? '+' : ''}\n                {successProbability.breakdown.sessionBonus.toFixed(0)}%\n              </DetailValue>\n              <DetailDescription>Current session performance boost</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Quality Bonus</DetailLabel>\n              <DetailValue>\n                {successProbability.breakdown.qualityBonus > 0 ? '+' : ''}\n                {successProbability.breakdown.qualityBonus.toFixed(0)}%\n              </DetailValue>\n              <DetailDescription>Pattern quality enhancement</DetailDescription>\n            </DetailItem>\n\n            <DetailItem>\n              <DetailLabel>Expected R-Multiple</DetailLabel>\n              <DetailValue>\n                {successProbability.expectedRMultiple.min.toFixed(1)} -{' '}\n                {successProbability.expectedRMultiple.max.toFixed(1)}\n              </DetailValue>\n              <DetailDescription>\n                Risk-reward range (avg: {successProbability.expectedRMultiple.average.toFixed(1)})\n              </DetailDescription>\n            </DetailItem>\n          </DetailGrid>\n        </SectionContent>\n      </ExpandableSection>\n    </PanelContainer>\n  );\n};\n\nexport default DetailedAnalysisPanel;\n", "/**\n * Market State Indicator Component\n *\n * Displays current market status and provides context for data freshness.\n * Addresses the weekend/off-hours 0.2/5.0 pattern quality issue by showing\n * when markets are closed and data may be stale.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport interface MarketState {\n  isOpen: boolean;\n  status: 'OPEN' | 'CLOSED' | 'PRE_MARKET' | 'AFTER_HOURS';\n  nextOpen?: string;\n  timeZone: string;\n  dayOfWeek: string;\n}\n\nexport interface MarketStateIndicatorProps {\n  /** Current market state */\n  marketState: MarketState;\n  /** Whether to show detailed information */\n  showDetails?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\n// Styled components\nconst StateContainer = styled.div<{ $isOpen: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  border-radius: 6px;\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  \n  background: ${({ $isOpen }) => \n    $isOpen \n      ? 'var(--success-bg, rgba(34, 197, 94, 0.1))' \n      : 'var(--warning-bg, rgba(251, 191, 36, 0.1))'\n  };\n  \n  border: 1px solid ${({ $isOpen }) => \n    $isOpen \n      ? 'var(--success-border, rgba(34, 197, 94, 0.3))' \n      : 'var(--warning-border, rgba(251, 191, 36, 0.3))'\n  };\n  \n  color: ${({ $isOpen }) => \n    $isOpen \n      ? 'var(--success-text, #22c55e)' \n      : 'var(--warning-text, #fbbf24)'\n  };\n`;\n\nconst StatusDot = styled.div<{ $isOpen: boolean }>`\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ${({ $isOpen }) => \n    $isOpen \n      ? 'var(--success-text, #22c55e)' \n      : 'var(--warning-text, #fbbf24)'\n  };\n  \n  ${({ $isOpen }) => $isOpen && `\n    animation: pulse 2s infinite;\n    \n    @keyframes pulse {\n      0%, 100% { opacity: 1; }\n      50% { opacity: 0.5; }\n    }\n  `}\n`;\n\nconst StatusText = styled.span`\n  font-size: 12px;\n  font-weight: 600;\n`;\n\nconst DetailText = styled.div`\n  font-size: 11px;\n  font-weight: 400;\n  color: var(--text-secondary);\n  margin-top: 4px;\n  text-transform: none;\n  letter-spacing: normal;\n`;\n\n/**\n * Get current market state based on NY time\n */\nexport const getCurrentMarketState = (): MarketState => {\n  const now = new Date();\n  \n  // Convert to NY time (EST/EDT)\n  const nyTime = new Date(now.toLocaleString(\"en-US\", {timeZone: \"America/New_York\"}));\n  const hour = nyTime.getHours();\n  const minutes = nyTime.getMinutes();\n  const dayOfWeek = nyTime.getDay(); // 0 = Sunday, 6 = Saturday\n  \n  const timeInMinutes = hour * 60 + minutes;\n  const marketOpen = 9 * 60 + 30; // 9:30 AM\n  const marketClose = 16 * 60; // 4:00 PM\n  \n  // Weekend check\n  if (dayOfWeek === 0 || dayOfWeek === 6) {\n    const nextMonday = new Date(nyTime);\n    nextMonday.setDate(nyTime.getDate() + (dayOfWeek === 0 ? 1 : 2)); // Next Monday\n    nextMonday.setHours(9, 30, 0, 0);\n    \n    return {\n      isOpen: false,\n      status: 'CLOSED',\n      nextOpen: nextMonday.toLocaleString('en-US', {\n        weekday: 'long',\n        hour: 'numeric',\n        minute: '2-digit',\n        timeZone: 'America/New_York'\n      }),\n      timeZone: 'ET',\n      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]\n    };\n  }\n  \n  // Weekday market hours\n  if (timeInMinutes >= marketOpen && timeInMinutes < marketClose) {\n    return {\n      isOpen: true,\n      status: 'OPEN',\n      timeZone: 'ET',\n      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]\n    };\n  } else if (timeInMinutes < marketOpen) {\n    const openTime = new Date(nyTime);\n    openTime.setHours(9, 30, 0, 0);\n    \n    return {\n      isOpen: false,\n      status: 'PRE_MARKET',\n      nextOpen: openTime.toLocaleTimeString('en-US', {\n        hour: 'numeric',\n        minute: '2-digit',\n        timeZone: 'America/New_York'\n      }),\n      timeZone: 'ET',\n      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]\n    };\n  } else {\n    // After hours - next open is tomorrow 9:30 AM (or Monday if Friday)\n    const nextOpen = new Date(nyTime);\n    if (dayOfWeek === 5) { // Friday\n      nextOpen.setDate(nyTime.getDate() + 3); // Monday\n    } else {\n      nextOpen.setDate(nyTime.getDate() + 1); // Tomorrow\n    }\n    nextOpen.setHours(9, 30, 0, 0);\n    \n    return {\n      isOpen: false,\n      status: 'AFTER_HOURS',\n      nextOpen: nextOpen.toLocaleString('en-US', {\n        weekday: dayOfWeek === 5 ? 'long' : undefined,\n        hour: 'numeric',\n        minute: '2-digit',\n        timeZone: 'America/New_York'\n      }),\n      timeZone: 'ET',\n      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][dayOfWeek]\n    };\n  }\n};\n\n/**\n * Market State Indicator Component\n */\nexport const MarketStateIndicator: React.FC<MarketStateIndicatorProps> = ({\n  marketState,\n  showDetails = false,\n  className\n}) => {\n  const getStatusText = () => {\n    switch (marketState.status) {\n      case 'OPEN':\n        return 'Markets Open';\n      case 'PRE_MARKET':\n        return 'Pre-Market';\n      case 'AFTER_HOURS':\n        return 'After Hours';\n      case 'CLOSED':\n        return `Markets Closed - ${marketState.dayOfWeek}`;\n      default:\n        return 'Unknown';\n    }\n  };\n\n  const getDetailText = () => {\n    if (!showDetails) return null;\n    \n    if (marketState.isOpen) {\n      return 'Live market data • Real-time analysis';\n    } else if (marketState.nextOpen) {\n      return `Next open: ${marketState.nextOpen} ${marketState.timeZone} • Using historical data`;\n    } else {\n      return 'Using historical data for analysis';\n    }\n  };\n\n  return (\n    <StateContainer $isOpen={marketState.isOpen} className={className}>\n      <StatusDot $isOpen={marketState.isOpen} />\n      <div>\n        <StatusText>{getStatusText()}</StatusText>\n        {showDetails && getDetailText() && (\n          <DetailText>{getDetailText()}</DetailText>\n        )}\n      </div>\n    </StateContainer>\n  );\n};\n\nexport default MarketStateIndicator;\n", "/**\n * Quick Decision Panel Component\n *\n * ADHD-optimized summary view showing only the essential information\n * needed for immediate trading decisions. Eliminates information overload\n * by focusing on actionable insights.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { SetupIntelligenceData } from '../hooks/useEnhancedSetupIntelligence';\nimport { ModelRecommendation } from '../hooks/useModelSelectionEngine';\nimport { PatternQualityScore } from '../hooks/usePatternQualityScoring';\nimport { SuccessProbability } from '../hooks/useSuccessProbabilityCalculator';\nimport { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';\n\nexport interface QuickDecisionPanelProps {\n  /** Model recommendation data */\n  modelRecommendation: ModelRecommendation;\n  /** Pattern quality analysis */\n  patternQuality: PatternQualityScore;\n  /** Success probability calculation */\n  successProbability: SuccessProbability;\n  /** Setup intelligence data */\n  setupIntelligence: SetupIntelligenceData;\n  /** Whether data is loading */\n  isLoading?: boolean;\n  /** Error state */\n  error?: string | null;\n  /** Callback to expand to detailed view */\n  onExpandDetails?: () => void;\n}\n\n// Neurodiversity-Safe F1 Command Center Components\nconst PanelContainer = styled.div`\n  background: var(--bg-secondary);\n  border: 2px solid var(--elite-card-border);\n  border-radius: 8px; /* Gentle corners, F1 angles when racing effects enabled */\n  padding: var(--spacing-lg);\n  margin-bottom: var(--spacing-lg);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n\n  /* F1 Aesthetic - User Controllable */\n  border-radius: calc(8px + (12px * var(--racing-effects, 0)));\n  clip-path: polygon(0 0, calc(100% - calc(10px * var(--racing-effects, 0))) 0, 100% 100%, 0 100%);\n\n  /* Accessibility-First Top Accent */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 2px;\n    background: var(--primary-color);\n    opacity: calc(0.6 + (0.4 * var(--racing-effects, 0)));\n    animation: racing-stripe var(--animation-speed) linear infinite;\n  }\n\n  /* Optional F1 Flag - Hidden in Low Stimulation */\n  &::after {\n    content: '🏁';\n    position: absolute;\n    top: 15px;\n    right: 20px;\n    font-size: 20px;\n    opacity: calc(0.2 * var(--racing-effects, 0));\n    animation: subtle-glow var(--animation-speed) ease-in-out infinite;\n  }\n\n  @keyframes racing-stripe {\n    0% {\n      transform: translateX(-100%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  }\n\n  @keyframes subtle-glow {\n    0%,\n    100% {\n      opacity: calc(0.2 * var(--racing-effects, 0));\n    }\n    50% {\n      opacity: calc(0.4 * var(--racing-effects, 0));\n    }\n  }\n`;\n\nconst PanelHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: var(--spacing-lg);\n  padding-bottom: var(--spacing-sm);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst Title = styled.h3`\n  margin: 0;\n  font-family: var(--font-primary); /* Orbitron for headers only */\n  font-size: var(--font-size-2xl);\n  font-weight: 700;\n  color: var(--text-primary);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  position: relative;\n\n  /* Accessibility-First Styling */\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n\n  /* Optional F1 Gradient - User Controllable */\n  background: linear-gradient(\n    135deg,\n    var(--text-primary) 0%,\n    var(--primary-color) 50%,\n    var(--text-primary) 100%\n  );\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n\n  /* Optional Racing Accent */\n  &::before {\n    content: '';\n    position: absolute;\n    left: -10px;\n    top: 50%;\n    transform: translateY(-50%);\n    width: calc(2px + (2px * var(--racing-effects, 0)));\n    height: 100%;\n    background: var(--primary-color);\n    border-radius: 2px;\n    opacity: calc(0.6 + (0.4 * var(--racing-effects, 0)));\n  }\n`;\n\nconst QuickGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-md);\n  align-items: stretch;\n\n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n`;\n\nconst MarketContextBanner = styled.div<{ $isWeekend: boolean }>`\n  background: ${({ $isWeekend }) =>\n    $isWeekend\n      ? 'linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%)'\n      : 'linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%)'};\n  border: 2px solid\n    ${({ $isWeekend }) => ($isWeekend ? 'var(--warning-color)' : 'var(--info-color)')};\n  border-radius: var(--spacing-xs);\n  padding: var(--spacing-md) var(--spacing-lg);\n  margin-bottom: var(--spacing-md);\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n  color: var(--text-primary);\n  text-align: center;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n`;\n\nconst PrimaryRecommendation = styled.div`\n  text-align: center;\n  padding: var(--spacing-lg);\n  background: linear-gradient(135deg, var(--elite-section-bg) 0%, rgba(42, 42, 42, 0.8) 100%);\n  border-radius: 0 15px 0 15px; /* F1 aerodynamic styling */\n  border: 2px solid var(--primary-color);\n  position: relative;\n  overflow: hidden;\n  transform-style: preserve-3d;\n  transition: all var(--transition-normal);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(90deg, transparent 0%, var(--primary-color) 50%, transparent 100%);\n    animation: victory-pulse 2s ease-in-out infinite;\n  }\n\n  &::after {\n    content: '#1';\n    position: absolute;\n    top: 8px;\n    right: 12px;\n    font-family: var(--racing-font);\n    font-size: 48px;\n    font-weight: 900;\n    color: var(--accent-color);\n    opacity: 0.15;\n    line-height: 1;\n  }\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);\n    border-color: var(--accent-color);\n  }\n\n  @keyframes victory-pulse {\n    0%,\n    100% {\n      opacity: 0.6;\n    }\n    50% {\n      opacity: 1;\n    }\n  }\n`;\n\nconst ModelName = styled.div`\n  font-family: var(--font-primary); /* Orbitron for pattern names */\n  font-size: clamp(1.5rem, 4vw, var(--font-size-3xl));\n  font-weight: 700;\n  color: var(--text-primary);\n  margin-bottom: var(--spacing-xs);\n  line-height: 1.2;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  position: relative;\n  z-index: 2;\n\n  /* Accessibility-First Styling */\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);\n\n  /* Optional F1 Glow - User Controllable */\n  filter: drop-shadow(0 0 calc(10px * var(--glow-opacity, 0)) var(--primary-color));\n`;\n\nconst ConfidenceBadge = styled.span<{ $confidence: string }>`\n  background: var(--accent-color);\n  color: var(--text-primary);\n  padding: var(--spacing-xxs) var(--spacing-sm);\n  border-radius: 20px;\n  font-weight: 700;\n  font-size: var(--font-size-xs);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);\n`;\n\nconst SetupRecommendation = styled.div`\n  padding: var(--spacing-lg);\n  background: linear-gradient(\n    135deg,\n    rgba(0, 255, 228, 0.08) 0%,\n    var(--elite-section-bg) 50%,\n    rgba(0, 128, 255, 0.05) 100%\n  );\n  border-radius: var(--spacing-xs);\n  border: 1px solid var(--elite-card-border);\n  border-left: 4px solid var(--primary-color);\n  position: relative;\n  clip-path: polygon(0 0, calc(100% - 15px) 0, 100% 100%, 0 100%);\n  backdrop-filter: blur(5px);\n  transition: all var(--transition-normal);\n\n  &::before {\n    content: '◢';\n    position: absolute;\n    top: 8px;\n    right: 12px;\n    color: var(--primary-color);\n    font-size: 18px;\n    opacity: 0.6;\n  }\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 1px;\n    background: linear-gradient(90deg, transparent 0%, var(--primary-color) 50%, transparent 100%);\n  }\n\n  &:hover {\n    transform: translateX(2px);\n    border-left-color: var(--accent-color);\n    box-shadow: -4px 0 20px rgba(0, 255, 228, 0.2);\n  }\n`;\n\nconst SetupTitle = styled.div`\n  font-size: var(--font-size-xs);\n  color: var(--accent-color);\n  margin-bottom: var(--spacing-xs);\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\nconst SetupDescription = styled.div`\n  font-size: var(--font-size-md);\n  line-height: 1.4;\n  color: var(--text-primary);\n  margin-bottom: var(--spacing-xs);\n  font-weight: 600;\n`;\n\nconst SetupStats = styled.div`\n  font-size: var(--font-size-xs);\n  color: var(--accent-color);\n  font-weight: 600;\n  letter-spacing: 0.5px;\n`;\n\nconst ProbabilitySection = styled.div`\n  text-align: center;\n  padding: var(--spacing-lg);\n  background: radial-gradient(\n    circle at center,\n    var(--elite-section-bg) 0%,\n    rgba(42, 42, 42, 0.9) 100%\n  );\n  border-radius: 50% 50% 0 0; /* Speedometer shape */\n  border: 2px solid var(--elite-card-border);\n  border-bottom: 4px solid var(--primary-color);\n  position: relative;\n  overflow: hidden;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 80px;\n    height: 80px;\n    border: 3px solid var(--primary-color);\n    border-radius: 50%;\n    opacity: 0.3;\n  }\n\n  &::after {\n    content: '⚡';\n    position: absolute;\n    top: 10px;\n    right: 15px;\n    font-size: 20px;\n    opacity: 0.6;\n    animation: electric-pulse 1.5s ease-in-out infinite;\n  }\n\n  @keyframes electric-pulse {\n    0%,\n    100% {\n      opacity: 0.6;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 1;\n      transform: scale(1.1);\n    }\n  }\n`;\n\nconst ProbabilityValue = styled.div<{ $probability: number }>`\n  font-family: var(--font-data); /* JetBrains Mono for data */\n  font-size: clamp(2rem, 5vw, var(--font-size-4xl));\n  font-weight: 700;\n  line-height: 1;\n  margin-bottom: var(--spacing-xxs);\n  position: relative;\n  z-index: 2;\n\n  /* Accessibility-First Color Coding */\n  color: ${({ $probability }) => {\n    if ($probability >= 70) return 'var(--success-color)';\n    if ($probability >= 50) return 'var(--warning-color)';\n    return 'var(--error-color)';\n  }};\n\n  /* Accessibility-First Text Shadow */\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);\n\n  /* Optional F1 Glow - User Controllable */\n  filter: drop-shadow(0 0 calc(15px * var(--glow-opacity, 0)) currentColor);\n\n  /* Optional Subtle Animation - User Controllable */\n  &::before {\n    content: '';\n    position: absolute;\n    inset: -8px;\n    background: radial-gradient(\n      circle,\n      ${({ $probability }) => {\n          if ($probability >= 70) return 'rgba(16, 185, 129, 0.1)';\n          if ($probability >= 50) return 'rgba(255, 165, 0, 0.1)';\n          return 'rgba(255, 107, 107, 0.1)';\n        }}\n        0%,\n      transparent 70%\n    );\n    border-radius: 50%;\n    z-index: -1;\n    opacity: var(--glow-opacity, 0);\n    animation: probability-glow var(--animation-speed) ease-in-out infinite;\n  }\n\n  @keyframes probability-glow {\n    0%,\n    100% {\n      opacity: calc(0.4 * var(--glow-opacity, 0));\n      transform: scale(1);\n    }\n    50% {\n      opacity: calc(0.8 * var(--glow-opacity, 0));\n      transform: scale(1.02);\n    }\n  }\n`;\n\nconst ProbabilityLabel = styled.div`\n  font-size: var(--font-size-xs);\n  color: var(--text-secondary);\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n`;\n\nconst QualityAlert = styled.div<{ $show: boolean }>`\n  display: ${({ $show }) => ($show ? 'block' : 'none')};\n  background: linear-gradient(\n    135deg,\n    var(--error-color) 0%,\n    rgba(225, 6, 0, 0.8) 50%,\n    var(--error-color) 100%\n  );\n  color: var(--text-primary);\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-radius: 0 10px 0 10px; /* F1 warning panel shape */\n  margin: var(--spacing-md) 0;\n  text-align: center;\n  font-family: var(--racing-font);\n  font-weight: 900;\n  font-size: var(--font-size-sm);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  border: 3px solid var(--error-color);\n  box-shadow: 0 8px 32px rgba(225, 6, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);\n  position: relative;\n  overflow: hidden;\n  animation: urgent-flash 1.5s ease-in-out infinite;\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: -100%;\n    width: 100%;\n    height: 100%;\n    background: linear-gradient(\n      90deg,\n      transparent 0%,\n      rgba(255, 255, 255, 0.3) 50%,\n      transparent 100%\n    );\n    animation: warning-sweep 2s linear infinite;\n  }\n\n  &::after {\n    content: '⚠️';\n    position: absolute;\n    top: 50%;\n    left: 15px;\n    transform: translateY(-50%);\n    font-size: 20px;\n    animation: warning-blink 1s ease-in-out infinite;\n  }\n\n  @keyframes urgent-flash {\n    0%,\n    100% {\n      opacity: 1;\n      transform: scale(1);\n      box-shadow: 0 8px 32px rgba(225, 6, 0, 0.4);\n    }\n    50% {\n      opacity: 0.9;\n      transform: scale(1.02);\n      box-shadow: 0 12px 40px rgba(225, 6, 0, 0.6);\n    }\n  }\n\n  @keyframes warning-sweep {\n    0% {\n      left: -100%;\n    }\n    100% {\n      left: 100%;\n    }\n  }\n\n  @keyframes warning-blink {\n    0%,\n    100% {\n      opacity: 1;\n    }\n    50% {\n      opacity: 0.5;\n    }\n  }\n`;\n\nconst ReasoningBox = styled.div`\n  background: linear-gradient(135deg, var(--elite-glass-bg) 0%, rgba(42, 42, 42, 0.4) 100%);\n  border: 1px solid var(--elite-glass-border);\n  border-radius: var(--spacing-xs);\n  padding: var(--spacing-lg);\n  margin-top: var(--spacing-md);\n  position: relative;\n  backdrop-filter: blur(10px);\n  transition: all var(--transition-normal);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg,\n      var(--primary-color) 0%,\n      var(--accent-color) 50%,\n      var(--primary-color) 100%\n    );\n    animation: data-flow 4s linear infinite;\n  }\n\n  &::after {\n    content: '💡';\n    position: absolute;\n    top: 15px;\n    right: 15px;\n    font-size: 16px;\n    opacity: 0.7;\n    animation: insight-glow 3s ease-in-out infinite;\n  }\n\n  &:hover {\n    border-color: var(--primary-color);\n    box-shadow: 0 8px 32px rgba(0, 255, 228, 0.1);\n  }\n\n  @keyframes data-flow {\n    0% {\n      transform: translateX(-100%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  }\n\n  @keyframes insight-glow {\n    0%,\n    100% {\n      opacity: 0.7;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 1;\n      transform: scale(1.1);\n    }\n  }\n`;\n\nconst ReasoningTitle = styled.div`\n  color: var(--primary-color);\n  font-family: var(--racing-font);\n  font-weight: 700;\n  margin-bottom: var(--spacing-xs);\n  font-size: var(--font-size-sm);\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n\n  &::before {\n    content: '▶';\n    font-size: 12px;\n    opacity: 0.8;\n  }\n`;\n\n/**\n * Quick Decision Panel Component\n */\nexport const QuickDecisionPanel: React.FC<QuickDecisionPanelProps> = ({\n  modelRecommendation,\n  patternQuality,\n  successProbability,\n  setupIntelligence,\n  isLoading = false,\n  error = null,\n}) => {\n  const marketState = getCurrentMarketState();\n  const isWeekend = marketState.dayOfWeek === 'Saturday' || marketState.dayOfWeek === 'Sunday';\n  const showContextBanner = !marketState.isOpen;\n\n  if (isLoading) {\n    return (\n      <PanelContainer>\n        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--text-secondary)' }}>\n          Analyzing market conditions...\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  if (error) {\n    return (\n      <PanelContainer>\n        <div style={{ textAlign: 'center', padding: '20px', color: 'var(--error-text)' }}>\n          Error loading intelligence data\n        </div>\n      </PanelContainer>\n    );\n  }\n\n  return (\n    <PanelContainer\n      role='region'\n      aria-labelledby='elite-intelligence-title'\n      aria-describedby='market-context'\n    >\n      <PanelHeader>\n        <Title id='elite-intelligence-title'>🏁 Elite Intelligence Command Center</Title>\n        <MarketStateIndicator marketState={marketState} />\n      </PanelHeader>\n\n      {showContextBanner && (\n        <MarketContextBanner\n          $isWeekend={isWeekend}\n          id='market-context'\n          role='status'\n          aria-live='polite'\n        >\n          {isWeekend\n            ? '📅 Weekend Analysis - Using most recent trading session data for pattern evaluation'\n            : `⏰ ${marketState.status.replace('_', ' ')} - Analysis based on ${\n                marketState.status === 'PRE_MARKET' ? 'previous session' : 'current session'\n              } data`}\n        </MarketContextBanner>\n      )}\n\n      <QuickGrid role='grid' aria-label='Trading intelligence summary'>\n        <PrimaryRecommendation\n          role='gridcell'\n          aria-labelledby='model-recommendation-title'\n          aria-describedby='model-confidence'\n        >\n          <ModelName\n            id='model-recommendation-title'\n            aria-label={`Recommended trading model: ${modelRecommendation.recommendedModel}`}\n          >\n            {modelRecommendation.recommendedModel}\n          </ModelName>\n          <ConfidenceBadge\n            $confidence={modelRecommendation.confidence}\n            id='model-confidence'\n            aria-label={`Confidence level: ${modelRecommendation.probability.toFixed(0)} percent ${\n              modelRecommendation.confidence\n            }`}\n          >\n            {modelRecommendation.probability.toFixed(0)}% {modelRecommendation.confidence}\n          </ConfidenceBadge>\n        </PrimaryRecommendation>\n\n        <SetupRecommendation\n          role='gridcell'\n          aria-labelledby='setup-title'\n          aria-describedby='setup-stats'\n        >\n          <SetupTitle id='setup-title'>Recommended Setup</SetupTitle>\n          <SetupDescription\n            aria-label={`Trading setup: ${setupIntelligence.currentRecommendations.primarySetup} plus ${setupIntelligence.currentRecommendations.secondarySetup}`}\n          >\n            {setupIntelligence.currentRecommendations.primarySetup} +{' '}\n            {setupIntelligence.currentRecommendations.secondarySetup}\n          </SetupDescription>\n          <SetupStats\n            id='setup-stats'\n            aria-label={`Expected performance: ${setupIntelligence.currentRecommendations.expectedWinRate.toFixed(\n              0\n            )} percent win rate, ${setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(\n              1\n            )} R average return`}\n          >\n            {setupIntelligence.currentRecommendations.expectedWinRate.toFixed(0)}% Win Rate |{' '}\n            {setupIntelligence.currentRecommendations.expectedRMultiple.toFixed(1)}R Avg\n          </SetupStats>\n        </SetupRecommendation>\n\n        <ProbabilitySection\n          role='gridcell'\n          aria-labelledby='probability-label'\n          aria-describedby='probability-value'\n        >\n          <ProbabilityValue\n            $probability={successProbability.finalProbability}\n            id='probability-value'\n            aria-label={`Success probability: ${successProbability.finalProbability.toFixed(\n              0\n            )} percent`}\n          >\n            {successProbability.finalProbability.toFixed(0)}%\n          </ProbabilityValue>\n          <ProbabilityLabel id='probability-label'>Success Probability</ProbabilityLabel>\n        </ProbabilitySection>\n      </QuickGrid>\n\n      <QualityAlert\n        $show={patternQuality.totalScore < 2.0}\n        role='alert'\n        aria-live='assertive'\n        aria-label={`Pattern quality warning: ${\n          patternQuality.rating\n        } quality with score ${patternQuality.totalScore.toFixed(1)} out of 5. Recommendation: ${\n          patternQuality.recommendation\n        }`}\n      >\n        ⚠️ Current Pattern Quality: {patternQuality.rating} ({patternQuality.totalScore.toFixed(1)}\n        /5.0) - {patternQuality.recommendation}\n      </QualityAlert>\n\n      <ReasoningBox\n        role='region'\n        aria-labelledby='reasoning-title'\n        aria-describedby='reasoning-content'\n      >\n        <ReasoningTitle id='reasoning-title'>\n          Why {modelRecommendation.recommendedModel}?\n        </ReasoningTitle>\n        <div\n          id='reasoning-content'\n          style={{\n            fontFamily: 'var(--font-body)' /* Inter for body text */,\n            fontSize: 'var(--font-size-sm)',\n            lineHeight: '1.5',\n            color: 'var(--text-secondary)',\n            fontWeight: '500',\n          }}\n          aria-label={`Model reasoning: ${modelRecommendation.reasoning}`}\n        >\n          {modelRecommendation.reasoning}\n        </div>\n      </ReasoningBox>\n    </PanelContainer>\n  );\n};\n\nexport default QuickDecisionPanel;\n", "/**\n * Elite Intelligence Layout Component\n *\n * Progressive disclosure layout that addresses information overload by providing\n * quick decision-making with expandable detailed analysis. Implements ADHD-optimized\n * design patterns with expansion toggle and state-aware displays.\n */\n\nimport { Card } from '@adhd-trading-dashboard/shared';\nimport React, { useEffect, useState } from 'react';\nimport styled from 'styled-components';\nimport { useEnhancedSetupIntelligence } from '../hooks/useEnhancedSetupIntelligence';\nimport { useGranularSessionIntelligence } from '../hooks/useGranularSessionIntelligence';\nimport { useModelSelectionEngine } from '../hooks/useModelSelectionEngine';\nimport { usePatternQualityScoring } from '../hooks/usePatternQualityScoring';\nimport { useSuccessProbabilityCalculator } from '../hooks/useSuccessProbabilityCalculator';\nimport { DetailedAnalysisPanel } from './DetailedAnalysisPanel';\nimport { MarketStateIndicator, getCurrentMarketState } from './MarketStateIndicator';\nimport { QuickDecisionPanel } from './QuickDecisionPanel';\n\nexport interface EliteIntelligenceLayoutProps {\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when the refresh button is clicked */\n  onRefresh?: () => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components\nconst LayoutContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n`;\n\nconst HeaderRight = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 12px;\n\n  @media (max-width: 768px) {\n    justify-content: space-between;\n  }\n`;\n\nconst RefreshButton = styled.button`\n  background: none;\n  border: 1px solid var(--border-color);\n  border-radius: 6px;\n  padding: 8px 12px;\n  font-size: 12px;\n  color: var(--text-secondary);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n\n  &:hover {\n    background: var(--hover-bg);\n    color: var(--text-primary);\n    border-color: var(--primary-color);\n  }\n\n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\nconst ContentContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n`;\n\nconst ExpandedContent = styled.div<{ $isExpanded: boolean }>`\n  overflow: hidden;\n  transition: all 0.3s ease-in-out;\n  opacity: ${({ $isExpanded }) => ($isExpanded ? 1 : 0)};\n  max-height: ${({ $isExpanded }) => ($isExpanded ? '2000px' : '0')};\n  margin-top: ${({ $isExpanded }) => ($isExpanded ? 'var(--spacing-md)' : '0')};\n`;\n\nconst ExpandToggleButton = styled.button<{ $isExpanded: boolean }>`\n  background: var(--elite-section-bg);\n  color: var(--text-primary);\n  border: 1px solid var(--primary-color);\n  border-radius: var(--spacing-xs);\n  padding: var(--spacing-xs) var(--spacing-md);\n  font-size: var(--font-size-sm);\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  box-shadow: var(--shadow-sm);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n  letter-spacing: 0.5px;\n  text-transform: uppercase;\n\n  &:hover:not(:disabled) {\n    background: var(--elite-card-bg);\n    transform: translateY(-2px);\n    box-shadow: var(--shadow-md);\n    border-color: var(--accent-color);\n  }\n\n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n    transform: none;\n  }\n\n  &:active {\n    transform: translateY(0);\n  }\n\n  .expand-icon {\n    transition: transform 0.3s ease;\n    transform: ${({ $isExpanded }) => ($isExpanded ? 'rotate(180deg)' : 'rotate(0deg)')};\n    font-size: var(--font-size-xs);\n  }\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  color: var(--text-secondary);\n  text-align: center;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  color: var(--error-text);\n  text-align: center;\n`;\n\nconst LoadingSpinner = styled.div`\n  width: 32px;\n  height: 32px;\n  border: 3px solid var(--border-color);\n  border-top: 3px solid var(--primary-color);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 16px;\n\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n`;\n\n/**\n * Elite Intelligence Layout Component\n */\nexport const EliteIntelligenceLayout: React.FC<EliteIntelligenceLayoutProps> = ({\n  isLoading = false,\n  error = null,\n  onRefresh,\n  className,\n}) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const marketState = getCurrentMarketState();\n\n  // Hook data\n  const {\n    recommendation: modelRec,\n    isLoading: modelLoading,\n    error: modelError,\n  } = useModelSelectionEngine();\n\n  const {\n    analysis: qualityAnalysis,\n    isLoading: qualityLoading,\n    error: qualityError,\n  } = usePatternQualityScoring();\n\n  const { isLoading: sessionLoading, error: sessionError } = useGranularSessionIntelligence();\n\n  const { successProbability, isLoading: probabilityLoading } = useSuccessProbabilityCalculator(\n    modelRec,\n    qualityAnalysis.currentScore\n  );\n\n  const {\n    setupIntelligence,\n    isLoading: setupLoading,\n    error: setupError,\n  } = useEnhancedSetupIntelligence();\n\n  // Aggregate loading and error states\n  const aggregatedLoading =\n    isLoading ||\n    modelLoading ||\n    qualityLoading ||\n    sessionLoading ||\n    probabilityLoading ||\n    setupLoading;\n\n  const aggregatedError = error || modelError || qualityError || sessionError || setupError;\n\n  // Persist expansion preference\n  useEffect(() => {\n    const savedExpansion = localStorage.getItem('elite-intelligence-expanded');\n    if (savedExpansion === 'true') {\n      setIsExpanded(true);\n    }\n  }, []);\n\n  const handleToggleExpansion = () => {\n    const newExpanded = !isExpanded;\n    setIsExpanded(newExpanded);\n    localStorage.setItem('elite-intelligence-expanded', newExpanded.toString());\n  };\n\n  // Loading state\n  if (aggregatedLoading) {\n    return (\n      <Card\n        title='🧠 Elite ICT Trading Intelligence'\n        className={className}\n        actions={\n          onRefresh && (\n            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>\n              🔄 Refresh\n            </RefreshButton>\n          )\n        }\n      >\n        <LoadingState>\n          <LoadingSpinner />\n          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>\n            Analyzing Market Conditions\n          </div>\n          <div style={{ fontSize: '14px', opacity: 0.7 }}>\n            Generating intelligent trading recommendations...\n          </div>\n        </LoadingState>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (aggregatedError) {\n    return (\n      <Card\n        title='🧠 Elite ICT Trading Intelligence'\n        className={className}\n        actions={onRefresh && <RefreshButton onClick={onRefresh}>🔄 Retry</RefreshButton>}\n      >\n        <ErrorState>\n          <div style={{ fontSize: '48px', marginBottom: '16px' }}>⚠️</div>\n          <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>\n            Error Loading Intelligence Data\n          </div>\n          <div style={{ fontSize: '14px', opacity: 0.7 }}>{aggregatedError}</div>\n        </ErrorState>\n      </Card>\n    );\n  }\n\n  return (\n    <Card\n      title='🧠 Elite ICT Trading Intelligence'\n      className={className}\n      actions={\n        <HeaderRight>\n          <MarketStateIndicator marketState={marketState} showDetails />\n          <ExpandToggleButton\n            $isExpanded={isExpanded}\n            onClick={handleToggleExpansion}\n            disabled={aggregatedLoading}\n          >\n            {isExpanded ? '📊 Collapse' : '📊 Detailed Analysis'}\n            <span className='expand-icon'>▼</span>\n          </ExpandToggleButton>\n          {onRefresh && (\n            <RefreshButton onClick={onRefresh} disabled={aggregatedLoading}>\n              🔄 Refresh\n            </RefreshButton>\n          )}\n        </HeaderRight>\n      }\n    >\n      <LayoutContainer>\n        <ContentContainer>\n          {/* Always show Quick Decision Panel */}\n          <QuickDecisionPanel\n            modelRecommendation={modelRec}\n            patternQuality={qualityAnalysis.currentScore}\n            successProbability={successProbability}\n            setupIntelligence={setupIntelligence}\n          />\n\n          {/* Expandable Detailed Analysis */}\n          <ExpandedContent $isExpanded={isExpanded}>\n            <DetailedAnalysisPanel\n              modelRecommendation={modelRec}\n              patternQuality={qualityAnalysis.currentScore}\n              successProbability={successProbability}\n              setupIntelligence={setupIntelligence}\n            />\n          </ExpandedContent>\n        </ContentContainer>\n      </LayoutContainer>\n    </Card>\n  );\n};\n\nexport default EliteIntelligenceLayout;\n", "/**\n * PD Array Intelligence Hook\n * \n * Analyzes real trading data to track active FVGs, RD levels, liquidity targets,\n * and parent PD arrays with performance statistics and recommendations.\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport { tradeStorageService } from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nexport interface PDArrayLevel {\n  type: 'FVG' | 'NWOG' | 'NDOG' | 'RD' | 'Liquidity';\n  level: string;\n  timeframe: string;\n  age: string;\n  isActive: boolean;\n  performance: {\n    totalTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    successRate: number;\n  };\n  recommendation: string;\n  priority: 'HIGH' | 'MEDIUM' | 'LOW';\n}\n\nexport interface PDArraySummary {\n  totalActiveLevels: number;\n  bestPerformingType: string;\n  overallSuccessRate: number;\n  avgRMultiple: number;\n  keyInsights: string[];\n}\n\nexport interface PDArrayIntelligence {\n  activePDArrays: PDArrayLevel[];\n  summary: PDArraySummary;\n  lastUpdated: string;\n}\n\n/**\n * Extract PD Array information from trade data\n */\nconst extractPDArrayInfo = (trade: CompleteTradeData): { type: PDArrayLevel['type']; details: string }[] => {\n  const arrays: { type: PDArrayLevel['type']; details: string }[] = [];\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const modelType = trade.trade.model_type?.toLowerCase() || '';\n  const combined = `${notes} ${setup} ${modelType}`;\n  \n  // FVG detection\n  if (combined.includes('fvg') || combined.includes('fair value gap') || combined.includes('imbalance')) {\n    arrays.push({ type: 'FVG', details: `FVG from ${trade.trade.date}` });\n  }\n  \n  // NWOG detection\n  if (combined.includes('nwog') || combined.includes('new week opening') || combined.includes('weekly gap')) {\n    arrays.push({ type: 'NWOG', details: `NWOG level from ${trade.trade.date}` });\n  }\n  \n  // NDOG detection\n  if (combined.includes('ndog') || combined.includes('new day opening') || combined.includes('daily gap')) {\n    arrays.push({ type: 'NDOG', details: `NDOG level from ${trade.trade.date}` });\n  }\n  \n  // RD detection\n  if (combined.includes('rd') || combined.includes('reaction') || combined.includes('displacement')) {\n    arrays.push({ type: 'RD', details: `RD level from ${trade.trade.date}` });\n  }\n  \n  // Liquidity detection\n  if (combined.includes('liquidity') || combined.includes('sweep') || combined.includes('raid') || combined.includes('hunt')) {\n    arrays.push({ type: 'Liquidity', details: `Liquidity target from ${trade.trade.date}` });\n  }\n  \n  return arrays;\n};\n\n/**\n * Calculate age of PD Array based on trade date\n */\nconst calculateAge = (tradeDate: string): string => {\n  const trade = new Date(tradeDate);\n  const now = new Date();\n  const diffTime = Math.abs(now.getTime() - trade.getTime());\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n  \n  if (diffDays === 1) return '1 day';\n  if (diffDays < 7) return `${diffDays} days`;\n  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks`;\n  return `${Math.floor(diffDays / 30)} months`;\n};\n\n/**\n * Determine timeframe from trade data\n */\nconst determineTimeframe = (trade: CompleteTradeData): string => {\n  const notes = trade.trade.notes?.toLowerCase() || '';\n  const setup = trade.trade.setup?.toLowerCase() || '';\n  const combined = `${notes} ${setup}`;\n  \n  if (combined.includes('daily') || combined.includes('1d')) return 'Daily';\n  if (combined.includes('4h') || combined.includes('4hr')) return '4H';\n  if (combined.includes('1h') || combined.includes('1hr')) return '1H';\n  if (combined.includes('15m') || combined.includes('15min')) return '15M';\n  if (combined.includes('5m') || combined.includes('5min')) return '5M';\n  if (combined.includes('1m') || combined.includes('1min')) return '1M';\n  \n  // Default based on session\n  const session = trade.trade.session;\n  if (session === 'NY Open' || session === 'Lunch Macro') return '15M';\n  return '5M';\n};\n\n/**\n * Generate level string from trade data\n */\nconst generateLevelString = (trade: CompleteTradeData, arrayType: PDArrayLevel['type']): string => {\n  const entryPrice = trade.trade.entry_price;\n  const exitPrice = trade.trade.exit_price;\n  \n  if (!entryPrice) return 'N/A';\n  \n  // Estimate level based on entry price and array type\n  switch (arrayType) {\n    case 'FVG':\n      // FVG typically has a range\n      const fvgRange = Math.abs((exitPrice || entryPrice) - entryPrice);\n      return `${(entryPrice - fvgRange/2).toFixed(0)}-${(entryPrice + fvgRange/2).toFixed(0)}`;\n    case 'NWOG':\n    case 'NDOG':\n    case 'RD':\n    case 'Liquidity':\n      return entryPrice.toFixed(0);\n    default:\n      return entryPrice.toFixed(0);\n  }\n};\n\n/**\n * Analyze PD Array performance\n */\nconst analyzePDArrayPerformance = (\n  arrayType: PDArrayLevel['type'],\n  trades: CompleteTradeData[]\n): PDArrayLevel['performance'] => {\n  const arrayTrades = trades.filter(trade => {\n    const arrays = extractPDArrayInfo(trade);\n    return arrays.some(arr => arr.type === arrayType);\n  });\n\n  const totalTrades = arrayTrades.length;\n  const winningTrades = arrayTrades.filter(t => t.trade.win_loss === 'Win').length;\n  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n  \n  const rMultiples = arrayTrades\n    .map(t => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined && r !== null);\n  const avgRMultiple = rMultiples.length > 0 ? \n    rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n  \n  // Success rate considers both wins and positive R-multiples\n  const successfulTrades = arrayTrades.filter(t => \n    t.trade.win_loss === 'Win' || (t.trade.r_multiple && t.trade.r_multiple > 0)\n  ).length;\n  const successRate = totalTrades > 0 ? (successfulTrades / totalTrades) * 100 : 0;\n\n  return {\n    totalTrades,\n    winRate,\n    avgRMultiple,\n    successRate\n  };\n};\n\n/**\n * Generate recommendations for PD Array\n */\nconst generateRecommendation = (\n  arrayType: PDArrayLevel['type'],\n  performance: PDArrayLevel['performance'],\n  age: string\n): { recommendation: string; priority: PDArrayLevel['priority'] } => {\n  const { winRate, avgRMultiple, totalTrades } = performance;\n  \n  // Base recommendations by array type\n  let baseRec = '';\n  switch (arrayType) {\n    case 'FVG':\n      baseRec = 'Target FVG fills with confluence';\n      break;\n    case 'NWOG':\n      baseRec = 'High-probability parent PD array reactions';\n      break;\n    case 'NDOG':\n      baseRec = 'Daily opening gap redelivery opportunities';\n      break;\n    case 'RD':\n      baseRec = 'Reaction delivery continuation setups';\n      break;\n    case 'Liquidity':\n      baseRec = 'Liquidity sweep and reversal opportunities';\n      break;\n  }\n  \n  // Adjust based on performance\n  let priority: PDArrayLevel['priority'] = 'MEDIUM';\n  let performanceNote = '';\n  \n  if (totalTrades >= 3) {\n    if (winRate >= 70 && avgRMultiple >= 1.5) {\n      priority = 'HIGH';\n      performanceNote = ` (${winRate.toFixed(0)}% win rate, ${avgRMultiple.toFixed(1)}R avg)`;\n    } else if (winRate >= 60 || avgRMultiple >= 1.2) {\n      priority = 'MEDIUM';\n      performanceNote = ` (${winRate.toFixed(0)}% win rate)`;\n    } else {\n      priority = 'LOW';\n      performanceNote = ` (${winRate.toFixed(0)}% win rate - use caution)`;\n    }\n  } else if (totalTrades > 0) {\n    performanceNote = ` (${totalTrades} trade${totalTrades > 1 ? 's' : ''} - limited data)`;\n  } else {\n    performanceNote = ' (no historical data)';\n    priority = 'LOW';\n  }\n  \n  // Age consideration\n  if (age.includes('day') && !age.includes('days')) {\n    performanceNote += ' - Fresh level';\n  } else if (age.includes('week')) {\n    performanceNote += ' - Established level';\n  }\n\n  return {\n    recommendation: baseRec + performanceNote,\n    priority\n  };\n};\n\n/**\n * PD Array Intelligence Hook\n */\nexport const usePDArrayIntelligence = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for PD Array intelligence:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate PD Array intelligence\n  const pdArrayIntelligence: PDArrayIntelligence | null = useMemo(() => {\n    if (trades.length === 0) {\n      return null;\n    }\n\n    // Get recent trades (last 30 days) for active levels\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n    \n    const recentTrades = trades.filter(trade => {\n      const tradeDate = new Date(trade.trade.date);\n      return tradeDate >= thirtyDaysAgo;\n    });\n\n    // Extract all unique PD Arrays from recent trades\n    const pdArrayMap = new Map<string, { type: PDArrayLevel['type']; trade: CompleteTradeData }>();\n    \n    recentTrades.forEach(trade => {\n      const arrays = extractPDArrayInfo(trade);\n      arrays.forEach(array => {\n        const key = `${array.type}-${trade.trade.date}-${trade.trade.entry_price}`;\n        if (!pdArrayMap.has(key)) {\n          pdArrayMap.set(key, { type: array.type, trade });\n        }\n      });\n    });\n\n    // Create PD Array levels\n    const activePDArrays: PDArrayLevel[] = Array.from(pdArrayMap.values()).map(({ type, trade }) => {\n      const performance = analyzePDArrayPerformance(type, trades);\n      const age = calculateAge(trade.trade.date);\n      const timeframe = determineTimeframe(trade);\n      const level = generateLevelString(trade, type);\n      const { recommendation, priority } = generateRecommendation(type, performance, age);\n      \n      // Consider active if recent and has good performance or is fresh\n      const isActive = (\n        (performance.totalTrades >= 2 && performance.winRate >= 50) ||\n        (performance.totalTrades < 2 && age.includes('day'))\n      );\n\n      return {\n        type,\n        level,\n        timeframe,\n        age,\n        isActive,\n        performance,\n        recommendation,\n        priority\n      };\n    });\n\n    // Sort by priority and performance\n    activePDArrays.sort((a, b) => {\n      const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };\n      const aPriority = priorityOrder[a.priority];\n      const bPriority = priorityOrder[b.priority];\n      \n      if (aPriority !== bPriority) return bPriority - aPriority;\n      return b.performance.winRate - a.performance.winRate;\n    });\n\n    // Calculate summary statistics\n    const totalActiveLevels = activePDArrays.filter(arr => arr.isActive).length;\n    \n    // Find best performing type\n    const typePerformance = ['FVG', 'NWOG', 'NDOG', 'RD', 'Liquidity'].map(type => {\n      const typeArrays = activePDArrays.filter(arr => arr.type === type);\n      const avgWinRate = typeArrays.length > 0 ? \n        typeArrays.reduce((sum, arr) => sum + arr.performance.winRate, 0) / typeArrays.length : 0;\n      return { type, avgWinRate, count: typeArrays.length };\n    });\n    \n    const bestType = typePerformance.reduce((best, current) => \n      current.avgWinRate > best.avgWinRate && current.count > 0 ? current : best\n    , { type: 'N/A', avgWinRate: 0, count: 0 });\n\n    const overallSuccessRate = activePDArrays.length > 0 ? \n      activePDArrays.reduce((sum, arr) => sum + arr.performance.successRate, 0) / activePDArrays.length : 0;\n    \n    const avgRMultiple = activePDArrays.length > 0 ? \n      activePDArrays.reduce((sum, arr) => sum + arr.performance.avgRMultiple, 0) / activePDArrays.length : 0;\n\n    // Generate key insights\n    const keyInsights: string[] = [];\n    if (bestType.type !== 'N/A') {\n      keyInsights.push(`${bestType.type} arrays show best performance (${bestType.avgWinRate.toFixed(0)}% avg win rate)`);\n    }\n    \n    const highPriorityCount = activePDArrays.filter(arr => arr.priority === 'HIGH').length;\n    if (highPriorityCount > 0) {\n      keyInsights.push(`${highPriorityCount} high-priority levels active`);\n    }\n    \n    if (overallSuccessRate >= 70) {\n      keyInsights.push('Strong PD Array performance overall');\n    } else if (overallSuccessRate < 50) {\n      keyInsights.push('Focus on quality over quantity');\n    }\n\n    const summary: PDArraySummary = {\n      totalActiveLevels,\n      bestPerformingType: bestType.type,\n      overallSuccessRate,\n      avgRMultiple,\n      keyInsights\n    };\n\n    return {\n      activePDArrays: activePDArrays.slice(0, 10), // Limit to top 10\n      summary,\n      lastUpdated: new Date().toISOString()\n    };\n  }, [trades]);\n\n  return {\n    pdArrayIntelligence,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    }\n  };\n};\n", "/**\n * PD Array Levels Component\n *\n * Replaces generic Key Levels with sophisticated ICT PD Array intelligence.\n * Tracks active FVGs, RD levels, liquidity targets, and parent PD arrays using real trading data.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Card } from '@adhd-trading-dashboard/shared';\nimport { usePDArrayIntelligence } from '../hooks/usePDArrayIntelligence';\n\nexport interface PDArrayLevelsProps {\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when the refresh button is clicked */\n  onRefresh?: () => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components with F1 theme\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n`;\n\nconst SectionTitle = styled.h3`\n  color: var(--session-text-primary);\n  font-size: 18px;\n  font-weight: 700;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst PDArrayCard = styled.div.attrs<{ arrayType: string; isActive: boolean }>(\n  ({ arrayType, isActive }) => ({\n    className: `pd-array-card PDArrayCard ${isActive ? 'active' : ''}`,\n    'data-array-type': arrayType.toLowerCase(),\n    'data-active': isActive,\n  })\n)<{ arrayType: string; isActive: boolean }>`\n  /* Use CSS variables for clean theming */\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-left: 4px solid var(--session-card-accent);\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 12px;\n  transition: all 0.2s ease;\n  box-shadow: var(--shadow-sm);\n\n  &[data-active='true'] {\n    border-left-color: var(--pd-array-accent, var(--session-card-accent));\n    box-shadow: var(--shadow-sm), 0 0 0 1px var(--pd-array-accent, var(--session-card-accent));\n  }\n\n  /* Array type styling handled by CSS variables */\n  &[data-array-type='fvg'][data-active='true'] {\n    --pd-array-accent: var(--info-color);\n  }\n\n  &[data-array-type='nwog'][data-active='true'] {\n    --pd-array-accent: var(--success-color);\n  }\n\n  &[data-array-type='ndog'][data-active='true'] {\n    --pd-array-accent: var(--warning-color);\n  }\n\n  &[data-array-type='rd'][data-active='true'] {\n    --pd-array-accent: var(--error-color);\n  }\n\n  &[data-array-type='liquidity'][data-active='true'] {\n    --pd-array-accent: var(--secondary-color);\n  }\n\n  &[data-array-type='summary'][data-active='true'] {\n    --pd-array-accent: var(--primary-color);\n  }\n`;\n\nconst ArrayHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n\nconst ArrayType = styled.div.attrs<{ arrayType: string }>(({ arrayType }) => ({\n  className: `array-type ArrayType`,\n  'data-array-type': arrayType.toLowerCase(),\n}))<{ arrayType: string }>`\n  font-size: 16px;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n\n  /* Use CSS variables for array type colors */\n  &[data-array-type='fvg'] {\n    color: var(--info-color);\n  }\n\n  &[data-array-type='nwog'] {\n    color: var(--success-color);\n  }\n\n  &[data-array-type='ndog'] {\n    color: var(--warning-color);\n  }\n\n  &[data-array-type='rd'] {\n    color: var(--error-color);\n  }\n\n  &[data-array-type='liquidity'] {\n    color: var(--secondary-color);\n  }\n\n  &[data-array-type='summary'] {\n    color: var(--primary-color);\n  }\n\n  /* Default color */\n  color: var(--session-text-primary);\n`;\n\nconst ArrayStatus = styled.div.attrs<{ isActive: boolean }>(({ isActive }) => ({\n  className: `array-status ArrayStatus`,\n  'data-active': isActive,\n}))<{ isActive: boolean }>`\n  background: ${({ isActive }) =>\n    isActive ? 'var(--success-color)' : 'var(--session-card-border)'};\n  color: ${({ isActive }) =>\n    isActive ? 'var(--session-text-primary)' : 'var(--session-text-secondary)'};\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n`;\n\nconst LevelInfo = styled.div`\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 12px;\n  margin: 12px 0;\n`;\n\nconst LevelDetail = styled.div`\n  text-align: center;\n`;\n\nconst LevelValue = styled.div`\n  font-size: 16px;\n  font-weight: 700;\n  color: var(--session-text-primary);\n`;\n\nconst LevelLabel = styled.div`\n  font-size: 10px;\n  color: var(--session-text-secondary);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  margin-top: 2px;\n`;\n\nconst PerformanceStats = styled.div.attrs({\n  className: 'performance-stats PerformanceStats',\n})`\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-radius: 8px;\n  padding: 12px;\n  margin-top: 12px;\n  box-shadow: var(--shadow-sm);\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 8px;\n`;\n\nconst StatItem = styled.div`\n  text-align: center;\n`;\n\nconst StatValue = styled.div`\n  font-size: 14px;\n  font-weight: 600;\n  color: var(--session-text-primary);\n`;\n\nconst StatLabel = styled.div`\n  font-size: 9px;\n  color: var(--session-text-secondary);\n  text-transform: uppercase;\n`;\n\nconst PriorityBadge = styled.div.attrs<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>(({ priority }) => ({\n  className: 'priority-badge',\n  'data-priority': priority,\n}))<{ priority: 'HIGH' | 'MEDIUM' | 'LOW' }>`\n  background: ${({ priority }) => {\n    switch (priority) {\n      case 'HIGH':\n        return 'var(--error-color)';\n      case 'MEDIUM':\n        return 'var(--warning-color)';\n      default:\n        return 'var(--session-card-border)';\n    }\n  }};\n  color: ${({ priority }) =>\n    priority === 'LOW' ? 'var(--session-text-secondary)' : 'var(--session-text-primary)'};\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 10px;\n  font-weight: 600;\n  text-transform: uppercase;\n  margin-left: 8px;\n`;\n\nconst RecommendationText = styled.div`\n  color: var(--session-text-secondary);\n  font-size: 12px;\n  line-height: 1.4;\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px solid var(--session-card-border);\n`;\n\nconst EmptyState = styled.div`\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-radius: 12px;\n  text-align: center;\n  padding: 40px 20px;\n  color: var(--session-text-secondary);\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: 16px;\n`;\n\nconst EmptyTitle = styled.h3`\n  color: var(--session-text-primary);\n  font-size: 18px;\n  margin-bottom: 8px;\n`;\n\nconst EmptyMessage = styled.p`\n  color: var(--session-text-secondary);\n  font-size: 14px;\n  line-height: 1.5;\n`;\n\n/**\n * PD Array Levels Component\n */\nexport const PDArrayLevels: React.FC<PDArrayLevelsProps> = ({\n  isLoading = false,\n  error = null,\n  onRefresh,\n  className,\n}) => {\n  const {\n    pdArrayIntelligence,\n    isLoading: intelligenceLoading,\n    error: intelligenceError,\n  } = usePDArrayIntelligence();\n\n  const loading = isLoading || intelligenceLoading;\n  const displayError = error || intelligenceError;\n\n  // Loading state\n  if (loading) {\n    return (\n      <Card title=\"🎯 PD Array Intelligence\">\n        <div style={{ padding: '24px', textAlign: 'center' }}>\n          Analyzing PD Array levels and liquidity targets...\n        </div>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (displayError) {\n    return (\n      <Card title=\"🎯 PD Array Intelligence\">\n        <div style={{ padding: '24px', textAlign: 'center', color: 'var(--error-color)' }}>\n          Error: {displayError}\n          {onRefresh && (\n            <button\n              onClick={onRefresh}\n              style={{\n                marginLeft: '16px',\n                padding: '8px 16px',\n                background: 'var(--session-card-bg)',\n                border: '1px solid var(--session-card-border)',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                color: 'var(--session-text-primary)',\n              }}\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </Card>\n    );\n  }\n\n  // Empty state\n  if (!pdArrayIntelligence || pdArrayIntelligence.activePDArrays.length === 0) {\n    return (\n      <Card title=\"🎯 PD Array Intelligence\">\n        <EmptyState>\n          <EmptyIcon>📊</EmptyIcon>\n          <EmptyTitle>No PD Array Data Available</EmptyTitle>\n          <EmptyMessage>\n            Import your trading data to begin tracking FVGs, NWOG/NDOG levels, RD formations, and\n            liquidity targets. The system will analyze your historical performance with each PD\n            Array type.\n          </EmptyMessage>\n        </EmptyState>\n      </Card>\n    );\n  }\n\n  return (\n    <Card\n      title=\"🎯 PD Array Intelligence\"\n      actions={\n        onRefresh ? (\n          <button\n            onClick={onRefresh}\n            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}\n          >\n            🔄 Refresh\n          </button>\n        ) : undefined\n      }\n    >\n      <Container className={className}>\n        {/* Active PD Arrays */}\n        <div>\n          <SectionTitle>\n            🔥 Active PD Arrays\n            <PriorityBadge priority=\"HIGH\">PRIORITY TARGETS</PriorityBadge>\n          </SectionTitle>\n\n          {pdArrayIntelligence.activePDArrays.map((array, index) => (\n            <PDArrayCard key={index} arrayType={array.type} isActive={array.isActive}>\n              <ArrayHeader>\n                <ArrayType arrayType={array.type}>{array.type}</ArrayType>\n                <ArrayStatus isActive={array.isActive}>\n                  {array.isActive ? 'ACTIVE' : 'INACTIVE'}\n                </ArrayStatus>\n              </ArrayHeader>\n\n              <LevelInfo>\n                <LevelDetail>\n                  <LevelValue>{array.level}</LevelValue>\n                  <LevelLabel>Level</LevelLabel>\n                </LevelDetail>\n                <LevelDetail>\n                  <LevelValue>{array.timeframe}</LevelValue>\n                  <LevelLabel>Timeframe</LevelLabel>\n                </LevelDetail>\n                <LevelDetail>\n                  <LevelValue>{array.age}</LevelValue>\n                  <LevelLabel>Age</LevelLabel>\n                </LevelDetail>\n              </LevelInfo>\n\n              <PerformanceStats>\n                <StatsGrid>\n                  <StatItem>\n                    <StatValue>{array.performance.totalTrades}</StatValue>\n                    <StatLabel>Trades</StatLabel>\n                  </StatItem>\n                  <StatItem>\n                    <StatValue>{array.performance.winRate.toFixed(0)}%</StatValue>\n                    <StatLabel>Win Rate</StatLabel>\n                  </StatItem>\n                  <StatItem>\n                    <StatValue>{array.performance.avgRMultiple.toFixed(1)}R</StatValue>\n                    <StatLabel>Avg R</StatLabel>\n                  </StatItem>\n                  <StatItem>\n                    <StatValue>{array.performance.successRate.toFixed(0)}%</StatValue>\n                    <StatLabel>Success Rate</StatLabel>\n                  </StatItem>\n                </StatsGrid>\n              </PerformanceStats>\n\n              <RecommendationText>\n                <strong>Strategy:</strong> {array.recommendation}\n              </RecommendationText>\n            </PDArrayCard>\n          ))}\n        </div>\n\n        {/* Summary Statistics */}\n        <div>\n          <SectionTitle>📊 PD Array Performance Summary</SectionTitle>\n          <PDArrayCard arrayType=\"summary\" isActive={true}>\n            <StatsGrid>\n              <StatItem>\n                <StatValue>{pdArrayIntelligence.summary.totalActiveLevels}</StatValue>\n                <StatLabel>Active Levels</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{pdArrayIntelligence.summary.bestPerformingType}</StatValue>\n                <StatLabel>Best Type</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{pdArrayIntelligence.summary.overallSuccessRate.toFixed(0)}%</StatValue>\n                <StatLabel>Overall Success</StatLabel>\n              </StatItem>\n              <StatItem>\n                <StatValue>{pdArrayIntelligence.summary.avgRMultiple.toFixed(1)}R</StatValue>\n                <StatLabel>Avg R-Multiple</StatLabel>\n              </StatItem>\n            </StatsGrid>\n\n            <RecommendationText>\n              <strong>Key Insights:</strong> {pdArrayIntelligence.summary.keyInsights.join(' • ')}\n            </RecommendationText>\n          </PDArrayCard>\n        </div>\n      </Container>\n    </Card>\n  );\n};\n", "/**\n * Enhanced Session Intelligence Hook\n *\n * Advanced ICT session analysis using real trading data from spreadsheet import.\n * Provides sophisticated timing, model selection, and PD Array insights.\n */\n\nimport { useState, useEffect, useMemo } from 'react';\nimport {\n  tradeStorageService,\n  getCurrentDualTime,\n  formatTimeInterval,\n  getCurrentNYMinutes,\n} from '@adhd-trading-dashboard/shared';\nimport type { CompleteTradeData } from '@adhd-trading-dashboard/shared';\n\nexport interface ICTSessionPerformance {\n  sessionName: string;\n  sessionType: 'Pre-Market' | 'NY Open' | 'Lunch Macro' | 'MOC';\n  timeRange: string;\n  performance: {\n    totalTrades: number;\n    winningTrades: number;\n    winRate: number;\n    avgRMultiple: number;\n    totalPnL: number;\n    avgRisk: number;\n  };\n  modelPreference: {\n    rdCont: { trades: number; winRate: number; avgR: number };\n    fvgRd: { trades: number; winRate: number; avgR: number };\n    recommendation: 'RD-Cont' | 'FVG-RD' | 'Either';\n  };\n  optimalWindows: {\n    start: string;\n    end: string;\n    description: string;\n    winRate: number;\n    trades: number;\n  }[];\n  qualityThreshold: number;\n  recommendations: string[];\n}\n\nexport interface CurrentSessionStatus {\n  currentTime: string;\n  currentTimeFormatted: string;\n  activeSession: ICTSessionPerformance | null;\n  nextSession: ICTSessionPerformance | null;\n  timeToNext: number;\n  timeToNextFormatted: string;\n  isOptimalWindow: boolean;\n  currentRecommendation: string;\n  urgency: 'LOW' | 'MEDIUM' | 'HIGH';\n}\n\nexport interface EnhancedSessionIntelligence {\n  sessions: ICTSessionPerformance[];\n  currentStatus: CurrentSessionStatus;\n  weeklyInsights: {\n    bestSession: string;\n    bestModel: 'RD-Cont' | 'FVG-RD';\n    avgQuality: number;\n    qualityThreshold: number;\n    recommendations: string[];\n  };\n}\n\n/**\n * Parse session from trade data\n */\nconst parseSession = (trade: CompleteTradeData): string | null => {\n  // First check the session field\n  if (trade.trade.session) {\n    return trade.trade.session;\n  }\n\n  // Fall back to entry time analysis\n  const entryTime = trade.trade.entry_time;\n  if (!entryTime) return null;\n\n  const [hours, minutes] = entryTime.split(':').map(Number);\n  const timeMinutes = hours * 60 + minutes;\n\n  // Map time to ICT sessions\n  if (timeMinutes >= 480 && timeMinutes < 570) return 'Pre-Market'; // 8:00-9:30\n  if (timeMinutes >= 570 && timeMinutes < 660) return 'NY Open'; // 9:30-11:00\n  if (timeMinutes >= 710 && timeMinutes < 810) return 'Lunch Macro'; // 11:50-13:30\n  if (timeMinutes >= 915 && timeMinutes < 960) return 'MOC'; // 15:15-16:00\n\n  return null;\n};\n\n/**\n * Calculate optimal windows for a session\n */\nconst calculateOptimalWindows = (sessionTrades: CompleteTradeData[], sessionType: string) => {\n  const windows = {\n    'Pre-Market': [\n      { start: '08:00', end: '08:30', description: 'Early Pre-Market Setup' },\n      { start: '08:30', end: '09:00', description: 'Prime Pre-Market Window' },\n      { start: '09:00', end: '09:30', description: 'Market Prep Phase' },\n    ],\n    'NY Open': [\n      { start: '09:30', end: '09:45', description: 'Market Open Volatility' },\n      { start: '09:45', end: '10:15', description: 'OPTIMAL WINDOW' },\n      { start: '10:15', end: '10:45', description: 'Secondary Opportunity' },\n      { start: '10:45', end: '11:00', description: 'Session Wind-down' },\n    ],\n    'Lunch Macro': [\n      { start: '11:50', end: '12:10', description: 'PRIMARY WINDOW' },\n      { start: '12:10', end: '12:30', description: 'Continuation Phase' },\n      { start: '12:30', end: '13:00', description: 'Midday Consolidation' },\n      { start: '13:00', end: '13:30', description: 'Afternoon Transition' },\n    ],\n    MOC: [\n      { start: '15:15', end: '15:45', description: 'Pre-Close Setup' },\n      { start: '15:45', end: '16:00', description: 'Final Momentum' },\n    ],\n  };\n\n  const sessionWindows = windows[sessionType as keyof typeof windows] || [];\n\n  return sessionWindows.map(window => {\n    const windowTrades = sessionTrades.filter(trade => {\n      const entryTime = trade.trade.entry_time;\n      if (!entryTime) return false;\n\n      const [hours, minutes] = entryTime.split(':').map(Number);\n      const tradeMinutes = hours * 60 + minutes;\n      const [startHours, startMins] = window.start.split(':').map(Number);\n      const [endHours, endMins] = window.end.split(':').map(Number);\n      const startMinutes = startHours * 60 + startMins;\n      const endMinutes = endHours * 60 + endMins;\n\n      return tradeMinutes >= startMinutes && tradeMinutes <= endMinutes;\n    });\n\n    const wins = windowTrades.filter(t => t.trade.win_loss === 'Win').length;\n    const winRate = windowTrades.length > 0 ? (wins / windowTrades.length) * 100 : 0;\n\n    return {\n      ...window,\n      winRate,\n      trades: windowTrades.length,\n    };\n  });\n};\n\n/**\n * Analyze session performance\n */\nconst analyzeSessionPerformance = (\n  sessionName: string,\n  sessionTrades: CompleteTradeData[]\n): ICTSessionPerformance => {\n  const sessionType = sessionName as ICTSessionPerformance['sessionType'];\n\n  // Basic performance metrics\n  const totalTrades = sessionTrades.length;\n  const winningTrades = sessionTrades.filter(t => t.trade.win_loss === 'Win').length;\n  const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;\n\n  const rMultiples = sessionTrades\n    .map(t => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined && r !== null);\n  const avgRMultiple =\n    rMultiples.length > 0 ? rMultiples.reduce((sum, r) => sum + r, 0) / rMultiples.length : 0;\n\n  const totalPnL = sessionTrades\n    .map(t => t.trade.achieved_pl || 0)\n    .reduce((sum, pl) => sum + pl, 0);\n\n  const riskPoints = sessionTrades.map(t => t.trade.risk_points || 0).filter(r => r > 0);\n  const avgRisk =\n    riskPoints.length > 0 ? riskPoints.reduce((sum, r) => sum + r, 0) / riskPoints.length : 0;\n\n  // Model preference analysis\n  const rdContTrades = sessionTrades.filter(t => t.trade.model_type === 'RD-Cont');\n  const fvgRdTrades = sessionTrades.filter(t => t.trade.model_type === 'FVG-RD');\n\n  const rdContWins = rdContTrades.filter(t => t.trade.win_loss === 'Win').length;\n  const fvgRdWins = fvgRdTrades.filter(t => t.trade.win_loss === 'Win').length;\n\n  const rdContWinRate = rdContTrades.length > 0 ? (rdContWins / rdContTrades.length) * 100 : 0;\n  const fvgRdWinRate = fvgRdTrades.length > 0 ? (fvgRdWins / fvgRdTrades.length) * 100 : 0;\n\n  const rdContRs = rdContTrades\n    .map(t => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined);\n  const fvgRdRs = fvgRdTrades\n    .map(t => t.trade.r_multiple)\n    .filter((r): r is number => r !== undefined);\n\n  const rdContAvgR =\n    rdContRs.length > 0 ? rdContRs.reduce((sum, r) => sum + r, 0) / rdContRs.length : 0;\n  const fvgRdAvgR =\n    fvgRdRs.length > 0 ? fvgRdRs.reduce((sum, r) => sum + r, 0) / fvgRdRs.length : 0;\n\n  // Determine model recommendation\n  let modelRecommendation: 'RD-Cont' | 'FVG-RD' | 'Either' = 'Either';\n  if (rdContTrades.length >= 2 && fvgRdTrades.length >= 2) {\n    if (rdContWinRate > fvgRdWinRate + 10) modelRecommendation = 'RD-Cont';\n    else if (fvgRdWinRate > rdContWinRate + 10) modelRecommendation = 'FVG-RD';\n  } else if (rdContTrades.length >= 3) {\n    modelRecommendation = 'RD-Cont';\n  } else if (fvgRdTrades.length >= 3) {\n    modelRecommendation = 'FVG-RD';\n  }\n\n  // Calculate quality threshold (qualityRatings removed as unused)\n  const winningQualities = sessionTrades\n    .filter(t => t.trade.win_loss === 'Win')\n    .map(t => t.trade.pattern_quality_rating)\n    .filter((q): q is number => q !== undefined && q !== null);\n\n  const avgWinningQuality =\n    winningQualities.length > 0\n      ? winningQualities.reduce((sum, q) => sum + q, 0) / winningQualities.length\n      : 3.5;\n  const qualityThreshold = Math.max(3.0, avgWinningQuality - 0.2);\n\n  // Generate recommendations\n  const recommendations: string[] = [];\n  if (modelRecommendation !== 'Either') {\n    recommendations.push(\n      `Focus on ${modelRecommendation} setups (${\n        modelRecommendation === 'RD-Cont' ? rdContWinRate.toFixed(0) : fvgRdWinRate.toFixed(0)\n      }% win rate)`\n    );\n  }\n  if (qualityThreshold > 3.0) {\n    recommendations.push(\n      `Maintain pattern quality >${qualityThreshold.toFixed(1)} (your success threshold)`\n    );\n  }\n  if (avgRisk > 0) {\n    recommendations.push(`Target ${avgRisk.toFixed(0)} point risk (your historical average)`);\n  }\n\n  // Time range mapping\n  const timeRanges = {\n    'Pre-Market': '08:00-09:30',\n    'NY Open': '09:30-11:00',\n    'Lunch Macro': '11:50-13:30',\n    MOC: '15:15-16:00',\n  };\n\n  return {\n    sessionName,\n    sessionType,\n    timeRange: timeRanges[sessionType] || '',\n    performance: {\n      totalTrades,\n      winningTrades,\n      winRate,\n      avgRMultiple,\n      totalPnL,\n      avgRisk,\n    },\n    modelPreference: {\n      rdCont: { trades: rdContTrades.length, winRate: rdContWinRate, avgR: rdContAvgR },\n      fvgRd: { trades: fvgRdTrades.length, winRate: fvgRdWinRate, avgR: fvgRdAvgR },\n      recommendation: modelRecommendation,\n    },\n    optimalWindows: calculateOptimalWindows(sessionTrades, sessionType),\n    qualityThreshold,\n    recommendations,\n  };\n};\n\n/**\n * Enhanced Session Intelligence Hook\n */\nexport const useEnhancedSessionIntelligence = () => {\n  const [trades, setTrades] = useState<CompleteTradeData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Fetch trade data\n  useEffect(() => {\n    const fetchTrades = async () => {\n      try {\n        setIsLoading(true);\n        setError(null);\n        const tradeData = await tradeStorageService.getAllTrades();\n        setTrades(tradeData);\n      } catch (err) {\n        console.error('Error fetching trades for enhanced session intelligence:', err);\n        setError('Failed to load trade data');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchTrades();\n  }, []);\n\n  // Calculate enhanced session intelligence\n  const intelligence: EnhancedSessionIntelligence = useMemo(() => {\n    if (trades.length === 0) {\n      const dualTime = getCurrentDualTime();\n      return {\n        sessions: [],\n        currentStatus: {\n          currentTime: dualTime.nyTime,\n          currentTimeFormatted: dualTime.formatted,\n          activeSession: null,\n          nextSession: null,\n          timeToNext: 0,\n          timeToNextFormatted: 'N/A',\n          isOptimalWindow: false,\n          currentRecommendation: 'No trading data available',\n          urgency: 'LOW',\n        },\n        weeklyInsights: {\n          bestSession: 'N/A',\n          bestModel: 'RD-Cont',\n          avgQuality: 0,\n          qualityThreshold: 3.5,\n          recommendations: ['Import trading data to begin analysis'],\n        },\n      };\n    }\n\n    // Group trades by session\n    const sessionGroups = trades.reduce((groups, trade) => {\n      const session = parseSession(trade);\n      if (session) {\n        if (!groups[session]) groups[session] = [];\n        groups[session].push(trade);\n      }\n      return groups;\n    }, {} as Record<string, CompleteTradeData[]>);\n\n    // Analyze each session\n    const sessions = Object.entries(sessionGroups).map(([sessionName, sessionTrades]) =>\n      analyzeSessionPerformance(sessionName, sessionTrades)\n    );\n\n    // Calculate current status\n    const dualTime = getCurrentDualTime();\n    const currentTime = dualTime.nyTime;\n    const currentTimeFormatted = dualTime.formatted;\n    // CRITICAL FIX: Use NY time for all session calculations, not local time\n    const currentMinutes = getCurrentNYMinutes();\n\n    // Find active session\n    const activeSession =\n      sessions.find(session => {\n        if (!session.timeRange) return false;\n        const [start, end] = session.timeRange.split('-');\n        if (!start || !end) return false;\n        const [startHours, startMins] = start.split(':').map(Number);\n        const [endHours, endMins] = end.split(':').map(Number);\n        const startMinutes = startHours * 60 + startMins;\n        const endMinutes = endHours * 60 + endMins;\n        return currentMinutes >= startMinutes && currentMinutes <= endMinutes;\n      }) || null;\n\n    // Find next session (handle cross-day scenarios)\n    const nextSession = (() => {\n      // First, try to find a session later today\n      const todayNext = sessions.find(session => {\n        if (!session.timeRange) return false;\n        const [start] = session.timeRange.split('-');\n        if (!start) return false;\n        const [startHours, startMins] = start.split(':').map(Number);\n        const startMinutes = startHours * 60 + startMins;\n        return startMinutes > currentMinutes;\n      });\n\n      if (todayNext) return todayNext;\n\n      // If no session today, return the first session (for tomorrow)\n      return sessions.length > 0 ? sessions[0] : null;\n    })();\n\n    const timeToNext =\n      nextSession && nextSession.timeRange\n        ? (() => {\n            const [start] = nextSession.timeRange.split('-');\n            if (!start) return 0;\n            const [startHours, startMins] = start.split(':').map(Number);\n            const startMinutes = startHours * 60 + startMins;\n\n            let diff = startMinutes - currentMinutes;\n\n            // If negative, it means the session is tomorrow\n            if (diff <= 0) {\n              diff = 24 * 60 + diff; // Add 24 hours\n            }\n\n            return diff;\n          })()\n        : 0;\n\n    const timeToNextFormatted = timeToNext > 0 ? formatTimeInterval(timeToNext).formatted : 'N/A';\n\n    // Check if in optimal window\n    const isOptimalWindow = activeSession\n      ? activeSession.optimalWindows.some(window => {\n          const [startHours, startMins] = window.start.split(':').map(Number);\n          const [endHours, endMins] = window.end.split(':').map(Number);\n          const startMinutes = startHours * 60 + startMins;\n          const endMinutes = endHours * 60 + endMins;\n          return currentMinutes >= startMinutes && currentMinutes <= endMinutes;\n        })\n      : false;\n\n    // Generate current recommendation\n    let currentRecommendation = '';\n    let urgency: 'LOW' | 'MEDIUM' | 'HIGH' = 'LOW';\n\n    if (activeSession && isOptimalWindow) {\n      currentRecommendation = `OPTIMAL WINDOW ACTIVE - ${\n        activeSession.sessionName\n      } (${activeSession.performance.winRate.toFixed(0)}% win rate)`;\n      urgency = 'HIGH';\n    } else if (activeSession) {\n      currentRecommendation = `${activeSession.sessionName} active - ${activeSession.modelPreference.recommendation} focus`;\n      urgency = 'MEDIUM';\n    } else if (nextSession && timeToNext <= 30) {\n      currentRecommendation = `${nextSession.sessionName} starting in ${timeToNextFormatted}`;\n      urgency = 'MEDIUM';\n    } else {\n      currentRecommendation = 'No active trading session - Monitor for setups';\n      urgency = 'LOW';\n    }\n\n    // Calculate weekly insights\n    const bestSession = sessions.reduce(\n      (best, current) => (current.performance.winRate > best.performance.winRate ? current : best),\n      sessions[0] || { sessionName: 'N/A', performance: { winRate: 0 } }\n    ).sessionName;\n\n    const allRdContTrades = trades.filter(t => t.trade.model_type === 'RD-Cont');\n    const allFvgRdTrades = trades.filter(t => t.trade.model_type === 'FVG-RD');\n    const rdContWinRate =\n      allRdContTrades.length > 0\n        ? (allRdContTrades.filter(t => t.trade.win_loss === 'Win').length /\n            allRdContTrades.length) *\n          100\n        : 0;\n    const fvgRdWinRate =\n      allFvgRdTrades.length > 0\n        ? (allFvgRdTrades.filter(t => t.trade.win_loss === 'Win').length / allFvgRdTrades.length) *\n          100\n        : 0;\n\n    const bestModel: 'RD-Cont' | 'FVG-RD' = rdContWinRate >= fvgRdWinRate ? 'RD-Cont' : 'FVG-RD';\n\n    const allQualities = trades\n      .map(t => t.trade.pattern_quality_rating)\n      .filter((q): q is number => q !== undefined && q !== null);\n    const avgQuality =\n      allQualities.length > 0\n        ? allQualities.reduce((sum, q) => sum + q, 0) / allQualities.length\n        : 0;\n\n    const winningQualities = trades\n      .filter(t => t.trade.win_loss === 'Win')\n      .map(t => t.trade.pattern_quality_rating)\n      .filter((q): q is number => q !== undefined && q !== null);\n    const avgWinningQuality =\n      winningQualities.length > 0\n        ? winningQualities.reduce((sum, q) => sum + q, 0) / winningQualities.length\n        : 3.5;\n    const qualityThreshold = Math.max(3.0, avgWinningQuality - 0.2);\n\n    const weeklyRecommendations = [\n      `Focus on ${bestSession} session (${\n        sessions.find(s => s.sessionName === bestSession)?.performance.winRate.toFixed(0) || 0\n      }% win rate)`,\n      `Prioritize ${bestModel} setups (${\n        bestModel === 'RD-Cont' ? rdContWinRate.toFixed(0) : fvgRdWinRate.toFixed(0)\n      }% success rate)`,\n      `Maintain pattern quality >${qualityThreshold.toFixed(1)} (your success threshold)`,\n    ];\n\n    return {\n      sessions,\n      currentStatus: {\n        currentTime,\n        currentTimeFormatted,\n        activeSession,\n        nextSession,\n        timeToNext,\n        timeToNextFormatted,\n        isOptimalWindow,\n        currentRecommendation,\n        urgency,\n      },\n      weeklyInsights: {\n        bestSession,\n        bestModel,\n        avgQuality,\n        qualityThreshold,\n        recommendations: weeklyRecommendations,\n      },\n    };\n  }, [trades]);\n\n  return {\n    intelligence,\n    isLoading,\n    error,\n    refresh: () => {\n      setTrades([]);\n    },\n  };\n};\n", "/**\n * Enhanced Session Focus Component\n *\n * Advanced ICT session intelligence using real trading data from spreadsheet import.\n * Provides sophisticated PD Array and model-specific analysis with ADHD-optimized format.\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { Card, DualTimeDisplay } from '@adhd-trading-dashboard/shared';\nimport { useSessionAnalytics, CurrentSessionRecommendation } from '../hooks/useSessionAnalytics';\nimport { useEnhancedSessionIntelligence } from '../hooks/useEnhancedSessionIntelligence';\n\nexport interface SessionFocusProps {\n  /** Whether the component is in a loading state */\n  isLoading?: boolean;\n  /** The error message, if any */\n  error?: string | null;\n  /** Function called when the refresh button is clicked */\n  onRefresh?: () => void;\n  /** Additional class name */\n  className?: string;\n}\n\n// Styled components with F1 theme\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.md || '16px'};\n`;\n\nconst CurrentSessionCard = styled.div.attrs({\n  className: 'session-card current-session-card',\n})`\n  /* Use CSS variables for theming */\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-accent);\n  border-left: 4px solid var(--session-card-accent);\n  border-radius: 12px;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n  box-shadow: var(--shadow-md), 0 0 0 1px var(--session-card-accent);\n\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: linear-gradient(\n      90deg,\n      var(--session-card-accent),\n      var(--session-card-active-accent),\n      var(--session-card-accent)\n    );\n    animation: pulse 2s ease-in-out infinite;\n  }\n\n  @keyframes pulse {\n    0%,\n    100% {\n      opacity: 1;\n    }\n    50% {\n      opacity: 0.7;\n    }\n  }\n`;\n\nconst SessionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n`;\n\nconst SessionTitle = styled.h3`\n  color: var(--session-text-primary);\n  font-size: 18px;\n  font-weight: 600;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n`;\n\nconst LiveIndicatorSpan = styled.span`\n  background: ${({ theme }) =>\n    `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 10px;\n  font-weight: 700;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  animation: f1Pulse 2s ease-in-out infinite;\n  box-shadow: 0 0 8px ${({ theme }) => theme.colors.sessionActive}40;\n\n  @keyframes f1Pulse {\n    0%,\n    100% {\n      opacity: 1;\n      transform: scale(1);\n    }\n    50% {\n      opacity: 0.8;\n      transform: scale(1.05);\n    }\n  }\n`;\n\nconst RecommendationBadge = styled.div<{ level: CurrentSessionRecommendation['recommendation'] }>`\n  padding: 6px 12px;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  color: var(--session-text-primary);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  background: ${({ level, theme }) => {\n    switch (level) {\n      case 'high':\n        // F1 Racing: Green flag for high recommendation\n        return `linear-gradient(135deg, ${theme.colors.sessionActive}, ${theme.colors.sessionOptimal})`;\n      case 'medium':\n        // F1 Racing: Yellow flag for caution/medium\n        return `linear-gradient(135deg, ${theme.colors.sessionCaution}, ${theme.colors.sessionTransition})`;\n      case 'low':\n        // F1 Racing: Orange flag for poor conditions\n        return `linear-gradient(135deg, ${theme.colors.sessionTransition}, ${theme.colors.performancePoor})`;\n      case 'avoid':\n        // F1 Racing: Red flag for danger/avoid\n        return `linear-gradient(135deg, ${theme.colors.performanceAvoid}, ${theme.colors.error})`;\n      default:\n        return 'var(--session-card-border)';\n    }\n  }};\n\n  /* F1 Racing: Add racing stripe for high recommendations */\n  ${({ level }) =>\n    level === 'high' &&\n    `\n    position: relative;\n    &::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 0;\n      height: 100%;\n      width: 3px;\n      background: linear-gradient(180deg, var(--session-optimal), var(--session-active));\n      border-radius: 6px 0 0 6px;\n    }\n  `}\n`;\n\nconst MetricsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n  gap: 16px;\n  margin-bottom: 16px;\n`;\n\nconst MetricCard = styled.div`\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-radius: 6px;\n  padding: 12px;\n  text-align: center;\n  box-shadow: var(--shadow-sm);\n`;\n\nconst MetricValue = styled.div`\n  font-size: 20px;\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-bottom: 4px;\n`;\n\nconst MetricLabel = styled.div`\n  font-size: 12px;\n  color: ${({ theme }) => theme.colors.textSecondary};\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst ActionItems = styled.div`\n  background: ${({ theme }) =>\n    `linear-gradient(135deg, ${theme.colors.sessionActive}20, ${theme.colors.sessionOptimal}10)`};\n  border: 2px solid ${({ theme }) => theme.colors.sessionActive};\n  border-left: 6px solid ${({ theme }) => theme.colors.sessionActive};\n  border-radius: 8px;\n  padding: 16px;\n  position: relative;\n\n  /* Mercedes F1 racing stripe accent */\n  &::before {\n    content: '';\n    position: absolute;\n    left: 0;\n    top: 0;\n    height: 100%;\n    width: 4px;\n    background: linear-gradient(\n      180deg,\n      ${({ theme }) => theme.colors.sessionOptimal},\n      ${({ theme }) => theme.colors.sessionActive}\n    );\n    border-radius: 8px 0 0 8px;\n  }\n`;\n\nconst ActionTitle = styled.h4`\n  color: ${({ theme }) => theme.colors.sessionActive};\n  font-size: 14px;\n  font-weight: 700;\n  margin: 0 0 12px 0;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;\n`;\n\nconst ActionList = styled.ul`\n  margin: 0;\n  padding: 0;\n  list-style: none;\n`;\n\nconst ActionItem = styled.li`\n  color: ${({ theme }) => theme.colors.textPrimary};\n  font-size: 14px;\n  margin-bottom: 8px;\n  padding-left: 20px;\n  position: relative;\n\n  &::before {\n    content: '▶';\n    position: absolute;\n    left: 0;\n    color: ${({ theme }) => theme.colors.sessionActive};\n    font-size: 12px;\n    text-shadow: 0 0 4px ${({ theme }) => theme.colors.sessionActive}40;\n  }\n\n  &:last-child {\n    margin-bottom: 0;\n  }\n`;\n\n// Unused styled components removed to fix TypeScript errors\n\n// Enhanced ICT Session Components with F1 Racing Theme\nconst ICTSessionCard = styled.div.attrs<{ isActive: boolean; isOptimal: boolean }>(\n  ({ isActive, isOptimal }) => ({\n    className: `session-card ict-session-card ${isActive ? 'active' : ''} ${\n      isOptimal ? 'optimal' : ''\n    }`,\n    'data-session-state':\n      isActive && isOptimal ? 'active-optimal' : isActive ? 'active' : 'inactive',\n  })\n)<{ isActive: boolean; isOptimal: boolean }>`\n  /* Use CSS variables for clean theming */\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-left: 4px solid var(--session-card-accent);\n  border-radius: 12px;\n  padding: 16px;\n  margin-bottom: 16px;\n  transition: all 0.2s ease;\n  box-shadow: var(--shadow-sm);\n\n  &[data-session-state='active'] {\n    border-left-color: var(--session-active);\n    border-color: var(--session-active);\n    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-active);\n  }\n\n  &[data-session-state='active-optimal'] {\n    border-left-color: var(--session-optimal);\n    border-color: var(--session-optimal);\n    box-shadow: var(--shadow-md), 0 0 0 1px var(--session-optimal), var(--shadow-accent);\n  }\n`;\n\nconst ICTSessionHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n`;\n\nconst ICTSessionName = styled.div<{ isActive: boolean }>`\n  font-size: 16px;\n  font-weight: 700;\n  color: ${({ isActive }) => (isActive ? 'var(--session-active)' : 'var(--session-text-primary)')};\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  text-shadow: none;\n`;\n\nconst ICTTimeRange = styled.div`\n  font-size: 12px;\n  color: var(--session-text-secondary);\n`;\n\nconst ICTPerformanceGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin: 12px 0;\n`;\n\nconst ICTMetric = styled.div`\n  text-align: center;\n`;\n\nconst ICTMetricValue = styled.div`\n  font-size: 18px;\n  font-weight: 700;\n  color: var(--session-text-primary);\n`;\n\nconst ICTMetricLabel = styled.div`\n  font-size: 10px;\n  color: var(--session-text-secondary);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst ModelPreferenceHeader = styled.div`\n  font-size: 12px;\n  color: var(--session-text-secondary);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  margin-bottom: 8px;\n  font-weight: 600;\n`;\n\nconst OptimalWindowsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n  margin-top: 8px;\n`;\n\nconst WindowCard = styled.div.attrs<{ isOptimal: boolean }>(({ isOptimal }) => ({\n  className: `window-card optimal-window-card ${isOptimal ? 'optimal' : ''}`,\n}))<{ isOptimal: boolean }>`\n  background: var(--session-card-bg);\n  border: 1px solid var(--session-card-border);\n  border-radius: 6px;\n  padding: 8px;\n  text-align: center;\n  transition: all 0.2s ease;\n  box-shadow: var(--shadow-sm);\n\n  ${({ isOptimal }) =>\n    isOptimal &&\n    `\n    border-left: 3px solid var(--session-optimal);\n    border-color: var(--session-optimal);\n    box-shadow:\n      var(--shadow-sm),\n      0 0 0 1px var(--session-optimal);\n  `}\n`;\n\nconst WindowTime = styled.div.attrs({\n  className: 'primary-text session-title',\n})`\n  font-size: 11px;\n  font-weight: 600;\n  color: var(--session-text-primary);\n`;\n\nconst WindowDescription = styled.div.attrs({\n  className: 'secondary-text session-description',\n})`\n  font-size: 9px;\n  color: var(--session-text-secondary);\n  margin: 2px 0;\n`;\n\nconst WindowStats = styled.div.attrs({\n  className: 'secondary-text metric-label',\n})`\n  font-size: 9px;\n  color: var(--session-text-secondary);\n`;\n\n// LiveIndicator styled component removed as unused\n\nconst WeeklyInsightsCard = styled.div`\n  background: var(--session-card-bg);\n  border: 1px solid var(--secondary-color);\n  border-left: 4px solid var(--secondary-color);\n  border-radius: 12px;\n  padding: 16px;\n  margin-top: 16px;\n  box-shadow: var(--shadow-sm);\n`;\n\nconst InsightsHeader = styled.div`\n  font-size: 14px;\n  font-weight: 700;\n  color: var(--secondary-color);\n  margin-bottom: 12px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n`;\n\nconst InsightsList = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n`;\n\nconst InsightItem = styled.div`\n  font-size: 12px;\n  color: var(--session-text-secondary);\n  padding: 6px 0;\n  border-bottom: 1px solid var(--session-card-border);\n\n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\n/**\n * Session Focus Component\n */\nexport const SessionFocus: React.FC<SessionFocusProps> = ({\n  isLoading = false,\n  error = null,\n  onRefresh,\n  className,\n}) => {\n  const { isLoading: analyticsLoading, error: analyticsError } = useSessionAnalytics();\n  const {\n    intelligence,\n    isLoading: enhancedLoading,\n    error: enhancedError,\n  } = useEnhancedSessionIntelligence();\n\n  const loading = isLoading || analyticsLoading || enhancedLoading;\n  const displayError = error || analyticsError || enhancedError;\n\n  // Loading state\n  if (loading) {\n    return (\n      <Card title='🎯 Session Focus'>\n        <div style={{ padding: '24px', textAlign: 'center' }}>\n          Analyzing your trading sessions...\n        </div>\n      </Card>\n    );\n  }\n\n  // Error state\n  if (displayError) {\n    return (\n      <Card title='🎯 Session Focus'>\n        <div style={{ padding: '24px', textAlign: 'center', color: 'var(--error-color)' }}>\n          Error: {displayError}\n          {onRefresh && (\n            <button\n              onClick={onRefresh}\n              style={{\n                marginLeft: '16px',\n                padding: '8px 16px',\n                background: 'var(--session-card-bg)',\n                border: '1px solid var(--session-card-border)',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                color: 'var(--session-text-primary)',\n              }}\n            >\n              Retry\n            </button>\n          )}\n        </div>\n      </Card>\n    );\n  }\n\n  // Unused destructured variables removed\n\n  return (\n    <Card\n      title='🕘 Enhanced Session Intelligence'\n      actions={\n        onRefresh ? (\n          <button\n            onClick={onRefresh}\n            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}\n          >\n            🔄 Refresh\n          </button>\n        ) : undefined\n      }\n    >\n      <Container className={className}>\n        {/* Current Session Status */}\n        <CurrentSessionCard>\n          <SessionHeader>\n            <SessionTitle>\n              {intelligence.currentStatus.currentRecommendation}\n              {intelligence.currentStatus.activeSession && (\n                <LiveIndicatorSpan>LIVE</LiveIndicatorSpan>\n              )}\n            </SessionTitle>\n            <RecommendationBadge\n              level={\n                intelligence.currentStatus.urgency.toLowerCase() as\n                  | 'high'\n                  | 'medium'\n                  | 'low'\n                  | 'avoid'\n              }\n            >\n              {intelligence.currentStatus.urgency}\n            </RecommendationBadge>\n          </SessionHeader>\n\n          <MetricsGrid>\n            <MetricCard>\n              <MetricValue>\n                <DualTimeDisplay mode='current' format='compact' showLive={true} />\n              </MetricValue>\n              <MetricLabel>Current Time</MetricLabel>\n            </MetricCard>\n            <MetricCard>\n              <MetricValue>\n                {intelligence.currentStatus.activeSession?.sessionName || 'None'}\n              </MetricValue>\n              <MetricLabel>Active Session</MetricLabel>\n            </MetricCard>\n            <MetricCard>\n              <MetricValue>{intelligence.currentStatus.timeToNextFormatted}</MetricValue>\n              <MetricLabel>Next Session</MetricLabel>\n            </MetricCard>\n          </MetricsGrid>\n        </CurrentSessionCard>\n\n        {/* Live Session Intelligence */}\n        {intelligence.currentStatus.activeSession && (\n          <ICTSessionCard isActive={true} isOptimal={true}>\n            <ICTSessionHeader>\n              <ICTSessionName isActive={true}>\n                ⏰ LIVE SESSION INTELLIGENCE\n                <LiveIndicatorSpan>LIVE SESSION</LiveIndicatorSpan>\n              </ICTSessionName>\n            </ICTSessionHeader>\n\n            <div style={{ marginBottom: '16px' }}>\n              <h4\n                style={{\n                  color: 'var(--session-text-primary)',\n                  marginBottom: '12px',\n                  fontSize: '16px',\n                }}\n              >\n                {intelligence.currentStatus.activeSession.sessionName} - Active\n              </h4>\n\n              {/* Session Windows Display */}\n              <div style={{ display: 'grid', gap: '12px' }}>\n                {intelligence.sessions\n                  .find(\n                    s => s.sessionName === intelligence.currentStatus.activeSession?.sessionName\n                  )\n                  ?.optimalWindows.map((window, index) => (\n                    <WindowCard key={index} isOptimal={window.winRate >= 70 && window.trades >= 2}>\n                      <WindowTime>\n                        <DualTimeDisplay\n                          mode='session'\n                          sessionStart={window.start}\n                          sessionEnd={window.end}\n                          format='compact'\n                        />\n                      </WindowTime>\n                      <WindowDescription>{window.description}</WindowDescription>\n                      <WindowStats>\n                        {window.trades > 0\n                          ? `${window.winRate.toFixed(0)}% win rate (${window.trades} trades)`\n                          : 'No data'}\n                      </WindowStats>\n                    </WindowCard>\n                  ))}\n              </div>\n            </div>\n          </ICTSessionCard>\n        )}\n\n        {/* ICT Session Analysis */}\n        {intelligence.sessions.map(session => {\n          const isActive =\n            intelligence.currentStatus.activeSession?.sessionName === session.sessionName;\n          const hasOptimalWindow = session.optimalWindows.some(w => w.winRate >= 70);\n\n          return (\n            <ICTSessionCard\n              key={session.sessionName}\n              isActive={isActive}\n              isOptimal={hasOptimalWindow}\n            >\n              <ICTSessionHeader>\n                <ICTSessionName isActive={isActive}>\n                  {session.sessionName}\n                  {isActive && <LiveIndicatorSpan>ACTIVE</LiveIndicatorSpan>}\n                </ICTSessionName>\n                <ICTTimeRange>\n                  {session.timeRange ? (\n                    <DualTimeDisplay\n                      mode='session'\n                      sessionStart={session.timeRange.split('-')[0]}\n                      sessionEnd={session.timeRange.split('-')[1]}\n                      format='compact'\n                    />\n                  ) : (\n                    <span>Time range not available</span>\n                  )}\n                </ICTTimeRange>\n              </ICTSessionHeader>\n\n              <ICTPerformanceGrid>\n                <ICTMetric>\n                  <ICTMetricValue>{session.performance.totalTrades}</ICTMetricValue>\n                  <ICTMetricLabel>Trades</ICTMetricLabel>\n                </ICTMetric>\n                <ICTMetric>\n                  <ICTMetricValue>{session.performance.winRate.toFixed(0)}%</ICTMetricValue>\n                  <ICTMetricLabel>Win Rate</ICTMetricLabel>\n                </ICTMetric>\n                <ICTMetric>\n                  <ICTMetricValue>{session.performance.avgRMultiple.toFixed(1)}R</ICTMetricValue>\n                  <ICTMetricLabel>Avg R-Multiple</ICTMetricLabel>\n                </ICTMetric>\n                <ICTMetric>\n                  <ICTMetricValue>{session.performance.avgRisk.toFixed(0)}</ICTMetricValue>\n                  <ICTMetricLabel>Avg Risk (pts)</ICTMetricLabel>\n                </ICTMetric>\n              </ICTPerformanceGrid>\n\n              {/* Optimal Windows */}\n              <div>\n                <ModelPreferenceHeader>Optimal Time Windows</ModelPreferenceHeader>\n                <OptimalWindowsGrid>\n                  {session.optimalWindows.map((window, index) => (\n                    <WindowCard key={index} isOptimal={window.winRate >= 70 && window.trades >= 2}>\n                      <WindowTime>\n                        <DualTimeDisplay\n                          mode='session'\n                          sessionStart={window.start}\n                          sessionEnd={window.end}\n                          format='compact'\n                        />\n                      </WindowTime>\n                      <WindowDescription>{window.description}</WindowDescription>\n                      <WindowStats>\n                        {window.trades > 0\n                          ? `${window.winRate.toFixed(0)}% (${window.trades} trades)`\n                          : 'No data'}\n                      </WindowStats>\n                    </WindowCard>\n                  ))}\n                </OptimalWindowsGrid>\n              </div>\n\n              {/* Session Recommendations */}\n              {session.recommendations.length > 0 && (\n                <ActionItems>\n                  <ActionTitle>Session Recommendations</ActionTitle>\n                  <ActionList>\n                    {session.recommendations.map((rec, index) => (\n                      <ActionItem key={index}>{rec}</ActionItem>\n                    ))}\n                  </ActionList>\n                </ActionItems>\n              )}\n            </ICTSessionCard>\n          );\n        })}\n\n        {/* Weekly Insights */}\n        <WeeklyInsightsCard>\n          <InsightsHeader>📊 Weekly Performance Insights</InsightsHeader>\n          <InsightsList>\n            <InsightItem>\n              <strong>Best Session:</strong> {intelligence.weeklyInsights.bestSession}\n            </InsightItem>\n            <InsightItem>\n              <strong>Best Model:</strong> {intelligence.weeklyInsights.bestModel}\n            </InsightItem>\n            <InsightItem>\n              <strong>Average Quality:</strong> {intelligence.weeklyInsights.avgQuality.toFixed(1)}\n              /5.0\n            </InsightItem>\n            <InsightItem>\n              <strong>Quality Threshold:</strong>{' '}\n              {`>${intelligence.weeklyInsights.qualityThreshold.toFixed(1)}`} for optimal\n              performance\n            </InsightItem>\n          </InsightsList>\n          <ActionItems style={{ marginTop: '12px' }}>\n            <ActionTitle>Key Recommendations</ActionTitle>\n            <ActionList>\n              {intelligence.weeklyInsights.recommendations.map((rec, index) => (\n                <ActionItem key={index}>{rec}</ActionItem>\n              ))}\n            </ActionList>\n          </ActionItems>\n        </WeeklyInsightsCard>\n      </Container>\n    </Card>\n  );\n};\n", "/**\n * Guide Tab Configuration\n *\n * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)\n * Centralized configuration for guide tabs and their content.\n *\n * BENEFITS:\n * - Single source of truth for tab definitions\n * - Easy to maintain and extend\n * - Type-safe tab configurations\n * - Reusable across different guide views\n * - Clear content mapping\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { EliteIntelligenceLayout } from './EliteIntelligenceLayout';\nimport { GuideTab } from './F1GuideTabs';\nimport { PDArrayLevels } from './PDArrayLevels';\nimport { SessionFocus } from './SessionFocus';\n\nexport interface GuideTabConfig {\n  id: GuideTab;\n  title: string;\n  description: string;\n  icon: string;\n  component: React.ComponentType<any>;\n  showInMobile: boolean;\n  requiresData: boolean;\n}\n\nexport interface GuideTabContentProps {\n  /** Current active tab */\n  activeTab: GuideTab;\n  /** Guide data */\n  data: {\n    marketOverview: any;\n    tradingPlan: any;\n    keyPriceLevels: any;\n    selectedDate: string;\n    currentDate: string;\n  };\n  /** Loading state */\n  isLoading: boolean;\n  /** Error state */\n  error: string | null;\n  /** Action handlers */\n  handlers: {\n    onDateChange: (date: string) => void;\n    onTradingPlanItemToggle: (id: string, completed: boolean) => void;\n    onAddTradingPlanItem: (item: any) => void;\n    onRemoveTradingPlanItem: (id: string) => void;\n    onRefresh: () => void;\n  };\n}\n\nconst EmptyState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 300px;\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n`;\n\nconst EmptyIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n`;\n\nconst EmptyTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst EmptyMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst NewsContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst NewsCard = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst NewsTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst NewsTime = styled.span`\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst NewsContent = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: ${({ theme }) => theme.spacing?.sm || '8px'} 0 0 0;\n  line-height: 1.6;\n`;\n\n/**\n * Session Focus Tab Content\n */\nconst OverviewTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {\n  if (error) {\n    return (\n      <EmptyState>\n        <EmptyIcon>⚠️</EmptyIcon>\n        <EmptyTitle>Error Loading Session Analytics</EmptyTitle>\n        <EmptyMessage>{error}</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  return <SessionFocus isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />;\n};\n\n/**\n * Trading Plan Tab Content - Now Elite ICT Intelligence System\n */\nconst PlanTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {\n  if (error) {\n    return (\n      <EmptyState>\n        <EmptyIcon>⚠️</EmptyIcon>\n        <EmptyTitle>Error Loading Elite ICT Intelligence</EmptyTitle>\n        <EmptyMessage>{error}</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  return (\n    <EliteIntelligenceLayout isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />\n  );\n};\n\n/**\n * PD Array Levels Tab Content - ICT-Focused Intelligence\n */\nconst LevelsTabContent: React.FC<GuideTabContentProps> = ({ isLoading, error, handlers }) => {\n  if (error) {\n    return (\n      <EmptyState>\n        <EmptyIcon>⚠️</EmptyIcon>\n        <EmptyTitle>Error Loading PD Array Intelligence</EmptyTitle>\n        <EmptyMessage>{error}</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  return <PDArrayLevels isLoading={isLoading} error={error} onRefresh={handlers.onRefresh} />;\n};\n\n/**\n * Market News Tab Content\n */\nconst NewsTabContent: React.FC<GuideTabContentProps> = ({ isLoading }) => {\n  if (isLoading) {\n    return (\n      <EmptyState>\n        <EmptyIcon>📰</EmptyIcon>\n        <EmptyTitle>Loading Market News</EmptyTitle>\n        <EmptyMessage>Fetching the latest market updates...</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  // Mock news data for now\n  const mockNews = [\n    {\n      id: 1,\n      title: 'Federal Reserve Maintains Interest Rates',\n      time: '2 hours ago',\n      content:\n        'The Federal Reserve announced it will maintain current interest rates, citing ongoing economic stability and controlled inflation metrics.',\n    },\n    {\n      id: 2,\n      title: 'Tech Sector Shows Strong Pre-Market Activity',\n      time: '4 hours ago',\n      content:\n        'Major technology stocks are showing positive momentum in pre-market trading, with several companies reporting better-than-expected earnings.',\n    },\n    {\n      id: 3,\n      title: 'Oil Prices Stabilize After Recent Volatility',\n      time: '6 hours ago',\n      content:\n        'Crude oil prices have stabilized following recent geopolitical tensions, with WTI trading within expected ranges.',\n    },\n  ];\n\n  return (\n    <NewsContainer>\n      {mockNews.map(news => (\n        <NewsCard key={news.id}>\n          <NewsTitle>{news.title}</NewsTitle>\n          <NewsTime>{news.time}</NewsTime>\n          <NewsContent>{news.content}</NewsContent>\n        </NewsCard>\n      ))}\n    </NewsContainer>\n  );\n};\n\n/**\n * Tab configuration with components and metadata\n */\nexport const GUIDE_TAB_CONFIG: Record<GuideTab, GuideTabConfig> = {\n  overview: {\n    id: 'overview',\n    title: 'Session Focus',\n    description: 'Personalized session analytics and trading recommendations',\n    icon: '🎯',\n    component: OverviewTabContent,\n    showInMobile: true,\n    requiresData: true,\n  },\n  plan: {\n    id: 'plan',\n    title: 'Elite Intelligence',\n    description:\n      'Advanced ICT trading intelligence with model selection, pattern scoring, and session analysis',\n    icon: '🧠',\n    component: PlanTabContent,\n    showInMobile: true,\n    requiresData: false,\n  },\n  levels: {\n    id: 'levels',\n    title: 'PD Array Levels',\n    description: 'ICT PD Array intelligence with FVG, NWOG, RD, and liquidity analysis',\n    icon: '🎯',\n    component: LevelsTabContent,\n    showInMobile: true,\n    requiresData: false,\n  },\n  news: {\n    id: 'news',\n    title: 'Market News',\n    description: 'Latest market news and economic events',\n    icon: '📰',\n    component: NewsTabContent,\n    showInMobile: false,\n    requiresData: false,\n  },\n};\n\n/**\n * Get tab configuration by ID\n */\nexport const getTabConfig = (tabId: GuideTab): GuideTabConfig => {\n  return GUIDE_TAB_CONFIG[tabId];\n};\n\n/**\n * Get all tab configurations\n */\nexport const getAllTabConfigs = (): GuideTabConfig[] => {\n  return Object.values(GUIDE_TAB_CONFIG);\n};\n\n/**\n * Get mobile-friendly tabs\n */\nexport const getMobileTabConfigs = (): GuideTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.showInMobile);\n};\n\n/**\n * Get tabs that require data\n */\nexport const getDataRequiredTabConfigs = (): GuideTabConfig[] => {\n  return getAllTabConfigs().filter(config => config.requiresData);\n};\n\n/**\n * Tab Content Renderer Component\n */\nexport const GuideTabContentRenderer: React.FC<GuideTabContentProps> = props => {\n  const { activeTab } = props;\n  const config = getTabConfig(activeTab);\n\n  if (!config) {\n    return (\n      <EmptyState>\n        <EmptyIcon>❌</EmptyIcon>\n        <EmptyTitle>Unknown Tab</EmptyTitle>\n        <EmptyMessage>Tab \"{activeTab}\" not found.</EmptyMessage>\n      </EmptyState>\n    );\n  }\n\n  const TabComponent = config.component;\n\n  return (\n    <div id={`guide-panel-${activeTab}`} role='tabpanel' aria-labelledby={`guide-tab-${activeTab}`}>\n      <TabComponent {...props} />\n    </div>\n  );\n};\n\nexport default GuideTabContentRenderer;\n", "/**\n * F1GuideContainer Component\n * \n * REFACTORED FROM: DailyGuide.tsx (158 lines → focused components)\n * Main orchestrator for daily guide with F1 container pattern.\n * \n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and loading states\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense } from 'react';\nimport styled from 'styled-components';\nimport { useDailyGuide } from '../hooks';\nimport { F1GuideHeader } from './F1GuideHeader';\nimport { F1GuideTabs } from './F1GuideTabs';\nimport { useGuideNavigation } from './useGuideNavigation';\nimport { GuideTabContentRenderer } from './guideTabConfig';\n\nexport interface F1GuideContainerProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'overview' | 'plan' | 'levels' | 'news';\n  /** Custom title */\n  title?: string;\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  min-height: 100vh;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  max-width: 1400px;\n  margin: 0 auto;\n`;\n\nconst ContentArea = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  flex: 1;\n`;\n\nconst TabContentContainer = styled.div`\n  animation: fadeIn 0.3s ease-in-out;\n  \n  @keyframes fadeIn {\n    from {\n      opacity: 0;\n      transform: translateY(10px);\n    }\n    to {\n      opacity: 1;\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n`;\n\nconst LoadingIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0%, 100% { opacity: 0.7; }\n    50% { opacity: 0.3; }\n  }\n`;\n\nconst LoadingText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '48px'};\n  text-align: center;\n  min-height: 400px;\n  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;\n  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n`;\n\nconst ErrorTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst ErrorMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst RetryButton = styled.button`\n  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  color: white;\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n  }\n`;\n\n/**\n * LoadingFallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <LoadingState>\n    <LoadingIcon>📅</LoadingIcon>\n    <LoadingText>Loading Daily Guide...</LoadingText>\n  </LoadingState>\n);\n\n/**\n * ErrorFallback Component\n */\nconst ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (\n  <ErrorState>\n    <ErrorIcon>⚠️</ErrorIcon>\n    <ErrorTitle>Guide Error</ErrorTitle>\n    <ErrorMessage>{error}</ErrorMessage>\n    <RetryButton onClick={onRetry}>\n      Try Again\n    </RetryButton>\n  </ErrorState>\n);\n\n/**\n * GuideContent Component\n */\nconst GuideContent: React.FC<F1GuideContainerProps> = ({ initialTab, title }) => {\n  const {\n    selectedDate,\n    marketOverview,\n    tradingPlan,\n    keyPriceLevels,\n    isLoading,\n    error,\n    currentDate,\n    onDateChange,\n    onTradingPlanItemToggle,\n    onAddTradingPlanItem,\n    onRemoveTradingPlanItem,\n    onRefresh,\n  } = useDailyGuide();\n  \n  const { activeTab, setActiveTab } = useGuideNavigation({\n    defaultTab: initialTab || 'overview',\n  });\n  \n  // Prepare data and handlers for tab content\n  const tabContentProps = {\n    activeTab,\n    data: {\n      marketOverview,\n      tradingPlan,\n      keyPriceLevels,\n      selectedDate,\n      currentDate,\n    },\n    isLoading,\n    error,\n    handlers: {\n      onDateChange,\n      onTradingPlanItemToggle,\n      onAddTradingPlanItem,\n      onRemoveTradingPlanItem,\n      onRefresh,\n    },\n  };\n  \n  if (error) {\n    return <ErrorFallback error={error} onRetry={onRefresh} />;\n  }\n  \n  return (\n    <Container>\n      {/* F1 Racing Header */}\n      <F1GuideHeader\n        isLoading={isLoading}\n        currentDate={currentDate}\n        selectedDate={selectedDate}\n        onDateChange={onDateChange}\n        onRefresh={onRefresh}\n        title={title}\n      />\n      \n      {/* F1 Racing Tabs */}\n      <F1GuideTabs\n        activeTab={activeTab}\n        onTabChange={setActiveTab}\n        disabled={isLoading}\n      />\n      \n      {/* Tab Content */}\n      <ContentArea>\n        <TabContentContainer>\n          <Suspense fallback={<LoadingFallback />}>\n            <GuideTabContentRenderer {...tabContentProps} />\n          </Suspense>\n        </TabContentContainer>\n      </ContentArea>\n    </Container>\n  );\n};\n\n/**\n * F1GuideContainer Component\n * \n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const F1GuideContainer: React.FC<F1GuideContainerProps> = (props) => {\n  return (\n    <Suspense fallback={<LoadingFallback />}>\n      <GuideContent {...props} />\n    </Suspense>\n  );\n};\n\nexport default F1GuideContainer;\n", "/**\n * Daily Guide Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 158 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 95% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { F1GuideContainer } from './F1GuideContainer';\nimport { DailyGuideProvider } from '../state/dailyGuideState';\n\nexport interface DailyGuideProps {\n  /** Custom className */\n  className?: string;\n  /** Initial tab to display */\n  initialTab?: 'overview' | 'plan' | 'levels' | 'news';\n  /** Custom title */\n  title?: string;\n}\n\n/**\n * Daily Guide Component\n *\n * Simple wrapper that renders the container with context provider.\n * Follows the proven architecture pattern.\n */\nexport const DailyGuide: React.FC<DailyGuideProps> = ({ className, initialTab, title }) => {\n  return (\n    <DailyGuideProvider>\n      <F1GuideContainer className={className} initialTab={initialTab} title={title} />\n    </DailyGuideProvider>\n  );\n};\n\nexport default DailyGuide;\n"], "names": ["getUserTimezone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "getTimezoneAbbreviation", "timezone", "date", "Date", "timeZonePart", "timeZoneName", "formatToParts", "find", "part", "type", "value", "convertNYToLocal", "nyTime", "userTimezone", "hours", "minutes", "split", "map", "Number", "today", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "timeStr", "isoString", "localDate", "nowLocal", "nowInNY", "toLocaleString", "offsetMs", "getTime", "adjustedDate", "toLocaleTimeString", "hour", "minute", "hour12", "getCurrentDualTime", "now", "localTime", "nyTimezone", "localTimezone", "formatted", "getCurrentNYMinutes", "timeToMinutes", "formatTimeInterval", "totalMinutes", "Math", "floor", "getTimeUntilNYTime", "targetNYTime", "nyTimeString", "nyNow", "targetTime", "setHours", "setDate", "diffMs", "diffMinutes", "convertSessionToDualTime", "nyStart", "nyEnd", "localStart", "localEnd", "localTimezoneAbbr", "formatTimeForMobile", "dualTime", "localFlag", "includes", "TimeContainer", "div", "withConfig", "displayName", "componentId", "format", "NYTime", "span", "LocalTime", "Separator", "Timezone", "LiveIndicator", "CountdownContainer", "CountdownValue", "CountdownLabel", "CurrentTimeDisplay", "showLive", "updateInterval", "setDualTime", "useState", "useEffect", "timer", "setInterval", "clearInterval", "jsxs", "jsx", "StaticTimeDisplay", "sessionTime", "CountdownDisplay", "timeUntil", "setTimeUntil", "SessionTimeDisplay", "sessionStart", "sessionEnd", "DualTimeDisplay", "props", "mode", "className", "containerProps", "style", "fontSize", "console", "warn", "createStoreContext", "reducer", "initialState", "Context", "createContext", "undefined", "Provider", "children", "overrideInitialState", "state", "dispatch", "useReducer", "useMemo", "useStore", "context", "useContext", "Error", "useSelector", "selector", "useAction", "actionCreator", "args", "useActions", "actionCreators", "boundActionCreators", "key", "createSelector", "resultFunc", "pop", "inputSelectors", "lastInputs", "lastResult", "inputs", "length", "some", "input", "index", "persistState", "options", "version", "migrate", "serialize", "JSON", "stringify", "deserialize", "parse", "filter", "merge", "persistedState", "debug", "loadState", "serializedState", "localStorage", "getItem", "persistedVersion", "log", "err", "error", "saveState", "filteredState", "setItem", "clear", "removeItem", "mergedInitialState", "persistedReducer", "action", "newState", "initialDailyGuideState", "data", "marketOverview", "tradingPlan", "keyPriceLevels", "watchlist", "marketNews", "isLoading", "selectedDate", "toISOString", "dailyGuideReducer", "DailyGuideActionTypes", "payload", "items", "item", "id", "completed", "strategy", "riskManagement", "maxRiskPerTrade", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxTrades", "positionSizing", "notes", "persistedInitialState", "DailyGuideContext", "DailyGuideProvider", "useDailyGuideStore", "useDailyGuideSelector", "useDailyGuideAction", "useDailyGuideActions", "dailyGuideActions", "fetchDataStart", "fetchDataSuccess", "fetchDataError", "updateTradingPlanItem", "addTradingPlanItem", "removeTradingPlanItem", "updateSelectedDate", "updateMarketOverview", "overview", "updateKeyPriceLevels", "levels", "resetState", "selectDailyGuideData", "selectMarketOverview", "selectTradingPlan", "selectKeyPriceLevels", "selectWatchlist", "selectMarketNews", "selectIsLoading", "selectError", "selectSelectedDate", "selectTradingPlanItems", "selectCompletedTradingPlanItems", "selectIncompleteTradingPlanItems", "selectTradingPlanCompletion", "allItems", "completedItems", "selectTradingPlanItemsByPriority", "result", "high", "medium", "low", "for<PERSON>ach", "priority", "push", "selectMarketIndices", "indices", "selectPositiveIndices", "change", "selectNegativeIndices", "selectMarketSentiment", "sentiment", "selectMarketSummary", "summary", "selectEconomicEvents", "economicEvents", "selectHighImpactEconomicEvents", "events", "event", "importance", "selectKeyPriceLevelsBySymbol", "level", "symbol", "selectWatchlistBySymbol", "selectHighImpactMarketNews", "news", "impact", "selectMarketNewsByDate", "timestamp", "selectHasData", "selectLastUpdated", "lastUpdated", "dailyGuideSelectors", "generateMockData", "mockMarketOverview", "random", "name", "changePercent", "title", "time", "expected", "previous", "actual", "source", "url", "description", "support", "resistance", "pivotPoint", "price", "reason", "setup", "entryPrice", "stopLoss", "takeProfit", "useDailyGuide", "actions", "tradingPlanItems", "tradingPlanCompletion", "marketSentiment", "marketSummary", "currentDate", "toLocaleDateString", "weekday", "fetchData", "useCallback", "Promise", "resolve", "setTimeout", "mockData", "errorMessage", "message", "handleDateChange", "handleTradingPlanItemToggle", "handleAddTradingPlanItem", "handleRemoveTradingPlanItem", "handleRefresh", "onDateChange", "onTradingPlanItemToggle", "onAddTradingPlanItem", "onRemoveTradingPlanItem", "onRefresh", "getCurrentESTHour", "utc", "getTimezoneOffset", "getHours", "getSessionLabel", "getPerformanceLevel", "winRate", "tradeCount", "getRecommendationLevel", "performance", "useSessionAnalytics", "trades", "setTrades", "setIsLoading", "setError", "tradeData", "tradeStorageService", "getAllTrades", "analytics", "sessionPerformance", "currentRecommendation", "currentHour", "<PERSON><PERSON><PERSON><PERSON>", "recommendation", "bestSetups", "riskLevel", "actionItems", "bestPerformingHours", "worstPerformingHours", "totalAnalyzedTrades", "tradesByHour", "trade", "entry_time", "parseInt", "hourTrades", "winningTrades", "t", "win_loss", "avgRMultiple", "reduce", "sum", "r_multiple", "totalPnL", "achieved_pl", "setupCounts", "model_type", "bestSetup", "Object", "keys", "a", "b", "label", "totalTrades", "currentSession", "currentHourTrades", "setupWinRates", "wins", "total", "entries", "_", "stats", "sort", "__", "slice", "sortedByPerformance", "s", "refresh", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "theme", "spacing", "lg", "xl", "<PERSON><PERSON><PERSON><PERSON>", "colors", "surface", "border", "borderRadius", "primary", "F1Title", "h1", "fontSizes", "h2", "textPrimary", "GuideIndicator", "xs", "$hasDate", "success", "textSecondary", "sm", "full", "SubHeader", "md", "TitleSection", "SubTitle", "xxl", "DateBadge", "xxs", "ActionsContainer", "DateSelector", "DateInput", "background", "ActionButton", "button", "$variant", "textInverse", "primaryDark", "F1GuideHeader", "isRefreshing", "hasDate", "maxDate", "e", "target", "TabsContainer", "Tab", "$isActive", "$disabled", "TabIcon", "TabLabel", "TAB_CONFIG", "icon", "plan", "F1GuideTabs", "activeTab", "onTabChange", "disabled", "handleTabClick", "tab", "handleKeyDown", "preventDefault", "config", "isActive", "AVAILABLE_TABS", "DEFAULT_STORAGE_KEY", "loadTabFromStorage", "storageKey", "defaultTab", "stored", "saveTabToStorage", "useGuideNavigation", "setActiveTabState", "setActiveTab", "nextTab", "nextIndex", "indexOf", "previousTab", "currentIndex", "previousIndex", "isTabActive", "getTabIndex", "document", "activeElement", "tagName", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "tabIndex", "altKey", "toLowerCase", "addEventListener", "window", "removeEventListener", "availableTabs", "analyzePrimarySetups", "setupMap", "Map", "primarySetup", "primary_setup", "has", "set", "get", "setupPerformances", "setupTrades", "setupName", "r<PERSON><PERSON><PERSON><PERSON>", "r", "qualities", "pattern_quality_rating", "q", "avgQuality", "sessionMap", "session", "sessionData", "bestSession", "bestSessionWinRate", "sessionWinRate", "successRate", "analyzeSetupCombinations", "combinationMap", "secondary", "secondary_setup", "liquidity", "liquidity_taken", "combinations", "comboTrades", "pl", "sessions", "Array", "from", "<PERSON><PERSON><PERSON>", "toFixed", "analyzeLiquidityIntelligence", "liquidityMap", "liquidityIntelligence", "liquidityTrades", "liquidityTarget", "modelMap", "model", "modelData", "bestModels", "bestSessions", "useEnhancedSetupIntelligence", "setupIntelligence", "topPrimarySetups", "topSecondarySetups", "bestCombinations", "currentRecommendations", "secondarySetup", "reasoning", "expectedWinRate", "expectedRMultiple", "bestCombo", "SESSION_WINDOWS", "NY_OPEN", "start", "end", "LUNCH_MACRO", "MOC", "PRE_MARKET", "getCurrentTime", "toString", "getMinutes", "isTimeInWindow", "currentTime", "current", "getTimeRemaining", "max", "extractSession", "entryTime", "timeMinutes", "analyzeSession", "sessionType", "windows", "sessionTrades", "windowAnalysis", "windowTrades", "rdContTrades", "fvgRdTrades", "rdContWinRate", "fvgRdWinRate", "modelPreference", "isOptimal", "isPrimary", "windowsWithData", "w", "bestWindow", "best", "worstWindow", "worst", "currentWindow", "timeRemaining", "currentMinutes", "nextWindow", "currentWindowAnalysis", "timeToNext", "overallPerformance", "currentStatus", "useGranularSessionIntelligence", "sessionAnalyses", "liveGuidance", "activeSession", "nextSession", "firstWindow", "timeToNextOptimal", "urgencyLevel", "optimalWindows", "nextOptimal", "calculateVolatility", "mean", "abs", "variance", "pow", "stdDev", "sqrt", "assessLiquidityContext", "recentTrades", "voidCount", "reactionCount", "combined", "determineHTFTrend", "longWins", "direction", "shortWins", "getPreviousSessionSuccess", "yesterday", "rdContWins", "fvgRdWins", "useModelSelectionEngine", "modelStats", "calculateStats", "modelTrades", "recentWins", "recentPerformance", "volatilityPerformance", "avgR", "lowVolTrades", "highVolTrades", "medVolTrades", "volTrades", "rs", "recommendedModel", "probability", "confidence", "alternativeModel", "alternativeCondition", "marketConditions", "volatility", "liquidityContext", "htfTrend", "previousSessionSuccess", "score", "reasons", "rdContStats", "fvgRdStats", "absScore", "min", "join", "extractPDArrayElements", "elements", "setupInfo", "calculatePDArrayConfluence", "elementCount", "assessFVGCharacteristics", "evaluateRDStrength", "rdType", "rd_type", "assessConfirmationSignals", "calculateVolumeConfirmation", "calculatePatternQuality", "breakdown", "pdArrayConfluence", "fvgCharacteristics", "rdStrength", "confirmationSignals", "volumeConfirmation", "totalScore", "values", "normalizedScore", "rating", "expectedWinProbability", "maxScore", "usePatternQualityScoring", "analysis", "currentScore", "historicalAccuracy", "scoreDistribution", "scoredTrades", "actualWin", "accuracyData", "st", "correctPredictions", "tradesInRange", "count", "getCurrentMarketContext", "dayOfWeek", "getDay", "marketHours", "isNewsDay", "newsImpact", "volumeProfile", "calculateSessionBonus", "calculateQualityBonus", "qualityScore", "calculateNewsImpact", "marketContext", "calculateVolumeBonus", "calculateConfluenceBonus", "modelConfidence", "qualityRating", "sessionBonus", "confluenceFactors", "generateRiskManagement", "maxRiskPercent", "stopLossMultiplier", "takeProfitTargets", "useSuccessProbabilityCalculator", "modelRecommendation", "patternQuality", "successProbability", "finalProbability", "average", "baseModelWinRate", "qualityBonus", "volumeBonus", "confluenceBonus", "totalBonus", "modelRMultiples", "PanelContainer", "ExpandableSection", "SectionHeader", "$isExpanded", "SectionTitle", "SectionSummary", "ExpandIcon", "SectionContent", "DetailGrid", "DetailItem", "DetailLabel", "DetailValue", "DetailDescription", "DetailedAnalysisPanel", "expandedSections", "setExpandedSections", "Set", "toggleSection", "sectionId", "newExpanded", "delete", "add", "textAlign", "padding", "color", "isExpanded", "toUpperCase", "replace", "StateContainer", "$isOpen", "StatusDot", "StatusText", "DetailText", "getCurrentMarketState", "timeInMinutes", "marketOpen", "marketClose", "nextMonday", "isOpen", "status", "nextOpen", "openTime", "MarketStateIndicator", "marketState", "showDetails", "getStatusText", "getDetailText", "PanelHeader", "Title", "h3", "QuickGrid", "MarketContextBanner", "$isWeekend", "PrimaryRecommendation", "ModelName", "ConfidenceBadge", "SetupRecommendation", "SetupTitle", "SetupDescription", "SetupStats", "ProbabilitySection", "ProbabilityValue", "$probability", "ProbabilityLabel", "QualityAlert", "$show", "ReasoningBox", "ReasoningTitle", "QuickDecisionPanel", "isWeekend", "showContextBanner", "fontFamily", "lineHeight", "fontWeight", "LayoutContainer", "HeaderRight", "RefreshButton", "ContentContainer", "ExpandedContent", "ExpandToggleButton", "LoadingState", "ErrorState", "LoadingSpinner", "EliteIntelligenceLayout", "setIsExpanded", "modelRec", "modelLoading", "modelError", "qualityAnalysis", "qualityLoading", "qualityError", "sessionLoading", "sessionError", "probabilityLoading", "setupLoading", "setupError", "aggregatedLoading", "aggregatedError", "handleToggleExpansion", "Card", "marginBottom", "opacity", "extractPDArrayInfo", "arrays", "modelType", "details", "calculateAge", "tradeDate", "diffTime", "diffDays", "ceil", "determineTimeframe", "generateLevelString", "arrayType", "entry_price", "exitPrice", "exit_price", "fvgRange", "analyzePDArrayPerformance", "arrayTrades", "arr", "successfulTrades", "generateRecommendation", "age", "baseRec", "performanceNote", "usePDArrayIntelligence", "pdArrayIntelligence", "thirtyDaysAgo", "pdArrayMap", "array", "activePDArrays", "timeframe", "priorityOrder", "HIGH", "MEDIUM", "LOW", "aPriority", "bPriority", "totalActiveLevels", "bestType", "typeArrays", "avgWinRate", "overallSuccessRate", "keyInsights", "highPriorityCount", "bestPerformingType", "Container", "PDArrayCard", "styled", "attrs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ArrayType", "ArrayStatus", "LevelInfo", "LevelDetail", "LevelValue", "LevelLabel", "PerformanceStats", "StatsGrid", "StatItem", "StatValue", "StatLabel", "PriorityBadge", "RecommendationText", "EmptyState", "EmptyIcon", "EmptyTitle", "EmptyMessage", "p", "PDArrayLevels", "intelligenceLoading", "intelligenceError", "loading", "displayError", "marginLeft", "cursor", "parseSession", "calculateOptimalWindows", "tradeMinutes", "startHours", "startMins", "endHours", "endMins", "startMinutes", "endMinutes", "analyzeSessionPerformance", "riskPoints", "risk_points", "avgRisk", "rdContRs", "fvgRdRs", "rdContAvgR", "fvgRdAvgR", "winningQualities", "avgWinningQuality", "qualityThreshold", "recommendations", "timeRange", "rdCont", "fvgRd", "useEnhancedSessionIntelligence", "intelligence", "currentTimeFormatted", "timeToNextFormatted", "isOptimalWindow", "urgency", "weeklyInsights", "bestModel", "sessionGroups", "groups", "todayNext", "diff", "allRdContTrades", "allFvgRdTrades", "allQualities", "weeklyRecommendations", "CurrentSessionCard", "<PERSON><PERSON><PERSON><PERSON>", "SessionTitle", "LiveIndicatorSpan", "sessionActive", "sessionOptimal", "RecommendationBadge", "sessionCaution", "sessionTransition", "performancePoor", "performanceAvoid", "MetricsGrid", "MetricCard", "MetricValue", "MetricLabel", "ActionItems", "ActionTitle", "h4", "ActionList", "ul", "ActionItem", "li", "ICTSessionCard", "ICTSessionHeader", "ICTSessionName", "ICTTimeRange", "ICTPerformanceGrid", "ICTMetric", "ICTMetricValue", "ICTMetricLabel", "ModelPreferenceHeader", "OptimalWindowsGrid", "WindowCard", "WindowTime", "WindowDescription", "WindowStats", "WeeklyInsightsCard", "InsightsHeader", "InsightsList", "InsightItem", "SessionFocus", "analyticsLoading", "analyticsError", "enhancedLoading", "enhancedError", "display", "gap", "hasOptimalWindow", "rec", "marginTop", "NewsContainer", "NewsCard", "NewsTitle", "NewsTime", "NewsContent", "OverviewTabContent", "handlers", "PlanTabContent", "LevelsTabContent", "NewsTabContent", "mockNews", "content", "GUIDE_TAB_CONFIG", "component", "showInMobile", "requiresData", "getTabConfig", "tabId", "GuideTabContent<PERSON><PERSON><PERSON>", "TabComponent", "ContentArea", "TabContentContainer", "LoadingIcon", "LoadingText", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ErrorMessage", "RetryButton", "LoadingFallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRetry", "GuideContent", "initialTab", "tabContentProps", "Suspense", "F1GuideContainer", "DailyGuide"], "mappings": "4NAiCO,MAAMA,GAAkBA,IACtBC,KAAKC,eAAAA,EAAiBC,gBAAAA,EAAkBC,SAMpCC,GAA0BA,CAACC,EAAkBC,EAAa,IAAIC,OAAmB,CAO5F,MAAMC,EANY,IAAIR,KAAKC,eAAe,KAAM,CAC9CE,SAAUE,EACVI,aAAc,OAAA,CACf,EAEuBC,cAAcJ,CAAI,EACfK,KAAeC,GAAAA,EAAKC,OAAS,cAAc,EACtE,OAAOL,GAAAA,YAAAA,EAAcM,QAAST,CAChC,EAKaU,GAAmBA,CAACC,EAAgBC,IAAkC,CAC3EZ,MAAAA,EAAWY,GAAgBlB,KAG3B,CAACmB,EAAOC,CAAO,EAAIH,EAAOI,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAI/CC,MAAYhB,KAIZiB,EAAOD,EAAME,cACbC,EAAQC,OAAOJ,EAAMK,SAAAA,EAAa,CAAC,EAAEC,SAAS,EAAG,GAAG,EACpDC,EAAMH,OAAOJ,EAAMQ,QAAS,CAAA,EAAEF,SAAS,EAAG,GAAG,EAC7CG,EAAU,GAAGL,OAAOT,CAAK,EAAEW,SAAS,EAAG,GAAG,KAAKF,OAAOR,CAAO,EAAEU,SAAS,EAAG,GAAG,OAG9EI,EAAY,GAAGT,KAAQE,KAASI,KAAOE,IAGvCE,EAAY,IAAI3B,KAAK0B,CAAS,EAI9BE,MAAe5B,KACf6B,EAAU,IAAI7B,KAAK4B,EAASE,eAAe,QAAS,CAAElC,SAAU,kBAAoB,CAAA,CAAC,EAIrFmC,EAHa,IAAI/B,KAAK4B,EAASE,eAAe,QAAS,CAAElC,SAAUE,CAAU,CAAA,CAAC,EAGxDkC,QAAQ,EAAIH,EAAQG,QAAQ,EAMjDC,OAHc,IAAIjC,KAAK2B,EAAUK,QAAAA,EAAYD,CAAQ,EAGxCG,mBAAmB,QAAS,CAC9CtC,SAAUE,EACVqC,KAAM,UACNC,OAAQ,UACRC,OAAQ,EAAA,CACT,CACH,EAwBaC,GAAsB5B,GAA2C,CACtEZ,MAAAA,EAAWY,GAAgBlB,KAC3B+C,MAAUvC,KAEVS,EAAS8B,EAAIL,mBAAmB,QAAS,CAC7CtC,SAAU,mBACVuC,KAAM,UACNC,OAAQ,UACRC,OAAQ,EAAA,CACT,EAEKG,EAAYD,EAAIL,mBAAmB,QAAS,CAChDtC,SAAUE,EACVqC,KAAM,UACNC,OAAQ,UACRC,OAAQ,EAAA,CACT,EAEKI,EAAa5C,GAAwB,mBAAoB0C,CAAG,EAC5DG,EAAgB7C,GAAwBC,EAAUyC,CAAG,EAEpD,MAAA,CACL9B,OAAAA,EACA+B,UAAAA,EACAC,WAAAA,EACAC,cAAAA,EACAC,UAAW,GAAGlC,KAAUgC,OAAgBD,KAAaE,GAAAA,CAEzD,EAKaE,GAAsBA,IAAc,CAEzCnC,MAAAA,MADUT,OACGkC,mBAAmB,QAAS,CAC7CtC,SAAU,mBACVuC,KAAM,UACNC,OAAQ,UACRC,OAAQ,EAAA,CACT,EAED,OAAOQ,GAAcpC,CAAM,CAC7B,EAcaqC,GAAsBC,GAAuC,CACxE,MAAMpC,EAAQqC,KAAKC,MAAMF,EAAe,EAAE,EACpCnC,EAAUmC,EAAe,GAE/B,IAAIJ,EAAY,GAChB,OAAIhC,EAAQ,EACVgC,EAAY/B,EAAU,EAAI,GAAGD,MAAUC,KAAa,GAAGD,KAEvDgC,EAAY,GAAG/B,KAGV,CACLmC,aAAAA,EACApC,MAAAA,EACAC,QAAAA,EACA+B,UAAAA,CAAAA,CAEJ,EAMaO,GAAsBC,GAAuC,CAIlEC,MAAAA,MAHUpD,OAGS8B,eAAe,QAAS,CAAElC,SAAU,kBAAA,CAAoB,EAC3EyD,EAAQ,IAAIrD,KAAKoD,CAAY,EAG7B,CAACzC,EAAOC,CAAO,EAAIuC,EAAatC,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACrDuC,EAAa,IAAItD,KAAKqD,CAAK,EACjCC,EAAWC,SAAS5C,EAAOC,EAAS,EAAG,CAAC,EAGpC0C,GAAcD,GAChBC,EAAWE,QAAQF,EAAW9B,QAAQ,EAAI,CAAC,EAG7C,MAAMiC,EAASH,EAAWtB,QAAQ,EAAIqB,EAAMrB,QAAQ,EAC9C0B,EAAcV,KAAKC,MAAMQ,GAAU,IAAO,GAAG,EAEnD,OAAOX,GAAmBY,CAAW,CACvC,EAYaC,GAA2BA,CACtCC,EACAC,EACAnD,IACoB,CACdZ,MAAAA,EAAWY,GAAgBlB,KAE3BsE,EAAatD,GAAiBoD,EAAS9D,CAAQ,EAC/CiE,EAAWvD,GAAiBqD,EAAO/D,CAAQ,EAE3CkE,EAAoBnE,GAAwBC,CAAQ,EAEnD,MAAA,CACL8D,QAAAA,EACAC,MAAAA,EACAC,WAAAA,EACAC,SAAAA,EACApB,UAAW,GAAGiB,KAAWC,UAAcC,KAAcC,KAAYC,GAAAA,CAErE,EAwBanB,GAAiBpB,GAA4B,CAClD,KAAA,CAACd,EAAOC,CAAO,EAAIa,EAAQZ,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACtD,OAAOJ,EAAQ,GAAKC,CACtB,EA8DaqD,GAAuBC,GAAsC,CACxE,MAAMC,EAAYD,EAASxB,cAAc0B,SAAS,KAAK,EAAI,OAAS,KACpE,MAAO,GAAGF,EAAS1B,aAAa2B,OAAeD,EAASzD,aAC1D,EClTM4D,EAAuBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,wFAAA,EAGvB,CAAC,CAAEC,OAAAA,CAAO,IAAOA,IAAW,SAAW,MAAQ,KAAM,EAKxDC,GAAgBC,EAAAA,KAAIL,WAAA,CAAAC,YAAA,SAAAC,YAAA,cAAA,CAGzB,EAAA,CAAA,kCAAA,CAAA,EAEKI,GAAmBD,EAAAA,KAAIL,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAG5B,EAAA,CAAA,kCAAA,CAAA,EAEKK,GAAmBF,EAAAA,KAAIL,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAG5B,EAAA,CAAA,kCAAA,CAAA,EAEKM,GAAkBH,EAAAA,KAAIL,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAI3B,EAAA,CAAA,iDAAA,CAAA,EAEKO,GAAuBJ,EAAAA,KAAIL,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAehC,EAAA,CAAA,oIAAA,CAAA,EAEKQ,GAA4BX,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAIpC,EAAA,CAAA,0CAAA,CAAA,EAEKS,GAAwBN,EAAAA,KAAIL,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAGjC,EAAA,CAAA,iCAAA,CAAA,EAEKU,GAAwBP,EAAAA,KAAIL,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAGjC,EAAA,CAAA,gCAAA,CAAA,EAKKW,GAIDA,CAAC,CAAEV,OAAAA,EAAQW,SAAAA,EAAUC,eAAAA,CAAe,IAAM,CAC7C,KAAM,CAACpB,EAAUqB,CAAW,EAAIC,EAAAA,SAAuBlD,GAAoB,CAAA,EAU3E,OARAmD,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAQC,YAAY,IAAM,CAC9BJ,EAAYjD,IAAoB,CAAA,EAC/BgD,EAAiB,GAAI,EAEjB,MAAA,IAAMM,cAAcF,CAAK,CAAA,EAC/B,CAACJ,CAAc,CAAC,EAEfZ,IAAW,SAEXmB,OAACxB,GAAc,OAAAK,EACb,SAAA,CAACoB,EAAA,IAAA,OAAA,CAAM7B,SAAoBC,GAAAA,CAAQ,CAAE,CAAA,EACpCmB,GAAaS,EAAAA,IAAAd,GAAA,CAAc,SAAI,MAAA,CAAA,CAClC,CAAA,CAAA,EAIAN,IAAW,UAEXmB,OAACxB,GAAc,OAAAK,EACb,SAAA,CAACoB,EAAAA,IAAAnB,GAAA,CAAQT,WAASzD,MAAO,CAAA,EACzBqF,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,EACZgB,EAAAA,IAACjB,GAAWX,CAAAA,SAAAA,EAAS1B,SAAU,CAAA,EAC9B6C,GAAaS,EAAAA,IAAAd,GAAA,CAAc,SAAI,MAAA,CAAA,CAClC,CAAA,CAAA,EAKFa,OAACxB,GAAc,OAAAK,EACb,SAAA,CAACoB,EAAAA,IAAAnB,GAAA,CAAQT,WAASzD,MAAO,CAAA,EACzBqF,EAAAA,IAACf,GAAUb,CAAAA,SAAAA,EAASzB,UAAW,CAAA,EAC/BqD,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,EACZgB,EAAAA,IAACjB,GAAWX,CAAAA,SAAAA,EAAS1B,SAAU,CAAA,EAC/BsD,EAAAA,IAACf,GAAUb,CAAAA,SAAAA,EAASxB,aAAc,CAAA,EACjC2C,GAAaS,EAAAA,IAAAd,GAAA,CAAc,SAAI,MAAA,CAAA,CAClC,CAAA,CAAA,CAEJ,EAKMe,GAGDA,CAAC,CAAEtF,OAAAA,EAAQiE,OAAAA,CAAO,IAAM,CAC3B,MAAMR,EAAW5B,KACX0D,EAAcrC,GAAyBlD,EAAQA,CAAM,EAE3D,OAAIiE,IAAW,SAEVoB,EAAA,IAAAzB,EAAA,CAAc,OAAAK,EACb,SAAAmB,OAAC,OACEG,CAAAA,SAAAA,CAAYlC,EAAAA,WAAW,WAASrD,EAAO,OAAA,CAC1C,CAAA,CACF,CAAA,EAIAiE,IAAW,UAEXmB,OAACxB,GAAc,OAAAK,EACb,SAAA,CAAAoB,EAAAA,IAACnB,IAAQlE,SAAOA,CAAA,CAAA,EAChBqF,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,EACZgB,EAAAA,IAACjB,GAAWmB,CAAAA,SAAAA,EAAYlC,UAAW,CAAA,CACrC,CAAA,CAAA,EAKF+B,OAACxB,GAAc,OAAAK,EACb,SAAA,CAAAoB,EAAAA,IAACnB,IAAQlE,SAAOA,CAAA,CAAA,EAChBqF,EAAAA,IAACf,GAAUb,CAAAA,SAAAA,EAASzB,UAAW,CAAA,EAC/BqD,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,EACZgB,EAAAA,IAACjB,GAAWmB,CAAAA,SAAAA,EAAYlC,UAAW,CAAA,EACnCgC,EAAAA,IAACf,GAAUb,CAAAA,SAAAA,EAASxB,aAAc,CAAA,CACpC,CAAA,CAAA,CAEJ,EAKMuD,GAIDA,CAAC,CAAE9C,aAAAA,EAAcuB,OAAAA,EAAQY,eAAAA,CAAe,IAAM,CACjD,KAAM,CAACY,EAAWC,CAAY,EAAIX,EAAuBtC,SAAAA,GAAmBC,CAAY,CAAC,EAUzF,OARAsC,EAAAA,UAAU,IAAM,CACRC,MAAAA,EAAQC,YAAY,IAAM,CACjBzC,EAAAA,GAAmBC,CAAY,CAAC,CAAA,EAC5CmC,EAAiB,GAAI,EAEjB,MAAA,IAAMM,cAAcF,CAAK,CAAA,EAC/B,CAACvC,EAAcmC,CAAc,CAAC,EAE7BZ,IAAW,gBAEVO,GACC,CAAA,SAAA,CAACa,EAAAA,IAAAZ,GAAA,CAAgBgB,WAAUvD,SAAU,CAAA,SACpCwC,GAAe,CAAA,SAAA,CAAA,SAAOhC,CAAAA,EAAa,CACtC,CAAA,CAAA,SAKD8B,GACC,CAAA,SAAA,CAAAa,EAAAA,IAACX,IAAe,SAAQ,UAAA,CAAA,EACxBW,EAAAA,IAACZ,GAAgBgB,CAAAA,SAAAA,EAAUvD,SAAU,CAAA,SACpCwC,GAAe,CAAA,SAAA,CAAA,IAAEhC,EAAa,MAAA,EAAI,CACrC,CAAA,CAAA,CAEJ,EAKMiD,GAIDA,CAAC,CAAEC,aAAAA,EAAcC,WAAAA,EAAY5B,OAAAA,CAAO,IAAM,CACvCsB,MAAAA,EAAcrC,GAAyB0C,EAAcC,CAAU,EAErE,OAAI5B,IAAW,eAEVL,EAAc,CAAA,OAAAK,EACb,eAAC,OAAMsB,CAAAA,SAAAA,EAAYrD,SAAU,CAAA,CAC/B,CAAA,EAIA+B,IAAW,UAEXmB,OAACxB,GAAc,OAAAK,EACb,SAAA,CAAAmB,OAAClB,GACE0B,CAAAA,SAAAA,CAAAA,EAAa,IAAEC,CAAAA,EAClB,EACAR,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,SACXD,GACEmB,CAAAA,SAAAA,CAAYlC,EAAAA,WAAW,IAAEkC,EAAYjC,QAAAA,EACxC,CACF,CAAA,CAAA,EAKF8B,OAACxB,GAAc,OAAAK,EACb,SAAA,CAACoB,EAAA,IAAA,MAAA,CACC,gBAACnB,GACE0B,CAAAA,SAAAA,CAAAA,EAAa,IAAEC,EAAW,KAAA,CAAA,CAC7B,CACF,CAAA,EACAR,EAAAA,IAAChB,IAAU,SAAC,GAAA,CAAA,EACZgB,EAAA,IAAC,MACC,CAAA,SAAAD,EAAAA,KAAChB,GACEmB,CAAAA,SAAAA,CAAYlC,EAAAA,WAAW,IAAEkC,EAAYjC,SAAS,QAAA,CAAA,CACjD,CACF,CAAA,CACF,CAAA,CAAA,CAEJ,EAKawC,GAA6DC,GAAA,CAClE,KAAA,CACJC,KAAAA,EAAO,UACPhG,OAAAA,EACA0C,aAAAA,EACAkD,aAAAA,EACAC,WAAAA,EACA5B,OAAAA,EAAS,UACTW,SAAAA,EAAW,GACXqB,UAAAA,EACApB,eAAAA,EAAiB,CACfkB,EAAAA,EACEG,EAAiB,CACrBD,UAAAA,EACAE,MAAO,CACLC,SAAUnC,IAAW,SAAW,OAASA,IAAW,UAAY,OAAS,MAC3E,CAAA,EAGF,OAAQ+B,EAAI,CACV,IAAK,SACH,OAAKhG,EAKHqF,MAAC,OAAI,GAAIa,EACP,eAACZ,GAAkB,CAAA,OAAAtF,EAAgB,OAAAiE,CAAe,CAAA,CACpD,CAAA,GANAoC,QAAQC,KAAK,qDAAqD,EAC3D,MAQX,IAAK,YACH,OAAK5D,EAKH2C,EAAA,IAAC,OAAI,GAAIa,EACP,eAACV,GACC,CAAA,aAAA9C,EACA,OAAAuB,EACA,eAAAY,CAA+B,CAAA,CAEnC,CAAA,GAVAwB,QAAQC,KAAK,8DAA8D,EACpE,MAYX,IAAK,UACC,MAAA,CAACV,GAAgB,CAACC,GACpBQ,QAAQC,KAAK,4EAA4E,EAClF,MAGPjB,EAAA,IAAC,OAAI,GAAIa,EACP,eAACP,GAAmB,CAAA,aAAAC,EAA4B,WAAAC,EAAwB,OAAA5B,CAAe,CAAA,CACzF,CAAA,EAGJ,IAAK,UACL,QAEI,OAAAoB,EAAA,IAAC,OAAI,GAAIa,EACP,eAACvB,GAAmB,CAAA,OAAAV,EAAgB,SAAAW,EAAoB,eAAAC,CAA+B,CAAA,CACzF,CAAA,CAEN,CACF,EC7RO,SAAS0B,GACdC,EACAC,EACA1C,EAAc,eACd,CAEM2C,MAAAA,EAAUC,EAAAA,cAA8CC,MAAS,EACvEF,EAAQ3C,YAAcA,EAGtB,MAAM8C,EAAyCA,CAAC,CAC9CC,SAAAA,EACAL,aAAcM,CAAAA,IACV,CACJ,KAAM,CAACC,EAAOC,CAAQ,EAAIC,EAAWV,WAAAA,EAASO,GAAwBN,CAAY,EAC5E3G,EAAQqH,EAAAA,QAAQ,KAAO,CAAEH,MAAAA,EAAOC,SAAAA,CAAAA,GAAa,CAACD,CAAK,CAAC,EAC1D,OAAQ3B,EAAAA,IAAAqB,EAAQ,SAAR,CAAiB,MAAA5G,EAAegH,SAAAA,CAAS,CAAA,CAAA,EAInD,SAASM,GAA+B,CAChCC,MAAAA,EAAUC,aAAWZ,CAAO,EAClC,GAAIW,IAAYT,OACd,MAAM,IAAIW,MAAM,MAAMxD,2BAAqCA,WAAqB,EAE3EsD,OAAAA,CACT,CAGA,SAASG,EAAeC,EAA6B,CAC7C,KAAA,CAAET,MAAAA,GAAUI,EAAS,EAC3B,OAAOK,EAAST,CAAK,CACvB,CAGA,SAASU,EAAsCC,EAAoD,CAC3F,KAAA,CAAEV,SAAAA,GAAaG,EAAS,EACvBD,OAAAA,EAAAA,QACL,IAAM,IAAIS,IAAwB,CACvBD,EAAAA,EAAc,GAAGC,CAAI,CAAC,CAAA,EAEjC,CAACX,EAAUU,CAAa,CAC1B,CACF,CAGA,SAASE,EACPC,EACyD,CACnD,KAAA,CAAEb,SAAAA,GAAaG,EAAS,EAC9B,OAAOD,UACL,IAAM,CACJ,MAAMY,EAAsB,CAAA,EAC5B,UAAWC,KAAOF,EACIE,EAAAA,CAAG,EAAI,IAAIJ,IAAoC,CACjEX,EAASa,EAAeE,CAAG,EAAE,GAAGJ,CAAI,CAAC,CAAA,EAGlCG,OAAAA,CAAAA,EAET,CAACd,EAAUa,CAAc,CAC3B,CACF,CAEO,MAAA,CACLpB,QAAAA,EACAG,SAAAA,EACAO,SAAAA,EACAI,YAAAA,EACAE,UAAAA,EACAG,WAAAA,CAAAA,CAEJ,CC1DO,SAASI,KACXL,EACa,CACVM,MAAAA,EAAaN,EAAKO,MAClBC,EAAiBR,EAEvB,IAAIS,EAA2B,KAC3BC,EAAuB,KAE3B,OAAQtB,GAAa,CACnB,MAAMuB,EAASH,EAAe/H,IAAgBoH,GAAAA,EAAST,CAAK,CAAC,EAG7D,OACEqB,IAAe,MACfE,EAAOC,SAAWH,EAAWG,QAC7BD,EAAOE,KAAK,CAACC,EAAOC,IAAUD,IAAUL,EAAYM,CAAK,CAAC,KAG7CT,EAAAA,EAAW,GAAGK,CAAM,EACpBA,EAAAA,GAGRD,CAAAA,CAEX,CChDgBM,SAAAA,GACdpC,EACAqC,EAC0B,CACpB,KAAA,CACJb,IAAAA,EACAvB,aAAAA,EACAqC,QAAAA,EAAU,EACVC,QAAAA,EACAC,UAAAA,EAAYC,KAAKC,UACjBC,YAAAA,EAAcF,KAAKG,MACnBC,OAAAA,EAAoBrC,GAAAA,EACpBsC,MAAAA,EAAQA,CAACC,EAAgB9C,KAAkB,CAAE,GAAGA,EAAc,GAAG8C,CAAAA,GACjEC,MAAAA,EAAQ,EACNX,EAAAA,EAGEY,EAAYA,IAAyB,CACrC,GAAA,CACIC,MAAAA,EAAkBC,aAAaC,QAAQ5B,CAAG,EAChD,GAAI0B,IAAoB,KACf,OAAA,KAGH,KAAA,CAAE1C,MAAAA,EAAO8B,QAASe,CAAAA,EAAqBV,EAAYO,CAAe,EAGpEG,OAAAA,IAAqBf,GAAWC,GAC9BS,GACMM,QAAAA,IAAI,gCAAgCD,QAAuBf,GAAS,EAEvEC,EAAQ/B,EAAO6C,CAAgB,GAGjC7C,QACA+C,GACP,OAAIP,GACMQ,QAAAA,MAAM,0CAA2CD,CAAG,EAEvD,IACT,CAAA,EAIIE,EAAajD,GAAmB,CAChC,GAAA,CACIkD,MAAAA,EAAgBb,EAAOrC,CAAK,EAC5B0C,EAAkBV,EAAU,CAChChC,MAAOkD,EACPpB,QAAAA,CAAAA,CACD,EACYqB,aAAAA,QAAQnC,EAAK0B,CAAe,QAClCK,GACHP,GACMQ,QAAAA,MAAM,uCAAwCD,CAAG,CAE7D,CAAA,EAIIK,EAAQA,IAAY,CACpB,GAAA,CACFT,aAAaU,WAAWrC,CAAG,QACpB+B,GACHP,GACMQ,QAAAA,MAAM,2CAA4CD,CAAG,CAEjE,CAAA,EAIIR,EAAiBE,IACjBa,EAAqBf,EAAiBD,EAAMC,EAAgB9C,CAAY,EAAIA,EAElF,OAAI+C,GAASD,IACHO,QAAAA,IAAI,0BAA2BP,CAAc,EAC7CO,QAAAA,IAAI,wBAAyBQ,CAAkB,GAUlD,CACL9D,QAPsC+D,CAACvD,EAAOwD,IAAW,CACnDC,MAAAA,EAAWjE,EAAQQ,EAAOwD,CAAM,EACtCP,OAAAA,EAAUQ,CAAQ,EACXA,CAAAA,EAKPhE,aAAc6D,EACdF,MAAAA,CAAAA,CAEJ,CCtBO,MAAMM,GAA0C,CACrDC,KAAM,CACJC,eAAgB,KAChBC,YAAa,KACbC,eAAgB,CAAE,EAClBC,UAAW,CAAE,EACbC,WAAY,CAAA,CACd,EACAC,UAAW,GACXjB,MAAO,KACPkB,iBAAkB3L,OAAO4L,YAAc/K,EAAAA,MAAM,GAAG,EAAE,CAAC,CACrD,EAGagL,GAAoBA,CAC/BpE,EACAwD,IACoB,CACpB,OAAQA,EAAO3K,KAAI,CACjB,IAAKwL,8BACI,MAAA,CACL,GAAGrE,EACHiE,UAAW,GACXjB,MAAO,IAAA,EAEX,IAAKqB,gCACI,MAAA,CACL,GAAGrE,EACH2D,KAAMH,EAAOc,QACbL,UAAW,GACXjB,MAAO,IAAA,EAEX,IAAKqB,8BACI,MAAA,CACL,GAAGrE,EACHiE,UAAW,GACXjB,MAAOQ,EAAOc,OAAAA,EAElB,IAAKD,sCACC,OAACrE,EAAM2D,KAAKE,YAGT,CACL,GAAG7D,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTE,YAAa,CACX,GAAG7D,EAAM2D,KAAKE,YACdU,MAAOvE,EAAM2D,KAAKE,YAAYU,MAAMlL,IAAImL,GACtCA,EAAKC,KAAOjB,EAAOc,QAAQG,GACvB,CAAE,GAAGD,EAAME,UAAWlB,EAAOc,QAAQI,WACrCF,CACN,CACF,CACF,CAAA,EAdOxE,EAgBX,IAAKqE,mCACC,OAACrE,EAAM2D,KAAKE,YAmBT,CACL,GAAG7D,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTE,YAAa,CACX,GAAG7D,EAAM2D,KAAKE,YACdU,MAAO,CAAC,GAAGvE,EAAM2D,KAAKE,YAAYU,MAAOf,EAAOc,OAAO,CACzD,CACF,CAAA,EA1BO,CACL,GAAGtE,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTE,YAAa,CACXU,MAAO,CAACf,EAAOc,OAAO,EACtBK,SAAU,GACVC,eAAgB,CACdC,gBAAiB,EACjBC,aAAc,EACdC,UAAW,EACXC,eAAgB,EAClB,EACAC,MAAO,EACT,CACF,CAAA,EAaN,IAAKZ,sCACC,OAACrE,EAAM2D,KAAKE,YAGT,CACL,GAAG7D,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTE,YAAa,CACX,GAAG7D,EAAM2D,KAAKE,YACdU,MAAOvE,EAAM2D,KAAKE,YAAYU,MAAMlC,OAC1BmC,GAAAA,EAAKC,KAAOjB,EAAOc,OAC7B,CACF,CACF,CAAA,EAZOtE,EAcX,IAAKqE,kCACI,MAAA,CACL,GAAGrE,EACHkE,aAAcV,EAAOc,OAAAA,EAEzB,IAAKD,oCACI,MAAA,CACL,GAAGrE,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTC,eAAgBJ,EAAOc,OACzB,CAAA,EAEJ,IAAKD,qCACI,MAAA,CACL,GAAGrE,EACH2D,KAAM,CACJ,GAAG3D,EAAM2D,KACTG,eAAgBN,EAAOc,OACzB,CAAA,EAEJ,IAAKD,yBACI,MAAA,CACL,GAAGX,GACHQ,aAAclE,EAAMkE,YAAAA,EAExB,QACSlE,OAAAA,CACX,CACF,EAGM,CAAER,QAAS+D,GAAkB9D,aAAcyF,EAAsB,EAAItD,GACzEwC,GACA,CACEpD,IAAK,aACLvB,aAAciE,GACd5B,QAAS,EACTO,OAAoBrC,IAAA,CAElBkE,aAAclE,EAAMkE,YAAAA,EAExB,CACF,EAGa,CACXxE,QAASyF,GACTtF,SAAUuF,GACVhF,SAAUiF,GACV7E,YAAa8E,EACb5E,UAAW6E,GACX1E,WAAY2E,EACd,EAAIjG,GACFgE,GACA2B,GACA,mBACF,EAGaO,GAAoB,CAC/BC,eAAgBA,KAA6B,CAC3C7M,KAAMwL,6BAAAA,GAERsB,iBAAmBhC,IAAkD,CACnE9K,KAAMwL,gCACNC,QAASX,CAAAA,GAEXiC,eAAiB5C,IAAyC,CACxDnK,KAAMwL,8BACNC,QAAStB,CAAAA,GAEX6C,sBAAuBA,CAACpB,EAAYC,KAAqD,CACvF7L,KAAMwL,sCACNC,QAAS,CAAEG,GAAAA,EAAIC,UAAAA,CAAU,CAAA,GAE3BoB,mBAAqBtB,IAAqD,CACxE3L,KAAMwL,mCACNC,QAASE,CAAAA,GAEXuB,sBAAwBtB,IAA6C,CACnE5L,KAAMwL,sCACNC,QAASG,CAAAA,GAEXuB,mBAAqB1N,IAA4C,CAC/DO,KAAMwL,kCACNC,QAAShM,CAAAA,GAEX2N,qBAAuBC,IAA4E,CACjGrN,KAAMwL,oCACNC,QAAS4B,CAAAA,GAEXC,qBAAuBC,IAA0E,CAC/FvN,KAAMwL,qCACNC,QAAS8B,CAAAA,GAEXC,WAAYA,KAAyB,CACnCxN,KAAMwL,wBAAAA,EAEV,ECvTaiC,GAAwBtG,GAA2BA,EAAM2D,KACzD4C,GAAwBvG,GAA2BA,EAAM2D,KAAKC,eAC9D4C,GAAqBxG,GAA2BA,EAAM2D,KAAKE,YAC3D4C,GAAwBzG,GAA2BA,EAAM2D,KAAKG,eAC9D4C,GAAmB1G,GAA2BA,EAAM2D,KAAKI,UACzD4C,GAAoB3G,GAA2BA,EAAM2D,KAAKK,WAC1D4C,GAAmB5G,GAA2BA,EAAMiE,UACpD4C,GAAe7G,GAA2BA,EAAMgD,MAChD8D,GAAsB9G,GAA2BA,EAAMkE,aAGvD6C,GAAyB9F,EACpCuF,OACiB3C,GAAAA,YAAAA,EAAaU,QAAS,CAAA,CACzC,EAEayC,GAAkC/F,EAC7C8F,GACCxC,GAAUA,EAAMlC,OAAOmC,GAAQA,EAAKE,SAAS,CAChD,EAEauC,GAAmChG,EAC9C8F,GACWxC,GAAAA,EAAMlC,OAAemC,GAAA,CAACA,EAAKE,SAAS,CACjD,EAEawC,GAA8BjG,EACzC8F,GACAC,GACA,CAACG,EAAUC,IACLD,EAAS3F,SAAW,EAAU,EAC3B4F,EAAe5F,OAAS2F,EAAS3F,MAE5C,EAEa6F,GAAmCpG,EAC9C8F,GACWxC,GAAA,CACT,MAAM+C,EAAoD,CACxDC,KAAM,CAAE,EACRC,OAAQ,CAAE,EACVC,IAAK,CAAA,CAAA,EAGPlD,OAAAA,EAAMmD,QAAgBlD,GAAA,CACpB8C,EAAO9C,EAAKmD,QAAQ,EAAEC,KAAKpD,CAAI,CAAA,CAChC,EAEM8C,CACT,CACF,EAEaO,GAAsB5G,EACjCsF,OACoB3C,GAAAA,YAAAA,EAAgBkE,UAAW,CAAA,CACjD,EAEaC,GAAwB9G,EACnC4G,GACaC,GAAAA,EAAQzF,OAAgBV,GAAAA,EAAMqG,OAAS,CAAC,CACvD,EAEaC,GAAwBhH,EACnC4G,GACaC,GAAAA,EAAQzF,OAAgBV,GAAAA,EAAMqG,OAAS,CAAC,CACvD,EAEaE,GAAwBjH,EACnCsF,GACoB3C,IAAAA,GAAAA,YAAAA,EAAgBuE,YAAa,SACnD,EAEaC,GAAsBnH,EACjCsF,GACoB3C,IAAAA,GAAAA,YAAAA,EAAgByE,UAAW,EACjD,EAEaC,GAAuBrH,EAClCsF,OACoB3C,GAAAA,YAAAA,EAAgB2E,iBAAkB,CAAA,CACxD,EAEaC,GAAiCvH,EAC5CqH,GACYG,GAAAA,EAAOpG,OAAgBqG,GAAAA,EAAMC,aAAe,MAAM,CAChE,EAEaC,GAA+B3H,EAC1CwF,GACYL,GAAA,CACV,MAAMkB,EAA2C,CAAA,EAEjDlB,OAAAA,EAAOsB,QAAiBmB,GAAA,CACfA,EAAAA,EAAMC,MAAM,EAAID,CAAAA,CACxB,EAEMvB,CACT,CACF,EAEayB,GAA0B9H,EACrCyF,GACe3C,GAAA,CACb,MAAMuD,EAA8C,CAAA,EAEpDvD,OAAAA,EAAU2D,QAAgBlD,GAAA,CACjBA,EAAAA,EAAKsE,MAAM,EAAItE,CAAAA,CACvB,EAEM8C,CACT,CACF,EAEa0B,GAA6B/H,EACxC0F,GACUsC,GAAAA,EAAK5G,OAAemC,GAAAA,EAAK0E,SAAW,MAAM,CACtD,EAEaC,GAAyBlI,EACpC0F,GACUsC,GAAA,CACR,MAAM3B,EAAsC,CAAA,EAE5C2B,OAAAA,EAAKvB,QAAgBlD,GAAA,CACblM,MAAAA,EAAO,IAAIC,KAAKiM,EAAK4E,SAAS,EAAEjF,YAAAA,EAAc/K,MAAM,GAAG,EAAE,CAAC,EAC3DkO,EAAOhP,CAAI,IACPA,EAAAA,CAAI,EAAI,IAEVA,EAAAA,CAAI,EAAEsP,KAAKpD,CAAI,CAAA,CACvB,EAEM8C,CACT,CACF,EAEa+B,GAAgBpI,EAC3BsF,GACAC,GACAC,GACA,CAAC7C,EAAgBC,EAAaC,IACrB,CAAC,CAACF,GAAkB,CAAC,CAACC,GAAeC,EAAetC,OAAS,CAExE,EAEa8H,GAAoBrI,EAC/BsF,GACoB3C,IAAAA,GAAAA,YAAAA,EAAgB2F,cAAe,IACrD,EAGaC,EAAsB,CACjClD,qBAAAA,GACAC,qBAAAA,GACAC,kBAAAA,GACAC,qBAAAA,GACAC,gBAAAA,GACAC,iBAAAA,GACAC,gBAAAA,GACAC,YAAAA,GACAC,mBAAAA,GACAC,uBAAAA,GACAC,gCAAAA,GACAC,iCAAAA,GACAC,4BAAAA,GACAG,iCAAAA,GACAQ,oBAAAA,GACAE,sBAAAA,GACAE,sBAAAA,GACAC,sBAAAA,GACAE,oBAAAA,GACAE,qBAAAA,GACAE,+BAAAA,GACAI,6BAAAA,GACAG,wBAAAA,GACAC,2BAAAA,GACAG,uBAAAA,GACAE,cAAAA,GACAC,kBAAAA,EACF,EC1KMG,GAAmBA,IAAsB,CAE7C,MAAMC,EAAqB,CACzBvB,UAAW,CAAC,UAAW,UAAW,SAAS,EAAE5M,KAAKC,MAAMD,KAAKoO,OAAW,EAAA,CAAC,CAAC,EAI1EtB,QACE,4HACFP,QAAS,CACP,CACEgB,OAAQ,MACRc,KAAM,UACN9Q,MAAO,KAAOyC,KAAKoO,OAAW,EAAA,IAC9B3B,OAAQzM,KAAKoO,OAAO,EAAI,EAAI,EAC5BE,eAAgBtO,KAAKoO,OAAO,EAAI,EAAI,GAAK,GAAA,EAE3C,CACEb,OAAQ,MACRc,KAAM,SACN9Q,MAAO,KAAQyC,KAAKoO,OAAW,EAAA,IAC/B3B,OAAQzM,KAAKoO,OAAO,EAAI,EAAI,EAC5BE,eAAgBtO,KAAKoO,OAAO,EAAI,EAAI,GAAK,GAAA,EAE3C,CACEb,OAAQ,MACRc,KAAM,YACN9Q,MAAO,KAAQyC,KAAKoO,OAAW,EAAA,IAC/B3B,OAAQzM,KAAKoO,OAAO,EAAI,EAAI,EAC5BE,eAAgBtO,KAAKoO,OAAO,EAAI,EAAI,GAAK,GAAA,CAC1C,EAEHpB,eAAgB,CACd,CACEuB,MAAO,6BACPC,KAAM,QACNpB,WAAY,OACZqB,SAAU,QACVC,SAAU,OAAA,EAEZ,CACEH,MAAO,sBACPC,KAAM,QACNpB,WAAY,SACZqB,SAAU,OACVC,SAAU,OACVC,OAAQ,MAAA,EAEV,CACEJ,MAAO,kBACPC,KAAM,QACNpB,WAAY,OACZqB,SAAU,OACVC,SAAU,MAAA,CACX,EAEHhB,KAAM,CACJ,CACExE,GAAI,IACJqF,MAAO,sDACPK,OAAQ,YACRf,UAAW,IAAI7Q,KAAK,EAAE4L,YAAY,EAClCiG,IAAK,6BACLlB,OAAQ,MAAA,EAEV,CACEzE,GAAI,IACJqF,MAAO,mDACPK,OAAQ,OACRf,UAAW,IAAI7Q,KAAK,EAAE4L,YAAY,EAClCiG,IAAK,6BACLlB,OAAQ,QAAA,EAEV,CACEzE,GAAI,IACJqF,MAAO,+CACPK,OAAQ,UACRf,UAAW,IAAI7Q,KAAK,EAAE4L,YAAY,EAClCiG,IAAK,6BACLlB,OAAQ,QAAA,CACT,EAEHK,YAAa,IAAIhR,KAAK,EAAE4L,YAAY,CAAA,EAuG/B,MAAA,CACLP,eAAgB8F,EAChB7F,YArGsB,CACtBU,MAAO,CACL,CACEE,GAAI,IACJ4F,YAAa,iDACb1C,SAAU,OACVjD,UAAW,EAAA,EAEb,CACED,GAAI,IACJ4F,YAAa,8CACb1C,SAAU,SACVjD,UAAW,EAAA,EAEb,CACED,GAAI,IACJ4F,YAAa,mDACb1C,SAAU,OACVjD,UAAW,EAAA,EAEb,CACED,GAAI,IACJ4F,YAAa,sDACb1C,SAAU,SACVjD,UAAW,EAAA,EAEb,CACED,GAAI,IACJ4F,YAAa,uCACb1C,SAAU,MACVjD,UAAW,EAAA,CACZ,EAEHC,SAAU,oEACVC,eAAgB,CACdC,gBAAiB,EACjBC,aAAc,EACdC,UAAW,EACXC,eAAgB,yBAClB,EACAC,MAAO,+EAAA,EA8DPnB,eA1DyB,CACzB,CACEgF,OAAQ,MACRwB,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCC,WAAY,QAAA,EAEd,CACE1B,OAAQ,MACRwB,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCC,WAAY,QAAA,EAEd,CACE1B,OAAQ,OACRwB,QAAS,CAAC,SAAU,SAAU,QAAQ,EACtCC,WAAY,CAAC,SAAU,SAAU,QAAQ,EACzCC,WAAY,QAAA,CACb,EAyCDzG,UArCoB,CACpB,CACE+E,OAAQ,OACRc,KAAM,aACNa,MAAO,MACPC,OAAQ,4BACRC,MAAO,qBACPC,WAAY,OACZC,SAAU,IACVC,WAAY,GAAA,EAEd,CACEhC,OAAQ,OACRc,KAAM,kBACNa,MAAO,OACPC,OAAQ,2BACRC,MAAO,2BACPC,WAAY,MACZC,SAAU,IACVC,WAAY,GAAA,EAEd,CACEhC,OAAQ,OACRc,KAAM,eACNa,MAAO,OACPC,OAAQ,wBACRC,MAAO,sBACPC,WAAY,IACZC,SAAU,IACVC,WAAY,GAAA,CACb,EAQD9G,WAAY0F,EAAmBT,IAAAA,CAEnC,EASO,SAAS8B,IAAgB,CAGxBC,MAAAA,EAAUxF,GAAqBC,EAAiB,EAGhDvB,EAAeoB,EAAsBkE,EAAoB1C,kBAAkB,EAC3ElD,EAAiB0B,EAAsBkE,EAAoBjD,oBAAoB,EAC/E1C,EAAcyB,EAAsBkE,EAAoBhD,iBAAiB,EACzE1C,EAAiBwB,EAAsBkE,EAAoB/C,oBAAoB,EAC/E1C,EAAYuB,EAAsBkE,EAAoB9C,eAAe,EACrE1C,EAAasB,EAAsBkE,EAAoB7C,gBAAgB,EACvE1C,EAAYqB,EAAsBkE,EAAoB5C,eAAe,EACrE5D,EAAQsC,EAAsBkE,EAAoB3C,WAAW,EAC7DoE,EAAmB3F,EAAsBkE,EAAoBzC,sBAAsB,EACnFmE,EAAwB5F,EAC5BkE,EAAoBtC,2BACtB,EACMiE,EAAkB7F,EAAsBkE,EAAoBtB,qBAAqB,EACjFkD,EAAgB9F,EAAsBkE,EAAoBpB,mBAAmB,EAC7EmB,EAAcjE,EAAsBkE,EAAoBF,iBAAiB,EAIzE+B,EADQ,IAAI9S,KAAK2L,CAAY,EACToH,mBAAmB,QAAS,CACpDC,QAAS,OACT/R,KAAM,UACNE,MAAO,OACPI,IAAK,SAAA,CACN,EAGK0R,EAAYC,EAAAA,YAAY,SAAY,CACpC,GAAA,CACFT,EAAQtF,eAAe,EAIvB,MAAM,IAAIgG,QAAQC,GAAWC,WAAWD,EAAS,GAAG,CAAC,EAErD,MAAME,EAAWpC,KACjBuB,EAAQrF,iBAAiBkG,CAAQ,QAC1B9I,GACP,MAAM+I,EAAe/I,aAAexC,MAAQwC,EAAIgJ,QAAU,gBAClD/I,QAAAA,MAAM,mCAAoCD,CAAG,EACrDiI,EAAQpF,eAAekG,CAAY,CACrC,CAAA,EACC,CAACd,CAAO,CAAC,EAGZhN,EAAAA,UAAU,IAAM,CACJwN,GAAA,EACT,CAACA,CAAS,CAAC,EAGdxN,EAAAA,UAAU,IAAM,CACJwN,GAAA,EACT,CAACtH,EAAcsH,CAAS,CAAC,EAGtBQ,MAAAA,EAAmBP,cACtBnT,GAAiB,CAChB0S,EAAQhF,mBAAmB1N,CAAI,CAAA,EAEjC,CAAC0S,CAAO,CACV,EAEMiB,EAA8BR,EAAAA,YAClC,CAAChH,EAAYC,IAAuB,CAC1BmB,EAAAA,sBAAsBpB,EAAIC,CAAS,CAAA,EAE7C,CAACsG,CAAO,CACV,EAEMkB,EAA2BT,cAC9BjH,GAA0B,CACzBwG,EAAQlF,mBAAmBtB,CAAI,CAAA,EAEjC,CAACwG,CAAO,CACV,EAEMmB,EAA8BV,cACjChH,GAAe,CACduG,EAAQjF,sBAAsBtB,CAAE,CAAA,EAElC,CAACuG,CAAO,CACV,EAEMoB,EAAgBX,EAAAA,YAAY,IAAM,CAC5BD,GAAA,EACT,CAACA,CAAS,CAAC,EAEP,MAAA,CAELtH,aAAAA,EACAN,eAAAA,EACAC,YAAAA,EACAC,eAAAA,EACAC,UAAAA,EACAC,WAAAA,EACAC,UAAAA,EACAjB,MAAAA,EACAiI,iBAAAA,EACAC,sBAAAA,EACAC,gBAAAA,EACAC,cAAAA,EACA7B,YAAAA,EACA8B,YAAAA,EAGAgB,aAAcL,EACdM,wBAAyBL,EACzBM,qBAAsBL,EACtBM,wBAAyBL,EACzBM,UAAWL,CAAAA,CAEf,CCnSA,MAAMM,GAAoBA,IAAc,CAChC5R,MAAAA,MAAUvC,KACVoU,EAAM7R,EAAIP,QAAAA,EAAaO,EAAI8R,kBAAsB,EAAA,IAEvD,OADY,IAAIrU,KAAKoU,EAAO,GAAK,IAAQ,EAC9BE,UACb,EAKMC,GAAmBpS,GACnBA,GAAQ,GAAKA,EAAO,EAAU,aAC9BA,GAAQ,GAAKA,EAAO,GAAW,UAC/BA,GAAQ,IAAMA,EAAO,GAAW,UAChCA,GAAQ,IAAMA,EAAO,GAAW,WAChCA,GAAQ,IAAMA,EAAO,GAAW,cAC7B,YAMHqS,GAAsBA,CAACC,EAAiBC,IACxCA,EAAa,EAAU,UACvBD,GAAW,GAAW,YACtBA,GAAW,GAAW,OACtBA,GAAW,GAAW,UACtBA,GAAW,GAAW,OACnB,QAMHE,GAA0BC,GAAmG,CACjI,OAAQA,EAAW,CACjB,IAAK,YAAoB,MAAA,OACzB,IAAK,OAAe,MAAA,SACpB,IAAK,UAAkB,MAAA,SACvB,IAAK,OAAe,MAAA,MACpB,IAAK,QAAgB,MAAA,QACrB,QAAgB,MAAA,QAClB,CACF,EAKaC,GAAsBA,IAAM,CACvC,KAAM,CAACC,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,+CAAgDD,CAAG,EACjEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EA4IE,CACLK,UA1IkCzN,EAAAA,QAAQ,IAAM,CAC5CkN,GAAAA,EAAO7L,SAAW,EACb,MAAA,CACLqM,mBAAoB,CAAE,EACtBC,sBAAuB,CACrBC,YAAarB,GAAkB,EAC/BsB,aAAclB,GAAgBJ,IAAmB,EACjDuB,eAAgB,SAChBjB,QAAS,EACTkB,WAAY,CAAE,EACdC,UAAW,SACXC,YAAa,CAAC,uCAAuC,CACvD,EACAC,oBAAqB,CAAE,EACvBC,qBAAsB,CAAE,EACxBC,oBAAqB,EACrBhF,gBAAiBhR,IAAK,EAK1B,MAAMiW,EAAwD,CAAA,EAE9DnB,EAAO3F,QAAiB+G,GAAA,CAClBA,GAAAA,EAAMA,MAAMC,WAAY,CACpBhU,MAAAA,EAAOiU,SAASF,EAAMA,MAAMC,WAAWtV,MAAM,GAAG,EAAE,CAAC,CAAC,EACrDoV,EAAa9T,CAAI,IACPA,EAAAA,CAAI,EAAI,IAEVA,EAAAA,CAAI,EAAEkN,KAAK6G,CAAK,EAC/B,CACD,EAGD,MAAMZ,EAA2C,CAAA,EAEjD,QAASnT,EAAO,EAAGA,EAAO,GAAIA,IAAQ,CACpC,MAAMkU,EAAaJ,EAAa9T,CAAI,GAAK,CAAA,EACnCmU,EAAgBD,EAAWvM,UAAYyM,EAAEL,MAAMM,WAAa,KAAK,EACjE/B,EAAU4B,EAAWpN,OAAS,EAAKqN,EAAcrN,OAASoN,EAAWpN,OAAU,IAAM,EACrFwN,EAAeJ,EAAWpN,OAAS,EACrCoN,EAAWK,OAAO,CAACC,EAAKJ,IAAMI,GAAOJ,EAAEL,MAAMU,YAAc,GAAI,CAAC,EAAIP,EAAWpN,OAC/E,EACE4N,EAAWR,EAAWK,OAAO,CAACC,EAAKJ,IAAMI,GAAOJ,EAAEL,MAAMY,aAAe,GAAI,CAAC,EAG5EC,EAA2C,CAAA,EACjDV,EAAWlH,QAAiB+G,GAAA,CACpB9D,MAAAA,EAAQ8D,EAAMA,MAAMc,YAAc,UACxCD,EAAY3E,CAAK,GAAK2E,EAAY3E,CAAK,GAAK,GAAK,CAAA,CAClD,EACD,MAAM6E,EAAYC,OAAOC,KAAKJ,CAAW,EAAEL,OAAO,CAACU,EAAGC,IACpDN,EAAYK,CAAC,EAAIL,EAAYM,CAAC,EAAID,EAAIC,EAAG,MAAM,EAEjD/B,EAAmBjG,KAAK,CACtBlN,KAAAA,EACAmV,MAAO/C,GAAgBpS,CAAI,EAC3BsS,QAAAA,EACA8C,YAAalB,EAAWpN,OACxBwN,aAAAA,EACAI,SAAAA,EACAI,UAAAA,EACArC,YAAaJ,GAAoBC,EAAS4B,EAAWpN,MAAM,CAAA,CAC5D,EAIH,MAAMuM,EAAcrB,KACdqD,EAAiBlC,EAAmBE,CAAW,EAG/CiC,EAAoBxB,EAAaT,CAAW,GAAK,CAAA,EACjDkC,EAAsE,CAAA,EAE5ED,EAAkBtI,QAAiB+G,GAAA,CAC3B9D,MAAAA,EAAQ8D,EAAMA,MAAMc,YAAc,UACnCU,EAActF,CAAK,IACtBsF,EAActF,CAAK,EAAI,CAAEuF,KAAM,EAAGC,MAAO,CAAA,GAE3CF,EAActF,CAAK,EAAEwF,QACjB1B,EAAMA,MAAMM,WAAa,OAC3BkB,EAActF,CAAK,EAAEuF,MACvB,CACD,EAED,MAAMhC,EAAauB,OAAOW,QAAQH,CAAa,EAC5C5N,OAAO,CAAC,CAACgO,EAAGC,CAAK,IAAMA,EAAMH,OAAS,CAAC,EACvCI,KAAK,CAAC,CAACF,EAAGV,CAAC,EAAG,CAACa,EAAIZ,CAAC,IAAOA,EAAEM,KAAON,EAAEO,MAAUR,EAAEO,KAAOP,EAAEQ,KAAM,EACjEM,MAAM,EAAG,CAAC,EACVpX,IAAI,CAAC,CAACsR,CAAK,IAAMA,CAAK,EAGnByD,EAAwB,CAAA,EAC1B2B,EAAe5C,cAAgB,aACjCiB,EAAYxG,KAAK,wDAAwD,EACzEwG,EAAYxG,KAAK,8BAA8B,GACtCmI,EAAe5C,cAAgB,QACxCiB,EAAYxG,KAAK,mDAAmD,EACpEwG,EAAYxG,KAAK,2BAA2B,GACnCmI,EAAe5C,cAAgB,QAAU4C,EAAe5C,cAAgB,SACjFiB,EAAYxG,KAAK,mDAAmD,EACpEwG,EAAYxG,KAAK,8BAA8B,EAC/CwG,EAAYxG,KAAK,qCAAqC,IAEtDwG,EAAYxG,KAAK,mDAAmD,EACpEwG,EAAYxG,KAAK,qCAAqC,GAGxD,MAAMkG,EAAsD,CAC1DC,YAAAA,EACAC,aAAclB,GAAgBiB,CAAW,EACzCE,eAAgBf,GAAuB6C,EAAe5C,WAAW,EACjEH,QAAS+C,EAAe/C,QACxBkB,WAAAA,EACAC,UAAW4B,EAAe5C,cAAgB,aAAe4C,EAAe5C,cAAgB,OAAS,MACtF4C,EAAe5C,cAAgB,UAAY,SAAW,OACjEiB,YAAAA,CAAAA,EAIIsC,EAAsB7C,EACzBxL,OAAOsO,GAAKA,EAAEb,aAAe,CAAC,EAC9BS,KAAK,CAACZ,EAAGC,IAAMA,EAAE5C,QAAU2C,EAAE3C,OAAO,EAEjCqB,EAAsBqC,EAAoBD,MAAM,EAAG,CAAC,EAAEpX,IAASsX,GAAAA,EAAEjW,IAAI,EACrE4T,EAAuBoC,EAAoBD,MAAM,EAAE,EAAEpX,IAAIsX,GAAKA,EAAEjW,IAAI,EAEnE,MAAA,CACLmT,mBAAAA,EACAC,sBAAAA,EACAO,oBAAAA,EACAC,qBAAAA,EACAC,oBAAqBlB,EAAO7L,OAC5B+H,gBAAiBhR,IAAK,CACxB,EACC,CAAC8U,CAAM,CAAC,EAITpJ,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CAEbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,EClOMuD,GAAyBhU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,kBAAA,GAAA,EAGzB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC1B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAAM,EAGvDC,GAAkBrU,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,yEAAA,uCAAA,kDAAA,kBAAA,+IAAA,0BAAA,EAId,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAG3C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAGxB,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAcpD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAMhEC,GAAiBC,EAAAA,GAAE3U,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,oIAAA,oBAAA,EACV,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBa,KAAM,UAE1C,CAAC,CAAEb,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAO1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAKrEM,GAAwBhV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,UAAA,0EAAA,YAAA,IAAA,kBAAA,qBAAA,eAAA,0CAAA,EAGxB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAClC,CAAC,CAAEC,SAAAA,EAAUjB,MAAAA,CAAM,aAC1BiB,OAAAA,IACIjB,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,UAAW,yBACzBlB,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,yBAIxB,CAAC,CAAEnB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YACxC,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAAS,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAC3E,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBqB,OAAQ,UACxC,CAAC,CAAEJ,SAAAA,EAAUjB,MAAAA,CAAM,aACrCiB,OAAAA,IACIjB,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,UAAW,yBACzBlB,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAChB,CAAC,CAAEU,SAAAA,EAAUjB,MAAAA,CAAM,WAC/BiB,OAAAA,EACI,KAAGjB,EAAAA,EAAMK,SAANL,YAAAA,EAAckB,UAAW,2BAC5B,cAAa,EAQfI,GAAmBvV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gFAAA,4BAAA,GAAA,EAIR,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC3B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAGrFiB,GAAsBzV,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGtB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,OAAM,EAG7CE,GAAkBZ,EAAAA,GAAE7U,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,mBAAA,mBAAA,EACX,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiB0B,MAAO,YAE3C,CAAC,CAAE1B,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,UAAS,EAI1Da,GAAmBtV,EAAAA,KAAIL,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,YAAA,IAAA,kBAAA,cAAA,qCAAA,iDAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAC7C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe4B,MAAO,OAAS,CAAC,CAAE5B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAC5E,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBqB,OAAQ,UAC/C,CAAC,CAAErB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBgB,KAAM,WAE/B,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAK9EsB,GAA0B9V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAG1B,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,OAAM,EAG7CO,GAAsB/V,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGtB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,MAAK,EAG5CW,GAAmBnR,EAAAA,MAAK5E,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,8GAAA,yBAAA,uGAAA,EACjB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAAS,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC9E,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcgC,aAAc,qBACrC,CAAC,CAAEhC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBuB,KAAM,OACjD,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WACxC,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YAQjC,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBAChC,CAAC,CAAET,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAUpFwB,GAAsBC,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,cAAA,UAAA,qBAAA,kBAAA,YAAA,IAAA,cAAA,uEAAA,sJAAA,oDAAA,gGAAA,EAClB,CAAC,CAAEiW,SAAAA,EAAUnC,MAAAA,CAAM,IAC/BmC,OAAAA,OAAAA,IAAa,YACTnC,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBACzB,eACG,CAAC,CAAE0B,SAAAA,EAAUnC,MAAAA,CAAM,IAC1BmC,SAAAA,OAAAA,IAAa,YACTnC,EAAAA,EAAMK,SAANL,YAAAA,EAAcoC,cAAe,YAC7BpC,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,yBACjB,CAAC,CAAEgB,SAAAA,EAAUnC,MAAAA,CAAM,IACrCmC,SAAAA,OAAAA,IAAa,YACTnC,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,yBACzBT,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBACb,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBuB,KAAM,OAC/C,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAAS,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC/E,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YAK5C,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAQ3B,CAAC,CAAEmB,SAAAA,EAAUnC,MAAAA,CAAM,IAC/BmC,SAAAA,OAAAA,IAAa,YACTnC,EAAAA,EAAMK,SAANL,YAAAA,EAAcqC,cAAe,wBAC7BrC,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAEP,CAAC,CAAE6B,SAAAA,EAAUnC,MAAAA,CAAM,IAAA,OACzCmC,OAAAA,IAAa,UACT,KAAGnC,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,2BAC5B,qBAAoB,EAuBjB6B,GAA8CA,CAAC,CAC1DnU,UAAAA,EACAgF,UAAAA,EAAY,GACZoP,aAAAA,EAAe,GACfhI,YAAAA,EACAnH,aAAAA,EACAmI,aAAAA,EACAI,UAAAA,EACA3C,MAAAA,EAAQ,qBACV,IAAM,CACEwJ,MAAAA,EAAU,CAAC,CAACjI,EACZkI,MAAchb,OAAO4L,YAAc/K,EAAAA,MAAM,GAAG,EAAE,CAAC,EAE/C4S,EAAoBwH,GAA2C,CAC/DnH,GACWmH,EAAAA,EAAEC,OAAO3a,KAAK,CAC7B,EAIA,OAAAsF,OAACyS,IAAgB,UAAA5R,EAEf,SAAA,CAAAb,OAAC8S,GACC,CAAA,SAAA,CAAA9S,OAACoT,GAAO,CAAA,SAAA,CAAA,aACInT,EAAAA,IAAC,QAAK,SAAK,OAAA,CAAA,CAAA,EACvB,QACCwT,GAAe,CAAA,SAAUyB,EACvBA,SAAAA,EAAUjI,EAAc,UAC3B,CAAA,EACF,SAGC+G,GACC,CAAA,SAAA,CAAAhU,OAACkU,GACC,CAAA,SAAA,CAAAjU,EAAAA,IAACkU,IAAUzI,SAAMA,CAAA,CAAA,EAChBwJ,UACEb,GAAS,CAAA,SAAA,CAAA,MACJpH,CAAAA,EACN,CAAA,EAEJ,SAECsH,GACC,CAAA,SAAA,CAAAtU,MAACuU,GACC,CAAA,SAAAvU,EAAAA,IAACwU,GACC,CAAA,KAAK,OACL,MAAO3O,GAAgB,GACvB,SAAU8H,EACV,IAAKuH,EACL,MAAM,+BAA+B,CAAA,EAEzC,EAEC9G,GACCrO,EAAA,KAAC2U,GACC,CAAA,SAAS,UACT,QAAStG,EACT,SAAUxI,EACV,MAAOA,EAAY,sBAAwB,qBAE1CA,SAAAA,CAAAA,GAAaoP,EAAe,IAAM,KAClCpP,EAAY,aAAe,SAAA,EAC9B,CAAA,EAEJ,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EC7RMyP,GAAuB7W,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6BAAA,MAAA,8BAAA,qBAAA,EAGpB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QACb,CAAC,CAAEH,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAIrFsC,GAAaX,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,MAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,6CAAA,WAAA,yCAAA,cAAA,8MAAA,2FAAA,qBAAA,0FAAA,uEAAA,uDAAA,qCAAA,IAAA,cAAA,IAAA,EACZ,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC3C,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAG/B,CAAC,CAAE4C,UAAAA,EAAW9C,MAAAA,CAAM,aAC3B8C,OAAAA,IAAY9C,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,YAAYd,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,yBAC5E,CAAC,CAAE4B,UAAAA,CAAU,IAAOA,EAAY,cAAgB,UAE3C,CAAC,CAAED,UAAAA,CAAU,IAAOA,EAAY,MAAQ,MAC1C,CAAC,CAAE9C,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBuB,KAAM,QAQ5C,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAegB,KAAM,OAU3B,CAAC,CAAEhB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBAClC,CAAC,CAAEqC,UAAAA,CAAU,IAAOA,EAAY,EAAI,EAO/C,CAAC,CAAEA,UAAAA,EAAW9C,MAAAA,CAAM,aAC3B8C,OAAAA,IAAY9C,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,YAAYd,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAKpE,CAAC,CAAEgC,UAAAA,EAAW9C,MAAAA,CAAM,aAChC8C,OAAAA,IAAY9C,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,yBAAyBT,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,yBASjG,CAAC,CAAE4B,UAAAA,CAAU,IACbA,GACA;AAAA;AAAA;AAAA,IAOW,CAAC,CAAE/C,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAC3C,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC3B,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,WAAU,EAI3D4B,GAAiB3W,EAAAA,KAAIL,WAAA,CAAAC,YAAA,UAAAC,YAAA,cAAA,CAM1B,EAAA,CAAA,0DAAA,CAAA,EAEK+W,GAAkB5W,EAAAA,KAAIL,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAI3B,EAAA,CAAA,yCAAA,CAAA,EAKKgX,GAAqF,CACzF9N,SAAU,CACR+N,KAAM,KACNpE,MAAO,gBACPxF,YAAa,uDACf,EACA6J,KAAM,CACJD,KAAM,KACNpE,MAAO,qBACPxF,YAAa,4EACf,EACAjE,OAAQ,CACN6N,KAAM,KACNpE,MAAO,kBACPxF,YAAa,sEACf,EACApB,KAAM,CACJgL,KAAM,KACNpE,MAAO,cACPxF,YAAa,+BACf,CACF,EAYa8J,GAA0CA,CAAC,CACtDC,UAAAA,EACAC,YAAAA,EACAC,SAAAA,EAAW,GACXrV,UAAAA,CACF,IAAM,CACEsV,MAAAA,EAAkBC,GAAkB,CACnCF,GACHD,EAAYG,CAAG,CACjB,EAGIC,EAAgBA,CAAC/L,EAA4B8L,IAAkB,EAC9D9L,EAAM1H,MAAQ,SAAW0H,EAAM1H,MAAQ,MAAQ,CAACsT,IACnD5L,EAAMgM,eAAe,EACrBL,EAAYG,CAAG,EACjB,EAIA,OAAAnW,EAAA,IAACqV,GAAc,CAAA,UAAAzU,EAAsB,KAAK,UACtCwQ,gBAAOC,KAAKsE,EAAU,EAAiB3a,IAAamb,GAAA,CAC9CG,MAAAA,EAASX,GAAWQ,CAAG,EACvBI,EAAWR,IAAcI,EAE/B,OACGpW,EAAAA,KAAAuV,GAAA,CAEC,UAAWiB,EACX,UAAWN,EACX,QAAS,IAAMC,EAAeC,CAAG,EACjC,aAAkBC,EAAcjB,EAAGgB,CAAG,EACtC,SAAAF,EACA,KAAK,MACL,gBAAeM,EACf,gBAAe,eAAeJ,IAC9B,SAAUF,EAAW,GAAK,EAC1B,MAAOK,EAAOtK,YAEd,SAAA,CAAChM,EAAAA,IAAAyV,GAAA,CAASa,WAAOV,IAAK,CAAA,EACtB5V,EAAAA,IAAC0V,GAAUY,CAAAA,SAAAA,EAAO9E,KAAM,CAAA,CAAA,CAAA,EAbnB2E,CAcP,CAEH,CAAA,CACH,CAAA,CAEJ,EC9JMK,EAA6B,CAAC,WAAY,OAAQ,SAAU,MAAM,EAKlEC,GAAsB,0CAKtBC,GAAqBA,CAACC,EAAoBC,IAAmC,CAC7E,GAAA,CACIC,MAAAA,EAASvS,aAAaC,QAAQoS,CAAU,EAC9C,GAAIE,GAAUL,EAAelY,SAASuY,CAAkB,EAC/CA,OAAAA,QAEFlS,GACC1D,QAAAA,KAAK,8CAA+C0D,CAAK,CACnE,CACOiS,OAAAA,CACT,EAKME,GAAmBA,CAACH,EAAoBR,IAAwB,CAChE,GAAA,CACWrR,aAAAA,QAAQ6R,EAAYR,CAAG,QAC7BxR,GACC1D,QAAAA,KAAK,4CAA6C0D,CAAK,CACjE,CACF,EAOaoS,GAAqBA,CAAC,CACjCH,WAAAA,EAAa,WACbD,WAAAA,EAAaF,EACU,EAAI,KAAiC,CAGtD,KAAA,CAACV,EAAWiB,CAAiB,EAAItX,EAAAA,SAAmB,IACxDgX,GAAmBC,EAAYC,CAAU,CAC3C,EAKMK,EAAe7J,cAAa+I,GAAkB,CAC9CK,EAAelY,SAAS6X,CAAG,IAC7Ba,EAAkBb,CAAG,EACrBW,GAAiBH,EAAYR,CAAG,EAClC,EACC,CAACQ,CAAU,CAAC,EAKTO,EAAU9J,EAAAA,YAAY,IAAM,CAE1B+J,MAAAA,GADeX,EAAeY,QAAQrB,CAAS,EACnB,GAAKS,EAAerT,OACzCqT,EAAAA,EAAeW,CAAS,CAAC,CAAA,EACrC,CAACpB,EAAWkB,CAAY,CAAC,EAKtBI,EAAcjK,EAAAA,YAAY,IAAM,CAC9BkK,MAAAA,EAAed,EAAeY,QAAQrB,CAAS,EAC/CwB,EAAgBD,IAAiB,EAAId,EAAerT,OAAS,EAAImU,EAAe,EACzEd,EAAAA,EAAee,CAAa,CAAC,CAAA,EACzC,CAACxB,EAAWkB,CAAY,CAAC,EAKtBO,EAAcpK,cAAa+I,GACxBJ,IAAcI,EACpB,CAACJ,CAAS,CAAC,EAKR0B,EAAcrK,cAAa+I,GACxBK,EAAeY,QAAQjB,CAAG,EAChC,CAAE,CAAA,EAGLxW,OAAAA,EAAAA,UAAU,IAAM,CACRyW,MAAAA,EAAiB/L,GAAyB,eAE1CqN,GAAAA,IAAAA,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,WACpCF,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,cACpCF,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,UAKxC,KAAKvN,EAAMwN,SAAWxN,EAAMyN,UAAY,CAACzN,EAAM0N,SAC7C,OAAQ1N,EAAM1H,IAAG,CACf,IAAK,YACH0H,EAAMgM,eAAe,EACTgB,IACZ,MACF,IAAK,aACHhN,EAAMgM,eAAe,EACba,IACR,KACJ,CAIE7M,GAAAA,EAAM1H,KAAO,KAAO0H,EAAM1H,KAAO,KAAO,CAAC0H,EAAMwN,SAAW,CAACxN,EAAMyN,QAAS,CAC5E,MAAME,EAAW1H,SAASjG,EAAM1H,GAAG,EAAI,EACnCqV,EAAWxB,EAAerT,SAC5BkH,EAAMgM,eAAe,EACRG,EAAAA,EAAewB,CAAQ,CAAC,GAKzC,GAAI3N,EAAM4N,QAAU,CAAC5N,EAAMwN,SAAW,CAACxN,EAAMyN,QACnCzN,OAAAA,EAAM1H,IAAIuV,YAAa,EAAA,CAC7B,IAAK,IACH7N,EAAMgM,eAAe,EACrBY,EAAa,UAAU,EACvB,MACF,IAAK,IACH5M,EAAMgM,eAAe,EACrBY,EAAa,MAAM,EACnB,MACF,IAAK,IACH5M,EAAMgM,eAAe,EACrBY,EAAa,QAAQ,EACrB,MACF,IAAK,IACH5M,EAAMgM,eAAe,EACrBY,EAAa,MAAM,EACnB,KACJ,CAIE5M,EAAM1H,IAAIuV,YAAY,IAAM,KAAO,CAAC7N,EAAMwN,SAAW,CAACxN,EAAMyN,SAAW,CAACzN,EAAM4N,UAE5EP,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,WACpCF,EAAAA,SAASC,gBAATD,YAAAA,EAAwBE,WAAY,aACtCvN,EAAMgM,eAAe,EACrBY,EAAa,UAAU,GAE3B,EAGKkB,cAAAA,iBAAiB,UAAW/B,CAAa,EACzC,IAAMgC,OAAOC,oBAAoB,UAAWjC,CAAa,CAC/D,EAAA,CAACc,EAASG,EAAaJ,CAAY,CAAC,EAEhC,CACLlB,UAAAA,EACAkB,aAAAA,EACAC,QAAAA,EACAG,YAAAA,EACAG,YAAAA,EACAC,YAAAA,EACAa,cAAe9B,CAAAA,CAEnB,EC7IM+B,GAAwBvJ,GAAoD,CAC1EwJ,MAAAA,MAAeC,IAErBzJ,EAAO3F,QAAiB+G,GAAA,OAChBsI,MAAAA,GAAetI,EAAAA,EAAM9D,QAAN8D,YAAAA,EAAauI,cAC9BD,IACGF,EAASI,IAAIF,CAAY,GACnBG,EAAAA,IAAIH,EAAc,CAAA,CAAE,EAE/BF,EAASM,IAAIJ,CAAY,EAAGnP,KAAK6G,CAAK,EACxC,CACD,EAED,MAAM2I,EAAwC,CAAA,EAErC1P,OAAAA,EAAAA,QAAQ,CAAC2P,EAAaC,IAAc,CAC3C,MAAMxH,EAAcuH,EAAY7V,OAC1B0O,EAAOmH,EAAYhV,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAC3DwL,EAAU8C,EAAc,EAAKI,EAAOJ,EAAe,IAAM,EAEzDyH,EAAaF,EAChBhe,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAE5DiW,EAAYJ,EACfhe,IAAIyV,GAAKA,EAAEL,MAAMiJ,sBAAsB,EACvCrV,OAAQsV,GAAsCA,GAAM,IAAI,EACrDC,EAAaH,EAAUjW,OAAS,EACpCiW,EAAUxI,OAAO,CAACC,EAAKyI,IAAMzI,EAAMyI,EAAG,CAAC,EAAIF,EAAUjW,OAAS,EAG1DqW,MAAiBf,IACvBO,EAAY3P,QAAiB+G,GAAA,CACrBqJ,MAAAA,EAAUrJ,EAAMA,MAAMqJ,SAAW,UAClCD,EAAWZ,IAAIa,CAAO,GACzBD,EAAWX,IAAIY,EAAS,CAAE5H,KAAM,EAAGC,MAAO,CAAA,CAAG,EAEzC4H,MAAAA,EAAcF,EAAWV,IAAIW,CAAO,EAC9B3H,EAAAA,QACR1B,EAAMA,MAAMM,WAAa,OACfmB,EAAAA,MACd,CACD,EAED,IAAI8H,EAAc,UACdC,EAAqB,EACdvQ,EAAAA,QAAQ,CAAC/D,EAAMmU,IAAY,CAC9BI,MAAAA,EAAiBvU,EAAKwM,MAAQ,EAAKxM,EAAKuM,KAAOvM,EAAKwM,MAAS,IAAM,EACrE+H,EAAiBD,GAAsBtU,EAAKwM,OAAS,IAClC+H,EAAAA,EACPJ,EAAAA,EAChB,CACD,EAEDV,EAAkBxP,KAAK,CACrB0P,UAAAA,EACAxH,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACA4I,WAAAA,EACAI,YAAAA,EACAG,YAAanL,CAAAA,CACd,CAAA,CACF,EAEMoK,EACJ/U,OAAOsO,GAAKA,EAAEb,aAAe,CAAC,EAC9BS,KAAK,CAACZ,EAAGC,IAAMA,EAAE5C,QAAU2C,EAAE3C,OAAO,EACpCyD,MAAM,EAAG,CAAC,CACf,EAKM2H,GAA4B/K,GAAoD,CAC9EgL,MAAAA,MAAqBvB,IAE3BzJ,EAAO3F,QAAiB+G,GAAA,WAChB8C,MAAAA,IAAU9C,EAAAA,EAAM9D,QAAN8D,YAAAA,EAAauI,gBAAiB,GACxCsB,IAAY7J,EAAAA,EAAM9D,QAAN8D,YAAAA,EAAa8J,kBAAmB,GAC5CC,IAAY/J,EAAAA,EAAM9D,QAAN8D,YAAAA,EAAagK,kBAAmB,GAE9ClH,GAAAA,GAAW+G,GAAaE,EAAW,CAC/BxX,MAAAA,EAAM,GAAGuQ,KAAW+G,KAAaE,IAClCH,EAAepB,IAAIjW,CAAG,GACVkW,EAAAA,IAAIlW,EAAK,CAAA,CAAE,EAE5BqX,EAAelB,IAAInW,CAAG,EAAG4G,KAAK6G,CAAK,EACrC,CACD,EAED,MAAMiK,EAAmC,CAAA,EAE1BhR,OAAAA,EAAAA,QAAQ,CAACiR,EAAa3X,IAAQ,CAC3C,KAAM,CAACuQ,EAAS+G,EAAWE,CAAS,EAAIxX,EAAI5H,MAAM,GAAG,EAC/C0W,EAAc6I,EAAYnX,OAEhC,GAAIsO,EAAc,EAAG,OAGf9C,MAAAA,EADO2L,EAAYtW,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACzCsO,EAAe,IAEjCyH,EAAaoB,EAChBtf,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAE5DiW,EAAYkB,EACftf,IAAIyV,GAAKA,EAAEL,MAAMiJ,sBAAsB,EACvCrV,OAAQsV,GAAsCA,GAAM,IAAI,EACrDC,EAAaH,EAAUjW,OAAS,EACpCiW,EAAUxI,OAAO,CAACC,EAAKyI,IAAMzI,EAAMyI,EAAG,CAAC,EAAIF,EAAUjW,OAAS,EAE1D4N,EAAWuJ,EACdtf,IAAIyV,GAAKA,EAAEL,MAAMY,aAAe,CAAC,EACjCJ,OAAO,CAACC,EAAK0J,IAAO1J,EAAM0J,EAAI,CAAC,EAG5Bf,MAAiBf,IACvB6B,EAAYjR,QAAiB+G,GAAA,CACrBqJ,MAAAA,EAAUrJ,EAAMA,MAAMqJ,SAAW,UAClCD,EAAWZ,IAAIa,CAAO,GACzBD,EAAWX,IAAIY,EAAS,CAAE5H,KAAM,EAAGC,MAAO,CAAA,CAAG,EAEzC4H,MAAAA,EAAcF,EAAWV,IAAIW,CAAO,EAC9B3H,EAAAA,QACR1B,EAAMA,MAAMM,WAAa,OACfmB,EAAAA,MACd,CACD,EAED,MAAM2I,EAAWC,MAAMC,KAAKlB,EAAWzH,SAAS,EAAE/W,IAAI,CAAC,CAAC2f,EAAarV,CAAI,KAAO,CAC9EqV,YAAAA,EACAhM,QAASrJ,EAAKwM,MAAQ,EAAKxM,EAAKuM,KAAOvM,EAAKwM,MAAS,IAAM,EAC3D9C,OAAQ1J,EAAKwM,KACb,EAAA,EAGF,IAAIlC,EAAiB,GACjBtG,EAAyC,MAEzCqF,GAAW,IAAM8C,GAAe,GACjB7B,EAAA,yCAAyCjB,EAAQiM,QAAQ,CAAC,eAChEtR,EAAA,QACFqF,GAAW,IAAMgC,GAAgB,KAC1Cf,EAAiB,iDAAiDjB,EAAQiM,QAAQ,CAAC,gBAAgBjK,EAAaiK,QAAQ,CAAC,UAC9GtR,EAAA,QACFqF,GAAW,IACHiB,EAAA,qCAAqCjB,EAAQiM,QAAQ,CAAC,eAC5DtR,EAAA,WAEMsG,EAAA,mDAAmDjB,EAAQiM,QAAQ,CAAC,eAC1EtR,EAAA,OAGb+Q,EAAa9Q,KAAK,CAChB2J,QAAAA,EACA+G,UAAAA,EACAE,UAAAA,EACArL,YAAa,CACX2C,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACA4I,WAAAA,EACAxI,SAAAA,CACF,EACAyJ,SAAAA,EACA5K,eAAAA,EACAtG,SAAAA,CAAAA,CACD,CAAA,CACF,EAEM+Q,EACJnI,KAAK,CAACZ,EAAGC,IAAMA,EAAEzC,YAAYH,QAAU2C,EAAExC,YAAYH,OAAO,EAC5DyD,MAAM,EAAG,EAAE,CAChB,EAKMyI,GAAgC7L,GAAyD,CACvF8L,MAAAA,MAAmBrC,IAEzBzJ,EAAO3F,QAAiB+G,GAAA,OAChB+J,MAAAA,GAAY/J,EAAAA,EAAM9D,QAAN8D,YAAAA,EAAagK,gBAC3BD,IACGW,EAAalC,IAAIuB,CAAS,GAChBtB,EAAAA,IAAIsB,EAAW,CAAA,CAAE,EAEhCW,EAAahC,IAAIqB,CAAS,EAAG5Q,KAAK6G,CAAK,EACzC,CACD,EAED,MAAM2K,EAAiD,CAAA,EAE1C1R,OAAAA,EAAAA,QAAQ,CAAC2R,EAAiBC,IAAoB,CACzD,MAAMxJ,EAAcuJ,EAAgB7X,OAEpC,GAAIsO,EAAc,EAAG,OAGf9C,MAAAA,EADOqM,EAAgBhX,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAC7CsO,EAAe,IAEjCyH,EAAa8B,EAChBhgB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAG5D+X,MAAezC,IACrBuC,EAAgB3R,QAAiB+G,GAAA,CACzB+K,MAAAA,EAAQ/K,EAAMA,MAAMc,WACrBgK,EAAStC,IAAIuC,CAAK,GACrBD,EAASrC,IAAIsC,EAAO,CAAEtJ,KAAM,EAAGC,MAAO,CAAA,CAAG,EAErCsJ,MAAAA,EAAYF,EAASpC,IAAIqC,CAAK,EAC1BrJ,EAAAA,QACN1B,EAAMA,MAAMM,WAAa,OACjBmB,EAAAA,MACZ,CACD,EAEKwJ,MAAAA,EAAaZ,MAAMC,KAAKQ,EAASnJ,SAAS,EAC7C/N,OAAO,CAAC,CAACgO,EAAG1M,CAAI,IAAMA,EAAKwM,OAAS,CAAC,EACrCI,KAAK,CAACZ,EAAGC,IAAOA,EAAE,CAAC,EAAEM,KAAON,EAAE,CAAC,EAAEO,MAAUR,EAAE,CAAC,EAAEO,KAAOP,EAAE,CAAC,EAAEQ,KAAM,EAClEM,MAAM,EAAG,CAAC,EACVpX,IAAI,CAAC,CAACmgB,EAAOnJ,CAAC,IAAMmJ,CAAK,EAGtB3B,MAAiBf,IACvBuC,EAAgB3R,QAAiB+G,GAAA,CACzBqJ,MAAAA,EAAUrJ,EAAMA,MAAMqJ,SAAW,UAClCD,EAAWZ,IAAIa,CAAO,GACzBD,EAAWX,IAAIY,EAAS,CAAE5H,KAAM,EAAGC,MAAO,CAAA,CAAG,EAEzC4H,MAAAA,EAAcF,EAAWV,IAAIW,CAAO,EAC9B3H,EAAAA,QACR1B,EAAMA,MAAMM,WAAa,OACfmB,EAAAA,MACd,CACD,EAEKyJ,MAAAA,EAAeb,MAAMC,KAAKlB,EAAWzH,SAAS,EACjD/N,OAAO,CAAC,CAACgO,EAAG1M,CAAI,IAAMA,EAAKwM,OAAS,CAAC,EACrCI,KAAK,CAACZ,EAAGC,IAAOA,EAAE,CAAC,EAAEM,KAAON,EAAE,CAAC,EAAEO,MAAUR,EAAE,CAAC,EAAEO,KAAOP,EAAE,CAAC,EAAEQ,KAAM,EAClEM,MAAM,EAAG,CAAC,EACVpX,IAAI,CAAC,CAACye,EAASzH,CAAC,IAAMyH,CAAO,EAGhC,IAAI7J,EAAiB,GACjBjB,GAAW,GACbiB,EAAiB,cAAcqL,+BAA6CtM,EAAQiM,QAAQ,CAAC,MACpFjM,GAAW,GACpBiB,EAAiB,UAAUqL,yBAAuCtM,EAAQiM,QAAQ,CAAC,MAEnFhL,EAAiB,iBAAiBqL,4BAA0CtM,EAAQiM,QAAQ,CAAC,MAG/FG,EAAsBxR,KAAK,CACzB0R,gBAAAA,EACAnM,YAAa,CACX2C,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACAmJ,YAAanL,CACf,EACA0M,WAAAA,EACAC,aAAAA,EACA1L,eAAAA,CAAAA,CACD,CAAA,CACF,EAEMmL,EACJ7I,KAAK,CAACZ,EAAGC,IAAMA,EAAEzC,YAAYH,QAAU2C,EAAExC,YAAYH,OAAO,EAC5DyD,MAAM,EAAG,CAAC,CACf,EAKamJ,GAA+BA,IAAM,CAChD,KAAM,CAACvM,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,gDAAiDD,CAAG,EAClEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EAqDE,CACLsM,kBAnD+C1Z,EAAAA,QAAQ,IAAM,aACzDkN,GAAAA,EAAO7L,SAAW,EACb,MAAA,CACLsY,iBAAkB,CAAE,EACpBC,mBAAoB,CAAE,EACtBC,iBAAkB,CAAE,EACpBZ,sBAAuB,CAAE,EACzBa,uBAAwB,CACtBlD,aAAc,oBACdmD,eAAgB,oBAChBZ,gBAAiB,oBACjBa,UAAW,qDACXC,gBAAiB,GACjBC,kBAAmB,CACrB,CAAA,EAIEP,MAAAA,EAAmBlD,GAAqBvJ,CAAM,EAC9C0M,EAAqBnD,GAAqBvJ,CAAM,EAChD2M,EAAmB5B,GAAyB/K,CAAM,EAClD+L,EAAwBF,GAA6B7L,CAAM,EAG3DiN,EAAYN,EAAiB,CAAC,EAC9BC,EAAyBK,EAAY,CACzCvD,aAAcuD,EAAU/I,QACxB2I,eAAgBI,EAAUhC,UAC1BgB,gBAAiBgB,EAAU9B,UAC3B2B,UAAW,gCAAgCG,EAAUnN,YAAYH,QAAQiM,QAAQ,CAAC,oBAAoBqB,EAAUnN,YAAY6B,aAAaiK,QAAQ,CAAC,aAClJmB,gBAAiBE,EAAUnN,YAAYH,QACvCqN,kBAAmBC,EAAUnN,YAAY6B,YAAAA,EACvC,CACF+H,eAAc+C,EAAAA,EAAiB,CAAC,IAAlBA,YAAAA,EAAqBxC,YAAa,UAChD4C,eAAgB,UAChBZ,kBAAiBF,EAAAA,EAAsB,CAAC,IAAvBA,YAAAA,EAA0BE,kBAAmB,UAC9Da,UAAW,wCACXC,kBAAiBN,EAAAA,EAAiB,CAAC,IAAlBA,YAAAA,EAAqB9M,UAAW,GACjDqN,oBAAmBP,EAAAA,EAAiB,CAAC,IAAlBA,YAAAA,EAAqB9K,eAAgB,CAAA,EAGnD,MAAA,CACL8K,iBAAAA,EACAC,mBAAAA,EACAC,iBAAAA,EACAZ,sBAAAA,EACAa,uBAAAA,CAAAA,CACF,EACC,CAAC5M,CAAM,CAAC,EAITpJ,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,EClXMiN,GAAkB,CACtBC,QAAS,CACP,CAAEC,MAAO,QAASC,IAAK,QAAS7K,MAAO,cAAexF,YAAa,+BAAA,EACnE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,iBAAkBxF,YAAa,4BAAA,EACtE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,mBAAoBxF,YAAa,4BAAA,EACxE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,oBAAqBxF,YAAa,yBAAA,CAA2B,EAEtGsQ,YAAa,CACX,CAAEF,MAAO,QAASC,IAAK,QAAS7K,MAAO,iBAAkBxF,YAAa,yBAAA,EACtE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,sBAAuBxF,YAAa,8BAAA,EAC3E,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,uBAAwBxF,YAAa,sBAAA,EAC5E,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,uBAAwBxF,YAAa,8BAAA,CAAgC,EAE9GuQ,IAAK,CACH,CAAEH,MAAO,QAASC,IAAK,QAAS7K,MAAO,kBAAmBxF,YAAa,wBAAA,EACvE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,eAAgBxF,YAAa,sBAAA,CAAwB,EAE9FwQ,WAAY,CACV,CAAEJ,MAAO,QAASC,IAAK,QAAS7K,MAAO,mBAAoBxF,YAAa,wBAAA,EACxE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,mBAAoBxF,YAAa,mBAAA,EACxE,CAAEoQ,MAAO,QAASC,IAAK,QAAS7K,MAAO,cAAexF,YAAa,mBAAA,CAAqB,CAE5F,EAKMjP,EAAiBpB,GAA4B,CAC3C,KAAA,CAACd,EAAOC,CAAO,EAAIa,EAAQZ,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACtD,OAAOJ,EAAQ,GAAKC,CACtB,EAKM2hB,GAAiBA,IAAc,CAC7BhgB,MAAAA,MAAUvC,KAChB,MAAO,GAAGuC,EAAI+R,SAAWkO,EAAAA,SAAAA,EAAWlhB,SAAS,EAAG,GAAG,KAAKiB,EAAIkgB,WAAW,EAAED,SAAWlhB,EAAAA,SAAS,EAAG,GAAG,GACrG,EAKMohB,GAAiBA,CAACC,EAAqBzE,IAAgC,CACrE0E,MAAAA,EAAU/f,EAAc8f,CAAW,EACnCT,EAAQrf,EAAcqb,EAAOgE,KAAK,EAClCC,EAAMtf,EAAcqb,EAAOiE,GAAG,EAE7BS,OAAAA,GAAWV,GAASU,GAAWT,CACxC,EAKMU,GAAmBA,CAACF,EAAqBzE,IAA+B,CACtE0E,MAAAA,EAAU/f,EAAc8f,CAAW,EACnCR,EAAMtf,EAAcqb,EAAOiE,GAAG,EAEpC,OAAOnf,KAAK8f,IAAI,EAAGX,EAAMS,CAAO,CAClC,EAKMG,GAAkB7M,GAA4C,CAC5DqJ,MAAAA,EAAUrJ,EAAMA,MAAMqJ,QACxBA,GAAAA,EAAgBA,OAAAA,EAGdyD,MAAAA,EAAY9M,EAAMA,MAAMC,WAC9B,GAAI,CAAC6M,EAAkB,OAAA,KAEjBC,MAAAA,EAAcpgB,EAAcmgB,CAAS,EAG3C,OAAIC,GAAepgB,EAAc,OAAO,GAAKogB,GAAepgB,EAAc,OAAO,EACxE,UACEogB,GAAepgB,EAAc,OAAO,GAAKogB,GAAepgB,EAAc,OAAO,EAC/E,cACEogB,GAAepgB,EAAc,OAAO,GAAKogB,GAAepgB,EAAc,OAAO,EAC/E,MACEogB,GAAepgB,EAAc,OAAO,GAAKogB,GAAepgB,EAAc,OAAO,EAC/E,aAGF,IACT,EAKMqgB,GAAiBA,CACrBC,EACA1C,EACA3L,IACoB,CACdsO,MAAAA,EAAUpB,GAAgBmB,CAAW,EACrCE,EAAgBvO,EAAOhL,OAAgBoM,GAC3B6M,GAAe7M,CAAK,IACjBuK,CACpB,EAGK6C,EAAkCF,EAAQtiB,IAAcod,GAAA,CACtDqF,MAAAA,EAAeF,EAAcvZ,OAAgBoM,GAAA,CAC3C8M,MAAAA,EAAY9M,EAAMA,MAAMC,WAC9B,OAAK6M,EACEN,GAAeM,EAAW9E,CAAM,EADhB,EACgB,CACxC,EAEK3G,EAAcgM,EAAata,OAC3BqN,EAAgBiN,EAAazZ,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACrEwL,EAAU8C,EAAc,EAAKjB,EAAgBiB,EAAe,IAAM,EAElEyH,EAAauE,EAChBziB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAE5D4N,EAAW0M,EACdziB,IAAIyV,GAAKA,EAAEL,MAAMY,aAAe,CAAC,EACjCJ,OAAO,CAACC,EAAK0J,IAAO1J,EAAM0J,EAAI,CAAC,EAG5BmD,EAAeD,EAAazZ,UAAYyM,EAAEL,MAAMc,aAAe,SAAS,EACxEyM,EAAcF,EAAazZ,UAAYyM,EAAEL,MAAMc,aAAe,QAAQ,EAEtE0M,GAAgBF,EAAava,OAAS,EACzCua,EAAa1Z,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAASua,EAAava,OAAU,IAAM,EACxF0a,GAAeF,EAAYxa,OAAS,EACvCwa,EAAY3Z,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAASwa,EAAYxa,OAAU,IAAM,EAE5F,IAAI2a,EAAoC,KACpCJ,EAAava,QAAU,GAAKwa,EAAYxa,QAAU,EAClCya,EAAAA,GAAgBC,GAAe,UAAY,SACpDH,EAAava,QAAU,EACd2a,EAAA,UACTH,EAAYxa,QAAU,IACb2a,EAAA,UAIdC,MAAAA,EAAYpP,GAAW,IAAM8C,GAAe,EAC5CuM,EAAY5F,EAAO5G,MAAMlT,SAAS,SAAS,GAAK8Z,EAAO5G,MAAMlT,SAAS,SAAS,EAGrF,IAAIsR,EAAiB,GACrB,OAAImO,EACFnO,EAAiB,gBAAgBjB,EAAQiM,QAAQ,CAAC,gBAAgBjK,EAAaiK,QAAQ,CAAC,UAC/EjM,GAAW,IAAM8C,GAAe,EACzC7B,EAAiB,sBAAsBjB,EAAQiM,QAAQ,CAAC,cAC/CnJ,EAAc,EACvB7B,EAAiB,gDAEjBA,EAAiB,aAAajB,EAAQiM,QAAQ,CAAC,qCAG1C,CACLxC,OAAAA,EACAtJ,YAAa,CACX2C,YAAAA,EACAjB,cAAAA,EACA7B,QAAAA,EACAgC,aAAAA,EACAI,SAAAA,CACF,EACA+M,gBAAAA,EACAC,UAAAA,EACAC,UAAAA,EACApO,eAAAA,CAAAA,CACF,CACD,EAGK6B,EAAc8L,EAAcpa,OAC5BqN,EAAgB+M,EAAcvZ,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACtEwL,EAAU8C,EAAc,EAAKjB,EAAgBiB,EAAe,IAAM,EAElEyH,EAAaqE,EAChBviB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAG5D8a,EAAkBT,EAAexZ,UAAYka,EAAEpP,YAAY2C,YAAc,CAAC,EAC1E0M,EAAaF,EAAgB9a,OAAS,EAC1C8a,EAAgBrN,OAAO,CAACwN,EAAMtB,IAC5BA,EAAQhO,YAAYH,QAAUyP,EAAKtP,YAAYH,QAAUmO,EAAUsB,CACrE,EAAEhG,OAAS,KAEPiG,EAAcJ,EAAgB9a,OAAS,EAC3C8a,EAAgBrN,OAAO,CAAC0N,EAAOxB,IAC7BA,EAAQhO,YAAYH,QAAU2P,EAAMxP,YAAYH,QAAUmO,EAAUwB,CACtE,EAAElG,OAAS,KAGPyE,EAAcJ,KACd8B,EAAgBjB,EAAQhjB,KAAK4jB,GAAKtB,GAAeC,EAAaqB,CAAC,CAAC,GAAK,KACrEM,EAAgBD,EAAgBxB,GAAiBF,EAAa0B,CAAa,EAAI,EAG/EE,EAAiB1hB,EAAc8f,CAAW,EAC1C6B,EAAapB,EAAQhjB,KAAK4jB,GAAKnhB,EAAcmhB,EAAE9B,KAAK,EAAIqC,CAAc,GAAK,KAE3ElI,EAAWgI,IAAkB,KAEnC,IAAI3O,EAAiB,GACrB,GAAI2G,GAAYgI,EAAe,CAC7B,MAAMI,EAAwBnB,EAAeljB,KAAU4jB,GAAAA,EAAE9F,SAAWmG,CAAa,EACjF3O,GAAiB+O,GAAAA,YAAAA,EAAuB/O,iBAAkB,oCACjD8O,EAAY,CACrB,MAAME,EAAa7hB,EAAc2hB,EAAWtC,KAAK,EAAIqC,EACpC7O,EAAA,gBAAgB8O,EAAWlN,YAAYoN,iBAEvChP,EAAA,qBAGZ,MAAA,CACL+K,YAAAA,EACA0C,YAAAA,EACAC,QAASE,EACTqB,mBAAoB,CAClBpN,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACAwN,WAAAA,EACAE,YAAAA,CACF,EACAS,cAAe,CACbvI,SAAAA,EACAgI,cAAAA,EACAC,cAAAA,EACAE,WAAAA,EACA9O,eAAAA,CACF,CAAA,CAEJ,EAKamP,GAAiCA,IAAM,CAClD,KAAM,CAAC/P,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,kDAAmDD,CAAG,EACpEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EAGC8P,MAAAA,EAAqCld,EAAAA,QAAQ,IAC1C,CACLsb,GAAe,UAAW,UAAWpO,CAAM,EAC3CoO,GAAe,cAAe,cAAepO,CAAM,EACnDoO,GAAe,MAAO,MAAOpO,CAAM,EACnCoO,GAAe,aAAc,aAAcpO,CAAM,CAAC,EAEnD,CAACA,CAAM,CAAC,EAGLiQ,EAAoCnd,EAAAA,QAAQ,IAAM,CACtD,MAAM+a,EAAcJ,KACdyC,EAAgBF,EAAgB1kB,QAAUgY,EAAEwM,cAAcvI,QAAQ,GAAK,KAGvEkI,EAAiB1hB,EAAc8f,CAAW,EAC1CsC,EAAcH,EACjBhb,OAAYsO,GAAA,CAACA,EAAEwM,cAAcvI,QAAQ,EACrCjc,KAAUgY,GAAA,CACH8M,MAAAA,EAAc9M,EAAEgL,QAAQ,CAAC,EAC/B,OAAOvgB,EAAcqiB,EAAYhH,OAAOgE,KAAK,EAAIqC,CAClD,CAAA,GAAK,KAGR,IAAIY,EAAoB,EACpBC,EAAoD,MAExD,GAAIJ,EAAe,CACXK,MAAAA,EAAiBL,EAAc5B,QAAQtZ,UAAYka,EAAEH,WAAaG,EAAEF,SAAS,EAC7EO,EAAgBW,EAAcJ,cAAcP,cAE9CA,GAAiBgB,EAAenc,QAAU8a,EAAE9F,SAAWmG,CAAa,IACtEc,EAAoBH,EAAcJ,cAAcN,cACjCa,EAAAA,GAAqB,GAAK,OAAS,kBAE3CF,EAAa,CAChBK,MAAAA,EAAcL,EAAY7B,QAAQhjB,QAAU4jB,EAAEH,WAAaG,EAAEF,SAAS,EACxEwB,IACFH,EAAoBtiB,EAAcyiB,EAAYpH,OAAOgE,KAAK,EAAIqC,EAC/CY,EAAAA,GAAqB,GAAK,SAAW,OAKxD,IAAI5P,EAAwB,GAC5B,OAAIyP,EACFzP,EAAwByP,EAAcJ,cAAclP,eAC3CuP,GAAeE,GAAqB,GACrB5P,EAAA,eAAe0P,EAAYxE,kBAAkB0E,YAE7C5P,EAAA,kDAGnB,CACLoN,YAAAA,EACAqC,cAAAA,EACAC,YAAAA,EACAE,kBAAAA,EACA5P,sBAAAA,EACA6P,aAAAA,CAAAA,CACF,EACC,CAACN,CAAe,CAAC,EAEb,MAAA,CACLA,gBAAAA,EACAC,aAAAA,EACArZ,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECrWMwQ,GAAuBzQ,GAAiD,CAC5E,GAAIA,EAAO7L,OAAS,EAAU,MAAA,SAI9B,MAAM+V,EADelK,EAAOoD,MAAM,GAAG,EAElCpX,IAAKyV,GAAMA,EAAEL,MAAMU,UAAU,EAC7B9M,OAAQmV,GAAsCA,GAAM,IAAI,EAE3D,GAAID,EAAW/V,OAAS,EAAU,MAAA,SAGlC,MAAMuc,EAAOxG,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAM3T,KAAKyiB,IAAIxG,CAAC,EAAG,CAAC,EAAID,EAAW/V,OACxEyc,EACJ1G,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAM3T,KAAK2iB,IAAI3iB,KAAKyiB,IAAIxG,CAAC,EAAIuG,EAAM,CAAC,EAAG,CAAC,EAAIxG,EAAW/V,OACjF2c,EAAS5iB,KAAK6iB,KAAKH,CAAQ,EAGjC,OAAIE,EAAS,IAAY,OACrBA,EAAS,GAAY,SAClB,KACT,EAKME,GAA0BhR,GAAkD,CAChF,GAAIA,EAAO7L,OAAS,EAAU,MAAA,QAExB8c,MAAAA,EAAejR,EAAOoD,MAAM,EAAE,EACpC,IAAI8N,EAAY,EACZC,EAAgB,EAmBpB,OAjBAF,EAAa5W,QAAmB+G,GAAA,CAC9B,MAAMxJ,GAASwJ,EAAMA,MAAMxJ,OAAS,IAAIsR,cAClC5L,GAAS8D,EAAMA,MAAM9D,OAAS,IAAI4L,cAClCkI,EAAW,GAAGxZ,KAAS0F,KAEzB8T,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,WAAW,IACxF4hB,KAGAE,EAAS9hB,SAAS,UAAU,GAC5B8hB,EAAS9hB,SAAS,WAAW,GAC7B8hB,EAAS9hB,SAAS,OAAO,IAEzB6hB,GACF,CACD,EAEGD,EAAYC,EAAsB,OAClCA,EAAgBD,EAAkB,WAC/B,OACT,EAKMG,GAAqBrR,GAAmE,CAC5F,GAAIA,EAAO7L,OAAS,EAAU,MAAA,UAG9B,MAAMqN,EADexB,EAAOoD,MAAM,GAAG,EACFpO,UAAcyM,EAAEL,MAAMM,WAAa,KAAK,EAE3E,GAAIF,EAAcrN,OAAS,EAAU,MAAA,UAE/Bmd,MAAAA,EAAW9P,EAAcxM,OAAQyM,GAAMA,EAAEL,MAAMmQ,YAAc,MAAM,EAAEpd,OACrEqd,EAAYhQ,EAAcxM,OAAQyM,GAAMA,EAAEL,MAAMmQ,YAAc,OAAO,EAAEpd,OAE7E,OAAImd,EAAWE,EAAY,IAAY,UACnCA,EAAYF,EAAW,IAAY,UAChC,SACT,EAKMG,GAA6BzR,GAAkD,CACnF,GAAIA,EAAO7L,OAAS,EAAU,OAAA,KAGxBud,MAAAA,MAAgBxmB,KACtBwmB,EAAUhjB,QAAQgjB,EAAUhlB,QAAQ,EAAI,CAAC,EAEnCukB,MAAAA,EAAejR,EAAOhL,OAAkBoM,GAC1B,IAAIlW,KAAKkW,EAAMA,MAAMnW,IAAI,GACvBymB,CACrB,EAED,GAAIT,EAAa9c,OAAS,EAAU,OAAA,KAEpC,MAAMwd,EAAaV,EAAajc,OAC7ByM,GAAMA,EAAEL,MAAMc,aAAe,WAAaT,EAAEL,MAAMM,WAAa,KAClE,EAAEvN,OAEIyd,EAAYX,EAAajc,OAC5ByM,GAAMA,EAAEL,MAAMc,aAAe,UAAYT,EAAEL,MAAMM,WAAa,KACjE,EAAEvN,OAEF,OAAIwd,EAAaC,EAAkB,UAC/BA,EAAYD,EAAmB,SAC5B,IACT,EAKaE,GAA0BA,IAAM,CAC3C,KAAM,CAAC7R,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,6CAA8CD,CAAG,EAC/DyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EAGC4R,MAAAA,EAAuDhf,EAAAA,QAAQ,IAAM,CACzE,MAAM4b,EAAe1O,EAAOhL,UAAcyM,EAAEL,MAAMc,aAAe,SAAS,EACpEyM,EAAc3O,EAAOhL,UAAcyM,EAAEL,MAAMc,aAAe,QAAQ,EAElE6P,EAAiBA,CACrBC,EACA7F,IAC0B,CAC1B,MAAM1J,EAAcuP,EAAY7d,OAC1BqN,EAAgBwQ,EAAYhd,UAAcyM,EAAEL,MAAMM,WAAa,KAAK,EACpE/B,EAAU8C,EAAc,EAAKjB,EAAcrN,OAASsO,EAAe,IAAM,EAEzEyH,EAAa8H,EAChBhmB,IAAKyV,GAAMA,EAAEL,MAAMU,UAAU,EAC7B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EACJuI,EAAW/V,OAAS,EAAI+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAGpF8c,EAAee,EAAY5O,MAAM,GAAG,EACpC6O,EAAahB,EAAajc,OAAQyM,GAAMA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACpE+d,EACJjB,EAAa9c,OAAS,EAAK8d,EAAahB,EAAa9c,OAAU,IAAM,EAGjEge,EAAwB,CAC5B/X,IAAK,CAAEuF,QAAS,EAAGyS,KAAM,EAAGpS,OAAQ,CAAE,EACtC7F,OAAQ,CAAEwF,QAAS,EAAGyS,KAAM,EAAGpS,OAAQ,CAAE,EACzC9F,KAAM,CAAEyF,QAAS,EAAGyS,KAAM,EAAGpS,OAAQ,CAAE,CAAA,EAKnCqS,EAAeL,EAAYhd,OAAQyM,IAAOA,EAAEL,MAAMU,YAAc,IAAM,CAAC,EACvEwQ,EAAgBN,EAAYhd,OAAQyM,IAAOA,EAAEL,MAAMU,YAAc,IAAM,CAAC,EACxEyQ,EAAeP,EAAYhd,OAAcyM,GAAA,CACvC0I,MAAAA,EAAI1I,EAAEL,MAAMU,YAAc,EACzBqI,OAAAA,EAAI,GAAKA,EAAI,CAAA,CACrB,EAGC,OAAA,CAAEnK,OAAQqS,EAAc1e,IAAK,KAAA,EAC7B,CAAEqM,OAAQuS,EAAc5e,IAAK,QAAA,EAC7B,CAAEqM,OAAQsS,EAAe3e,IAAK,MAAA,CAAiB,EAC/C0G,QAAQ,CAAC,CAAE2F,OAAQwS,EAAW7e,IAAAA,CAAAA,IAAU,CAClCkP,MAAAA,EAAO2P,EAAUxd,OAAQyM,GAAMA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAC3Dse,EAAKD,EACRxmB,IAAKyV,GAAMA,EAAEL,MAAMU,UAAU,EAC7B9M,OAAQmV,GAAmBA,IAAM5X,MAAS,EAE7C4f,EAAsBxe,CAAG,EAAI,CAC3BgM,QAAS6S,EAAUre,OAAS,EAAK0O,EAAO2P,EAAUre,OAAU,IAAM,EAClEie,KAAMK,EAAGte,OAAS,EAAIse,EAAG7Q,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAIsI,EAAGte,OAAS,EACtE6L,OAAQwS,EAAUre,MAAAA,CACpB,CACD,EAEM,CACLgY,MAAAA,EACA1J,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACAuQ,kBAAAA,EACAC,sBAAAA,CAAAA,CACF,EAGK,MAAA,CACL,UAAWJ,EAAerD,EAAc,SAAS,EACjD,SAAUqD,EAAepD,EAAa,QAAQ,CAAA,CAChD,EACC,CAAC3O,CAAM,CAAC,EA0HJ,MAAA,CACLY,eAxH0C9N,EAAAA,QAAQ,IAAM,CACpDkN,GAAAA,EAAO7L,OAAS,EACX,MAAA,CACLue,iBAAkB,UAClBC,YAAa,GACbC,WAAY,MACZ9F,UAAW,kEACX+F,iBAAkB,SAClBC,qBAAsB,8BACtBC,iBAAkB,CAChBC,WAAY,SACZC,iBAAkB,QAClBC,SAAU,UACVC,uBAAwB,IAC1B,CAAA,EAKEH,MAAAA,EAAavC,GAAoBzQ,CAAM,EACvCiT,EAAmBjC,GAAuBhR,CAAM,EAChDkT,EAAW7B,GAAkBrR,CAAM,EACnCmT,EAAyB1B,GAA0BzR,CAAM,EAEzD+S,EAAqC,CACzCC,WAAAA,EACAC,iBAAAA,EACAC,SAAAA,EACAC,uBAAAA,CAAAA,EAIF,IAAIC,EAAQ,EACZ,MAAMC,EAAoB,CAAA,EAGtBL,IAAe,QACRI,GAAA,EACTC,EAAQ9Y,KAAK,6DAA6D,GACjEyY,IAAe,QACfI,GAAA,EACTC,EAAQ9Y,KAAK,iDAAiD,GAI5D0Y,IAAqB,QACdG,GAAA,IACTC,EAAQ9Y,KAAK,sDAAsD,GAC1D0Y,IAAqB,aACrBG,GAAA,IACTC,EAAQ9Y,KAAK,gEAAgE,GAI3E2Y,IAAa,YACNE,GAAA,EACTC,EAAQ9Y,KAAK,oEAAoE,GAI/E4Y,IAA2B,WACpBC,GAAA,GACTC,EAAQ9Y,KAAK,yCAAyC,GAC7C4Y,IAA2B,WAC3BC,GAAA,GACTC,EAAQ9Y,KAAK,wCAAwC,GAIjD+Y,MAAAA,EAAcxB,EAAW,SAAS,EAClCyB,EAAazB,EAAW,QAAQ,EAElCwB,EAAY3T,QAAU4T,EAAW5T,QAAU,IACpCyT,GAAA,EACD7Y,EAAAA,KACN,oCAAoC+Y,EAAY3T,QAAQiM,QACtD,CACF,SAAS2H,EAAW5T,QAAQiM,QAAQ,CAAC,KACvC,GACS2H,EAAW5R,aAAe2R,EAAY3R,aAAe,KACrDyR,GAAA,EACD7Y,EAAAA,KACN,qCAAqCgZ,EAAW5R,aAAaiK,QAC3D,CACF,QAAQ0H,EAAY3R,aAAaiK,QAAQ,CAAC,IAC5C,GAII8G,MAAAA,EAA8BU,EAAQ,EAAI,SAAW,UACrDP,EAA8BO,EAAQ,EAAI,UAAY,SAGtDI,EAAWtlB,KAAKyiB,IAAIyC,CAAK,EACzBT,EAAczkB,KAAKulB,IAAI,GAAKD,EAAW,EAAG,EAAE,EAE9CZ,IAAAA,EACAY,GAAY,EAAgBZ,EAAA,OACvBY,GAAY,IAAkBZ,EAAA,SACrBA,EAAA,MAEZ9F,MAAAA,EAAYuG,EAAQK,KAAK,IAAI,EAO5B,MAAA,CACLhB,iBAAAA,EACAC,YAAAA,EACAC,WAAAA,EACA9F,UAAAA,EACA+F,iBAAAA,EACAC,qBAVAJ,IAAqB,SACjB,4CACA,4CASJK,iBAAAA,CAAAA,CACF,EACC,CAAC/S,EAAQ8R,CAAU,CAAC,EAIrBA,WAAAA,EACAlb,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECtVM0T,GAA0BvS,GAA+C,WAC7E,MAAMwS,EAA6B,CAAA,EAC7BC,IAAYzS,EAAAA,EAAMA,MAAMc,aAAZd,YAAAA,EAAwB8H,gBAAiB,GACrDtR,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CkI,EAAW,GAAGyC,KAAajc,KAAS0F,IAGtC8T,OAAAA,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,gBAAgB,GAAK8hB,EAAS9hB,SAAS,WAAW,IAClGskB,EAASrZ,KAAK,KAAK,GAIjB6W,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,kBAAkB,GAAK8hB,EAAS9hB,SAAS,YAAY,IACtGskB,EAASrZ,KAAK,MAAM,GAIlB6W,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,iBAAiB,GAAK8hB,EAAS9hB,SAAS,WAAW,IACpGskB,EAASrZ,KAAK,MAAM,GAIlB6W,EAAS9hB,SAAS,WAAW,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,MAAM,IACvHskB,EAASrZ,KAAK,WAAW,EAGpBqZ,CACT,EAKME,GAA8BF,GAAuC,CACzE,MAAMG,EAAeH,EAASzf,OAE9B,OAAI4f,IAAiB,EAAU,EAC3BA,IAAiB,EAAU,GAC3BA,IAAiB,EAAU,IAC3BA,IAAiB,EAAU,IACxB,CACT,EAKMC,GAA4B5S,GAAqC,SACrE,MAAMxJ,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CkI,EAAW,GAAGxZ,KAAS0F,IAE7B,GAAI,CAAC8T,EAAS9hB,SAAS,KAAK,GAAK,CAAC8hB,EAAS9hB,SAAS,gBAAgB,GAAK,CAAC8hB,EAAS9hB,SAAS,WAAW,EAC9F,MAAA,GAGT,IAAI8jB,EAAQ,GAGRhC,OAAAA,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,SAAS,KAChF8jB,GAAA,KAIPhC,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,kBAAkB,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,IAAI,KAClH8jB,GAAA,KAIPhC,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,UAAU,KAC/E8jB,GAAA,KAIPhC,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,QAAQ,GAAK8hB,EAAS9hB,SAAS,UAAU,KAChF8jB,GAAA,IAGJllB,KAAKulB,IAAIvlB,KAAK8f,IAAIoF,EAAO,CAAC,EAAG,GAAG,CACzC,EAKMa,GAAsB7S,GAAqC,WAC/D,MAAMxJ,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CgL,IAAS9S,EAAAA,EAAMA,MAAM+S,UAAZ/S,YAAAA,EAAqB8H,gBAAiB,GAC/CkI,EAAW,GAAGxZ,KAAS0F,KAAS4W,IAEtC,IAAId,EAAQ,EAGRhC,OAAAA,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,UAAU,GAAK8hB,EAAS9hB,SAAS,cAAc,KACrF8jB,GAAA,KAIPhC,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,QAAQ,GAAK8hB,EAAS9hB,SAAS,YAAY,GAAK8hB,EAAS9hB,SAAS,OAAO,KACjH8jB,GAAA,KAIPhC,EAAS9hB,SAAS,QAAQ,GAAK8hB,EAAS9hB,SAAS,UAAU,GAAK8hB,EAAS9hB,SAAS,OAAO,KAClF8jB,GAAA,KAIPhC,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,SAAS,KAChF8jB,GAAA,IAGJllB,KAAKulB,IAAIL,EAAO,CAAG,CAC5B,EAKMgB,GAA6BhT,GAAqC,SACtE,MAAMxJ,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CkI,EAAW,GAAGxZ,KAAS0F,IAE7B,IAAI8V,EAAQ,EAGZ,OAAIhC,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,QAAQ,GAAK8hB,EAAS9hB,SAAS,WAAW,GAAK8hB,EAAS9hB,SAAS,SAAS,KAClH8jB,GAAA,KAIPhC,EAAS9hB,SAAS,UAAU,GAAK8hB,EAAS9hB,SAAS,gBAAgB,GAAK8hB,EAAS9hB,SAAS,cAAc,KACjG8jB,GAAA,KAIPhC,EAAS9hB,SAAS,YAAY,GAAK8hB,EAAS9hB,SAAS,WAAW,GAAK8hB,EAAS9hB,SAAS,aAAa,KAC7F8jB,GAAA,IAGJllB,KAAKulB,IAAIL,EAAO,CAAG,CAC5B,EAKMiB,GAA+BjT,GAAqC,SACxE,MAAMxJ,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CkI,EAAW,GAAGxZ,KAAS0F,IAE7B,OAAI8T,EAAS9hB,SAAS,QAAQ,GAAK8hB,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,OAAO,EAC7G,GAGF,CACT,EAKMglB,GAA2BlT,GAAkD,CAC3EwS,MAAAA,EAAWD,GAAuBvS,CAAK,EAEvCmT,EAAY,CAChBC,kBAAmBV,GAA2BF,CAAQ,EACtDa,mBAAoBT,GAAyB5S,CAAK,EAClDsT,WAAYT,GAAmB7S,CAAK,EACpCuT,oBAAqBP,GAA0BhT,CAAK,EACpDwT,mBAAoBP,GAA4BjT,CAAK,CAAA,EAGjDyT,EAAazS,OAAO0S,OAAOP,CAAS,EAAE3S,OAAO,CAACC,EAAKuR,IAAUvR,EAAMuR,EAAO,CAAC,EAE3E2B,EAAkB7mB,KAAKulB,IAAKoB,EADjB,EAC0C,EAAG,CAAC,EAG3DG,IAAAA,EACAD,GAAmB,IAAcC,EAAA,cAC5BD,GAAmB,IAAcC,EAAA,YACjCD,GAAmB,IAAcC,EAAA,OACjCD,GAAmB,IAAcC,EAAA,OAC5BA,EAAA,OAGVpU,IAAAA,EACAmU,GAAmB,EACJnU,EAAA,gDACRmU,GAAmB,EACXnU,EAAA,+CACRmU,GAAmB,EACXnU,EAAA,uCAEAA,EAAA,2CAInB,MAAMqU,EAAyB/mB,KAAKulB,IAAI,GAAMsB,EAAkB,GAAK,EAAE,EAEhE,MAAA,CACLF,WAAYE,EACZG,SAAU,EACVX,UAAAA,EACAS,OAAAA,EACApU,eAAAA,EACAqU,uBAAAA,CAAAA,CAEJ,EAKaE,GAA2BA,IAAM,CAC5C,KAAM,CAACnV,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,qDAAsDD,CAAG,EACvEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EA8FE,CACLkV,SA5FoCtiB,EAAAA,QAAQ,IAAM,CAC9CkN,GAAAA,EAAO7L,SAAW,EACb,MAAA,CACLkhB,aAAc,CACZR,WAAY,EACZK,SAAU,EACVX,UAAW,CACTC,kBAAmB,EACnBC,mBAAoB,EACpBC,WAAY,EACZC,oBAAqB,EACrBC,mBAAoB,CACtB,EACAI,OAAQ,OACRpU,eAAgB,iCAChBqU,uBAAwB,EAC1B,EACAK,mBAAoB,EACpBC,kBAAmB,CAAA,CAAA,EAKjBC,MAAAA,EAAexV,EAAOhU,IAAcoV,IAAA,CACxCA,MAAAA,EACAgS,MAAOkB,GAAwBlT,CAAK,EACpCqU,UAAWrU,EAAMA,MAAMM,WAAa,KACpC,EAAA,EAGIgU,EAAeF,EAAaxgB,UAAa2gB,EAAGvC,MAAMyB,WAAa,CAAC,EACtE,IAAIe,EAAqB,EAEzBF,EAAarb,QAAcsb,GAAA,CACJA,EAAGvC,MAAM6B,uBAAyB,KAClCU,EAAGF,WACtBG,GACF,CACD,EAED,MAAMN,EAAqBI,EAAavhB,OAAS,EAC9CyhB,EAAqBF,EAAavhB,OAAU,IAAM,EAG/CohB,EAAoB,CAAC,EAAG,EAAG,EAAG,EAAG,CAAC,EAAEvpB,IAAaonB,GAAA,CACrD,MAAMyC,EAAgBL,EAAaxgB,OAAO2gB,GACxCA,EAAGvC,MAAMyB,YAAczB,EAAQ,IAAOuC,EAAGvC,MAAMyB,WAAazB,EAAQ,EACtE,EAEMvQ,EAAOgT,EAAc7gB,OAAa2gB,GAAAA,EAAGF,SAAS,EAAEthB,OAChDwL,EAAUkW,EAAc1hB,OAAS,EAAK0O,EAAOgT,EAAc1hB,OAAU,IAAM,EAE3E+V,EAAa2L,EAChB7pB,IAAI2pB,GAAMA,EAAGvU,MAAMA,MAAMU,UAAU,EACnC9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAE3D,MAAA,CACLif,MAAAA,EACA0C,MAAOD,EAAc1hB,OACrBwL,QAAAA,EACAgC,aAAAA,CAAAA,CACF,CACD,EAoBM,MAAA,CACL0T,aAlBmBrV,EAAO7L,OAAS,EACnCmgB,GAAwBtU,EAAOA,EAAO7L,OAAS,CAAC,CAAC,EACjD,CACE0gB,WAAY,EACZK,SAAU,EACVX,UAAW,CACTC,kBAAmB,EACnBC,mBAAoB,EACpBC,WAAY,EACZC,oBAAqB,EACrBC,mBAAoB,CACtB,EACAI,OAAQ,OACRpU,eAAgB,8BAChBqU,uBAAwB,EAAA,EAK1BK,mBAAAA,EACAC,kBAAAA,CAAAA,CACF,EACC,CAACvV,CAAM,CAAC,EAITpJ,UAAAA,EACAjB,MAAAA,EACA2e,wBAAAA,GACA/Q,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECzUM8V,GAA0BA,IAAqB,CAC7CtoB,MAAAA,MAAUvC,KACVmC,EAAOI,EAAI+R,WACXwW,EAAY,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EAAEvoB,EAAIwoB,OAAQ,CAAA,EAGzGC,IAAAA,EACA7oB,EAAO,GAAMA,IAAS,GAAKI,EAAIkgB,aAAe,GAClCuI,EAAA,aACL7oB,GAAQ,GACH6oB,EAAA,cAEAA,EAAA,UAIVC,MAAAA,EAAYH,IAAc,UAAYA,IAAc,YACpDI,EAA0CD,EAAY,SAAW,MAGjEE,EACJH,IAAgB,WAAc7oB,GAAQ,GAAKA,GAAQ,GAAM,OAAS,SAE7D,MAAA,CACL8oB,UAAAA,EACAC,WAAAA,EACAC,cAAAA,EACAH,YAAAA,EACAF,UAAAA,CAAAA,CAEJ,EAKMM,GAAwBA,CAC5B5V,EACAF,IACW,CACX,MAAMkC,EAAiBlC,EAAmBlV,KAAUgY,GAAAA,EAAEjW,OAASqT,CAAW,EACtE,MAAA,CAACgC,GAAkBA,EAAeD,YAAc,EAAU,EAG1DC,EAAe/C,SAAW,GAAW,GACrC+C,EAAe/C,SAAW,GAAW,GACrC+C,EAAe/C,SAAW,GAAW,EACrC+C,EAAe/C,QAAU,GAAW,IACjC,CACT,EAKM4W,GAAyBC,GACzBA,GAAgB,IAAY,GAC5BA,GAAgB,EAAY,GAC5BA,GAAgB,IAAY,GAC5BA,GAAgB,EAAY,EAC5BA,EAAe,EAAY,IACxB,EAMHC,GAAsBA,CAACC,EAA8BvK,IACrDuK,EAAcN,aAAe,MAAc,EAG3CM,EAAcN,aAAe,OACxBjK,IAAU,UAAY,EAAI,IAI5BA,IAAU,UAAY,EAAI,GAM7BwK,GAAwBD,GAAyC,CACrE,OAAQA,EAAcL,cAAa,CACjC,IAAK,OAAe,MAAA,GACpB,IAAK,MAAc,MAAA,GACnB,QAAgB,MAAA,EAClB,CACF,EAKMO,GAA2BA,CAC/BC,EACAC,EACAC,IACW,CACX,IAAIC,EAAoB,EAOxB,OALIH,IAAoB,QAAQG,KAC5BF,IAAkB,aAAeA,IAAkB,gBAAeE,IAClED,GAAgB,IAAIC,IAGpBA,GAAqB,EAAU,GAC/BA,GAAqB,EAAU,EAC5B,CACT,EAKMC,GAAyBA,CAC7BtE,EACAC,EACA4D,IACyC,CACrC7e,IAAAA,EACAuf,EACAC,EACAC,EAEJ,OAAIzE,GAAe,IAAMC,IAAe,QAAU4D,GAAgB,GAC/C7e,EAAA,aACAuf,EAAA,IACIC,EAAA,EACDC,EAAA,CAAC,IAAK,IAAK,CAAG,GACzBzE,GAAe,IAAMC,IAAe,OAC5Bjb,EAAA,WACAuf,EAAA,EACIC,EAAA,EACDC,EAAA,CAAC,IAAK,EAAK,CAAG,IAEjBzf,EAAA,eACAuf,EAAA,EACIC,EAAA,GACDC,EAAA,CAAC,EAAK,IAAK,CAAG,GAG7B,CACLzf,eAAAA,EACAuf,eAAAA,EACAC,mBAAAA,EACAC,kBAAAA,CAAAA,CAEJ,EAKaC,GAAkCA,CAC7CC,EACAC,IACG,CACH,KAAM,CAACvX,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,qDAAsDD,CAAG,EACvEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EAuHE,CACLsX,mBArH6C1kB,EAAAA,QAAQ,IAAM,CACvDkN,GAAAA,EAAO7L,OAAS,EACX,MAAA,CACLsjB,iBAAkB,GAClB7E,WAAY,MACZhS,eAAgB,WAChBoM,kBAAmB,CAAEyG,IAAK,EAAKzF,IAAK,EAAK0J,QAAS,GAAI,EACtDnD,UAAW,CACToD,iBAAkB,GAClBZ,aAAc,EACda,aAAc,EACdxB,WAAY,EACZyB,YAAa,EACbC,gBAAiB,CACnB,EACAvgB,eAAgB,CACdI,eAAgB,eAChBuf,eAAgB,EAChBC,mBAAoB,EACpBC,kBAAmB,CAAC,EAAK,IAAK,CAAG,CACnC,CAAA,EAIJ,MAAMV,EAAgBX,KAChBrV,EAAc,IAAIxV,KAAK,EAAEsU,SAAS,EAGlCwS,EAAchS,EAAOhL,OAAOyM,GAAKA,EAAEL,MAAMc,aAAeoV,EAAoB5E,gBAAgB,EAC5FiF,EAAmB3F,EAAY7d,OAAS,EAC3C6d,EAAYhd,OAAYyM,GAAAA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAAS6d,EAAY7d,OAAU,IACpFmjB,EAAoB3E,YAGhBnS,EAAqBiL,MAAMC,KAAK,CAAEvX,OAAQ,EAAA,EAAM,CAAC6O,EAAG3V,IAAS,CAC3DkU,MAAAA,EAAavB,EAAOhL,OAAYyM,GAAA,CAC9ByM,MAAAA,GAAYzM,EAAEL,MAAMC,WAC1B,OAAK6M,GACa5M,SAAS4M,GAAUniB,MAAM,GAAG,EAAE,CAAC,CAAC,IAC7BsB,EAFE,EAEFA,CACtB,EAEM,MAAA,CACLA,KAAAA,EACAsS,QAAS4B,EAAWpN,OAAS,EAC1BoN,EAAWvM,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAASoN,EAAWpN,OAAU,IAAM,EAC1FsO,YAAalB,EAAWpN,MAAAA,CAC1B,CACD,EAGK4iB,EAAeT,GAAsB5V,EAAaF,CAAkB,EACpEoX,EAAerB,GAAsBgB,EAAe1C,UAAU,EAC9DuB,EAAaK,GAAoBC,EAAeY,EAAoB5E,gBAAgB,EACpFmF,EAAclB,GAAqBD,CAAa,EAChDoB,EAAkBlB,GACtBU,EAAoB1E,WACpB2E,EAAevC,OACf+B,CACF,EAGMU,EAAmBvpB,KAAKulB,IAAIvlB,KAAK8f,IACrC2J,EAAmBZ,EAAea,EAAexB,EAAayB,EAAcC,EAC5E,EACF,EAAG,EAAE,EAGDlF,IAAAA,EACJ,MAAMmF,EAAa7pB,KAAKyiB,IAAIoG,EAAea,EAAeE,CAAe,EACrEC,GAAc,IAAMT,EAAoB1E,aAAe,OAAqBA,EAAA,OACvEmF,GAAc,GAAiBnF,EAAA,SACtBA,EAAA,MAGdhS,IAAAA,EACA6W,GAAoB,IAAM7E,IAAe,OAAyBhS,EAAA,aAC7D6W,GAAoB,GAAqB7W,EAAA,gBACzC6W,GAAoB,GAAqB7W,EAAA,WACzC6W,GAAoB,GAAqB7W,EAAA,cAC5BA,EAAA,QAGtB,MAAMoX,EAAkBhG,EACrBhmB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EAErDiI,EAAO4F,EAAgB7jB,OAAS,EACpC6jB,EAAgBpW,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAI6N,EAAgB7jB,OAAS,IAEtE6Y,EAAoB,CACxByG,IAAKvlB,KAAK8f,IAAIoE,EAAO,GAAK,EAAG,EAC7BpE,IAAKoE,EAAO,IACZsF,QAAStF,CAAAA,EAIL7a,EAAiB0f,GAAuBQ,EAAkB7E,EAAY2E,EAAe1C,UAAU,EAE9F,MAAA,CACL4C,iBAAAA,EACA7E,WAAAA,EACAhS,eAAAA,EACAoM,kBAAAA,EACAuH,UAAW,CACToD,iBAAAA,EACAZ,aAAAA,EACAa,aAAAA,EACAxB,WAAAA,EACAyB,YAAAA,EACAC,gBAAAA,CACF,EACAvgB,eAAAA,CAAAA,CAED,EAAA,CAACyI,EAAQsX,EAAqBC,CAAc,CAAC,EAI9C3gB,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECjUMgY,GAAwBzoB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAIhC,EAAA,CAAA,8CAAA,CAAA,EAEKuoB,GAA2B1oB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,aAAA,CAOnC,EAAA,CAAA,wLAAA,CAAA,EAEKwoB,GAAuBxS,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,4PAAA,GAAA,EAgBhB,CAAC,CAAEyoB,YAAAA,CAAY,IAAOA,EAAc,gCAAkC,MAAO,EAG1FC,GAAsB7oB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAO9B,EAAA,CAAA,mGAAA,CAAA,EAEK2oB,GAAwB9oB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAIhC,EAAA,CAAA,4DAAA,CAAA,EAEK4oB,GAAoBzoB,EAAAA,KAAIL,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2DAAA,GAAA,EAGf,CAAC,CAAEyoB,YAAAA,CAAY,IAAOA,EAAc,iBAAmB,cAAe,EAG/EI,GAAwBhpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,4DAAA,GAAA,EACjB,CAAC,CAAEyoB,YAAAA,CAAY,IAAOA,EAAc,SAAW,IAGlD,CAAC,CAAEA,YAAAA,CAAY,IAAOA,EAAc,OAAS,QAAS,EAG7DK,GAAoBjpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAK5B,EAAA,CAAA,oGAAA,CAAA,EAEK+oB,EAAoBlpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAY5B,EAAA,CAAA,oQAAA,CAAA,EAEKgpB,EAAqBnpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAO7B,EAAA,CAAA,6HAAA,CAAA,EAEKipB,EAAqBppB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAK7B,EAAA,CAAA,6EAAA,CAAA,EAEKkpB,EAA2BrpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAInC,EAAA,CAAA,yDAAA,CAAA,EAKYmpB,GAA8DA,CAAC,CAC1ExB,oBAAAA,EACAC,eAAAA,EACAC,mBAAAA,EACA5gB,UAAAA,EAAY,GACZjB,MAAAA,EAAQ,IACV,IAAM,CACE,KAAA,CAACojB,EAAkBC,CAAmB,EAAItoB,EAAAA,aAA0BuoB,IAAI,CAAC,OAAO,CAAC,CAAC,EAElFC,EAAiBC,GAAsB,CACrCC,MAAAA,EAAc,IAAIH,IAAIF,CAAgB,EACxCK,EAAYxP,IAAIuP,CAAS,EAC3BC,EAAYC,OAAOF,CAAS,EAE5BC,EAAYE,IAAIH,CAAS,EAE3BH,EAAoBI,CAAW,CAAA,EAGjC,GAAIxiB,EACF,OACG5F,EAAAA,IAAAinB,GAAA,CACC,SAACjnB,EAAAA,IAAA,MAAA,CAAI,MAAO,CAAEuoB,UAAW,SAAUC,QAAS,OAAQC,MAAO,uBAAwB,EAAE,wCAErF,CACF,CAAA,EAIJ,GAAI9jB,EACF,OACG3E,EAAAA,IAAAinB,GAAA,CACC,SAAClnB,EAAAA,KAAA,MAAA,CAAI,MAAO,CAAEwoB,UAAW,SAAUC,QAAS,OAAQC,MAAO,mBAAsB,EAAA,SAAA,CAAA,oCAC7C9jB,CAAAA,CACpC,CAAA,CACF,CAAA,EAIJ,MAAM+jB,EAAcP,GAAsBJ,EAAiBnP,IAAIuP,CAAS,EAExE,cACGlB,GAEC,CAAA,SAAA,CAAAlnB,OAACmnB,GACC,CAAA,SAAA,CAACnnB,EAAAA,KAAAonB,GAAA,CAAc,YAAauB,EAAW,OAAO,EAAG,QAAS,IAAMR,EAAc,OAAO,EACnF,SAAA,CAAAnoB,OAAC,MACC,CAAA,SAAA,CAAAC,EAAAA,IAACqnB,IAAa,SAA2B,6BAAA,CAAA,SACxCC,GACEhB,CAAAA,SAAAA,CAAoB5E,EAAAA,iBAAiB,MAAI4E,EAAoB3E,YAAY/G,QAAQ,CAAC,EAAE,cAAA,EAEvF,CAAA,EACF,QACC2M,GAAW,CAAA,YAAamB,EAAW,OAAO,EAAG,SAAC,IAAA,CAAA,EACjD,EAEC3oB,EAAA,KAAAynB,GAAA,CAAe,YAAakB,EAAW,OAAO,EAC7C,SAAA,CAAA3oB,OAAC0nB,GACC,CAAA,SAAA,CAAA1nB,OAAC2nB,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAiB,mBAAA,CAAA,EAC9B3nB,EAAAA,IAAC4nB,EAAatB,CAAAA,SAAAA,EAAoB5E,gBAAiB,CAAA,SAClDmG,EAAmBvB,CAAAA,SAAAA,CAAoB1E,EAAAA,WAAW,aAAA,EAAW,CAAA,EAChE,SAEC8F,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAiB,mBAAA,CAAA,EAC9B3nB,EAAAA,IAAC4nB,EAAatB,CAAAA,SAAAA,EAAoBzE,gBAAiB,CAAA,EACnD7hB,EAAAA,IAAC6nB,EAAmBvB,CAAAA,SAAAA,EAAoBxE,oBAAqB,CAAA,CAAA,EAC/D,SAEC4F,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAiB,mBAAA,CAAA,QAC7BC,EACEtB,CAAAA,SAAAA,EAAoBvE,iBAAiBC,WAAW2G,cACnD,EACA3oB,EAAAA,IAAC6nB,GAAkB,SAAoC,sCAAA,CAAA,CAAA,EACzD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAS,WAAA,CAAA,QACrBC,EACEtB,CAAAA,SAAAA,EAAoBvE,iBAAiBG,SAASyG,cACjD,EACA3oB,EAAAA,IAAC6nB,GAAkB,SAAgC,kCAAA,CAAA,CAAA,EACrD,CAAA,EACF,EAEA9nB,OAAC8nB,GACC,MAAO,CAAEW,QAAS,OAAQ/T,WAAY,oBAAqBxB,aAAc,KAEzE,EAAA,SAAA,CAAAjT,EAAAA,IAAC,UAAO,SAAU,YAAA,CAAA,EAAS,IAAEsmB,EAAoBxK,SAAAA,EACnD,CAAA,EACF,CAAA,EACF,SAGCoL,GACC,CAAA,SAAA,CAACnnB,EAAAA,KAAAonB,GAAA,CAAc,YAAauB,EAAW,SAAS,EAAG,QAAS,IAAMR,EAAc,SAAS,EACvF,SAAA,CAAAnoB,OAAC,MACC,CAAA,SAAA,CAAAC,EAAAA,IAACqnB,IAAa,SAA2B,6BAAA,CAAA,SACxCC,GACEf,CAAAA,SAAAA,CAAe1C,EAAAA,WAAWjJ,QAAQ,CAAC,EAAE,UAAQ2L,EAAevC,MAAAA,EAC/D,CAAA,EACF,QACCuD,GAAW,CAAA,YAAamB,EAAW,SAAS,EAAG,SAAC,IAAA,CAAA,EACnD,EAEC3oB,EAAA,KAAAynB,GAAA,CAAe,YAAakB,EAAW,SAAS,EAC/C,SAAA,CAAA3oB,OAAC0nB,GACC,CAAA,SAAA,CAAA1nB,OAAC2nB,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAmB,qBAAA,CAAA,QAC/BC,EAAarB,CAAAA,SAAAA,EAAehD,UAAUC,kBAAkB5I,QAAQ,CAAC,EAAE,EACpE5a,EAAAA,IAAC6nB,GAAkB,SAA2B,6BAAA,CAAA,CAAA,EAChD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAmB,qBAAA,CAAA,QAC/BC,EAAarB,CAAAA,SAAAA,EAAehD,UAAUE,mBAAmB7I,QAAQ,CAAC,EAAE,EACrE5a,EAAAA,IAAC6nB,GAAkB,SAAsB,wBAAA,CAAA,CAAA,EAC3C,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAW,aAAA,CAAA,QACvBC,EAAarB,CAAAA,SAAAA,EAAehD,UAAUG,WAAW9I,QAAQ,CAAC,EAAE,EAC7D5a,EAAAA,IAAC6nB,GAAkB,SAA2B,6BAAA,CAAA,CAAA,EAChD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAoB,sBAAA,CAAA,QAChCC,EAAarB,CAAAA,SAAAA,EAAehD,UAAUI,oBAAoB/I,QAAQ,CAAC,EAAE,EACtE5a,EAAAA,IAAC6nB,GAAkB,SAA4B,8BAAA,CAAA,CAAA,EACjD,CAAA,EACF,EAEA9nB,OAAC8nB,GACC,MAAO,CAAEW,QAAS,OAAQ/T,WAAY,oBAAqBxB,aAAc,KAEzE,EAAA,SAAA,CAAAjT,EAAAA,IAAC,UAAO,SAAe,iBAAA,CAAA,EAAS,IAAEumB,EAAe3W,cAAAA,EACnD,CAAA,EACF,CAAA,EACF,SAGCsX,GACC,CAAA,SAAA,CAACnnB,EAAAA,KAAAonB,GAAA,CACC,YAAauB,EAAW,aAAa,EACrC,QAAS,IAAMR,EAAc,aAAa,EAE1C,SAAA,CAAAnoB,OAAC,MACC,CAAA,SAAA,CAAAC,EAAAA,IAACqnB,IAAa,SAA+B,iCAAA,CAAA,SAC5CC,GACEd,CAAAA,SAAAA,CAAmBC,EAAAA,iBAAiB7L,QAAQ,CAAC,EAAE,MAAI,IACnD4L,EAAmB5W,eAAegZ,QAAQ,IAAK,GAAG,CAAA,EACrD,CAAA,EACF,QACCrB,GAAW,CAAA,YAAamB,EAAW,aAAa,EAAG,SAAC,IAAA,CAAA,EACvD,QAEClB,GAAe,CAAA,YAAakB,EAAW,aAAa,EACnD,gBAACjB,GACC,CAAA,SAAA,CAAA1nB,OAAC2nB,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAmB,qBAAA,CAAA,SAC/BC,EAAapB,CAAAA,SAAAA,CAAmBjD,EAAAA,UAAUoD,iBAAiB/L,QAAQ,CAAC,EAAE,GAAA,EAAC,EACxE5a,EAAAA,IAAC6nB,GAAkB,SAA4B,8BAAA,CAAA,CAAA,EACjD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAa,eAAA,CAAA,SACzBC,EACEpB,CAAAA,SAAAA,CAAmBjD,EAAAA,UAAUwC,aAAe,EAAI,IAAM,GACtDS,EAAmBjD,UAAUwC,aAAanL,QAAQ,CAAC,EAAE,GAAA,EACxD,EACA5a,EAAAA,IAAC6nB,GAAkB,SAAiC,mCAAA,CAAA,CAAA,EACtD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAa,eAAA,CAAA,SACzBC,EACEpB,CAAAA,SAAAA,CAAmBjD,EAAAA,UAAUqD,aAAe,EAAI,IAAM,GACtDJ,EAAmBjD,UAAUqD,aAAahM,QAAQ,CAAC,EAAE,GAAA,EACxD,EACA5a,EAAAA,IAAC6nB,GAAkB,SAA2B,6BAAA,CAAA,CAAA,EAChD,SAECH,EACC,CAAA,SAAA,CAAA1nB,EAAAA,IAAC2nB,GAAY,SAAmB,qBAAA,CAAA,SAC/BC,EACEpB,CAAAA,SAAAA,CAAmBxK,EAAAA,kBAAkByG,IAAI7H,QAAQ,CAAC,EAAE,KAAG,IACvD4L,EAAmBxK,kBAAkBgB,IAAIpC,QAAQ,CAAC,CAAA,EACrD,SACCiN,EAAiB,CAAA,SAAA,CAAA,2BACSrB,EAAmBxK,kBAAkB0K,QAAQ9L,QAAQ,CAAC,EAAE,GAAA,EACnF,CAAA,EACF,CAAA,CAAA,CACF,CACF,CAAA,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,ECrTMiO,GAAwBrqB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,sKAAA,qBAAA,UAAA,GAAA,EAWjB,CAAC,CAAEmqB,QAAAA,CAAQ,IACvBA,EACI,4CACA,6CAGc,CAAC,CAAEA,QAAAA,CAAQ,IAC7BA,EACI,gDACA,iDAGG,CAAC,CAAEA,QAAAA,CAAQ,IAClBA,EACI,+BACA,8BAA8B,EAIhCC,GAAmBvqB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,qDAAA,IAAA,EAAA,EAIZ,CAAC,CAAEmqB,QAAAA,CAAQ,IACvBA,EACI,+BACA,+BAGJ,CAAC,CAAEA,QAAAA,CAAQ,IAAMA,GAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAO7B,EAGGE,GAAoBlqB,EAAAA,KAAIL,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAG7B,EAAA,CAAA,iCAAA,CAAA,EAEKsqB,GAAoBzqB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAO5B,EAAA,CAAA,sHAAA,CAAA,EAKYuqB,GAAwBA,IAAmB,CAChDzsB,MAAAA,MAAUvC,KAGVS,EAAS,IAAIT,KAAKuC,EAAIT,eAAe,QAAS,CAAClC,SAAU,kBAAmB,CAAA,CAAC,EAC7EuC,EAAO1B,EAAO6T,WACd1T,EAAUH,EAAOgiB,aACjBqI,EAAYrqB,EAAOsqB,SAEnBkE,EAAgB9sB,EAAO,GAAKvB,EAC5BsuB,EAAa,EAAI,GAAK,GACtBC,EAAc,GAAK,GAGrBrE,GAAAA,IAAc,GAAKA,IAAc,EAAG,CAChCsE,MAAAA,EAAa,IAAIpvB,KAAKS,CAAM,EAClC2uB,OAAAA,EAAW5rB,QAAQ/C,EAAOe,WAAaspB,IAAc,EAAI,EAAI,EAAE,EAC/DsE,EAAW7rB,SAAS,EAAG,GAAI,EAAG,CAAC,EAExB,CACL8rB,OAAQ,GACRC,OAAQ,SACRC,SAAUH,EAAWttB,eAAe,QAAS,CAC3CkR,QAAS,OACT7Q,KAAM,UACNC,OAAQ,UACRxC,SAAU,kBAAA,CACX,EACDA,SAAU,KACVkrB,UAAW,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EAAEA,CAAS,CAAA,EAKnGmE,GAAAA,GAAiBC,GAAcD,EAAgBE,EAC1C,MAAA,CACLE,OAAQ,GACRC,OAAQ,OACR1vB,SAAU,KACVkrB,UAAW,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EAAEA,CAAS,CAAA,EAEvG,GAAWmE,EAAgBC,EAAY,CAC/BM,MAAAA,EAAW,IAAIxvB,KAAKS,CAAM,EAChC+uB,OAAAA,EAASjsB,SAAS,EAAG,GAAI,EAAG,CAAC,EAEtB,CACL8rB,OAAQ,GACRC,OAAQ,aACRC,SAAUC,EAASttB,mBAAmB,QAAS,CAC7CC,KAAM,UACNC,OAAQ,UACRxC,SAAU,kBAAA,CACX,EACDA,SAAU,KACVkrB,UAAW,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EAAEA,CAAS,CAAA,MAEhG,CAECyE,MAAAA,EAAW,IAAIvvB,KAAKS,CAAM,EAChC,OAAIqqB,IAAc,EAChByE,EAAS/rB,QAAQ/C,EAAOe,QAAQ,EAAI,CAAC,EAErC+tB,EAAS/rB,QAAQ/C,EAAOe,QAAQ,EAAI,CAAC,EAEvC+tB,EAAShsB,SAAS,EAAG,GAAI,EAAG,CAAC,EAEtB,CACL8rB,OAAQ,GACRC,OAAQ,cACRC,SAAUA,EAASztB,eAAe,QAAS,CACzCkR,QAAS8X,IAAc,EAAI,OAASzjB,OACpClF,KAAM,UACNC,OAAQ,UACRxC,SAAU,kBAAA,CACX,EACDA,SAAU,KACVkrB,UAAW,CAAC,SAAU,SAAU,UAAW,YAAa,WAAY,SAAU,UAAU,EAAEA,CAAS,CAAA,EAGzG,EAKa2E,GAA4DA,CAAC,CACxEC,YAAAA,EACAC,YAAAA,EAAc,GACdjpB,UAAAA,CACF,IAAM,CACJ,MAAMkpB,EAAgBA,IAAM,CAC1B,OAAQF,EAAYJ,OAAM,CACxB,IAAK,OACI,MAAA,eACT,IAAK,aACI,MAAA,aACT,IAAK,cACI,MAAA,cACT,IAAK,SACH,MAAO,oBAAoBI,EAAY5E,YACzC,QACS,MAAA,SACX,CAAA,EAGI+E,EAAgBA,IACfF,EAEDD,EAAYL,OACP,wCACEK,EAAYH,SACd,cAAcG,EAAYH,YAAYG,EAAY9vB,mCAElD,qCAPgB,KAW3B,OACGiG,EAAAA,KAAA8oB,GAAA,CAAe,QAASe,EAAYL,OAAQ,UAAA3oB,EAC3C,SAAA,CAACZ,EAAAA,IAAA+oB,GAAA,CAAU,QAASa,EAAYL,MAAO,CAAA,SACtC,MACC,CAAA,SAAA,CAACvpB,EAAAA,IAAAgpB,GAAA,CAAYc,WAAgB,CAAA,CAAA,EAC5BD,GAAeE,EAAc,GAC3B/pB,EAAA,IAAAipB,GAAA,CAAYc,aAAgB,CAAA,EAEjC,CACF,CAAA,CAAA,CAEJ,EC7LM9C,GAAwBzoB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAwDhC,EAAA,CAAA,o+BAAA,CAAA,EAEKqrB,GAAqBxrB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAO7B,EAAA,CAAA,+KAAA,CAAA,EAEKsrB,GAAeC,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAwCtB,EAAA,CAAA,4sBAAA,CAAA,EAEKwrB,GAAmB3rB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAW3B,EAAA,CAAA,qMAAA,CAAA,EAEKyrB,GAA6B5rB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,qRAAA,EACtB,CAAC,CAAE0rB,WAAAA,CAAW,IAC1BA,EACI,6EACA,uEAEF,CAAC,CAAEA,WAAAA,CAAW,IAAOA,EAAa,uBAAyB,mBAAoB,EAY/EC,GAA+B9rB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,wBAAAC,YAAA,cAAA,CAkDvC,EAAA,CAAA,w1BAAA,CAAA,EAEK4rB,GAAmB/rB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAiB3B,EAAA,CAAA,2WAAA,CAAA,EAEK6rB,GAAyB1rB,EAAAA,KAAIL,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAUlC,EAAA,CAAA,4PAAA,CAAA,EAEK8rB,GAA6BjsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAyCrC,EAAA,CAAA,4xBAAA,CAAA,EAEK+rB,GAAoBlsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAO5B,EAAA,CAAA,sJAAA,CAAA,EAEKgsB,GAA0BnsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAMlC,EAAA,CAAA,0HAAA,CAAA,EAEKisB,GAAoBpsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAK5B,EAAA,CAAA,+FAAA,CAAA,EAEKksB,GAA4BrsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,eAAA,CAgDpC,EAAA,CAAA,usBAAA,CAAA,EAEKmsB,GAA0BtsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,gLAAA,uMAAA,mUAAA,EAUxB,CAAC,CAAEosB,aAAAA,CAAa,IACnBA,GAAgB,GAAW,uBAC3BA,GAAgB,GAAW,uBACxB,qBAgBH,CAAC,CAAEA,aAAAA,CAAa,IACVA,GAAgB,GAAW,0BAC3BA,GAAgB,GAAW,yBACxB,0BACR,EAuBHC,GAA0BxsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAMlC,EAAA,CAAA,wHAAA,CAAA,EAEKssB,GAAsBzsB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,WAAA,yvCAAA,EAClB,CAAC,CAAEusB,MAAAA,CAAM,IAAOA,EAAQ,QAAU,MAAO,EAoFhDC,GAAsB3sB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CA6D9B,EAAA,CAAA,k6BAAA,CAAA,EAEKysB,GAAwB5sB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAiBhC,EAAA,CAAA,8RAAA,CAAA,EAKY0sB,GAAwDA,CAAC,CACpE/E,oBAAAA,EACAC,eAAAA,EACAC,mBAAAA,EACAhL,kBAAAA,EACA5V,UAAAA,EAAY,GACZjB,MAAAA,EAAQ,IACV,IAAM,CACJ,MAAMilB,EAAcV,KACdoC,EAAY1B,EAAY5E,YAAc,YAAc4E,EAAY5E,YAAc,SAC9EuG,EAAoB,CAAC3B,EAAYL,OAEvC,OAAI3jB,EAEC5F,EAAAA,IAAAinB,GAAA,CACC,SAACjnB,EAAAA,IAAA,MAAA,CAAI,MAAO,CAAEuoB,UAAW,SAAUC,QAAS,OAAQC,MAAO,uBAAwB,EAAE,0CAErF,CACF,CAAA,EAIA9jB,EAEC3E,EAAAA,IAAAinB,GAAA,CACC,SAACjnB,EAAAA,IAAA,MAAA,CAAI,MAAO,CAAEuoB,UAAW,SAAUC,QAAS,OAAQC,MAAO,mBAAoB,EAAE,2CAEjF,CACF,CAAA,SAKDxB,GACC,CAAA,KAAK,SACL,kBAAgB,2BAChB,mBAAiB,iBAEjB,SAAA,CAAAlnB,OAACiqB,GACC,CAAA,SAAA,CAAChqB,EAAA,IAAAiqB,GAAA,CAAM,GAAG,2BAA2B,SAAoC,uCAAA,EACzEjqB,MAAC2pB,IAAqB,YAAAC,EAAyB,CAAA,EACjD,EAEC2B,GACEvrB,EAAAA,IAAAoqB,GAAA,CACC,WAAYkB,EACZ,GAAG,iBACH,KAAK,SACL,YAAU,SAETA,SAAAA,EACG,sFACA,KAAK1B,EAAYJ,OAAOZ,QAAQ,IAAK,GAAG,yBACtCgB,EAAYJ,SAAW,aAAe,mBAAqB,wBAEnE,CAAA,EAGDzpB,EAAA,KAAAoqB,GAAA,CAAU,KAAK,OAAO,aAAW,+BAChC,SAAA,CAAApqB,OAACuqB,IACC,KAAK,WACL,kBAAgB,6BAChB,mBAAiB,mBAEjB,SAAA,CAACtqB,EAAAA,IAAAuqB,GAAA,CACC,GAAG,6BACH,aAAY,8BAA8BjE,EAAoB5E,mBAE7D4E,WAAoB5E,gBACvB,CAAA,EACC3hB,OAAAyqB,GAAA,CACC,YAAalE,EAAoB1E,WACjC,GAAG,mBACH,aAAY,qBAAqB0E,EAAoB3E,YAAY/G,QAAQ,CAAC,aACxE0L,EAAoB1E,aAGrB0E,SAAAA,CAAoB3E,EAAAA,YAAY/G,QAAQ,CAAC,EAAE,KAAG0L,EAAoB1E,UAAAA,EACrE,CAAA,EACF,SAEC6I,GACC,CAAA,KAAK,WACL,kBAAgB,cAChB,mBAAiB,cAEjB,SAAA,CAACzqB,EAAA,IAAA0qB,GAAA,CAAW,GAAG,cAAc,SAAiB,oBAAA,EAC9C3qB,EAAAA,KAAC4qB,IACC,aAAY,kBAAkBnP,EAAkBI,uBAAuBlD,qBAAqB8C,EAAkBI,uBAAuBC,iBAEpIL,SAAAA,CAAAA,EAAkBI,uBAAuBlD,aAAa,KAAG,IACzD8C,EAAkBI,uBAAuBC,cAAAA,EAC5C,SACC+O,GACC,CAAA,GAAG,cACH,aAAY,yBAAyBpP,EAAkBI,uBAAuBG,gBAAgBnB,QAC5F,CACF,uBAAuBY,EAAkBI,uBAAuBI,kBAAkBpB,QAChF,CACF,qBAECY,SAAAA,CAAkBI,EAAAA,uBAAuBG,gBAAgBnB,QAAQ,CAAC,EAAE,eAAa,IACjFY,EAAkBI,uBAAuBI,kBAAkBpB,QAAQ,CAAC,EAAE,OAAA,EACzE,CAAA,EACF,SAECiQ,GACC,CAAA,KAAK,WACL,kBAAgB,oBAChB,mBAAiB,oBAEjB,SAAA,CAAA9qB,EAAA,KAAC+qB,GACC,CAAA,aAActE,EAAmBC,iBACjC,GAAG,oBACH,aAAY,wBAAwBD,EAAmBC,iBAAiB7L,QACtE,CACF,YAEC4L,SAAAA,CAAmBC,EAAAA,iBAAiB7L,QAAQ,CAAC,EAAE,GAAA,EAClD,EACC5a,EAAA,IAAAgrB,GAAA,CAAiB,GAAG,oBAAoB,SAAmB,sBAAA,CAAA,EAC9D,CAAA,EACF,EAEAjrB,EAAAA,KAACkrB,IACC,MAAO1E,EAAe1C,WAAa,EACnC,KAAK,QACL,YAAU,YACV,aAAY,4BACV0C,EAAevC,6BACMuC,EAAe1C,WAAWjJ,QAAQ,CAAC,+BACxD2L,EAAe3W,iBACd,SAAA,CAAA,+BAE0B2W,EAAevC,OAAO,KAAGuC,EAAe1C,WAAWjJ,QAAQ,CAAC,EAAC,WACjF2L,EAAe3W,cAAAA,EAC1B,SAECub,GACC,CAAA,KAAK,SACL,kBAAgB,kBAChB,mBAAiB,oBAEjB,SAAA,CAACprB,EAAAA,KAAAqrB,GAAA,CAAe,GAAG,kBAAiB,SAAA,CAAA,OAC7B9E,EAAoB5E,iBAAiB,GAAA,EAC5C,EACC1hB,EAAAA,IAAA,MAAA,CACC,GAAG,oBACH,MAAO,CACLwrB,WAAY,mBACZzqB,SAAU,sBACV0qB,WAAY,MACZhD,MAAO,wBACPiD,WAAY,KAAA,EAEd,aAAY,oBAAoBpF,EAAoBxK,YAEnDwK,WAAoBxK,UACvB,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,ECluBM6P,GAAyBntB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAIjC,EAAA,CAAA,8CAAA,CAAA,EAEKitB,GAAqBptB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAQ7B,EAAA,CAAA,mGAAA,CAAA,EAEKktB,GAAuBlX,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAuBlC,EAAA,CAAA,gWAAA,CAAA,EAEKmtB,GAA0BttB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAIlC,EAAA,CAAA,2DAAA,CAAA,EAEKotB,GAAyBvtB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2DAAA,eAAA,eAAA,GAAA,EAGrB,CAAC,CAAEyoB,YAAAA,CAAY,IAAOA,EAAc,EAAI,EACrC,CAAC,CAAEA,YAAAA,CAAY,IAAOA,EAAc,SAAW,IAC/C,CAAC,CAAEA,YAAAA,CAAY,IAAOA,EAAc,oBAAsB,GAAI,EAGxE4E,GAA4BrX,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gtBAAA,kCAAA,EAqCvB,CAAC,CAAEyoB,YAAAA,CAAY,IAAOA,EAAc,iBAAmB,cAAe,EAKjF6E,GAAsBztB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAQ9B,EAAA,CAAA,+IAAA,CAAA,EAEKutB,GAAoB1tB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAQ5B,EAAA,CAAA,2IAAA,CAAA,EAEKwtB,GAAwB3tB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAiBhC,EAAA,CAAA,0PAAA,CAAA,EAKYytB,GAAkEA,CAAC,CAC9ExmB,UAAAA,EAAY,GACZjB,MAAAA,EAAQ,KACRyJ,UAAAA,EACAxN,UAAAA,CACF,IAAM,CACJ,KAAM,CAAC8nB,EAAY2D,CAAa,EAAI3sB,WAAS,EAAK,EAC5CkqB,EAAcV,KAGd,CACJtZ,eAAgB0c,EAChB1mB,UAAW2mB,EACX5nB,MAAO6nB,GACL3L,GAAwB,EAEtB,CACJuD,SAAUqI,EACV7mB,UAAW8mB,EACX/nB,MAAOgoB,GACLxI,GAAyB,EAEvB,CAAEve,UAAWgnB,EAAgBjoB,MAAOkoB,GAAiB9N,GAA+B,EAEpF,CAAEyH,mBAAAA,EAAoB5gB,UAAWknB,CAAuBzG,EAAAA,GAC5DiG,EACAG,EAAgBpI,YAClB,EAEM,CACJ7I,kBAAAA,EACA5V,UAAWmnB,EACXpoB,MAAOqoB,GACLzR,GAA6B,EAG3B0R,EACJrnB,GACA2mB,GACAG,GACAE,GACAE,GACAC,EAEIG,EAAkBvoB,GAAS6nB,GAAcG,GAAgBE,GAAgBG,EAG/ErtB,EAAAA,UAAU,IAAM,CACS2E,aAAaC,QAAQ,6BAA6B,IAClD,QACrB8nB,EAAc,EAAI,CAEtB,EAAG,CAAE,CAAA,EAEL,MAAMc,EAAwBA,IAAM,CAClC,MAAM/E,EAAc,CAACM,EACrB2D,EAAcjE,CAAW,EACzB9jB,aAAaQ,QAAQ,8BAA+BsjB,EAAY1L,SAAU,CAAA,CAAA,EAI5E,OAAIuQ,QAECG,EACC,CAAA,MAAM,oCACN,UAAAxsB,EACA,QACEwN,GACEpO,EAAA,IAAC6rB,GAAc,CAAA,QAASzd,EAAW,SAAU6e,EAAkB,SAE/D,YAAA,CAAA,EAIJ,gBAAChB,GACC,CAAA,SAAA,CAAAjsB,EAAA,IAACmsB,GAAc,EAAA,EACfnsB,MAAC,OAAI,MAAO,CAAEe,SAAU,OAAQ2qB,WAAY,MAAO2B,aAAc,KAAA,EAAQ,SAEzE,8BAAA,EACArtB,MAAC,OAAI,MAAO,CAAEe,SAAU,OAAQusB,QAAS,EAAA,EAAM,SAE/C,oDAAA,CAAA,CACF,CAAA,CACF,CAAA,EAKAJ,EAECltB,EAAA,IAAAotB,EAAA,CACC,MAAM,oCACN,UAAAxsB,EACA,QAASwN,GAAcpO,MAAA6rB,GAAA,CAAc,QAASzd,EAAW,SAAQ,UAAA,CAAA,EAEjE,gBAAC8d,GACC,CAAA,SAAA,CAAAlsB,MAAC,OAAI,MAAO,CAAEe,SAAU,OAAQssB,aAAc,MAAA,EAAU,SAAE,KAAA,EAC1DrtB,MAAC,OAAI,MAAO,CAAEe,SAAU,OAAQ2qB,WAAY,MAAO2B,aAAc,KAAA,EAAQ,SAEzE,kCAAA,EACArtB,MAAC,OAAI,MAAO,CAAEe,SAAU,OAAQusB,QAAS,EAAA,EAAQJ,SAAgBA,EAAA,CAAA,CACnE,CAAA,CACF,CAAA,QAKDE,EACC,CAAA,MAAM,oCACN,UAAAxsB,EACA,eACGgrB,GACC,CAAA,SAAA,CAAC5rB,EAAAA,IAAA2pB,GAAA,CAAqB,YAAAC,EAA0B,YAAW,EAAA,CAAA,SAC1DoC,GACC,CAAA,YAAatD,EACb,QAASyE,EACT,SAAUF,EAETvE,SAAAA,CAAAA,EAAa,cAAgB,uBAC7B1oB,EAAA,IAAA,OAAA,CAAK,UAAU,cAAc,SAAC,IAAA,CAAA,EACjC,EACCoO,GACEpO,EAAAA,IAAA6rB,GAAA,CAAc,QAASzd,EAAW,SAAU6e,EAAkB,SAE/D,aAAA,CAAA,EAEJ,EAGF,SAAAjtB,EAAAA,IAAC2rB,GACC,CAAA,SAAA5rB,EAAA,KAAC+rB,GAEC,CAAA,SAAA,CAAA9rB,MAACqrB,IACC,oBAAqBiB,EACrB,eAAgBG,EAAgBpI,aAChC,mBAAAmC,EACA,kBAAAhL,EAAqC,EAItCxb,EAAA,IAAA+rB,GAAA,CAAgB,YAAarD,EAC5B,SAAC1oB,EAAA,IAAA8nB,GAAA,CACC,oBAAqBwE,EACrB,eAAgBG,EAAgBpI,aAChC,mBAAAmC,EACA,kBAAAhL,CAAqC,CAAA,EAEzC,CAAA,EACF,EACF,CACF,CAAA,CAEJ,ECvRM+R,GAAsBnd,GAAgF,WAC1G,MAAMod,EAA4D,CAAA,EAC5D5mB,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CuV,IAAYrd,EAAAA,EAAMA,MAAMc,aAAZd,YAAAA,EAAwB8H,gBAAiB,GACrDkI,EAAW,GAAGxZ,KAAS0F,KAASmhB,IAGlCrN,OAAAA,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,gBAAgB,GAAK8hB,EAAS9hB,SAAS,WAAW,IAClGkvB,EAAOjkB,KAAK,CAAE/O,KAAM,MAAOkzB,QAAS,YAAYtd,EAAMA,MAAMnW,MAAAA,CAAQ,GAIlEmmB,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,kBAAkB,GAAK8hB,EAAS9hB,SAAS,YAAY,IACtGkvB,EAAOjkB,KAAK,CAAE/O,KAAM,OAAQkzB,QAAS,mBAAmBtd,EAAMA,MAAMnW,MAAAA,CAAQ,GAI1EmmB,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,iBAAiB,GAAK8hB,EAAS9hB,SAAS,WAAW,IACpGkvB,EAAOjkB,KAAK,CAAE/O,KAAM,OAAQkzB,QAAS,mBAAmBtd,EAAMA,MAAMnW,MAAAA,CAAQ,GAI1EmmB,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,UAAU,GAAK8hB,EAAS9hB,SAAS,cAAc,IAC9FkvB,EAAOjkB,KAAK,CAAE/O,KAAM,KAAMkzB,QAAS,iBAAiBtd,EAAMA,MAAMnW,MAAAA,CAAQ,GAItEmmB,EAAS9hB,SAAS,WAAW,GAAK8hB,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,MAAM,GAAK8hB,EAAS9hB,SAAS,MAAM,IACvHkvB,EAAOjkB,KAAK,CAAE/O,KAAM,YAAakzB,QAAS,yBAAyBtd,EAAMA,MAAMnW,MAAAA,CAAQ,EAGlFuzB,CACT,EAKMG,GAAgBC,GAA8B,CAC5Cxd,MAAAA,EAAQ,IAAIlW,KAAK0zB,CAAS,EAE1BC,EAAW3wB,KAAKyiB,QADNzlB,OACcgC,UAAYkU,EAAMlU,SAAS,EACnD4xB,EAAW5wB,KAAK6wB,KAAKF,GAAY,IAAO,GAAK,GAAK,GAAG,EAE3D,OAAIC,IAAa,EAAU,QACvBA,EAAW,EAAU,GAAGA,SACxBA,EAAW,GAAW,GAAG5wB,KAAKC,MAAM2wB,EAAW,CAAC,UAC7C,GAAG5wB,KAAKC,MAAM2wB,EAAW,EAAE,UACpC,EAKME,GAAsB5d,GAAqC,SAC/D,MAAMxJ,IAAQwJ,EAAAA,EAAMA,MAAMxJ,QAAZwJ,YAAAA,EAAmB8H,gBAAiB,GAC5C5L,IAAQ8D,EAAAA,EAAMA,MAAM9D,QAAZ8D,YAAAA,EAAmB8H,gBAAiB,GAC5CkI,EAAW,GAAGxZ,KAAS0F,IAE7B,GAAI8T,EAAS9hB,SAAS,OAAO,GAAK8hB,EAAS9hB,SAAS,IAAI,EAAU,MAAA,QAClE,GAAI8hB,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,KAAK,EAAU,MAAA,KAChE,GAAI8hB,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,KAAK,EAAU,MAAA,KAChE,GAAI8hB,EAAS9hB,SAAS,KAAK,GAAK8hB,EAAS9hB,SAAS,OAAO,EAAU,MAAA,MACnE,GAAI8hB,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,MAAM,EAAU,MAAA,KACjE,GAAI8hB,EAAS9hB,SAAS,IAAI,GAAK8hB,EAAS9hB,SAAS,MAAM,EAAU,MAAA,KAG3Dmb,MAAAA,EAAUrJ,EAAMA,MAAMqJ,QACxBA,OAAAA,IAAY,WAAaA,IAAY,cAAsB,MACxD,IACT,EAKMwU,GAAsBA,CAAC7d,EAA0B8d,IAA4C,CAC3F3hB,MAAAA,EAAa6D,EAAMA,MAAM+d,YACzBC,EAAYhe,EAAMA,MAAMie,WAE9B,GAAI,CAAC9hB,EAAmB,MAAA,MAGxB,OAAQ2hB,EAAS,CACf,IAAK,MAEH,MAAMI,EAAWpxB,KAAKyiB,KAAKyO,GAAa7hB,GAAcA,CAAU,EACzD,MAAA,IAAIA,EAAa+hB,EAAS,GAAG1T,QAAQ,CAAC,MAAMrO,EAAa+hB,EAAS,GAAG1T,QAAQ,CAAC,IACvF,IAAK,OACL,IAAK,OACL,IAAK,KACL,IAAK,YACIrO,OAAAA,EAAWqO,QAAQ,CAAC,EAC7B,QACSrO,OAAAA,EAAWqO,QAAQ,CAAC,CAC/B,CACF,EAKM2T,GAA4BA,CAChCL,EACAlf,IACgC,CAC1Bwf,MAAAA,EAAcxf,EAAOhL,OAAgBoM,GAC1Bmd,GAAmBnd,CAAK,EACzBhN,KAAYqrB,GAAAA,EAAIj0B,OAAS0zB,CAAS,CACjD,EAEKzc,EAAc+c,EAAYrrB,OAC1BqN,EAAgBge,EAAYxqB,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACpEwL,EAAU8C,EAAc,EAAKjB,EAAgBiB,EAAe,IAAM,EAElEyH,EAAasV,EAChBxzB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EAAeuI,EAAW/V,OAAS,EACvC+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAG5DurB,EAAmBF,EAAYxqB,OAAOyM,GAC1CA,EAAEL,MAAMM,WAAa,OAAUD,EAAEL,MAAMU,YAAcL,EAAEL,MAAMU,WAAa,CAC5E,EAAE3N,OACI2W,EAAcrI,EAAc,EAAKid,EAAmBjd,EAAe,IAAM,EAExE,MAAA,CACLA,YAAAA,EACA9C,QAAAA,EACAgC,aAAAA,EACAmJ,YAAAA,CAAAA,CAEJ,EAKM6U,GAAyBA,CAC7BT,EACApf,EACA8f,IACmE,CAC7D,KAAA,CAAEjgB,QAAAA,EAASgC,aAAAA,EAAcc,YAAAA,CAAgB3C,EAAAA,EAG/C,IAAI+f,EAAU,GACd,OAAQX,EAAS,CACf,IAAK,MACOW,EAAA,mCACV,MACF,IAAK,OACOA,EAAA,6CACV,MACF,IAAK,OACOA,EAAA,6CACV,MACF,IAAK,KACOA,EAAA,wCACV,MACF,IAAK,YACOA,EAAA,6CACV,KACJ,CAGA,IAAIvlB,EAAqC,SACrCwlB,EAAkB,GAEtB,OAAIrd,GAAe,EACb9C,GAAW,IAAMgC,GAAgB,KACxBrH,EAAA,OACXwlB,EAAkB,KAAKngB,EAAQiM,QAAQ,CAAC,gBAAgBjK,EAAaiK,QAAQ,CAAC,WACrEjM,GAAW,IAAMgC,GAAgB,KAC/BrH,EAAA,SACOwlB,EAAA,KAAKngB,EAAQiM,QAAQ,CAAC,iBAE7BtR,EAAA,MACOwlB,EAAA,KAAKngB,EAAQiM,QAAQ,CAAC,8BAEjCnJ,EAAc,EACvBqd,EAAkB,KAAKrd,UAAoBA,EAAc,EAAI,IAAM,sBAEjDqd,EAAA,wBACPxlB,EAAA,OAITslB,EAAItwB,SAAS,KAAK,GAAK,CAACswB,EAAItwB,SAAS,MAAM,EAC1BwwB,GAAA,iBACVF,EAAItwB,SAAS,MAAM,IACTwwB,GAAA,wBAGd,CACLlf,eAAgBif,EAAUC,EAC1BxlB,SAAAA,CAAAA,CAEJ,EAKaylB,GAAyBA,IAAM,CAC1C,KAAM,CAAC/f,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,mDAAoDD,CAAG,EACrEyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EAuHE,CACL8f,oBArHsDltB,EAAAA,QAAQ,IAAM,CAChEkN,GAAAA,EAAO7L,SAAW,EACb,OAAA,KAIH8rB,MAAAA,MAAoB/0B,KAC1B+0B,EAAcvxB,QAAQuxB,EAAcvzB,QAAQ,EAAI,EAAE,EAE5CukB,MAAAA,EAAejR,EAAOhL,OAAgBoM,GACxB,IAAIlW,KAAKkW,EAAMA,MAAMnW,IAAI,GACvBg1B,CACrB,EAGKC,MAAiBzW,IAEvBwH,EAAa5W,QAAiB+G,GAAA,CACbmd,GAAmBnd,CAAK,EAChC/G,QAAiB8lB,GAAA,CAChBxsB,MAAAA,EAAM,GAAGwsB,EAAM30B,QAAQ4V,EAAMA,MAAMnW,QAAQmW,EAAMA,MAAM+d,cACxDe,EAAWtW,IAAIjW,CAAG,GACrBusB,EAAWrW,IAAIlW,EAAK,CAAEnI,KAAM20B,EAAM30B,KAAM4V,MAAAA,CAAAA,CAAO,CACjD,CACD,CAAA,CACF,EAGKgf,MAAAA,EAAiC3U,MAAMC,KAAKwU,EAAWpL,OAAQ,CAAA,EAAE9oB,IAAI,CAAC,CAAER,KAAAA,EAAM4V,MAAAA,CAAAA,IAAY,CACxFtB,MAAAA,EAAcyf,GAA0B/zB,EAAMwU,CAAM,EACpD4f,EAAMjB,GAAavd,EAAMA,MAAMnW,IAAI,EACnCo1B,EAAYrB,GAAmB5d,CAAK,EACpC5F,EAAQyjB,GAAoB7d,EAAO5V,CAAI,EACvC,CAAEoV,eAAAA,EAAgBtG,SAAAA,CAAaqlB,EAAAA,GAAuBn0B,EAAMsU,EAAa8f,CAAG,EAG5ErY,EACHzH,EAAY2C,aAAe,GAAK3C,EAAYH,SAAW,IACvDG,EAAY2C,YAAc,GAAKmd,EAAItwB,SAAS,KAAK,EAG7C,MAAA,CACL9D,KAAAA,EACAgQ,MAAAA,EACA6kB,UAAAA,EACAT,IAAAA,EACArY,SAAAA,EACAzH,YAAAA,EACAc,eAAAA,EACAtG,SAAAA,CAAAA,CACF,CACD,EAGc4I,EAAAA,KAAK,CAACZ,EAAGC,IAAM,CAC5B,MAAM+d,EAAgB,CAAEC,KAAM,EAAGC,OAAQ,EAAGC,IAAK,CAAA,EAC3CC,EAAYJ,EAAche,EAAEhI,QAAQ,EACpCqmB,EAAYL,EAAc/d,EAAEjI,QAAQ,EAE1C,OAAIomB,IAAcC,EAAkBA,EAAYD,EACzCne,EAAEzC,YAAYH,QAAU2C,EAAExC,YAAYH,OAAAA,CAC9C,EAGD,MAAMihB,EAAoBR,EAAeprB,OAAcyqB,GAAAA,EAAIlY,QAAQ,EAAEpT,OAU/D0sB,EAPkB,CAAC,MAAO,OAAQ,OAAQ,KAAM,WAAW,EAAE70B,IAAYR,GAAA,CAC7E,MAAMs1B,EAAaV,EAAeprB,OAAcyqB,GAAAA,EAAIj0B,OAASA,CAAI,EAC3Du1B,EAAaD,EAAW3sB,OAAS,EACrC2sB,EAAWlf,OAAO,CAACC,EAAK4d,IAAQ5d,EAAM4d,EAAI3f,YAAYH,QAAS,CAAC,EAAImhB,EAAW3sB,OAAS,EACnF,MAAA,CAAE3I,KAAAA,EAAMu1B,WAAAA,EAAYjL,MAAOgL,EAAW3sB,MAAAA,CAAO,CACrD,EAEgCyN,OAAO,CAACwN,EAAMtB,IAC7CA,EAAQiT,WAAa3R,EAAK2R,YAAcjT,EAAQgI,MAAQ,EAAIhI,EAAUsB,EACtE,CAAE5jB,KAAM,MAAOu1B,WAAY,EAAGjL,MAAO,CAAA,CAAG,EAEpCkL,EAAqBZ,EAAejsB,OAAS,EACjDisB,EAAexe,OAAO,CAACC,EAAK4d,IAAQ5d,EAAM4d,EAAI3f,YAAYgL,YAAa,CAAC,EAAIsV,EAAejsB,OAAS,EAEhGwN,EAAeye,EAAejsB,OAAS,EAC3CisB,EAAexe,OAAO,CAACC,EAAK4d,IAAQ5d,EAAM4d,EAAI3f,YAAY6B,aAAc,CAAC,EAAIye,EAAejsB,OAAS,EAGjG8sB,EAAwB,CAAA,EAC1BJ,EAASr1B,OAAS,OACR+O,EAAAA,KAAK,GAAGsmB,EAASr1B,sCAAsCq1B,EAASE,WAAWnV,QAAQ,CAAC,kBAAkB,EAGpH,MAAMsV,EAAoBd,EAAeprB,UAAcyqB,EAAInlB,WAAa,MAAM,EAAEnG,OAC5E+sB,EAAoB,GACV3mB,EAAAA,KAAK,GAAG2mB,+BAA+C,EAGjEF,GAAsB,GACxBC,EAAY1mB,KAAK,qCAAqC,EAC7CymB,EAAqB,IAC9BC,EAAY1mB,KAAK,gCAAgC,EAGnD,MAAMS,EAA0B,CAC9B4lB,kBAAAA,EACAO,mBAAoBN,EAASr1B,KAC7Bw1B,mBAAAA,EACArf,aAAAA,EACAsf,YAAAA,CAAAA,EAGK,MAAA,CACLb,eAAgBA,EAAehd,MAAM,EAAG,EAAE,EAC1CpI,QAAAA,EACAkB,YAAa,IAAIhR,KAAK,EAAE4L,YAAY,CAAA,CACtC,EACC,CAACkJ,CAAM,CAAC,EAITpJ,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECjXMmhB,GAAmB5xB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAI3B,EAAA,CAAA,8CAAA,CAAA,EAEK0oB,GAAsB6C,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAQ7B,EAAA,CAAA,8HAAA,CAAA,EAEK0xB,GAAcC,EAAO9xB,IAAI+xB,MAC7B,CAAC,CAAErC,UAAAA,EAAW3X,SAAAA,CAAS,KAAO,CAC5B3V,UAAW,6BAA6B2V,EAAW,SAAW,KAC9D,kBAAmB2X,EAAUhW,YAAY,EACzC,cAAe3B,CACjB,EACF,EAAC9X,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAwCA,EAAA,CAAA,k6BAAA,CAAA,EAEK6xB,GAAqBhyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAK7B,EAAA,CAAA,mFAAA,CAAA,EAEK8xB,GAAYH,EAAO9xB,IAAI+xB,MAA6B,CAAC,CAAErC,UAAAA,CAAU,KAAO,CAC5EttB,UAAW,uBACX,kBAAmBstB,EAAUhW,YAAY,CAC3C,EAAE,EAACzZ,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAiCF,EAAA,CAAA,ubAAA,CAAA,EAEK+xB,GAAcJ,EAAO9xB,IAAI+xB,MAA6B,CAAC,CAAEha,SAAAA,CAAS,KAAO,CAC7E3V,UAAW,2BACX,cAAe2V,CACjB,EAAE,EAAC9X,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,8FAAA,EACa,CAAC,CAAE4X,SAAAA,CAAS,IACxBA,EAAW,uBAAyB,6BAC7B,CAAC,CAAEA,SAAAA,CAAS,IACnBA,EAAW,8BAAgC,+BAA+B,EAQxEoa,GAAmBnyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAK3B,EAAA,CAAA,0EAAA,CAAA,EAEKiyB,GAAqBpyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAE7B,EAAA,CAAA,oBAAA,CAAA,EAEKkyB,GAAoBryB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAI5B,EAAA,CAAA,mEAAA,CAAA,EAEKmyB,GAAoBtyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAM5B,EAAA,CAAA,kHAAA,CAAA,EAEKoyB,GAA0BvyB,EAAAA,IAAI+xB,MAAM,CACxC3vB,UAAW,oCACb,CAAC,EAACnC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAOD,EAAA,CAAA,2JAAA,CAAA,EAEKqyB,GAAmBxyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAI3B,EAAA,CAAA,2DAAA,CAAA,EAEKsyB,EAAkBzyB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,eAAA,CAE1B,EAAA,CAAA,oBAAA,CAAA,EAEKuyB,EAAmB1yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAI3B,EAAA,CAAA,mEAAA,CAAA,EAEKwyB,EAAmB3yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAI3B,EAAA,CAAA,6EAAA,CAAA,EAEKyyB,GAAgBd,EAAO9xB,IAAI+xB,MAA+C,CAAC,CAAEjnB,SAAAA,CAAS,KAAO,CACjG1I,UAAW,iBACX,gBAAiB0I,CACnB,EAAE,EAAC7K,WAAA,CAAAC,YAAA,gBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,cAAA,UAAA,8GAAA,EACa,CAAC,CAAE2K,SAAAA,CAAS,IAAM,CAC9B,OAAQA,EAAQ,CACd,IAAK,OACI,MAAA,qBACT,IAAK,SACI,MAAA,uBACT,QACS,MAAA,4BACX,CACF,EACS,CAAC,CAAEA,SAAAA,CAAS,IACnBA,IAAa,MAAQ,gCAAkC,6BAA6B,EASlF+nB,GAA4B7yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,eAAA,CAOpC,EAAA,CAAA,oJAAA,CAAA,EAEK2yB,GAAoB9yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAO5B,EAAA,CAAA,2KAAA,CAAA,EAEK4yB,GAAmB/yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAG3B,EAAA,CAAA,oCAAA,CAAA,EAEK6yB,GAAoBtH,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAI3B,EAAA,CAAA,qEAAA,CAAA,EAEK8yB,GAAsBC,EAAAA,EAACjzB,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAI5B,EAAA,CAAA,qEAAA,CAAA,EAKYgzB,GAA8CA,CAAC,CAC1D/rB,UAAAA,EAAY,GACZjB,MAAAA,EAAQ,KACRyJ,UAAAA,EACAxN,UAAAA,CACF,IAAM,CACE,KAAA,CACJouB,oBAAAA,EACAppB,UAAWgsB,EACXjtB,MAAOktB,GACL9C,GAAuB,EAErB+C,EAAUlsB,GAAagsB,EACvBG,EAAeptB,GAASktB,EAG9B,OAAIC,QAEC1E,EAAK,CAAA,MAAM,2BACV,SAAAptB,EAAA,IAAC,OAAI,MAAO,CAAEwoB,QAAS,OAAQD,UAAW,QAAS,EAAE,8DAErD,CACF,CAAA,EAKAwJ,QAEC3E,EAAK,CAAA,MAAM,2BACV,SAAArtB,EAAA,KAAC,OAAI,MAAO,CAAEyoB,QAAS,OAAQD,UAAW,SAAUE,MAAO,oBAAuB,EAAA,SAAA,CAAA,UACxEsJ,EACP3jB,GACCpO,EAAA,IAAC,SACC,CAAA,QAASoO,EACT,MAAO,CACL4jB,WAAY,OACZxJ,QAAS,WACT/T,WAAY,yBACZzB,OAAQ,uCACRC,aAAc,MACdgf,OAAQ,UACRxJ,MAAO,6BAAA,EACP,SAGJ,QAAA,CAAA,CAEJ,CAAA,CACF,CAAA,EAKA,CAACuG,GAAuBA,EAAoBI,eAAejsB,SAAW,EAErEnD,EAAA,IAAAotB,EAAA,CAAK,MAAM,2BACV,gBAACkE,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAE,IAAA,CAAA,EACbvxB,EAAAA,IAACwxB,IAAW,SAA0B,4BAAA,CAAA,EACtCxxB,EAAAA,IAACyxB,IAAY,SAIb,uLAAA,CAAA,CAAA,CACF,CAAA,CACF,CAAA,EAKFzxB,EAAA,IAACotB,EACC,CAAA,MAAM,2BACN,QACEhf,EACGpO,EAAAA,IAAA,SAAA,CACC,QAASoO,EACT,MAAO,CAAEqG,WAAY,OAAQzB,OAAQ,OAAQif,OAAQ,UAAWxJ,MAAO,WAAY,SAGrF,aAAA,EACElnB,OAGN,SAAAxB,EAAAA,KAACqwB,IAAU,UAAAxvB,EAET,SAAA,CAAAb,OAAC,MACC,CAAA,SAAA,CAAAA,OAACsnB,GAAY,CAAA,SAAA,CAAA,sBAEVrnB,EAAA,IAAAoxB,GAAA,CAAc,SAAS,OAAO,SAAgB,mBAAA,CAAA,EACjD,EAECpC,EAAoBI,eAAep0B,IAAI,CAACm0B,EAAO7rB,IAC7CvD,EAAAA,KAAAswB,GAAA,CAAwB,UAAWlB,EAAM30B,KAAM,SAAU20B,EAAM5Y,SAC9D,SAAA,CAAAxW,OAACywB,GACC,CAAA,SAAA,CAAAxwB,MAACywB,GAAU,CAAA,UAAWtB,EAAM30B,KAAO20B,WAAM30B,KAAK,EAC9CwF,EAAAA,IAAC0wB,IAAY,SAAUvB,EAAM5Y,SAC1B4Y,SAAM5Y,EAAAA,SAAW,SAAW,UAC/B,CAAA,CAAA,EACF,SAECoa,GACC,CAAA,SAAA,CAAA5wB,OAAC6wB,GACC,CAAA,SAAA,CAAC5wB,EAAAA,IAAA6wB,GAAA,CAAY1B,WAAM3kB,KAAM,CAAA,EACzBxK,EAAAA,IAAC8wB,IAAW,SAAK,OAAA,CAAA,CAAA,EACnB,SACCF,GACC,CAAA,SAAA,CAAC5wB,EAAAA,IAAA6wB,GAAA,CAAY1B,WAAME,SAAU,CAAA,EAC7BrvB,EAAAA,IAAC8wB,IAAW,SAAS,WAAA,CAAA,CAAA,EACvB,SACCF,GACC,CAAA,SAAA,CAAC5wB,EAAAA,IAAA6wB,GAAA,CAAY1B,WAAMP,GAAI,CAAA,EACvB5uB,EAAAA,IAAC8wB,IAAW,SAAG,KAAA,CAAA,CAAA,EACjB,CAAA,EACF,EAEA9wB,EAAA,IAAC+wB,GACC,CAAA,SAAAhxB,EAAAA,KAACixB,GACC,CAAA,SAAA,CAAAjxB,OAACkxB,EACC,CAAA,SAAA,CAACjxB,EAAA,IAAAkxB,EAAA,CAAW/B,SAAMrgB,EAAAA,YAAY2C,YAAY,EAC1CzR,EAAAA,IAACmxB,GAAU,SAAM,QAAA,CAAA,CAAA,EACnB,SACCF,EACC,CAAA,SAAA,CAAAlxB,OAACmxB,EAAW/B,CAAAA,SAAAA,CAAMrgB,EAAAA,YAAYH,QAAQiM,QAAQ,CAAC,EAAE,GAAA,EAAC,EAClD5a,EAAAA,IAACmxB,GAAU,SAAQ,UAAA,CAAA,CAAA,EACrB,SACCF,EACC,CAAA,SAAA,CAAAlxB,OAACmxB,EAAW/B,CAAAA,SAAAA,CAAMrgB,EAAAA,YAAY6B,aAAaiK,QAAQ,CAAC,EAAE,GAAA,EAAC,EACvD5a,EAAAA,IAACmxB,GAAU,SAAK,OAAA,CAAA,CAAA,EAClB,SACCF,EACC,CAAA,SAAA,CAAAlxB,OAACmxB,EAAW/B,CAAAA,SAAAA,CAAMrgB,EAAAA,YAAYgL,YAAYc,QAAQ,CAAC,EAAE,GAAA,EAAC,EACtD5a,EAAAA,IAACmxB,GAAU,SAAY,cAAA,CAAA,CAAA,EACzB,CAAA,CAAA,CACF,CACF,CAAA,SAECE,GACC,CAAA,SAAA,CAAArxB,EAAAA,IAAC,UAAO,SAAS,WAAA,CAAA,EAAS,IAAEmvB,EAAMvf,cAAAA,EACpC,CAAA,CAAA,EA9CgBtM,CA+ClB,CACD,CAAA,EACH,SAGC,MACC,CAAA,SAAA,CAAAtD,EAAAA,IAACqnB,IAAa,SAA+B,iCAAA,CAAA,EAC5CtnB,EAAA,KAAAswB,GAAA,CAAY,UAAU,UAAU,SAAU,GACzC,SAAA,CAAAtwB,OAACixB,GACC,CAAA,SAAA,CAAAjxB,OAACkxB,EACC,CAAA,SAAA,CAACjxB,EAAA,IAAAkxB,EAAA,CAAWlC,SAAoBhlB,EAAAA,QAAQ4lB,kBAAkB,EAC1D5vB,EAAAA,IAACmxB,GAAU,SAAa,eAAA,CAAA,CAAA,EAC1B,SACCF,EACC,CAAA,SAAA,CAACjxB,EAAA,IAAAkxB,EAAA,CAAWlC,SAAoBhlB,EAAAA,QAAQmmB,mBAAmB,EAC3DnwB,EAAAA,IAACmxB,GAAU,SAAS,WAAA,CAAA,CAAA,EACtB,SACCF,EACC,CAAA,SAAA,CAAAlxB,OAACmxB,EAAWlC,CAAAA,SAAAA,CAAoBhlB,EAAAA,QAAQgmB,mBAAmBpV,QAAQ,CAAC,EAAE,GAAA,EAAC,EACvE5a,EAAAA,IAACmxB,GAAU,SAAe,iBAAA,CAAA,CAAA,EAC5B,SACCF,EACC,CAAA,SAAA,CAAAlxB,OAACmxB,EAAWlC,CAAAA,SAAAA,CAAoBhlB,EAAAA,QAAQ2G,aAAaiK,QAAQ,CAAC,EAAE,GAAA,EAAC,EACjE5a,EAAAA,IAACmxB,GAAU,SAAc,gBAAA,CAAA,CAAA,EAC3B,CAAA,EACF,SAECE,GACC,CAAA,SAAA,CAAArxB,EAAAA,IAAC,UAAO,SAAa,eAAA,CAAA,EAAS,IAAEgvB,EAAoBhlB,QAAQimB,YAAYvN,KAAK,KAAK,CAAA,EACpF,CAAA,EACF,CAAA,EACF,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,ECpXMwP,GAAgB9hB,GAA4C,CAE5DA,GAAAA,EAAMA,MAAMqJ,QACd,OAAOrJ,EAAMA,MAAMqJ,QAIfyD,MAAAA,EAAY9M,EAAMA,MAAMC,WAC9B,GAAI,CAAC6M,EAAkB,OAAA,KAEjB,KAAA,CAACriB,EAAOC,CAAO,EAAIoiB,EAAUniB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAClDkiB,EAActiB,EAAQ,GAAKC,EAG7BqiB,OAAAA,GAAe,KAAOA,EAAc,IAAY,aAChDA,GAAe,KAAOA,EAAc,IAAY,UAChDA,GAAe,KAAOA,EAAc,IAAY,cAChDA,GAAe,KAAOA,EAAc,IAAY,MAE7C,IACT,EAKMgV,GAA0BA,CAAC5U,EAAoCF,KACnD,CACd,aAAc,CACZ,CAAEjB,MAAO,QAASC,IAAK,QAASrQ,YAAa,wBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,yBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,mBAAA,CAAqB,EAEpE,UAAW,CACT,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,wBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,gBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,uBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,mBAAA,CAAqB,EAEpE,cAAe,CACb,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,gBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,oBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,sBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,sBAAA,CAAwB,EAEvEuQ,IAAK,CACH,CAAEH,MAAO,QAASC,IAAK,QAASrQ,YAAa,iBAAA,EAC7C,CAAEoQ,MAAO,QAASC,IAAK,QAASrQ,YAAa,gBAAA,CAAkB,CAAA,EAIpCqR,CAAmC,GAAK,CAAA,GAEjDriB,IAAcod,GAAA,CAC5BqF,MAAAA,EAAeF,EAAcvZ,OAAgBoM,GAAA,CAC3C8M,MAAAA,EAAY9M,EAAMA,MAAMC,WAC9B,GAAI,CAAC6M,EAAkB,MAAA,GAEjB,KAAA,CAACriB,EAAOC,CAAO,EAAIoiB,EAAUniB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAClDm3B,EAAev3B,EAAQ,GAAKC,EAC5B,CAACu3B,EAAYC,CAAS,EAAIla,EAAOgE,MAAMrhB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAC5D,CAACs3B,EAAUC,CAAO,EAAIpa,EAAOiE,IAAIthB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACtDw3B,EAAeJ,EAAa,GAAKC,EACjCI,EAAaH,EAAW,GAAKC,EAE5BJ,OAAAA,GAAgBK,GAAgBL,GAAgBM,CAAAA,CACxD,EAEK7gB,EAAO4L,EAAazZ,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAC5DwL,EAAU8O,EAAata,OAAS,EAAK0O,EAAO4L,EAAata,OAAU,IAAM,EAExE,MAAA,CACL,GAAGiV,EACHzJ,QAAAA,EACAK,OAAQyO,EAAata,MAAAA,CACvB,CACD,EAMGwvB,GAA4BA,CAChChY,EACA4C,IAC0B,CAC1B,MAAMF,EAAc1C,EAGdlJ,EAAc8L,EAAcpa,OAC5BqN,EAAgB+M,EAAcvZ,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACtEwL,EAAU8C,EAAc,EAAKjB,EAAgBiB,EAAe,IAAM,EAElEyH,EAAaqE,EAChBviB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAsCA,GAAM,IAAI,EACrDxI,EACJuI,EAAW/V,OAAS,EAAI+V,EAAWtI,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAID,EAAW/V,OAAS,EAEpF4N,EAAWwM,EACdviB,IAAIyV,GAAKA,EAAEL,MAAMY,aAAe,CAAC,EACjCJ,OAAO,CAACC,EAAK0J,IAAO1J,EAAM0J,EAAI,CAAC,EAE5BqY,EAAarV,EAAcviB,IAAIyV,GAAKA,EAAEL,MAAMyiB,aAAe,CAAC,EAAE7uB,OAAYmV,GAAAA,EAAI,CAAC,EAC/E2Z,EACJF,EAAWzvB,OAAS,EAAIyvB,EAAWhiB,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAIyZ,EAAWzvB,OAAS,EAGpFua,EAAeH,EAAcvZ,UAAYyM,EAAEL,MAAMc,aAAe,SAAS,EACzEyM,EAAcJ,EAAcvZ,UAAYyM,EAAEL,MAAMc,aAAe,QAAQ,EAEvEyP,EAAajD,EAAa1Z,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAClEyd,EAAYjD,EAAY3Z,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAEhEya,EAAgBF,EAAava,OAAS,EAAKwd,EAAajD,EAAava,OAAU,IAAM,EACrF0a,EAAeF,EAAYxa,OAAS,EAAKyd,EAAYjD,EAAYxa,OAAU,IAAM,EAEjF4vB,EAAWrV,EACd1iB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAmBA,IAAM5X,MAAS,EACvCyxB,EAAUrV,EACb3iB,IAAIyV,GAAKA,EAAEL,MAAMU,UAAU,EAC3B9M,OAAQmV,GAAmBA,IAAM5X,MAAS,EAEvC0xB,EACJF,EAAS5vB,OAAS,EAAI4vB,EAASniB,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAI4Z,EAAS5vB,OAAS,EAC9E+vB,EACJF,EAAQ7vB,OAAS,EAAI6vB,EAAQpiB,OAAO,CAACC,EAAKsI,IAAMtI,EAAMsI,EAAG,CAAC,EAAI6Z,EAAQ7vB,OAAS,EAGjF,IAAImjB,EAAuD,SACvD5I,EAAava,QAAU,GAAKwa,EAAYxa,QAAU,EAChDya,EAAgBC,EAAe,GAA0ByI,EAAA,UACpDzI,EAAeD,EAAgB,KAA0B0I,EAAA,UACzD5I,EAAava,QAAU,EACVmjB,EAAA,UACb3I,EAAYxa,QAAU,IACTmjB,EAAA,UAIlB6M,MAAAA,EAAmB5V,EACtBvZ,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EACtC1V,OAASyV,EAAEL,MAAMiJ,sBAAsB,EACvCrV,OAAQsV,GAAsCA,GAAM,IAAI,EAErD8Z,EACJD,EAAiBhwB,OAAS,EACtBgwB,EAAiBviB,OAAO,CAACC,EAAKyI,IAAMzI,EAAMyI,EAAG,CAAC,EAAI6Z,EAAiBhwB,OACnE,IACAkwB,EAAmBn2B,KAAK8f,IAAI,EAAKoW,EAAoB,EAAG,EAGxDE,EAA4B,CAAA,EAClC,OAAIhN,IAAwB,UAC1BgN,EAAgB/pB,KACd,YAAY+c,aACVA,IAAwB,UAAY1I,EAAchD,QAAQ,CAAC,EAAIiD,EAAajD,QAAQ,CAAC,cAEzF,EAEEyY,EAAmB,GACrBC,EAAgB/pB,KACd,6BAA6B8pB,EAAiBzY,QAAQ,CAAC,4BACzD,EAEEkY,EAAU,GACZQ,EAAgB/pB,KAAK,UAAUupB,EAAQlY,QAAQ,CAAC,wCAAwC,EAWnF,CACLD,YAAAA,EACA0C,YAAAA,EACAkW,UAViB,CACjB,aAAc,cACd,UAAW,cACX,cAAe,cACfhX,IAAK,aAAA,EAMiBc,CAAW,GAAK,GACtCvO,YAAa,CACX2C,YAAAA,EACAjB,cAAAA,EACA7B,QAAAA,EACAgC,aAAAA,EACAI,SAAAA,EACA+hB,QAAAA,CACF,EACAhV,gBAAiB,CACf0V,OAAQ,CAAExkB,OAAQ0O,EAAava,OAAQwL,QAASiP,EAAewD,KAAM6R,CAAW,EAChFQ,MAAO,CAAEzkB,OAAQ2O,EAAYxa,OAAQwL,QAASkP,EAAcuD,KAAM8R,CAAU,EAC5EtjB,eAAgB0W,CAClB,EACA/G,eAAgB4S,GAAwB5U,EAAeF,CAAW,EAClEgW,iBAAAA,EACAC,gBAAAA,CAAAA,CAEJ,EAKaI,GAAiCA,IAAM,CAClD,KAAM,CAAC1kB,EAAQC,CAAS,EAAIvP,EAAAA,SAA8B,CAAE,CAAA,EACtD,CAACkG,EAAWsJ,CAAY,EAAIxP,WAAS,EAAI,EACzC,CAACiF,EAAOwK,CAAQ,EAAIzP,WAAwB,IAAI,EAGtDC,OAAAA,EAAAA,UAAU,IAAM,EACM,SAAY,CAC1B,GAAA,CACFuP,EAAa,EAAI,EACjBC,EAAS,IAAI,EACPC,MAAAA,EAAY,MAAMC,GAAoBC,eAC5CL,EAAUG,CAAS,QACZ1K,GACCC,QAAAA,MAAM,2DAA4DD,CAAG,EAC7EyK,EAAS,2BAA2B,CAAA,QAC5B,CACRD,EAAa,EAAK,CACpB,CAAA,IAIJ,EAAG,CAAE,CAAA,EA+ME,CACLykB,aA7MgD7xB,EAAAA,QAAQ,IAAM,QAC1DkN,GAAAA,EAAO7L,SAAW,EAAG,CACvB,MAAM/E,EAAW5B,KACV,MAAA,CACLge,SAAU,CAAE,EACZsE,cAAe,CACbjC,YAAaze,EAASzD,OACtBi5B,qBAAsBx1B,EAASvB,UAC/BqiB,cAAe,KACfC,YAAa,KACbP,WAAY,EACZiV,oBAAqB,MACrBC,gBAAiB,GACjBrkB,sBAAuB,4BACvBskB,QAAS,KACX,EACAC,eAAgB,CACdra,YAAa,MACbsa,UAAW,UACX1a,WAAY,EACZ8Z,iBAAkB,IAClBC,gBAAiB,CAAC,uCAAuC,CAC3D,CAAA,EAKJ,MAAMY,EAAgBllB,EAAO4B,OAAO,CAACujB,EAAQ/jB,IAAU,CAC/CqJ,MAAAA,EAAUyY,GAAa9hB,CAAK,EAClC,OAAIqJ,IACG0a,EAAO1a,CAAO,IAAUA,EAAAA,CAAO,EAAI,IACjCA,EAAAA,CAAO,EAAElQ,KAAK6G,CAAK,GAErB+jB,CACT,EAAG,CAAyC,CAAA,EAGtC3Z,EAAWpJ,OAAOW,QAAQmiB,CAAa,EAAEl5B,IAAI,CAAC,CAAC2f,EAAa4C,CAAa,IAC7EoV,GAA0BhY,EAAa4C,CAAa,CACtD,EAGMnf,EAAW5B,KACXqgB,EAAcze,EAASzD,OACvBi5B,EAAuBx1B,EAASvB,UAEhC4hB,EAAiB3hB,KAGjBoiB,EACJ1E,EAASlgB,KAAgBmf,GAAA,CACvB,GAAI,CAACA,EAAQ8Z,UAAkB,MAAA,GAC/B,KAAM,CAACnX,EAAOC,CAAG,EAAI5C,EAAQ8Z,UAAUx4B,MAAM,GAAG,EAC5C,GAAA,CAACqhB,GAAS,CAACC,EAAY,MAAA,GACrB,KAAA,CAACgW,EAAYC,CAAS,EAAIlW,EAAMrhB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACrD,CAACs3B,EAAUC,EAAO,EAAInW,EAAIthB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAC/Cw3B,GAAeJ,EAAa,GAAKC,EACjCI,GAAaH,EAAW,GAAKC,GAC5B/T,OAAAA,GAAkBgU,IAAgBhU,GAAkBiU,EAC5D,CAAA,GAAK,KAGFvT,GAAe,IAAM,CAEnBiV,MAAAA,EAAY5Z,EAASlgB,KAAgBmf,GAAA,CACzC,GAAI,CAACA,EAAQ8Z,UAAkB,MAAA,GAC/B,KAAM,CAACnX,CAAK,EAAI3C,EAAQ8Z,UAAUx4B,MAAM,GAAG,EAC3C,GAAI,CAACqhB,EAAc,MAAA,GACb,KAAA,CAACiW,EAAYC,CAAS,EAAIlW,EAAMrhB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAE3D,OADqBo3B,EAAa,GAAKC,EACjB7T,CAAAA,CACvB,EAEG2V,OAAAA,IAGG5Z,EAASrX,OAAS,EAAIqX,EAAS,CAAC,EAAI,KAAA,KAGvCoE,EACJO,GAAeA,EAAYoU,WACtB,IAAM,CACL,KAAM,CAACnX,CAAK,EAAI+C,EAAYoU,UAAUx4B,MAAM,GAAG,EAC/C,GAAI,CAACqhB,EAAc,MAAA,GACb,KAAA,CAACiW,EAAYC,CAAS,EAAIlW,EAAMrhB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAG3D,IAAIo5B,EAFiBhC,EAAa,GAAKC,EAEb7T,EAG1B,OAAI4V,GAAQ,IACVA,EAAO,GAAK,GAAKA,GAGZA,MAET,EAEAR,EAAsBjV,EAAa,EAAI5hB,GAAmB4hB,CAAU,EAAE/hB,UAAY,MAGlFi3B,EAAkB5U,EACpBA,EAAcK,eAAenc,KAAegV,GAAA,CACpC,KAAA,CAACia,EAAYC,CAAS,EAAIla,EAAOgE,MAAMrhB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EAC5D,CAACs3B,EAAUC,CAAO,EAAIpa,EAAOiE,IAAIthB,MAAM,GAAG,EAAEC,IAAIC,MAAM,EACtDw3B,EAAeJ,EAAa,GAAKC,EACjCI,GAAaH,EAAW,GAAKC,EAC5B/T,OAAAA,GAAkBgU,GAAgBhU,GAAkBiU,EAC5D,CAAA,EACD,GAGJ,IAAIjjB,EAAwB,GACxBskB,EAAqC,MAErC7U,GAAiB4U,GACnBrkB,EAAwB,2BACtByP,EAAcvE,gBACXuE,EAAcpQ,YAAYH,QAAQiM,QAAQ,CAAC,eACtCmZ,EAAA,QACD7U,GACTzP,EAAwB,GAAGyP,EAAcvE,wBAAwBuE,EAAcpB,gBAAgBlO,uBACrFmkB,EAAA,UACD5U,GAAeP,GAAc,IACdnP,EAAA,GAAG0P,EAAYxE,2BAA2BkZ,IACxDE,EAAA,WAEctkB,EAAA,iDACdskB,EAAA,OAIZ,MAAMpa,EAAca,EAAS5J,OAC3B,CAACwN,EAAMtB,IAAaA,EAAQhO,YAAYH,QAAUyP,EAAKtP,YAAYH,QAAUmO,EAAUsB,EACvF5D,EAAS,CAAC,GAAK,CAAEG,YAAa,MAAO7L,YAAa,CAAEH,QAAS,CAAE,CACjE,CAAA,EAAEgM,YAEI2Z,EAAkBtlB,EAAOhL,UAAYyM,EAAEL,MAAMc,aAAe,SAAS,EACrEqjB,EAAiBvlB,EAAOhL,UAAYyM,EAAEL,MAAMc,aAAe,QAAQ,EACnE0M,EACJ0W,EAAgBnxB,OAAS,EACpBmxB,EAAgBtwB,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OACvDmxB,EAAgBnxB,OAClB,IACA,EACA0a,EACJ0W,EAAepxB,OAAS,EACnBoxB,EAAevwB,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EAAEvN,OAASoxB,EAAepxB,OAChF,IACA,EAEA8wB,EAAkCrW,GAAiBC,EAAe,UAAY,SAE9E2W,EAAexlB,EAClBhU,IAAIyV,GAAKA,EAAEL,MAAMiJ,sBAAsB,EACvCrV,OAAQsV,GAAsCA,GAAM,IAAI,EACrDC,EACJib,EAAarxB,OAAS,EAClBqxB,EAAa5jB,OAAO,CAACC,EAAKyI,IAAMzI,EAAMyI,EAAG,CAAC,EAAIkb,EAAarxB,OAC3D,EAEAgwB,EAAmBnkB,EACtBhL,OAAOyM,GAAKA,EAAEL,MAAMM,WAAa,KAAK,EACtC1V,OAASyV,EAAEL,MAAMiJ,sBAAsB,EACvCrV,OAAQsV,GAAsCA,GAAM,IAAI,EACrD8Z,EACJD,EAAiBhwB,OAAS,EACtBgwB,EAAiBviB,OAAO,CAACC,EAAKyI,IAAMzI,EAAMyI,EAAG,CAAC,EAAI6Z,EAAiBhwB,OACnE,IACAkwB,EAAmBn2B,KAAK8f,IAAI,EAAKoW,EAAoB,EAAG,EAExDqB,GAAwB,CAC5B,YAAY9a,gBACVa,GAAAA,EAASlgB,KAAKgY,GAAKA,EAAEqI,cAAgBhB,CAAW,IAAhDa,YAAAA,GAAmD1L,YAAYH,QAAQiM,QAAQ,KAAM,eAEvF,cAAcqZ,aACZA,IAAc,UAAYrW,EAAchD,QAAQ,CAAC,EAAIiD,EAAajD,QAAQ,CAAC,mBAE7E,6BAA6ByY,EAAiBzY,QAAQ,CAAC,4BAA4B,EAG9E,MAAA,CACLJ,SAAAA,EACAsE,cAAe,CACbjC,YAAAA,EACA+W,qBAAAA,EACA1U,cAAAA,EACAC,YAAAA,EACAP,WAAAA,EACAiV,oBAAAA,EACAC,gBAAAA,EACArkB,sBAAAA,EACAskB,QAAAA,CACF,EACAC,eAAgB,CACdra,YAAAA,EACAsa,UAAAA,EACA1a,WAAAA,EACA8Z,iBAAAA,EACAC,gBAAiBmB,EACnB,CAAA,CACF,EACC,CAACzlB,CAAM,CAAC,EAITpJ,UAAAA,EACAjB,MAAAA,EACA4N,QAASA,IAAM,CACbtD,EAAU,CAAE,CAAA,CACd,CAAA,CAEJ,ECteMmhB,GAAmB5xB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGnB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,OAAM,EAG7C0gB,GAA4Bl2B,EAAAA,IAAI+xB,MAAM,CAC1C3vB,UAAW,mCACb,CAAC,EAACnC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,cAAA,CAoCD,EAAA,CAAA,siBAAA,CAAA,EAEKg2B,GAAuBn2B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,cAAA,CAK/B,EAAA,CAAA,mFAAA,CAAA,EAEKi2B,GAAsB1K,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAQ7B,EAAA,CAAA,oHAAA,CAAA,EAEKk2B,GAA2B/1B,EAAAA,KAAIL,WAAA,CAAAC,YAAA,oBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,UAAA,iLAAA,sGAAA,EACrB,CAAC,CAAE8T,MAAAA,CAAM,IACrB,2BAA2BA,EAAMK,OAAOgiB,kBAAkBriB,EAAMK,OAAOiiB,kBAChE,CAAC,CAAEtiB,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YAQf,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,aAAa,EAe3DE,GAA6Bx2B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gKAAA,IAAA,EAAA,EAQtB,CAAC,CAAE6L,MAAAA,EAAOiI,MAAAA,CAAM,IAAM,CAClC,OAAQjI,EAAK,CACX,IAAK,OAEH,MAAO,2BAA2BiI,EAAMK,OAAOgiB,kBAAkBriB,EAAMK,OAAOiiB,kBAChF,IAAK,SAEH,MAAO,2BAA2BtiB,EAAMK,OAAOmiB,mBAAmBxiB,EAAMK,OAAOoiB,qBACjF,IAAK,MAEH,MAAO,2BAA2BziB,EAAMK,OAAOoiB,sBAAsBziB,EAAMK,OAAOqiB,mBACpF,IAAK,QAEH,MAAO,2BAA2B1iB,EAAMK,OAAOsiB,qBAAqB3iB,EAAMK,OAAOnO,SACnF,QACS,MAAA,4BACX,CACF,EAGE,CAAC,CAAE6F,MAAAA,CAAM,IACTA,IAAU,QACV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAYD,EAGG6qB,GAAqB72B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAK7B,EAAA,CAAA,oGAAA,CAAA,EAEK22B,GAAoB92B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAO5B,EAAA,CAAA,6JAAA,CAAA,EAEK42B,GAAqB/2B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wCAAA,qBAAA,EAGnB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,WAAW,EAI5CiiB,GAAqBh3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wBAAA,iDAAA,EAEnB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,EAAMK,OAAOc,aAAa,EAK9C6hB,GAAqBj3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,0BAAA,kKAAA,IAAA,gCAAA,EACd,CAAC,CAAE8T,MAAAA,CAAM,IACrB,2BAA2BA,EAAMK,OAAOgiB,oBAAoBriB,EAAMK,OAAOiiB,oBACvD,CAAC,CAAEtiB,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,cACvB,CAAC,CAAEriB,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,cAe/C,CAAC,CAAEriB,MAAAA,CAAM,IAAMA,EAAMK,OAAOiiB,eAC5B,CAAC,CAAEtiB,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,aAAa,EAM3CY,GAAqBC,EAAAA,GAAEl3B,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,SAAA,uHAAA,KAAA,EAClB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,cAMd,CAAC,CAAEriB,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,aAAa,EAG5Dc,GAAoBC,EAAAA,GAAEp3B,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAI3B,EAAA,CAAA,qCAAA,CAAA,EAEKm3B,GAAoBC,EAAAA,GAAEt3B,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,SAAA,8HAAA,uCAAA,oCAAA,EACjB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,EAAMK,OAAOS,YAU1B,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,cAEd,CAAC,CAAEriB,MAAAA,CAAM,IAAMA,EAAMK,OAAOgiB,aAAa,EAW9DkB,GAAiB1F,EAAO9xB,IAAI+xB,MAChC,CAAC,CAAEha,SAAAA,EAAUwH,UAAAA,CAAU,KAAO,CAC5Bnd,UAAW,iCAAiC2V,EAAW,SAAW,MAChEwH,EAAY,UAAY,KAE1B,qBACExH,GAAYwH,EAAY,iBAAmBxH,EAAW,SAAW,UACrE,EACF,EAAC9X,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAsBA,EAAA,CAAA,ulBAAA,CAAA,EAEKs3B,GAA0Bz3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,mBAAAC,YAAA,eAAA,CAKlC,EAAA,CAAA,mFAAA,CAAA,EAEKu3B,GAAwB13B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,wCAAA,4DAAA,EAGtB,CAAC,CAAE4X,SAAAA,CAAS,IAAOA,EAAW,wBAA0B,6BAA8B,EAO3F4f,GAAsB33B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAG9B,EAAA,CAAA,qDAAA,CAAA,EAEKy3B,GAA4B53B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,eAAA,CAKpC,EAAA,CAAA,0EAAA,CAAA,EAEK03B,GAAmB73B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,eAAA,CAE3B,EAAA,CAAA,oBAAA,CAAA,EAEK23B,GAAwB93B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAIhC,EAAA,CAAA,mEAAA,CAAA,EAEK43B,GAAwB/3B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAKhC,EAAA,CAAA,mGAAA,CAAA,EAEK63B,GAA+Bh4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,wBAAAC,YAAA,eAAA,CAOvC,EAAA,CAAA,qIAAA,CAAA,EAEK83B,GAA4Bj4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,eAAA,CAKpC,EAAA,CAAA,0EAAA,CAAA,EAEK+3B,GAAapG,EAAO9xB,IAAI+xB,MAA8B,CAAC,CAAExS,UAAAA,CAAU,KAAO,CAC9End,UAAW,mCAAmCmd,EAAY,UAAY,IACxE,EAAE,EAACtf,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,sLAAA,EAAA,EASC,CAAC,CAAEof,UAAAA,CAAU,IACbA,GACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,GAMD,EAGG4Y,GAAoBn4B,EAAAA,IAAI+xB,MAAM,CAClC3vB,UAAW,4BACb,CAAC,EAACnC,WAAA,CAAAC,YAAA,aAAAC,YAAA,eAAA,CAID,EAAA,CAAA,mEAAA,CAAA,EAEKi4B,GAA2Bp4B,EAAAA,IAAI+xB,MAAM,CACzC3vB,UAAW,oCACb,CAAC,EAACnC,WAAA,CAAAC,YAAA,oBAAAC,YAAA,eAAA,CAID,EAAA,CAAA,iEAAA,CAAA,EAEKk4B,GAAqBr4B,EAAAA,IAAI+xB,MAAM,CACnC3vB,UAAW,6BACb,CAAC,EAACnC,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAGD,EAAA,CAAA,oDAAA,CAAA,EAIKm4B,GAA4Bt4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,qBAAAC,YAAA,eAAA,CAQpC,EAAA,CAAA,qMAAA,CAAA,EAEKo4B,GAAwBv4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,eAAA,CAOhC,EAAA,CAAA,+HAAA,CAAA,EAEKq4B,GAAsBx4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,eAAA,CAI9B,EAAA,CAAA,6CAAA,CAAA,EAEKs4B,GAAqBz4B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAS7B,EAAA,CAAA,uJAAA,CAAA,EAKYu4B,GAA4CA,CAAC,CACxDtxB,UAAAA,EAAY,GACZjB,MAAAA,EAAQ,KACRyJ,UAAAA,EACAxN,UAAAA,CACF,IAAM,SACE,KAAA,CAAEgF,UAAWuxB,EAAkBxyB,MAAOyyB,GAAmBroB,GAAoB,EAC7E,CACJ4kB,aAAAA,EACA/tB,UAAWyxB,EACX1yB,MAAO2yB,GACL5D,GAA+B,EAE7B5B,EAAUlsB,GAAauxB,GAAoBE,EAC3CtF,EAAeptB,GAASyyB,GAAkBE,EAGhD,OAAIxF,QAEC1E,EAAK,CAAA,MAAM,mBACV,SAAAptB,EAAA,IAAC,OAAI,MAAO,CAAEwoB,QAAS,OAAQD,UAAW,QAAS,EAAE,8CAErD,CACF,CAAA,EAKAwJ,QAEC3E,EAAK,CAAA,MAAM,mBACV,SAAArtB,EAAA,KAAC,OAAI,MAAO,CAAEyoB,QAAS,OAAQD,UAAW,SAAUE,MAAO,oBAAuB,EAAA,SAAA,CAAA,UACxEsJ,EACP3jB,GACCpO,EAAA,IAAC,SACC,CAAA,QAASoO,EACT,MAAO,CACL4jB,WAAY,OACZxJ,QAAS,WACT/T,WAAY,yBACZzB,OAAQ,uCACRC,aAAc,MACdgf,OAAQ,UACRxJ,MAAO,6BAAA,EACP,SAGJ,QAAA,CAAA,CAEJ,CAAA,CACF,CAAA,EAOFzoB,EAAA,IAACotB,EACC,CAAA,MAAM,mCACN,QACEhf,EACGpO,EAAAA,IAAA,SAAA,CACC,QAASoO,EACT,MAAO,CAAEqG,WAAY,OAAQzB,OAAQ,OAAQif,OAAQ,UAAWxJ,MAAO,WAAY,SAGrF,aAAA,EACElnB,OAGN,SAAAxB,EAAAA,KAACqwB,IAAU,UAAAxvB,EAET,SAAA,CAAAb,OAAC20B,GACC,CAAA,SAAA,CAAA30B,OAAC40B,GACC,CAAA,SAAA,CAAA50B,OAAC60B,GACEjB,CAAAA,SAAAA,CAAAA,EAAa7U,cAAcrP,sBAC3BkkB,EAAa7U,cAAcI,eAC1Blf,EAAAA,IAAC60B,IAAkB,SAAI,OAAA,CAAA,EAE3B,EACA70B,EAAAA,IAACg1B,GACC,CAAA,MACErB,EAAa7U,cAAciV,QAAQ7b,YAAY,EAOhDyb,SAAa7U,EAAAA,cAAciV,OAC9B,CAAA,CAAA,EACF,SAECsB,GACC,CAAA,SAAA,CAAAt1B,OAACu1B,GACC,CAAA,SAAA,CAACt1B,EAAAA,IAAAu1B,GAAA,CACC,eAAC90B,GAAgB,CAAA,KAAK,UAAU,OAAO,UAAU,SAAU,EAAA,CAAK,CAClE,CAAA,EACAT,EAAAA,IAACw1B,IAAY,SAAY,cAAA,CAAA,CAAA,EAC3B,SACCF,GACC,CAAA,SAAA,CAAAt1B,MAACu1B,GACE5B,CAAAA,WAAAA,EAAAA,EAAa7U,cAAcI,gBAA3ByU,YAAAA,EAA0ChZ,cAAe,OAC5D,EACA3a,EAAAA,IAACw1B,IAAY,SAAc,gBAAA,CAAA,CAAA,EAC7B,SACCF,GACC,CAAA,SAAA,CAACt1B,EAAA,IAAAu1B,GAAA,CAAa5B,SAAa7U,EAAAA,cAAc+U,oBAAoB,EAC7D7zB,EAAAA,IAACw1B,IAAY,SAAY,cAAA,CAAA,CAAA,EAC3B,CAAA,EACF,CAAA,EACF,EAGC7B,EAAa7U,cAAcI,eAC1Bnf,EAAAA,KAACi2B,IAAe,SAAU,GAAM,UAAW,GACzC,SAAA,CAAAh2B,MAACi2B,GACC,CAAA,SAAAl2B,EAAAA,KAACm2B,GAAe,CAAA,SAAU,GAAK,SAAA,CAAA,8BAE7Bl2B,EAAAA,IAAC60B,IAAkB,SAAY,cAAA,CAAA,CAAA,CAAA,CACjC,CACF,CAAA,EAEA90B,OAAC,OAAI,MAAO,CAAEstB,aAAc,MAC1B,EAAA,SAAA,CAAAttB,OAAC,MACC,MAAO,CACL0oB,MAAO,8BACP4E,aAAc,OACdtsB,SAAU,MAGX4yB,EAAAA,SAAAA,CAAAA,EAAa7U,cAAcI,cAAcvE,YAAY,WAAA,EACxD,EAGA3a,MAAC,OAAI,MAAO,CAAEu3B,QAAS,OAAQC,IAAK,MAAO,EACxC7D,UAAanZ,EAAAA,EAAAA,SACXlgB,KACCgY,GAAAA,OAAKA,OAAAA,EAAEqI,gBAAgBgZ,EAAAA,EAAa7U,cAAcI,gBAA3ByU,YAAAA,EAA0ChZ,aACnE,IAHYH,YAAAA,EAIV+E,eAAevkB,IAAI,CAACod,EAAQ9U,IAC3BvD,OAAA22B,GAAA,CAAuB,UAAWte,EAAOzJ,SAAW,IAAMyJ,EAAOpJ,QAAU,EAC1E,SAAA,CAAAhP,EAAA,IAAC22B,GACC,CAAA,SAAA32B,EAAA,IAACS,GACC,CAAA,KAAK,UACL,aAAc2X,EAAOgE,MACrB,WAAYhE,EAAOiE,IACnB,OAAO,SAAS,CAAA,EAEpB,EACArc,EAAAA,IAAC42B,GAAmBxe,CAAAA,SAAAA,EAAOpM,WAAY,CAAA,EACtChM,EAAA,IAAA62B,GAAA,CACEze,SAAOpJ,EAAAA,OAAS,EACb,GAAGoJ,EAAOzJ,QAAQiM,QAAQ,CAAC,gBAAgBxC,EAAOpJ,iBAClD,UACN,CAAA,GAde1L,CAejB,GAEN,CAAA,EACF,CAAA,EACF,EAIDqwB,EAAanZ,SAASxf,IAAeye,GAAA,OACpC,MAAMlD,IACJod,EAAAA,EAAa7U,cAAcI,gBAA3ByU,YAAAA,EAA0ChZ,eAAgBlB,EAAQkB,YAC9D8c,EAAmBhe,EAAQ8F,eAAenc,KAAU8a,GAAAA,EAAEvP,SAAW,EAAE,EAEzE,OACG5O,EAAAA,KAAAi2B,GAAA,CAEC,SAAAzf,EACA,UAAWkhB,EAEX,SAAA,CAAA13B,OAACk2B,GACC,CAAA,SAAA,CAAAl2B,EAAAA,KAACm2B,IAAe,SAAA3f,EACbkD,SAAAA,CAAQkB,EAAAA,YACRpE,GAAavW,EAAAA,IAAA60B,GAAA,CAAkB,SAAM,QAAA,CAAA,CAAA,EACxC,EACC70B,EAAA,IAAAm2B,GAAA,CACE1c,SAAQ8Z,EAAAA,UACNvzB,EAAAA,IAAAS,GAAA,CACC,KAAK,UACL,aAAcgZ,EAAQ8Z,UAAUx4B,MAAM,GAAG,EAAE,CAAC,EAC5C,WAAY0e,EAAQ8Z,UAAUx4B,MAAM,GAAG,EAAE,CAAC,EAC1C,OAAO,SACP,CAAA,EAEDiF,EAAAA,IAAA,OAAA,CAAK,mCAAwB,CAAA,EAElC,CAAA,EACF,SAECo2B,GACC,CAAA,SAAA,CAAAr2B,OAACs2B,GACC,CAAA,SAAA,CAACr2B,EAAA,IAAAs2B,GAAA,CAAgB7c,SAAQ3K,EAAAA,YAAY2C,YAAY,EACjDzR,EAAAA,IAACu2B,IAAe,SAAM,QAAA,CAAA,CAAA,EACxB,SACCF,GACC,CAAA,SAAA,CAAAt2B,OAACu2B,GAAgB7c,CAAAA,SAAAA,CAAQ3K,EAAAA,YAAYH,QAAQiM,QAAQ,CAAC,EAAE,GAAA,EAAC,EACzD5a,EAAAA,IAACu2B,IAAe,SAAQ,UAAA,CAAA,CAAA,EAC1B,SACCF,GACC,CAAA,SAAA,CAAAt2B,OAACu2B,GAAgB7c,CAAAA,SAAAA,CAAQ3K,EAAAA,YAAY6B,aAAaiK,QAAQ,CAAC,EAAE,GAAA,EAAC,EAC9D5a,EAAAA,IAACu2B,IAAe,SAAc,gBAAA,CAAA,CAAA,EAChC,SACCF,GACC,CAAA,SAAA,CAAAr2B,MAACs2B,IAAgB7c,SAAQ3K,EAAAA,YAAYgkB,QAAQlY,QAAQ,CAAC,EAAE,EACxD5a,EAAAA,IAACu2B,IAAe,SAAc,gBAAA,CAAA,CAAA,EAChC,CAAA,EACF,SAGC,MACC,CAAA,SAAA,CAAAv2B,EAAAA,IAACw2B,IAAsB,SAAoB,sBAAA,CAAA,QAC1CC,GACEhd,CAAAA,SAAAA,EAAQ8F,eAAevkB,IAAI,CAACod,EAAQ9U,IAClCvD,EAAAA,KAAA22B,GAAA,CAAuB,UAAWte,EAAOzJ,SAAW,IAAMyJ,EAAOpJ,QAAU,EAC1E,SAAA,CAAAhP,EAAA,IAAC22B,GACC,CAAA,SAAA32B,EAAA,IAACS,GACC,CAAA,KAAK,UACL,aAAc2X,EAAOgE,MACrB,WAAYhE,EAAOiE,IACnB,OAAO,SAAS,CAAA,EAEpB,EACArc,EAAAA,IAAC42B,GAAmBxe,CAAAA,SAAAA,EAAOpM,WAAY,CAAA,EACtChM,EAAA,IAAA62B,GAAA,CACEze,SAAOpJ,EAAAA,OAAS,EACb,GAAGoJ,EAAOzJ,QAAQiM,QAAQ,CAAC,OAAOxC,EAAOpJ,iBACzC,UACN,CAAA,GAde1L,CAejB,CACD,EACH,CAAA,EACF,EAGCmW,EAAQ6Z,gBAAgBnwB,OAAS,UAC/BsyB,GACC,CAAA,SAAA,CAAAz1B,EAAAA,IAAC01B,IAAY,SAAuB,yBAAA,CAAA,EACnC11B,EAAA,IAAA41B,GAAA,CACEnc,SAAQ6Z,EAAAA,gBAAgBt4B,IAAI,CAAC08B,EAAKp0B,IAChCtD,EAAA,IAAA81B,GAAA,CAAwB4B,SAARp0B,CAAAA,EAAAA,CAAY,CAC9B,EACH,CAAA,EACF,CAAA,GA5EGmW,EAAQkB,WA8Ef,CAAA,CAEH,SAGAmc,GACC,CAAA,SAAA,CAAA92B,EAAAA,IAAC+2B,IAAe,SAA8B,gCAAA,CAAA,SAC7CC,GACC,CAAA,SAAA,CAAAj3B,OAACk3B,GACC,CAAA,SAAA,CAAAj3B,EAAAA,IAAC,UAAO,SAAa,eAAA,CAAA,EAAS,IAAE2zB,EAAaK,eAAera,WAAAA,EAC9D,SACCsd,GACC,CAAA,SAAA,CAAAj3B,EAAAA,IAAC,UAAO,SAAW,aAAA,CAAA,EAAS,IAAE2zB,EAAaK,eAAeC,SAAAA,EAC5D,SACCgD,GACC,CAAA,SAAA,CAAAj3B,EAAAA,IAAC,UAAO,SAAgB,kBAAA,CAAA,EAAS,IAAE2zB,EAAaK,eAAeza,WAAWqB,QAAQ,CAAC,EAAC,MAAA,EAEtF,SACCqc,GACC,CAAA,SAAA,CAAAj3B,EAAAA,IAAC,UAAO,SAAkB,oBAAA,CAAA,EAAU,IACnC,IAAI2zB,EAAaK,eAAeX,iBAAiBzY,QAAQ,CAAC,IAAI,0BAAA,EAEjE,CAAA,EACF,EACA7a,OAAC01B,IAAY,MAAO,CAAEkC,UAAW,MAC/B,EAAA,SAAA,CAAA33B,EAAAA,IAAC01B,IAAY,SAAmB,qBAAA,CAAA,EAC/B11B,EAAA,IAAA41B,GAAA,CACEjC,SAAaK,EAAAA,eAAeV,gBAAgBt4B,IAAI,CAAC08B,EAAKp0B,IACpDtD,EAAAA,IAAA81B,GAAA,CAAwB4B,SAARp0B,GAAAA,CAAY,CAC9B,EACH,CAAA,EACF,CAAA,EACF,CAAA,CACF,CAAA,CACF,CAAA,CAEJ,ECxpBMguB,GAAoB9yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,kBAAA,qBAAA,GAAA,EAKhB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QAGjC,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBACrC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OACtC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,wBAAuB,EAG9Eue,GAAmB/yB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,gCAAA,eAAA,EAET,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,OAAM,EAIvDwd,GAAoBtH,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WACvC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,MAAK,EAGnD4d,GAAsBC,EAAAA,EAACjzB,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YAC1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,wBAAuB,EAK1EgkB,GAAuBp5B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAG7CklB,GAAkBr5B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,YAAA,kDAAA,4EAAA,EACX,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcM,UAAW,uBAClC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcO,SAAU,yBAC1C,CAAC,CAAEP,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAC/C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAI7B,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,uBAAsB,EAM5E4kB,GAAmB5N,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACZ,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WACvC,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,MAAK,EAGnDkkB,GAAkBj5B,EAAAA,KAAIL,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,mDAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBgB,KAAM,WAC1C,CAAC,CAAEhB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,wBAAuB,EAK1EokB,GAAqBtG,EAAAA,EAACjzB,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,WAAA,yBAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YAC1C,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,yBAC7C,CAAC,CAAEnB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,MAAK,EAO/CokB,GAAqDA,CAAC,CAAEryB,UAAAA,EAAWjB,MAAAA,EAAOuzB,SAAAA,CAAS,IACnFvzB,SAEC2sB,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAE,IAAA,CAAA,EACbvxB,EAAAA,IAACwxB,IAAW,SAA+B,iCAAA,CAAA,EAC3CxxB,EAAAA,IAACyxB,IAAc9sB,SAAMA,CAAA,CAAA,CACvB,CAAA,CAAA,QAIIuyB,GAAa,CAAA,UAAAtxB,EAAsB,MAAAjB,EAAc,UAAWuzB,EAAS9pB,SAAa,CAAA,EAMtF+pB,GAAiDA,CAAC,CAAEvyB,UAAAA,EAAWjB,MAAAA,EAAOuzB,SAAAA,CAAS,IAC/EvzB,SAEC2sB,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAE,IAAA,CAAA,EACbvxB,EAAAA,IAACwxB,IAAW,SAAoC,sCAAA,CAAA,EAChDxxB,EAAAA,IAACyxB,IAAc9sB,SAAMA,CAAA,CAAA,CACvB,CAAA,CAAA,QAKDynB,GAAwB,CAAA,UAAAxmB,EAAsB,MAAAjB,EAAc,UAAWuzB,EAAS9pB,SAAa,CAAA,EAO5FgqB,GAAmDA,CAAC,CAAExyB,UAAAA,EAAWjB,MAAAA,EAAOuzB,SAAAA,CAAS,IACjFvzB,SAEC2sB,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAE,IAAA,CAAA,EACbvxB,EAAAA,IAACwxB,IAAW,SAAmC,qCAAA,CAAA,EAC/CxxB,EAAAA,IAACyxB,IAAc9sB,SAAMA,CAAA,CAAA,CACvB,CAAA,CAAA,QAIIgtB,GAAc,CAAA,UAAA/rB,EAAsB,MAAAjB,EAAc,UAAWuzB,EAAS9pB,SAAa,CAAA,EAMvFiqB,GAAiDA,CAAC,CAAEzyB,UAAAA,CAAU,IAAM,CACxE,GAAIA,EACF,cACG0rB,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAE,IAAA,CAAA,EACbvxB,EAAAA,IAACwxB,IAAW,SAAmB,qBAAA,CAAA,EAC/BxxB,EAAAA,IAACyxB,IAAa,SAAqC,uCAAA,CAAA,CACrD,CAAA,CAAA,EAKJ,MAAM6G,EAAW,CACf,CACElyB,GAAI,EACJqF,MAAO,2CACPC,KAAM,cACN6sB,QACE,4IAAA,EAEJ,CACEnyB,GAAI,EACJqF,MAAO,+CACPC,KAAM,cACN6sB,QACE,8IAAA,EAEJ,CACEnyB,GAAI,EACJqF,MAAO,+CACPC,KAAM,cACN6sB,QACE,mHAAA,CACH,EAGH,aACGX,GACEU,CAAAA,SAAAA,EAASt9B,IAAI4P,UACXitB,GACC,CAAA,SAAA,CAAC73B,EAAAA,IAAA83B,GAAA,CAAWltB,WAAKa,KAAM,CAAA,EACvBzL,EAAAA,IAAC+3B,GAAUntB,CAAAA,SAAAA,EAAKc,IAAK,CAAA,EACrB1L,EAAAA,IAACg4B,GAAaptB,CAAAA,SAAAA,EAAK2tB,OAAQ,CAAA,CAHd3tB,CAAAA,EAAAA,EAAKxE,EAIpB,CACD,CACH,CAAA,CAEJ,EAKaoyB,GAAqD,CAChE3wB,SAAU,CACRzB,GAAI,WACJqF,MAAO,gBACPO,YAAa,6DACb4J,KAAM,KACN6iB,UAAWR,GACXS,aAAc,GACdC,aAAc,EAChB,EACA9iB,KAAM,CACJzP,GAAI,OACJqF,MAAO,qBACPO,YACE,gGACF4J,KAAM,KACN6iB,UAAWN,GACXO,aAAc,GACdC,aAAc,EAChB,EACA5wB,OAAQ,CACN3B,GAAI,SACJqF,MAAO,kBACPO,YAAa,uEACb4J,KAAM,KACN6iB,UAAWL,GACXM,aAAc,GACdC,aAAc,EAChB,EACA/tB,KAAM,CACJxE,GAAI,OACJqF,MAAO,cACPO,YAAa,yCACb4J,KAAM,KACN6iB,UAAWJ,GACXK,aAAc,GACdC,aAAc,EAChB,CACF,EAKaC,GAAgBC,GACpBL,GAAiBK,CAAK,EA2BlBC,GAAmEp4B,GAAA,CACxE,KAAA,CAAEqV,UAAAA,CAAcrV,EAAAA,EAChB4V,EAASsiB,GAAa7iB,CAAS,EAErC,GAAI,CAACO,EACH,cACGgb,GACC,CAAA,SAAA,CAAAtxB,EAAAA,IAACuxB,IAAU,SAAC,GAAA,CAAA,EACZvxB,EAAAA,IAACwxB,IAAW,SAAW,aAAA,CAAA,SACtBC,GAAa,CAAA,SAAA,CAAA,QAAM1b,EAAU,cAAA,EAAY,CAC5C,CAAA,CAAA,EAIJ,MAAMgjB,EAAeziB,EAAOmiB,UAE5B,OACGz4B,EAAA,IAAA,MAAA,CAAI,GAAI,eAAe+V,IAAa,KAAK,WAAW,kBAAiB,aAAaA,IACjF,SAAA/V,EAAAA,IAAC+4B,EAAa,CAAA,GAAIr4B,EAAM,CAC1B,CAAA,CAEJ,EC3SM0vB,GAAmB5xB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,eAAA,UAAA,6BAAA,kCAAA,EAGnB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC7B,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcgC,aAAc,qBAChD,CAAC,CAAEhC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcc,cAAe,WAE1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAKjDqmB,GAAqBx6B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,UAAA,EAGrB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAI7CsmB,GAA6Bz6B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,cAAA,CAarC,EAAA,CAAA,wIAAA,CAAA,EAEKstB,GAAsBztB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,sCAAA,EAKlB,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,OAAM,EAKjDsmB,GAAqB16B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,mGAAA,EAEX,CAAC,CAAE8T,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,OAAM,EAUvDmlB,GAAqBzH,EAAAA,EAACjzB,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,YAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,wBAAuB,EAI1EsY,GAAoB1tB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,uBAAA,oBAAA,WAAA,KAAA,EAKhB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeG,KAAM,QAGjC,CAAC,CAAEH,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAc9N,QAAS,sBAChC,CAAC,CAAE8N,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAc9N,QAAS,sBACzC,CAAC,CAAE8N,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBE,KAAM,OAChD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAGhDymB,GAAmB56B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,UAAA,GAAA,EAET,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC5C,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAc9N,QAAS,qBAAoB,EAG/D00B,GAAoBnP,EAAAA,GAAEzrB,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAc9N,QAAS,sBACjC,CAAC,CAAE8N,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,MAAK,EAGnDylB,GAAsB5H,EAAAA,EAACjzB,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMY,YAANZ,YAAAA,EAAiBoB,KAAM,YAC1C,CAAC,CAAEpB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcmB,gBAAiB,wBAAuB,EAK1E2lB,GAAqB5kB,EAAAA,OAAMlW,WAAA,CAAAC,YAAA,cAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,eAAA,0CAAA,+EAAA,+BAAA,EACjB,CAAC,CAAE8T,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QACvC,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeoB,KAAM,OAAS,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeuB,KAAM,QAC9E,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcS,UAAW,wBAGrC,CAAC,CAAET,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMQ,eAANR,YAAAA,EAAoBuB,KAAM,OAM1C,CAAC,CAAEvB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMK,SAANL,YAAAA,EAAcqC,cAAe,sBAAqB,EAQ7E0kB,GAA4BA,IAChCz5B,EAAAA,KAACksB,GACC,CAAA,SAAA,CAAAjsB,EAAAA,IAACk5B,IAAY,SAAE,IAAA,CAAA,EACfl5B,EAAAA,IAACm5B,IAAY,SAAsB,wBAAA,CAAA,CAAA,CACrC,CAAA,EAMIM,GAAkEA,CAAC,CAAE90B,MAAAA,EAAO+0B,QAAAA,CAAQ,WACvFxN,GACC,CAAA,SAAA,CAAAlsB,EAAAA,IAACo5B,IAAU,SAAE,IAAA,CAAA,EACbp5B,EAAAA,IAACq5B,IAAW,SAAW,aAAA,CAAA,EACvBr5B,EAAAA,IAACs5B,IAAc30B,SAAMA,CAAA,CAAA,EACpB3E,EAAA,IAAAu5B,GAAA,CAAY,QAASG,EAAQ,SAE9B,YAAA,CAAA,CACF,CAAA,EAMIC,GAAgDA,CAAC,CAAEC,WAAAA,EAAYnuB,MAAAA,CAAM,IAAM,CACzE,KAAA,CACJ5F,aAAAA,EACAN,eAAAA,EACAC,YAAAA,EACAC,eAAAA,EACAG,UAAAA,EACAjB,MAAAA,EACAqI,YAAAA,EACAgB,aAAAA,EACAC,wBAAAA,EACAC,qBAAAA,EACAC,wBAAAA,EACAC,UAAAA,GACE1B,GAAc,EAEZ,CAAEqJ,UAAAA,EAAWkB,aAAAA,GAAiBF,GAAmB,CACrDH,WAAYgjB,GAAc,UAAA,CAC3B,EAGKC,EAAkB,CACtB9jB,UAAAA,EACAzQ,KAAM,CACJC,eAAAA,EACAC,YAAAA,EACAC,eAAAA,EACAI,aAAAA,EACAmH,YAAAA,CACF,EACApH,UAAAA,EACAjB,MAAAA,EACAuzB,SAAU,CACRlqB,aAAAA,EACAC,wBAAAA,EACAC,qBAAAA,EACAC,wBAAAA,EACAC,UAAAA,CACF,CAAA,EAGF,OAAIzJ,EACM3E,EAAAA,IAAAy5B,GAAA,CAAc,MAAA90B,EAAc,QAASyJ,CAAa,CAAA,SAIzDgiB,GAEC,CAAA,SAAA,CAAApwB,MAAC+U,IACC,UAAAnP,EACA,YAAAoH,EACA,aAAAnH,EACA,aAAAmI,EACA,UAAAI,EACA,MAAA3C,EAAa,QAIdqK,GACC,CAAA,UAAAC,EACA,YAAakB,EACb,SAAUrR,EAAU,QAIrBozB,GACC,CAAA,SAAAh5B,MAACi5B,GACC,CAAA,SAAAj5B,EAAA,IAAC85B,YAAS,SAAU95B,EAAAA,IAACw5B,GAAe,EAAA,EAClC,eAACV,GAAwB,CAAA,GAAIe,EAAgB,CAAA,CAC/C,CACF,CAAA,EACF,CACF,CAAA,CAAA,CAEJ,EAWaE,GAA+Dr5B,GAExEV,EAAAA,IAAC85B,EAAAA,SAAS,CAAA,SAAW95B,EAAA,IAAAw5B,GAAA,CAAe,CAAA,EAClC,SAACx5B,EAAAA,IAAA25B,GAAA,CAAa,GAAIj5B,CAAM,CAAA,CAC1B,CAAA,ECnOSs5B,GAAwCA,CAAC,CAAEp5B,UAAAA,EAAWg5B,WAAAA,EAAYnuB,MAAAA,CAAM,UAEhF1E,GACC,CAAA,SAAA/G,EAAAA,IAAC+5B,IAAiB,UAAAn5B,EAAsB,WAAAg5B,EAAwB,MAAAnuB,CAAa,CAAA,CAC/E,CAAA"}