import{j as r}from"./client-d6fc67cc.js";import{C as n,s as e}from"./styled-components-00fe3932.js";const y={none:n(["padding:0;"]),small:n(["padding:",";"],({theme:o})=>o.spacing.sm),medium:n(["padding:",";"],({theme:o})=>o.spacing.md),large:n(["padding:",";"],({theme:o})=>o.spacing.lg)},v={default:n(["background-color:",";"],({theme:o})=>o.colors.surface),primary:n(["background-color:","10;border-color:","30;"],({theme:o})=>o.colors.primary,({theme:o})=>o.colors.primary),secondary:n(["background-color:","10;border-color:","30;"],({theme:o})=>o.colors.secondary,({theme:o})=>o.colors.secondary),outlined:n(["background-color:transparent;border:1px solid ",";"],({theme:o})=>o.colors.border),elevated:n(["background-color:",";box-shadow:",";border:none;"],({theme:o})=>o.colors.surface,({theme:o})=>o.shadows.md)},u=e.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:o})=>o.borderRadius.md,({theme:o})=>o.transitions.fast,({bordered:o,theme:s})=>o&&n(["border:1px solid ",";"],s.colors.border),({padding:o})=>y[o],({variant:o})=>v[o],({clickable:o})=>o&&n(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:s})=>s.shadows.md)),w=e.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:o})=>o.spacing.md),j=e.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),I=e.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:o})=>o.fontSizes.lg,({theme:o})=>o.fontWeights.semibold,({theme:o})=>o.colors.textPrimary),N=e.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:o})=>o.spacing.xs,({theme:o})=>o.fontSizes.sm,({theme:o})=>o.colors.textSecondary),k=e.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:o})=>o.spacing.sm),S=e.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),z=e.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:o})=>o.spacing.md,({theme:o})=>o.spacing.md,({theme:o})=>o.colors.border),H=e.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:o})=>`${o.colors.background}80`),L=e.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:o})=>o.spacing.md,({theme:o})=>o.colors.error,({theme:o})=>o.borderRadius.sm,({theme:o})=>o.colors.error,({theme:o})=>o.spacing.md),A=e.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:o})=>o.colors.background,({theme:o})=>o.colors.primary),F=({children:o,title:s="",subtitle:i="",bordered:c=!0,variant:l="default",padding:m="medium",className:p="",footer:a,actions:d,isLoading:g=!1,hasError:f=!1,errorMessage:C="An error occurred",clickable:t=!1,onClick:h,...x})=>{const b=s||i||d;return r.jsxs(u,{bordered:c,variant:l,padding:m,clickable:t,className:p,onClick:t?h:void 0,...x,children:[g&&r.jsx(H,{children:r.jsx(A,{})}),b&&r.jsxs(w,{children:[r.jsxs(j,{children:[s&&r.jsx(I,{children:s}),i&&r.jsx(N,{children:i})]}),d&&r.jsx(k,{children:d})]}),f&&r.jsx(L,{children:r.jsx("p",{children:C})}),r.jsx(S,{children:o}),a&&r.jsx(z,{children:a})]})};export{F as C};
//# sourceMappingURL=Card-1e58b487.js.map
