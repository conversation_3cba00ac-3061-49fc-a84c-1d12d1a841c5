{"version": 3, "file": "Settings-ff0bb2e7.js", "sources": ["../../src/features/settings/components/SettingsHeader.tsx", "../../src/features/settings/components/SettingsFormField.tsx", "../../src/features/settings/components/SettingsForm.tsx", "../../src/features/settings/hooks/useSettingsForm.ts", "../../src/features/settings/components/SettingsContainer.tsx", "../../src/features/settings/Settings.tsx"], "sourcesContent": ["/**\n * SettingsHeader Component\n * \n * REFACTORED FROM: Settings.tsx (271 lines → focused components)\n * F1 racing-themed header for the settings page.\n * \n * BENEFITS:\n * - Focused responsibility (header only)\n * - F1 racing theme with red accents\n * - Reusable across different settings contexts\n * - Consistent with other F1Header components\n * - Better separation of concerns\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport interface SettingsHeaderProps {\n  /** Custom className */\n  className?: string;\n  /** Whether settings have unsaved changes */\n  hasUnsavedChanges?: boolean;\n  /** Save handler */\n  onSave?: () => void;\n  /** Reset handler */\n  onReset?: () => void;\n}\n\nconst HeaderContainer = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n  border-bottom: 2px solid ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  margin-bottom: ${({ theme }) => theme.spacing?.lg || '24px'};\n  position: relative;\n  \n  /* F1 Racing accent line */\n  &::after {\n    content: '';\n    position: absolute;\n    bottom: -2px;\n    left: 0;\n    width: 60px;\n    height: 4px;\n    background: linear-gradient(\n      90deg,\n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,\n      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}80 50%,\n      transparent 100%\n    );\n    border-radius: 2px;\n  }\n`;\n\nconst TitleSection = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n`;\n\nconst Title = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0;\n  letter-spacing: -0.025em;\n  \n  /* F1 Racing style */\n  text-transform: uppercase;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;\n`;\n\nconst Subtitle = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  font-weight: 400;\n`;\n\nconst ActionsSection = styled.div`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n`;\n\nconst StatusIndicator = styled.div<{ $hasChanges: boolean }>`\n  display: flex;\n  align-items: center;\n  gap: ${({ theme }) => theme.spacing?.xs || '4px'};\n  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n  \n  ${({ $hasChanges, theme }) =>\n    $hasChanges\n      ? `\n        background: ${theme.colors?.warning || 'var(--warning-color)'}20;\n        color: ${theme.colors?.warning || 'var(--warning-color)'};\n        border: 1px solid ${theme.colors?.warning || 'var(--warning-color)'}40;\n      `\n      : `\n        background: ${theme.colors?.success || 'var(--success-color)'}20;\n        color: ${theme.colors?.success || 'var(--success-color)'};\n        border: 1px solid ${theme.colors?.success || 'var(--success-color)'}40;\n      `}\n`;\n\nconst StatusDot = styled.div<{ $hasChanges: boolean }>`\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background: ${({ $hasChanges, theme }) =>\n    $hasChanges \n      ? theme.colors?.warning || 'var(--warning-color)'\n      : theme.colors?.success || 'var(--success-color)'};\n  animation: ${({ $hasChanges }) => $hasChanges ? 'pulse 2s infinite' : 'none'};\n  \n  @keyframes pulse {\n    0%, 100% { opacity: 1; }\n    50% { opacity: 0.5; }\n  }\n`;\n\nconst ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: 1px solid;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  min-width: 80px;\n  \n  ${({ $variant, theme }) =>\n    $variant === 'primary'\n      ? `\n        background: ${theme.colors?.primary || 'var(--primary-color)'};\n        color: white;\n        border-color: ${theme.colors?.primary || 'var(--primary-color)'};\n        \n        &:hover:not(:disabled) {\n          background: ${theme.colors?.primaryDark || 'var(--primary-dark)'};\n          border-color: ${theme.colors?.primaryDark || 'var(--primary-dark)'};\n          transform: translateY(-1px);\n          box-shadow: 0 4px 8px ${theme.colors?.primary || 'var(--primary-color)'}40;\n        }\n      `\n      : `\n        background: transparent;\n        color: ${theme.colors?.textSecondary || 'var(--text-secondary)'};\n        border-color: ${theme.colors?.border || 'var(--border-primary)'};\n        \n        &:hover:not(:disabled) {\n          color: ${theme.colors?.textPrimary || '#ffffff'};\n          border-color: ${theme.colors?.textPrimary || '#ffffff'};\n          background: ${theme.colors?.surface || 'var(--bg-secondary)'};\n        }\n      `}\n  \n  &:active:not(:disabled) {\n    transform: translateY(0);\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\n/**\n * SettingsHeader Component\n * \n * PATTERN: F1 Header Pattern\n * - Racing-inspired styling with red accents\n * - Status indicators with animations\n * - Action buttons with hover effects\n * - Consistent with F1 design system\n * - Accessible and responsive\n */\nexport const SettingsHeader: React.FC<SettingsHeaderProps> = ({\n  className,\n  hasUnsavedChanges = false,\n  onSave,\n  onReset,\n}) => {\n  return (\n    <HeaderContainer className={className}>\n      <TitleSection>\n        <Title>Settings</Title>\n        <Subtitle>\n          Configure your trading dashboard preferences and system settings\n        </Subtitle>\n      </TitleSection>\n      \n      <ActionsSection>\n        <StatusIndicator $hasChanges={hasUnsavedChanges}>\n          <StatusDot $hasChanges={hasUnsavedChanges} />\n          {hasUnsavedChanges ? 'Unsaved Changes' : 'All Saved'}\n        </StatusIndicator>\n        \n        {hasUnsavedChanges && (\n          <>\n            <ActionButton\n              $variant=\"secondary\"\n              onClick={onReset}\n              title=\"Reset to last saved state\"\n            >\n              Reset\n            </ActionButton>\n            \n            <ActionButton\n              $variant=\"primary\"\n              onClick={onSave}\n              title=\"Save current settings\"\n            >\n              Save\n            </ActionButton>\n          </>\n        )}\n      </ActionsSection>\n    </HeaderContainer>\n  );\n};\n\nexport default SettingsHeader;\n", "/**\n * SettingsFormField Component\n * \n * REFACTORED FROM: Settings.tsx (271 lines → focused components)\n * Reusable form field component with F1 racing theme.\n * \n * BENEFITS:\n * - Focused responsibility (single form field)\n * - Reusable across different settings\n * - F1 racing theme with consistent styling\n * - Built-in validation and error handling\n * - Accessible form controls\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\n\nexport type FieldType = 'text' | 'number' | 'select' | 'toggle' | 'textarea';\n\nexport interface FieldOption {\n  value: string | number;\n  label: string;\n}\n\nexport interface SettingsFormFieldProps {\n  /** Field identifier */\n  name: string;\n  /** Field label */\n  label: string;\n  /** Field description */\n  description?: string;\n  /** Field type */\n  type: FieldType;\n  /** Current value */\n  value: any;\n  /** Change handler */\n  onChange: (name: string, value: any) => void;\n  /** Options for select fields */\n  options?: FieldOption[];\n  /** Input props for text/number fields */\n  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;\n  /** Validation error */\n  error?: string;\n  /** Whether field is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst FieldContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.md || '12px'};\n  padding: ${({ theme }) => theme.spacing?.md || '12px'} 0;\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  \n  &:last-child {\n    border-bottom: none;\n  }\n`;\n\nconst FieldRow = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  gap: ${({ theme }) => theme.spacing?.lg || '24px'};\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: stretch;\n    gap: ${({ theme }) => theme.spacing?.md || '12px'};\n  }\n`;\n\nconst LabelSection = styled.div`\n  flex: 1;\n  min-width: 0;\n`;\n\nconst FieldLabel = styled.label`\n  display: block;\n  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin-bottom: ${({ theme }) => theme.spacing?.xs || '4px'};\n  cursor: pointer;\n`;\n\nconst FieldDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  line-height: 1.4;\n`;\n\nconst ControlSection = styled.div`\n  flex-shrink: 0;\n  min-width: 120px;\n  \n  @media (max-width: 768px) {\n    min-width: 0;\n  }\n`;\n\nconst Input = styled.input<{ $hasError?: boolean }>`\n  width: 100%;\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasError }) => \n    $hasError \n      ? theme.colors?.error || 'var(--error-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  transition: all 0.2s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\nconst Select = styled.select<{ $hasError?: boolean }>`\n  width: 100%;\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  border: 1px solid ${({ theme, $hasError }) => \n    $hasError \n      ? theme.colors?.error || 'var(--error-color)'\n      : theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:focus {\n    outline: none;\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  }\n  \n  &:disabled {\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n`;\n\nconst ToggleContainer = styled.label`\n  position: relative;\n  display: inline-block;\n  width: 52px;\n  height: 28px;\n  cursor: pointer;\n`;\n\nconst ToggleInput = styled.input`\n  opacity: 0;\n  width: 0;\n  height: 0;\n  \n  &:checked + span {\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  }\n  \n  &:checked + span:before {\n    transform: translateX(24px);\n  }\n  \n  &:focus + span {\n    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;\n  }\n`;\n\nconst ToggleSlider = styled.span`\n  position: absolute;\n  cursor: pointer;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  transition: all 0.3s ease;\n  border-radius: 28px;\n  \n  &:before {\n    position: absolute;\n    content: '';\n    height: 20px;\n    width: 20px;\n    left: 4px;\n    bottom: 4px;\n    background: white;\n    transition: all 0.3s ease;\n    border-radius: 50%;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  }\n`;\n\nconst ErrorMessage = styled.div`\n  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  margin-top: ${({ theme }) => theme.spacing?.xs || '4px'};\n  font-weight: 500;\n`;\n\n/**\n * SettingsFormField Component\n * \n * PATTERN: F1 Form Field Pattern\n * - Racing-inspired styling with red accents\n * - Consistent form controls across field types\n * - Built-in validation and error states\n * - Accessible with proper labels and focus\n * - Responsive design for mobile\n */\nexport const SettingsFormField: React.FC<SettingsFormFieldProps> = ({\n  name,\n  label,\n  description,\n  type,\n  value,\n  onChange,\n  options = [],\n  inputProps = {},\n  error,\n  disabled = false,\n  className,\n}) => {\n  const fieldId = `settings-field-${name}`;\n  \n  const handleChange = (newValue: any) => {\n    if (!disabled) {\n      onChange(name, newValue);\n    }\n  };\n  \n  const renderControl = () => {\n    switch (type) {\n      case 'text':\n      case 'number':\n        return (\n          <Input\n            id={fieldId}\n            type={type}\n            value={value || ''}\n            onChange={(e) => handleChange(\n              type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value\n            )}\n            disabled={disabled}\n            $hasError={!!error}\n            {...inputProps}\n          />\n        );\n        \n      case 'select':\n        return (\n          <Select\n            id={fieldId}\n            value={value || ''}\n            onChange={(e) => handleChange(e.target.value)}\n            disabled={disabled}\n            $hasError={!!error}\n          >\n            {options.map((option) => (\n              <option key={option.value} value={option.value}>\n                {option.label}\n              </option>\n            ))}\n          </Select>\n        );\n        \n      case 'toggle':\n        return (\n          <ToggleContainer>\n            <ToggleInput\n              id={fieldId}\n              type=\"checkbox\"\n              checked={!!value}\n              onChange={(e) => handleChange(e.target.checked)}\n              disabled={disabled}\n            />\n            <ToggleSlider />\n          </ToggleContainer>\n        );\n        \n      default:\n        return null;\n    }\n  };\n  \n  return (\n    <FieldContainer className={className}>\n      <FieldRow>\n        <LabelSection>\n          <FieldLabel htmlFor={fieldId}>\n            {label}\n          </FieldLabel>\n          {description && (\n            <FieldDescription>\n              {description}\n            </FieldDescription>\n          )}\n        </LabelSection>\n        \n        <ControlSection>\n          {renderControl()}\n        </ControlSection>\n      </FieldRow>\n      \n      {error && (\n        <ErrorMessage role=\"alert\">\n          {error}\n        </ErrorMessage>\n      )}\n    </FieldContainer>\n  );\n};\n\nexport default SettingsFormField;\n", "/**\n * SettingsForm Component\n *\n * REFACTORED FROM: Settings.tsx (271 lines → focused components)\n * F1 racing-themed form with organized sections and validation.\n *\n * BENEFITS:\n * - Focused responsibility (form logic only)\n * - Organized sections with clear hierarchy\n * - F1 racing theme with consistent styling\n * - Built-in validation and error handling\n * - Reusable form field components\n */\n\nimport React from 'react';\nimport styled from 'styled-components';\nimport { SettingsFormField, FieldOption } from './SettingsFormField';\n\nexport interface SettingsFormData {\n  theme: string;\n  refreshInterval: number;\n  showNotifications: boolean;\n  enableAdvancedMetrics: boolean;\n  autoSaveJournal: boolean;\n}\n\nexport interface SettingsFormProps {\n  /** Form data */\n  data: SettingsFormData;\n  /** Change handler */\n  onChange: (name: string, value: any) => void;\n  /** Validation errors */\n  errors?: Record<string, string>;\n  /** Whether form is disabled */\n  disabled?: boolean;\n  /** Custom className */\n  className?: string;\n}\n\nconst FormContainer = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: ${({ theme }) => theme.spacing?.xl || '32px'};\n`;\n\nconst FormSection = styled.div`\n  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};\n  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  overflow: hidden;\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n  transition: all 0.2s ease;\n\n  &:hover {\n    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}40;\n    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);\n  }\n`;\n\nconst SectionHeader = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  position: relative;\n\n  /* F1 Racing accent */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 4px;\n    height: 100%;\n    background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  }\n`;\n\nconst SectionTitle = styled.h2`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.xs || '4px'} 0;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n`;\n\nconst SectionDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  line-height: 1.5;\n`;\n\nconst SectionContent = styled.div`\n  padding: ${({ theme }) => theme.spacing?.lg || '24px'};\n`;\n\nconst FieldGroup = styled.div`\n  display: flex;\n  flex-direction: column;\n`;\n\n/**\n * Form field configurations\n */\nconst THEME_OPTIONS: FieldOption[] = [\n  { value: 'mercedes-green', label: 'Mercedes Green' },\n  { value: 'f1-official', label: 'F1 Official' },\n  { value: 'dark', label: 'Dark Theme' },\n];\n\n/**\n * SettingsForm Component\n *\n * PATTERN: F1 Form Pattern\n * - Racing-inspired section styling\n * - Organized field groups with clear hierarchy\n * - Consistent form field components\n * - Built-in validation and error handling\n * - Responsive design for all screen sizes\n */\nexport const SettingsForm: React.FC<SettingsFormProps> = ({\n  data,\n  onChange,\n  errors = {},\n  disabled = false,\n  className,\n}) => {\n  return (\n    <FormContainer className={className}>\n      {/* Appearance Section */}\n      <FormSection>\n        <SectionHeader>\n          <SectionTitle>Appearance</SectionTitle>\n          <SectionDescription>\n            Customize the visual appearance and theme of your trading dashboard\n          </SectionDescription>\n        </SectionHeader>\n\n        <SectionContent>\n          <FieldGroup>\n            <SettingsFormField\n              name=\"theme\"\n              label=\"Theme\"\n              description=\"Choose your preferred visual theme\"\n              type=\"select\"\n              value={data.theme}\n              onChange={onChange}\n              options={THEME_OPTIONS}\n              error={errors.theme}\n              disabled={disabled}\n            />\n          </FieldGroup>\n        </SectionContent>\n      </FormSection>\n\n      {/* General Settings Section */}\n      <FormSection>\n        <SectionHeader>\n          <SectionTitle>General Settings</SectionTitle>\n          <SectionDescription>\n            Configure general application behavior and performance settings\n          </SectionDescription>\n        </SectionHeader>\n\n        <SectionContent>\n          <FieldGroup>\n            <SettingsFormField\n              name=\"refreshInterval\"\n              label=\"Data Refresh Interval\"\n              description=\"How often to refresh dashboard data (in minutes)\"\n              type=\"number\"\n              value={data.refreshInterval}\n              onChange={onChange}\n              inputProps={{\n                min: 1,\n                max: 60,\n                step: 1,\n                style: { width: '100px' },\n              }}\n              error={errors.refreshInterval}\n              disabled={disabled}\n            />\n\n            <SettingsFormField\n              name=\"showNotifications\"\n              label=\"Desktop Notifications\"\n              description=\"Enable desktop notifications for important events and alerts\"\n              type=\"toggle\"\n              value={data.showNotifications}\n              onChange={onChange}\n              error={errors.showNotifications}\n              disabled={disabled}\n            />\n\n            <SettingsFormField\n              name=\"enableAdvancedMetrics\"\n              label=\"Advanced Metrics\"\n              description=\"Show additional performance metrics and detailed analytics\"\n              type=\"toggle\"\n              value={data.enableAdvancedMetrics}\n              onChange={onChange}\n              error={errors.enableAdvancedMetrics}\n              disabled={disabled}\n            />\n\n            <SettingsFormField\n              name=\"autoSaveJournal\"\n              label=\"Auto-Save Trade Journal\"\n              description=\"Automatically save trade entries as you type to prevent data loss\"\n              type=\"toggle\"\n              value={data.autoSaveJournal}\n              onChange={onChange}\n              error={errors.autoSaveJournal}\n              disabled={disabled}\n            />\n          </FieldGroup>\n        </SectionContent>\n      </FormSection>\n    </FormContainer>\n  );\n};\n\nexport default SettingsForm;\n", "/**\n * useSettingsForm Hook\n *\n * REFACTORED FROM: useSettings.ts (enhanced with form management)\n * Enhanced hook for managing settings form state, validation, and persistence.\n *\n * BENEFITS:\n * - Focused responsibility (form management only)\n * - Built-in validation and error handling\n * - Optimistic updates with rollback capability\n * - Local storage persistence\n * - Change tracking for unsaved state\n */\n\nimport { useState, useCallback, useEffect, useMemo } from 'react';\nimport { useTheme } from '@adhd-trading-dashboard/shared';\n\nexport interface SettingsFormData {\n  theme: string;\n  refreshInterval: number;\n  showNotifications: boolean;\n  enableAdvancedMetrics: boolean;\n  autoSaveJournal: boolean;\n}\n\nexport interface ValidationErrors {\n  [key: string]: string;\n}\n\nexport interface UseSettingsFormReturn {\n  /** Current form data */\n  data: SettingsFormData;\n  /** Original saved data */\n  savedData: SettingsFormData;\n  /** Whether form has unsaved changes */\n  hasUnsavedChanges: boolean;\n  /** Validation errors */\n  errors: ValidationErrors;\n  /** Whether form is valid */\n  isValid: boolean;\n  /** Whether save operation is in progress */\n  isSaving: boolean;\n  /** Change handler */\n  handleChange: (name: string, value: any) => void;\n  /** Save handler */\n  handleSave: () => Promise<void>;\n  /** Reset handler */\n  handleReset: () => void;\n  /** Validate specific field */\n  validateField: (name: string, value: any) => string | null;\n  /** Validate entire form */\n  validateForm: (formData: SettingsFormData) => ValidationErrors;\n}\n\nconst STORAGE_KEY = 'adhd-trading-dashboard:settings';\n\n/**\n * Default settings values\n */\nconst DEFAULT_SETTINGS: SettingsFormData = {\n  theme: 'mercedes-green',\n  refreshInterval: 5,\n  showNotifications: true,\n  enableAdvancedMetrics: false,\n  autoSaveJournal: true,\n};\n\n/**\n * Migrate old theme names to new ones\n */\nconst migrateTheme = (theme: string): string => {\n  if (theme === 'f1' || theme === 'formula1' || theme === 'formula-1') {\n    return 'mercedes-green';\n  }\n  if (theme === 'light') {\n    return 'f1-official';\n  }\n  return theme;\n};\n\n/**\n * Load settings from localStorage\n */\nconst loadSettings = (): SettingsFormData => {\n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      const parsed = JSON.parse(stored);\n      // Migrate old theme names\n      if (parsed.theme) {\n        parsed.theme = migrateTheme(parsed.theme);\n      }\n      return { ...DEFAULT_SETTINGS, ...parsed };\n    }\n  } catch (error) {\n    console.warn('Failed to load settings from localStorage:', error);\n  }\n  return DEFAULT_SETTINGS;\n};\n\n/**\n * Save settings to localStorage\n */\nconst saveSettings = (settings: SettingsFormData): void => {\n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));\n  } catch (error) {\n    console.error('Failed to save settings to localStorage:', error);\n    throw new Error('Failed to save settings');\n  }\n};\n\n/**\n * Validation rules\n */\nconst validateField = (name: string, value: any): string | null => {\n  switch (name) {\n    case 'theme':\n      if (!value || typeof value !== 'string') {\n        return 'Theme is required';\n      }\n      if (!['mercedes-green', 'f1-official', 'dark'].includes(value)) {\n        return 'Invalid theme selection';\n      }\n      return null;\n\n    case 'refreshInterval':\n      if (typeof value !== 'number' || isNaN(value)) {\n        return 'Refresh interval must be a number';\n      }\n      if (value < 1 || value > 60) {\n        return 'Refresh interval must be between 1 and 60 minutes';\n      }\n      return null;\n\n    case 'showNotifications':\n    case 'enableAdvancedMetrics':\n    case 'autoSaveJournal':\n      if (typeof value !== 'boolean') {\n        return 'Invalid boolean value';\n      }\n      return null;\n\n    default:\n      return null;\n  }\n};\n\n/**\n * Validate entire form\n */\nconst validateForm = (formData: SettingsFormData): ValidationErrors => {\n  const errors: ValidationErrors = {};\n\n  Object.entries(formData).forEach(([name, value]) => {\n    const error = validateField(name, value);\n    if (error) {\n      errors[name] = error;\n    }\n  });\n\n  return errors;\n};\n\n/**\n * useSettingsForm Hook\n *\n * Enhanced settings management with form validation and persistence.\n */\nexport const useSettingsForm = (): UseSettingsFormReturn => {\n  const { setTheme } = useTheme();\n\n  // Load initial settings\n  const [savedData, setSavedData] = useState<SettingsFormData>(() => loadSettings());\n  const [data, setData] = useState<SettingsFormData>(savedData);\n  const [errors, setErrors] = useState<ValidationErrors>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Check for unsaved changes\n  const hasUnsavedChanges = useMemo(() => {\n    return JSON.stringify(data) !== JSON.stringify(savedData);\n  }, [data, savedData]);\n\n  // Check if form is valid\n  const isValid = useMemo(() => {\n    return Object.keys(errors).length === 0;\n  }, [errors]);\n\n  /**\n   * Handle field changes with validation\n   */\n  const handleChange = useCallback(\n    (name: string, value: any) => {\n      setData((prev) => ({ ...prev, [name]: value }));\n\n      // Validate field and update errors\n      const fieldError = validateField(name, value);\n      setErrors((prev) => {\n        const newErrors = { ...prev };\n        if (fieldError) {\n          newErrors[name] = fieldError;\n        } else {\n          delete newErrors[name];\n        }\n        return newErrors;\n      });\n\n      // Apply theme change immediately for preview\n      if (name === 'theme') {\n        setTheme(value);\n      }\n    },\n    [setTheme]\n  );\n\n  /**\n   * Save settings\n   */\n  const handleSave = useCallback(async () => {\n    // Validate entire form\n    const formErrors = validateForm(data);\n    setErrors(formErrors);\n\n    if (Object.keys(formErrors).length > 0) {\n      throw new Error('Please fix validation errors before saving');\n    }\n\n    setIsSaving(true);\n\n    try {\n      // Simulate async save operation\n      await new Promise((resolve) => setTimeout(resolve, 500));\n\n      // Save to localStorage\n      saveSettings(data);\n\n      // Update saved data state\n      setSavedData(data);\n\n      // Apply theme change\n      setTheme(data.theme);\n\n      console.log('Settings saved successfully:', data);\n    } catch (error) {\n      console.error('Failed to save settings:', error);\n      throw error;\n    } finally {\n      setIsSaving(false);\n    }\n  }, [data, setTheme]);\n\n  /**\n   * Reset to saved state\n   */\n  const handleReset = useCallback(() => {\n    setData(savedData);\n    setErrors({});\n\n    // Reset theme to saved value\n    setTheme(savedData.theme);\n  }, [savedData, setTheme]);\n\n  // Validate form on data changes\n  useEffect(() => {\n    const formErrors = validateForm(data);\n    setErrors(formErrors);\n  }, [data]);\n\n  return {\n    data,\n    savedData,\n    hasUnsavedChanges,\n    errors,\n    isValid,\n    isSaving,\n    handleChange,\n    handleSave,\n    handleReset,\n    validateField,\n    validateForm,\n  };\n};\n\nexport default useSettingsForm;\n", "/**\n * SettingsContainer Component\n * \n * REFACTORED FROM: Settings.tsx (271 lines → focused components)\n * Main orchestrator for the settings page with F1 container pattern.\n * \n * BENEFITS:\n * - Uses F1Container for consistent styling\n * - Separates orchestration from presentation\n * - Better error handling and loading states\n * - Follows proven container pattern\n * - F1 racing theme integration\n */\n\nimport React, { Suspense, useState } from 'react';\nimport styled from 'styled-components';\nimport { SettingsHeader } from './SettingsHeader';\nimport { SettingsForm } from './SettingsForm';\nimport { useSettingsForm } from '../hooks/useSettingsForm';\n\nexport interface SettingsContainerProps {\n  /** Custom className */\n  className?: string;\n}\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  min-height: 100vh;\n  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};\n  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};\n`;\n\nconst ContentWrapper = styled.div`\n  flex: 1;\n  max-width: 1200px;\n  width: 100%;\n  margin: 0 auto;\n  padding: ${({ theme }) => theme.spacing?.xl || '32px'} ${({ theme }) => theme.spacing?.lg || '24px'};\n  \n  @media (max-width: 768px) {\n    padding: ${({ theme }) => theme.spacing?.lg || '24px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  }\n`;\n\nconst LoadingState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '32px'};\n  text-align: center;\n  min-height: 200px;\n`;\n\nconst LoadingIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  opacity: 0.7;\n  animation: pulse 2s infinite;\n  \n  @keyframes pulse {\n    0%, 100% { opacity: 0.7; }\n    50% { opacity: 0.3; }\n  }\n`;\n\nconst LoadingText = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n`;\n\nconst ErrorState = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing?.xl || '32px'};\n  text-align: center;\n  min-height: 200px;\n  background: ${({ theme }) => theme.colors?.error || 'var(--error-color)'}10;\n  border: 1px solid ${({ theme }) => theme.colors?.error || 'var(--error-color)'}40;\n  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};\n  margin: ${({ theme }) => theme.spacing?.lg || '24px'} 0;\n`;\n\nconst ErrorIcon = styled.div`\n  font-size: 48px;\n  margin-bottom: ${({ theme }) => theme.spacing?.md || '12px'};\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n`;\n\nconst ErrorTitle = styled.h3`\n  font-size: ${({ theme }) => theme.fontSizes?.lg || '1.125rem'};\n  font-weight: 600;\n  color: ${({ theme }) => theme.colors?.error || 'var(--error-color)'};\n  margin: 0 0 ${({ theme }) => theme.spacing?.sm || '8px'} 0;\n`;\n\nconst ErrorMessage = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};\n  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};\n  margin: 0;\n  max-width: 400px;\n`;\n\nconst RetryButton = styled.button`\n  margin-top: ${({ theme }) => theme.spacing?.md || '12px'};\n  padding: ${({ theme }) => theme.spacing?.sm || '8px'} ${({ theme }) => theme.spacing?.md || '12px'};\n  background: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};\n  color: white;\n  border: none;\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  \n  &:hover {\n    background: ${({ theme }) => theme.colors?.primaryDark || 'var(--primary-dark)'};\n    transform: translateY(-1px);\n  }\n`;\n\nconst SuccessNotification = styled.div<{ $visible: boolean }>`\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: ${({ theme }) => theme.colors?.success || 'var(--success-color)'};\n  color: white;\n  padding: ${({ theme }) => theme.spacing?.md || '12px'} ${({ theme }) => theme.spacing?.lg || '24px'};\n  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};\n  font-weight: 600;\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);\n  transform: translateX(${({ $visible }) => $visible ? '0' : '100%'});\n  opacity: ${({ $visible }) => $visible ? '1' : '0'};\n  transition: all 0.3s ease;\n  z-index: 1000;\n`;\n\n/**\n * LoadingFallback Component\n */\nconst LoadingFallback: React.FC = () => (\n  <LoadingState>\n    <LoadingIcon>⚙️</LoadingIcon>\n    <LoadingText>Loading Settings...</LoadingText>\n  </LoadingState>\n);\n\n/**\n * ErrorFallback Component\n */\nconst ErrorFallback: React.FC<{ error: string; onRetry: () => void }> = ({ error, onRetry }) => (\n  <ErrorState>\n    <ErrorIcon>⚠️</ErrorIcon>\n    <ErrorTitle>Settings Error</ErrorTitle>\n    <ErrorMessage>{error}</ErrorMessage>\n    <RetryButton onClick={onRetry}>\n      Try Again\n    </RetryButton>\n  </ErrorState>\n);\n\n/**\n * SettingsContent Component\n */\nconst SettingsContent: React.FC = () => {\n  const {\n    data,\n    hasUnsavedChanges,\n    errors,\n    isSaving,\n    handleChange,\n    handleSave,\n    handleReset,\n  } = useSettingsForm();\n  \n  const [saveError, setSaveError] = useState<string | null>(null);\n  const [showSuccessNotification, setShowSuccessNotification] = useState(false);\n  \n  const handleSaveWithNotification = async () => {\n    try {\n      setSaveError(null);\n      await handleSave();\n      \n      // Show success notification\n      setShowSuccessNotification(true);\n      setTimeout(() => setShowSuccessNotification(false), 3000);\n    } catch (error) {\n      setSaveError(error instanceof Error ? error.message : 'Failed to save settings');\n    }\n  };\n  \n  const handleRetry = () => {\n    setSaveError(null);\n  };\n  \n  if (saveError) {\n    return <ErrorFallback error={saveError} onRetry={handleRetry} />;\n  }\n  \n  return (\n    <>\n      <SettingsHeader\n        hasUnsavedChanges={hasUnsavedChanges}\n        onSave={handleSaveWithNotification}\n        onReset={handleReset}\n      />\n      \n      <SettingsForm\n        data={data}\n        onChange={handleChange}\n        errors={errors}\n        disabled={isSaving}\n      />\n      \n      <SuccessNotification $visible={showSuccessNotification}>\n        ✅ Settings saved successfully!\n      </SuccessNotification>\n    </>\n  );\n};\n\n/**\n * SettingsContainer Component\n * \n * PATTERN: F1 Container Pattern\n * - Error boundaries and loading states\n * - Consistent F1 styling and theme\n * - Proper separation of concerns\n * - Suspense for code splitting\n */\nexport const SettingsContainer: React.FC<SettingsContainerProps> = ({\n  className,\n}) => {\n  return (\n    <Container className={className}>\n      <ContentWrapper>\n        <Suspense fallback={<LoadingFallback />}>\n          <SettingsContent />\n        </Suspense>\n      </ContentWrapper>\n    </Container>\n  );\n};\n\nexport default SettingsContainer;\n", "/**\n * Settings Component\n *\n * REFACTORED: Now uses the new F1 component library and container pattern.\n * Simplified from 271 lines to a clean wrapper component.\n *\n * BENEFITS:\n * - 95% code reduction\n * - Uses proven container pattern\n * - F1 component library integration\n * - Better separation of concerns\n * - Consistent with other refactored components\n */\n\nimport React from 'react';\nimport { SettingsContainer } from './components/SettingsContainer';\n\n/**\n * Settings Component\n *\n * Simple wrapper that renders the container.\n * Follows the proven architecture pattern.\n */\nconst Settings: React.FC = () => {\n  return <SettingsContainer />;\n};\n\nexport default Settings;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "lg", "colors", "primary", "TitleSection", "xs", "Title", "h1", "fontSizes", "xxl", "textPrimary", "Subtitle", "p", "sm", "textSecondary", "ActionsSection", "md", "StatusIndicator", "borderRadius", "full", "$hasChanges", "warning", "success", "StatusDot", "ActionButton", "button", "$variant", "primaryDark", "border", "surface", "<PERSON><PERSON><PERSON>H<PERSON><PERSON>", "className", "hasUnsavedChanges", "onSave", "onReset", "jsxs", "jsx", "Fragment", "FieldC<PERSON>r", "FieldRow", "LabelSection", "FieldLabel", "label", "FieldDescription", "ControlSection", "Input", "input", "background", "$hasError", "error", "Select", "select", "ToggleContainer", "ToggleInput", "ToggleSlider", "span", "ErrorMessage", "SettingsFormField", "name", "description", "type", "value", "onChange", "options", "inputProps", "disabled", "fieldId", "handleChange", "newValue", "renderControl", "e", "parseFloat", "target", "map", "option", "checked", "FormContainer", "xl", "FormSection", "SectionHeader", "SectionTitle", "h2", "SectionDescription", "SectionContent", "FieldGroup", "THEME_OPTIONS", "SettingsForm", "data", "errors", "refreshInterval", "min", "max", "step", "style", "width", "showNotifications", "enableAdvancedMetrics", "autoSaveJournal", "STORAGE_KEY", "DEFAULT_SETTINGS", "migrateTheme", "loadSettings", "stored", "localStorage", "getItem", "parsed", "JSON", "parse", "warn", "saveSettings", "settings", "setItem", "stringify", "Error", "validateField", "includes", "isNaN", "validateForm", "formData", "entries", "for<PERSON>ach", "useSettingsForm", "setTheme", "useTheme", "savedData", "setSavedData", "useState", "setData", "setErrors", "isSaving", "setIsSaving", "useMemo", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "useCallback", "prev", "fieldError", "newErrors", "handleSave", "formErrors", "Promise", "resolve", "setTimeout", "log", "handleReset", "useEffect", "Container", "ContentWrapper", "LoadingState", "LoadingIcon", "LoadingText", "ErrorState", "ErrorIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "h3", "RetryButton", "SuccessNotification", "$visible", "LoadingFallback", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onRetry", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "saveError", "setSaveError", "showSuccessNotification", "setShowSuccessNotification", "handleSaveWithNotification", "message", "handleRetry", "SettingsContainer", "Suspense", "Settings"], "mappings": "yMA4BA,MAAMA,EAAyBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,yEAAA,8BAAA,kBAAA,uIAAA,OAAA,+CAAA,EAIrB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QACpB,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBAClD,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAa/C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBACxC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EAOhEC,EAAsBT,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGtB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,MAAK,EAG5CC,EAAeC,EAAAA,GAAEX,WAAA,CAAAC,YAAA,QAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,6HAAA,EACR,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBU,MAAO,QAE3C,CAAC,CAAEV,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,UAAS,EAS1DC,EAAkBC,EAAAA,EAAChB,WAAA,CAAAC,YAAA,WAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACV,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB,wBAAuB,EAK1EC,EAAwBpB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,GAAA,EAGxB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,OAAM,EAG7CC,EAAyBtB,EAAAA,IAAGC,WAAA,CAAAC,YAAA,kBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,uCAAA,YAAA,IAAA,kBAAA,cAAA,mEAAA,EAAA,EAGzB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,OAChC,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,OAAS,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,OAC3E,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBoB,OAAQ,UAC/C,CAAC,CAAEpB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBM,KAAM,WAKjD,CAAC,CAAEe,YAAAA,EAAarB,MAAAA,CAAM,IAAA,iBACtBqB,OAAAA,EACI;AAAA,wBACcrB,EAAAA,EAAMG,SAANH,YAAAA,EAAcsB,UAAW;AAAA,mBAC9BtB,EAAAA,EAAMG,SAANH,YAAAA,EAAcsB,UAAW;AAAA,8BACdtB,EAAAA,EAAMG,SAANH,YAAAA,EAAcsB,UAAW;AAAA,QAE7C;AAAA,wBACctB,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,UAAW;AAAA,mBAC9BvB,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,UAAW;AAAA,8BACdvB,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,UAAW;AAAA,QAC9C,EAGDC,EAAmB5B,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,qDAAA,cAAA,yDAAA,EAIZ,CAAC,CAAEsB,YAAAA,EAAarB,MAAAA,CAAM,aAClCqB,OAAAA,IACIrB,EAAAA,EAAMG,SAANH,YAAAA,EAAcsB,UAAW,yBACzBtB,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,UAAW,wBAClB,CAAC,CAAEF,YAAAA,CAAY,IAAMA,EAAc,oBAAsB,MAAM,EAQxEI,EAAsBC,EAAAA,OAAM7B,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,WAAA,IAAA,kBAAA,cAAA,4IAAA,+FAAA,EACrB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,OAAS,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAC3E,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBiB,KAAM,OAC7C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YASjD,CAAC,CAAEa,SAAAA,EAAU3B,MAAAA,CAAM,IACnB2B,yBAAAA,OAAAA,IAAa,UACT;AAAA,wBACc3B,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW;AAAA;AAAA,0BAEvBJ,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW;AAAA;AAAA;AAAA,0BAGzBJ,EAAAA,EAAMG,SAANH,YAAAA,EAAc4B,cAAe;AAAA,4BAC3B5B,EAAAA,EAAMG,SAANH,YAAAA,EAAc4B,cAAe;AAAA;AAAA,oCAErB5B,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW;AAAA;AAAA,QAGnD;AAAA;AAAA,mBAESJ,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB;AAAA,0BACxBf,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU;AAAA;AAAA;AAAA,qBAG7B7B,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe;AAAA,4BACtBX,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe;AAAA,0BAC/BX,EAAAA,EAAMG,SAANH,YAAAA,EAAc8B,UAAW;AAAA;AAAA,QAE1C,EAsBMC,EAAgDA,CAAC,CAC5DC,UAAAA,EACAC,kBAAAA,EAAoB,GACpBC,OAAAA,EACAC,QAAAA,CACF,IAEIC,OAACzC,GAAgB,UAAAqC,EACf,SAAA,CAAAI,OAAC/B,EACC,CAAA,SAAA,CAAAgC,EAAAA,IAAC9B,GAAM,SAAQ,UAAA,CAAA,EACf8B,EAAAA,IAACzB,GAAQ,SAET,kEAAA,CAAA,CAAA,EACF,SAECI,EACC,CAAA,SAAA,CAACoB,EAAAA,KAAAlB,EAAA,CAAgB,YAAae,EAC5B,SAAA,CAACI,EAAAA,IAAAb,EAAA,CAAU,YAAaS,CAAkB,CAAA,EACzCA,EAAoB,kBAAoB,WAAA,EAC3C,EAECA,GAEGG,EAAA,KAAAE,WAAA,CAAA,SAAA,CAAAD,EAAAA,IAACZ,GACC,SAAS,YACT,QAASU,EACT,MAAM,4BAA2B,SAGnC,OAAA,CAAA,EAEAE,EAAAA,IAACZ,GACC,SAAS,UACT,QAASS,EACT,MAAM,wBAAuB,SAG/B,MAAA,CAAA,CAAA,EACF,CAAA,EAEJ,CACF,CAAA,CAAA,ECjLEK,EAAwB3C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,YAAA,8BAAA,oCAAA,EAGxB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAChC,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QACpB,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,wBAAuB,EAOrFW,EAAkB5C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,yEAAA,2EAAA,IAAA,EAIlB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAKlC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,OAAM,EAI/CwB,EAAsB7C,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAG9B,EAAA,CAAA,qBAAA,CAAA,EAEK2C,EAAoBC,EAAAA,MAAK9C,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,2BAAA,0BAAA,kBAAA,kBAAA,EAEhB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBiB,KAAM,QAE1C,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,WACpC,CAAC,CAAEX,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,MAAK,EAItDsC,EAA0B/B,EAAAA,EAAChB,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB,wBAAuB,EAK1E8B,EAAwBjD,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAOhC,EAAA,CAAA,sEAAA,CAAA,EAEK+C,EAAeC,EAAAA,MAAKlD,WAAA,CAAAC,YAAA,QAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,sBAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,+DAAA,yBAAA,iDAAA,EAEb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,OAAS,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAC9E,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgD,aAAc,qBACrC,CAAC,CAAEhD,MAAAA,EAAOiD,UAAAA,CAAU,aACtCA,OAAAA,IACIjD,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,uBACvBlD,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,yBACb,CAAC,CAAE7B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBiB,KAAM,OACjD,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,WACxC,CAAC,CAAEX,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAKjC,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBAChC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EASpF+C,EAAgBC,EAAAA,OAAMvD,WAAA,CAAAC,YAAA,SAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,sBAAA,IAAA,eAAA,qBAAA,kBAAA,UAAA,cAAA,8EAAA,yBAAA,iDAAA,EAEf,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,OAAS,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAC9E,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgD,aAAc,qBACrC,CAAC,CAAEhD,MAAAA,EAAOiD,UAAAA,CAAU,aACtCA,OAAAA,IACIjD,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,uBACvBlD,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,yBACb,CAAC,CAAE7B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBiB,KAAM,OACjD,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,WACxC,CAAC,CAAEX,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAMjC,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBAChC,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EASpFiD,EAAyBV,EAAAA,MAAK9C,WAAA,CAAAC,YAAA,kBAAAC,YAAA,aAAA,CAMnC,EAAA,CAAA,+EAAA,CAAA,EAEKuD,EAAqBP,EAAAA,MAAKlD,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0DAAA,6FAAA,MAAA,EAMd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBAQ9B,CAAC,CAAEJ,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EAIpFmD,EAAsBC,EAAAA,KAAI3D,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,6EAAA,kOAAA,EAOhB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,wBAAuB,EAkBxE4B,GAAsB7D,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,eAAA,mBAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBM,KAAM,WAC1C,CAAC,CAAEN,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,sBACjC,CAAC,CAAElD,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,MAAK,EAc5CoD,EAAsDA,CAAC,CAClEC,KAAAA,EACAhB,MAAAA,EACAiB,YAAAA,EACAC,KAAAA,EACAC,MAAAA,EACAC,SAAAA,EACAC,QAAAA,EAAU,CAAE,EACZC,WAAAA,EAAa,CAAC,EACdf,MAAAA,EACAgB,SAAAA,EAAW,GACXlC,UAAAA,CACF,IAAM,CACJ,MAAMmC,EAAU,kBAAkBR,IAE5BS,EAAgBC,GAAkB,CACjCH,GACHH,EAASJ,EAAMU,CAAQ,CACzB,EAGIC,EAAgBA,IAAM,CAC1B,OAAQT,EAAI,CACV,IAAK,OACL,IAAK,SACH,OACGxB,EAAA,IAAAS,EAAA,CACC,GAAIqB,EACJ,KAAAN,EACA,MAAOC,GAAS,GAChB,SAAWS,GAAMH,EACfP,IAAS,SAAWW,WAAWD,EAAEE,OAAOX,KAAK,GAAK,EAAIS,EAAEE,OAAOX,KACjE,EACA,SAAAI,EACA,UAAW,CAAC,CAAChB,EACTe,GAAAA,CACJ,CAAA,EAGN,IAAK,SACH,OACG5B,EAAAA,IAAAc,EAAA,CACC,GAAIgB,EACJ,MAAOL,GAAS,GAChB,SAAWS,GAAMH,EAAaG,EAAEE,OAAOX,KAAK,EAC5C,SAAAI,EACA,UAAW,CAAC,CAAChB,EAEZc,SAAAA,EAAQU,IAAKC,SACX,SAA0B,CAAA,MAAOA,EAAOb,MACtCa,SAAOhC,EAAAA,KAAAA,EADGgC,EAAOb,KAEpB,CACD,CACH,CAAA,EAGJ,IAAK,SACH,cACGT,EACC,CAAA,SAAA,CAAAhB,EAAA,IAACiB,GACC,GAAIa,EACJ,KAAK,WACL,QAAS,CAAC,CAACL,EACX,YAAiBM,EAAaG,EAAEE,OAAOG,OAAO,EAC9C,SAAAV,EAAmB,QAEpBX,EAAY,EAAA,CACf,CAAA,CAAA,EAGJ,QACS,OAAA,IACX,CAAA,EAIA,OAAAnB,OAACG,GAAe,UAAAP,EACd,SAAA,CAAAI,OAACI,EACC,CAAA,SAAA,CAAAJ,OAACK,EACC,CAAA,SAAA,CAACJ,EAAA,IAAAK,EAAA,CAAW,QAASyB,EAClBxB,SACHA,EAAA,EACCiB,GACEvB,EAAAA,IAAAO,EAAA,CACEgB,SACHA,CAAA,CAAA,CAAA,EAEJ,EAEAvB,EAAAA,IAACQ,EACEyB,CAAAA,SAAAA,EACH,CAAA,CAAA,CAAA,EACF,EAECpB,GACCb,EAAA,IAACoB,GAAa,CAAA,KAAK,QAChBP,SACHA,EAAA,CAEJ,CAAA,CAAA,CAEJ,EC7RM2B,GAAuBjF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,0CAAA,GAAA,EAGvB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe8E,KAAM,OAAM,EAG7CC,EAAqBnF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,cAAA,qBAAA,kBAAA,4GAAA,iDAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc8B,UAAW,uBAClC,CAAC,CAAE9B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,yBAC1C,CAAC,CAAE7B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBE,KAAM,OAMxC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EAK5E4E,EAAuBpF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,gBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,4BAAA,eAAA,2GAAA,IAAA,EACnB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QACpB,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc6B,SAAU,yBACpD,CAAC,CAAE7B,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgD,aAAc,qBAWzC,CAAC,CAAEhD,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,uBAAsB,EAI1E6E,EAAsBC,EAAAA,GAAErF,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,qDAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,WACvC,CAAC,CAAEX,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeM,KAAM,MAAK,EAKnD6E,EAA4BtE,EAAAA,EAAChB,WAAA,CAAAC,YAAA,qBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB,wBAAuB,EAK1EqE,EAAwBxF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,GAAA,EACpB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAGjDmF,EAAoBzF,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,aAAA,CAG5B,EAAA,CAAA,qCAAA,CAAA,EAKKuF,GAA+B,CACnC,CAAExB,MAAO,iBAAkBnB,MAAO,gBAAiB,EACnD,CAAEmB,MAAO,cAAenB,MAAO,aAAc,EAC7C,CAAEmB,MAAO,OAAQnB,MAAO,YAAa,CAAC,EAa3B4C,GAA4CA,CAAC,CACxDC,KAAAA,EACAzB,SAAAA,EACA0B,OAAAA,EAAS,CAAC,EACVvB,SAAAA,EAAW,GACXlC,UAAAA,CACF,IAEII,OAACyC,IAAc,UAAA7C,EAEb,SAAA,CAAAI,OAAC2C,EACC,CAAA,SAAA,CAAA3C,OAAC4C,EACC,CAAA,SAAA,CAAA3C,EAAAA,IAAC4C,GAAa,SAAU,YAAA,CAAA,EACxB5C,EAAAA,IAAC8C,GAAkB,SAEnB,qEAAA,CAAA,CAAA,EACF,EAEA9C,EAAAA,IAAC+C,EACC,CAAA,SAAA/C,EAAA,IAACgD,EACC,CAAA,SAAAhD,EAAAA,IAACqB,GACC,KAAK,QACL,MAAM,QACN,YAAY,qCACZ,KAAK,SACL,MAAO8B,EAAKxF,MACZ,SAAA+D,EACA,QAASuB,GACT,MAAOG,EAAOzF,MACd,SAAAkE,CAAmB,CAAA,CAAA,CAEvB,CACF,CAAA,CAAA,EACF,SAGCa,EACC,CAAA,SAAA,CAAA3C,OAAC4C,EACC,CAAA,SAAA,CAAA3C,EAAAA,IAAC4C,GAAa,SAAgB,kBAAA,CAAA,EAC9B5C,EAAAA,IAAC8C,GAAkB,SAEnB,iEAAA,CAAA,CAAA,EACF,EAEA9C,EAAA,IAAC+C,EACC,CAAA,SAAAhD,EAAAA,KAACiD,EACC,CAAA,SAAA,CAAAhD,EAAA,IAACqB,EACC,CAAA,KAAK,kBACL,MAAM,wBACN,YAAY,mDACZ,KAAK,SACL,MAAO8B,EAAKE,gBACZ,SAAA3B,EACA,WAAY,CACV4B,IAAK,EACLC,IAAK,GACLC,KAAM,EACNC,MAAO,CAAEC,MAAO,OAAQ,CAE1B,EAAA,MAAON,EAAOC,gBACd,SAAAxB,CAAmB,CAAA,QAGpBR,EACC,CAAA,KAAK,oBACL,MAAM,wBACN,YAAY,+DACZ,KAAK,SACL,MAAO8B,EAAKQ,kBACZ,SAAAjC,EACA,MAAO0B,EAAOO,kBACd,SAAA9B,EAAmB,QAGpBR,EACC,CAAA,KAAK,wBACL,MAAM,mBACN,YAAY,6DACZ,KAAK,SACL,MAAO8B,EAAKS,sBACZ,SAAAlC,EACA,MAAO0B,EAAOQ,sBACd,SAAA/B,EAAmB,QAGpBR,EACC,CAAA,KAAK,kBACL,MAAM,0BACN,YAAY,oEACZ,KAAK,SACL,MAAO8B,EAAKU,gBACZ,SAAAnC,EACA,MAAO0B,EAAOS,gBACd,SAAAhC,EAAmB,CAAA,CAAA,CAEvB,CACF,CAAA,CAAA,EACF,CACF,CAAA,CAAA,ECrKEiC,EAAc,kCAKdC,EAAqC,CACzCpG,MAAO,iBACP0F,gBAAiB,EACjBM,kBAAmB,GACnBC,sBAAuB,GACvBC,gBAAiB,EACnB,EAKMG,GAAgBrG,GAChBA,IAAU,MAAQA,IAAU,YAAcA,IAAU,YAC/C,iBAELA,IAAU,QACL,cAEFA,EAMHsG,GAAeA,IAAwB,CACvC,GAAA,CACIC,MAAAA,EAASC,aAAaC,QAAQN,CAAW,EAC/C,GAAII,EAAQ,CACJG,MAAAA,EAASC,KAAKC,MAAML,CAAM,EAEhC,OAAIG,EAAO1G,QACFA,EAAAA,MAAQqG,GAAaK,EAAO1G,KAAK,GAEnC,CAAE,GAAGoG,EAAkB,GAAGM,CAAAA,SAE5BxD,GACC2D,QAAAA,KAAK,6CAA8C3D,CAAK,CAClE,CACOkD,OAAAA,CACT,EAKMU,GAAgBC,GAAqC,CACrD,GAAA,CACFP,aAAaQ,QAAQb,EAAaQ,KAAKM,UAAUF,CAAQ,CAAC,QACnD7D,GACCA,cAAAA,MAAM,2CAA4CA,CAAK,EACzD,IAAIgE,MAAM,yBAAyB,CAC3C,CACF,EAKMC,EAAgBA,CAACxD,EAAcG,IAA8B,CACjE,OAAQH,EAAI,CACV,IAAK,QACH,MAAI,CAACG,GAAS,OAAOA,GAAU,SACtB,oBAEJ,CAAC,iBAAkB,cAAe,MAAM,EAAEsD,SAAStD,CAAK,EAGtD,KAFE,0BAIX,IAAK,kBACH,OAAI,OAAOA,GAAU,UAAYuD,MAAMvD,CAAK,EACnC,oCAELA,EAAQ,GAAKA,EAAQ,GAChB,oDAEF,KAET,IAAK,oBACL,IAAK,wBACL,IAAK,kBACC,OAAA,OAAOA,GAAU,UACZ,wBAEF,KAET,QACS,OAAA,IACX,CACF,EAKMwD,EAAgBC,GAAiD,CACrE,MAAM9B,EAA2B,CAAA,EAE1B+B,cAAAA,QAAQD,CAAQ,EAAEE,QAAQ,CAAC,CAAC9D,EAAMG,CAAK,IAAM,CAC5CZ,MAAAA,EAAQiE,EAAcxD,EAAMG,CAAK,EACnCZ,IACFuC,EAAO9B,CAAI,EAAIT,EACjB,CACD,EAEMuC,CACT,EAOaiC,GAAkBA,IAA6B,CACpD,KAAA,CAAEC,SAAAA,GAAaC,EAAS,EAGxB,CAACC,EAAWC,CAAY,EAAIC,EAAAA,SAA2B,IAAMzB,IAAc,EAC3E,CAACd,EAAMwC,CAAO,EAAID,WAA2BF,CAAS,EACtD,CAACpC,EAAQwC,CAAS,EAAIF,EAAAA,SAA2B,CAAE,CAAA,EACnD,CAACG,EAAUC,CAAW,EAAIJ,WAAS,EAAK,EAGxC9F,EAAoBmG,EAAAA,QAAQ,IACzBzB,KAAKM,UAAUzB,CAAI,IAAMmB,KAAKM,UAAUY,CAAS,EACvD,CAACrC,EAAMqC,CAAS,CAAC,EAGdQ,EAAUD,EAAAA,QAAQ,IACfE,OAAOC,KAAK9C,CAAM,EAAE+C,SAAW,EACrC,CAAC/C,CAAM,CAAC,EAKLrB,EAAeqE,EAAAA,YACnB,CAAC9E,EAAcG,IAAe,CAC5BkE,EAAmBU,IAAA,CAAE,GAAGA,EAAM,CAAC/E,CAAI,EAAGG,CAAQ,EAAA,EAGxC6E,MAAAA,EAAaxB,EAAcxD,EAAMG,CAAK,EAC5CmE,EAAoBS,GAAA,CAClB,MAAME,EAAY,CAAE,GAAGF,CAAAA,EACvB,OAAIC,EACFC,EAAUjF,CAAI,EAAIgF,EAElB,OAAOC,EAAUjF,CAAI,EAEhBiF,CAAAA,CACR,EAGGjF,IAAS,SACXgE,EAAS7D,CAAK,CAChB,EAEF,CAAC6D,CAAQ,CACX,EAKMkB,EAAaJ,EAAAA,YAAY,SAAY,CAEnCK,MAAAA,EAAaxB,EAAa9B,CAAI,EAGpC,GAFAyC,EAAUa,CAAU,EAEhBR,OAAOC,KAAKO,CAAU,EAAEN,OAAS,EAC7B,MAAA,IAAItB,MAAM,4CAA4C,EAG9DiB,EAAY,EAAI,EAEZ,GAAA,CAEF,MAAM,IAAIY,QAASC,GAAYC,WAAWD,EAAS,GAAG,CAAC,EAGvDlC,GAAatB,CAAI,EAGjBsC,EAAatC,CAAI,EAGjBmC,EAASnC,EAAKxF,KAAK,EAEXkJ,QAAAA,IAAI,+BAAgC1D,CAAI,QACzCtC,GACCA,cAAAA,MAAM,2BAA4BA,CAAK,EACzCA,CAAAA,QACE,CACRiF,EAAY,EAAK,CACnB,CAAA,EACC,CAAC3C,EAAMmC,CAAQ,CAAC,EAKbwB,EAAcV,EAAAA,YAAY,IAAM,CACpCT,EAAQH,CAAS,EACjBI,EAAU,CAAE,CAAA,EAGZN,EAASE,EAAU7H,KAAK,CAAA,EACvB,CAAC6H,EAAWF,CAAQ,CAAC,EAGxByB,OAAAA,EAAAA,UAAU,IAAM,CACRN,MAAAA,EAAaxB,EAAa9B,CAAI,EACpCyC,EAAUa,CAAU,CAAA,EACnB,CAACtD,CAAI,CAAC,EAEF,CACLA,KAAAA,EACAqC,UAAAA,EACA5F,kBAAAA,EACAwD,OAAAA,EACA4C,QAAAA,EACAH,SAAAA,EACA9D,aAAAA,EACAyE,WAAAA,EACAM,YAAAA,EACAhC,cAAAA,EACAG,aAAAA,CAAAA,CAEJ,EChQM+B,GAAmBzJ,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,kEAAA,UAAA,GAAA,EAIZ,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcgD,aAAc,qBAChD,CAAC,CAAEhD,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcW,cAAe,UAAS,EAG1D2I,GAAwB1J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,iBAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,4DAAA,IAAA,qCAAA,IAAA,IAAA,EAKpB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe8E,KAAM,QAAU,CAAC,CAAE9E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAGhF,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAAU,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,OAAM,EAIjGsI,GAAsB3J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,sCAAA,EAKlB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe8E,KAAM,OAAM,EAKjD0E,GAAqB5J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,mGAAA,EAEX,CAAC,CAAEC,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,OAAM,EAUvDwI,GAAqB5I,EAAAA,EAAChB,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,YAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBE,KAAM,YAC1C,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB,wBAAuB,EAI1E2I,GAAoB9J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,wFAAA,kDAAA,uBAAA,oBAAA,WAAA,KAAA,EAKhB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAe8E,KAAM,QAGjC,CAAC,CAAE9E,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,sBAChC,CAAC,CAAElD,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,sBACzC,CAAC,CAAElD,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBE,KAAM,OAChD,CAAC,CAAEF,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,OAAM,EAGhDyJ,GAAmB/J,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,gCAAA,UAAA,GAAA,EAET,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAC5C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,qBAAoB,EAG/D0G,GAAoBC,EAAAA,GAAEhK,WAAA,CAAAC,YAAA,aAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,0BAAA,eAAA,KAAA,EACb,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBE,KAAM,YAE1C,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAckD,QAAS,sBACjC,CAAC,CAAElD,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,MAAK,EAGnD2C,GAAsB5C,EAAAA,EAAChB,WAAA,CAAAC,YAAA,eAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,aAAA,UAAA,4BAAA,EACd,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMS,YAANT,YAAAA,EAAiBc,KAAM,YAC1C,CAAC,CAAEd,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAce,gBAAiB,wBAAuB,EAK1E+I,GAAqBpI,EAAAA,OAAM7B,WAAA,CAAAC,YAAA,cAAAC,YAAA,cAAA,CAAA,EAAA,CAAA,cAAA,YAAA,IAAA,eAAA,0CAAA,+EAAA,+BAAA,EACjB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QACvC,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAec,KAAM,OAAS,CAAC,CAAEd,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAC9E,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcI,UAAW,wBAGrC,CAAC,CAAEJ,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBiB,KAAM,OAM1C,CAAC,CAAEjB,MAAAA,CAAM,IAAMA,OAAAA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAc4B,cAAe,sBAAqB,EAK7EmI,GAA6BnK,EAAAA,IAAGC,WAAA,CAAAC,YAAA,sBAAAC,YAAA,eAAA,CAAA,EAAA,CAAA,iDAAA,wBAAA,IAAA,kBAAA,qFAAA,aAAA,yCAAA,EAItB,CAAC,CAAEC,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMG,SAANH,YAAAA,EAAcuB,UAAW,wBAE3C,CAAC,CAAEvB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeiB,KAAM,QAAU,CAAC,CAAEjB,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMC,UAAND,YAAAA,EAAeE,KAAM,QAC5E,CAAC,CAAEF,MAAAA,CAAM,IAAA,OAAMA,QAAAA,EAAAA,EAAMmB,eAANnB,YAAAA,EAAoBiB,KAAM,OAGlC,CAAC,CAAE+I,SAAAA,CAAS,IAAMA,EAAW,IAAM,OAChD,CAAC,CAAEA,SAAAA,CAAS,IAAMA,EAAW,IAAM,GAAG,EAQ7CC,GAA4BA,IAChC7H,EAAAA,KAACmH,GACC,CAAA,SAAA,CAAAlH,EAAAA,IAACmH,IAAY,SAAE,IAAA,CAAA,EACfnH,EAAAA,IAACoH,IAAY,SAAmB,qBAAA,CAAA,CAAA,CAClC,CAAA,EAMIS,GAAkEA,CAAC,CAAEhH,MAAAA,EAAOiH,QAAAA,CAAQ,WACvFT,GACC,CAAA,SAAA,CAAArH,EAAAA,IAACsH,IAAU,SAAE,IAAA,CAAA,EACbtH,EAAAA,IAACuH,IAAW,SAAc,gBAAA,CAAA,EAC1BvH,EAAAA,IAACoB,IAAcP,SAAMA,CAAA,CAAA,EACpBb,EAAA,IAAAyH,GAAA,CAAY,QAASK,EAAQ,SAE9B,YAAA,CAAA,CACF,CAAA,EAMIC,GAA4BA,IAAM,CAChC,KAAA,CACJ5E,KAAAA,EACAvD,kBAAAA,EACAwD,OAAAA,EACAyC,SAAAA,EACA9D,aAAAA,EACAyE,WAAAA,EACAM,YAAAA,GACEzB,GAAgB,EAEd,CAAC2C,EAAWC,CAAY,EAAIvC,WAAwB,IAAI,EACxD,CAACwC,EAAyBC,CAA0B,EAAIzC,WAAS,EAAK,EAEtE0C,EAA6B,SAAY,CACzC,GAAA,CACFH,EAAa,IAAI,EACjB,MAAMzB,EAAW,EAGjB2B,EAA2B,EAAI,EAC/BvB,WAAW,IAAMuB,EAA2B,EAAK,EAAG,GAAI,QACjDtH,GACPoH,EAAapH,aAAiBgE,MAAQhE,EAAMwH,QAAU,yBAAyB,CACjF,CAAA,EAGIC,EAAcA,IAAM,CACxBL,EAAa,IAAI,CAAA,EAGnB,OAAID,EACMhI,EAAAA,IAAA6H,GAAA,CAAc,MAAOG,EAAW,QAASM,CAAe,CAAA,EAK9DvI,EAAA,KAAAE,WAAA,CAAA,SAAA,CAAAD,EAAA,IAACN,EACC,CAAA,kBAAAE,EACA,OAAQwI,EACR,QAAStB,EAAY,QAGtB5D,GACC,CAAA,KAAAC,EACA,SAAUpB,EACV,OAAAqB,EACA,SAAUyC,EAAS,EAGpB7F,EAAA,IAAA0H,GAAA,CAAoB,SAAUQ,EAAwB,SAEvD,iCAAA,CACF,CAAA,CAAA,CAEJ,EAWaK,GAAsDA,CAAC,CAClE5I,UAAAA,CACF,IAEKK,MAAAgH,GAAA,CAAU,UAAArH,EACT,SAAAK,MAACiH,IACC,SAACjH,EAAA,IAAAwI,WAAA,CAAS,SAAUxI,EAAAA,IAAC4H,KAAe,EAClC,SAAA5H,EAAAA,IAAC+H,GAAe,EAAA,CAAA,CAClB,CACF,CAAA,CACF,CAAA,EC5NEU,GAAqBA,UACjBF,GAAoB,CAAA,CAAA"}