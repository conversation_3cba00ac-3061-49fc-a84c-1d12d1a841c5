{"version": 3, "file": "NotFound-89e7ec7d.js", "sources": ["../../src/components/NotFound.tsx"], "sourcesContent": ["/**\n * Not Found Component\n *\n * Displayed when a user navigates to a non-existent route\n */\n\nimport React from \"react\";\nimport styled from \"styled-components\";\nimport { Link } from \"react-router-dom\";\n\nconst Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: ${({ theme }) => theme.spacing.xl};\n  text-align: center;\n`;\n\nconst ErrorCode = styled.div`\n  font-size: 6rem;\n  font-weight: 700;\n  color: ${({ theme }) => theme.colors.primary};\n  margin-bottom: ${({ theme }) => theme.spacing.md};\n`;\n\nconst ErrorMessage = styled.h1`\n  font-size: ${({ theme }) => theme.fontSizes.xl};\n  color: ${({ theme }) => theme.colors.textPrimary};\n  margin-bottom: ${({ theme }) => theme.spacing.lg};\n`;\n\nconst ErrorDescription = styled.p`\n  font-size: ${({ theme }) => theme.fontSizes.md};\n  color: ${({ theme }) => theme.colors.textSecondary};\n  max-width: 500px;\n  margin: 0 auto ${({ theme }) => theme.spacing.lg};\n`;\n\nconst HomeLink = styled(Link)`\n  padding: ${({ theme }) => `${theme.spacing.sm} ${theme.spacing.lg}`};\n  background-color: ${({ theme }) => theme.colors.primary};\n  color: white;\n  border-radius: ${({ theme }) => theme.borderRadius.md};\n  text-decoration: none;\n  font-weight: 500;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: ${({ theme }) => theme.colors.primaryDark};\n  }\n`;\n\nconst NotFound: React.FC = () => {\n  return (\n    <Container>\n      <ErrorCode>404</ErrorCode>\n      <ErrorMessage>Page Not Found</ErrorMessage>\n      <ErrorDescription>\n        The page you are looking for might have been removed, had its name\n        changed, or is temporarily unavailable.\n      </ErrorDescription>\n      <HomeLink to=\"/\">Back to Dashboard</HomeLink>\n    </Container>\n  );\n};\n\nexport default NotFound;\n"], "names": ["Container", "div", "withConfig", "displayName", "componentId", "theme", "spacing", "xl", "ErrorCode", "colors", "primary", "md", "ErrorMessage", "h1", "fontSizes", "textPrimary", "lg", "ErrorDescription", "p", "textSecondary", "HomeLink", "styled", "Link", "sm", "borderRadius", "primaryDark", "NotFound", "jsx"], "mappings": "kKAUA,MAAMA,EAAmBC,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wFAAA,qBAAA,EAKf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMC,QAAQC,EAAE,EAItCC,EAAmBP,EAAAA,IAAGC,WAAA,CAAAC,YAAA,YAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,wCAAA,kBAAA,GAAA,EAGjB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMI,OAAOC,QACpB,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMC,QAAQK,EAAE,EAG5CC,EAAsBC,EAAAA,GAAEX,WAAA,CAAAC,YAAA,eAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kBAAA,GAAA,EACf,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMS,UAAUP,GACnC,CAAC,CAAEF,MAAAA,CAAM,IAAMA,EAAMI,OAAOM,YACpB,CAAC,CAAEV,MAAAA,CAAM,IAAMA,EAAMC,QAAQU,EAAE,EAG5CC,EAA0BC,EAAAA,EAAChB,WAAA,CAAAC,YAAA,mBAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,aAAA,UAAA,kCAAA,GAAA,EAClB,CAAC,CAAEC,MAAAA,CAAM,IAAMA,EAAMS,UAAUH,GACnC,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMI,OAAOU,cAEpB,CAAC,CAAEd,MAAAA,CAAM,IAAMA,EAAMC,QAAQU,EAAE,EAG5CI,EAAWC,EAAOC,CAAI,EAACpB,WAAA,CAAAC,YAAA,WAAAC,YAAA,aAAA,CAAA,EAAA,CAAA,WAAA,qBAAA,8BAAA,mGAAA,IAAA,EAChB,CAAC,CAAEC,MAAAA,CAAM,IAAM,GAAGA,EAAMC,QAAQiB,MAAMlB,EAAMC,QAAQU,KAC3C,CAAC,CAAEX,MAAAA,CAAM,IAAMA,EAAMI,OAAOC,QAE/B,CAAC,CAAEL,MAAAA,CAAM,IAAMA,EAAMmB,aAAab,GAM7B,CAAC,CAAEN,MAAAA,CAAM,IAAMA,EAAMI,OAAOgB,WAAW,EAIzDC,EAAqBA,WAEtB1B,EACC,CAAA,SAAA,CAAA2B,EAAAA,IAACnB,GAAU,SAAG,KAAA,CAAA,EACdmB,EAAAA,IAACf,GAAa,SAAc,gBAAA,CAAA,EAC5Be,EAAAA,IAACV,GAAgB,SAGjB,4GAAA,CAAA,EACCU,EAAA,IAAAP,EAAA,CAAS,GAAG,IAAI,SAAiB,oBAAA,CACpC,CAAA,CAAA"}