var B=Object.defineProperty;var W=(v,e,o)=>e in v?B(v,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):v[e]=o;var R=(v,e,o)=>(W(v,typeof e!="symbol"?e+"":e,o),o);const d={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class U{constructor(){R(this,"dbName","adhd-trading-dashboard");R(this,"version",2);R(this,"db",null);R(this,"stores",{trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"})}async initDB(){return this.db?this.db:new Promise((e,o)=>{const l=indexedDB.open(this.dbName,this.version);l.onupgradeneeded=_=>{var p;const r=_.target.result;if(!r.objectStoreNames.contains(this.stores.trades)){const a=r.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});a.createIndex(d.DATE,d.DATE,{unique:!1}),a.createIndex(d.MODEL_TYPE,d.MODEL_TYPE,{unique:!1}),a.createIndex(d.SESSION,d.SESSION,{unique:!1}),a.createIndex(d.WIN_LOSS,d.WIN_LOSS,{unique:!1}),a.createIndex(d.R_MULTIPLE,d.R_MULTIPLE,{unique:!1})}if(r.objectStoreNames.contains(this.stores.fvg_details)||r.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),r.objectStoreNames.contains(this.stores.setups)||r.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),r.objectStoreNames.contains(this.stores.analysis)||r.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!r.objectStoreNames.contains(this.stores.sessions)){r.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const s=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(p=l.transaction)==null||p.addEventListener("complete",()=>{const t=r.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);s.forEach(c=>t.add(c))})}},l.onsuccess=_=>{this.db=_.target.result,e(this.db)},l.onerror=_=>{console.error("Error opening IndexedDB:",_),o(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(e){try{const o=await this.initDB();return new Promise((l,_)=>{const r=o.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");r.onerror=i=>{console.error("Transaction error:",i),_(new Error("Failed to save trade with details"))};const p=r.objectStore(this.stores.trades),a={...e.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},s=p.add(a);s.onsuccess=()=>{const i=s.result,t=[];if(e.fvg_details){const c=r.objectStore(this.stores.fvg_details),S={...e.fvg_details,trade_id:i};t.push(new Promise((w,h)=>{const u=c.add(S);u.onsuccess=()=>w(),u.onerror=()=>h(new Error("Failed to save FVG details"))}))}if(e.setup){const c=r.objectStore(this.stores.setups),S={...e.setup,trade_id:i};t.push(new Promise((w,h)=>{const u=c.add(S);u.onsuccess=()=>w(),u.onerror=()=>h(new Error("Failed to save setup data"))}))}if(e.analysis){const c=r.objectStore(this.stores.analysis),S={...e.analysis,trade_id:i};t.push(new Promise((w,h)=>{const u=c.add(S);u.onsuccess=()=>w(),u.onerror=()=>h(new Error("Failed to save analysis data"))}))}r.oncomplete=()=>{l(i)}},s.onerror=i=>{console.error("Error saving trade:",i),_(new Error("Failed to save trade"))}})}catch(o){throw console.error("Error in saveTradeWithDetails:",o),new Error("Failed to save trade with details")}}async getTradeById(e){try{const o=await this.initDB();return new Promise((l,_)=>{const r=o.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),a=r.objectStore(this.stores.trades).get(e);a.onsuccess=()=>{const s=a.result;if(!s){l(null);return}const i={trade:s},S=r.objectStore(this.stores.fvg_details).index("trade_id").get(e);S.onsuccess=()=>{S.result&&(i.fvg_details=S.result);const u=r.objectStore(this.stores.setups).index("trade_id").get(e);u.onsuccess=()=>{u.result&&(i.setup=u.result);const g=r.objectStore(this.stores.analysis).index("trade_id").get(e);g.onsuccess=()=>{g.result&&(i.analysis=g.result),l(i)},g.onerror=b=>{console.error("Error getting analysis data:",b),l(i)}},u.onerror=I=>{console.error("Error getting setup data:",I),l(i)}},S.onerror=w=>{console.error("Error getting FVG details:",w),l(i)}},a.onerror=s=>{console.error("Error getting trade:",s),_(new Error("Failed to get trade"))}})}catch(o){return console.error("Error in getTradeById:",o),null}}async getPerformanceMetrics(){try{const e=await this.initDB();return new Promise((o,l)=>{const p=e.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();p.onsuccess=()=>{const a=p.result;if(a.length===0){o({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const s=a.length,i=a.filter(n=>n[d.WIN_LOSS]==="Win").length,t=a.filter(n=>n[d.WIN_LOSS]==="Loss").length,c=s>0?i/s*100:0,S=a.filter(n=>n.achieved_pl!==void 0).map(n=>n.achieved_pl),w=S.reduce((n,f)=>n+f,0),h=S.filter(n=>n>0),u=S.filter(n=>n<0),I=h.length>0?h.reduce((n,f)=>n+f,0)/h.length:0,m=u.length>0?Math.abs(u.reduce((n,f)=>n+f,0)/u.length):0,g=h.length>0?Math.max(...h):0,b=u.length>0?Math.abs(Math.min(...u)):0,T=h.reduce((n,f)=>n+f,0),y=Math.abs(u.reduce((n,f)=>n+f,0)),M=y>0?T/y:0,E=a.filter(n=>n[d.R_MULTIPLE]!==void 0).map(n=>n[d.R_MULTIPLE]),L=E.length>0?E.reduce((n,f)=>n+f,0)/E.length:0,N=L*(c/100);let q=0,x=0,D=0;for(const n of a)if(n.achieved_pl!==void 0){q+=n.achieved_pl,q>x&&(x=q);const f=x-q;f>D&&(D=f)}const j=x>0?D/x*100:0,A=E.length>0?Math.sqrt(E.length)*L/Math.sqrt(E.reduce((n,f)=>n+Math.pow(f-L,2),0)/E.length):0,P=a.map(n=>n.date).sort(),O=P.length>0?P[0]:"",F=P.length>0?P[P.length-1]:"";o({totalTrades:s,winningTrades:i,losingTrades:t,winRate:c,profitFactor:M,averageWin:I,averageLoss:m,largestWin:g,largestLoss:b,totalPnl:w,maxDrawdown:D,maxDrawdownPercent:j,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:L,expectancy:N,sqn:A,period:"all",startDate:O,endDate:F})},p.onerror=a=>{console.error("Error getting performance metrics:",a),l(new Error("Failed to get performance metrics"))}})}catch(e){throw console.error("Error in getPerformanceMetrics:",e),new Error("Failed to get performance metrics")}}async filterTrades(e){try{const o=await this.initDB();return new Promise((l,_)=>{const r=o.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),a=r.objectStore(this.stores.trades).getAll();a.onsuccess=async()=>{let s=a.result;e.dateFrom&&(s=s.filter(t=>t.date>=e.dateFrom)),e.dateTo&&(s=s.filter(t=>t.date<=e.dateTo)),e.model_type&&(s=s.filter(t=>t[d.MODEL_TYPE]===e.model_type)),e.session&&(s=s.filter(t=>t[d.SESSION]===e.session)),e.direction&&(s=s.filter(t=>t[d.DIRECTION]===e.direction)),e.win_loss&&(s=s.filter(t=>t[d.WIN_LOSS]===e.win_loss)),e.market&&(s=s.filter(t=>t[d.MARKET]===e.market)),e.min_r_multiple!==void 0&&(s=s.filter(t=>t[d.R_MULTIPLE]!==void 0&&t[d.R_MULTIPLE]>=e.min_r_multiple)),e.max_r_multiple!==void 0&&(s=s.filter(t=>t[d.R_MULTIPLE]!==void 0&&t[d.R_MULTIPLE]<=e.max_r_multiple)),e.min_pattern_quality!==void 0&&(s=s.filter(t=>t[d.PATTERN_QUALITY_RATING]!==void 0&&t[d.PATTERN_QUALITY_RATING]>=e.min_pattern_quality)),e.max_pattern_quality!==void 0&&(s=s.filter(t=>t[d.PATTERN_QUALITY_RATING]!==void 0&&t[d.PATTERN_QUALITY_RATING]<=e.max_pattern_quality));const i=[];for(const t of s){const c={trade:t},h=r.objectStore(this.stores.fvg_details).index("trade_id").get(t.id);await new Promise(y=>{h.onsuccess=()=>{h.result&&(c.fvg_details=h.result),y()},h.onerror=()=>y()});const m=r.objectStore(this.stores.setups).index("trade_id").get(t.id);await new Promise(y=>{m.onsuccess=()=>{m.result&&(c.setup=m.result),y()},m.onerror=()=>y()});const T=r.objectStore(this.stores.analysis).index("trade_id").get(t.id);await new Promise(y=>{T.onsuccess=()=>{T.result&&(c.analysis=T.result),y()},T.onerror=()=>y()}),i.push(c)}l(i)},a.onerror=s=>{console.error("Error filtering trades:",s),_(new Error("Failed to filter trades"))}})}catch(o){throw console.error("Error in filterTrades:",o),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(e){return console.error("Error in getAllTrades:",e),[]}}async deleteTrade(e){try{const o=await this.initDB();return new Promise((l,_)=>{const r=o.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");r.onerror=m=>{console.error("Transaction error:",m),_(new Error("Failed to delete trade"))};const s=r.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(e));s.onsuccess=m=>{const g=m.target.result;g&&(g.delete(),g.continue())};const c=r.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(e));c.onsuccess=m=>{const g=m.target.result;g&&(g.delete(),g.continue())};const h=r.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(e));h.onsuccess=m=>{const g=m.target.result;g&&(g.delete(),g.continue())};const I=r.objectStore(this.stores.trades).delete(e);r.oncomplete=()=>{l()},I.onerror=m=>{console.error("Error deleting trade:",m),_(new Error("Failed to delete trade"))}})}catch(o){throw console.error("Error in deleteTrade:",o),new Error("Failed to delete trade")}}async updateTradeWithDetails(e,o){try{const l=await this.initDB();return new Promise((_,r)=>{const p=l.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");p.onerror=t=>{console.error("Transaction error:",t),r(new Error("Failed to update trade"))};const a=p.objectStore(this.stores.trades),s={...o.trade,id:e,updated_at:new Date().toISOString()},i=a.put(s);i.onsuccess=()=>{if(o.fvg_details){const t=p.objectStore(this.stores.fvg_details),c={...o.fvg_details,trade_id:e};t.put(c)}if(o.setup){const t=p.objectStore(this.stores.setups),c={...o.setup,trade_id:e};t.put(c)}if(o.analysis){const t=p.objectStore(this.stores.analysis),c={...o.analysis,trade_id:e};t.put(c)}},p.oncomplete=()=>{_()},i.onerror=t=>{console.error("Error updating trade:",t),r(new Error("Failed to update trade"))}})}catch(l){throw console.error("Error in updateTradeWithDetails:",l),new Error("Failed to update trade")}}}const k=new U,C=k;export{C as t};
//# sourceMappingURL=tradeStorage-a5c0ed9a.js.map
