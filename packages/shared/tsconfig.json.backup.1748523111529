{"extends": "../../tsconfig.json", "compilerOptions": {"target": "es2020", "module": "esnext", "moduleResolution": "node", "declaration": true, "declarationMap": true, "outDir": "./dist", "rootDir": "./src", "composite": true, "jsx": "react-jsx", "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "sourceMap": true, "noEmit": false, "noEmitOnError": false, "strict": false, "noImplicitAny": false, "noImplicitThis": false, "strictNullChecks": false, "noImplicitReturns": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "types": ["vitest/globals"], "baseUrl": "src", "paths": {"@api/*": ["api/*"], "@components/*": ["components/*"], "@context/*": ["context/*"], "@hooks/*": ["hooks/*"], "@utils/*": ["utils/*"], "@theme/*": ["theme/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/*", "**/*.bak"]}