{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "tsBuildInfoFile": "tsconfig.tsbuildinfo", "skipLibCheck": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*", "**/*.config.js", "**/*.config.ts", "**/*.d.ts"]}