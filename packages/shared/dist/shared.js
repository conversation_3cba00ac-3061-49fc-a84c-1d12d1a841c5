"use strict";var wo=Object.defineProperty;var Co=(e,r,t)=>r in e?wo(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t;var xe=(e,r,t)=>(Co(e,typeof r!="symbol"?r+"":r,t),t);Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const y=require("react"),n=require("styled-components"),To=require("react-dom");var yt=(e=>(e.LONG="LONG",e.SHORT="SHORT",e))(yt||{}),vt=(e=>(e.OPEN="OPEN",e.CLOSED="CLOSED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e.PENDING="PENDING",e))(vt||{}),St=(e=>(e.MARKET="MARKET",e.LIMIT="LIMIT",e.STOP="STOP",e.STOP_LIMIT="STOP_LIMIT",e))(St||{}),wt=(e=>(e.BUY="BUY",e.SELL="SELL",e))(wt||{}),Ct=(e=>(e.PENDING="PENDING",e.FILLED="FILLED",e.PARTIALLY_FILLED="PARTIALLY_FILLED",e.CANCELED="CANCELED",e.REJECTED="REJECTED",e))(Ct||{}),Tt=(e=>(e.GTC="GTC",e.IOC="IOC",e.FOK="FOK",e.DAY="DAY",e))(Tt||{});const hr={completeTradeToLegacy:e=>{var t;const r=e.trade;return{id:((t=r.id)==null?void 0:t.toString())||"0",symbol:r.market||"MNQ",date:r.date,direction:r.direction,size:r.no_of_contracts||1,entry:r.entry_price||0,exit:r.exit_price||0,stopLoss:0,takeProfit:0,profitLoss:r.achieved_pl||0,strategy:r.setup||"",notes:r.notes||"",tags:[],images:[]}},legacyToCompleteTrade:e=>({trade:{id:parseInt(e.id)||void 0,date:e.date,model_type:"Unknown",direction:e.direction,market:e.symbol,entry_price:e.entry,exit_price:e.exit,achieved_pl:e.profitLoss,no_of_contracts:e.size,setup:e.strategy,notes:e.notes,created_at:new Date().toISOString(),updated_at:new Date().toISOString()}}),completeTradeArrayToLegacy:e=>e.map(hr.completeTradeToLegacy),legacyArrayToCompleteTrade:e=>e.map(hr.legacyToCompleteTrade)};var Y=(e=>(e.LONDON="london",e.NEW_YORK_AM="new-york-am",e.NEW_YORK_PM="new-york-pm",e.ASIA="asia",e.PRE_MARKET="pre-market",e.AFTER_HOURS="after-hours",e.OVERNIGHT="overnight",e))(Y||{}),P=(e=>(e.MORNING_BREAKOUT="morning-breakout",e.MID_MORNING_REVERSION="mid-morning-reversion",e.PRE_LUNCH="pre-lunch",e.LUNCH_MACRO_EXTENDED="lunch-macro-extended",e.LUNCH_MACRO="lunch-macro",e.POST_LUNCH="post-lunch",e.PRE_CLOSE="pre-close",e.POWER_HOUR="power-hour",e.MOC="moc",e.LONDON_OPEN="london-open",e.LONDON_NY_OVERLAP="london-ny-overlap",e.CUSTOM="custom",e))(P||{});const Se={constant:{parentArrays:["NWOG","Old-NWOG","NDOG","Old-NDOG","Monthly-FVG","Weekly-FVG","Daily-FVG","15min-Top/Bottom-FVG","1h-Top/Bottom-FVG"],fvgTypes:["Strong-FVG","AM-FPFVG","PM-FPFVG","Asia-FPFVG","Premarket-FPFVG","MNOR-FVG","Macro-FVG","News-FVG","Top/Bottom-FVG"]},action:{liquidityEvents:["None","London-H/L","Premarket-H/L","09:30-Opening-Range-H/L","Lunch-H/L","Prev-Day-H/L","Prev-Week-H/L","Monthly-H/L","Macro-H/L"]},variable:{rdTypes:["None","True-RD","IMM-RD","Dispersed-RD","Wide-Gap-RD"]},entry:{methods:["Simple-Entry","Complex-Entry","Complex-Entry/Mini"]}},Eo=["RD-Cont","FVG-RD","Combined"];var o={},Io={get exports(){return o},set exports(e){o=e}},je={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yr;function jo(){if(Yr)return je;Yr=1;var e=y,r=Symbol.for("react.element"),t=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(p,d,f){var l,m={},x=null,h=null;f!==void 0&&(x=""+f),d.key!==void 0&&(x=""+d.key),d.ref!==void 0&&(h=d.ref);for(l in d)s.call(d,l)&&!c.hasOwnProperty(l)&&(m[l]=d[l]);if(p&&p.defaultProps)for(l in d=p.defaultProps,d)m[l]===void 0&&(m[l]=d[l]);return{$$typeof:r,type:p,key:x,ref:h,props:m,_owner:i.current}}return je.Fragment=t,je.jsx=a,je.jsxs=a,je}var Ne={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gr;function No(){return Gr||(Gr=1,process.env.NODE_ENV!=="production"&&function(){var e=y,r=Symbol.for("react.element"),t=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),p=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),l=Symbol.for("react.suspense_list"),m=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),h=Symbol.for("react.offscreen"),g=Symbol.iterator,b="@@iterator";function T(u){if(u===null||typeof u!="object")return null;var S=g&&u[g]||u[b];return typeof S=="function"?S:null}var w=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function C(u){{for(var S=arguments.length,E=new Array(S>1?S-1:0),D=1;D<S;D++)E[D-1]=arguments[D];M("error",u,E)}}function M(u,S,E){{var D=w.ReactDebugCurrentFrame,F=D.getStackAddendum();F!==""&&(S+="%s",E=E.concat([F]));var H=E.map(function(z){return String(z)});H.unshift("Warning: "+S),Function.prototype.apply.call(console[u],console,H)}}var A=!1,N=!1,k=!1,R=!1,j=!1,L;L=Symbol.for("react.module.reference");function W(u){return!!(typeof u=="string"||typeof u=="function"||u===s||u===c||j||u===i||u===f||u===l||R||u===h||A||N||k||typeof u=="object"&&u!==null&&(u.$$typeof===x||u.$$typeof===m||u.$$typeof===a||u.$$typeof===p||u.$$typeof===d||u.$$typeof===L||u.getModuleId!==void 0))}function V(u,S,E){var D=u.displayName;if(D)return D;var F=S.displayName||S.name||"";return F!==""?E+"("+F+")":E}function _(u){return u.displayName||"Context"}function q(u){if(u==null)return null;if(typeof u.tag=="number"&&C("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof u=="function")return u.displayName||u.name||null;if(typeof u=="string")return u;switch(u){case s:return"Fragment";case t:return"Portal";case c:return"Profiler";case i:return"StrictMode";case f:return"Suspense";case l:return"SuspenseList"}if(typeof u=="object")switch(u.$$typeof){case p:var S=u;return _(S)+".Consumer";case a:var E=u;return _(E._context)+".Provider";case d:return V(u,u.render,"ForwardRef");case m:var D=u.displayName||null;return D!==null?D:q(u.type)||"Memo";case x:{var F=u,H=F._payload,z=F._init;try{return q(z(H))}catch{return null}}}return null}var J=Object.assign,Z=0,le,ee,$,K,he,B,ue;function _r(){}_r.__reactDisabledLog=!0;function Qt(){{if(Z===0){le=console.log,ee=console.info,$=console.warn,K=console.error,he=console.group,B=console.groupCollapsed,ue=console.groupEnd;var u={configurable:!0,enumerable:!0,value:_r,writable:!0};Object.defineProperties(console,{info:u,log:u,warn:u,error:u,group:u,groupCollapsed:u,groupEnd:u})}Z++}}function Xt(){{if(Z--,Z===0){var u={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:J({},u,{value:le}),info:J({},u,{value:ee}),warn:J({},u,{value:$}),error:J({},u,{value:K}),group:J({},u,{value:he}),groupCollapsed:J({},u,{value:B}),groupEnd:J({},u,{value:ue})})}Z<0&&C("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var sr=w.ReactCurrentDispatcher,nr;function Ve(u,S,E){{if(nr===void 0)try{throw Error()}catch(F){var D=F.stack.trim().match(/\n( *(at )?)/);nr=D&&D[1]||""}return`
`+nr+u}}var ir=!1,Ye;{var Jt=typeof WeakMap=="function"?WeakMap:Map;Ye=new Jt}function Mr(u,S){if(!u||ir)return"";{var E=Ye.get(u);if(E!==void 0)return E}var D;ir=!0;var F=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var H;H=sr.current,sr.current=null,Qt();try{if(S){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(pe){D=pe}Reflect.construct(u,[],z)}else{try{z.call()}catch(pe){D=pe}u.call(z.prototype)}}else{try{throw Error()}catch(pe){D=pe}u()}}catch(pe){if(pe&&D&&typeof pe.stack=="string"){for(var O=pe.stack.split(`
`),oe=D.stack.split(`
`),Q=O.length-1,X=oe.length-1;Q>=1&&X>=0&&O[Q]!==oe[X];)X--;for(;Q>=1&&X>=0;Q--,X--)if(O[Q]!==oe[X]){if(Q!==1||X!==1)do if(Q--,X--,X<0||O[Q]!==oe[X]){var ae=`
`+O[Q].replace(" at new "," at ");return u.displayName&&ae.includes("<anonymous>")&&(ae=ae.replace("<anonymous>",u.displayName)),typeof u=="function"&&Ye.set(u,ae),ae}while(Q>=1&&X>=0);break}}}finally{ir=!1,sr.current=H,Xt(),Error.prepareStackTrace=F}var ve=u?u.displayName||u.name:"",Vr=ve?Ve(ve):"";return typeof u=="function"&&Ye.set(u,Vr),Vr}function Zt(u,S,E){return Mr(u,!1)}function eo(u){var S=u.prototype;return!!(S&&S.isReactComponent)}function Ge(u,S,E){if(u==null)return"";if(typeof u=="function")return Mr(u,eo(u));if(typeof u=="string")return Ve(u);switch(u){case f:return Ve("Suspense");case l:return Ve("SuspenseList")}if(typeof u=="object")switch(u.$$typeof){case d:return Zt(u.render);case m:return Ge(u.type,S,E);case x:{var D=u,F=D._payload,H=D._init;try{return Ge(H(F),S,E)}catch{}}}return""}var We=Object.prototype.hasOwnProperty,Rr={},Pr=w.ReactDebugCurrentFrame;function Ke(u){if(u){var S=u._owner,E=Ge(u.type,u._source,S?S.type:null);Pr.setExtraStackFrame(E)}else Pr.setExtraStackFrame(null)}function ro(u,S,E,D,F){{var H=Function.call.bind(We);for(var z in u)if(H(u,z)){var O=void 0;try{if(typeof u[z]!="function"){var oe=Error((D||"React class")+": "+E+" type `"+z+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof u[z]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw oe.name="Invariant Violation",oe}O=u[z](S,z,D,E,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(Q){O=Q}O&&!(O instanceof Error)&&(Ke(F),C("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",D||"React class",E,z,typeof O),Ke(null)),O instanceof Error&&!(O.message in Rr)&&(Rr[O.message]=!0,Ke(F),C("Failed %s type: %s",E,O.message),Ke(null))}}}var to=Array.isArray;function ar(u){return to(u)}function oo(u){{var S=typeof Symbol=="function"&&Symbol.toStringTag,E=S&&u[Symbol.toStringTag]||u.constructor.name||"Object";return E}}function so(u){try{return Dr(u),!1}catch{return!0}}function Dr(u){return""+u}function $r(u){if(so(u))return C("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",oo(u)),Dr(u)}var Ie=w.ReactCurrentOwner,no={key:!0,ref:!0,__self:!0,__source:!0},Or,Ar,cr;cr={};function io(u){if(We.call(u,"ref")){var S=Object.getOwnPropertyDescriptor(u,"ref").get;if(S&&S.isReactWarning)return!1}return u.ref!==void 0}function ao(u){if(We.call(u,"key")){var S=Object.getOwnPropertyDescriptor(u,"key").get;if(S&&S.isReactWarning)return!1}return u.key!==void 0}function co(u,S){if(typeof u.ref=="string"&&Ie.current&&S&&Ie.current.stateNode!==S){var E=q(Ie.current.type);cr[E]||(C('Component "%s" contains the string ref "%s". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',q(Ie.current.type),u.ref),cr[E]=!0)}}function lo(u,S){{var E=function(){Or||(Or=!0,C("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",S))};E.isReactWarning=!0,Object.defineProperty(u,"key",{get:E,configurable:!0})}}function uo(u,S){{var E=function(){Ar||(Ar=!0,C("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",S))};E.isReactWarning=!0,Object.defineProperty(u,"ref",{get:E,configurable:!0})}}var po=function(u,S,E,D,F,H,z){var O={$$typeof:r,type:u,key:S,ref:E,props:z,_owner:H};return O._store={},Object.defineProperty(O._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(O,"_self",{configurable:!1,enumerable:!1,writable:!1,value:D}),Object.defineProperty(O,"_source",{configurable:!1,enumerable:!1,writable:!1,value:F}),Object.freeze&&(Object.freeze(O.props),Object.freeze(O)),O};function fo(u,S,E,D,F){{var H,z={},O=null,oe=null;E!==void 0&&($r(E),O=""+E),ao(S)&&($r(S.key),O=""+S.key),io(S)&&(oe=S.ref,co(S,F));for(H in S)We.call(S,H)&&!no.hasOwnProperty(H)&&(z[H]=S[H]);if(u&&u.defaultProps){var Q=u.defaultProps;for(H in Q)z[H]===void 0&&(z[H]=Q[H])}if(O||oe){var X=typeof u=="function"?u.displayName||u.name||"Unknown":u;O&&lo(z,X),oe&&uo(z,X)}return po(u,O,oe,F,D,Ie.current,z)}}var lr=w.ReactCurrentOwner,zr=w.ReactDebugCurrentFrame;function ye(u){if(u){var S=u._owner,E=Ge(u.type,u._source,S?S.type:null);zr.setExtraStackFrame(E)}else zr.setExtraStackFrame(null)}var dr;dr=!1;function ur(u){return typeof u=="object"&&u!==null&&u.$$typeof===r}function Fr(){{if(lr.current){var u=q(lr.current.type);if(u)return`

Check the render method of \``+u+"`."}return""}}function mo(u){{if(u!==void 0){var S=u.fileName.replace(/^.*[\\\/]/,""),E=u.lineNumber;return`

Check your code at `+S+":"+E+"."}return""}}var Br={};function go(u){{var S=Fr();if(!S){var E=typeof u=="string"?u:u.displayName||u.name;E&&(S=`

Check the top-level render call using <`+E+">.")}return S}}function qr(u,S){{if(!u._store||u._store.validated||u.key!=null)return;u._store.validated=!0;var E=go(S);if(Br[E])return;Br[E]=!0;var D="";u&&u._owner&&u._owner!==lr.current&&(D=" It was passed a child from "+q(u._owner.type)+"."),ye(u),C('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',E,D),ye(null)}}function Hr(u,S){{if(typeof u!="object")return;if(ar(u))for(var E=0;E<u.length;E++){var D=u[E];ur(D)&&qr(D,S)}else if(ur(u))u._store&&(u._store.validated=!0);else if(u){var F=T(u);if(typeof F=="function"&&F!==u.entries)for(var H=F.call(u),z;!(z=H.next()).done;)ur(z.value)&&qr(z.value,S)}}}function ho(u){{var S=u.type;if(S==null||typeof S=="string")return;var E;if(typeof S=="function")E=S.propTypes;else if(typeof S=="object"&&(S.$$typeof===d||S.$$typeof===m))E=S.propTypes;else return;if(E){var D=q(S);ro(E,u.props,"prop",D,u)}else if(S.PropTypes!==void 0&&!dr){dr=!0;var F=q(S);C("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",F||"Unknown")}typeof S.getDefaultProps=="function"&&!S.getDefaultProps.isReactClassApproved&&C("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function xo(u){{for(var S=Object.keys(u.props),E=0;E<S.length;E++){var D=S[E];if(D!=="children"&&D!=="key"){ye(u),C("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",D),ye(null);break}}u.ref!==null&&(ye(u),C("Invalid attribute `ref` supplied to `React.Fragment`."),ye(null))}}function Ur(u,S,E,D,F,H){{var z=W(u);if(!z){var O="";(u===void 0||typeof u=="object"&&u!==null&&Object.keys(u).length===0)&&(O+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var oe=mo(F);oe?O+=oe:O+=Fr();var Q;u===null?Q="null":ar(u)?Q="array":u!==void 0&&u.$$typeof===r?(Q="<"+(q(u.type)||"Unknown")+" />",O=" Did you accidentally export a JSX literal instead of a component?"):Q=typeof u,C("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",Q,O)}var X=fo(u,S,E,F,H);if(X==null)return X;if(z){var ae=S.children;if(ae!==void 0)if(D)if(ar(ae)){for(var ve=0;ve<ae.length;ve++)Hr(ae[ve],u);Object.freeze&&Object.freeze(ae)}else C("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Hr(ae,u)}return u===s?xo(X):ho(X),X}}function bo(u,S,E){return Ur(u,S,E,!0)}function yo(u,S,E){return Ur(u,S,E,!1)}var vo=yo,So=bo;Ne.Fragment=s,Ne.jsx=vo,Ne.jsxs=So}()),Ne}(function(e){process.env.NODE_ENV==="production"?e.exports=jo():e.exports=No()})(Io);const ko={small:n.css(["padding:",";font-size:",";min-height:20px;min-width:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs,({dot:e})=>e?"8px":"20px"),medium:n.css(["padding:",";font-size:",";min-height:24px;min-width:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm,({dot:e})=>e?"10px":"24px"),large:n.css(["padding:",";font-size:",";min-height:32px;min-width:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md,({dot:e})=>e?"12px":"32px")},Lo=(e,r,t=!1)=>n.css(["",""],({theme:s})=>{let i,c,a;switch(e){case"primary":i=r?s.colors.primary:`${s.colors.primary}20`,c=r?s.colors.textInverse:s.colors.primary,a=s.colors.primary;break;case"secondary":i=r?s.colors.secondary:`${s.colors.secondary}20`,c=r?s.colors.textInverse:s.colors.secondary,a=s.colors.secondary;break;case"success":i=r?s.colors.success:`${s.colors.success}20`,c=r?s.colors.textInverse:s.colors.success,a=s.colors.success;break;case"warning":i=r?s.colors.warning:`${s.colors.warning}20`,c=r?s.colors.textInverse:s.colors.warning,a=s.colors.warning;break;case"error":i=r?s.colors.error:`${s.colors.error}20`,c=r?s.colors.textInverse:s.colors.error,a=s.colors.error;break;case"info":i=r?s.colors.info:`${s.colors.info}20`,c=r?s.colors.textInverse:s.colors.info,a=s.colors.info;break;case"neutral":i=r?s.colors.textSecondary:`${s.colors.textSecondary}10`,c=r?s.colors.textInverse:s.colors.textSecondary,a=s.colors.textSecondary;break;default:i=r?s.colors.textSecondary:`${s.colors.textSecondary}20`,c=r?s.colors.textInverse:s.colors.textSecondary,a=s.colors.textSecondary}return t?`
          background-color: transparent;
          color: ${a};
          border: 1px solid ${a};
        `:`
        background-color: ${i};
        color: ${c};
        border: 1px solid transparent;
      `}),Et=n.span.withConfig({displayName:"IconContainer",componentId:"sc-10uskub-0"})(["display:flex;align-items:center;justify-content:center;"]),_o=n(Et).withConfig({displayName:"StartIcon",componentId:"sc-10uskub-1"})(["margin-right:",";"],({theme:e})=>e.spacing.xxs),Mo=n(Et).withConfig({displayName:"EndIcon",componentId:"sc-10uskub-2"})(["margin-left:",";"],({theme:e})=>e.spacing.xxs),Ro=n.span.withConfig({displayName:"StyledBadge",componentId:"sc-10uskub-3"})(["display:",";align-items:center;justify-content:center;border-radius:",";font-weight:",";white-space:nowrap;"," "," "," "," ",""],({inline:e})=>e?"inline-flex":"flex",({theme:e,rounded:r,dot:t})=>t?"50%":r?"9999px":e.borderRadius.sm,({theme:e})=>e.fontWeights.medium,({size:e})=>ko[e],({variant:e,solid:r,outlined:t})=>Lo(e,r,t||!1),({dot:e})=>e&&n.css(["padding:0;height:8px;width:8px;"]),({counter:e})=>e&&n.css(["min-width:1.5em;height:1.5em;padding:0 0.5em;border-radius:1em;"]),({clickable:e})=>e&&n.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),De=({children:e,variant:r="default",size:t="medium",solid:s=!1,className:i="",style:c,onClick:a,rounded:p=!1,dot:d=!1,counter:f=!1,outlined:l=!1,startIcon:m,endIcon:x,max:h,inline:g=!0})=>{let b=e;return f&&typeof e=="number"&&h!==void 0&&e>h&&(b=`${h}+`),o.jsx(Ro,{variant:r,size:t,solid:s,clickable:!!a,className:i,style:c,onClick:a,rounded:p,dot:d,counter:f,outlined:l,inline:g,children:!d&&o.jsxs(o.Fragment,{children:[m&&o.jsx(_o,{children:m}),b,x&&o.jsx(Mo,{children:x})]})})},Po=n.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Do=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1rze74q-0"})(["width:16px;height:16px;border:2px solid rgba(255,255,255,0.3);border-radius:50%;border-top-color:#fff;animation:"," 0.8s linear infinite;margin-right:",";"],Po,({theme:e})=>e.spacing.xs),$o={small:n.css(["padding:",";font-size:",";min-height:32px;"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.xs),medium:n.css(["padding:",";font-size:",";min-height:40px;"],({theme:e})=>`${e.spacing.xs} ${e.spacing.md}`,({theme:e})=>e.fontSizes.sm),large:n.css(["padding:",";font-size:",";min-height:48px;"],({theme:e})=>`${e.spacing.sm} ${e.spacing.lg}`,({theme:e})=>e.fontSizes.md)},Oo={primary:n.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.primaryDark,({theme:e})=>e.colors.primaryDark),secondary:n.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:",";transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){background-color:",";transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.textPrimary||e.colors.textInverse||"#fff",({theme:e})=>e.colors.secondaryDark,({theme:e})=>e.colors.secondaryDark),outline:n.css(["background-color:transparent;color:",";border:1px solid ",";&:hover:not(:disabled){background-color:","0d;transform:translateY(-1px);}&:active:not(:disabled){background-color:","1a;transform:translateY(0);}"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),text:n.css(["background-color:transparent;color:",";border:none;padding-left:",";padding-right:",";&:hover:not(:disabled){background-color:","0d;}&:active:not(:disabled){background-color:","1a;}"],({theme:e})=>e.colors.primary,({theme:e})=>e.spacing.xs,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),success:n.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.success,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.success),danger:n.css(["background-color:",";color:",";border:none;&:hover:not(:disabled){background-color:","dd;transform:translateY(-1px);box-shadow:0 4px 6px rgba(0,0,0,0.1);}&:active:not(:disabled){transform:translateY(0);box-shadow:none;}"],({theme:e})=>e.colors.error,({theme:e})=>e.colors.textInverse||"#fff",({theme:e})=>e.colors.error)},Ao=n.button.withConfig({displayName:"StyledButton",componentId:"sc-1rze74q-1"})(["display:inline-flex;align-items:center;justify-content:center;border-radius:",";font-weight:",";cursor:pointer;transition:all ",";position:relative;overflow:hidden;"," "," "," &:disabled{opacity:0.6;cursor:not-allowed;box-shadow:none;transform:translateY(0);}"," ",""],({theme:e})=>e.borderRadius.sm,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({size:e="medium"})=>$o[e],({variant:e="primary"})=>Oo[e],({fullWidth:e})=>e&&n.css(["width:100%;"]),({$hasStartIcon:e})=>e&&n.css(["& > *:first-child{margin-right:",";}"],({theme:r})=>r.spacing.xs),({$hasEndIcon:e})=>e&&n.css(["& > *:last-child{margin-left:",";}"],({theme:r})=>r.spacing.xs)),zo=n.div.withConfig({displayName:"ButtonContent",componentId:"sc-1rze74q-2"})(["display:flex;align-items:center;justify-content:center;"]),ne=({children:e,variant:r="primary",disabled:t=!1,loading:s=!1,size:i="medium",fullWidth:c=!1,startIcon:a,endIcon:p,onClick:d,className:f="",type:l="button",...m})=>o.jsx(Ao,{variant:r,disabled:t||s,size:i,fullWidth:c,onClick:d,className:f,type:l,$hasStartIcon:!!a&&!s,$hasEndIcon:!!p&&!s,...m,children:o.jsxs(zo,{children:[s&&o.jsx(Do,{}),!s&&a,e,!s&&p]})}),Fo=n.div.withConfig({displayName:"InputWrapper",componentId:"sc-uv3rzi-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Bo=n.label.withConfig({displayName:"Label",componentId:"sc-uv3rzi-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),qo=n.div.withConfig({displayName:"InputContainer",componentId:"sc-uv3rzi-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:t,isFocused:s})=>r?e.colors.error:t?e.colors.success:s?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&n.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:t,hasSuccess:s})=>e&&n.css(["box-shadow:0 0 0 2px ",";"],t?`${r.colors.error}33`:s?`${r.colors.success}33`:`${r.colors.primary}33`),({$size:e})=>{switch(e){case"small":return n.css(["height:32px;"]);case"large":return n.css(["height:48px;"]);default:return n.css(["height:40px;"])}}),Wr=n.div.withConfig({displayName:"IconContainer",componentId:"sc-uv3rzi-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Ho=n.input.withConfig({displayName:"StyledInput",componentId:"sc-uv3rzi-4"})(["flex:1;border:none;background:transparent;color:",";width:100%;outline:none;&:disabled{cursor:not-allowed;}&::placeholder{color:",";}"," "," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.textDisabled,({hasStartIcon:e})=>e&&n.css(["padding-left:0;"]),({hasEndIcon:e})=>e&&n.css(["padding-right:0;"]),({$size:e,theme:r})=>e==="small"?n.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?n.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):n.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),Uo=n.button.withConfig({displayName:"ClearButton",componentId:"sc-uv3rzi-5"})(["background:none;border:none;cursor:pointer;color:",";padding:0 ",";display:flex;align-items:center;justify-content:center;&:hover{color:",";}&:focus{outline:none;}"],({theme:e})=>e.colors.textDisabled,({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),Vo=n.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-uv3rzi-6"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:t})=>r?e.colors.error:t?e.colors.success:e.colors.textSecondary),be=({value:e,onChange:r,placeholder:t="",disabled:s=!1,error:i="",type:c="text",name:a="",id:p="",className:d="",required:f=!1,autoComplete:l="",label:m="",helperText:x="",startIcon:h,endIcon:g,loading:b=!1,success:T=!1,clearable:w=!1,onClear:C,maxLength:M,showCharCount:A=!1,size:N="medium",fullWidth:k=!1,...R})=>{const[j,L]=y.useState(!1),W=y.useRef(null),V=()=>{C?C():r(""),W.current&&W.current.focus()},_=ee=>{L(!0),R.onFocus&&R.onFocus(ee)},q=ee=>{L(!1),R.onBlur&&R.onBlur(ee)},J=w&&e&&!s,Z=(e==null?void 0:e.length)||0,le=A||M!==void 0&&M>0;return o.jsxs(Fo,{className:d,fullWidth:k,children:[m&&o.jsxs(Bo,{htmlFor:p,children:[m,f&&" *"]}),o.jsxs(qo,{hasError:!!i,hasSuccess:!!T,disabled:!!s,$size:N,hasStartIcon:!!h,hasEndIcon:!!(g||J),isFocused:!!j,children:[h&&o.jsx(Wr,{children:h}),o.jsx(Ho,{ref:W,type:c,value:e,onChange:ee=>r(ee.target.value),placeholder:t,disabled:!!(s||b),name:a,id:p,required:!!f,autoComplete:l,hasStartIcon:!!h,hasEndIcon:!!(g||J),$size:N,maxLength:M,onFocus:_,onBlur:q,...R}),J&&o.jsx(Uo,{type:"button",onClick:V,tabIndex:-1,children:"✕"}),g&&o.jsx(Wr,{children:g})]}),(i||x||le)&&o.jsxs(Vo,{hasError:!!i,hasSuccess:!!T,children:[o.jsx("div",{children:i||x}),le&&o.jsxs("div",{children:[Z,M!==void 0&&`/${M}`]})]})]})},Kr={small:n.css(["height:100px;"]),medium:n.css(["height:200px;"]),large:n.css(["height:300px;"]),custom:e=>n.css(["height:",";width:",";"],e.customHeight,e.customWidth||"100%")},Yo={default:n.css(["background-color:",";border-radius:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.md),card:n.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm),text:n.css(["background-color:transparent;height:auto !important;min-height:1.5em;"]),list:n.css(["background-color:",";border-radius:",";margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.spacing.sm)},Go=n.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),Wo=n.div.withConfig({displayName:"Container",componentId:"sc-12vczt5-0"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;"," ",""],({size:e,customHeight:r,customWidth:t})=>e==="custom"?Kr.custom({customHeight:r,customWidth:t}):Kr[e],({variant:e})=>Yo[e]),Ko=n.div.withConfig({displayName:"Spinner",componentId:"sc-12vczt5-1"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:"," 1s linear infinite;margin-bottom:",";"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary,Go,({theme:e})=>e.spacing.sm),Qo=n.div.withConfig({displayName:"Text",componentId:"sc-12vczt5-2"})(["color:",";font-size:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm),It=({variant:e="default",size:r="medium",height:t="200px",width:s="",text:i="Loading...",showSpinner:c=!0,className:a=""})=>o.jsxs(Wo,{variant:e,size:r,customHeight:t,customWidth:s,className:a,children:[c&&o.jsx(Ko,{}),i&&o.jsx(Qo,{children:i})]}),Xo=n.div.withConfig({displayName:"SelectWrapper",componentId:"sc-wvk2um-0"})(["display:flex;flex-direction:column;width:",";position:relative;"],({fullWidth:e})=>e?"100%":"auto"),Jo=n.label.withConfig({displayName:"Label",componentId:"sc-wvk2um-1"})(["font-size:",";color:",";margin-bottom:",";font-weight:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.spacing.xxs,({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500}),Zo=n.div.withConfig({displayName:"SelectContainer",componentId:"sc-wvk2um-2"})(["display:flex;align-items:center;position:relative;width:100%;border-radius:",";border:1px solid ",";background-color:",";transition:all ",";"," "," ",""],({theme:e})=>e.borderRadius.sm,({theme:e,hasError:r,hasSuccess:t,isFocused:s})=>r?e.colors.error:t?e.colors.success:s?e.colors.primary:e.colors.border,({theme:e})=>e.colors.surface,({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"0.2s ease"},({disabled:e,theme:r})=>e&&n.css(["opacity:0.6;background-color:",";cursor:not-allowed;"],r.colors.background),({isFocused:e,theme:r,hasError:t,hasSuccess:s})=>e&&n.css(["box-shadow:0 0 0 2px ",";"],t?`${r.colors.error}33`:s?`${r.colors.success}33`:`${r.colors.primary}33`),({$size:e})=>{switch(e){case"small":return n.css(["height:32px;"]);case"large":return n.css(["height:48px;"]);default:return n.css(["height:40px;"])}}),es=n.div.withConfig({displayName:"IconContainer",componentId:"sc-wvk2um-3"})(["display:flex;align-items:center;justify-content:center;padding:0 ",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.colors.textSecondary),rs=n.select.withConfig({displayName:"StyledSelect",componentId:"sc-wvk2um-4"})(["flex:1;border:none;background:transparent;color:",`;width:100%;outline:none;appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right `," center;background-size:16px;padding-right:",";&:disabled{cursor:not-allowed;}"," ",""],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.xl,({hasStartIcon:e})=>e&&n.css(["padding-left:0;"]),({$size:e,theme:r})=>e==="small"?n.css(["font-size:",";padding:"," ",";"],r.fontSizes.xs,r.spacing.xxs,r.spacing.xs):e==="large"?n.css(["font-size:",";padding:"," ",";"],r.fontSizes.md,r.spacing.sm,r.spacing.md):n.css(["font-size:",";padding:"," ",";"],r.fontSizes.sm,r.spacing.xs,r.spacing.sm)),ts=n.div.withConfig({displayName:"HelperTextContainer",componentId:"sc-wvk2um-5"})(["display:flex;justify-content:space-between;margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xxs,({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r,hasSuccess:t})=>r?e.colors.error:t?e.colors.success:e.colors.textSecondary),os=n.optgroup.withConfig({displayName:"OptionGroup",componentId:"sc-wvk2um-6"})(["font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>e.colors.textPrimary),we=({options:e,value:r,onChange:t,disabled:s=!1,error:i="",name:c="",id:a="",className:p="",required:d=!1,placeholder:f="",label:l="",helperText:m="",size:x="medium",fullWidth:h=!0,loading:g=!1,success:b=!1,startIcon:T,...w})=>{const[C,M]=y.useState(!1),A=L=>{M(!0),w.onFocus&&w.onFocus(L)},N=L=>{M(!1),w.onBlur&&w.onBlur(L)},k={},R=[];e.forEach(L=>{L.group?(k[L.group]||(k[L.group]=[]),k[L.group].push(L)):R.push(L)});const j=Object.keys(k).length>0;return o.jsxs(Xo,{className:p,fullWidth:h,children:[l&&o.jsxs(Jo,{htmlFor:a,children:[l,d&&" *"]}),o.jsxs(Zo,{hasError:!!i,hasSuccess:!!b,disabled:!!(s||g),$size:x,hasStartIcon:!!T,isFocused:!!C,children:[T&&o.jsx(es,{children:T}),o.jsxs(rs,{value:r,onChange:L=>t(L.target.value),disabled:!!(s||g),name:c,id:a,required:!!d,hasStartIcon:!!T,$size:x,onFocus:A,onBlur:N,...w,children:[f&&o.jsx("option",{value:"",disabled:!0,children:f}),j?o.jsxs(o.Fragment,{children:[R.map(L=>o.jsx("option",{value:L.value,disabled:L.disabled,children:L.label},L.value)),Object.entries(k).map(([L,W])=>o.jsx(os,{label:L,children:W.map(V=>o.jsx("option",{value:V.value,disabled:V.disabled,children:V.label},V.value))},L))]}):e.map(L=>o.jsx("option",{value:L.value,disabled:L.disabled,children:L.label},L.value))]})]}),(i||m)&&o.jsx(ts,{hasError:!!i,hasSuccess:!!b,children:o.jsx("div",{children:i||m})})]})},Qr={small:"8px",medium:"12px",large:"16px"},ss={small:n.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.xs,({theme:e})=>e.spacing.xs),medium:n.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.sm),large:n.css(["font-size:",";margin-left:",";"],({theme:e})=>e.fontSizes.md,({theme:e})=>e.spacing.md)},ns=n.css(["@keyframes pulse{0%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0.7);}70%{transform:scale(1);box-shadow:0 0 0 6px rgba(var(--pulse-color),0);}100%{transform:scale(0.95);box-shadow:0 0 0 0 rgba(var(--pulse-color),0);}}animation:pulse 2s infinite;"]),is=n.div.withConfig({displayName:"Container",componentId:"sc-gwj3m-0"})(["display:inline-flex;align-items:center;"]),as=n.div.withConfig({displayName:"Indicator",componentId:"sc-gwj3m-1"})(["border-radius:50%;width:",";height:",";",""],({size:e})=>Qr[e],({size:e})=>Qr[e],({status:e,theme:r,pulse:t})=>{let s,i;switch(e){case"success":s=r.colors.success,i="76, 175, 80";break;case"error":s=r.colors.error,i="244, 67, 54";break;case"warning":s=r.colors.warning,i="255, 152, 0";break;case"info":s=r.colors.info,i="33, 150, 243";break;default:s=r.colors.textSecondary,i="158, 158, 158"}return n.css(["background-color:",";",""],s,t&&n.css(["--pulse-color:",";",""],i,ns))}),cs=n.span.withConfig({displayName:"Label",componentId:"sc-gwj3m-2"})([""," ",""],({size:e})=>ss[e],({status:e,theme:r})=>{let t;switch(e){case"success":t=r.colors.success;break;case"error":t=r.colors.error;break;case"warning":t=r.colors.warning;break;case"info":t=r.colors.info;break;default:t=r.colors.textSecondary}return n.css(["color:",";font-weight:",";"],t,r.fontWeights.medium)}),ls=({status:e,size:r="medium",pulse:t=!1,showLabel:s=!1,label:i="",className:c=""})=>{const a=i||e.charAt(0).toUpperCase()+e.slice(1);return o.jsxs(is,{className:c,children:[o.jsx(as,{status:e,size:r,pulse:t}),s&&o.jsx(cs,{status:e,size:r,children:a})]})},ds={small:n.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xxs} ${e.spacing.xs}`,({theme:e})=>e.fontSizes.xs),medium:n.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.xs} ${e.spacing.sm}`,({theme:e})=>e.fontSizes.sm),large:n.css(["padding:",";font-size:",";"],({theme:e})=>`${e.spacing.sm} ${e.spacing.md}`,({theme:e})=>e.fontSizes.md)},us=e=>n.css(["",""],({theme:r})=>{let t,s,i;switch(e){case"primary":t=`${r.colors.primary}10`,s=r.colors.primary,i=`${r.colors.primary}30`;break;case"secondary":t=`${r.colors.secondary}10`,s=r.colors.secondary,i=`${r.colors.secondary}30`;break;case"success":t=`${r.colors.success}10`,s=r.colors.success,i=`${r.colors.success}30`;break;case"warning":t=`${r.colors.warning}10`,s=r.colors.warning,i=`${r.colors.warning}30`;break;case"error":t=`${r.colors.error}10`,s=r.colors.error,i=`${r.colors.error}30`;break;case"info":t=`${r.colors.info}10`,s=r.colors.info,i=`${r.colors.info}30`;break;default:t=`${r.colors.textSecondary}10`,s=r.colors.textSecondary,i=`${r.colors.textSecondary}30`}return`
        background-color: ${t};
        color: ${s};
        border: 1px solid ${i};
      `}),ps=n.span.withConfig({displayName:"StyledTag",componentId:"sc-11nmnw9-0"})(["display:inline-flex;align-items:center;border-radius:",";font-weight:",";"," "," ",""],({theme:e})=>e.borderRadius.pill,({theme:e})=>e.fontWeights.medium,({size:e})=>ds[e],({variant:e})=>us(e),({clickable:e})=>e&&n.css(["cursor:pointer;transition:opacity ",";&:hover{opacity:0.8;}&:active{opacity:0.6;}"],({theme:r})=>r.transitions.fast)),fs=n.button.withConfig({displayName:"RemoveButton",componentId:"sc-11nmnw9-1"})(["display:inline-flex;align-items:center;justify-content:center;background:none;border:none;cursor:pointer;color:inherit;opacity:0.7;margin-left:",";padding:0;"," &:hover{opacity:1;}"],({theme:e})=>e.spacing.xs,({size:e,theme:r})=>{const t={small:"12px",medium:"14px",large:"16px"};return`
      width: ${t[e]};
      height: ${t[e]};
      font-size: ${r.fontSizes.xs};
    `}),ms=({children:e,variant:r="default",size:t="medium",removable:s=!1,onRemove:i,className:c="",onClick:a})=>{const p=d=>{d.stopPropagation(),i==null||i()};return o.jsxs(ps,{variant:r,size:t,clickable:!!a,className:c,onClick:a,children:[e,s&&o.jsx(fs,{size:t,onClick:p,children:"×"})]})},gs=n.div.withConfig({displayName:"TimePickerContainer",componentId:"sc-v5w9zw-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),hs=n.label.withConfig({displayName:"Label",componentId:"sc-v5w9zw-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),xs=n.input.withConfig({displayName:"TimeInput",componentId:"sc-v5w9zw-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),bs=({id:e,name:r,value:t,onChange:s,label:i,required:c=!1,disabled:a=!1,className:p,placeholder:d="HH:MM",min:f,max:l})=>o.jsxs(gs,{className:p,children:[i&&o.jsxs(hs,{htmlFor:e,children:[i,c&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsx(xs,{id:e,name:r,type:"time",value:t,onChange:s,required:c,disabled:a,placeholder:d,min:f,max:l})]}),ys=bs,tr=()=>Intl.DateTimeFormat().resolvedOptions().timeZone,xr=(e,r=new Date)=>{const i=new Intl.DateTimeFormat("en",{timeZone:e,timeZoneName:"short"}).formatToParts(r).find(c=>c.type==="timeZoneName");return(i==null?void 0:i.value)||e},br=(e,r)=>{const t=r||tr(),[s,i]=e.split(":").map(Number),c=new Date,a=c.getFullYear(),p=String(c.getMonth()+1).padStart(2,"0"),d=String(c.getDate()).padStart(2,"0"),f=`${String(s).padStart(2,"0")}:${String(i).padStart(2,"0")}:00`,l=`${a}-${p}-${d}T${f}`,m=new Date(l),x=new Date,h=new Date(x.toLocaleString("en-US",{timeZone:"America/New_York"})),b=new Date(x.toLocaleString("en-US",{timeZone:t})).getTime()-h.getTime();return new Date(m.getTime()+b).toLocaleTimeString("en-GB",{timeZone:t,hour:"2-digit",minute:"2-digit",hour12:!1})},vs=(e,r)=>{const t=new Date;return new Date(`${t.toDateString()} ${e}:00`).toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1})},Ze=e=>{const r=e||tr(),t=new Date,s=t.toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=t.toLocaleTimeString("en-GB",{timeZone:r,hour:"2-digit",minute:"2-digit",hour12:!1}),c=xr("America/New_York",t),a=xr(r,t);return{nyTime:s,localTime:i,nyTimezone:c,localTimezone:a,formatted:`${s} ${c} | ${i} ${a}`}},Ss=()=>{const r=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1});return Me(r)},ws=()=>{const e=new Date;return new Date(e.toLocaleString("en-US",{timeZone:"America/New_York"}))},jt=e=>{const r=Math.floor(e/60),t=e%60;let s="";return r>0?s=t>0?`${r}h ${t}m`:`${r}h`:s=`${t}m`,{totalMinutes:e,hours:r,minutes:t,formatted:s}},Pe=e=>{const t=new Date().toLocaleString("en-US",{timeZone:"America/New_York"}),s=new Date(t),[i,c]=e.split(":").map(Number),a=new Date(s);a.setHours(i,c,0,0),a<=s&&a.setDate(a.getDate()+1);const p=a.getTime()-s.getTime(),d=Math.floor(p/(1e3*60));return jt(d)},Cs=e=>Pe(e),or=(e,r,t)=>{const s=t||tr(),i=br(e,s),c=br(r,s),a=xr(s);return{nyStart:e,nyEnd:r,localStart:i,localEnd:c,formatted:`${e}-${r} NY | ${i}-${c} ${a}`}},Nt=(e,r)=>{const s=new Date().toLocaleTimeString("en-US",{timeZone:"America/New_York",hour:"2-digit",minute:"2-digit",hour12:!1}),i=Me(s),c=Me(e),a=Me(r);return i>=c&&i<=a},Me=e=>{const[r,t]=e.split(":").map(Number);return r*60+t},Ts=e=>{const r=Math.floor(e/60),t=e%60;return`${r.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},kt=e=>{const r=e.localTimezone.includes("GMT")?"🇮🇪":"🌍";return`${e.localTime} ${r} | ${e.nyTime} 🇺🇸`},Es=e=>`${e.localTime} Local (${e.localTimezone}) | ${e.nyTime} NY (${e.nyTimezone})`,Is=(e,r,t,s)=>{const i=Nt(r,t),c=or(r,t,s);if(i)return{isActive:!0,timeRemaining:Cs(t),sessionTime:c,status:"active"};const a=Pe(r);return{isActive:!1,timeUntilStart:a,sessionTime:c,status:a.totalMinutes<24*60?"upcoming":"ended"}},ge=n.div.withConfig({displayName:"TimeContainer",componentId:"sc-10dqpqu-0"})(["display:flex;align-items:center;gap:",";font-family:'SF Mono','Monaco','Inconsolata','Roboto Mono',monospace;font-weight:600;"],({format:e})=>e==="mobile"?"4px":"8px"),Ce=n.span.withConfig({displayName:"NYTime",componentId:"sc-10dqpqu-1"})(["color:#3b82f6;font-size:inherit;"]),Te=n.span.withConfig({displayName:"LocalTime",componentId:"sc-10dqpqu-2"})(["color:#10b981;font-size:inherit;"]),Ee=n.span.withConfig({displayName:"Separator",componentId:"sc-10dqpqu-3"})(["color:#6b7280;font-size:inherit;"]),er=n.span.withConfig({displayName:"Timezone",componentId:"sc-10dqpqu-4"})(["color:#9ca3af;font-size:0.85em;font-weight:500;"]),pr=n.span.withConfig({displayName:"LiveIndicator",componentId:"sc-10dqpqu-5"})(["color:#ef4444;font-size:0.75em;font-weight:bold;animation:pulse 2s infinite;@keyframes pulse{0%,100%{opacity:1;}50%{opacity:0.5;}}"]),Xr=n.div.withConfig({displayName:"CountdownContainer",componentId:"sc-10dqpqu-6"})(["display:flex;align-items:center;gap:8px;"]),Jr=n.span.withConfig({displayName:"CountdownValue",componentId:"sc-10dqpqu-7"})(["color:#f59e0b;font-weight:bold;"]),fr=n.span.withConfig({displayName:"CountdownLabel",componentId:"sc-10dqpqu-8"})(["color:#9ca3af;font-size:0.9em;"]),js=({format:e,showLive:r,updateInterval:t})=>{const[s,i]=y.useState(Ze());return y.useEffect(()=>{const c=setInterval(()=>{i(Ze())},t*1e3);return()=>clearInterval(c)},[t]),e==="mobile"?o.jsxs(ge,{format:e,children:[o.jsx("span",{children:kt(s)}),r&&o.jsx(pr,{children:"LIVE"})]}):e==="compact"?o.jsxs(ge,{format:e,children:[o.jsx(Ce,{children:s.nyTime}),o.jsx(Ee,{children:"|"}),o.jsx(Te,{children:s.localTime}),r&&o.jsx(pr,{children:"LIVE"})]}):o.jsxs(ge,{format:e,children:[o.jsx(Ce,{children:s.nyTime}),o.jsx(er,{children:s.nyTimezone}),o.jsx(Ee,{children:"|"}),o.jsx(Te,{children:s.localTime}),o.jsx(er,{children:s.localTimezone}),r&&o.jsx(pr,{children:"LIVE"})]})},Ns=({nyTime:e,format:r})=>{const t=Ze(),s=or(e,e);return r==="mobile"?o.jsx(ge,{format:r,children:o.jsxs("span",{children:[s.localStart," 🇮🇪 | ",e," 🇺🇸"]})}):r==="compact"?o.jsxs(ge,{format:r,children:[o.jsx(Ce,{children:e}),o.jsx(Ee,{children:"|"}),o.jsx(Te,{children:s.localStart})]}):o.jsxs(ge,{format:r,children:[o.jsx(Ce,{children:e}),o.jsx(er,{children:t.nyTimezone}),o.jsx(Ee,{children:"|"}),o.jsx(Te,{children:s.localStart}),o.jsx(er,{children:t.localTimezone})]})},ks=({targetNYTime:e,format:r,updateInterval:t})=>{const[s,i]=y.useState(Pe(e));return y.useEffect(()=>{const c=setInterval(()=>{i(Pe(e))},t*1e3);return()=>clearInterval(c)},[e,t]),r==="mobile"?o.jsxs(Xr,{children:[o.jsx(Jr,{children:s.formatted}),o.jsxs(fr,{children:["until ",e]})]}):o.jsxs(Xr,{children:[o.jsx(fr,{children:"Next in:"}),o.jsx(Jr,{children:s.formatted}),o.jsxs(fr,{children:["(",e," NY)"]})]})},Ls=({sessionStart:e,sessionEnd:r,format:t})=>{const s=or(e,r);return t==="mobile"?o.jsx(ge,{format:t,children:o.jsx("span",{children:s.formatted})}):t==="compact"?o.jsxs(ge,{format:t,children:[o.jsxs(Ce,{children:[e,"-",r]}),o.jsx(Ee,{children:"|"}),o.jsxs(Te,{children:[s.localStart,"-",s.localEnd]})]}):o.jsxs(ge,{format:t,children:[o.jsx("div",{children:o.jsxs(Ce,{children:[e,"-",r," NY"]})}),o.jsx(Ee,{children:"|"}),o.jsx("div",{children:o.jsxs(Te,{children:[s.localStart,"-",s.localEnd," Local"]})})]})},_s=e=>{const{mode:r="current",nyTime:t,targetNYTime:s,sessionStart:i,sessionEnd:c,format:a="desktop",showLive:p=!1,className:d,updateInterval:f=1}=e,l={className:d,style:{fontSize:a==="mobile"?"14px":a==="compact"?"13px":"14px"}};switch(r){case"static":return t?o.jsx("div",{...l,children:o.jsx(Ns,{nyTime:t,format:a})}):(console.warn("DualTimeDisplay: nyTime is required for static mode"),null);case"countdown":return s?o.jsx("div",{...l,children:o.jsx(ks,{targetNYTime:s,format:a,updateInterval:f})}):(console.warn("DualTimeDisplay: targetNYTime is required for countdown mode"),null);case"session":return!i||!c?(console.warn("DualTimeDisplay: sessionStart and sessionEnd are required for session mode"),null):o.jsx("div",{...l,children:o.jsx(Ls,{sessionStart:i,sessionEnd:c,format:a})});case"current":default:return o.jsx("div",{...l,children:o.jsx(js,{format:a,showLive:p,updateInterval:f})})}},Ms=n.div.withConfig({displayName:"SelectContainer",componentId:"sc-w0dp8e-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>e.spacing.xs),Rs=n.label.withConfig({displayName:"Label",componentId:"sc-w0dp8e-1"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.fontWeights.medium,({theme:e})=>e.colors.textPrimary),Ps=n.select.withConfig({displayName:"Select",componentId:"sc-w0dp8e-2"})(["padding:",";border:1px solid ",";border-radius:",";font-size:",";color:",";background-color:",";transition:border-color ",";&:focus{outline:none;border-color:",";}&:disabled{background-color:",";cursor:not-allowed;}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.fontSizes.md,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.surface,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.chartGrid),Ds=({id:e,name:r,value:t,onChange:s,options:i,label:c,required:a=!1,disabled:p=!1,className:d,placeholder:f})=>o.jsxs(Ms,{className:d,children:[c&&o.jsxs(Rs,{htmlFor:e,children:[c,a&&o.jsx("span",{style:{color:"red"},children:" *"})]}),o.jsxs(Ps,{id:e,name:r,value:t,onChange:s,required:a,disabled:p,children:[f&&o.jsx("option",{value:"",disabled:!0,children:f}),i.map(l=>o.jsx("option",{value:l.value,children:l.label},l.value))]})]}),$s=Ds,Os=n.span.withConfig({displayName:"StyledLoadingCell",componentId:"sc-1i0qdjp-0"})(["display:inline-flex;align-items:center;justify-content:flex-end;opacity:0.6;position:relative;"," border-radius:",";&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"],({$size:e,theme:r})=>{var t,s,i,c,a,p,d,f,l;switch(e){case"small":return n.css(["font-size:",";padding:"," ",";"],((t=r.fontSizes)==null?void 0:t.xs)||"12px",((s=r.spacing)==null?void 0:s.xxs)||"2px",((i=r.spacing)==null?void 0:i.xs)||"4px");case"large":return n.css(["font-size:",";padding:"," ",";"],((c=r.fontSizes)==null?void 0:c.lg)||"18px",((a=r.spacing)==null?void 0:a.sm)||"8px",((p=r.spacing)==null?void 0:p.md)||"12px");default:return n.css(["font-size:",";padding:"," ",";"],((d=r.fontSizes)==null?void 0:d.sm)||"14px",((f=r.spacing)==null?void 0:f.xs)||"4px",((l=r.spacing)==null?void 0:l.sm)||"8px")}},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"}),As=n.span.withConfig({displayName:"LoadingPlaceholder",componentId:"sc-1i0qdjp-1"})(["display:inline-block;width:",";height:1em;background-color:currentColor;opacity:0.3;border-radius:2px;"],({$width:e})=>e||"60px"),zs=e=>{const{size:r="medium",width:t,className:s,"aria-label":i}=e;return o.jsx(Os,{className:s,$size:r,$width:t,"aria-label":i||"Loading data",role:"cell","aria-busy":"true",children:o.jsx(As,{$width:t})})},Fs=zs,Bs=n.keyframes(["0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}"]),qs=n.keyframes(["0%{background-position:0% 0%;}100%{background-position:100% 100%;}"]),Hs=n.div.withConfig({displayName:"StyledSpinner",componentId:"sc-1hoaoss-0"})(["display:inline-block;position:relative;"," &::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;border-radius:50%;border:2px solid transparent;"," animation:"," ","s linear infinite;}",""],({$size:e})=>{switch(e){case"xs":return n.css(["width:16px;height:16px;"]);case"sm":return n.css(["width:20px;height:20px;"]);case"md":return n.css(["width:32px;height:32px;"]);case"lg":return n.css(["width:48px;height:48px;"]);case"xl":return n.css(["width:64px;height:64px;"]);default:return n.css(["width:32px;height:32px;"])}},({$variant:e,theme:r})=>{var t,s,i,c,a,p;switch(e){case"primary":return n.css(["border-top-color:",";border-right-color:",";"],((t=r.colors)==null?void 0:t.primary)||"#dc2626",((s=r.colors)==null?void 0:s.primary)||"#dc2626");case"secondary":return n.css(["border-top-color:",";border-right-color:",";"],((i=r.colors)==null?void 0:i.textSecondary)||"#9ca3af",((c=r.colors)==null?void 0:c.textSecondary)||"#9ca3af");case"white":return n.css(["border-top-color:#ffffff;border-right-color:#ffffff;"]);case"red":return n.css(["border-top-color:#dc2626;border-right-color:#dc2626;"]);default:return n.css(["border-top-color:",";border-right-color:",";"],((a=r.colors)==null?void 0:a.primary)||"#dc2626",((p=r.colors)==null?void 0:p.primary)||"#dc2626")}},Bs,({$speed:e})=>1/e,({$showStripes:e,$variant:r})=>e&&n.css(["&::after{content:'';position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:50%;background:",";background-size:8px 8px;animation:"," ","s linear infinite;}"],r==="red"||r==="primary"?"linear-gradient(45deg, transparent 25%, rgba(220, 38, 38, 0.3) 25%, rgba(220, 38, 38, 0.3) 50%, transparent 50%, transparent 75%, rgba(220, 38, 38, 0.3) 75%)":"linear-gradient(45deg, transparent 25%, rgba(156, 163, 175, 0.3) 25%, rgba(156, 163, 175, 0.3) 50%, transparent 50%, transparent 75%, rgba(156, 163, 175, 0.3) 75%)",qs,t=>2/t.$speed)),Us=n.div.withConfig({displayName:"SpinnerContainer",componentId:"sc-1hoaoss-1"})(["display:inline-flex;align-items:center;justify-content:center;"]),Vs=e=>{const{size:r="md",variant:t="primary",className:s,"aria-label":i,speed:c=1,showStripes:a=!1}=e;return o.jsx(Us,{className:s,children:o.jsx(Hs,{$size:r,$variant:t,$speed:c,$showStripes:a,role:"status","aria-label":i||"Loading","aria-live":"polite"})})},Ys=Vs,Gs={none:n.css(["padding:0;"]),small:n.css(["padding:",";"],({theme:e})=>e.spacing.sm),medium:n.css(["padding:",";"],({theme:e})=>e.spacing.md),large:n.css(["padding:",";"],({theme:e})=>e.spacing.lg)},Ws={default:n.css(["background-color:",";"],({theme:e})=>e.colors.surface),primary:n.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),secondary:n.css(["background-color:","10;border-color:","30;"],({theme:e})=>e.colors.secondary,({theme:e})=>e.colors.secondary),outlined:n.css(["background-color:transparent;border:1px solid ",";"],({theme:e})=>e.colors.border),elevated:n.css(["background-color:",";box-shadow:",";border:none;"],({theme:e})=>e.colors.surface,({theme:e})=>e.shadows.md)},Ks=n.div.withConfig({displayName:"CardContainer",componentId:"sc-mv9m67-0"})(["border-radius:",";overflow:hidden;transition:all ",";position:relative;"," "," "," ",""],({theme:e})=>e.borderRadius.md,({theme:e})=>e.transitions.fast,({bordered:e,theme:r})=>e&&n.css(["border:1px solid ",";"],r.colors.border),({padding:e})=>Gs[e],({variant:e})=>Ws[e],({clickable:e})=>e&&n.css(["cursor:pointer;&:hover{transform:translateY(-2px);box-shadow:",";}&:active{transform:translateY(0);}"],({theme:r})=>r.shadows.md)),Qs=n.div.withConfig({displayName:"CardHeader",componentId:"sc-mv9m67-1"})(["display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:",";"],({theme:e})=>e.spacing.md),Xs=n.div.withConfig({displayName:"HeaderContent",componentId:"sc-mv9m67-2"})(["flex:1;"]),Js=n.h3.withConfig({displayName:"CardTitle",componentId:"sc-mv9m67-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),Zs=n.div.withConfig({displayName:"CardSubtitle",componentId:"sc-mv9m67-4"})(["margin-top:",";font-size:",";color:",";"],({theme:e})=>e.spacing.xs,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.colors.textSecondary),en=n.div.withConfig({displayName:"ActionsContainer",componentId:"sc-mv9m67-5"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),rn=n.div.withConfig({displayName:"CardContent",componentId:"sc-mv9m67-6"})([""]),tn=n.div.withConfig({displayName:"CardFooter",componentId:"sc-mv9m67-7"})(["margin-top:",";padding-top:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.md,({theme:e})=>e.colors.border),on=n.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-mv9m67-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),sn=n.div.withConfig({displayName:"ErrorContainer",componentId:"sc-mv9m67-9"})(["padding:",";background-color:","10;border-radius:",";color:",";margin-bottom:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.error,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.md),nn=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-mv9m67-10"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),Lt=({children:e,title:r="",subtitle:t="",bordered:s=!0,variant:i="default",padding:c="medium",className:a="",footer:p,actions:d,isLoading:f=!1,hasError:l=!1,errorMessage:m="An error occurred",clickable:x=!1,onClick:h,...g})=>{const b=r||t||d;return o.jsxs(Ks,{bordered:s,variant:i,padding:c,clickable:x,className:a,onClick:x?h:void 0,...g,children:[f&&o.jsx(on,{children:o.jsx(nn,{})}),b&&o.jsxs(Qs,{children:[o.jsxs(Xs,{children:[r&&o.jsx(Js,{children:r}),t&&o.jsx(Zs,{children:t})]}),d&&o.jsx(en,{children:d})]}),l&&o.jsx(sn,{children:o.jsx("p",{children:m})}),o.jsx(rn,{children:e}),p&&o.jsx(tn,{children:p})]})},an=n.h3.withConfig({displayName:"Title",componentId:"sc-1jsjvya-0"})(["margin:0 0 "," 0;color:",";font-weight:",";",""],({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontWeights.semibold,({size:e,theme:r})=>{const t={small:r.fontSizes.md,medium:r.fontSizes.lg,large:r.fontSizes.xl};return n.css(["font-size:",";"],t[e])}),cn=n.p.withConfig({displayName:"Description",componentId:"sc-1jsjvya-1"})(["margin:0 0 "," 0;color:",";",""],({theme:e})=>e.spacing.lg,({theme:e})=>e.colors.textSecondary,({size:e,theme:r})=>{const t={small:r.fontSizes.sm,medium:r.fontSizes.md,large:r.fontSizes.lg};return n.css(["font-size:",";"],t[e])}),ln={default:n.css(["background-color:transparent;"]),compact:n.css(["background-color:transparent;text-align:left;align-items:flex-start;"]),card:n.css(["background-color:",";border-radius:",";box-shadow:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.sm)},dn=n.div.withConfig({displayName:"Container",componentId:"sc-1jsjvya-2"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;width:100%;"," ",""],({variant:e})=>ln[e],({size:e,theme:r})=>{switch(e){case"small":return n.css(["padding:",";min-height:120px;"],r.spacing.md);case"large":return n.css(["padding:",";min-height:300px;"],r.spacing.xl);default:return n.css(["padding:",";min-height:200px;"],r.spacing.lg)}}),un=n.div.withConfig({displayName:"IconContainer",componentId:"sc-1jsjvya-3"})(["margin-bottom:",";",""],({theme:e})=>e.spacing.md,({size:e,theme:r})=>{const t={small:"32px",medium:"48px",large:"64px"};return n.css(["font-size:",";svg{width:",";height:",";color:",";}"],t[e],t[e],t[e],r.colors.textSecondary)}),pn=n.div.withConfig({displayName:"ActionContainer",componentId:"sc-1jsjvya-4"})(["margin-top:",";"],({theme:e})=>e.spacing.md),fn=n.div.withConfig({displayName:"ChildrenContainer",componentId:"sc-1jsjvya-5"})(["margin-top:",";width:100%;"],({theme:e})=>e.spacing.lg),yr=({title:e="",description:r="",icon:t,actionText:s="",onAction:i,variant:c="default",size:a="medium",className:p="",children:d})=>o.jsxs(dn,{variant:c,size:a,className:p,children:[t&&o.jsx(un,{size:a,children:t}),e&&o.jsx(an,{size:a,children:e}),r&&o.jsx(cn,{size:a,children:r}),s&&i&&o.jsx(pn,{children:o.jsx(ne,{variant:"primary",size:a==="small"?"small":"medium",onClick:i,children:s})}),d&&o.jsx(fn,{children:d})]}),Zr=n.div.withConfig({displayName:"ErrorContainer",componentId:"sc-jxqb9h-0"})(["padding:1.5rem;margin:",";border-radius:0.5rem;background-color:",";color:#ffffff;",""],e=>e.isAppLevel?"0":"1rem 0",e=>e.isAppLevel?"#1a1f2c":"#f44336",e=>e.isAppLevel&&`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  `),mn=n.div.withConfig({displayName:"ErrorCard",componentId:"sc-jxqb9h-1"})(["background-color:#252a37;border-radius:0.5rem;padding:2rem;width:100%;box-shadow:0 4px 6px rgba(0,0,0,0.1);"]),et=n.h3.withConfig({displayName:"ErrorTitle",componentId:"sc-jxqb9h-2"})(["margin-top:0;font-size:",";font-weight:700;text-align:",";"],e=>e.isAppLevel?"1.5rem":"1.25rem",e=>e.isAppLevel?"center":"left"),Qe=n.p.withConfig({displayName:"ErrorMessage",componentId:"sc-jxqb9h-3"})(["margin-bottom:1rem;text-align:",";"],e=>e.isAppLevel?"center":"left"),rt=n.details.withConfig({displayName:"ErrorDetails",componentId:"sc-jxqb9h-4"})(["margin-bottom:1rem;summary{cursor:pointer;color:#2196f3;font-weight:500;margin-bottom:0.5rem;}"]),tt=n.pre.withConfig({displayName:"ErrorStack",componentId:"sc-jxqb9h-5"})(["font-size:0.875rem;background-color:rgba(0,0,0,0.1);padding:0.5rem;border-radius:0.25rem;overflow:auto;max-height:200px;"]),gn=n.div.withConfig({displayName:"ButtonContainer",componentId:"sc-jxqb9h-6"})(["display:flex;gap:0.5rem;justify-content:flex-start;"]),_t=n.button.withConfig({displayName:"RetryButton",componentId:"sc-jxqb9h-7"})(["background-color:#ffffff;color:#f44336;border:none;border-radius:0.25rem;padding:0.5rem 1rem;font-weight:700;cursor:pointer;transition:background-color 0.2s;&:hover{background-color:#f5f5f5;}"]),hn=n.button.withConfig({displayName:"SkipButton",componentId:"sc-jxqb9h-8"})(["padding:0.5rem 1rem;background-color:transparent;color:#ffffff;border:1px solid #ffffff;border-radius:0.25rem;font-size:0.875rem;font-weight:500;cursor:pointer;transition:all 0.2s;&:hover{background-color:rgba(255,255,255,0.1);}"]),xn=n(_t).withConfig({displayName:"ReloadButton",componentId:"sc-jxqb9h-9"})(["margin-top:1rem;width:100%;"]),bn=({error:e,resetError:r,isAppLevel:t,name:s,onSkip:i})=>{const c=()=>{window.location.reload()};return t?o.jsx(Zr,{isAppLevel:!0,children:o.jsxs(mn,{children:[o.jsx(et,{isAppLevel:!0,children:"Something went wrong"}),o.jsx(Qe,{isAppLevel:!0,children:"We're sorry, but an unexpected error has occurred. Please try reloading the application."}),o.jsxs(rt,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Qe,{children:e.message}),e.stack&&o.jsx(tt,{children:e.stack})]}),o.jsx(xn,{onClick:c,children:"Reload Application"})]})}):o.jsxs(Zr,{children:[o.jsx(et,{children:s?`Error in ${s}`:"Something went wrong"}),o.jsx(Qe,{children:s?`We encountered a problem while loading ${s}. You can try again${i?" or skip this feature":""}.`:"An unexpected error occurred. Please try again."}),o.jsxs(rt,{children:[o.jsx("summary",{children:"Technical Details"}),o.jsx(Qe,{children:e.message}),e.stack&&o.jsx(tt,{children:e.stack})]}),o.jsxs(gn,{children:[o.jsx(_t,{onClick:r,children:"Try Again"}),i&&o.jsx(hn,{onClick:i,children:"Skip This Feature"})]})]})};class Mt extends y.Component{constructor(t){super(t);xe(this,"resetError",()=>{this.setState({hasError:!1,error:null})});this.state={hasError:!1,error:null}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,s){const{name:i}=this.props,c=i?`ErrorBoundary(${i})`:"ErrorBoundary";console.error(`Error caught by ${c}:`,t,s),this.props.onError&&this.props.onError(t,s)}componentDidUpdate(t){this.state.hasError&&this.props.resetOnPropsChange&&t.children!==this.props.children&&this.resetError()}componentWillUnmount(){this.state.hasError&&this.props.resetOnUnmount&&this.resetError()}render(){const{hasError:t,error:s}=this.state,{children:i,fallback:c,name:a,isFeatureBoundary:p,onSkip:d}=this.props;return t&&s?typeof c=="function"?c({error:s,resetError:this.resetError}):c||o.jsx(bn,{error:s,resetError:this.resetError,isAppLevel:!p,name:a,onSkip:d}):i}}const wr=({isAppLevel:e=!1,isFeatureBoundary:r=!1,children:t,...s})=>{const i=e?"app":r?"feature":"component",c={resetOnPropsChange:i!=="app",resetOnUnmount:i!=="app",isFeatureBoundary:i==="feature"};return o.jsx(Mt,{...c,...s,children:t})},yn=e=>o.jsx(wr,{isAppLevel:!0,...e}),vn=({featureName:e,children:r,...t})=>o.jsx(wr,{isFeatureBoundary:!0,name:e,children:r,...t}),Sn=n.div.withConfig({displayName:"TabContainer",componentId:"sc-lgz9vh-0"})(["display:flex;flex-direction:column;width:100%;"]),wn=n.div.withConfig({displayName:"TabList",componentId:"sc-lgz9vh-1"})(["display:flex;border-bottom:1px solid ",";margin-bottom:",";"],({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md),Cn=n.button.withConfig({displayName:"TabButton",componentId:"sc-lgz9vh-2"})(["padding:"," ",";background:none;border:none;border-bottom:2px solid ",";color:",";font-weight:",";cursor:pointer;transition:all ",";&:hover{color:",";}&:focus{outline:none;color:",";}"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md,({active:e,theme:r})=>e?r.colors.primary:"transparent",({active:e,theme:r})=>e?r.colors.primary:r.colors.textSecondary,({active:e,theme:r})=>e?r.fontWeights.semibold:r.fontWeights.regular,({theme:e})=>e.transitions.fast,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary),Tn=n.div.withConfig({displayName:"TabContent",componentId:"sc-lgz9vh-3"})(["padding:"," 0;"],({theme:e})=>e.spacing.sm),En=({tabs:e,defaultTab:r,className:t,activeTab:s,onTabClick:i})=>{var f;const[c,a]=y.useState(r||e[0].id),p=s!==void 0?s:c,d=(l,m)=>{l.preventDefault(),l.stopPropagation(),i?i(m):a(m)};return o.jsxs(Sn,{className:t,children:[o.jsx(wn,{children:e.map(l=>o.jsx(Cn,{active:p===l.id,onClick:m=>d(m,l.id),type:"button",form:"",tabIndex:0,"data-tab-id":l.id,children:l.label},l.id))}),o.jsx(Tn,{children:(f=e.find(l=>l.id===p))==null?void 0:f.content})]})},In=En,Rt={required:(e="This field is required")=>({validate:r=>typeof r=="string"?r.trim().length>0:typeof r=="number"?!isNaN(r):Array.isArray(r)?r.length>0:r!=null&&r!==void 0,message:e}),email:(e="Please enter a valid email address")=>({validate:r=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r),message:e}),minLength:(e,r)=>({validate:t=>t.length>=e,message:r||`Must be at least ${e} characters`}),maxLength:(e,r)=>({validate:t=>t.length<=e,message:r||`Must be no more than ${e} characters`}),min:(e,r)=>({validate:t=>t>=e,message:r||`Must be at least ${e}`}),max:(e,r)=>({validate:t=>t<=e,message:r||`Must be no more than ${e}`}),pattern:(e,r)=>({validate:t=>e.test(t),message:r})},Pt=(e={})=>{const{initialValue:r="",required:t=!1,type:s="text",validationRules:i=[],validateOnChange:c=!1,validateOnBlur:a=!0,transform:p}=e,d=y.useMemo(()=>{const j=[...i];return t&&!i.some(L=>L.message.toLowerCase().includes("required"))&&j.unshift(Rt.required()),j},[t,i]),[f,l]=y.useState(r),[m,x]=y.useState(null),[h,g]=y.useState(!1),[b,T]=y.useState(!1),w=y.useMemo(()=>f!==r,[f,r]),C=y.useMemo(()=>m===null&&!b,[m,b]),M=y.useMemo(()=>m===null&&!b,[m,b]),A=y.useCallback(async()=>{T(!0);try{for(const j of d)if(!j.validate(f))return x(j.message),T(!1),!1;return x(null),T(!1),!0}catch{return x("Validation error occurred"),T(!1),!1}},[f,d]),N=y.useCallback(()=>{l(r),x(null),g(!1),T(!1)},[r]),k=y.useCallback(j=>{let L;s==="number"?L=parseFloat(j.target.value)||0:L=j.target.value,p&&(L=p(L)),l(L),c&&h&&setTimeout(()=>A(),0)},[s,p,c,h,A]),R=y.useCallback(j=>{g(!0),a&&A()},[a,A]);return{value:f,error:m,touched:h,dirty:w,valid:C,isValid:M,validating:b,setValue:l,setError:x,setTouched:g,validate:A,reset:N,handleChange:k,handleBlur:R}},jn=n.div.withConfig({displayName:"FieldContainer",componentId:"sc-oh07s1-0"})(["display:flex;flex-direction:column;gap:",";width:100%;margin-bottom:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Nn=n.label.withConfig({displayName:"Label",componentId:"sc-oh07s1-1"})(["font-size:",";font-weight:",";color:",";",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||"500"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$required:e})=>e&&n.css(["&::after{content:' *';color:",";}"],({theme:r})=>{var t;return((t=r.colors)==null?void 0:t.error)||"#dc2626"})),Cr=n.css(["width:100%;border:1px solid ",";border-radius:",";background-color:",";color:",";font-size:",";padding:",";transition:",";&:focus{outline:none;border-color:",";box-shadow:0 0 0 2px ",";}&:disabled{background-color:",";color:",";cursor:not-allowed;}&::placeholder{color:",";}"],({theme:e,$hasError:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.error)||"#dc2626":((s=e.colors)==null?void 0:s.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e,$size:r})=>{var t,s,i;switch(r){case"sm":return((t=e.fontSizes)==null?void 0:t.sm)||"14px";case"lg":return((s=e.fontSizes)==null?void 0:s.lg)||"18px";default:return((i=e.fontSizes)==null?void 0:i.md)||"16px"}},({theme:e,$size:r})=>{var t,s,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((s=e.spacing)==null?void 0:s.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.primary?`${e.colors.primary}20`:"rgba(220, 38, 38, 0.2)"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.chartGrid)||"#374151"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),kn=n.input.withConfig({displayName:"StyledInput",componentId:"sc-oh07s1-2"})(["",""],Cr),Ln=n.textarea.withConfig({displayName:"StyledTextarea",componentId:"sc-oh07s1-3"})([""," resize:vertical;min-height:80px;"],Cr),_n=n.select.withConfig({displayName:"StyledSelect",componentId:"sc-oh07s1-4"})([""," cursor:pointer;"],Cr),Mn=n.div.withConfig({displayName:"ErrorMessage",componentId:"sc-oh07s1-5"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#dc2626"}),Rn=n.div.withConfig({displayName:"HelpText",componentId:"sc-oh07s1-6"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Pn=e=>{const{name:r,label:t,placeholder:s,disabled:i=!1,className:c,size:a="md",helpText:p,inputType:d="input",options:f=[],rows:l=4,onChange:m,onBlur:x,...h}=e,g=Pt({...h,validateOnBlur:!0});y.useEffect(()=>{m&&m(g.value)},[g.value,m]);const b=C=>{g.handleBlur(C),x&&x()},T={id:r,name:r,value:g.value,onChange:g.handleChange,onBlur:b,disabled:i,placeholder:s,$hasError:!!g.error,$disabled:i,$size:a},w=()=>{switch(d){case"textarea":return o.jsx(Ln,{...T,rows:l});case"select":return o.jsxs(_n,{...T,children:[s&&o.jsx("option",{value:"",disabled:!0,children:s}),f.map(C=>o.jsx("option",{value:C.value,children:C.label},C.value))]});default:return o.jsx(kn,{...T,type:h.type||"text"})}};return o.jsxs(jn,{className:c,children:[t&&o.jsx(Nn,{htmlFor:r,$required:!!h.required,children:t}),w(),g.error&&g.touched&&o.jsx(Mn,{role:"alert",children:g.error}),p&&!g.error&&o.jsx(Rn,{children:p})]})},Dn=Pn,$n={string:e=>(r,t)=>{const s=String(r[e]||""),i=String(t[e]||"");return s.localeCompare(i)},number:e=>(r,t)=>{const s=Number(r[e])||0,i=Number(t[e])||0;return s-i},date:e=>(r,t)=>{const s=new Date(r[e]).getTime(),i=new Date(t[e]).getTime();return s-i},boolean:e=>(r,t)=>{const s=!!r[e],i=!!t[e];return Number(s)-Number(i)}},Dt=({data:e,columns:r,defaultSort:t})=>{const[s,i]=y.useState(t?{field:t.field,direction:t.direction}:null),c=y.useCallback(l=>{const m=r.find(x=>x.field===l);m!=null&&m.sortable&&i(x=>{var h;if((x==null?void 0:x.field)===l)return{field:l,direction:x.direction==="asc"?"desc":"asc"};{const g=typeof((h=e[0])==null?void 0:h[l])=="number"?"desc":"asc";return{field:l,direction:g}}})},[r,e]),a=y.useMemo(()=>{if(!s)return e;const l=r.find(x=>x.field===s.field);return l?[...e].sort((x,h)=>{let g=0;if(l.sortFn)g=l.sortFn(x,h);else{const b=x[s.field],T=h[s.field];typeof b=="string"&&typeof T=="string"?g=b.localeCompare(T):typeof b=="number"&&typeof T=="number"?g=b-T:g=String(b).localeCompare(String(T))}return s.direction==="asc"?g:-g}):e},[e,s,r]),p=y.useCallback(l=>!s||s.field!==l?null:s.direction==="asc"?"↑":"↓",[s]),d=y.useCallback(l=>(s==null?void 0:s.field)===l,[s]),f=y.useCallback(l=>(s==null?void 0:s.field)===l?s.direction:null,[s]);return{sortedData:a,sortConfig:s,handleSort:c,getSortIcon:p,isSorted:d,getSortDirection:f}},ot=n.div.withConfig({displayName:"Container",componentId:"sc-13j9udn-0"})(["overflow-x:auto;border-radius:",";border:1px solid ",";"],({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),On=n.table.withConfig({displayName:"Table",componentId:"sc-13j9udn-1"})(["width:100%;border-collapse:collapse;font-size:",";"],({theme:e,$size:r})=>{var t,s,i;switch(r){case"sm":return((t=e.fontSizes)==null?void 0:t.xs)||"12px";case"lg":return((s=e.fontSizes)==null?void 0:s.md)||"16px";default:return((i=e.fontSizes)==null?void 0:i.sm)||"14px"}}),An=n.thead.withConfig({displayName:"TableHead",componentId:"sc-13j9udn-2"})(["background-color:",";border-bottom:2px solid ",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),zn=n.tbody.withConfig({displayName:"TableBody",componentId:"sc-13j9udn-3"})([""]),st=n.tr.withConfig({displayName:"TableRow",componentId:"sc-13j9udn-4"})([""," "," "," border-bottom:1px solid ",";"],({$striped:e,theme:r})=>{var t;return e&&n.css(["&:nth-child(even){background-color:",";}"],((t=r.colors)==null?void 0:t.background)||"#0f0f0f")},({$hoverable:e,theme:r})=>{var t;return e&&n.css(["&:hover{background-color:",";}"],((t=r.colors)==null?void 0:t.surface)||"#1f2937")},({$clickable:e})=>e&&n.css(["cursor:pointer;"]),({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),Fn=n.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13j9udn-5"})(["text-align:left;font-weight:",";color:",";cursor:",";user-select:none;transition:",";padding:",";&:hover{","}&:focus{outline:2px solid ",";outline-offset:-2px;}"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||"600"},({theme:e,$active:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((s=e.colors)==null?void 0:s.textPrimary)||"#ffffff"},({$sortable:e})=>e?"pointer":"default",({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e,$size:r})=>{var t,s,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((s=e.spacing)==null?void 0:s.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({$sortable:e,theme:r})=>{var t;return e&&n.css(["color:",";"],((t=r.colors)==null?void 0:t.primary)||"#dc2626")},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),Bn=n.td.withConfig({displayName:"TableCell",componentId:"sc-13j9udn-6"})(["padding:",";color:",";"],({theme:e,$size:r})=>{var t,s,i,c,a,p;switch(r){case"sm":return`${((t=e.spacing)==null?void 0:t.xs)||"4px"} ${((s=e.spacing)==null?void 0:s.sm)||"8px"}`;case"lg":return`${((i=e.spacing)==null?void 0:i.md)||"12px"} ${((c=e.spacing)==null?void 0:c.lg)||"16px"}`;default:return`${((a=e.spacing)==null?void 0:a.sm)||"8px"} ${((p=e.spacing)==null?void 0:p.md)||"12px"}`}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),qn=n.span.withConfig({displayName:"SortIcon",componentId:"sc-13j9udn-7"})(["display:inline-block;margin-left:",";font-size:",";&::after{content:'","';}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({$direction:e})=>e==="asc"?"↑":"↓"),Hn=n.div.withConfig({displayName:"EmptyState",componentId:"sc-13j9udn-8"})(["padding:",";text-align:center;color:",";font-style:italic;"],({theme:e,$size:r})=>{var t,s,i;switch(r){case"sm":return((t=e.spacing)==null?void 0:t.md)||"12px";case"lg":return((s=e.spacing)==null?void 0:s.xl)||"24px";default:return((i=e.spacing)==null?void 0:i.lg)||"16px"}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Un=({data:e,columns:r,className:t,emptyMessage:s="No data available",defaultSort:i,renderCell:c,onRowClick:a,size:p="md",striped:d=!0,hoverable:f=!0})=>{const{sortedData:l,handleSort:m,getSortIcon:x,isSorted:h}=Dt({data:e,columns:r,defaultSort:i});return e.length===0?o.jsx(ot,{className:t,children:o.jsx(Hn,{$size:p,children:s})}):o.jsx(ot,{className:t,children:o.jsxs(On,{$size:p,$striped:d,$hoverable:f,children:[o.jsx(An,{children:o.jsx(st,{$striped:!1,$hoverable:!1,$clickable:!1,children:r.map(g=>o.jsxs(Fn,{$sortable:g.sortable||!1,$active:h(g.field),$size:p,onClick:()=>g.sortable&&m(g.field),tabIndex:g.sortable?0:-1,onKeyDown:b=>{g.sortable&&(b.key==="Enter"||b.key===" ")&&(b.preventDefault(),m(g.field))},role:g.sortable?"button":void 0,"aria-sort":h(g.field)?x(g.field)==="↑"?"ascending":"descending":void 0,children:[g.label,h(g.field)&&o.jsx(qn,{$direction:x(g.field)==="↑"?"asc":"desc"})]},String(g.field)))})}),o.jsx(zn,{children:l.map((g,b)=>o.jsx(st,{$striped:d,$hoverable:f,$clickable:!!a,onClick:()=>a==null?void 0:a(g,b),tabIndex:a?0:-1,onKeyDown:T=>{a&&(T.key==="Enter"||T.key===" ")&&(T.preventDefault(),a(g,b))},role:a?"button":void 0,children:r.map(T=>{const w=g[T.field];return o.jsx(Bn,{$size:p,children:c?c(w,g,T):String(w)},String(T.field))})},b))})]})})},Vn=Un,Yn=n.div.withConfig({displayName:"FieldContainer",componentId:"sc-i922jg-0"})(["display:flex;flex-direction:column;margin-bottom:",";"],({theme:e})=>e.spacing.md),Gn=n.label.withConfig({displayName:"Label",componentId:"sc-i922jg-1"})(["font-size:",";font-weight:500;margin-bottom:",";color:",";.required-indicator{color:",";margin-left:",";}"],({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xxs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textPrimary,({theme:e})=>e.colors.error,({theme:e})=>e.spacing.xxs),Wn=n.div.withConfig({displayName:"HelperText",componentId:"sc-i922jg-2"})(["font-size:",";color:",";margin-top:",";"],({theme:e})=>e.fontSizes.xs,({theme:e,hasError:r})=>r?e.colors.error:e.colors.textSecondary,({theme:e})=>e.spacing.xxs),Kn=({children:e,label:r,helperText:t,required:s=!1,error:i,className:c,id:a,...p})=>{const d=a||`field-${Math.random().toString(36).substr(2,9)}`,f=y.Children.map(e,l=>y.isValidElement(l)?y.cloneElement(l,{id:d,required:s,error:i}):l);return o.jsxs(Yn,{className:c,...p,children:[o.jsxs(Gn,{htmlFor:d,hasError:!!i,children:[r,s&&o.jsx("span",{className:"required-indicator",children:"*"})]}),f,(t||i)&&o.jsx(Wn,{hasError:!!i,children:i||t})]})},Qn=n.keyframes(["from{opacity:0;}to{opacity:1;}"]),Xn=n.keyframes(["from{transform:translateY(-20px);opacity:0;}to{transform:translateY(0);opacity:1;}"]),Jn=n.div.withConfig({displayName:"Backdrop",componentId:"sc-1cuqxtr-0"})(["position:fixed;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:",";animation:"," 0.2s ease-out;"],({zIndex:e})=>e||1e3,Qn),Zn=n.div.withConfig({displayName:"ModalContainer",componentId:"sc-1cuqxtr-1"})(["background-color:",";border-radius:",";box-shadow:",";display:flex;flex-direction:column;max-height:",";width:",";max-width:95vw;animation:"," 0.2s ease-out;position:relative;"," ",""],({theme:e})=>e.colors.surface,({theme:e})=>e.borderRadius.md,({theme:e})=>e.shadows.lg,({size:e})=>e==="fullscreen"?"100vh":"90vh",({size:e})=>{switch(e){case"small":return"400px";case"medium":return"600px";case"large":return"800px";case"fullscreen":return"100vw";default:return"600px"}},Xn,({size:e})=>e==="fullscreen"&&n.css(["height:100vh;border-radius:0;"]),({centered:e})=>e&&n.css(["margin:auto;"])),ei=n.div.withConfig({displayName:"ModalHeader",componentId:"sc-1cuqxtr-2"})(["display:flex;justify-content:space-between;align-items:center;padding:",";border-bottom:1px solid ",";"],({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),ri=n.h3.withConfig({displayName:"ModalTitle",componentId:"sc-1cuqxtr-3"})(["margin:0;font-size:",";font-weight:",";color:",";"],({theme:e})=>e.fontSizes.lg,({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textPrimary),ti=n.button.withConfig({displayName:"CloseButton",componentId:"sc-1cuqxtr-4"})(["background:none;border:none;cursor:pointer;font-size:",";color:",";padding:0;display:flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",";&:hover{background-color:",";}&:focus{outline:none;box-shadow:0 0 0 2px ","33;}"],({theme:e})=>e.fontSizes.xl,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.borderRadius.sm,({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary),oi=n.div.withConfig({displayName:"ModalContent",componentId:"sc-1cuqxtr-5"})(["padding:",";",""],({theme:e})=>e.spacing.lg,({scrollable:e})=>e&&n.css(["overflow-y:auto;flex:1;"])),si=n.div.withConfig({displayName:"ModalFooter",componentId:"sc-1cuqxtr-6"})(["display:flex;justify-content:flex-end;gap:",";padding:",";border-top:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>`${e.spacing.md} ${e.spacing.lg}`,({theme:e})=>e.colors.border),ni=({isOpen:e,title:r="",children:t,onClose:s,size:i="medium",closeOnOutsideClick:c=!0,showCloseButton:a=!0,footer:p,hasFooter:d=!0,primaryActionText:f="",onPrimaryAction:l,primaryActionDisabled:m=!1,primaryActionLoading:x=!1,secondaryActionText:h="",onSecondaryAction:g,secondaryActionDisabled:b=!1,className:T="",zIndex:w=1e3,centered:C=!0,scrollable:M=!0})=>{const A=y.useRef(null);y.useEffect(()=>{const j=L=>{L.key==="Escape"&&e&&c&&s()};return document.addEventListener("keydown",j),()=>{document.removeEventListener("keydown",j)}},[e,s,c]);const N=j=>{A.current&&!A.current.contains(j.target)&&c&&s()};y.useEffect(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="",()=>{document.body.style.overflow=""}),[e]);const k=o.jsxs(o.Fragment,{children:[h&&o.jsx(ne,{variant:"outline",onClick:g,disabled:b,children:h}),f&&o.jsx(ne,{onClick:l,disabled:m,loading:x,children:f})]});if(!e)return null;const R=o.jsx(Jn,{onClick:N,zIndex:w,children:o.jsxs(Zn,{ref:A,size:i,className:T,centered:C,scrollable:M,onClick:j=>j.stopPropagation(),children:[(r||a)&&o.jsxs(ei,{children:[r&&o.jsx(ri,{children:r}),a&&o.jsx(ti,{onClick:s,"aria-label":"Close",children:"×"})]}),o.jsx(oi,{scrollable:M,children:t}),d&&(p||f||h)&&o.jsx(si,{children:p||k})]})});return To.createPortal(R,document.body)},ii=n.div.withConfig({displayName:"TableContainer",componentId:"sc-4as3uq-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),ai=n.table.withConfig({displayName:"StyledTable",componentId:"sc-4as3uq-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>e.fontSizes.sm,({bordered:e,theme:r})=>e&&n.css(["border:1px solid ",";border-radius:",";"],r.colors.border,r.borderRadius.sm),({compact:e,theme:r})=>e?n.css(["th,td{padding:"," ",";}"],r.spacing.xs,r.spacing.sm):n.css(["th,td{padding:"," ",";}"],r.spacing.sm,r.spacing.md)),ci=n.thead.withConfig({displayName:"TableHeader",componentId:"sc-4as3uq-2"})(["",""],({stickyHeader:e})=>e&&n.css(["position:sticky;top:0;z-index:1;"])),li=n.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-4as3uq-3"})(["background-color:",";"],({theme:e})=>e.colors.background),di=n.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-4as3uq-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>e.fontWeights.semibold,({theme:e})=>e.colors.textSecondary,({theme:e})=>e.colors.border,({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&n.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>r.colors.background),({isSorted:e,theme:r})=>e&&n.css(["color:",";"],r.colors.primary)),ui=n.span.withConfig({displayName:"SortIcon",componentId:"sc-4as3uq-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>e.spacing.xs,({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),pi=n.tbody.withConfig({displayName:"TableBody",componentId:"sc-4as3uq-6"})([""]),fi=n.tr.withConfig({displayName:"TableRow",componentId:"sc-4as3uq-7"})([""," "," "," ",""],({striped:e,theme:r,isSelected:t})=>e&&!t&&n.css(["&:nth-child(even){background-color:","50;}"],r.colors.background),({hoverable:e,theme:r,isSelected:t})=>e&&!t&&n.css(["&:hover{background-color:","aa;}"],r.colors.background),({isSelected:e,theme:r})=>e&&n.css(["background-color:","15;"],r.colors.primary),({isClickable:e})=>e&&n.css(["cursor:pointer;"])),mi=n.td.withConfig({displayName:"TableCell",componentId:"sc-4as3uq-8"})(["text-align:",";border-bottom:1px solid ",";color:",";"],({align:e})=>e||"left",({theme:e})=>e.colors.border,({theme:e})=>e.colors.textPrimary),gi=n.div.withConfig({displayName:"EmptyState",componentId:"sc-4as3uq-9"})(["padding:",";text-align:center;color:",";"],({theme:e})=>e.spacing.xl,({theme:e})=>e.colors.textSecondary),hi=n.div.withConfig({displayName:"PaginationContainer",componentId:"sc-4as3uq-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.fontSizes.sm),xi=n.div.withConfig({displayName:"PageInfo",componentId:"sc-4as3uq-11"})(["color:",";"],({theme:e})=>e.colors.textSecondary),bi=n.div.withConfig({displayName:"PaginationControls",componentId:"sc-4as3uq-12"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.xs),yi=n.div.withConfig({displayName:"PageSizeSelector",componentId:"sc-4as3uq-13"})(["display:flex;align-items:center;gap:",";margin-right:",";"],({theme:e})=>e.spacing.sm,({theme:e})=>e.spacing.md),vi=n.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-4as3uq-14"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>`${e.colors.background}80`),Si=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-4as3uq-15"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>e.colors.background,({theme:e})=>e.colors.primary);function wi({columns:e,data:r,isLoading:t=!1,bordered:s=!0,striped:i=!0,hoverable:c=!0,compact:a=!1,stickyHeader:p=!1,height:d,onRowClick:f,isRowSelected:l,onSort:m,sortColumn:x,sortDirection:h,pagination:g=!1,currentPage:b=1,pageSize:T=10,totalRows:w=0,onPageChange:C,onPageSizeChange:M,className:A,emptyMessage:N="No data available",scrollable:k=!0}){const R=y.useMemo(()=>e.filter(_=>!_.hidden),[e]),j=y.useMemo(()=>Math.ceil(w/T),[w,T]),L=y.useMemo(()=>{if(!g)return r;const _=(b-1)*T,q=_+T;return w>0&&r.length<=T?r:r.slice(_,q)},[r,g,b,T,w]),W=_=>{if(!m)return;m(_,x===_&&h==="asc"?"desc":"asc")},V=_=>{_<1||_>j||!C||C(_)};return o.jsxs("div",{style:{position:"relative"},children:[t&&o.jsx(vi,{children:o.jsx(Si,{})}),o.jsx(ii,{height:d,scrollable:k,children:o.jsxs(ai,{bordered:s,striped:i,compact:a,className:A,children:[o.jsx(ci,{stickyHeader:p,children:o.jsx(li,{children:R.map(_=>o.jsxs(di,{sortable:_.sortable,isSorted:x===_.id,align:_.align,width:_.width,onClick:()=>_.sortable&&W(_.id),children:[_.header,_.sortable&&o.jsx(ui,{direction:x===_.id?h:void 0})]},_.id))})}),o.jsx(pi,{children:L.length>0?L.map((_,q)=>o.jsx(fi,{hoverable:c,striped:i,isSelected:l?l(_,q):!1,isClickable:!!f,onClick:()=>f&&f(_,q),children:R.map(J=>o.jsx(mi,{align:J.align,children:J.cell(_,q)},J.id))},q)):o.jsx("tr",{children:o.jsx("td",{colSpan:R.length,children:o.jsx(gi,{children:N})})})})]})}),g&&j>0&&o.jsxs(hi,{children:[o.jsxs(xi,{children:["Showing ",Math.min((b-1)*T+1,w)," to"," ",Math.min(b*T,w)," of ",w," entries"]}),o.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[M&&o.jsxs(yi,{children:[o.jsx("span",{children:"Show"}),o.jsx("select",{value:T,onChange:_=>M(Number(_.target.value)),style:{padding:"4px 8px",borderRadius:"4px",border:"1px solid #ccc"},children:[10,25,50,100].map(_=>o.jsx("option",{value:_,children:_},_))}),o.jsx("span",{children:"entries"})]}),o.jsxs(bi,{children:[o.jsx(ne,{size:"small",variant:"outline",onClick:()=>V(1),disabled:b===1,children:"First"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>V(b-1),disabled:b===1,children:"Prev"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>V(b+1),disabled:b===j,children:"Next"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>V(j),disabled:b===j,children:"Last"})]})]})]})]})}const ce={[P.MORNING_BREAKOUT]:{type:P.MORNING_BREAKOUT,name:"9:50-10:10 Macro",timeRange:{start:"09:50:00",end:"10:10:00"},description:"Morning breakout period - high volatility after market open",characteristics:["High Volume","Breakout Setups","Gap Fills","Opening Range"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[P.MID_MORNING_REVERSION]:{type:P.MID_MORNING_REVERSION,name:"10:50-11:10 Macro",timeRange:{start:"10:50:00",end:"11:10:00"},description:"Mid-morning reversion period - mean reversion opportunities",characteristics:["Mean Reversion","Pullback Setups","Support/Resistance Tests"],volatilityLevel:3,volumeLevel:3,isHighProbability:!0},[P.PRE_LUNCH]:{type:P.PRE_LUNCH,name:"11:50-12:10 Macro",timeRange:{start:"11:50:00",end:"12:10:00"},description:"Pre-lunch macro window - specific high-activity period within lunch session",characteristics:["Consolidation","Range Trading","Pre-Lunch Activity"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,parentMacro:P.LUNCH_MACRO_EXTENDED},[P.LUNCH_MACRO_EXTENDED]:{type:P.LUNCH_MACRO_EXTENDED,name:"Lunch Macro (11:30-13:30)",timeRange:{start:"11:30:00",end:"13:30:00"},description:"Extended lunch period spanning late morning through early afternoon",characteristics:["Multi-Session","Lunch Trading","Lower Volume","Transition Period"],volatilityLevel:2,volumeLevel:2,isHighProbability:!1,isMultiSession:!0,spansSessions:[Y.NEW_YORK_AM,Y.NEW_YORK_PM],subPeriods:[]},[P.LUNCH_MACRO]:{type:P.LUNCH_MACRO,name:"Lunch Macro (12:00-13:30)",timeRange:{start:"12:00:00",end:"13:30:00"},description:"Traditional lunch time trading - typically lower volume",characteristics:["Low Volume","Range Bound","Choppy Price Action"],volatilityLevel:2,volumeLevel:1,isHighProbability:!1},[P.POST_LUNCH]:{type:P.POST_LUNCH,name:"13:50-14:10 Macro",timeRange:{start:"13:50:00",end:"14:10:00"},description:"Post-lunch macro window",characteristics:["Volume Pickup","Trend Resumption"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1},[P.PRE_CLOSE]:{type:P.PRE_CLOSE,name:"14:50-15:10 Macro",timeRange:{start:"14:50:00",end:"15:10:00"},description:"Pre-close macro window",characteristics:["Institutional Activity","Position Adjustments"],volatilityLevel:3,volumeLevel:4,isHighProbability:!1},[P.POWER_HOUR]:{type:P.POWER_HOUR,name:"15:15-15:45 Macro (Power Hour)",timeRange:{start:"15:15:00",end:"15:45:00"},description:"Last hour macro - high activity before close",characteristics:["High Volume","Institutional Flows","EOD Positioning"],volatilityLevel:4,volumeLevel:5,isHighProbability:!0},[P.MOC]:{type:P.MOC,name:"MOC (Market on Close)",timeRange:{start:"15:45:00",end:"16:00:00"},description:"Market on close period",characteristics:["MOC Orders","Final Positioning","High Volume"],volatilityLevel:4,volumeLevel:5,isHighProbability:!1},[P.LONDON_OPEN]:{type:P.LONDON_OPEN,name:"London Open",timeRange:{start:"08:00:00",end:"09:00:00"},description:"London market opening hour",characteristics:["European Activity","Currency Moves","News Reactions"],volatilityLevel:4,volumeLevel:4,isHighProbability:!0},[P.LONDON_NY_OVERLAP]:{type:P.LONDON_NY_OVERLAP,name:"London/NY Overlap",timeRange:{start:"14:00:00",end:"16:00:00"},description:"London and New York session overlap",characteristics:["Highest Volume","Major Moves","Cross-Market Activity"],volatilityLevel:5,volumeLevel:5,isHighProbability:!0},[P.CUSTOM]:{type:P.CUSTOM,name:"Custom Period",timeRange:{start:"00:00:00",end:"23:59:59"},description:"User-defined custom time period",characteristics:["Custom"],volatilityLevel:3,volumeLevel:3,isHighProbability:!1}},Ci=()=>{const e=Object.values(Ti).map(i=>({id:i.type,...i})),t=[{...ce[P.LUNCH_MACRO_EXTENDED],id:"lunch-macro-extended",subPeriods:[{...ce[P.PRE_LUNCH],id:"pre-lunch-sub"}]}],s={};return e.forEach(i=>{i.macroPeriods.forEach(c=>{s[c.type]={...c,parentSession:i.type}})}),t.forEach(i=>{s[i.type]={...i,spansSessions:i.spansSessions}}),{sessions:e,sessionsByType:e.reduce((i,c)=>(i[c.type]=c,i),{}),macrosByType:s,multiSessionMacros:t}},Ti={[Y.NEW_YORK_AM]:{type:Y.NEW_YORK_AM,name:"New York AM Session",timeRange:{start:"09:30:00",end:"12:00:00"},description:"New York morning session - high activity and volatility",timezone:"America/New_York",characteristics:["High Volume","Trend Development","Breakout Opportunities"],color:"#dc2626",macroPeriods:[{...ce[P.MORNING_BREAKOUT],id:"morning-breakout"},{...ce[P.MID_MORNING_REVERSION],id:"mid-morning-reversion"},{...ce[P.PRE_LUNCH],id:"pre-lunch"}]},[Y.NEW_YORK_PM]:{type:Y.NEW_YORK_PM,name:"New York PM Session",timeRange:{start:"12:00:00",end:"16:00:00"},description:"New York afternoon session - institutional activity increases toward close",timezone:"America/New_York",characteristics:["Institutional Flows","EOD Positioning","Power Hour Activity"],color:"#dc2626",macroPeriods:[{...ce[P.LUNCH_MACRO],id:"lunch-macro"},{...ce[P.POST_LUNCH],id:"post-lunch"},{...ce[P.PRE_CLOSE],id:"pre-close"},{...ce[P.POWER_HOUR],id:"power-hour"},{...ce[P.MOC],id:"moc"}]},[Y.LONDON]:{type:Y.LONDON,name:"London Session",timeRange:{start:"08:00:00",end:"16:00:00"},description:"London trading session - European market activity",timezone:"Europe/London",characteristics:["European Activity","Currency Focus","News-Driven"],color:"#1f2937",macroPeriods:[{...ce[P.LONDON_OPEN],id:"london-open"},{...ce[P.LONDON_NY_OVERLAP],id:"london-ny-overlap"}]},[Y.ASIA]:{type:Y.ASIA,name:"Asia Session",timeRange:{start:"18:00:00",end:"03:00:00"},description:"Asian trading session - typically lower volatility",timezone:"Asia/Tokyo",characteristics:["Lower Volume","Range Trading","News Reactions"],color:"#4b5563",macroPeriods:[]},[Y.PRE_MARKET]:{type:Y.PRE_MARKET,name:"Pre-Market",timeRange:{start:"04:00:00",end:"09:30:00"},description:"Pre-market trading hours",timezone:"America/New_York",characteristics:["Low Volume","News Reactions","Gap Setups"],color:"#6b7280",macroPeriods:[]},[Y.AFTER_HOURS]:{type:Y.AFTER_HOURS,name:"After Hours",timeRange:{start:"16:00:00",end:"20:00:00"},description:"After-hours trading",timezone:"America/New_York",characteristics:["Low Volume","Earnings Reactions","News-Driven"],color:"#6b7280",macroPeriods:[]},[Y.OVERNIGHT]:{type:Y.OVERNIGHT,name:"Overnight",timeRange:{start:"20:00:00",end:"04:00:00"},description:"Overnight session",timezone:"America/New_York",characteristics:["Very Low Volume","Futures Activity"],color:"#374151",macroPeriods:[]}};class te{static getSessionHierarchy(){return this.hierarchy||(this.hierarchy=this.buildHierarchy()),this.hierarchy}static buildHierarchy(){return Ci()}static timeToMinutes(r){const[t,s,i=0]=r.split(":").map(Number);return t*60+s+i/60}static minutesToTime(r){const t=Math.floor(r/60),s=Math.floor(r%60),i=Math.floor(r%1*60);return`${t.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`}static isTimeInRange(r,t){const s=this.timeToMinutes(r),i=this.timeToMinutes(t.start),c=this.timeToMinutes(t.end);return c<i?s>=i||s<=c:s>=i&&s<=c}static validateTime(r){var c;if(!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(r))return{isValid:!1,error:"Invalid time format. Use HH:MM or HH:MM:SS format."};const s=this.getSessionHierarchy(),i=[];for(const[a,p]of Object.entries(s.macrosByType))this.isTimeInRange(r,p.timeRange)&&i.push({type:a,macro:p,isSubPeriod:!!p.parentMacro});if(i.length>0){const p=i.sort((f,l)=>{if(f.isSubPeriod&&!l.isSubPeriod)return-1;if(!f.isSubPeriod&&l.isSubPeriod)return 1;const m=this.timeToMinutes(f.macro.timeRange.end)-this.timeToMinutes(f.macro.timeRange.start),x=this.timeToMinutes(l.macro.timeRange.end)-this.timeToMinutes(l.macro.timeRange.start);return m-x})[0],d=i.length>1;return{isValid:!0,suggestedMacro:p.type,suggestedSession:p.macro.parentSession||((c=p.macro.spansSessions)==null?void 0:c[0]),warning:d?`Time falls within ${i.length} overlapping macro periods. Suggesting most specific: ${p.macro.name}`:void 0}}for(const a of s.sessions)if(this.isTimeInRange(r,a.timeRange))return{isValid:!0,suggestedSession:a.type,warning:"Time falls within session but not in a specific macro period."};return{isValid:!0,warning:"Time does not fall within any defined session or macro period."}}static getSession(r){return this.getSessionHierarchy().sessionsByType[r]||null}static getMacroPeriod(r){return this.getSessionHierarchy().macrosByType[r]||null}static getMacroPeriodsForSession(r){const t=this.getSession(r);return(t==null?void 0:t.macroPeriods)||[]}static createSessionSelection(r,t,s){if(t){const i=this.getMacroPeriod(t);return{session:i==null?void 0:i.parentSession,macroPeriod:t,displayLabel:(i==null?void 0:i.name)||"Unknown Macro",selectionType:"macro"}}if(r){const i=this.getSession(r);return{session:r,displayLabel:(i==null?void 0:i.name)||"Unknown Session",selectionType:"session"}}return s?{customTimeRange:s,displayLabel:`${s.start} - ${s.end}`,selectionType:"custom"}:{displayLabel:"No Selection",selectionType:"custom"}}static filterSessions(r={}){var c,a;const t=this.getSessionHierarchy();let s=[...t.sessions],i=Object.values(t.macrosByType);return r.activeOnly&&(s=s.filter(p=>p.isActive)),(c=r.sessionTypes)!=null&&c.length&&(s=s.filter(p=>r.sessionTypes.includes(p.type))),(a=r.macroTypes)!=null&&a.length&&(i=i.filter(p=>r.macroTypes.includes(p.type))),r.highProbabilityOnly&&(i=i.filter(p=>p.isHighProbability)),r.minVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel>=r.minVolatility)),r.maxVolatility!==void 0&&(i=i.filter(p=>p.volatilityLevel<=r.maxVolatility)),{sessions:s,macros:i}}static getCurrentSession(){const r=new Date,t=`${r.getHours().toString().padStart(2,"0")}:${r.getMinutes().toString().padStart(2,"0")}:00`,s=this.validateTime(t);return s.suggestedMacro?this.createSessionSelection(s.suggestedSession,s.suggestedMacro):s.suggestedSession?this.createSessionSelection(s.suggestedSession):null}static timeRangesOverlap(r,t){const s=this.timeToMinutes(r.start),i=this.timeToMinutes(r.end),c=this.timeToMinutes(t.start),a=this.timeToMinutes(t.end);return Math.max(s,c)<Math.min(i,a)}static getDisplayOptions(){const r=this.getSessionHierarchy(),t=r.sessions.map(i=>({value:i.type,label:i.name,group:"Sessions"})),s=Object.values(r.macrosByType).filter(i=>i.parentSession).map(i=>{var c;return{value:i.type,label:i.name,group:((c=r.sessionsByType[i.parentSession])==null?void 0:c.name)||"Other",parentSession:i.parentSession}});return{sessionOptions:t,macroOptions:s}}static getOverlappingMacros(r){const t=this.getSessionHierarchy(),s=[];for(const[i,c]of Object.entries(t.macrosByType))this.isTimeInRange(r,c.timeRange)&&s.push({type:i,macro:c,isSubPeriod:!!c.parentMacro,isMultiSession:!!c.spansSessions});return s.sort((i,c)=>{if(i.isSubPeriod&&!c.isSubPeriod)return-1;if(!i.isSubPeriod&&c.isSubPeriod)return 1;const a=this.timeToMinutes(i.macro.timeRange.end)-this.timeToMinutes(i.macro.timeRange.start),p=this.timeToMinutes(c.macro.timeRange.end)-this.timeToMinutes(c.macro.timeRange.start);return a-p})}static getMultiSessionMacros(){return this.getSessionHierarchy().multiSessionMacros||[]}static hasSubPeriods(r){const t=this.getMacroPeriod(r);return!!(t!=null&&t.subPeriods&&t.subPeriods.length>0)}static getSubPeriods(r){const t=this.getMacroPeriod(r);return(t==null?void 0:t.subPeriods)||[]}static convertLegacySession(r){const s={"NY Open":{session:Y.NEW_YORK_AM},"London Open":{session:Y.LONDON},"Lunch Macro":{macro:P.LUNCH_MACRO_EXTENDED},"Lunch Macro (11:30-13:30)":{macro:P.LUNCH_MACRO_EXTENDED},"Lunch Macro (12:00-13:30)":{macro:P.LUNCH_MACRO},MOC:{macro:P.MOC},Overnight:{session:Y.OVERNIGHT},"Pre-Market":{session:Y.PRE_MARKET},"After Hours":{session:Y.AFTER_HOURS},"Power Hour":{macro:P.POWER_HOUR},"10:50-11:10":{macro:P.MID_MORNING_REVERSION},"11:50-12:10":{macro:P.PRE_LUNCH},"15:15-15:45":{macro:P.POWER_HOUR}}[r];return s?this.createSessionSelection(s.session,s.macro):null}}xe(te,"hierarchy",null);const $t=(e={})=>{const{initialSelection:r,autoDetectCurrent:t=!1,filterOptions:s={},onSelectionChange:i,validateTimes:c=!0}=e,[a,p]=y.useState(r||{displayLabel:"No Selection",selectionType:"custom"}),d=y.useMemo(()=>te.getCurrentSession(),[]),f=y.useMemo(()=>d!==null,[d]),{availableSessions:l,availableMacros:m}=y.useMemo(()=>{const{sessions:k,macros:R}=te.filterSessions(s),{sessionOptions:j,macroOptions:L}=te.getDisplayOptions(),W=j.filter(_=>k.some(q=>q.type===_.value)),V=L.filter(_=>R.some(q=>q.type===_.value));return{availableSessions:W,availableMacros:V}},[s]),x=y.useMemo(()=>l.map(k=>{const R=m.filter(j=>j.parentSession===k.value).map(j=>({value:j.value,label:j.label}));return{session:k.value,sessionLabel:k.label,macros:R}}),[l,m]);y.useEffect(()=>{t&&d&&!r&&p(d)},[t,d,r]),y.useEffect(()=>{i==null||i(a)},[a,i]);const h=y.useCallback(k=>{const R=te.createSessionSelection(k);p(R)},[]),g=y.useCallback(k=>{const R=te.createSessionSelection(void 0,k);p(R)},[]),b=y.useCallback(k=>{const R=te.createSessionSelection(void 0,void 0,k);p(R)},[]),T=y.useCallback(()=>{p({displayLabel:"No Selection",selectionType:"custom"})},[]),w=y.useCallback(k=>c?te.validateTime(k):{isValid:!0},[c]),C=y.useMemo(()=>{if(a.selectionType==="session"&&a.session)return te.getSession(a.session)!==null;if(a.selectionType==="macro"&&a.macroPeriod)return te.getMacroPeriod(a.macroPeriod)!==null;if(a.selectionType==="custom"&&a.customTimeRange){const k=w(a.customTimeRange.start),R=w(a.customTimeRange.end);return k.isValid&&R.isValid}return a.selectionType==="custom"&&!a.customTimeRange},[a,w]),M=y.useCallback(k=>te.getSession(k),[]),A=y.useCallback(k=>te.getMacroPeriod(k),[]),N=y.useCallback(k=>te.convertLegacySession(k),[]);return{selection:a,selectSession:h,selectMacro:g,selectCustomRange:b,clearSelection:T,validateTime:w,isValidSelection:C,availableSessions:l,availableMacros:m,hierarchicalOptions:x,currentSession:d,isCurrentSessionActive:f,getSessionDetails:M,getMacroDetails:A,convertLegacySession:N}},Ei=n.div.withConfig({displayName:"Container",componentId:"sc-1reqqnl-0"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),Ii=n.div.withConfig({displayName:"SelectorContainer",componentId:"sc-1reqqnl-1"})(["position:relative;border:1px solid ",";border-radius:",";background:",";transition:all 0.2s ease;opacity:",";pointer-events:",";&:hover{border-color:","40;}&:focus-within{border-color:",";box-shadow:0 0 0 3px ","20;}"],({theme:e,hasError:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.error)||"#ef4444":((s=e.colors)==null?void 0:s.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({disabled:e})=>e?.6:1,({disabled:e})=>e?"none":"auto",({theme:e,hasError:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.error)||"#ef4444":((s=e.colors)==null?void 0:s.primary)||"#dc2626"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),ji=n.div.withConfig({displayName:"SelectedValue",componentId:"sc-1reqqnl-2"})(["padding:",";color:",";font-size:",";cursor:pointer;display:flex;align-items:center;justify-content:space-between;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.md)||"1rem"}),Ni=n.div.withConfig({displayName:"DropdownIcon",componentId:"sc-1reqqnl-3"})(["transition:transform 0.2s ease;transform:",";color:",";"],({isOpen:e})=>e?"rotate(180deg)":"rotate(0deg)",({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),ki=n.div.withConfig({displayName:"DropdownMenu",componentId:"sc-1reqqnl-4"})(["position:absolute;top:100%;left:0;right:0;z-index:1000;background:",";border:1px solid ",";border-radius:",";box-shadow:0 10px 25px -5px rgba(0,0,0,0.3);max-height:400px;overflow-y:auto;display:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"6px"},({isOpen:e})=>e?"block":"none"),Li=n.div.withConfig({displayName:"MultiSessionGroup",componentId:"sc-1reqqnl-5"})(["border-bottom:1px solid ",";background:",";&:last-child{border-bottom:none;}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"}),_i=n.div.withConfig({displayName:"MultiSessionHeader",componentId:"sc-1reqqnl-6"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;border-left:3px solid ",";&:hover{background:","40;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e,isSelected:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((s=e.colors)==null?void 0:s.surface)||"#1f2937"},({theme:e,isSelected:r})=>{var t;return r?"#ffffff":((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.warning)||"#f59e0b"},({theme:e,isSelected:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((s=e.colors)==null?void 0:s.border)||"#4b5563"}),Mi=n.div.withConfig({displayName:"MultiSessionIndicator",componentId:"sc-1reqqnl-7"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.warning)||"#f59e0b"}),Ri=n.div.withConfig({displayName:"SessionGroup",componentId:"sc-1reqqnl-8"})(["border-bottom:1px solid ",";&:last-child{border-bottom:none;}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"}),Pi=n.div.withConfig({displayName:"SessionHeader",componentId:"sc-1reqqnl-9"})(["padding:",";background:",";color:",";font-weight:600;cursor:pointer;display:flex;align-items:center;justify-content:space-between;transition:background-color 0.2s ease;&:hover{background:","40;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e,isSelected:r})=>{var t;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":"transparent"},({theme:e,isSelected:r})=>{var t;return r?"#ffffff":((t=e.colors)==null?void 0:t.textPrimary)||"#ffffff"},({theme:e,isSelected:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((s=e.colors)==null?void 0:s.border)||"#4b5563"}),Di=n.div.withConfig({displayName:"MacroList",componentId:"sc-1reqqnl-10"})(["background:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"}),$i=n.div.withConfig({displayName:"MacroItem",componentId:"sc-1reqqnl-11"})(["padding:"," ",";color:",";cursor:pointer;font-size:",";transition:all 0.2s ease;border-left:3px solid ",";&:hover{background:","20;color:",";}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"24px"},({theme:e,isSelected:r})=>{var t,s;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":((s=e.colors)==null?void 0:s.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e,isSelected:r})=>{var t;return r?((t=e.colors)==null?void 0:t.primary)||"#dc2626":"transparent"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),Oi=n.div.withConfig({displayName:"CurrentSessionIndicator",componentId:"sc-1reqqnl-12"})(["display:inline-flex;align-items:center;gap:",";font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.success)||"#10b981"}),Ai=n.div.withConfig({displayName:"ErrorMessage",componentId:"sc-1reqqnl-13"})(["color:",";font-size:",";margin-top:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#ef4444"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),zi=({value:e,onChange:r,showMacroPeriods:t=!0,showCurrentSession:s=!0,placeholder:i="Select session or macro period",disabled:c=!1,error:a,className:p})=>{const[d,f]=y.useState(!1),{hierarchicalOptions:l,currentSession:m,selectSession:x,selectMacro:h}=$t({onSelectionChange:r}),g=y.useMemo(()=>te.getMultiSessionMacros(),[]),b=y.useMemo(()=>e!=null&&e.displayLabel?e.displayLabel:i,[e,i]),T=N=>{x(N),f(!1)},w=N=>{h(N),f(!1)},C=N=>(e==null?void 0:e.session)===N&&(e==null?void 0:e.selectionType)==="session",M=N=>(e==null?void 0:e.macroPeriod)===N&&(e==null?void 0:e.selectionType)==="macro",A=N=>(m==null?void 0:m.session)===N;return o.jsxs(Ei,{className:p,hasError:!!a,children:[o.jsxs(Ii,{hasError:!!a,disabled:c,onClick:()=>!c&&f(!d),children:[o.jsxs(ji,{children:[o.jsx("span",{children:b}),o.jsx(Ni,{isOpen:d,children:"▼"})]}),o.jsxs(ki,{isOpen:d,children:[t&&g.length>0&&o.jsx(Li,{children:g.map(N=>o.jsxs(_i,{isSelected:M(N.type),onClick:k=>{k.stopPropagation(),w(N.type)},children:[o.jsx("span",{children:N.name}),o.jsx(Mi,{children:"🌐 MULTI-SESSION"})]},N.type))}),l.map(({session:N,sessionLabel:k,macros:R})=>o.jsxs(Ri,{children:[o.jsxs(Pi,{isSelected:C(N),onClick:j=>{j.stopPropagation(),T(N)},children:[o.jsx("span",{children:k}),s&&A(N)&&o.jsx(Oi,{children:"🔴 LIVE"})]}),t&&R.length>0&&o.jsx(Di,{children:R.map(({value:j,label:L})=>o.jsxs($i,{isSelected:M(j),onClick:W=>{W.stopPropagation(),w(j)},children:[L,te.hasSubPeriods(j)&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.75rem",opacity:.7},children:"📋 Has sub-periods"})]},j))})]},N))]})]}),a&&o.jsx(Ai,{children:a})]})},I={DATE:"date",SYMBOL:"symbol",DIRECTION:"direction",MODEL_TYPE:"model_type",SESSION:"session",ENTRY_PRICE:"entry_price",EXIT_PRICE:"exit_price",R_MULTIPLE:"r_multiple",ACHIEVED_PL:"achieved_pl",WIN_LOSS:"win_loss",PATTERN_QUALITY:"pattern_quality_rating",ENTRY_TIME:"entry_time",EXIT_TIME:"exit_time"},Tr=n.span.withConfig({displayName:"ProfitLossCell",componentId:"sc-14bks31-0"})(["color:",";font-weight:",";"],({isProfit:e,theme:r})=>e?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),Ot=n(De).withConfig({displayName:"DirectionBadge",componentId:"sc-14bks31-1"})(["background-color:",";color:white;"],({direction:e,theme:r})=>e==="Long"?r.colors.success||"#10b981":r.colors.error||"#ef4444"),At=n.span.withConfig({displayName:"QualityRating",componentId:"sc-14bks31-2"})(["color:",";font-weight:",";"],({rating:e,theme:r})=>e>=4?r.colors.success||"#10b981":e>=3?r.colors.warning||"#f59e0b":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),Er=n.span.withConfig({displayName:"RMultipleCell",componentId:"sc-14bks31-3"})(["color:",";font-weight:",";"],({rMultiple:e,theme:r})=>e>0?r.colors.success||"#10b981":r.colors.error||"#ef4444",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600}),Re=e=>e==null?"-":new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2}).format(e),Ir=e=>{try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}catch{return e}},vr=e=>e||"-",zt=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ir(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"80px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Direction",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(Ot,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION]})},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.ENTRY_PRICE,header:"Entry",sortable:!0,width:"100px",align:"right",cell:e=>Re(e.trade[I.ENTRY_PRICE])},{id:I.EXIT_PRICE,header:"Exit",sortable:!0,width:"100px",align:"right",cell:e=>Re(e.trade[I.EXIT_PRICE])},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>{var r;return o.jsx(Er,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(r=e.trade[I.R_MULTIPLE])==null?void 0:r.toFixed(2)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(Tr,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Re(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(De,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(At,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.ENTRY_TIME,header:"Entry Time",sortable:!0,width:"100px",align:"center",cell:e=>vr(e.trade[I.ENTRY_TIME])},{id:I.EXIT_TIME,header:"Exit Time",sortable:!0,width:"100px",align:"center",cell:e=>vr(e.trade[I.EXIT_TIME])}],Ft=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"90px",cell:e=>Ir(e.trade[I.DATE])},{id:I.SYMBOL,header:"Symbol",sortable:!0,width:"60px",cell:e=>e.trade.market||"MNQ"},{id:I.DIRECTION,header:"Dir",sortable:!0,width:"50px",align:"center",cell:e=>o.jsx(Ot,{direction:e.trade[I.DIRECTION],size:"small",children:e.trade[I.DIRECTION].charAt(0)})},{id:I.R_MULTIPLE,header:"R",sortable:!0,width:"60px",align:"right",cell:e=>{var r;return o.jsx(Er,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(r=e.trade[I.R_MULTIPLE])==null?void 0:r.toFixed(1)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"80px",align:"right",cell:e=>o.jsx(Tr,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Re(e.trade[I.ACHIEVED_PL])})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"60px",align:"center",cell:e=>o.jsx(De,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]==="Win"?"W":e.trade[I.WIN_LOSS]==="Loss"?"L":"-"})}],Bt=()=>[{id:I.DATE,header:"Date",sortable:!0,width:"100px",cell:e=>Ir(e.trade[I.DATE])},{id:I.MODEL_TYPE,header:"Model",sortable:!0,width:"120px",cell:e=>e.trade[I.MODEL_TYPE]||"-"},{id:I.SESSION,header:"Session",sortable:!0,width:"120px",cell:e=>e.trade[I.SESSION]||"-"},{id:I.R_MULTIPLE,header:"R Multiple",sortable:!0,width:"100px",align:"right",cell:e=>{var r;return o.jsx(Er,{rMultiple:e.trade[I.R_MULTIPLE]||0,children:e.trade[I.R_MULTIPLE]?`${(r=e.trade[I.R_MULTIPLE])==null?void 0:r.toFixed(2)}R`:"-"})}},{id:I.ACHIEVED_PL,header:"P&L",sortable:!0,width:"100px",align:"right",cell:e=>o.jsx(Tr,{isProfit:(e.trade[I.ACHIEVED_PL]||0)>0,children:Re(e.trade[I.ACHIEVED_PL])})},{id:I.PATTERN_QUALITY,header:"Quality",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(At,{rating:e.trade[I.PATTERN_QUALITY]||0,children:e.trade[I.PATTERN_QUALITY]?`${e.trade[I.PATTERN_QUALITY]}/5`:"-"})},{id:I.WIN_LOSS,header:"Result",sortable:!0,width:"80px",align:"center",cell:e=>o.jsx(De,{variant:e.trade[I.WIN_LOSS]==="Win"?"success":"error",size:"small",children:e.trade[I.WIN_LOSS]||"-"})}],Fi=n.tr.withConfig({displayName:"TableRow",componentId:"sc-uyrnn-0"})([""," "," "," "," ",""],({striped:e,theme:r,isSelected:t})=>{var s;return e&&!t&&n.css(["&:nth-child(even){background-color:","50;}"],((s=r.colors)==null?void 0:s.background)||"#f8f9fa")},({hoverable:e,theme:r,isSelected:t})=>{var s;return e&&!t&&n.css(["&:hover{background-color:","aa;}"],((s=r.colors)==null?void 0:s.background)||"#f8f9fa")},({isSelected:e,theme:r})=>{var t;return e&&n.css(["background-color:","15;"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")},({isClickable:e})=>e&&n.css(["cursor:pointer;"]),({isExpanded:e,theme:r})=>{var t;return e&&n.css(["border-bottom:2px solid ",";"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")}),nt=n.td.withConfig({displayName:"TableCell",componentId:"sc-uyrnn-1"})(["text-align:",";border-bottom:1px solid ",";color:",";padding:"," ",";vertical-align:middle;"],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),Bi=n.tr.withConfig({displayName:"ExpandedRow",componentId:"sc-uyrnn-2"})(["display:",";"],({isVisible:e})=>e?"table-row":"none"),qi=n.td.withConfig({displayName:"ExpandedCell",componentId:"sc-uyrnn-3"})(["padding:0;border-bottom:1px solid ",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),Hi=n.div.withConfig({displayName:"ExpandedContent",componentId:"sc-uyrnn-4"})(["padding:",";background-color:","30;border-left:3px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),Ui=n.button.withConfig({displayName:"ExpandButton",componentId:"sc-uyrnn-5"})(["background:none;border:none;cursor:pointer;padding:",";color:",";font-size:",";display:flex;align-items:center;justify-content:center;border-radius:",";transition:all 0.2s ease;&:hover{background-color:",";color:",";}&:focus{outline:2px solid ",";outline-offset:2px;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),Vi=n.span.withConfig({displayName:"ExpandIcon",componentId:"sc-uyrnn-6"})(["display:inline-block;transition:transform 0.2s ease;transform:",";&::after{content:'▶';}"],({isExpanded:e})=>e?"rotate(90deg)":"rotate(0deg)"),Yi=n.div.withConfig({displayName:"TradeDetails",componentId:"sc-uyrnn-7"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"}),ke=n.div.withConfig({displayName:"DetailGroup",componentId:"sc-uyrnn-8"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),Le=n.span.withConfig({displayName:"DetailLabel",componentId:"sc-uyrnn-9"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),se=n.span.withConfig({displayName:"DetailValue",componentId:"sc-uyrnn-10"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#111827"}),Gi=({trade:e})=>o.jsxs(Yi,{children:[e.fvg_details&&o.jsxs(ke,{children:[o.jsx(Le,{children:"FVG Details"}),o.jsxs(se,{children:["Type: ",e.fvg_details.rd_type||"-"]}),o.jsxs(se,{children:["Entry Version: ",e.fvg_details.entry_version||"-"]}),o.jsxs(se,{children:["Draw on Liquidity: ",e.fvg_details.draw_on_liquidity||"-"]})]}),e.setup&&o.jsxs(ke,{children:[o.jsx(Le,{children:"Setup Classification"}),o.jsxs(se,{children:["Primary: ",e.setup.primary_setup||"-"]}),o.jsxs(se,{children:["Secondary: ",e.setup.secondary_setup||"-"]}),o.jsxs(se,{children:["Liquidity: ",e.setup.liquidity_taken||"-"]})]}),e.analysis&&o.jsxs(ke,{children:[o.jsx(Le,{children:"Analysis"}),o.jsxs(se,{children:["DOL Target: ",e.analysis.dol_target_type||"-"]}),o.jsxs(se,{children:["Path Quality: ",e.analysis.path_quality||"-"]}),o.jsxs(se,{children:["Clustering: ",e.analysis.clustering||"-"]})]}),o.jsxs(ke,{children:[o.jsx(Le,{children:"Timing"}),o.jsxs(se,{children:["Entry: ",e.trade.entry_time||"-"]}),o.jsxs(se,{children:["Exit: ",e.trade.exit_time||"-"]}),o.jsxs(se,{children:["FVG: ",e.trade.fvg_time||"-"]}),o.jsxs(se,{children:["RD: ",e.trade.rd_time||"-"]})]}),e.trade.notes&&o.jsxs(ke,{style:{gridColumn:"1 / -1"},children:[o.jsx(Le,{children:"Notes"}),o.jsx(se,{children:e.trade.notes})]})]}),qt=({trade:e,index:r,columns:t,isSelected:s=!1,hoverable:i=!0,striped:c=!0,expandable:a=!1,isExpanded:p=!1,onRowClick:d,onToggleExpand:f,expandedContent:l})=>{const[m,x]=y.useState(!1),h=p!==void 0?p:m,g=w=>{w.target.closest("button")||d==null||d(e,r)},b=w=>{w.stopPropagation(),f?f(e,r):x(!m)},T=t.filter(w=>!w.hidden);return o.jsxs(o.Fragment,{children:[o.jsxs(Fi,{hoverable:i,striped:c,isSelected:s,isClickable:!!d,isExpanded:h,onClick:g,children:[a&&o.jsx(nt,{align:"center",style:{width:"40px",padding:"8px"},children:o.jsx(Ui,{onClick:b,children:o.jsx(Vi,{isExpanded:h})})}),T.map(w=>o.jsx(nt,{align:w.align,children:w.cell(e,r)},w.id))]}),a&&o.jsx(Bi,{isVisible:h,children:o.jsx(qi,{colSpan:T.length+1,children:o.jsx(Hi,{children:l||o.jsx(Gi,{trade:e})})})})]})},de={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",DATE_FROM:"dateFrom",DATE_TO:"dateTo",SESSION:"session",DIRECTION:"direction",MARKET:"market",MIN_R_MULTIPLE:"min_r_multiple",MAX_R_MULTIPLE:"max_r_multiple",MIN_PATTERN_QUALITY:"min_pattern_quality",MAX_PATTERN_QUALITY:"max_pattern_quality"},Wi=n.div.withConfig({displayName:"FiltersContainer",componentId:"sc-32k3gq-0"})(["display:flex;flex-direction:column;gap:",";padding:",";background-color:",";border-radius:",";border:1px solid ",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.md)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),it=n.div.withConfig({displayName:"FilterRow",componentId:"sc-32k3gq-1"})(["display:flex;gap:",";align-items:end;flex-wrap:wrap;@media (max-width:768px){flex-direction:column;align-items:stretch;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),fe=n.div.withConfig({displayName:"FilterGroup",componentId:"sc-32k3gq-2"})(["display:flex;flex-direction:column;gap:",";min-width:120px;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),me=n.label.withConfig({displayName:"FilterLabel",componentId:"sc-32k3gq-3"})(["font-size:",";font-weight:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.medium)||500},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),Ki=n.div.withConfig({displayName:"FilterActions",componentId:"sc-32k3gq-4"})(["display:flex;gap:",";align-items:center;margin-left:auto;@media (max-width:768px){margin-left:0;justify-content:flex-end;}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"12px"}),Qi=n.div.withConfig({displayName:"AdvancedFilters",componentId:"sc-32k3gq-5"})(["display:",";flex-direction:column;gap:",";padding-top:",";border-top:1px solid ",";"],({isVisible:e})=>e?"flex":"none",({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"}),at=n.div.withConfig({displayName:"RangeInputGroup",componentId:"sc-32k3gq-6"})(["display:flex;gap:",";align-items:center;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),ct=n.span.withConfig({displayName:"RangeLabel",componentId:"sc-32k3gq-7"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),Ht=({filters:e,onFiltersChange:r,onReset:t,isLoading:s=!1,showAdvanced:i=!1,onToggleAdvanced:c})=>{var f,l,m,x;const a=(h,g)=>{r({...e,[h]:g})},p=()=>{r({}),t==null||t()},d=Object.values(e).some(h=>h!==void 0&&h!==""&&h!==null);return o.jsxs(Wi,{children:[o.jsxs(it,{children:[o.jsxs(fe,{children:[o.jsx(me,{children:"Date From"}),o.jsx(be,{type:"date",value:e.dateFrom||"",onChange:h=>a(de.DATE_FROM,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Date To"}),o.jsx(be,{type:"date",value:e.dateTo||"",onChange:h=>a(de.DATE_TO,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Model Type"}),o.jsx(we,{options:[{value:"",label:"All Models"},{value:"RD-Cont",label:"RD-Cont"},{value:"FVG-RD",label:"FVG-RD"},{value:"True-RD",label:"True-RD"},{value:"IMM-RD",label:"IMM-RD"},{value:"Dispersed-RD",label:"Dispersed-RD"},{value:"Wide-Gap-RD",label:"Wide-Gap-RD"}],value:e.model_type||"",onChange:h=>a(de.MODEL_TYPE,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Session"}),o.jsx(we,{options:[{value:"",label:"All Sessions"},{value:"Pre-Market",label:"Pre-Market"},{value:"NY Open",label:"NY Open"},{value:"10:50-11:10",label:"10:50-11:10"},{value:"11:50-12:10",label:"11:50-12:10"},{value:"Lunch Macro",label:"Lunch Macro"},{value:"13:50-14:10",label:"13:50-14:10"},{value:"14:50-15:10",label:"14:50-15:10"},{value:"15:15-15:45",label:"15:15-15:45"},{value:"MOC",label:"MOC"},{value:"Post MOC",label:"Post MOC"}],value:e.session||"",onChange:h=>a(de.SESSION,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Direction"}),o.jsx(we,{options:[{value:"",label:"All Directions"},{value:"Long",label:"Long"},{value:"Short",label:"Short"}],value:e.direction||"",onChange:h=>a(de.DIRECTION,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Result"}),o.jsx(we,{options:[{value:"",label:"All Results"},{value:"Win",label:"Win"},{value:"Loss",label:"Loss"}],value:e.win_loss||"",onChange:h=>a(de.WIN_LOSS,h),disabled:s})]}),o.jsxs(Ki,{children:[c&&o.jsxs(ne,{variant:"outline",size:"small",onClick:c,disabled:s,children:[i?"Hide":"Show"," Advanced"]}),o.jsx(ne,{variant:"outline",size:"small",onClick:p,disabled:s||!d,children:"Reset"})]})]}),o.jsx(Qi,{isVisible:i,children:o.jsxs(it,{children:[o.jsxs(fe,{children:[o.jsx(me,{children:"Market"}),o.jsx(we,{options:[{value:"",label:"All Markets"},{value:"MNQ",label:"MNQ"},{value:"NQ",label:"NQ"},{value:"ES",label:"ES"},{value:"MES",label:"MES"},{value:"YM",label:"YM"},{value:"MYM",label:"MYM"}],value:e.market||"",onChange:h=>a(de.MARKET,h),disabled:s})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"R Multiple Range"}),o.jsxs(at,{children:[o.jsx(be,{type:"number",placeholder:"Min",step:"0.1",value:((f=e.min_r_multiple)==null?void 0:f.toString())||"",onChange:h=>a(de.MIN_R_MULTIPLE,h?Number(h):void 0),disabled:s,style:{width:"80px"}}),o.jsx(ct,{children:"to"}),o.jsx(be,{type:"number",placeholder:"Max",step:"0.1",value:((l=e.max_r_multiple)==null?void 0:l.toString())||"",onChange:h=>a(de.MAX_R_MULTIPLE,h?Number(h):void 0),disabled:s,style:{width:"80px"}})]})]}),o.jsxs(fe,{children:[o.jsx(me,{children:"Pattern Quality Range"}),o.jsxs(at,{children:[o.jsx(be,{type:"number",placeholder:"Min",min:"1",max:"5",step:"0.1",value:((m=e.min_pattern_quality)==null?void 0:m.toString())||"",onChange:h=>a(de.MIN_PATTERN_QUALITY,h?Number(h):void 0),disabled:s,style:{width:"80px"}}),o.jsx(ct,{children:"to"}),o.jsx(be,{type:"number",placeholder:"Max",min:"1",max:"5",step:"0.1",value:((x=e.max_pattern_quality)==null?void 0:x.toString())||"",onChange:h=>a(de.MAX_PATTERN_QUALITY,h?Number(h):void 0),disabled:s,style:{width:"80px"}})]})]})]})})]})},Xi=n.div.withConfig({displayName:"TableContainer",componentId:"sc-13oxwmo-0"})(["width:100%;overflow:auto;"," ",""],({height:e})=>e&&`height: ${e};`,({scrollable:e})=>e&&"overflow-x: auto;"),Ji=n.table.withConfig({displayName:"StyledTable",componentId:"sc-13oxwmo-1"})(["width:100%;border-collapse:separate;border-spacing:0;font-size:",";"," ",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({bordered:e,theme:r})=>{var t,s;return e&&n.css(["border:1px solid ",";border-radius:",";"],((t=r.colors)==null?void 0:t.border)||"#e5e7eb",((s=r.borderRadius)==null?void 0:s.sm)||"4px")},({compact:e,theme:r})=>{var t,s,i,c;return e?n.css(["th,td{padding:"," ",";}"],((t=r.spacing)==null?void 0:t.xs)||"8px",((s=r.spacing)==null?void 0:s.sm)||"12px"):n.css(["th,td{padding:"," ",";}"],((i=r.spacing)==null?void 0:i.sm)||"12px",((c=r.spacing)==null?void 0:c.md)||"16px")}),Zi=n.thead.withConfig({displayName:"TableHeader",componentId:"sc-13oxwmo-2"})(["",""],({stickyHeader:e})=>e&&n.css(["position:sticky;top:0;z-index:1;"])),ea=n.tr.withConfig({displayName:"TableHeaderRow",componentId:"sc-13oxwmo-3"})(["background-color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"}),lt=n.th.withConfig({displayName:"TableHeaderCell",componentId:"sc-13oxwmo-4"})(["text-align:",";font-weight:",";color:",";border-bottom:1px solid ",";white-space:nowrap;"," "," ",""],({align:e})=>e||"left",({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||600},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#e5e7eb"},({width:e})=>e&&`width: ${e};`,({sortable:e})=>e&&n.css(["cursor:pointer;user-select:none;&:hover{background-color:","aa;}"],({theme:r})=>{var t;return((t=r.colors)==null?void 0:t.background)||"#f8f9fa"}),({isSorted:e,theme:r})=>{var t;return e&&n.css(["color:",";"],((t=r.colors)==null?void 0:t.primary)||"#3b82f6")}),ra=n.span.withConfig({displayName:"SortIcon",componentId:"sc-13oxwmo-5"})(["display:inline-block;margin-left:",";&::after{content:'","';}"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"},({direction:e})=>e==="asc"?"↑":e==="desc"?"↓":"↕"),ta=n.tbody.withConfig({displayName:"TableBody",componentId:"sc-13oxwmo-6"})([""]),oa=n.div.withConfig({displayName:"EmptyState",componentId:"sc-13oxwmo-7"})(["padding:",";text-align:center;color:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xl)||"32px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),sa=n.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-13oxwmo-8"})(["position:absolute;top:0;left:0;right:0;bottom:0;background-color:",";display:flex;align-items:center;justify-content:center;z-index:1;"],({theme:e})=>{var r;return`${((r=e.colors)==null?void 0:r.background)||"#ffffff"}80`}),na=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-13oxwmo-9"})(["width:32px;height:32px;border:3px solid ",";border-top:3px solid ",";border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#f8f9fa"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#3b82f6"}),ia=n.div.withConfig({displayName:"PaginationContainer",componentId:"sc-13oxwmo-10"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;font-size:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"16px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"}),aa=n.div.withConfig({displayName:"PageInfo",componentId:"sc-13oxwmo-11"})(["color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#6b7280"}),ca=n.div.withConfig({displayName:"PaginationControls",componentId:"sc-13oxwmo-12"})(["display:flex;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"8px"}),la=({data:e,isLoading:r=!1,bordered:t=!0,striped:s=!0,hoverable:i=!0,compact:c=!1,stickyHeader:a=!1,height:p="",onRowClick:d,isRowSelected:f,onSort:l,sortColumn:m="",sortDirection:x="asc",pagination:h=!1,currentPage:g=1,pageSize:b=10,totalRows:T=0,onPageChange:w,onPageSizeChange:C,className:M="",emptyMessage:A="No trades available",scrollable:N=!0,showFilters:k=!1,filters:R={},onFiltersChange:j,columnPreset:L="default",customColumns:W,expandableRows:V=!1,renderExpandedContent:_})=>{const[q,J]=y.useState(!1),Z=y.useMemo(()=>{if(W)return W;switch(L){case"compact":return Ft();case"performance":return Bt();default:return zt()}},[W,L]),le=y.useMemo(()=>Z.filter(B=>!B.hidden),[Z]),ee=y.useMemo(()=>Math.ceil(T/b),[T,b]),$=y.useMemo(()=>{if(!h)return e;const B=(g-1)*b,ue=B+b;return T>0&&e.length<=b?e:e.slice(B,ue)},[e,h,g,b,T]),K=B=>{if(!l)return;l(B,m===B&&x==="asc"?"desc":"asc")},he=B=>{B<1||B>ee||!w||w(B)};return o.jsxs("div",{children:[k&&j&&o.jsx(Ht,{filters:R,onFiltersChange:j,isLoading:r,showAdvanced:q,onToggleAdvanced:()=>J(!q)}),o.jsxs("div",{style:{position:"relative"},children:[r&&o.jsx(sa,{children:o.jsx(na,{})}),o.jsx(Xi,{height:p,scrollable:N,children:o.jsxs(Ji,{bordered:t,striped:s,compact:c,className:M,children:[o.jsx(Zi,{stickyHeader:a,children:o.jsxs(ea,{children:[V&&o.jsx(lt,{width:"40px",align:"center"}),le.map(B=>o.jsxs(lt,{sortable:B.sortable,isSorted:m===B.id,align:B.align,width:B.width,onClick:()=>B.sortable&&K(B.id),children:[B.header,B.sortable&&o.jsx(ra,{direction:m===B.id?x:void 0})]},B.id))]})}),o.jsx(ta,{children:$.length>0?$.map((B,ue)=>o.jsx(qt,{trade:B,index:ue,columns:le,isSelected:f?f(B,ue):!1,hoverable:i,striped:s,expandable:V,onRowClick:d,expandedContent:_==null?void 0:_(B)},B.trade.id||ue)):o.jsx("tr",{children:o.jsx("td",{colSpan:le.length+(V?1:0),children:o.jsx(oa,{children:A})})})})]})}),h&&ee>0&&o.jsxs(ia,{children:[o.jsxs(aa,{children:["Showing ",Math.min((g-1)*b+1,T)," to"," ",Math.min(g*b,T)," of ",T," entries"]}),o.jsxs(ca,{children:[o.jsx(ne,{size:"small",variant:"outline",onClick:()=>he(1),disabled:g===1,children:"First"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>he(g-1),disabled:g===1,children:"Prev"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>he(g+1),disabled:g===ee,children:"Next"}),o.jsx(ne,{size:"small",variant:"outline",onClick:()=>he(ee),disabled:g===ee,children:"Last"})]})]})]})]})},da=n.div.withConfig({displayName:"HeaderActions",componentId:"sc-1l7c7gv-0"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),ua=({title:e,children:r,isLoading:t=!1,hasError:s=!1,errorMessage:i="An error occurred while loading data",showRetry:c=!0,onRetry:a,isEmpty:p=!1,emptyMessage:d="No data available",emptyActionText:f,onEmptyAction:l,actionButton:m,className:x,...h})=>{const g=o.jsx(da,{children:m});let b;return t?b=o.jsx(It,{variant:"card",text:"Loading data..."}):s?b=o.jsx(yr,{title:"Error",description:i,variant:"compact",actionText:c?"Retry":void 0,onAction:c?a:void 0}):p?b=o.jsx(yr,{title:"No Data",description:d,variant:"compact",actionText:f,onAction:l}):b=r,o.jsx(Lt,{title:e,actions:g,className:x,...h,children:b})},pa=n.div.withConfig({displayName:"SectionContainer",componentId:"sc-14y246p-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";margin-bottom:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg,({theme:e})=>e.spacing.lg),fa=n.div.withConfig({displayName:"SectionHeader",componentId:"sc-14y246p-1"})(["display:flex;justify-content:space-between;align-items:center;margin-bottom:",";padding-bottom:",";border-bottom:1px solid ",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.spacing.sm,({theme:e})=>e.colors.border),ma=n.h2.withConfig({displayName:"SectionTitle",componentId:"sc-14y246p-2"})(["color:",";font-size:",";font-weight:600;margin:0;"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),ga=n.div.withConfig({displayName:"SectionActions",componentId:"sc-14y246p-3"})(["display:flex;gap:",";"],({theme:e})=>e.spacing.sm),ha=n.div.withConfig({displayName:"SectionContent",componentId:"sc-14y246p-4"})(["min-height:200px;"]),dt=n.div.withConfig({displayName:"LoadingState",componentId:"sc-14y246p-5"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";"],({theme:e})=>e.colors.textSecondary),xa=n.div.withConfig({displayName:"ErrorState",componentId:"sc-14y246p-6"})(["display:flex;align-items:center;justify-content:center;min-height:200px;color:",";text-align:center;"],({theme:e})=>e.colors.danger),ba=({name:e,title:r,children:t,actions:s,isLoading:i=!1,error:c=null,className:a,collapsible:p=!1,defaultCollapsed:d=!1})=>{const[f,l]=y.useState(d),m=()=>{p&&l(!f)},x=r||e.charAt(0).toUpperCase()+e.slice(1),h=()=>c?o.jsx(xa,{children:o.jsxs("div",{children:[o.jsxs("div",{children:["Error loading ",e]}),o.jsx("div",{style:{fontSize:"0.9em",marginTop:"8px"},children:c})]})}):i?o.jsxs(dt,{children:["Loading ",e,"..."]}):t||o.jsxs(dt,{children:["No ",e," data available"]});return o.jsxs(pa,{className:a,"data-section":e,children:[o.jsxs(fa,{children:[o.jsxs(ma,{onClick:m,style:{cursor:p?"pointer":"default"},children:[x,p&&o.jsx("span",{style:{marginLeft:"8px",fontSize:"0.8em"},children:f?"▶":"▼"})]}),s&&o.jsx(ga,{children:s})]}),!f&&o.jsx(ha,{children:h()})]})},ya=ba,va=n.div.withConfig({displayName:"Container",componentId:"sc-djltr5-0"})(["display:grid;grid-template-areas:'header header' 'sidebar content';grid-template-columns:",";grid-template-rows:auto 1fr;height:100vh;width:100%;overflow:hidden;transition:grid-template-columns "," ease;"],({sidebarCollapsed:e})=>e?"auto 1fr":"240px 1fr",({theme:e})=>e.transitions.normal),Sa=n.header.withConfig({displayName:"HeaderContainer",componentId:"sc-djltr5-1"})(["grid-area:header;background-color:",";border-bottom:1px solid ",";padding:",";z-index:",";"],({theme:e})=>e.colors.headerBackground,({theme:e})=>e.colors.border,({theme:e})=>e.spacing.md,({theme:e})=>e.zIndex.fixed),wa=n.aside.withConfig({displayName:"SidebarContainer",componentId:"sc-djltr5-2"})(["grid-area:sidebar;background-color:",";border-right:1px solid ",";overflow-y:auto;transition:width "," ease;width:",";"],({theme:e})=>e.colors.sidebarBackground,({theme:e})=>e.colors.border,({theme:e})=>e.transitions.normal,({collapsed:e})=>e?"60px":"240px"),Ca=n.main.withConfig({displayName:"ContentContainer",componentId:"sc-djltr5-3"})(["grid-area:content;overflow-y:auto;padding:",";background-color:",";"],({theme:e})=>e.spacing.md,({theme:e})=>e.colors.background),Ta=({header:e,sidebar:r,children:t,sidebarCollapsed:s=!1,className:i})=>o.jsxs(va,{sidebarCollapsed:s,className:i,children:[o.jsx(Sa,{children:e}),o.jsx(wa,{collapsed:s,children:r}),o.jsx(Ca,{children:t})]}),Ea=n.div.withConfig({displayName:"BuilderContainer",componentId:"sc-5duzr2-0"})(["background:#1a1a1a;border:1px solid #4b5563;border-radius:8px;padding:24px;margin-bottom:16px;"]),Ia=n.h3.withConfig({displayName:"SectionTitle",componentId:"sc-5duzr2-1"})(["color:#ffffff;font-size:1.1rem;font-weight:600;margin-bottom:16px;border-bottom:2px solid #dc2626;padding-bottom:8px;"]),ja=n.div.withConfig({displayName:"MatrixGrid",componentId:"sc-5duzr2-2"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin-bottom:20px;"]),Xe=n.div.withConfig({displayName:"ElementSection",componentId:"sc-5duzr2-3"})(["background:#262626;border:1px solid #4b5563;border-radius:6px;padding:16px;"]),_e=n.h4.withConfig({displayName:"ElementTitle",componentId:"sc-5duzr2-4"})(["color:#ffffff;font-size:0.9rem;font-weight:600;margin-bottom:12px;text-transform:uppercase;letter-spacing:0.5px;"]),Je=n.select.withConfig({displayName:"Select",componentId:"sc-5duzr2-5"})(["width:100%;padding:8px 12px;background:#0f0f0f;border:1px solid #4b5563;border-radius:4px;color:#ffffff;font-size:0.9rem;&:focus{outline:none;border-color:#dc2626;box-shadow:0 0 0 2px rgba(220,38,38,0.2);}option{background:#0f0f0f;color:#ffffff;}"]),Na=n.div.withConfig({displayName:"PreviewContainer",componentId:"sc-5duzr2-6"})(["background:#0f0f0f;border:1px solid #4b5563;border-radius:6px;padding:16px;margin-top:16px;"]),ka=n.div.withConfig({displayName:"PreviewText",componentId:"sc-5duzr2-7"})(["color:#ffffff;font-family:'Monaco','Menlo','Ubuntu Mono',monospace;font-size:0.9rem;line-height:1.4;min-height:20px;"]),ut=n.span.withConfig({displayName:"RequiredIndicator",componentId:"sc-5duzr2-8"})(["color:#dc2626;margin-left:4px;"]),pt=n.span.withConfig({displayName:"OptionalIndicator",componentId:"sc-5duzr2-9"})(["color:#9ca3af;font-size:0.8rem;margin-left:4px;"]),La=({onSetupChange:e,initialComponents:r})=>{const[t,s]=y.useState({constant:(r==null?void 0:r.constant)||"",action:(r==null?void 0:r.action)||"None",variable:(r==null?void 0:r.variable)||"None",entry:(r==null?void 0:r.entry)||""});y.useEffect(()=>{t.constant&&t.entry&&e(t)},[t,e]);const i=(a,p)=>{s(d=>({...d,[a]:p}))},c=()=>{const{constant:a,action:p,variable:d,entry:f}=t;if(!a||!f)return"Select required elements to see setup preview...";let l=a;return p&&p!=="None"&&(l+=` → ${p}`),d&&d!=="None"&&(l+=` → ${d}`),l+=` [${f}]`,l};return o.jsxs(Ea,{children:[o.jsx(Ia,{children:"Setup Construction Matrix"}),o.jsxs(ja,{children:[o.jsxs(Xe,{children:[o.jsxs(_e,{children:["Constant Element",o.jsx(ut,{children:"*"})]}),o.jsxs(Je,{value:t.constant,onChange:a=>i("constant",a.target.value),children:[o.jsx("option",{value:"",children:"Select Constant"}),Se.constant.parentArrays.map(a=>o.jsx("option",{value:a,children:a},a)),Se.constant.fvgTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Xe,{children:[o.jsxs(_e,{children:["Action Element",o.jsx(pt,{children:"(optional)"})]}),o.jsxs(Je,{value:t.action,onChange:a=>i("action",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),Se.action.liquidityEvents.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Xe,{children:[o.jsxs(_e,{children:["Variable Element",o.jsx(pt,{children:"(optional)"})]}),o.jsxs(Je,{value:t.variable,onChange:a=>i("variable",a.target.value),children:[o.jsx("option",{value:"None",children:"None"}),Se.variable.rdTypes.map(a=>o.jsx("option",{value:a,children:a},a))]})]}),o.jsxs(Xe,{children:[o.jsxs(_e,{children:["Entry Method",o.jsx(ut,{children:"*"})]}),o.jsxs(Je,{value:t.entry,onChange:a=>i("entry",a.target.value),children:[o.jsx("option",{value:"",children:"Select Entry Method"}),Se.entry.methods.map(a=>o.jsx("option",{value:a,children:a},a))]})]})]}),o.jsxs(Na,{children:[o.jsx(_e,{children:"Setup Preview"}),o.jsx(ka,{children:c()})]})]})},_a=La,ft=n.div.withConfig({displayName:"MetricsContainer",componentId:"sc-opkdti-0"})(["display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:",";"],({theme:e})=>e.spacing.md),mt=n.div.withConfig({displayName:"MetricCard",componentId:"sc-opkdti-1"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.md),gt=n.div.withConfig({displayName:"MetricLabel",componentId:"sc-opkdti-2"})(["color:",";font-size:",";margin-bottom:",";"],({theme:e})=>e.colors.textSecondary,({theme:e})=>e.fontSizes.sm,({theme:e})=>e.spacing.xs),ht=n.div.withConfig({displayName:"MetricValue",componentId:"sc-opkdti-3"})(["color:",";font-size:",";font-weight:600;"],({theme:e,positive:r,negative:t})=>r?e.colors.success:t?e.colors.danger:e.colors.textPrimary,({theme:e})=>e.fontSizes.lg),Ma=({metrics:e,isLoading:r})=>r?o.jsx(ft,{children:Array.from({length:4}).map((t,s)=>o.jsxs(mt,{children:[o.jsx(gt,{children:"Loading..."}),o.jsx(ht,{children:"--"})]},s))}):o.jsx(ft,{children:e.map((t,s)=>o.jsxs(mt,{children:[o.jsx(gt,{children:t.label}),o.jsx(ht,{positive:t.positive,negative:t.negative,children:t.value})]},s))}),Ra=Ma,Pa=n.div.withConfig({displayName:"AnalysisContainer",componentId:"sc-tp1ymt-0"})(["background:",";border:1px solid ",";border-radius:",";padding:",";"],({theme:e})=>e.colors.surface,({theme:e})=>e.colors.border,({theme:e})=>e.borderRadius.md,({theme:e})=>e.spacing.lg),Da=n.h3.withConfig({displayName:"AnalysisTitle",componentId:"sc-tp1ymt-1"})(["color:",";font-size:",";font-weight:600;margin-bottom:",";"],({theme:e})=>e.colors.textPrimary,({theme:e})=>e.fontSizes.lg,({theme:e})=>e.spacing.md),$a=n.div.withConfig({displayName:"AnalysisContent",componentId:"sc-tp1ymt-2"})(["color:",";line-height:1.6;"],({theme:e})=>e.colors.textSecondary),Oa=({title:e="Trade Analysis",children:r,isLoading:t})=>o.jsxs(Pa,{children:[o.jsx(Da,{children:e}),o.jsx($a,{children:t?o.jsx("div",{children:"Loading analysis..."}):r||o.jsx("div",{children:"No analysis data available"})})]}),Aa=Oa,v={f1Red:"#e10600",f1RedDark:"#c10500",f1RedLight:"#ff3b36",f1Blue:"#0600EF",f1BlueDark:"#0500CC",f1BlueLight:"#4169E1",f1MercedesGreen:"#00D2BE",f1MercedesGreenDark:"#00A896",f1MercedesGreenLight:"#00FFE5",f1McLarenOrange:"#FF8700",f1McLarenOrangeDark:"#E67600",f1McLarenOrangeLight:"#FFA500",f1RacingYellow:"#FFD320",f1RacingYellowDark:"#E6BE1D",f1RacingYellowLight:"#FFDC4A",f1Carbon:"#1A1A1A",f1Silver:"#C0C0C0",white:"#ffffff",black:"#000000",gray50:"#f9fafb",gray100:"#f3f4f6",gray200:"#e5e7eb",gray300:"#d1d5db",gray400:"#9ca3af",gray500:"#6b7280",gray600:"#4b5563",gray700:"#374151",gray800:"#1f2937",gray900:"#111827",green:"#4caf50",greenDark:"#388e3c",greenLight:"#81c784",yellow:"#ffeb3b",yellowDark:"#fbc02d",yellowLight:"#fff59d",orange:"#ff9800",orangeDark:"#f57c00",orangeLight:"#ffb74d",red:"#f44336",redDark:"#d32f2f",redLight:"#e57373",blue:"#2196f3",blueDark:"#1976d2",blueLight:"#64b5f6",purple:"#9c27b0",purpleDark:"#7b1fa2",purpleLight:"#ba68c8",whiteTransparent10:"rgba(255, 255, 255, 0.1)",blackTransparent10:"rgba(0, 0, 0, 0.1)"},re={background:"#0f0f0f",surface:"#1a1a1a",cardBackground:"#1a1a1a",border:"#333333",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#ffffff",textSecondary:"#aaaaaa",textDisabled:"#666666",textInverse:"#1a1f2c",success:v.green,warning:v.yellow,error:v.red,info:v.blue,chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:v.f1Red,profit:v.green,loss:v.red,neutral:v.gray400,tooltipBackground:"rgba(37, 42, 55, 0.9)",modalBackground:"rgba(26, 31, 44, 0.8)"},za={background:"#f5f5f5",surface:"#ffffff",cardBackground:"#ffffff",border:"#e0e0e0",divider:"rgba(0, 0, 0, 0.1)",textPrimary:"#333333",textSecondary:"#666666",textDisabled:"#999999",textInverse:"#ffffff",success:v.green,warning:v.yellow,error:v.red,info:v.blue,chartGrid:"rgba(0, 0, 0, 0.1)",chartLine:v.f1Red,profit:v.green,loss:v.red,neutral:v.gray400,tooltipBackground:"rgba(255, 255, 255, 0.9)",modalBackground:"rgba(255, 255, 255, 0.8)"},G={xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"24px",xl:"32px",xxl:"48px"},ie={xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2.5rem",h1:"2.5rem",h2:"2rem",h3:"1.75rem",h4:"1.5rem",h5:"1.25rem",h6:"1rem"},$e={light:300,regular:400,medium:500,semibold:600,bold:700},Oe={tight:1.25,normal:1.5,relaxed:1.75},Ae={body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif",mono:"SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace"},ze={xs:"480px",sm:"640px",md:"768px",lg:"1024px",xl:"1280px"},Fe={xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px",pill:"9999px",circle:"50%"},Be={sm:"0 1px 3px rgba(0, 0, 0, 0.1)",md:"0 4px 6px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px rgba(0, 0, 0, 0.1)"},qe={fast:"0.1s",normal:"0.3s",slow:"0.5s"},He={base:1,overlay:10,modal:20,popover:30,tooltip:40,fixed:100},Fa=n.div.withConfig({displayName:"HeaderContainer",componentId:"sc-e71xhh-0"})(["display:flex;justify-content:space-between;align-items:center;padding:"," 0;border-bottom:2px solid #4b5563;margin-bottom:",";",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"16px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.lg)||"16px"},({$variant:e})=>{switch(e){case"dashboard":return n.css(["padding:24px 0;margin-bottom:24px;"]);case"form":return n.css(["padding:16px 0;margin-bottom:16px;"]);default:return n.css(["padding:20px 0;margin-bottom:20px;"])}}),Ba=n.div.withConfig({displayName:"TitleSection",componentId:"sc-e71xhh-1"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),qa=n.h1.withConfig({displayName:"MainTitle",componentId:"sc-e71xhh-2"})(["font-weight:700;color:",";margin:0;letter-spacing:-0.025em;text-transform:uppercase;font-family:'Inter',-apple-system,BlinkMacSystemFont,sans-serif;"," span{color:",";}"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$variant:e})=>{switch(e){case"dashboard":return n.css(["font-size:",";"],ie.xxxl);case"analysis":return n.css(["font-size:",";"],ie.xxl);case"form":return n.css(["font-size:",";"],ie.xl);default:return n.css(["font-size:",";"],ie.xxl)}},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),Ha=n.div.withConfig({displayName:"Subtitle",componentId:"sc-e71xhh-3"})(["font-size:",";color:#9ca3af;font-weight:500;text-transform:uppercase;letter-spacing:0.05em;"],ie.sm),Ua=n.div.withConfig({displayName:"ActionsSection",componentId:"sc-e71xhh-4"})(["display:flex;align-items:center;gap:",";flex-wrap:wrap;"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),Va=n.div.withConfig({displayName:"StatusIndicator",componentId:"sc-e71xhh-5"})(["display:flex;align-items:center;gap:",";padding:"," ",";border-radius:",";font-size:",";font-weight:600;text-transform:uppercase;letter-spacing:0.05em;",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({$isLive:e,$variant:r,theme:t})=>{var s,i,c,a,p,d,f,l,m;return e?n.css(["background:","20;border:1px solid ",";color:",";"],((s=t.colors)==null?void 0:s.sessionActive)||"#00D2BE",((i=t.colors)==null?void 0:i.sessionActive)||"#00D2BE",((c=t.colors)==null?void 0:c.sessionActive)||"#00D2BE"):r==="active"?n.css(["background:","20;border:1px solid ",";color:",";"],((a=t.colors)==null?void 0:a.sessionOptimal)||"#00FFE5",((p=t.colors)==null?void 0:p.sessionOptimal)||"#00FFE5",((d=t.colors)==null?void 0:d.sessionOptimal)||"#00FFE5"):n.css(["background:","20;border:1px solid ",";color:",";"],((f=t.colors)==null?void 0:f.textSecondary)||"#9ca3af",((l=t.colors)==null?void 0:l.textSecondary)||"#9ca3af",((m=t.colors)==null?void 0:m.textSecondary)||"#9ca3af")}),Ya=n.div.withConfig({displayName:"StatusDot",componentId:"sc-e71xhh-6"})(["width:6px;height:6px;border-radius:50%;background:",";",""],({$isLive:e,theme:r})=>{var t,s;return e?((t=r.colors)==null?void 0:t.sessionActive)||"#00D2BE":((s=r.colors)==null?void 0:s.sessionOptimal)||"#00FFE5"},({$isLive:e})=>e&&n.css(["animation:mercedesPulse 2s infinite;@keyframes mercedesPulse{0%,100%{opacity:1;transform:scale(1);}50%{opacity:0.7;transform:scale(1.2);}}"])),Ga=n.button.withConfig({displayName:"RefreshButton",componentId:"sc-e71xhh-7"})(["padding:"," ",";background:transparent;color:",";border:1px solid #4b5563;border-radius:",";cursor:pointer;font-weight:500;font-size:",";transition:all 0.2s ease;min-width:100px;position:relative;&:hover{background:#4b5563;color:",";border-color:",";}&:disabled{opacity:0.6;cursor:not-allowed;}",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"},({$isRefreshing:e})=>e&&n.css(["&::after{content:'';position:absolute;top:50%;left:50%;width:16px;height:16px;margin:-8px 0 0 -8px;border:2px solid transparent;border-top:2px solid currentColor;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"])),Wa=n.div.withConfig({displayName:"CustomActions",componentId:"sc-e71xhh-8"})(["display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),Ka=e=>{const{title:r,subtitle:t,isLive:s=!1,liveText:i="LIVE SESSION",statusText:c,onRefresh:a,isRefreshing:p=!1,actions:d,variant:f="dashboard",className:l}=e,m=s?i:c;return o.jsxs(Fa,{$variant:f,className:l,children:[o.jsxs(Ba,{children:[o.jsx(qa,{$variant:f,children:f==="dashboard"?o.jsxs(o.Fragment,{children:["🏎️ ",r.replace("Trading","TRADING").replace("Dashboard","DASHBOARD")]}):r}),t&&o.jsx(Ha,{children:t})]}),o.jsxs(Ua,{children:[m&&o.jsxs(Va,{$isLive:s,$variant:!s&&c?"active":void 0,children:[o.jsx(Ya,{$isLive:s}),m]}),a&&o.jsx(Ga,{onClick:a,disabled:p,$isRefreshing:p,children:p?"Refreshing...":"Refresh"}),d&&o.jsx(Wa,{children:d})]})]})},mr=n.div.withConfig({displayName:"Container",componentId:"sc-vuv4tf-0"})(["display:flex;flex-direction:column;width:100%;max-width:",";margin:0 auto;min-height:",";"," "," "," ",""],({$maxWidth:e})=>typeof e=="number"?`${e}px`:e,({$variant:e})=>e==="dashboard"?"100vh":"auto",({$padding:e})=>{const r={sm:G.sm,md:G.md,lg:G.lg,xl:G.xl};return n.css(["padding:",";"],r[e||"lg"])},({$background:e,theme:r})=>{const t={default:r.colors.background,surface:r.colors.surface,elevated:r.colors.elevated};return n.css(["background:",";"],t[e||"default"])},({$variant:e})=>{switch(e){case"dashboard":return n.css(["gap:24px;padding-top:0;"]);case"form":return n.css(["gap:16px;max-width:800px;"]);case"analysis":return n.css(["gap:20px;max-width:1400px;"]);case"settings":return n.css(["gap:16px;max-width:1000px;"]);default:return n.css(["gap:16px;"])}},({$animated:e})=>e&&n.css(["transition:all 0.3s ease-in-out;&.entering{opacity:0;transform:translateY(20px);}&.entered{opacity:1;transform:translateY(0);}"])),Qa=n.div.withConfig({displayName:"LoadingContainer",componentId:"sc-vuv4tf-1"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:400px;gap:16px;color:#9ca3af;"]),Xa=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-vuv4tf-2"})(["width:40px;height:40px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),Ja=n.div.withConfig({displayName:"ErrorContainer",componentId:"sc-vuv4tf-3"})(["display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px;background:rgba(244,67,54,0.1);border:1px solid #f44336;border-radius:8px;color:#f44336;text-align:center;gap:16px;"]),Za=n.div.withConfig({displayName:"ErrorIcon",componentId:"sc-vuv4tf-4"})(["font-size:48px;opacity:0.8;"]),ec=n.div.withConfig({displayName:"ErrorMessage",componentId:"sc-vuv4tf-5"})(["font-size:16px;font-weight:500;"]),rc=n.button.withConfig({displayName:"RetryButton",componentId:"sc-vuv4tf-6"})(["padding:8px 16px;background:#f44336;color:white;border:none;border-radius:4px;cursor:pointer;font-weight:500;transition:background 0.2s ease;&:hover{background:#d32f2f;}"]),xt=()=>o.jsxs(Qa,{children:[o.jsx(Xa,{}),o.jsx("div",{children:"Loading..."})]}),tc=({error:e,onRetry:r})=>o.jsxs(Ja,{children:[o.jsx(Za,{children:"⚠️"}),o.jsx(ec,{children:e}),r&&o.jsx(rc,{onClick:r,children:"Retry"})]}),oc=e=>{const{children:r,variant:t="dashboard",maxWidth:s="100%",padding:i="lg",isLoading:c=!1,error:a=null,loadingFallback:p,errorFallback:d,className:f,animated:l=!0,background:m="default"}=e,x={$variant:t,$maxWidth:s,$padding:i,$animated:l,$background:m};return a?o.jsx(mr,{...x,className:f,children:d||o.jsx(tc,{error:a})}):c?o.jsx(mr,{...x,className:f,children:p||o.jsx(xt,{})}):o.jsx(mr,{...x,className:f,children:o.jsx(y.Suspense,{fallback:p||o.jsx(xt,{}),children:r})})},sc=n.form.withConfig({displayName:"FormContainer",componentId:"sc-1gwzj6e-0"})(["display:flex;flex-direction:column;gap:",";background:",";border-radius:",";border:1px solid ",";position:relative;"," "," ",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.surface)||"#1f2937"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.border)||"#4b5563"},({$variant:e})=>{switch(e){case"quick":return n.css(["padding:",";max-width:600px;"],G.lg);case"detailed":return n.css(["padding:",";max-width:800px;"],G.xl);case"modal":return n.css(["padding:",";max-width:500px;margin:0 auto;"],G.lg);case"inline":return n.css(["padding:",";background:transparent;border:none;"],G.md);default:return n.css(["padding:",";"],G.lg)}},({$showAccent:e,theme:r})=>{var t,s,i,c,a;return e&&n.css(["&::before{content:'';position:absolute;top:0;left:0;right:0;height:3px;background:linear-gradient( 90deg,",",",","," );border-radius:"," "," 0 0;}"],((t=r.colors)==null?void 0:t.primary)||"#dc2626",((s=r.colors)==null?void 0:s.primaryDark)||"#b91c1c",((i=r.colors)==null?void 0:i.primary)||"#dc2626",((c=r.borderRadius)==null?void 0:c.lg)||"8px",((a=r.borderRadius)==null?void 0:a.lg)||"8px")},({$disabled:e})=>e&&n.css(["opacity:0.6;pointer-events:none;"])),nc=n.div.withConfig({displayName:"FormHeader",componentId:"sc-1gwzj6e-1"})(["display:flex;flex-direction:column;gap:",";margin-bottom:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),ic=n.h3.withConfig({displayName:"FormTitle",componentId:"sc-1gwzj6e-2"})(["font-size:",";font-weight:700;color:",";margin:0;text-transform:uppercase;letter-spacing:0.025em;display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"1.125rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),ac=n.div.withConfig({displayName:"FormSubtitle",componentId:"sc-1gwzj6e-3"})(["font-size:",";color:",";font-weight:500;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),cc=n.div.withConfig({displayName:"FormContent",componentId:"sc-1gwzj6e-4"})(["display:flex;flex-direction:column;gap:",";"],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"}),bt=n.div.withConfig({displayName:"FormMessage",componentId:"sc-1gwzj6e-5"})(["padding:"," ",";border-radius:",";font-size:",";font-weight:500;display:flex;align-items:center;gap:",";",""],({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({$type:e})=>{switch(e){case"error":return n.css(["background:rgba(244,67,54,0.1);border:1px solid #f44336;color:#f44336;"]);case"success":return n.css(["background:rgba(34,197,94,0.1);border:1px solid #22c55e;color:#22c55e;"]);case"info":return n.css(["background:rgba(59,130,246,0.1);border:1px solid #3b82f6;color:#3b82f6;"])}}),lc=n.div.withConfig({displayName:"LoadingOverlay",componentId:"sc-1gwzj6e-6"})(["position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;border-radius:",";z-index:10;"],({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.lg)||"8px"}),dc=n.div.withConfig({displayName:"LoadingSpinner",componentId:"sc-1gwzj6e-7"})(["width:32px;height:32px;border:3px solid #4b5563;border-top:3px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),uc=n.div.withConfig({displayName:"AutoSaveIndicator",componentId:"sc-1gwzj6e-8"})(["position:absolute;top:8px;right:8px;font-size:",";color:",";opacity:",";transition:opacity 0.3s ease;"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"},({$visible:e})=>e?1:0),pc=e=>{const{children:r,onSubmit:t,title:s,subtitle:i,isSubmitting:c=!1,error:a=null,success:p=null,variant:d="quick",showAccent:f=!0,className:l,disabled:m=!1,autoSave:x=!1}=e,h=async g=>{g.preventDefault(),t&&!c&&!m&&await t(g)};return o.jsxs(sc,{$variant:d,$showAccent:f,$disabled:m,className:l,onSubmit:h,noValidate:!0,children:[c&&o.jsx(lc,{children:o.jsx(dc,{})}),x&&o.jsx(uc,{$visible:!c,children:"Auto-save enabled"}),(s||i)&&o.jsxs(nc,{children:[s&&o.jsx(ic,{children:s}),i&&o.jsx(ac,{children:i})]}),a&&o.jsxs(bt,{$type:"error",children:["⚠️ ",a]}),p&&o.jsxs(bt,{$type:"success",children:["✅ ",p]}),o.jsx(cc,{children:r})]})},fc=n.div.withConfig({displayName:"FieldContainer",componentId:"sc-sq94oz-0"})(["display:flex;flex-direction:column;gap:",";"],({$size:e})=>({sm:G.xs,md:G.sm,lg:G.md})[e||"md"]),mc=n.label.withConfig({displayName:"Label",componentId:"sc-sq94oz-1"})(["font-size:",";font-weight:600;color:",";display:flex;align-items:center;gap:",";"," ",""],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"0.875rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({$variant:e})=>{switch(e){case"trading":return n.css(["text-transform:uppercase;letter-spacing:0.025em;"]);case"analysis":return n.css(["font-weight:500;"]);default:return n.css([""])}},({$required:e,theme:r})=>{var t;return e&&n.css(["&::after{content:'*';color:",";margin-left:2px;}"],((t=r.colors)==null?void 0:t.primary)||"#dc2626")}),gc=n.div.withConfig({displayName:"InputContainer",componentId:"sc-sq94oz-2"})(["position:relative;display:flex;align-items:center;",""],({$disabled:e})=>e&&n.css(["opacity:0.6;pointer-events:none;"])),jr=n.css(["width:100%;border:1px solid ",";border-radius:",";background:",";color:",";font-family:inherit;transition:all 0.2s ease;"," &:focus{outline:none;border-color:",";box-shadow:0 0 0 2px rgba(220,38,38,0.2);}&:disabled{background:#374151;color:#9ca3af;cursor:not-allowed;}&::placeholder{color:#6b7280;}"],({$hasError:e,theme:r})=>{var t;return e?((t=r.colors)==null?void 0:t.error)||"#f44336":"#4b5563"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"},({$size:e})=>({sm:n.css(["padding:"," ",";font-size:",";"],G.xs,G.sm,ie.sm),md:n.css(["padding:"," ",";font-size:",";"],G.sm,G.md,ie.md),lg:n.css(["padding:"," ",";font-size:",";"],G.md,G.lg,ie.lg)})[e||"md"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.primary)||"#dc2626"}),hc=n.input.withConfig({displayName:"Input",componentId:"sc-sq94oz-3"})(["",""],jr),xc=n.select.withConfig({displayName:"Select",componentId:"sc-sq94oz-4"})([""," cursor:pointer;option{background:",";color:",";}"],jr,({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.background)||"#111827"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"}),bc=n.textarea.withConfig({displayName:"TextArea",componentId:"sc-sq94oz-5"})([""," resize:vertical;min-height:80px;font-family:inherit;"],jr),yc=n.div.withConfig({displayName:"PrefixContainer",componentId:"sc-sq94oz-6"})(["position:absolute;left:12px;display:flex;align-items:center;color:",";pointer-events:none;z-index:1;"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),vc=n.div.withConfig({displayName:"SuffixContainer",componentId:"sc-sq94oz-7"})(["position:absolute;right:12px;display:flex;align-items:center;color:",";"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Sc=n.div.withConfig({displayName:"ErrorMessage",componentId:"sc-sq94oz-8"})(["font-size:",";color:",";font-weight:500;display:flex;align-items:center;gap:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.error)||"#f44336"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),wc=n.div.withConfig({displayName:"HelpText",componentId:"sc-sq94oz-9"})(["font-size:",";color:",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"0.75rem"},({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textSecondary)||"#9ca3af"}),Cc=n.div.withConfig({displayName:"ValidationIndicator",componentId:"sc-sq94oz-10"})(["position:absolute;right:8px;display:flex;align-items:center;"," ",""],({$validating:e})=>e&&n.css(["&::after{content:'';width:12px;height:12px;border:2px solid #4b5563;border-top:2px solid #dc2626;border-radius:50%;animation:spin 1s linear infinite;}@keyframes spin{0%{transform:rotate(0deg);}100%{transform:rotate(360deg);}}"]),({$valid:e,$validating:r})=>!r&&n.css(["color:",";&::after{content:'","';}"],e?"#22c55e":"#f44336",e?"✓":"✗")),Tc=e=>{const{label:r,field:t,type:s="text",placeholder:i,required:c=!1,disabled:a=!1,helpText:p,options:d=[],inputProps:f={},className:l,size:m="md",variant:x="default",prefix:h,suffix:g}=e,b=!!(t.error&&t.touched),T=t.touched&&!t.validating,w=()=>{const C={id:f.id||r.toLowerCase().replace(/\s+/g,"-"),value:t.value,onChange:t.setValue,onBlur:()=>t.setTouched(!0),disabled:a,placeholder:i,$hasError:b,$size:m,...f};switch(s){case"select":return o.jsxs(xc,{...C,children:[i&&o.jsx("option",{value:"",disabled:!0,children:i}),d.map(M=>o.jsx("option",{value:M.value,children:M.label},M.value))]});case"textarea":return o.jsx(bc,{...C});default:return o.jsx(hc,{...C,type:s})}};return o.jsxs(fc,{$size:m,className:l,children:[o.jsx(mc,{$required:c,$variant:x,htmlFor:f.id||r.toLowerCase().replace(/\s+/g,"-"),children:r}),o.jsxs(gc,{$hasError:b,$disabled:a,children:[h&&o.jsx(yc,{children:h}),w(),g&&o.jsx(vc,{children:g}),T&&o.jsx(Cc,{$valid:t.valid,$validating:t.validating})]}),b&&o.jsxs(Sc,{children:["⚠️ ",t.error]}),p&&!b&&o.jsx(wc,{children:p})]})},Ut=(e=!1)=>{const[r,t]=y.useState(e),[s,i]=y.useState(null),[c,a]=y.useState(!1),p=y.useCallback(h=>{t(h),h&&(i(null),a(!1))},[]),d=y.useCallback(h=>{i(h),t(!1),a(!1)},[]),f=y.useCallback(()=>{i(null)},[]),l=y.useCallback(()=>{t(!1),i(null),a(!1)},[]),m=y.useCallback(async h=>{p(!0);try{const g=await h();return a(!0),t(!1),g}catch(g){const b=g instanceof Error?g.message:"An unexpected error occurred";throw d(b),g}},[p,d]),x=y.useCallback(h=>async(...g)=>{try{await m(()=>h(...g))}catch(b){console.error("Operation failed:",b)}},[m]);return{isLoading:r,error:s,isSuccess:c,isError:s!==null,setLoading:p,setError:d,clearError:f,reset:l,withLoading:m,withLoadingCallback:x}};function Ec(e,r={}){const{fetchOnMount:t=!0,dependencies:s=[]}=r,[i,c]=y.useState({data:null,isLoading:!1,error:null,isInitialized:!1}),a=y.useCallback(async(...p)=>{c(d=>({...d,isLoading:!0,error:null}));try{const d=await e(...p);return c({data:d,isLoading:!1,error:null,isInitialized:!0}),d}catch(d){const f=d instanceof Error?d:new Error(String(d));throw c(l=>({...l,isLoading:!1,error:f,isInitialized:!0})),f}},[e]);return y.useEffect(()=>{t&&a()},[t,a,...s]),{...i,fetchData:a,refetch:()=>a()}}function Ic(e,r){const[t,s]=y.useState(e);return y.useEffect(()=>{const i=setTimeout(()=>{s(e)},r);return()=>{clearTimeout(i)}},[e,r]),t}function jc(e={}){const{componentName:r,logToConsole:t=!0,reportToMonitoring:s=!0,onError:i}=e,[c,a]=y.useState(null),[p,d]=y.useState(!1),f=y.useCallback(x=>{if(a(x),d(!0),t){const h=r?`[${r}]`:"";console.error(`Error caught by useErrorHandler${h}:`,x)}i&&i(x)},[r,t,s,i]),l=y.useCallback(()=>{a(null),d(!1)},[]),m=y.useCallback(async x=>{try{return await x()}catch(h){f(h);return}},[f]);return y.useEffect(()=>()=>{a(null),d(!1)},[]),{error:c,hasError:p,handleError:f,resetError:l,tryExecute:m}}function Sr(e,r){const t=()=>{if(typeof window>"u")return r;try{const a=window.localStorage.getItem(e);return a?JSON.parse(a):r}catch(a){return console.warn(`Error reading localStorage key "${e}":`,a),r}},[s,i]=y.useState(t),c=a=>{try{const p=a instanceof Function?a(s):a;i(p),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(p))}catch(p){console.warn(`Error setting localStorage key "${e}":`,p)}};return y.useEffect(()=>{const a=p=>{p.key===e&&p.newValue&&i(JSON.parse(p.newValue))};return window.addEventListener("storage",a),()=>window.removeEventListener("storage",a)},[e]),[s,c]}function Nc(e){const{totalItems:r,itemsPerPage:t=10,initialPage:s=1,persistKey:i}=e,[c,a]=i?Sr(`${i}_page`,s):y.useState(s),[p,d]=i?Sr(`${i}_itemsPerPage`,t):y.useState(t),f=y.useMemo(()=>Math.max(1,Math.ceil(r/p)),[r,p]),l=y.useMemo(()=>Math.min(Math.max(1,c),f),[c,f]);l!==c&&a(l);const m=(l-1)*p,x=Math.min(m+p-1,r-1),h=l>1,g=l<f,b=y.useMemo(()=>{const N=[];if(f<=5)for(let k=1;k<=f;k++)N.push(k);else{let k=Math.max(1,l-Math.floor(2.5));const R=Math.min(f,k+5-1);R===f&&(k=Math.max(1,R-5+1));for(let j=k;j<=R;j++)N.push(j)}return N},[l,f]),T=y.useCallback(()=>{g&&a(l+1)},[g,l,a]),w=y.useCallback(()=>{h&&a(l-1)},[h,l,a]),C=y.useCallback(A=>{const N=Math.min(Math.max(1,A),f);a(N)},[f,a]),M=y.useCallback(A=>{d(A),a(1)},[d,a]);return{currentPage:l,itemsPerPage:p,totalPages:f,hasPreviousPage:h,hasNextPage:g,startIndex:m,endIndex:x,pageRange:b,nextPage:T,previousPage:w,goToPage:C,setItemsPerPage:M}}const kc=(e,r="$",t=!1)=>{const i=Math.abs(e).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2});return e>0?t?`+${r}${i}`:`${r}${i}`:e<0?`-${r}${i}`:`${r}${i}`},Lc=(e,r={})=>{const{currency:t="$",showPositiveSign:s=!1,customAriaLabel:i}=r;return y.useMemo(()=>{if(e==null)return{formattedAmount:"",isProfit:!1,isLoss:!1,isNeutral:!1,isEmpty:!0,ariaLabel:i||"No profit/loss data available"};const c=e>0,a=e<0,p=e===0,d=kc(e,t,s),f=`${c?"Profit":a?"Loss":"Breakeven"} of ${d}`;return{formattedAmount:d,isProfit:c,isLoss:a,isNeutral:p,isEmpty:!1,ariaLabel:i||f}},[e,t,s,i])},_c=e=>e==null?!0:Array.isArray(e)?e.length===0:typeof e=="object"?Object.keys(e).length===0:typeof e=="string"?e.trim().length===0:!1,Mc=e=>{const{fetchData:r,initialData:t=null,fetchOnMount:s=!0,refreshInterval:i,isEmpty:c=_c,transformError:a,dependencies:p=[]}=e,[d,f]=y.useState(t),[l,m]=y.useState(null),x=Ut(),h=y.useMemo(()=>d===null||c(d),[d,c]),g=y.useCallback(async()=>{try{const C=await x.withLoading(r);f(C),m(new Date)}catch(C){const M=a&&C instanceof Error?a(C):C instanceof Error?C.message:"Failed to fetch data";x.setError(M),console.error("Data fetch failed:",C)}},[r,x,a]),b=y.useCallback(async()=>{await g()},[g]),T=y.useCallback(()=>{f(t),m(null),x.reset()},[t,x]),w=y.useCallback(C=>{f(C),m(new Date),x.clearError()},[x]);return y.useEffect(()=>{s&&g()},[s,g]),y.useEffect(()=>{p.length>0&&l!==null&&g()},p),y.useEffect(()=>{if(!i||i<=0)return;const C=setInterval(()=>{!x.isLoading&&!x.error&&g()},i);return()=>clearInterval(C)},[i,x.isLoading,x.error,g]),{data:d,isLoading:x.isLoading,error:x.error,isEmpty:h,isSuccess:x.isSuccess,isError:x.isError,lastFetched:l,refresh:b,clearError:x.clearError,reset:T,setData:w}},Rc=(e="en-US")=>y.useMemo(()=>({formatCurrency:(d,f={})=>{const{currency:l="USD",locale:m=e,minimumFractionDigits:x=2,maximumFractionDigits:h=2,showPositiveSign:g=!1}=f,T=new Intl.NumberFormat(m,{style:"currency",currency:l,minimumFractionDigits:x,maximumFractionDigits:h}).format(Math.abs(d));return d>0&&g?`+${T}`:d<0?`-${T}`:T},formatPercent:(d,f={})=>{const{locale:l=e,minimumFractionDigits:m=2,maximumFractionDigits:x=2,showPositiveSign:h=!1}=f,g=new Intl.NumberFormat(l,{style:"percent",minimumFractionDigits:m,maximumFractionDigits:x}),b=d>1?d/100:d,T=g.format(Math.abs(b));return b>0&&h?`+${T}`:b<0?`-${T}`:T},formatNumber:(d,f={})=>{const{locale:l=e,minimumFractionDigits:m=0,maximumFractionDigits:x=2,useGrouping:h=!0}=f;return new Intl.NumberFormat(l,{minimumFractionDigits:m,maximumFractionDigits:x,useGrouping:h}).format(d)},formatDate:(d,f="medium")=>{const l=typeof d=="string"?new Date(d):d;return new Intl.DateTimeFormat(e,{dateStyle:f}).format(l)},formatTime:(d,f="short")=>{const l=typeof d=="string"?new Date(d):d;return new Intl.DateTimeFormat(e,{timeStyle:f}).format(l)},formatRelativeTime:d=>{const f=typeof d=="string"?new Date(d):d,m=Math.floor((new Date().getTime()-f.getTime())/1e3);if(typeof Intl.RelativeTimeFormat<"u"){const b=new Intl.RelativeTimeFormat(e,{numeric:"auto"}),T=[{unit:"year",seconds:31536e3},{unit:"month",seconds:2592e3},{unit:"day",seconds:86400},{unit:"hour",seconds:3600},{unit:"minute",seconds:60},{unit:"second",seconds:1}];for(const w of T){const C=Math.floor(Math.abs(m)/w.seconds);if(C>=1)return b.format(m>0?-C:C,w.unit)}return b.format(0,"second")}const x=Math.abs(m),h=m<0;if(x<60)return h?"in a few seconds":"a few seconds ago";if(x<3600){const b=Math.floor(x/60);return h?`in ${b} minute${b>1?"s":""}`:`${b} minute${b>1?"s":""} ago`}if(x<86400){const b=Math.floor(x/3600);return h?`in ${b} hour${b>1?"s":""}`:`${b} hour${b>1?"s":""} ago`}const g=Math.floor(x/86400);return h?`in ${g} day${g>1?"s":""}`:`${g} day${g>1?"s":""} ago`}}),[e]),Vt={small:n.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.xs)||"12px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xxs)||"2px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"}),medium:n.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.sm)||"14px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.xs)||"4px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"}),large:n.css(["font-size:",";padding:"," ",";"],({theme:e})=>{var r;return((r=e.fontSizes)==null?void 0:r.lg)||"18px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.sm)||"8px"},({theme:e})=>{var r;return((r=e.spacing)==null?void 0:r.md)||"12px"})},Yt={profit:n.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.profit)||((t=e.colors)==null?void 0:t.success)||"#4caf50"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.profit?`${e.colors.profit}15`:"rgba(76, 175, 80, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.profit?`${e.colors.profit}30`:"rgba(76, 175, 80, 0.2)"}),loss:n.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.loss)||((t=e.colors)==null?void 0:t.error)||"#f44336"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.loss?`${e.colors.loss}15`:"rgba(244, 67, 54, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.loss?`${e.colors.loss}30`:"rgba(244, 67, 54, 0.2)"}),neutral:n.css(["color:",";background-color:",";border:1px solid ",";"],({theme:e})=>{var r,t;return((r=e.colors)==null?void 0:r.neutral)||((t=e.colors)==null?void 0:t.textSecondary)||"#757575"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.neutral?`${e.colors.neutral}15`:"rgba(117, 117, 117, 0.1)"},({theme:e})=>{var r;return(r=e.colors)!=null&&r.neutral?`${e.colors.neutral}30`:"rgba(117, 117, 117, 0.2)"}),default:n.css(["color:",";background-color:transparent;border:1px solid transparent;"],({theme:e})=>{var r;return((r=e.colors)==null?void 0:r.textPrimary)||"#ffffff"})},Pc=n.css(["display:inline-flex;align-items:center;justify-content:flex-end;font-weight:",";font-family:",";transition:",";border-radius:",";&:hover{transform:translateY(-1px);box-shadow:",";}"],({theme:e})=>{var r;return((r=e.fontWeights)==null?void 0:r.semibold)||"600"},({theme:e})=>{var r;return((r=e.fontFamilies)==null?void 0:r.mono)||"monospace"},({theme:e})=>{var r;return((r=e.transitions)==null?void 0:r.fast)||"all 0.2s ease"},({theme:e})=>{var r;return((r=e.borderRadius)==null?void 0:r.sm)||"4px"},({theme:e})=>{var r;return((r=e.shadows)==null?void 0:r.sm)||"0 2px 4px rgba(0, 0, 0, 0.1)"}),Dc=n.css(["opacity:0.6;position:relative;&::after{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,0.2),transparent);animation:shimmer 1.5s infinite;}@keyframes shimmer{0%{transform:translateX(-100%);}100%{transform:translateX(100%);}}"]),$c=e=>Vt[e],Oc=(e,r,t)=>e?"profit":r?"loss":t?"neutral":"default",Ac=e=>Yt[e],Ue={name:"mercedes-green",colors:{primary:v.f1MercedesGreen,primaryDark:v.f1MercedesGreenDark,primaryLight:v.f1MercedesGreenLight,secondary:v.f1Blue,secondaryDark:v.f1BlueDark,secondaryLight:v.f1BlueLight,accent:v.f1McLarenOrange,accentDark:v.f1McLarenOrangeDark,accentLight:v.f1McLarenOrangeLight,success:v.f1MercedesGreen,warning:v.f1McLarenOrange,error:v.f1Red,danger:v.f1Red,info:v.f1Blue,background:re.background,surface:re.surface,elevated:v.gray700,cardBackground:re.surface,border:re.border,divider:"rgba(255, 255, 255, 0.1)",textPrimary:re.textPrimary,textSecondary:re.textSecondary,textDisabled:re.textDisabled,textInverse:re.textInverse,chartGrid:re.chartGrid,chartLine:re.chartLine,chartAxis:v.gray400,chartTooltip:re.tooltipBackground,profit:v.f1MercedesGreen,loss:v.f1Red,neutral:v.f1Silver,tabActive:v.f1MercedesGreen,tabInactive:v.gray600,tooltipBackground:re.tooltipBackground,modalBackground:re.modalBackground,sidebarBackground:v.gray800,headerBackground:"rgba(0, 0, 0, 0.2)",sessionActive:v.f1MercedesGreen,sessionOptimal:v.f1MercedesGreenLight,sessionCaution:v.f1RacingYellow,sessionTransition:v.f1McLarenOrange,sessionInactive:v.gray600,performanceExcellent:v.f1MercedesGreen,performanceGood:v.f1Blue,performanceAverage:v.f1Silver,performancePoor:v.f1McLarenOrange,performanceAvoid:v.f1Red},spacing:G,breakpoints:ze,fontSizes:ie,fontWeights:$e,lineHeights:Oe,fontFamilies:Ae,borderRadius:Fe,shadows:Be,transitions:qe,zIndex:He},zc=Object.freeze(Object.defineProperty({__proto__:null,f1Theme:Ue},Symbol.toStringTag,{value:"Module"})),rr={name:"f1-official",colors:{primary:"#e10600",primaryDark:"#b30500",primaryLight:"#ff1e1e",secondary:"#15151e",secondaryDark:"#0f0f17",secondaryLight:"#1e1e2e",accent:"#ffd700",accentDark:"#e6be1d",accentLight:"#ffdc4a",success:"#00ff41",warning:"#ffd700",error:"#ff1e1e",danger:"#ff1e1e",info:"#00b4d8",background:"#15151e",surface:"#1e1e2e",cardBackground:"#2a2a3a",elevated:"#353545",border:"#3a3a4a",divider:"#4a4a5a",textPrimary:"#ffffff",textSecondary:"#b8b8c8",textDisabled:"#8b8b9b",textInverse:"#15151e",chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:"#e10600",chartAxis:"#b8b8c8",chartTooltip:"rgba(42, 42, 58, 0.9)",profit:"#00ff41",loss:"#ff1e1e",neutral:"#b8b8c8",tabActive:"#e10600",tabInactive:"#8b8b9b",tooltipBackground:"rgba(42, 42, 58, 0.9)",modalBackground:"rgba(21, 21, 30, 0.8)",sidebarBackground:"#1e1e2e",headerBackground:"rgba(21, 21, 30, 0.9)",sessionActive:"#e10600",sessionOptimal:"#ffd700",sessionCaution:"#ff8700",sessionTransition:"#00b4d8",sessionInactive:"#8b8b9b",performanceExcellent:"#00ff41",performanceGood:"#ffd700",performanceAverage:"#ff8700",performancePoor:"#ff1e1e",performanceAvoid:"#8b8b9b"},spacing:G,breakpoints:ze,fontSizes:ie,fontWeights:$e,lineHeights:Oe,fontFamilies:Ae,borderRadius:Fe,shadows:Be,transitions:qe,zIndex:He},Fc=Object.freeze(Object.defineProperty({__proto__:null,default:rr,f1OfficialTheme:rr},Symbol.toStringTag,{value:"Module"})),Bc={name:"f1-official",colors:{primary:v.f1Red,primaryDark:v.f1RedDark,primaryLight:v.f1RedLight,secondary:v.f1Blue,secondaryDark:v.f1BlueDark,secondaryLight:v.f1BlueLight,accent:"#FFD700",accentDark:"#E6C200",accentLight:"#FFF700",success:"#00FF41",warning:"#FFD700",error:"#FF1E1E",danger:"#FF1E1E",info:"#00B4D8",background:"#15151E",surface:"#1E1E2E",elevated:"#2A2A3A",cardBackground:"#2A2A3A",border:"#3A3A4A",divider:"rgba(255, 255, 255, 0.1)",textPrimary:"#FFFFFF",textSecondary:"#B8B8C8",textDisabled:"#8B8B9B",textInverse:"#15151E",chartGrid:"rgba(255, 255, 255, 0.1)",chartLine:v.f1Red,chartAxis:"#B8B8C8",chartTooltip:"rgba(21, 21, 30, 0.95)",profit:"#00FF41",loss:"#FF1E1E",neutral:"#FFD700",tabActive:v.f1Red,tabInactive:"#8B8B9B",tooltipBackground:"rgba(21, 21, 30, 0.95)",modalBackground:"rgba(21, 21, 30, 0.9)",sidebarBackground:"#1A1A24",headerBackground:"rgba(21, 21, 30, 0.95)",sessionActive:"#00FF41",sessionOptimal:"#9D4EDD",sessionCaution:"#FFD700",sessionTransition:"#00B4D8",sessionInactive:"#8B8B9B",performanceExcellent:"#00FF41",performanceGood:"#00B4D8",performanceAverage:"#FFD700",performancePoor:"#FF8700",performanceAvoid:"#FF1E1E"},spacing:G,breakpoints:ze,fontSizes:ie,fontWeights:$e,lineHeights:Oe,fontFamilies:Ae,borderRadius:Fe,shadows:Be,transitions:qe,zIndex:He},qc=Bc,Nr={name:"mercedes-dark",colors:{primary:v.f1Silver,primaryDark:v.gray500,primaryLight:v.gray300,secondary:v.f1MercedesGreenDark,secondaryDark:"#006B5D",secondaryLight:v.f1MercedesGreen,accent:v.f1Silver,accentDark:v.gray500,accentLight:v.gray300,success:v.f1MercedesGreen,warning:v.f1Silver,error:v.red,danger:v.red,info:v.f1MercedesGreenDark,background:v.gray900,surface:v.gray800,elevated:v.gray700,cardBackground:v.gray800,border:v.gray700,divider:"rgba(255, 255, 255, 0.1)",textPrimary:v.white,textSecondary:v.gray200,textDisabled:v.gray400,textInverse:v.gray900,chartGrid:re.chartGrid,chartLine:v.f1Blue,chartAxis:v.gray400,chartTooltip:re.tooltipBackground,profit:v.f1MercedesGreen,loss:v.red,neutral:v.f1Silver,tabActive:v.f1Silver,tabInactive:v.gray600,tooltipBackground:"rgba(26, 32, 44, 0.9)",modalBackground:"rgba(26, 32, 44, 0.8)",sidebarBackground:v.gray900,headerBackground:"rgba(0, 0, 0, 0.3)",sessionActive:v.f1MercedesGreen,sessionOptimal:v.f1MercedesGreenLight,sessionCaution:v.f1Silver,sessionTransition:v.gray300,sessionInactive:v.gray600,performanceExcellent:v.f1MercedesGreen,performanceGood:v.f1Silver,performanceAverage:v.gray400,performancePoor:v.gray500,performanceAvoid:v.red},spacing:G,breakpoints:ze,fontSizes:ie,fontWeights:$e,lineHeights:Oe,fontFamilies:Ae,borderRadius:Fe,shadows:Be,transitions:qe,zIndex:He},Hc=Object.freeze(Object.defineProperty({__proto__:null,darkTheme:Nr},Symbol.toStringTag,{value:"Module"})),Uc=n.createGlobalStyle(["*,*::before,*::after{box-sizing:border-box;}html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,a,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,embed,figure,figcaption,footer,header,hgroup,menu,nav,output,ruby,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;}body{line-height:1.5;font-family:",";background-color:",";color:",";-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}ol,ul{list-style:none;}blockquote,q{quotes:none;}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none;}table{border-collapse:collapse;border-spacing:0;}a{color:",";text-decoration:none;&:hover{text-decoration:underline;}}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;}::-webkit-scrollbar{width:8px;height:8px;}::-webkit-scrollbar-track{background:",";}::-webkit-scrollbar-thumb{background:",";border-radius:4px;}::-webkit-scrollbar-thumb:hover{background:",";}:focus{outline:2px solid ",";outline-offset:2px;}::selection{background-color:",";color:",";}"],({theme:e})=>e.fontFamilies.body,({theme:e})=>e.colors.background,({theme:e})=>e.colors.textPrimary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.background,({theme:e})=>e.colors.border,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.primary,({theme:e})=>e.colors.textInverse),Vc=Uc,Yc={"mercedes-green":Ue,"f1-official":rr,dark:Nr},kr=Ue,Gc=e=>e==="f1"||e==="formula1"||e==="formula-1"?"mercedes-green":e==="light"?"f1-official":e,gr=e=>{const r=Gc(e);return Yc[r]||kr},Lr=y.createContext({theme:kr,setTheme:()=>{}}),Wc=()=>y.useContext(Lr),Kc=({initialTheme:e=kr,persistTheme:r=!0,storageKey:t="adhd-dashboard-theme",children:s})=>{const[i,c]=y.useState(()=>{if(r&&typeof window<"u"){const f=window.localStorage.getItem(t);if(f)try{const l=gr(f);return l||JSON.parse(f)}catch(l){console.error("Failed to parse stored theme:",l)}}return typeof e=="string"?gr(e):e});y.useEffect(()=>{typeof document<"u"&&document.documentElement.setAttribute("data-theme",i.name)},[i.name]);const a=d=>{const f=typeof d=="string"?gr(d):d;c(f),r&&typeof window<"u"&&window.localStorage.setItem(t,f.name||JSON.stringify(f))},p=({children:d})=>o.jsxs(n.ThemeProvider,{theme:i,children:[o.jsx(Vc,{}),d]});return o.jsx(Lr.Provider,{value:{theme:i,setTheme:a},children:o.jsx(p,{children:s})})};function Gt(e){const r=[];return r.push(`[data-theme="${e.name}"] {`),Object.entries(e.colors).forEach(([t,s])=>{r.push(`  --color-${Qc(t)}: ${s};`)}),Object.entries(e.spacing).forEach(([t,s])=>{r.push(`  --spacing-${t}: ${s};`)}),Object.entries(e.fontSizes).forEach(([t,s])=>{r.push(`  --font-size-${t}: ${s};`)}),Object.entries(e.fontWeights).forEach(([t,s])=>{r.push(`  --font-weight-${t}: ${s};`)}),Object.entries(e.fontFamilies).forEach(([t,s])=>{r.push(`  --font-family-${t}: ${s};`)}),Object.entries(e.borderRadius).forEach(([t,s])=>{r.push(`  --border-radius-${t}: ${s};`)}),Object.entries(e.shadows).forEach(([t,s])=>{r.push(`  --shadow-${t}: ${s};`)}),Object.entries(e.transitions).forEach(([t,s])=>{r.push(`  --transition-${t}: ${s};`)}),Object.entries(e.zIndex).forEach(([t,s])=>{r.push(`  --z-index-${t}: ${s};`)}),r.push("}"),r.join(`
`)}function Wt(e){const r=[];return r.push(`[data-theme="${e.name}"] {`),r.push("  /* Component Semantic Variables */"),r.push("  --primary-color: var(--color-primary);"),r.push("  --secondary-color: var(--color-secondary);"),r.push("  --accent-color: var(--color-accent);"),r.push("  --success-color: var(--color-success);"),r.push("  --warning-color: var(--color-warning);"),r.push("  --error-color: var(--color-error);"),r.push("  --info-color: var(--color-info);"),r.push("  --bg-primary: var(--color-background);"),r.push("  --bg-secondary: var(--color-surface);"),r.push("  --bg-card: var(--color-card-background);"),r.push("  --bg-elevated: var(--color-elevated);"),r.push("  --text-primary: var(--color-text-primary);"),r.push("  --text-secondary: var(--color-text-secondary);"),r.push("  --text-disabled: var(--color-text-disabled);"),r.push("  --text-inverse: var(--color-text-inverse);"),r.push("  --border-primary: var(--color-border);"),r.push("  --border-secondary: var(--color-divider);"),r.push("  --session-card-bg: var(--bg-card);"),r.push("  --session-card-border: var(--border-primary);"),r.push("  --session-card-accent: var(--primary-color);"),r.push("  --session-active: var(--color-session-active);"),r.push("  --session-optimal: var(--color-session-optimal);"),r.push("  --session-caution: var(--color-session-caution);"),r.push("  --session-transition: var(--color-session-transition);"),r.push("  --session-inactive: var(--color-session-inactive);"),r.push("}"),r.join(`
`)}function Qc(e){return e.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g,"$1-$2").toLowerCase()}function Xc(e){const r=[];return r.push(`/**
 * Generated Theme CSS Variables
 * 
 * This file is auto-generated from theme definitions.
 * Do not edit manually - changes will be overwritten.
 */`),Object.values(e).forEach(t=>{r.push(""),r.push(`/* ${t.name} Theme */`),r.push(Gt(t)),r.push(""),r.push(Wt(t))}),r.join(`
`)}const Jc={"mercedes-green":()=>Promise.resolve().then(()=>zc).then(e=>e.f1Theme),"f1-official":()=>Promise.resolve().then(()=>Fc).then(e=>e.f1OfficialTheme),dark:()=>Promise.resolve().then(()=>Hc).then(e=>e.darkTheme)};function Zc(e,r,t="StoreContext"){const s=y.createContext(void 0);s.displayName=t;const i=({children:f,initialState:l})=>{const[m,x]=y.useReducer(e,l||r),h=y.useMemo(()=>({state:m,dispatch:x}),[m]);return o.jsx(s.Provider,{value:h,children:f})};function c(){const f=y.useContext(s);if(f===void 0)throw new Error(`use${t} must be used within a ${t}Provider`);return f}function a(f){const{state:l}=c();return f(l)}function p(f){const{dispatch:l}=c();return y.useMemo(()=>(...m)=>{l(f(...m))},[l,f])}function d(f){const{dispatch:l}=c();return y.useMemo(()=>{const m={};for(const x in f)m[x]=(...h)=>{l(f[x](...h))};return m},[l,f])}return{Context:s,Provider:i,useStore:c,useSelector:a,useAction:p,useActions:d}}function el(...e){const r=e.pop(),t=e;let s=null,i=null;return c=>{const a=t.map(p=>p(c));return(s===null||a.length!==s.length||a.some((p,d)=>p!==s[d]))&&(i=r(...a),s=a),i}}function rl(e,r){const{key:t,initialState:s,version:i=1,migrate:c,serialize:a=JSON.stringify,deserialize:p=JSON.parse,filter:d=w=>w,merge:f=(w,C)=>({...C,...w}),debug:l=!1}=r,m=()=>{try{const w=localStorage.getItem(t);if(w===null)return null;const{state:C,version:M}=p(w);return M!==i&&c?(l&&console.log(`Migrating state from version ${M} to ${i}`),c(C,M)):C}catch(w){return l&&console.error("Error loading state from local storage:",w),null}},x=w=>{try{const C=d(w),M=a({state:C,version:i});localStorage.setItem(t,M)}catch(C){l&&console.error("Error saving state to local storage:",C)}},h=()=>{try{localStorage.removeItem(t)}catch(w){l&&console.error("Error clearing state from local storage:",w)}},g=m(),b=g?f(g,s):s;return l&&g&&(console.log("Loaded persisted state:",g),console.log("Merged initial state:",b)),{reducer:(w,C)=>{const M=e(w,C);return x(M),M},initialState:b,clear:h}}function tl(e,r="$"){return`${r}${e.toFixed(2)}`}function ol(e,r=1){return`${(e*100).toFixed(r)}%`}function sl(e,r="short"){const t=typeof e=="string"?new Date(e):e;switch(r){case"medium":return t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});case"long":return t.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});case"short":default:return t.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"})}}function nl(e,r=50){return e.length<=r?e:`${e.substring(0,r-3)}...`}function il(){return Math.random().toString(36).substring(2,9)}function al(e,r){let t=null;return function(...s){const i=()=>{t=null,e(...s)};t&&clearTimeout(t),t=setTimeout(i,r)}}function cl(e,r){let t=!1;return function(...s){t||(e(...s),t=!0,setTimeout(()=>{t=!1},r))}}function ll(e={}){console.log("Monitoring service initialized",e)}function dl(e,r){console.error("Error captured by monitoring service:",e,r)}function ul(e){console.log("User set for monitoring service:",e)}function pl(e,r){const t=performance.now();return{name:e,startTime:t,finish:()=>{const i=performance.now()-t;console.log(`Transaction "${e}" finished in ${i.toFixed(2)}ms`,r)}}}const U={MODEL_TYPE:"model_type",WIN_LOSS:"win_loss",R_MULTIPLE:"r_multiple",DATE:"date",SESSION:"session",DIRECTION:"direction",MARKET:"market",ACHIEVED_PL:"achieved_pl",PATTERN_QUALITY_RATING:"pattern_quality_rating"};class fl{constructor(){xe(this,"dbName","adhd-trading-dashboard");xe(this,"version",2);xe(this,"db",null);xe(this,"stores",{trades:"trades",fvg_details:"trade_fvg_details",setups:"trade_setups",analysis:"trade_analysis",sessions:"trading_sessions"})}async initDB(){return this.db?this.db:new Promise((r,t)=>{const s=indexedDB.open(this.dbName,this.version);s.onupgradeneeded=i=>{var a;const c=i.target.result;if(!c.objectStoreNames.contains(this.stores.trades)){const p=c.createObjectStore(this.stores.trades,{keyPath:"id",autoIncrement:!0});p.createIndex(U.DATE,U.DATE,{unique:!1}),p.createIndex(U.MODEL_TYPE,U.MODEL_TYPE,{unique:!1}),p.createIndex(U.SESSION,U.SESSION,{unique:!1}),p.createIndex(U.WIN_LOSS,U.WIN_LOSS,{unique:!1}),p.createIndex(U.R_MULTIPLE,U.R_MULTIPLE,{unique:!1})}if(c.objectStoreNames.contains(this.stores.fvg_details)||c.createObjectStore(this.stores.fvg_details,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.setups)||c.createObjectStore(this.stores.setups,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),c.objectStoreNames.contains(this.stores.analysis)||c.createObjectStore(this.stores.analysis,{keyPath:"id",autoIncrement:!0}).createIndex("trade_id","trade_id",{unique:!1}),!c.objectStoreNames.contains(this.stores.sessions)){c.createObjectStore(this.stores.sessions,{keyPath:"id",autoIncrement:!0}).createIndex("name","name",{unique:!0});const d=[{name:"Pre-Market",start_time:"04:00:00",end_time:"09:30:00",description:"Pre-market trading hours"},{name:"NY Open",start_time:"09:30:00",end_time:"10:30:00",description:"New York opening hour"},{name:"10:50-11:10",start_time:"10:50:00",end_time:"11:10:00",description:"Mid-morning macro window"},{name:"11:50-12:10",start_time:"11:50:00",end_time:"12:10:00",description:"Pre-lunch macro window"},{name:"Lunch Macro",start_time:"12:00:00",end_time:"13:30:00",description:"Lunch time trading"},{name:"13:50-14:10",start_time:"13:50:00",end_time:"14:10:00",description:"Post-lunch macro window"},{name:"14:50-15:10",start_time:"14:50:00",end_time:"15:10:00",description:"Pre-close macro window"},{name:"15:15-15:45",start_time:"15:15:00",end_time:"15:45:00",description:"Late afternoon window"},{name:"MOC",start_time:"15:45:00",end_time:"16:00:00",description:"Market on close"},{name:"Post MOC",start_time:"16:00:00",end_time:"20:00:00",description:"After hours trading"}];(a=s.transaction)==null||a.addEventListener("complete",()=>{const l=c.transaction([this.stores.sessions],"readwrite").objectStore(this.stores.sessions);d.forEach(m=>l.add(m))})}},s.onsuccess=i=>{this.db=i.target.result,r(this.db)},s.onerror=i=>{console.error("Error opening IndexedDB:",i),t(new Error("Failed to open IndexedDB"))}})}async saveTradeWithDetails(r){try{const t=await this.initDB();return new Promise((s,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=f=>{console.error("Transaction error:",f),i(new Error("Failed to save trade with details"))};const a=c.objectStore(this.stores.trades),p={...r.trade,created_at:new Date().toISOString(),updated_at:new Date().toISOString()},d=a.add(p);d.onsuccess=()=>{const f=d.result,l=[];if(r.fvg_details){const m=c.objectStore(this.stores.fvg_details),x={...r.fvg_details,trade_id:f};l.push(new Promise((h,g)=>{const b=m.add(x);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save FVG details"))}))}if(r.setup){const m=c.objectStore(this.stores.setups),x={...r.setup,trade_id:f};l.push(new Promise((h,g)=>{const b=m.add(x);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save setup data"))}))}if(r.analysis){const m=c.objectStore(this.stores.analysis),x={...r.analysis,trade_id:f};l.push(new Promise((h,g)=>{const b=m.add(x);b.onsuccess=()=>h(),b.onerror=()=>g(new Error("Failed to save analysis data"))}))}c.oncomplete=()=>{s(f)}},d.onerror=f=>{console.error("Error saving trade:",f),i(new Error("Failed to save trade"))}})}catch(t){throw console.error("Error in saveTradeWithDetails:",t),new Error("Failed to save trade with details")}}async getTradeById(r){try{const t=await this.initDB();return new Promise((s,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).get(r);p.onsuccess=()=>{const d=p.result;if(!d){s(null);return}const f={trade:d},x=c.objectStore(this.stores.fvg_details).index("trade_id").get(r);x.onsuccess=()=>{x.result&&(f.fvg_details=x.result);const b=c.objectStore(this.stores.setups).index("trade_id").get(r);b.onsuccess=()=>{b.result&&(f.setup=b.result);const C=c.objectStore(this.stores.analysis).index("trade_id").get(r);C.onsuccess=()=>{C.result&&(f.analysis=C.result),s(f)},C.onerror=M=>{console.error("Error getting analysis data:",M),s(f)}},b.onerror=T=>{console.error("Error getting setup data:",T),s(f)}},x.onerror=h=>{console.error("Error getting FVG details:",h),s(f)}},p.onerror=d=>{console.error("Error getting trade:",d),i(new Error("Failed to get trade"))}})}catch(t){return console.error("Error in getTradeById:",t),null}}async getPerformanceMetrics(){try{const r=await this.initDB();return new Promise((t,s)=>{const a=r.transaction([this.stores.trades],"readonly").objectStore(this.stores.trades).getAll();a.onsuccess=()=>{const p=a.result;if(p.length===0){t({totalTrades:0,winningTrades:0,losingTrades:0,winRate:0,profitFactor:0,averageWin:0,averageLoss:0,largestWin:0,largestLoss:0,totalPnl:0,maxDrawdown:0,maxDrawdownPercent:0,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:0,expectancy:0,sqn:0,period:"all",startDate:"",endDate:""});return}const d=p.length,f=p.filter($=>$[U.WIN_LOSS]==="Win").length,l=p.filter($=>$[U.WIN_LOSS]==="Loss").length,m=d>0?f/d*100:0,x=p.filter($=>$.achieved_pl!==void 0).map($=>$.achieved_pl),h=x.reduce(($,K)=>$+K,0),g=x.filter($=>$>0),b=x.filter($=>$<0),T=g.length>0?g.reduce(($,K)=>$+K,0)/g.length:0,w=b.length>0?Math.abs(b.reduce(($,K)=>$+K,0)/b.length):0,C=g.length>0?Math.max(...g):0,M=b.length>0?Math.abs(Math.min(...b)):0,A=g.reduce(($,K)=>$+K,0),N=Math.abs(b.reduce(($,K)=>$+K,0)),k=N>0?A/N:0,R=p.filter($=>$[U.R_MULTIPLE]!==void 0).map($=>$[U.R_MULTIPLE]),j=R.length>0?R.reduce(($,K)=>$+K,0)/R.length:0,L=j*(m/100);let W=0,V=0,_=0;for(const $ of p)if($.achieved_pl!==void 0){W+=$.achieved_pl,W>V&&(V=W);const K=V-W;K>_&&(_=K)}const q=V>0?_/V*100:0,J=R.length>0?Math.sqrt(R.length)*j/Math.sqrt(R.reduce(($,K)=>$+Math.pow(K-j,2),0)/R.length):0,Z=p.map($=>$.date).sort(),le=Z.length>0?Z[0]:"",ee=Z.length>0?Z[Z.length-1]:"";t({totalTrades:d,winningTrades:f,losingTrades:l,winRate:m,profitFactor:k,averageWin:T,averageLoss:w,largestWin:C,largestLoss:M,totalPnl:h,maxDrawdown:_,maxDrawdownPercent:q,sharpeRatio:0,sortinoRatio:0,calmarRatio:0,averageRMultiple:j,expectancy:L,sqn:J,period:"all",startDate:le,endDate:ee})},a.onerror=p=>{console.error("Error getting performance metrics:",p),s(new Error("Failed to get performance metrics"))}})}catch(r){throw console.error("Error in getPerformanceMetrics:",r),new Error("Failed to get performance metrics")}}async filterTrades(r){try{const t=await this.initDB();return new Promise((s,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readonly"),p=c.objectStore(this.stores.trades).getAll();p.onsuccess=async()=>{let d=p.result;r.dateFrom&&(d=d.filter(l=>l.date>=r.dateFrom)),r.dateTo&&(d=d.filter(l=>l.date<=r.dateTo)),r.model_type&&(d=d.filter(l=>l[U.MODEL_TYPE]===r.model_type)),r.session&&(d=d.filter(l=>l[U.SESSION]===r.session)),r.direction&&(d=d.filter(l=>l[U.DIRECTION]===r.direction)),r.win_loss&&(d=d.filter(l=>l[U.WIN_LOSS]===r.win_loss)),r.market&&(d=d.filter(l=>l[U.MARKET]===r.market)),r.min_r_multiple!==void 0&&(d=d.filter(l=>l[U.R_MULTIPLE]!==void 0&&l[U.R_MULTIPLE]>=r.min_r_multiple)),r.max_r_multiple!==void 0&&(d=d.filter(l=>l[U.R_MULTIPLE]!==void 0&&l[U.R_MULTIPLE]<=r.max_r_multiple)),r.min_pattern_quality!==void 0&&(d=d.filter(l=>l[U.PATTERN_QUALITY_RATING]!==void 0&&l[U.PATTERN_QUALITY_RATING]>=r.min_pattern_quality)),r.max_pattern_quality!==void 0&&(d=d.filter(l=>l[U.PATTERN_QUALITY_RATING]!==void 0&&l[U.PATTERN_QUALITY_RATING]<=r.max_pattern_quality));const f=[];for(const l of d){const m={trade:l},g=c.objectStore(this.stores.fvg_details).index("trade_id").get(l.id);await new Promise(N=>{g.onsuccess=()=>{g.result&&(m.fvg_details=g.result),N()},g.onerror=()=>N()});const w=c.objectStore(this.stores.setups).index("trade_id").get(l.id);await new Promise(N=>{w.onsuccess=()=>{w.result&&(m.setup=w.result),N()},w.onerror=()=>N()});const A=c.objectStore(this.stores.analysis).index("trade_id").get(l.id);await new Promise(N=>{A.onsuccess=()=>{A.result&&(m.analysis=A.result),N()},A.onerror=()=>N()}),f.push(m)}s(f)},p.onerror=d=>{console.error("Error filtering trades:",d),i(new Error("Failed to filter trades"))}})}catch(t){throw console.error("Error in filterTrades:",t),new Error("Failed to filter trades")}}async getAllTrades(){try{return await this.filterTrades({})}catch(r){return console.error("Error in getAllTrades:",r),[]}}async deleteTrade(r){try{const t=await this.initDB();return new Promise((s,i)=>{const c=t.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");c.onerror=w=>{console.error("Transaction error:",w),i(new Error("Failed to delete trade"))};const d=c.objectStore(this.stores.fvg_details).index("trade_id").openCursor(IDBKeyRange.only(r));d.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const m=c.objectStore(this.stores.setups).index("trade_id").openCursor(IDBKeyRange.only(r));m.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const g=c.objectStore(this.stores.analysis).index("trade_id").openCursor(IDBKeyRange.only(r));g.onsuccess=w=>{const C=w.target.result;C&&(C.delete(),C.continue())};const T=c.objectStore(this.stores.trades).delete(r);c.oncomplete=()=>{s()},T.onerror=w=>{console.error("Error deleting trade:",w),i(new Error("Failed to delete trade"))}})}catch(t){throw console.error("Error in deleteTrade:",t),new Error("Failed to delete trade")}}async updateTradeWithDetails(r,t){try{const s=await this.initDB();return new Promise((i,c)=>{const a=s.transaction([this.stores.trades,this.stores.fvg_details,this.stores.setups,this.stores.analysis],"readwrite");a.onerror=l=>{console.error("Transaction error:",l),c(new Error("Failed to update trade"))};const p=a.objectStore(this.stores.trades),d={...t.trade,id:r,updated_at:new Date().toISOString()},f=p.put(d);f.onsuccess=()=>{if(t.fvg_details){const l=a.objectStore(this.stores.fvg_details),m={...t.fvg_details,trade_id:r};l.put(m)}if(t.setup){const l=a.objectStore(this.stores.setups),m={...t.setup,trade_id:r};l.put(m)}if(t.analysis){const l=a.objectStore(this.stores.analysis),m={...t.analysis,trade_id:r};l.put(m)}},a.oncomplete=()=>{i()},f.onerror=l=>{console.error("Error updating trade:",l),c(new Error("Failed to update trade"))}})}catch(s){throw console.error("Error in updateTradeWithDetails:",s),new Error("Failed to update trade")}}}const Kt=new fl,ml=Kt,gl=Kt;exports.AVAILABLE_THEMES=Jc;exports.AppErrorBoundary=yn;exports.Badge=De;exports.Button=ne;exports.Card=Lt;exports.DashboardSection=ya;exports.DashboardTemplate=Ta;exports.DataCard=ua;exports.DualTimeDisplay=_s;exports.EmptyState=yr;exports.EnhancedFormField=Dn;exports.ErrorBoundary=Mt;exports.F1Container=oc;exports.F1Form=pc;exports.F1FormField=Tc;exports.F1Header=Ka;exports.FeatureErrorBoundary=vn;exports.FormField=Kn;exports.HierarchicalSessionSelector=zi;exports.Input=be;exports.LoadingCell=Fs;exports.LoadingPlaceholder=It;exports.LoadingSpinner=Ys;exports.MacroPeriodType=P;exports.Modal=ni;exports.OrderSide=wt;exports.OrderStatus=Ct;exports.OrderType=St;exports.SETUP_ELEMENTS=Se;exports.Select=we;exports.SelectDropdown=$s;exports.SessionType=Y;exports.SessionUtils=te;exports.SetupBuilder=_a;exports.SortableTable=Vn;exports.StatusIndicator=ls;exports.TRADE_COLUMN_IDS=I;exports.TabPanel=In;exports.Table=wi;exports.Tag=ms;exports.ThemeContext=Lr;exports.ThemeProvider=Kc;exports.TimeInForce=Tt;exports.TimePicker=ys;exports.TradeAnalysis=Aa;exports.TradeConverters=hr;exports.TradeDirection=yt;exports.TradeMetrics=Ra;exports.TradeStatus=vt;exports.TradeTable=la;exports.TradeTableFilters=Ht;exports.TradeTableRow=qt;exports.UnifiedErrorBoundary=wr;exports.VALID_TRADING_MODELS=Eo;exports.baseColors=v;exports.borderRadius=Fe;exports.breakpoints=ze;exports.captureError=dl;exports.convertLocalToNY=vs;exports.convertNYToLocal=br;exports.convertSessionToDualTime=or;exports.createSelector=el;exports.createStoreContext=Zc;exports.darkModeColors=re;exports.darkTheme=Nr;exports.debounce=al;exports.f1OfficialTheme=rr;exports.f1Theme=Ue;exports.fontFamilies=Ae;exports.fontSizes=ie;exports.fontWeights=$e;exports.formatCurrency=tl;exports.formatDate=sl;exports.formatPercentage=ol;exports.formatTime=vr;exports.formatTimeForDesktop=Es;exports.formatTimeForMobile=kt;exports.formatTimeInterval=jt;exports.generateAllThemeCSS=Xc;exports.generateCSSVariables=Gt;exports.generateId=il;exports.generateSemanticVariables=Wt;exports.getCompactTradeTableColumns=Ft;exports.getCurrentDualTime=Ze;exports.getCurrentNYMinutes=Ss;exports.getCurrentNYTime=ws;exports.getPerformanceTradeTableColumns=Bt;exports.getProfitLossColors=Ac;exports.getProfitLossSize=$c;exports.getProfitLossVariant=Oc;exports.getSessionStatus=Is;exports.getTimeUntilNYTime=Pe;exports.getTradeTableColumns=zt;exports.getUserTimezone=tr;exports.initMonitoring=ll;exports.isCurrentTimeInNYWindow=Nt;exports.lightModeColors=za;exports.lightTheme=qc;exports.lineHeights=Oe;exports.mercedesGreenTheme=Ue;exports.minutesToTime=Ts;exports.persistState=rl;exports.profitLossBaseStyles=Pc;exports.profitLossColors=Yt;exports.profitLossLoadingStyles=Dc;exports.profitLossSizes=Vt;exports.setUser=ul;exports.shadows=Be;exports.sortFunctions=$n;exports.spacing=G;exports.startTransaction=pl;exports.throttle=cl;exports.timeToMinutes=Me;exports.tradeStorage=gl;exports.tradeStorageService=ml;exports.transitions=qe;exports.truncateText=nl;exports.useAsyncData=Ec;exports.useDataFormatting=Rc;exports.useDataSection=Mc;exports.useDebounce=Ic;exports.useErrorHandler=jc;exports.useFormField=Pt;exports.useLoadingState=Ut;exports.useLocalStorage=Sr;exports.usePagination=Nc;exports.useProfitLossFormatting=Lc;exports.useSessionSelection=$t;exports.useSortableTable=Dt;exports.useTheme=Wc;exports.validationRules=Rt;exports.zIndex=He;
