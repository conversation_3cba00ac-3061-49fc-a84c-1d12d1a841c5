/**
 * Theme Tokens
 *
 * Design tokens for the theme system
 */
export interface BaseColors {
    f1Red: string;
    f1RedDark: string;
    f1RedLight: string;
    f1Blue: string;
    f1BlueDark: string;
    f1BlueLight: string;
    f1MercedesGreen: string;
    f1MercedesGreenDark: string;
    f1MercedesGreenLight: string;
    f1McLarenOrange: string;
    f1McLarenOrangeDark: string;
    f1McLarenOrangeLight: string;
    f1RacingYellow: string;
    f1RacingYellowDark: string;
    f1RacingYellowLight: string;
    f1Carbon: string;
    f1Silver: string;
    white: string;
    black: string;
    gray50: string;
    gray100: string;
    gray200: string;
    gray300: string;
    gray400: string;
    gray500: string;
    gray600: string;
    gray700: string;
    gray800: string;
    gray900: string;
    green: string;
    greenDark: string;
    greenLight: string;
    yellow: string;
    yellowDark: string;
    yellowLight: string;
    orange: string;
    orangeDark: string;
    orangeLight: string;
    red: string;
    redDark: string;
    redLight: string;
    blue: string;
    blueDark: string;
    blueLight: string;
    purple: string;
    purpleDark: string;
    purpleLight: string;
    whiteTransparent10: string;
    blackTransparent10: string;
}
export interface ColorMode {
    background: string;
    surface: string;
    cardBackground: string;
    border: string;
    divider: string;
    textPrimary: string;
    textSecondary: string;
    textDisabled: string;
    textInverse: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    chartGrid: string;
    chartLine: string;
    profit: string;
    loss: string;
    neutral: string;
    tooltipBackground: string;
    modalBackground: string;
}
export declare const baseColors: BaseColors;
export declare const darkModeColors: ColorMode;
export declare const lightModeColors: ColorMode;
export declare const spacing: {
    xxs: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
};
export declare const fontSizes: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
    xxxl: string;
    h1: string;
    h2: string;
    h3: string;
    h4: string;
    h5: string;
    h6: string;
};
export declare const fontWeights: {
    light: number;
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
};
export declare const lineHeights: {
    tight: number;
    normal: number;
    relaxed: number;
};
export declare const fontFamilies: {
    body: string;
    heading: string;
    mono: string;
};
export declare const breakpoints: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
};
export declare const borderRadius: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    pill: string;
    circle: string;
};
export declare const shadows: {
    sm: string;
    md: string;
    lg: string;
};
export declare const transitions: {
    fast: string;
    normal: string;
    slow: string;
};
export declare const zIndex: {
    base: number;
    overlay: number;
    modal: number;
    popover: number;
    tooltip: number;
    fixed: number;
};
//# sourceMappingURL=tokens.d.ts.map