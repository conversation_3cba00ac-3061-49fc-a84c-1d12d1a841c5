import { default as React } from 'react';
import { Theme } from './types';
export declare const ThemeContext: React.Context<{
    theme: Theme;
    setTheme: (theme: Theme | string) => void;
}>;
export declare const useTheme: () => {
    theme: Theme;
    setTheme: (theme: Theme | string) => void;
};
interface ThemeProviderProps {
    /** The initial theme to use */
    initialTheme?: string | Theme;
    /** Whether to store the theme in local storage */
    persistTheme?: boolean;
    /** The key to use for storing the theme in local storage */
    storageKey?: string;
    /** The child components */
    children: React.ReactNode;
}
/**
 * Theme Provider Component
 *
 * Provides theme context to the application and handles theme switching.
 */
export declare const ThemeProvider: ({ initialTheme, persistTheme, storageKey, children, }: ThemeProviderProps) => JSX.Element;
export {};
//# sourceMappingURL=ThemeProvider.d.ts.map