/**
 * Theme System
 *
 * This file exports the theme system for the ADHD Trading Dashboard.
 * Centralized theme architecture with CSS variable generation.
 */
export * from './types';
export * from './profitLossTheme';
export * from './tokens';
export { f1Theme as mercedesGreenTheme } from './f1Theme';
export { f1OfficialTheme } from './f1OfficialTheme';
export { lightTheme } from './lightTheme';
export { darkTheme } from './darkTheme';
export { f1Theme } from './f1Theme';
export * from './ThemeProvider';
export * from './css-generator';
export declare const AVAILABLE_THEMES: {
    readonly 'mercedes-green': () => Promise<import('./types').Theme>;
    readonly 'f1-official': () => Promise<import('./types').Theme>;
    readonly dark: () => Promise<import('./types').Theme>;
};
export type ThemeName = keyof typeof AVAILABLE_THEMES;
//# sourceMappingURL=index.d.ts.map