/**
 * Theme Tokens
 *
 * This module exports all the design tokens used in the theme.
 */
export * from './colors';
export * from './spacing';
export * from './typography';
/**
 * Breakpoints
 *
 * These are the breakpoint values used for responsive design.
 */
export declare const breakpoints: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
};
/**
 * Border Radius
 *
 * These are the border radius values used throughout the application.
 */
export declare const borderRadius: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    pill: string;
    circle: string;
};
/**
 * Shadows
 *
 * These are the shadow values used throughout the application.
 */
export declare const shadows: {
    sm: string;
    md: string;
    lg: string;
};
/**
 * Transitions
 *
 * These are the transition values used throughout the application.
 */
export declare const transitions: {
    fast: string;
    normal: string;
    slow: string;
};
/**
 * Z-Index
 *
 * These are the z-index values used throughout the application.
 */
export declare const zIndex: {
    base: number;
    overlay: number;
    modal: number;
    popover: number;
    tooltip: number;
    fixed: number;
};
//# sourceMappingURL=index.d.ts.map