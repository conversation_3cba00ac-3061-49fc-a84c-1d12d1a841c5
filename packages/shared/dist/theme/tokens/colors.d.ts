/**
 * Color Tokens
 *
 * This file defines the base color tokens used throughout the application.
 */
/**
 * Base Colors
 *
 * These are the raw color values used as the foundation for the theme.
 */
export declare const baseColors: {
    f1Red: string;
    f1RedDark: string;
    f1RedLight: string;
    f1Blue: string;
    f1BlueDark: string;
    f1BlueLight: string;
    f1MercedesGreen: string;
    f1MercedesGreenDark: string;
    f1MercedesGreenLight: string;
    f1McLarenOrange: string;
    f1McLarenOrangeDark: string;
    f1McLarenOrangeLight: string;
    f1RacingYellow: string;
    f1RacingYellowDark: string;
    f1RacingYellowLight: string;
    f1Carbon: string;
    f1Silver: string;
    white: string;
    black: string;
    gray100: string;
    gray200: string;
    gray300: string;
    gray400: string;
    gray500: string;
    gray600: string;
    gray700: string;
    gray800: string;
    gray900: string;
    green: string;
    greenDark: string;
    greenLight: string;
    yellow: string;
    yellowDark: string;
    yellowLight: string;
    orange: string;
    orangeDark: string;
    orangeLight: string;
    red: string;
    redDark: string;
    redLight: string;
    purple: string;
    purpleDark: string;
    purpleLight: string;
    transparent: string;
    blackTransparent10: string;
    blackTransparent20: string;
    blackTransparent50: string;
    whiteTransparent10: string;
    whiteTransparent20: string;
    whiteTransparent50: string;
};
/**
 * Semantic Colors - Dark Mode
 *
 * These map the base colors to semantic meanings for dark mode.
 */
export declare const darkModeColors: {
    background: string;
    surface: string;
    surfaceHover: string;
    surfaceActive: string;
    textPrimary: string;
    textSecondary: string;
    textDisabled: string;
    textInverse: string;
    border: string;
    borderHover: string;
    borderFocus: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    profit: string;
    loss: string;
    neutral: string;
    tooltipBackground: string;
    modalBackground: string;
    chartGrid: string;
    chartLine: string;
};
/**
 * Semantic Colors - Light Mode
 *
 * These map the base colors to semantic meanings for light mode.
 */
export declare const lightModeColors: {
    background: string;
    surface: string;
    surfaceHover: string;
    surfaceActive: string;
    textPrimary: string;
    textSecondary: string;
    textDisabled: string;
    textInverse: string;
    border: string;
    borderHover: string;
    borderFocus: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    profit: string;
    loss: string;
    neutral: string;
    tooltipBackground: string;
    modalBackground: string;
    chartGrid: string;
    chartLine: string;
};
//# sourceMappingURL=colors.d.ts.map