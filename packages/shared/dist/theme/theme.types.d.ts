/**
 * Theme Type Definitions
 *
 * This file contains TypeScript type definitions for the theme system.
 */
export interface ColorPalette {
    primary: string;
    primaryDark: string;
    primaryLight: string;
    secondary: string;
    secondaryDark: string;
    secondaryLight: string;
    accent: string;
    accentDark: string;
    accentLight: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: string;
    surface: string;
    cardBackground: string;
    border: string;
    divider: string;
    textPrimary: string;
    textSecondary: string;
    textDisabled: string;
    textInverse: string;
    chartGrid: string;
    chartLine: string;
    chartAxis: string;
    chartTooltip: string;
    profit: string;
    loss: string;
    neutral: string;
    tabActive: string;
    tabInactive: string;
    tooltipBackground: string;
    modalBackground: string;
    sidebarBackground: string;
    headerBackground: string;
}
export interface Spacing {
    xxs: string;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
}
export interface Breakpoints {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
}
export interface FontSizes {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
    h1: string;
    h2: string;
    h3: string;
    h4: string;
    h5: string;
    h6: string;
}
export interface BorderRadius {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    pill: string;
    circle: string;
}
export interface Shadows {
    sm: string;
    md: string;
    lg: string;
}
export interface Transitions {
    fast: string;
    normal: string;
    slow: string;
}
export interface ZIndex {
    base: number;
    overlay: number;
    modal: number;
    popover: number;
    tooltip: number;
    fixed: number;
}
export interface FontWeights {
    light: number;
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
}
export interface LineHeights {
    tight: number;
    normal: number;
    loose: number;
}
export interface FontFamilies {
    sans: string;
    mono: string;
}
export interface Theme {
    name: string;
    colors: ColorPalette;
    spacing: Spacing;
    breakpoints: Breakpoints;
    fontSizes: FontSizes;
    fontWeights: FontWeights;
    lineHeights: LineHeights;
    fontFamilies: FontFamilies;
    borderRadius: BorderRadius;
    shadows: Shadows;
    transitions: Transitions;
    zIndex: ZIndex;
}
//# sourceMappingURL=theme.types.d.ts.map