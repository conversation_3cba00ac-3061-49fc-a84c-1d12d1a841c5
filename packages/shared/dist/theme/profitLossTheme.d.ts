/**
 * Profit/Loss Theme Configuration
 *
 * EXTRACTED FROM: ProfitLossCell.tsx (reducing complexity)
 * Centralized styling logic and theme constants for profit/loss components.
 */
export type ProfitLossSize = 'small' | 'medium' | 'large';
export type ProfitLossVariant = 'profit' | 'loss' | 'neutral' | 'default';
/**
 * Size configurations for profit/loss components
 */
export declare const profitLossSizes: {
    small: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
    medium: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
    large: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
};
/**
 * Color configurations for profit/loss variants
 */
export declare const profitLossColors: {
    profit: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
    loss: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
    neutral: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
    default: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
};
/**
 * Base styles for profit/loss components
 */
export declare const profitLossBaseStyles: import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
/**
 * Loading animation styles
 */
export declare const profitLossLoadingStyles: import('styled-components').FlattenSimpleInterpolation;
/**
 * Helper function to get size styles
 */
export declare const getProfitLossSize: (size: ProfitLossSize) => import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
/**
 * Helper function to get color styles based on amount
 */
export declare const getProfitLossVariant: (isProfit: boolean, isLoss: boolean, isNeutral: boolean) => ProfitLossVariant;
/**
 * Helper function to get color styles
 */
export declare const getProfitLossColors: (variant: ProfitLossVariant) => import('styled-components').FlattenInterpolation<import('styled-components').ThemeProps<import('styled-components').DefaultTheme>>;
//# sourceMappingURL=profitLossTheme.d.ts.map