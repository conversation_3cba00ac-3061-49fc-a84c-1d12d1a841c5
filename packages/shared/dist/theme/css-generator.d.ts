import { Theme } from './types';
/**
 * Converts a theme object to CSS variables
 */
export declare function generateCSSVariables(theme: Theme): string;
/**
 * Generates semantic CSS variables for components
 */
export declare function generateSemanticVariables(theme: Theme): string;
/**
 * Generates complete CSS for all themes
 */
export declare function generateAllThemeCSS(themes: Record<string, Theme>): string;
//# sourceMappingURL=css-generator.d.ts.map