/**
 * Utilities
 *
 * Common utility functions for the application
 */
/**
 * Format currency value
 * @param value - The value to format
 * @param currency - The currency symbol (default: $)
 * @returns Formatted currency string
 */
export declare function formatCurrency(value: number, currency?: string): string;
/**
 * Format percentage value
 * @param value - The value to format (0-1)
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export declare function formatPercentage(value: number, decimals?: number): string;
/**
 * Format date
 * @param date - Date string or Date object
 * @param format - Format style (default: 'short')
 * @returns Formatted date string
 */
export declare function formatDate(date: string | Date, format?: 'short' | 'medium' | 'long'): string;
/**
 * Truncate text
 * @param text - The text to truncate
 * @param maxLength - Maximum length (default: 50)
 * @returns Truncated text
 */
export declare function truncateText(text: string, maxLength?: number): string;
/**
 * Generate a unique ID
 * @returns Unique ID string
 */
export declare function generateId(): string;
/**
 * Debounce function
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export declare function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void;
/**
 * Throttle function
 * @param func - Function to throttle
 * @param limit - Limit time in milliseconds
 * @returns Throttled function
 */
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void;
export { SessionUtils } from './sessionUtils';
export { getCurrentNYTime, getCurrentNYMinutes, convertNYToLocal, convertLocalToNY, getUserTimezone, formatTimeForMobile, formatTimeForDesktop, formatTimeInterval, getTimeUntilNYTime, getCurrentDualTime, convertSessionToDualTime, getSessionStatus, timeToMinutes, minutesToTime, isCurrentTimeInNYWindow, type TimeInterval, type SessionTimeInfo, } from './timeZoneUtils';
//# sourceMappingURL=index.d.ts.map