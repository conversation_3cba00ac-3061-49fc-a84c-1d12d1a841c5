export interface ErrorHandlerOptions {
    /** Name of the component for error reporting */
    componentName?: string;
    /** Whether to log errors to the console */
    logToConsole?: boolean;
    /** Whether to report errors to monitoring service */
    reportToMonitoring?: boolean;
    /** Custom error handler */
    onError?: (error: Error) => void;
}
/**
 * Error handler hook
 *
 * @param options - Options for the error handler
 * @returns An object with error state and functions to handle errors
 */
export declare function useErrorHandler(options?: ErrorHandlerOptions): {
    error: Error | null;
    hasError: boolean;
    handleError: (error: Error) => void;
    resetError: () => void;
    tryExecute: <T>(fn: () => Promise<T> | T) => Promise<T | undefined>;
};
export default useErrorHandler;
//# sourceMappingURL=useErrorHandler.d.ts.map