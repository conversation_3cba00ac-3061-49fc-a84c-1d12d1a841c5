{"version": 3, "file": "useSessionSelection.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/useSessionSelection.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EACL,gBAAgB,EAChB,WAAW,EACX,eAAe,EACf,SAAS,EACT,oBAAoB,EACpB,oBAAoB,EACrB,MAAM,0BAA0B,CAAC;AAGlC,MAAM,WAAW,0BAA0B;IACzC,gCAAgC;IAChC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,6CAA6C;IAC7C,iBAAiB,CAAC,EAAE,OAAO,CAAC;IAC5B,mDAAmD;IACnD,aAAa,CAAC,EAAE,oBAAoB,CAAC;IACrC,sCAAsC;IACtC,iBAAiB,CAAC,EAAE,CAAC,SAAS,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAC1D,sCAAsC;IACtC,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,MAAM,WAAW,yBAAyB;IAExC,SAAS,EAAE,gBAAgB,CAAC;IAG5B,aAAa,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,CAAC;IAClD,WAAW,EAAE,CAAC,SAAS,EAAE,eAAe,KAAK,IAAI,CAAC;IAClD,iBAAiB,EAAE,CAAC,SAAS,EAAE,SAAS,KAAK,IAAI,CAAC;IAClD,cAAc,EAAE,MAAM,IAAI,CAAC;IAG3B,YAAY,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,oBAAoB,CAAC;IACrD,gBAAgB,EAAE,OAAO,CAAC;IAG1B,iBAAiB,EAAE,KAAK,CAAC;QAAE,KAAK,EAAE,WAAW,CAAC;QAAC,KAAK,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;IAC/E,eAAe,EAAE,KAAK,CAAC;QACrB,KAAK,EAAE,eAAe,CAAC;QACvB,KAAK,EAAE,MAAM,CAAC;QACd,KAAK,EAAE,MAAM,CAAC;QACd,aAAa,EAAE,WAAW,CAAC;KAC5B,CAAC,CAAC;IAGH,mBAAmB,EAAE,KAAK,CAAC;QACzB,OAAO,EAAE,WAAW,CAAC;QACrB,YAAY,EAAE,MAAM,CAAC;QACrB,MAAM,EAAE,KAAK,CAAC;YAAE,KAAK,EAAE,eAAe,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;KAC1D,CAAC,CAAC;IAGH,cAAc,EAAE,gBAAgB,GAAG,IAAI,CAAC;IACxC,sBAAsB,EAAE,OAAO,CAAC;IAGhC,iBAAiB,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,GAAG,CAAC;IACrD,eAAe,EAAE,CAAC,SAAS,EAAE,eAAe,KAAK,GAAG,CAAC;IACrD,oBAAoB,EAAE,CAAC,aAAa,EAAE,MAAM,KAAK,gBAAgB,GAAG,IAAI,CAAC;CAC1E;AAED;;GAEG;AACH,eAAO,MAAM,mBAAmB,GAC9B,UAAS,0BAA+B,KACvC,yBAyKF,CAAC"}