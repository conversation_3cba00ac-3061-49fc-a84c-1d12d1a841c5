# Build System Documentation

> **⚙️ Comprehensive guide to the ADHD Trading Dashboard build system**

## 📋 Overview

The ADHD Trading Dashboard uses a sophisticated build system optimized for
TypeScript monorepos with Vite, featuring incremental compilation, hot module
replacement, and production optimization.

## 🏗️ Build Architecture

### Technology Stack

- **Build Tool**: Vite 4.3+ with ES modules
- **TypeScript**: Project references for incremental compilation
- **Package Manager**: Yarn workspaces with exact versions
- **Module System**: ES modules throughout the codebase

### Build Flow

```
Source Code → TypeScript Compilation → Vite Build → Optimized Output
```

## 📦 Package Build Configuration

### Shared Package (`packages/shared/`)

**TypeScript Configuration** (`tsconfig.json`):

```json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "composite": true,
    "declaration": true,
    "declarationMap": true,
    "outDir": "dist",
    "rootDir": "src"
  },
  "include": ["src/**/*"],
  "exclude": ["dist", "**/*.test.*", "**/*.stories.*"]
}
```

**Vite Configuration** (`vite.config.ts`):

```typescript
export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.ts',
      formats: ['es', 'cjs'],
      fileName: format => `index.${format}.js`,
    },
    rollupOptions: {
      external: ['react', 'react-dom', 'styled-components'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'styled-components': 'styled',
        },
      },
    },
  },
});
```

### Dashboard Package (`packages/dashboard/`)

**TypeScript Configuration** (`tsconfig.json`):

```json
{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true
  },
  "references": [{ "path": "../shared" }],
  "include": ["src/**/*"],
  "exclude": ["dist", "**/*.test.*"]
}
```

**Vite Configuration** (`vite.config.ts`):

```typescript
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../shared/src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['recharts'],
          utils: ['date-fns', 'lodash'],
        },
      },
    },
  },
});
```

## 🛠️ Build Commands

### Development Commands

```bash
# Enhanced development server
yarn dev                    # Start dashboard with hot reload

# Package-specific development
yarn build:shared --watch   # Watch mode for shared package
yarn dev:storybook          # Component development
```

### Production Commands

```bash
# Full production build
yarn build                  # Build all packages

# Package-specific builds
yarn build:shared           # Build shared package only
yarn build:dashboard        # Build dashboard package only

# Clean builds
yarn build:clean            # Clean build from scratch
yarn clean                  # Remove build artifacts
```

### Build Validation

```bash
# TypeScript validation
yarn typescript:validate    # Check TypeScript compilation

# Build validation
yarn build:validate         # Validate build output

# Performance analysis
yarn analyze                # Bundle analysis
```

## ⚡ Performance Optimizations

### Code Splitting

- **Vendor Chunks**: React, React DOM separated
- **Feature Chunks**: Trading features split by route
- **Utility Chunks**: Date/math utilities grouped
- **Dynamic Imports**: Lazy loading for non-critical features

### Tree Shaking

- **ES Modules**: Full tree shaking support
- **Side Effect Free**: Marked packages for aggressive optimization
- **Dead Code Elimination**: Unused code removal

### Bundle Optimization

- **Minification**: Terser for JavaScript, cssnano for CSS
- **Compression**: Gzip and Brotli compression
- **Asset Optimization**: Image and font optimization

## 🔧 Development Optimizations

### Hot Module Replacement (HMR)

- **React Fast Refresh**: Component state preservation
- **CSS Hot Reload**: Instant style updates
- **TypeScript Integration**: Type checking in development

### Incremental Compilation

- **TypeScript Project References**: Only rebuild changed packages
- **Build Cache**: Persistent build cache for faster rebuilds
- **Watch Mode**: Efficient file watching with debouncing

## 📊 Build Monitoring

### Performance Metrics

```bash
# Build time analysis
yarn analyze:performance    # Build performance metrics

# Bundle size analysis
yarn analyze:bundle         # Bundle size breakdown

# Memory usage monitoring
yarn health                 # System resource monitoring
```

### Build Reports

- **Bundle Analyzer**: Visual bundle composition
- **TypeScript Performance**: Compilation time tracking
- **Dependency Analysis**: Package size impact

## 🚨 Troubleshooting

### Common Build Issues

**TypeScript Compilation Errors**:

```bash
# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo
yarn build:clean
```

**Dependency Resolution Issues**:

```bash
# Clean and reinstall
yarn clean:deep
yarn install
```

**Memory Issues**:

```bash
# Increase Node.js memory
export NODE_OPTIONS="--max-old-space-size=4096"
yarn build
```

### Build Debugging

```bash
# Verbose build output
yarn build --verbose

# TypeScript diagnostics
yarn typescript:validate --verbose

# Dependency tree analysis
yarn deps:check
```

## 🔄 CI/CD Integration

### GitHub Actions Configuration

```yaml
name: Build and Test
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'

      - run: yarn install --frozen-lockfile
      - run: yarn build
      - run: yarn test
      - run: yarn test:e2e
```

### Build Validation Pipeline

1. **Dependency Installation**: Frozen lockfile for reproducibility
2. **TypeScript Compilation**: Strict type checking
3. **Unit Testing**: Vitest test suite
4. **E2E Testing**: Playwright automation
5. **Build Verification**: Production build validation

## 📈 Build Metrics

### Performance Targets

- **Development Build**: < 5 seconds
- **Production Build**: < 30 seconds
- **Hot Reload**: < 1 second
- **Bundle Size**: < 1MB gzipped

### Monitoring

- **Build Time Tracking**: Historical build performance
- **Bundle Size Monitoring**: Size regression detection
- **Memory Usage**: Resource consumption tracking

---

**Next Steps**: Explore the [Development Guide](../DEVELOPMENT.md) for
development workflow or [Architecture Guide](../ARCHITECTURE.md) for system
design details.
