# Development Guide

> **🔧 Comprehensive development workflow and best practices for ADHD Trading
> Dashboard**

## 🚀 Development Environment Setup

### Prerequisites

- **Node.js v18 LTS** (recommended for compatibility)
- **Yarn** package manager (required for workspaces)
- **Git** for version control
- **VS Code** (recommended with extensions)

### Initial Setup

```bash
# Clone and setup
git clone https://github.com/HeinekenBottle/adhd-trading-dashboard-lib.git
cd adhd-trading-dashboard-lib

# Enhanced setup (recommended)
yarn setup

# Manual setup alternative
yarn install
yarn build:shared
yarn dev
```

## 🛠️ Development Workflow

### Daily Development Commands

```bash
# Start development
yarn dev                    # Enhanced dev server with hot reload

# Code quality
yarn health                 # System health check
yarn lint                   # ESLint with auto-fix
yarn test:watch            # Watch mode testing

# Quick workflows (AI-optimized)
yarn setup:quick           # Fast setup
yarn test:quick            # Quick test workflow
yarn quality:check         # Comprehensive quality check
```

### Build Commands

```bash
# Development builds
yarn build:shared          # Build shared package only
yarn build:dashboard       # Build dashboard package only
yarn build:dev             # Development build

# Production builds
yarn build                 # Full production build
yarn build:clean           # Clean build from scratch
```

## 📁 Project Structure & Conventions

### Monorepo Organization

```
packages/
├── shared/                 # Foundation layer
│   ├── src/components/     # Atomic design components
│   ├── src/hooks/         # Reusable React hooks
│   ├── src/theme/         # F1 theme system
│   ├── src/services/      # Storage and API services
│   └── src/types/         # Shared TypeScript types
└── dashboard/             # Application layer
    ├── src/features/      # Feature-based organization
    ├── src/pages/         # Route-level components
    └── src/layouts/       # Application layouts
```

### Feature Development Pattern

Each feature follows a consistent structure:

```
features/feature-name/
├── components/            # Feature-specific UI components
├── hooks/                # Feature-specific custom hooks
├── services/             # API integration and business logic
├── types/                # Feature-specific type definitions
├── utils/                # Feature-specific utilities
├── __tests__/            # Feature tests
├── FeatureName.tsx       # Main feature component
└── index.ts              # Feature exports
```

## 🎨 Component Development

### Atomic Design Principles

1. **Atoms** (`shared/components/atoms/`)

   - Basic HTML elements (Button, Input, Typography)
   - No business logic, pure presentation
   - Highly reusable across features

2. **Molecules** (`shared/components/molecules/`)

   - Combinations of atoms (FormField, Card, Modal)
   - Simple interactive behavior
   - Domain-agnostic functionality

3. **Organisms** (`shared/components/organisms/`)
   - Complex combinations of molecules and atoms
   - Feature-specific behavior (DataTable, Charts)
   - Reusable across similar use cases

### Component Creation Guidelines

```typescript
// Example component structure
import React from 'react';
import styled from 'styled-components';
import { theme } from '@adhd-trading-dashboard/shared';

interface ComponentProps {
  /** Component description */
  prop: string;
  /** Optional prop with default */
  optional?: boolean;
}

const StyledComponent = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  background: ${({ theme }) => theme.colors.background.primary};
`;

/**
 * Component Description
 *
 * @param props - Component props
 */
export const Component: React.FC<ComponentProps> = ({
  prop,
  optional = false,
}) => {
  return <StyledComponent>{/* Component content */}</StyledComponent>;
};
```

## 🎨 Theme System Usage

### Available Themes

- **Mercedes Green**: Authentic F1 Mercedes team aesthetics with Petronas teal
- **F1 Official**: Official F1 colors with racing typography
- **Dark**: Professional dark mode with high contrast

### Theme Usage Patterns

```typescript
// Using theme in styled-components
const StyledComponent = styled.div`
  color: ${({ theme }) => theme.colors.primary};
  background: ${({ theme }) => theme.colors.background.dark};
  border: 1px solid ${({ theme }) => theme.colors.border.primary};
`;

// Using theme hook
import { useTheme } from 'styled-components';

const Component = () => {
  const theme = useTheme();

  return (
    <div
      style={{
        color: theme.colors.text.primary,
        fontSize: theme.typography.body.fontSize,
      }}
    >
      Content
    </div>
  );
};
```

## 🗄️ Data Management

### IndexedDB Integration

```typescript
import { tradeStorageService } from '@adhd-trading-dashboard/shared';

// Fetch trades
const trades = await tradeStorageService.getAllTrades();

// Add new trade
const newTrade = await tradeStorageService.addTrade(tradeData);

// Update trade
const updatedTrade = await tradeStorageService.updateTrade(id, updates);
```

### State Management Patterns

```typescript
// Context-based state management
import { createContext, useContext } from 'react';

interface FeatureState {
  data: any[];
  loading: boolean;
  error: string | null;
}

const FeatureContext = createContext<FeatureState | null>(null);

export const useFeatureState = () => {
  const context = useContext(FeatureContext);
  if (!context) {
    throw new Error('useFeatureState must be used within FeatureProvider');
  }
  return context;
};
```

## 🧪 Testing Strategy

### Unit Testing with Vitest

```typescript
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { Component } from './Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component prop='test' />);
    expect(screen.getByText('test')).toBeInTheDocument();
  });
});
```

### E2E Testing with Playwright

```typescript
import { test, expect } from '@playwright/test';

test('daily guide navigation', async ({ page }) => {
  await page.goto('http://localhost:3000/daily-guide');
  await expect(page.locator('[data-testid="session-focus"]')).toBeVisible();
});
```

## 🔧 Development Tools

### VS Code Configuration

Recommended extensions:

- TypeScript and JavaScript Language Features
- ESLint
- Prettier
- Styled Components

### Enhanced Development Scripts

```bash
# Health monitoring
yarn health                # System diagnostics
yarn deps:check           # Dependency analysis
yarn analyze              # Architecture analysis

# Performance monitoring
yarn analyze:performance  # Performance bottlenecks
yarn analyze:bundle      # Bundle analysis
```

## 🚨 Troubleshooting

### Common Issues

**TypeScript Errors**

```bash
# Clear TypeScript cache
rm -rf packages/*/tsconfig.tsbuildinfo
yarn build:clean
```

**Port Conflicts**

```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9
```

**Dependency Issues**

```bash
# Clean and reinstall
yarn clean:deep
yarn install
```

### Performance Optimization

- Use React.memo for expensive components
- Implement proper loading states
- Optimize bundle size with code splitting
- Use IndexedDB efficiently for large datasets

## 📋 Code Quality Standards

### TypeScript Guidelines

- Use strict TypeScript mode
- Import types with "import type"
- Export types with "export type"
- Never use 'any' types
- Verify all component references exist

### ESLint Rules

- No unused variables or imports
- Consistent import organization
- Proper TypeScript typing
- React hooks rules compliance

### Git Workflow

```bash
# Feature development
git checkout -b feature/feature-name
git commit -m "feat: add feature description"
git push origin feature/feature-name

# Quality checks before PR
yarn quality:check
yarn test
yarn build
```

---

**Next Steps**: Explore the [Architecture Guide](./ARCHITECTURE.md) for system
design details.
