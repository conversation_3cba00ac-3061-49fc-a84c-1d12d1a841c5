# ADHD Trading Dashboard Documentation

> **📚 Comprehensive documentation for the elite ICT trading intelligence
> system**

Welcome to the complete documentation for the ADHD Trading Dashboard - a
sophisticated React TypeScript monorepo that transforms trading analysis through
advanced ICT methodology integration and Formula 1-inspired design.

## 🚀 Quick Navigation

### Getting Started

- **[📖 Getting Started Guide](./GETTING_STARTED.md)** - Complete setup and
  installation guide
- **[🏗️ System Architecture](./ARCHITECTURE.md)** - Monorepo structure and
  design patterns
- **[🔧 Development Guide](./DEVELOPMENT.md)** - Development workflow and best
  practices

### Project Information

- **[📋 Project Overview](./PROJECT_OVERVIEW.md)** - Executive summary and
  feature overview
- **[📦 Package Documentation](./packages/)** - Package-specific documentation

## 📁 Documentation Structure

```
docs/
├── README.md                    # This file - documentation index
├── GETTING_STARTED.md          # Setup and installation guide
├── ARCHITECTURE.md             # System design and structure
├── DEVELOPMENT.md              # Development workflow and practices
├── PROJECT_OVERVIEW.md         # Executive summary and features
├── packages/                   # Package-specific documentation
│   ├── SHARED.md              # Shared package documentation
│   └── DASHBOARD.md           # Dashboard package documentation
└── technical/                  # Technical documentation
    └── BUILD.md               # Build system documentation
```

## 🎯 What You'll Find Here

### For New Developers

1. **Start with [Getting Started](./GETTING_STARTED.md)** - Set up your
   development environment
2. **Read [Project Overview](./PROJECT_OVERVIEW.md)** - Understand the project
   vision and features
3. **Explore [Architecture](./ARCHITECTURE.md)** - Learn the system design and
   patterns
4. **Follow [Development Guide](./DEVELOPMENT.md)** - Master the development
   workflow

### For Contributors

- **[Development Guide](./DEVELOPMENT.md)** - Coding standards and best
  practices
- **[Architecture Guide](./ARCHITECTURE.md)** - System design principles
- **[Package Documentation](./packages/)** - Detailed package APIs and usage

### For Users

- **[Project Overview](./PROJECT_OVERVIEW.md)** - Feature overview and
  capabilities
- **[Getting Started](./GETTING_STARTED.md)** - How to run and use the
  application

## 🏎️ Key Features Documented

### Elite ICT Intelligence

- **Model Selection Engine**: RD-Cont vs FVG-RD analysis with real performance
  data
- **Pattern Quality Scoring**: ICT-specific 5-point rating system
- **Session Intelligence**: Granular session windows with live recommendations
- **Success Probability Calculator**: Multi-factor analysis with risk management

### Advanced Trading Analytics

- **Real Data Integration**: 15+ professional trading metrics from IndexedDB
- **Performance Optimization**: Batch calculations with caching
- **PD Array Tracking**: FVG, NWOG, NDOG, RD levels, and liquidity analysis
- **Dynamic Daily Guide**: Session Focus with time-based insights

### F1-Themed UI System

- **Mercedes Green Theme**: Authentic F1 Mercedes team aesthetics
- **F1 Official Theme**: Official F1 colors with racing typography
- **Dark Theme**: Professional dark mode with high contrast
- **ADHD Optimization**: Quick-scan format with color-coded priorities

## 🛠️ Technical Documentation

### Architecture & Design

- **Monorepo Structure**: Clean separation with shared → dashboard dependency
  flow
- **Atomic Design**: Components organized as atoms → molecules → organisms
- **Feature-Based Organization**: Isolated features with clear boundaries
- **TypeScript First**: Strict typing throughout the codebase

### Data & Storage

- **IndexedDB Integration**: 4-table relational structure for trading data
- **Real-Time Analytics**: Live calculations and performance monitoring
- **State Management**: React Context with feature-specific state
- **Performance Caching**: Optimized calculations for large datasets

### Development Tools

- **Enhanced Development Server**: Hot reload with performance monitoring
- **Quality Assurance**: ESLint, TypeScript, and comprehensive testing
- **AI-Optimized Workflows**: Commands designed for efficient iteration
- **Build Optimization**: Vite with ES modules and code splitting

## 📊 Current Status

### ✅ Completed (Phase 1 & 2)

- **Architecture Cleanup**: Clean monorepo with zero TypeScript errors
- **Real Data Integration**: 15+ trading metrics from actual IndexedDB data
- **Elite ICT Intelligence**: Complete model selection and pattern analysis
- **F1-Themed UI**: Three themes with ADHD optimization
- **Performance Optimization**: Batch calculations with caching

### 🚀 Live Features

- **Main Dashboard**: [http://localhost:3000](http://localhost:3000)
- **Daily Guide**:
  [http://localhost:3000/daily-guide](http://localhost:3000/daily-guide)
- **Elite Intelligence**: Daily Guide → Elite Intelligence tab
- **PD Array Tracker**: Daily Guide → PD Array tab
- **Session Focus**: Daily Guide → Session Focus tab (default)

## 🔧 Development Commands

### Quick Start

```bash
yarn setup                     # Complete development setup
yarn dev                       # Enhanced development server
yarn health                    # System health check
```

### Quality Assurance

```bash
yarn quality:check             # Comprehensive quality check
yarn test:quick                # Quick test workflow
yarn lint                      # ESLint with TypeScript rules
yarn analyze                   # Architecture analysis
```

### Build & Deploy

```bash
yarn build                     # Production build
yarn build:clean               # Clean build from scratch
yarn preview                   # Preview production build
```

## 📚 Additional Resources

### External Links

- **Repository**:
  [GitHub - ADHD Trading Dashboard](https://github.com/HeinekenBottle/adhd-trading-dashboard-lib)
- **Live Demo**: [http://localhost:3000](http://localhost:3000) (when running
  locally)

### Package Information

- **Shared Package**: Foundation components, theme system, storage services
- **Dashboard Package**: Trading features, ICT intelligence, analytics

### Technology Stack

- **Framework**: React 18 + TypeScript 5.8+
- **Build**: Vite 4.3+ with ES modules
- **Styling**: styled-components with CSS variables
- **Testing**: Vitest (unit) + Playwright (E2E) + Storybook (visual)
- **Storage**: IndexedDB with trade storage service

## 🤝 Contributing

1. **Read the [Development Guide](./DEVELOPMENT.md)** for coding standards
2. **Understand the [Architecture](./ARCHITECTURE.md)** for system design
3. **Follow the quality workflow**: `yarn quality:check` before commits
4. **Test thoroughly**: Use `yarn test` and `yarn test:e2e`

## 📄 Documentation Maintenance

This documentation is actively maintained and reflects the current state of the
codebase. If you find any inconsistencies or outdated information, please:

1. Check the latest code in the repository
2. Update the relevant documentation files
3. Submit a pull request with your improvements

---

**Ready to start?** Begin with the [Getting Started Guide](./GETTING_STARTED.md)
to set up your development environment and explore the elite ICT trading
intelligence system! 🏎️💨
