# Dashboard Package Documentation

> **🏎️ Main trading application with ICT intelligence and F1-themed features**

## 📋 Package Overview

The `@adhd-trading-dashboard/dashboard` package is the main application layer
that provides trading-specific features, ICT intelligence, and business logic.
It consumes the shared package for components and utilities while implementing
sophisticated trading analysis and decision-making tools.

## 📁 Package Structure

```
packages/dashboard/src/
├── features/           # Feature-based organization
│   ├── daily-guide/    # Daily trading intelligence and planning
│   ├── trade-analysis/ # Performance analytics and metrics
│   ├── trade-journal/  # Trade logging and management
│   └── trading-dashboard/ # Main dashboard interface
├── layouts/            # Application layouts and structure
├── pages/              # Route-level components
├── routes/             # Route definitions and configuration
└── App.tsx            # Main application component
```

## 🧠 Elite ICT Intelligence Features

### Daily Guide (`features/daily-guide/`)

The Daily Guide is the centerpiece of the ICT intelligence system, providing
comprehensive trading analysis and recommendations.

#### Session Focus

Real-time session analysis with performance insights:

```typescript
// Session analytics hook
import { useSessionAnalytics } from './hooks/useSessionAnalytics';

const { currentSession, sessionPerformance, recommendations, isLoading } =
  useSessionAnalytics();

// Session performance data
interface SessionPerformance {
  winRate: number;
  totalTrades: number;
  avgRMultiple: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  recommendation: string;
}
```

#### Elite Intelligence

Advanced ICT model selection and pattern analysis:

```typescript
// Model selection engine
import { useModelSelectionEngine } from './hooks/useModelSelectionEngine';

const { recommendation, confidence, reasoning, historicalPerformance } =
  useModelSelectionEngine();

// Model recommendation structure
interface ModelRecommendation {
  model: 'RD-Cont' | 'FVG-RD';
  confidence: number; // 0-100
  reasoning: string;
  expectedWinRate: number;
  expectedRMultiple: number;
}
```

#### PD Array Intelligence

Real-time tracking of ICT Price Delivery Arrays:

```typescript
// PD Array analytics
import { usePDArrayIntelligence } from './hooks/usePDArrayIntelligence';

const { activeLevels, performance, recommendations } = usePDArrayIntelligence();

// PD Array level structure
interface PDArrayLevel {
  type: 'FVG' | 'NWOG' | 'NDOG' | 'RD' | 'Liquidity';
  level: string;
  timeframe: string;
  age: string;
  isActive: boolean;
  performance: {
    totalTrades: number;
    winRate: number;
    avgRMultiple: number;
  };
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
}
```

### Pattern Quality Scoring

Advanced ICT pattern quality assessment:

```typescript
// Pattern quality analysis
import { usePatternQualityScoring } from './hooks/usePatternQualityScoring';

const { analysis, currentScore, qualityBreakdown } = usePatternQualityScoring();

// Quality analysis structure
interface PatternQualityAnalysis {
  currentScore: number; // 1-5 scale
  qualityBreakdown: {
    confluence: number;
    timeframe: number;
    structure: number;
    momentum: number;
    risk: number;
  };
  recommendation: string;
  expectedOutcome: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR';
}
```

## 📊 Trading Analytics (`features/trade-analysis/`)

Comprehensive performance tracking and analysis system.

### Real Trade Analysis API

```typescript
// Real trade analysis service
import { realTradeAnalysisApi } from './services/realTradeAnalysisApi';

// Fetch comprehensive analysis
const analysisData = await realTradeAnalysisApi.getAnalysisData({
  dateRange: { start: startDate, end: endDate },
  symbols: ['EURUSD', 'GBPUSD'],
  tradeTypes: ['LONG', 'SHORT'],
});

// Analysis data structure
interface TradeAnalysisData {
  performanceMetrics: PerformanceMetrics;
  categoryPerformance: CategoryPerformance[];
  timePerformance: TimePerformance[];
  equityCurve: EquityCurvePoint[];
  distributionData: DistributionData;
}
```

### Performance Metrics

15+ professional trading metrics with real-time calculations:

```typescript
// Performance metrics calculation
interface PerformanceMetrics {
  totalTrades: number;
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  avgWin: number;
  avgLoss: number;
  largestWin: number;
  largestLoss: number;
  consecutiveWins: number;
  consecutiveLosses: number;
  avgHoldTime: number;
  totalPnL: number;
  avgRMultiple: number;
  expectancy: number;
}
```

### Performance Caching

Optimized calculations with batch processing:

```typescript
// Performance cache service
import {
  batchCalculateAnalytics,
  performanceMonitor,
} from './services/performanceCache';

// Batch calculation with caching
const analyticsResults = await batchCalculateAnalytics(trades, {
  performanceMetrics: calculateRealPerformanceMetrics,
  categoryPerformance: calculateCategoryPerformance,
  timePerformance: calculateTimePerformance,
  equityCurve: generateEquityCurve,
  distributionData: generateDistributionData,
});

// Performance monitoring
performanceMonitor.logMemoryUsage('before analysis');
const result = await performanceMonitor.measureTime('Calculation', () =>
  heavyCalculation(data)
);
```

## 📝 Trade Journal (`features/trade-journal/`)

Comprehensive trade logging and management system.

### Trade Entry System

```typescript
// Trade entry with ICT data
interface TradeEntryData {
  // Basic trade information
  symbol: string;
  tradeType: 'LONG' | 'SHORT';
  entryPrice: number;
  exitPrice?: number;
  quantity: number;

  // ICT-specific fields
  modelType: 'RD-Cont' | 'FVG-RD';
  sessionTiming: string;
  patternQualityRating: number; // 1-5

  // Setup classification
  primarySetup: string;
  secondarySetup?: string;
  confluenceFactors: string[];

  // FVG and liquidity data
  fvgDate?: string;
  fvgTime?: string;
  rdType?: string;
  rdTime?: string;
  drawOnLiquidity?: string;
  liquidityTaken?: boolean;
}
```

### Trade Management

```typescript
// Trade management hooks
import { useTradeManagement } from './hooks/useTradeManagement';

const { trades, addTrade, updateTrade, deleteTrade, isLoading, error } =
  useTradeManagement();

// Add new trade
const newTrade = await addTrade({
  symbol: 'EURUSD',
  tradeType: 'LONG',
  entryPrice: 1.085,
  quantity: 10000,
  modelType: 'RD-Cont',
  patternQualityRating: 4,
});
```

## 🏎️ Trading Dashboard (`features/trading-dashboard/`)

Main dashboard interface with F1-themed components.

### F1 Dashboard Container

```typescript
// F1-themed dashboard container
import { F1DashboardContainer } from './components/F1DashboardContainer';

// Dashboard with F1 styling
<F1DashboardContainer initialTab='summary' className='f1-dashboard' />;

// Container structure
interface F1DashboardContainerProps {
  initialTab?: 'summary' | 'trades' | 'setups' | 'analytics';
  className?: string;
}
```

### Dashboard Tabs

```typescript
// Dashboard tab configuration
const dashboardTabs = [
  {
    id: 'summary',
    label: 'Summary',
    icon: '📊',
    component: SummaryTab,
  },
  {
    id: 'trades',
    label: 'Trades',
    icon: '📈',
    component: TradesTab,
  },
  {
    id: 'setups',
    label: 'Setups',
    icon: '🎯',
    component: SetupsTab,
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: '🧮',
    component: AnalyticsTab,
  },
];
```

## 🎨 F1-Themed Components

### Theme Integration

All dashboard components use the F1 theme system:

```typescript
// F1-themed component
import styled from 'styled-components';

const F1Card = styled.div`
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.primary};
  border-radius: ${({ theme }) => theme.borderRadius.md};

  /* F1 racing stripes */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: ${({ theme }) => theme.colors.primary};
  }
`;

// Live indicator component
const LiveIndicator = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: ${({ theme }) => theme.colors.success};
    animation: pulse 2s infinite;
  }
`;
```

### ADHD-Optimized Design

Components designed for quick scanning and reduced cognitive load:

```typescript
// Quick-scan card component
interface QuickScanCardProps {
  title: string;
  value: string | number;
  status: 'excellent' | 'good' | 'average' | 'poor';
  trend?: 'up' | 'down' | 'neutral';
  priority?: 'high' | 'medium' | 'low';
}

const QuickScanCard: React.FC<QuickScanCardProps> = ({
  title,
  value,
  status,
  trend,
  priority,
}) => {
  const statusColor = getStatusColor(status);
  const priorityBorder = getPriorityBorder(priority);

  return (
    <Card statusColor={statusColor} priorityBorder={priorityBorder}>
      <CardHeader>
        <Title>{title}</Title>
        {trend && <TrendIndicator trend={trend} />}
      </CardHeader>
      <CardValue status={status}>{value}</CardValue>
    </Card>
  );
};
```

## 🔄 State Management

### Feature-Based Context

Each feature manages its own state with React Context:

```typescript
// Daily Guide context
import { createContext, useContext } from 'react';

interface DailyGuideState {
  activeTab: string;
  sessionData: SessionData;
  pdArrayData: PDArrayData[];
  eliteIntelligence: EliteIntelligenceData;
  isLoading: boolean;
  error: string | null;
}

const DailyGuideContext = createContext<DailyGuideState | null>(null);

export const useDailyGuideState = () => {
  const context = useContext(DailyGuideContext);
  if (!context) {
    throw new Error(
      'useDailyGuideState must be used within DailyGuideProvider'
    );
  }
  return context;
};
```

### Cross-Feature Communication

```typescript
// Global trading state
interface TradingState {
  selectedTrade: CompleteTradeData | null;
  activeFilters: TradeFilters;
  currentTimeframe: string;
  marketSession: MarketSession;
}

// State actions
interface TradingActions {
  selectTrade: (trade: CompleteTradeData) => void;
  updateFilters: (filters: Partial<TradeFilters>) => void;
  setTimeframe: (timeframe: string) => void;
  updateSession: (session: MarketSession) => void;
}
```

## 🧪 Testing Strategy

### Feature Testing

```typescript
// Feature integration test
import { render, screen, waitFor } from '@testing-library/react';
import { DailyGuide } from '../DailyGuide';
import { mockTradeData } from '../../__mocks__/tradeData';

describe('DailyGuide', () => {
  it('displays session focus with real data', async () => {
    render(<DailyGuide />);

    await waitFor(() => {
      expect(screen.getByTestId('session-focus')).toBeInTheDocument();
    });

    expect(screen.getByText(/Current Session/)).toBeInTheDocument();
    expect(screen.getByText(/Win Rate/)).toBeInTheDocument();
  });
});
```

### E2E Testing

```typescript
// E2E workflow test
import { test, expect } from '@playwright/test';

test('complete trading workflow', async ({ page }) => {
  // Navigate to daily guide
  await page.goto('http://localhost:3000/daily-guide');

  // Check session focus
  await expect(page.locator('[data-testid="session-focus"]')).toBeVisible();

  // Switch to Elite Intelligence
  await page.click('[data-testid="elite-intelligence-tab"]');
  await expect(
    page.locator('[data-testid="model-recommendation"]')
  ).toBeVisible();

  // Check PD Array tracking
  await page.click('[data-testid="pd-array-tab"]');
  await expect(page.locator('[data-testid="pd-array-levels"]')).toBeVisible();
});
```

## 📦 Build Configuration

### Vite Configuration

```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../shared/src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          charts: ['recharts'],
          utils: ['date-fns', 'lodash'],
        },
      },
    },
  },
});
```

### Development Scripts

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest run",
    "test:watch": "vitest",
    "test:e2e": "playwright test",
    "storybook": "storybook dev -p 6006"
  }
}
```

## 🚀 Feature Access

### Main Routes

- **Dashboard**: `/` - Main trading dashboard interface
- **Daily Guide**: `/daily-guide` - ICT intelligence and planning
- **Trade Journal**: `/trades` - Trade logging and management
- **Analytics**: `/analytics` - Performance analysis

### Daily Guide Tabs

- **Session Focus**: Default tab with session analysis
- **Elite Intelligence**: ICT model selection and pattern analysis
- **PD Array**: Real-time PD Array tracking and intelligence

### URL Examples

```
http://localhost:3000/                    # Main dashboard
http://localhost:3000/daily-guide        # Daily guide (Session Focus)
http://localhost:3000/daily-guide?tab=elite-intelligence  # Elite Intelligence
http://localhost:3000/daily-guide?tab=pd-array           # PD Array tracker
http://localhost:3000/trades             # Trade journal
http://localhost:3000/analytics          # Performance analytics
```

## 🔮 Future Enhancements

### Planned Features

- **Real-time Market Data**: Live price feeds and alerts
- **Advanced Backtesting**: Historical strategy validation
- **Machine Learning**: Enhanced pattern recognition
- **Mobile App**: React Native implementation
- **API Integration**: External broker and data provider connections

### Performance Optimizations

- **Virtual Scrolling**: For large trade datasets
- **Web Workers**: Background calculations
- **Service Workers**: Offline functionality
- **Progressive Loading**: Incremental feature loading

---

**Next Steps**: Explore the [Architecture Guide](../ARCHITECTURE.md) for system
design details or the [Development Guide](../DEVELOPMENT.md) for contribution
guidelines.
