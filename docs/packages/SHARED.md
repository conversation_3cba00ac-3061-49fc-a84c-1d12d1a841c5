# Shared Package Documentation

> **🔧 Foundation layer providing reusable components, theme system, and
> utilities**

## 📋 Package Overview

The `@adhd-trading-dashboard/shared` package serves as the foundation layer of
the monorepo, providing reusable components, theme system, storage services, and
utilities that are consumed by the dashboard application.

## 📁 Package Structure

```
packages/shared/src/
├── components/         # Atomic design component library
│   ├── atoms/          # Basic elements (Button, Input, Typography)
│   ├── molecules/      # Composite components (Card, FormField, Modal)
│   └── organisms/      # Complex components (DataTable, Chart, FilterPanel)
├── hooks/              # Custom React hooks
├── services/           # Core services (storage, data management)
├── state/              # State management utilities
├── theme/              # Design system and theming
├── types/              # Shared TypeScript types
└── utils/              # Pure utility functions
```

## 🎨 Component Library

### Atomic Design Architecture

The component library follows atomic design principles for maximum reusability
and maintainability:

#### Atoms (`components/atoms/`)

Basic building blocks that cannot be broken down further:

- **Button**: Primary, secondary, and icon button variants
- **Input**: Text inputs with validation states
- **Typography**: Heading, body, and caption text components
- **Icon**: SVG icon components with theme integration
- **Badge**: Status and notification badges

#### Molecules (`components/molecules/`)

Combinations of atoms that form functional units:

- **FormField**: Input with label, validation, and help text
- **Card**: Container with header, body, and footer sections
- **Modal**: Overlay dialog with backdrop and close functionality
- **Tooltip**: Contextual information overlay
- **SearchBox**: Input with search icon and clear functionality

#### Organisms (`components/organisms/`)

Complex components that combine molecules and atoms:

- **DataTable**: Sortable, filterable table with pagination
- **Chart**: Trading chart components with theme integration
- **FilterPanel**: Advanced filtering interface for trading data
- **Navigation**: Application navigation with theme switching
- **ErrorBoundary**: Error handling wrapper component

### Component Usage Examples

```typescript
// Basic atom usage
import { Button, Typography } from '@adhd-trading-dashboard/shared';

<Button variant='primary' onClick={handleClick}>
  <Typography variant='button'>Submit Trade</Typography>
</Button>;

// Molecule usage
import { FormField } from '@adhd-trading-dashboard/shared';

<FormField
  label='Symbol'
  value={symbol}
  onChange={setSymbol}
  error={symbolError}
  required
/>;

// Organism usage
import { DataTable } from '@adhd-trading-dashboard/shared';

<DataTable
  data={trades}
  columns={tradeColumns}
  onSort={handleSort}
  onFilter={handleFilter}
  pagination={{ page: 1, pageSize: 20 }}
/>;
```

## 🎨 Theme System

### Available Themes

The shared package provides a comprehensive theme system with three main themes:

#### Mercedes Green Theme

- **Primary Colors**: Petronas teal (#00D2BE) for active/profitable states
- **Background**: Dark racing aesthetic (#0f0f0f)
- **Accents**: Silver borders (#4b5563) and white text
- **Usage**: Default theme with authentic F1 Mercedes team aesthetics

#### F1 Official Theme

- **Primary Colors**: Official F1 red (#E10600) and gold (#FFD700)
- **Background**: F1 black (#15151E) with racing stripes
- **Typography**: Racing-inspired fonts with live timing aesthetics
- **Usage**: Official F1 app-inspired design

#### Dark Theme

- **Primary Colors**: Professional blue (#0600EF) and green accents
- **Background**: High contrast dark mode
- **Accessibility**: WCAG AA compliant contrast ratios
- **Usage**: Professional dark mode for extended use

### Theme Usage

```typescript
// Theme provider setup
import {
  ThemeProvider,
  mercedesGreenTheme,
} from '@adhd-trading-dashboard/shared';

<ThemeProvider theme={mercedesGreenTheme}>
  <App />
</ThemeProvider>;

// Using theme in components
import styled from 'styled-components';

const StyledComponent = styled.div`
  color: ${({ theme }) => theme.colors.text.primary};
  background: ${({ theme }) => theme.colors.background.primary};
  border: 1px solid ${({ theme }) => theme.colors.border.primary};
`;

// Theme hook usage
import { useTheme } from 'styled-components';

const Component = () => {
  const theme = useTheme();
  return <div style={{ color: theme.colors.primary }}>Themed content</div>;
};
```

### CSS Variables

The theme system generates CSS variables for consistent styling:

```css
:root {
  --color-primary: #00d2be;
  --color-background-primary: #0f0f0f;
  --color-text-primary: #ffffff;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --font-size-body: 14px;
  --font-weight-medium: 500;
}
```

## 🗄️ Storage Services

### TradeStorageService

The primary service for managing trading data with IndexedDB:

```typescript
import { tradeStorageService } from '@adhd-trading-dashboard/shared';

// CRUD operations
const trades = await tradeStorageService.getAllTrades();
const trade = await tradeStorageService.getTradeById(id);
const newTrade = await tradeStorageService.addTrade(tradeData);
const updated = await tradeStorageService.updateTrade(id, updates);
await tradeStorageService.deleteTrade(id);

// Advanced queries
const filtered = await tradeStorageService.getTradesByDateRange(
  startDate,
  endDate
);
const bySymbol = await tradeStorageService.getTradesBySymbol('EURUSD');
```

### Database Schema

The storage service manages a 4-table relational structure:

1. **trades**: Core trade information

   - id, symbol, entry_price, exit_price, quantity, timestamp
   - trade_type, session_timing, model_type

2. **trade_analysis**: Analysis and quality data

   - trade_id, pattern_quality_rating, confluence_factors
   - risk_reward_ratio, trade_outcome

3. **trade_setups**: Setup classification

   - trade_id, primary_setup, secondary_setup, setup_notes
   - confluence_count, setup_strength

4. **trade_fvg_details**: ICT-specific data
   - trade_id, fvg_date, fvg_time, rd_type, rd_time
   - draw_on_liquidity, liquidity_taken

## 🔧 Custom Hooks

### Data Management Hooks

```typescript
// Trade data hooks
import { useTrades, useTradeAnalytics } from '@adhd-trading-dashboard/shared';

const { trades, loading, error, refresh } = useTrades();
const { metrics, isLoading } = useTradeAnalytics(trades);

// Storage hooks
import { useLocalStorage, useIndexedDB } from '@adhd-trading-dashboard/shared';

const [preferences, setPreferences] = useLocalStorage(
  'user-preferences',
  defaultPrefs
);
const { data, save, remove } = useIndexedDB('trades');
```

### UI Hooks

```typescript
// Theme and UI hooks
import { useTheme, useModal, useToast } from '@adhd-trading-dashboard/shared';

const theme = useTheme();
const { isOpen, open, close } = useModal();
const { showToast } = useToast();

// Form hooks
import { useForm, useValidation } from '@adhd-trading-dashboard/shared';

const { values, errors, handleChange, handleSubmit } = useForm({
  initialValues: { symbol: '', quantity: 0 },
  validationSchema: tradeValidationSchema,
});
```

## 🔧 Utilities

### Data Processing

```typescript
import {
  formatCurrency,
  formatPercentage,
  calculateMetrics,
  validateTradeData,
} from '@adhd-trading-dashboard/shared';

// Formatting utilities
const formattedPrice = formatCurrency(1234.56, 'USD'); // "$1,234.56"
const formattedPercent = formatPercentage(0.1234); // "12.34%"

// Trading calculations
const metrics = calculateMetrics(trades);
const isValid = validateTradeData(tradeData);
```

### Date and Time

```typescript
import {
  formatTradeTime,
  getSessionFromTime,
  convertToNYTime,
} from '@adhd-trading-dashboard/shared';

// Time formatting
const tradeTime = formatTradeTime(timestamp, 'America/New_York');
const session = getSessionFromTime(timestamp); // 'London', 'NY', 'Asian'
const nyTime = convertToNYTime(localTimestamp);
```

## 📊 TypeScript Types

### Core Trading Types

```typescript
import type {
  CompleteTradeData,
  TradeAnalysis,
  TradeSetup,
  FVGDetails,
  TradeMetrics,
} from '@adhd-trading-dashboard/shared';

// Complete trade with all related data
interface CompleteTradeData {
  trade: Trade;
  analysis?: TradeAnalysis;
  setup?: TradeSetup;
  fvgDetails?: FVGDetails;
}

// Performance metrics
interface TradeMetrics {
  totalTrades: number;
  winRate: number;
  profitFactor: number;
  sharpeRatio: number;
  maxDrawdown: number;
  avgWin: number;
  avgLoss: number;
}
```

### Theme Types

```typescript
import type {
  Theme,
  ThemeColors,
  ThemeSpacing,
} from '@adhd-trading-dashboard/shared';

// Theme structure
interface Theme {
  colors: ThemeColors;
  spacing: ThemeSpacing;
  typography: ThemeTypography;
  breakpoints: ThemeBreakpoints;
}
```

## 🧪 Testing

### Component Testing

```typescript
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@adhd-trading-dashboard/shared';
import { Button } from '../Button';

describe('Button', () => {
  it('renders with theme', () => {
    render(
      <ThemeProvider theme={mercedesGreenTheme}>
        <Button>Test Button</Button>
      </ThemeProvider>
    );
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### Service Testing

```typescript
import { tradeStorageService } from '../tradeStorageService';

describe('TradeStorageService', () => {
  it('stores and retrieves trades', async () => {
    const trade = { symbol: 'EURUSD', quantity: 1000 };
    const stored = await tradeStorageService.addTrade(trade);
    const retrieved = await tradeStorageService.getTradeById(stored.id);
    expect(retrieved).toEqual(stored);
  });
});
```

## 📦 Build Configuration

### Package.json Scripts

```json
{
  "scripts": {
    "build": "tsc --emitDeclarationOnly --skipLibCheck && vite build",
    "build:dev": "tsc --emitDeclarationOnly --skipLibCheck && vite build --mode development",
    "dev": "tsc --emitDeclarationOnly --skipLibCheck --watch & vite build --watch",
    "test": "vitest run",
    "test:watch": "vitest",
    "storybook": "storybook dev -p 6006"
  }
}
```

### Vite Configuration

The package uses Vite for building with optimized configuration for library
mode:

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.ts',
      formats: ['es', 'cjs'],
      fileName: format => `index.${format}.js`,
    },
    rollupOptions: {
      external: ['react', 'react-dom', 'styled-components'],
      output: {
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
          'styled-components': 'styled',
        },
      },
    },
  },
});
```

## 🚀 Usage in Dashboard

The dashboard package consumes the shared package:

```typescript
// In dashboard package
import {
  Button,
  DataTable,
  tradeStorageService,
  useTradeAnalytics,
  formatCurrency,
} from '@adhd-trading-dashboard/shared';

// Use shared components and services
const TradingComponent = () => {
  const { trades } = useTrades();
  const metrics = useTradeAnalytics(trades);

  return <DataTable data={trades} columns={tradeColumns} />;
};
```

---

**Next Steps**: Explore the [Dashboard Package Documentation](./DASHBOARD.md) to
see how these shared components are used in trading features.
