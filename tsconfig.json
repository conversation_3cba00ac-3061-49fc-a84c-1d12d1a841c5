{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "Node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noImplicitAny": true, "noImplicitThis": true, "strictNullChecks": true, "resolveJsonModule": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@adhd-trading-dashboard/shared": ["packages/shared/src"], "@adhd-trading-dashboard/shared/*": ["packages/shared/src/*"], "@adhd-trading-dashboard/dashboard": ["packages/dashboard/src"], "@adhd-trading-dashboard/dashboard/*": ["packages/dashboard/src/*"]}, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "isolatedModules": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo"}, "exclude": ["node_modules", "dist", "scripts", "**/node_modules", "**/dist", "**/dev-dist", "**/code-health", "**/public/assets", "**/*.config.js", "**/*.config.ts", "**/babel.config.*", "**/scripts/**/*", "**/*.d.ts"], "references": [{"path": "./packages/shared"}, {"path": "./packages/dashboard"}]}