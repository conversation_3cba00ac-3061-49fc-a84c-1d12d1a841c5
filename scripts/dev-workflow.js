#!/usr/bin/env node

/**
 * Optimized Development Workflow
 * 
 * Streamlined commands for Augment AI iteration:
 * - Quick setup and validation
 * - Efficient testing workflows
 * - Build and deployment helpers
 * - Code quality checks
 */

import { execSync } from 'child_process';
import chalk from 'chalk';
import { performance } from 'perf_hooks';
import { devToolsConfig } from './dev-tools-config.js';

const { augmentCommands, performance: perfConfig } = devToolsConfig;

/**
 * Execute command with timing and error handling
 */
function runCommand(name, command, options = {}) {
  const startTime = performance.now();
  
  console.log(chalk.blue(`🚀 ${name}`));
  console.log(chalk.gray(`   Command: ${command}\n`));

  try {
    execSync(command, {
      stdio: 'inherit',
      env: { ...process.env, FORCE_COLOR: 'true' },
      ...options
    });

    const duration = performance.now() - startTime;
    const durationSec = (duration / 1000).toFixed(2);
    
    // Performance warnings
    const maxTime = parseFloat(perfConfig.buildTime.maxTime.replace('s', ''));
    const warningTime = parseFloat(perfConfig.buildTime.warning.replace('s', ''));
    
    if (duration / 1000 > maxTime) {
      console.log(chalk.red(`\n⚠️  ${name} took ${durationSec}s (exceeds ${maxTime}s threshold)`));
    } else if (duration / 1000 > warningTime) {
      console.log(chalk.yellow(`\n⚠️  ${name} took ${durationSec}s (approaching ${maxTime}s threshold)`));
    } else {
      console.log(chalk.green(`\n✅ ${name} completed in ${durationSec}s`));
    }
    
    return true;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(chalk.red(`\n❌ ${name} failed after ${(duration / 1000).toFixed(2)}s`));
    console.error(chalk.red(`   Exit code: ${error.status}`));
    return false;
  }
}

/**
 * Quick development setup
 */
function quickSetup() {
  console.log(chalk.blue('🛠️  Quick Development Setup\n'));
  
  const steps = [
    ['Install Dependencies', 'yarn install'],
    ['Type Check', 'yarn type-check'],
    ['Health Check', 'yarn health'],
  ];

  for (const [name, command] of steps) {
    const success = runCommand(name, command);
    if (!success) {
      console.error(chalk.red(`\n💥 Setup failed at: ${name}`));
      return false;
    }
  }

  console.log(chalk.green('\n🎉 Development environment ready!'));
  console.log(chalk.gray('   Next: yarn dev'));
  return true;
}

/**
 * Quick test workflow
 */
function quickTest() {
  console.log(chalk.blue('🧪 Quick Test Workflow\n'));
  
  const steps = [
    ['Lint Check', 'yarn lint'],
    ['Type Check', 'yarn type-check'],
    ['Unit Tests', 'yarn test'],
  ];

  let allPassed = true;
  for (const [name, command] of steps) {
    const success = runCommand(name, command);
    allPassed = allPassed && success;
  }

  if (allPassed) {
    console.log(chalk.green('\n🎉 All quality checks passed!'));
  } else {
    console.log(chalk.red('\n💥 Some quality checks failed'));
  }
  
  return allPassed;
}

/**
 * Quick build workflow
 */
function quickBuild() {
  console.log(chalk.blue('📦 Quick Build Workflow\n'));
  
  const steps = [
    ['Clean Build', 'yarn clean'],
    ['Type Check', 'yarn type-check'],
    ['Build All', 'yarn build'],
  ];

  for (const [name, command] of steps) {
    const success = runCommand(name, command);
    if (!success) {
      console.error(chalk.red(`\n💥 Build failed at: ${name}`));
      return false;
    }
  }

  console.log(chalk.green('\n🎉 Build completed successfully!'));
  return true;
}

/**
 * Full quality check
 */
function fullQualityCheck() {
  console.log(chalk.blue('🔍 Full Quality Check\n'));
  
  const steps = [
    ['Lint Check', 'yarn lint'],
    ['Type Check', 'yarn type-check'],
    ['Unit Tests', 'yarn test'],
    ['Build Check', 'yarn build'],
    ['Health Check', 'yarn health'],
  ];

  const results = [];
  for (const [name, command] of steps) {
    const success = runCommand(name, command);
    results.push([name, success]);
  }

  // Summary
  console.log(chalk.blue('\n📋 Quality Check Summary:'));
  console.log(chalk.blue('=========================='));
  
  for (const [name, passed] of results) {
    const status = passed ? chalk.green('✅ PASS') : chalk.red('❌ FAIL');
    console.log(`${status} ${name}`);
  }

  const allPassed = results.every(([, passed]) => passed);
  
  if (allPassed) {
    console.log(chalk.green('\n🎉 All quality checks passed!'));
  } else {
    console.log(chalk.red('\n💥 Some quality checks failed'));
  }
  
  return allPassed;
}

/**
 * CLI interface
 */
const command = process.argv[2];

switch (command) {
  case 'setup':
    quickSetup();
    break;
  case 'test':
    quickTest();
    break;
  case 'build':
    quickBuild();
    break;
  case 'quality':
    fullQualityCheck();
    break;
  default:
    console.log(chalk.blue('🚀 Optimized Development Workflow\n'));
    console.log('Usage:');
    console.log('  node scripts/dev-workflow.js setup   - Quick development setup');
    console.log('  node scripts/dev-workflow.js test    - Quick test workflow');
    console.log('  node scripts/dev-workflow.js build   - Quick build workflow');
    console.log('  node scripts/dev-workflow.js quality - Full quality check');
    console.log('\nAugment AI Commands:');
    Object.entries(augmentCommands).forEach(([key, cmd]) => {
      console.log(chalk.gray(`  ${key}: ${cmd}`));
    });
    break;
}

export { quickSetup, quickTest, quickBuild, fullQualityCheck };
