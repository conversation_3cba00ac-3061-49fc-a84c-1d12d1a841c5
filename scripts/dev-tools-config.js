/**
 * Enhanced Development Tools Configuration
 * Centralized configuration for all development tooling
 */

export const devToolsConfig = {
  // Project structure
  monorepo: {
    root: process.cwd(),
    packages: [
      {
        name: 'shared',
        path: 'packages/shared',
        workspace: '@adhd-trading-dashboard/shared',
        type: 'library',
        buildTarget: 'es2020',
      },
      {
        name: 'dashboard',
        path: 'packages/dashboard',
        workspace: '@adhd-trading-dashboard/dashboard',
        type: 'application',
        buildTarget: 'es2020',
      },
    ],
    buildOrder: ['shared', 'dashboard'],
  },

  // Build configuration
  build: {
    target: 'es2020',
    minify: true,
    sourceMaps: true,
    outDir: 'dist',
    formats: ['es', 'cjs'],
    external: ['react', 'react-dom', 'styled-components'],
  },

  // Testing configuration
  testing: {
    framework: 'vitest',
    environment: 'jsdom',
    coverage: {
      threshold: 70,
      exclude: ['**/node_modules/**', '**/dist/**', '**/*.config.*'],
    },
    timeout: 10000,
    setupFiles: ['./vitest.setup.ts'],
  },

  // Linting and formatting
  codeQuality: {
    eslint: {
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      ignorePatterns: ['dist/', 'node_modules/', 'coverage/'],
    },
    prettier: {
      enabled: true,
      configFile: '.prettierrc',
    },
    typescript: {
      strict: true,
      noEmit: false,
      composite: true,
    },
  },

  // Development server
  devServer: {
    port: 3000,
    host: 'localhost',
    open: true,
    hmr: true,
    https: false,
  },

  // Analysis tools
  analysis: {
    bundleAnalyzer: true,
    duplicateCode: true,
    complexity: true,
    dependencies: true,
    performance: true,
  },

  // Deployment
  deployment: {
    environments: {
      development: {
        url: 'http://localhost:3000',
        branch: 'develop',
      },
      production: {
        url: 'https://app.example.com',
        branch: 'main',
      },
    },
  },

  // Tool paths
  paths: {
    src: 'src',
    dist: 'dist',
    build: 'build',
    temp: 'temp',
    coverage: 'coverage',
    docs: 'docs',
    scripts: 'scripts',
  },

  // Commands for Augment AI iteration
  augmentCommands: {
    // Quick development workflow
    quickStart: 'yarn setup && yarn dev',
    
    // Testing workflow
    testAll: 'yarn test:all',
    testWatch: 'yarn test:watch',
    testCoverage: 'yarn test:coverage',
    
    // Build workflow
    buildAll: 'yarn build',
    buildWatch: 'yarn build:watch',
    buildClean: 'yarn build:clean',
    
    // Quality checks
    lint: 'yarn lint',
    typeCheck: 'yarn type-check',
    healthCheck: 'yarn health',
    
    // Analysis
    analyze: 'yarn analyze',
    analyzeBundle: 'yarn analyze:bundle',
    analyzeComponents: 'yarn analyze:components',
    
    // Maintenance
    cleanup: 'yarn cleanup',
    depsSync: 'yarn deps:sync',
    depsCheck: 'yarn deps:check',
  },

  // Performance thresholds
  performance: {
    bundleSize: {
      maxSize: '500kb',
      warning: '400kb',
    },
    buildTime: {
      maxTime: '30s',
      warning: '20s',
    },
    testTime: {
      maxTime: '60s',
      warning: '30s',
    },
  },

  // Feature flags for tools
  features: {
    storybook: true,
    playwright: true,
    bundleAnalyzer: true,
    performanceMonitoring: true,
    dependencyAnalysis: true,
    codeComplexityAnalysis: true,
    duplicateCodeDetection: true,
    architectureDocumentation: true,
  },
};

export default devToolsConfig;
